// React Query POC Configuration
// This file validates React Query integration feasibility

import { QueryClient } from '@tanstack/react-query';

// Proposed configuration for Lohari
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time: 5 minutes (data remains fresh)
      staleTime: 5 * 60 * 1000,
      // Cache time: 10 minutes (data stays in cache)
      gcTime: 10 * 60 * 1000,
      // Retry failed requests 3 times
      retry: 3,
      // Refetch on window focus
      refetchOnWindowFocus: false,
      // Refetch on reconnect
      refetchOnReconnect: 'always',
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
    },
  },
});

// Example hook migration from SWR to React Query
export const useOrdersExample = () => {
  // Current SWR approach
  // const { data, error, mutate } = useSWR('/api/orders', fetcher);
  
  // React Query approach
  return {
    queryKey: ['orders'],
    queryFn: async () => {
      const response = await fetch('/api/orders');
      if (!response.ok) throw new Error('Failed to fetch orders');
      return response.json();
    },
    // Advanced features not available in SWR
    select: (data: any) => data.orders,
    placeholderData: [],
    enabled: true,
  };
};

// Request deduplication example
export const deduplicatedFetcher = (() => {
  const cache = new Map<string, Promise<any>>();
  
  return async function fetch<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const promise = fetcher();
    cache.set(key, promise);
    
    try {
      return await promise;
    } finally {
      // Clean up after 100ms to allow deduplication
      setTimeout(() => cache.delete(key), 100);
    }
  };
})();

// Virtual scrolling configuration
export const virtualScrollConfig = {
  itemHeight: 80,
  overscan: 5,
  estimatedItemSize: 80,
  getItemKey: (index: number, data: any) => data[index]?.id || index,
};

// Performance monitoring setup
export const performanceConfig = {
  enableWebVitals: true,
  reportThreshold: {
    LCP: 2500, // 2.5s
    FID: 100,  // 100ms
    CLS: 0.1,  // 0.1
    TTFB: 800, // 800ms
  },
};
