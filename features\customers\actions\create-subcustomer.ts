"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";

import { createSubcustomerSchema } from "./schema";

export async function createSubCustomer(
    data: z.infer<typeof createSubcustomerSchema>,
) {
    try {
        const validatedData = createSubcustomerSchema.parse(data);

        // Verificar que el cliente padre existe
        const parentCustomer = await db.customer.findUnique({
            where: { id: validatedData.parentId },
        });

        if (!parentCustomer) {
            return { success: false, error: "Cliente padre no encontrado" };
        }

        // Verificar que no exista otro cliente con el mismo nombre
        const existingCustomer = await db.customer.findFirst({
            where: { name: validatedData.name },
        });

        if (existingCustomer) {
            return {
                success: false,
                error: "Ya existe un cliente con ese nombre",
            };
        }

        const subCustomer = await db.customer.create({
            data: {
                name: validatedData.name,
                parentId: validatedData.parentId,
                displayName:
                    validatedData.displayName ||
                    `${parentCustomer.name} - ${validatedData.name}`,
            },
            include: {
                parent: true,
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
        });

        revalidatePath("/dashboard/customers");
        revalidatePath(
            `/dashboard/customers/${validatedData.parentId}/details`,
        );

        return { success: true, data: subCustomer };
    } catch (error) {
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos inválidos",
            };
        }

        return handleDbError(() => {
            throw error;
        }, "Error al crear el subcliente");
    }
}
