"use client";

import { useEffect, useState, useRef } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import {
    motion,
    AnimatePresence,
    useMotionValue,
    useTransform,
} from "framer-motion";

export function RouteLoader() {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [loading, setLoading] = useState(false);
    const timeoutRef = useRef<NodeJS.Timeout>();
    const intervalRef = useRef<NodeJS.Timeout>();

    // Use motion value for smooth animations
    const progress = useMotionValue(0);
    const scaleX = useTransform(progress, [0, 100], [0, 1]);
    const glowOpacity = useTransform(progress, [0, 50, 100], [0.3, 0.6, 0.3]);

    useEffect(() => {
        // Clear any existing timers
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        if (intervalRef.current) clearInterval(intervalRef.current);

        const handleStart = () => {
            setLoading(true);
            progress.set(0);

            // Smooth progress increment
            let currentProgress = 0;

            intervalRef.current = setInterval(() => {
                if (currentProgress < 90) {
                    // Natural easing - fast at start, slow near end
                    const increment = Math.max(
                        0.5,
                        (90 - currentProgress) * 0.15,
                    );

                    currentProgress = Math.min(90, currentProgress + increment);
                    progress.set(currentProgress);
                }
            }, 16); // 60fps for smooth animation
        };

        const handleComplete = () => {
            // Quick jump to 100%
            progress.set(100);

            // Clear interval
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }

            // Hide after completion
            timeoutRef.current = setTimeout(() => {
                setLoading(false);
                progress.set(0);
            }, 300);
        };

        handleStart();

        // Complete after route loads
        const completeTimer = setTimeout(handleComplete, 500);

        return () => {
            clearTimeout(completeTimer);
            if (timeoutRef.current) clearTimeout(timeoutRef.current);
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, [pathname, searchParams, progress]);

    return (
        <AnimatePresence mode="wait">
            {loading && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className="fixed top-0 left-0 right-0 z-[9999] pointer-events-none"
                    exit={{ opacity: 0, y: -4 }}
                    initial={{ opacity: 0, y: -4 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                >
                    {/* Main progress bar container */}
                    <div className="relative h-[3px] overflow-hidden bg-black/5">
                        {/* Progress bar */}
                        <motion.div
                            className="absolute inset-y-0 left-0 right-0 bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600"
                            style={{
                                scaleX,
                                transformOrigin: "left",
                            }}
                        />

                        {/* Glow effect */}
                        <motion.div
                            className="absolute inset-y-0 left-0 right-0 bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 blur-sm"
                            style={{
                                scaleX,
                                opacity: glowOpacity,
                                transformOrigin: "left",
                            }}
                        />

                        {/* Shimmer effect */}
                        <motion.div
                            animate={{
                                x: "calc(100vw + 150px)",
                            }}
                            className="absolute top-0 h-full w-[150px] pointer-events-none"
                            initial={{ x: "-150px" }}
                            transition={{
                                duration: 1.5,
                                repeat: Infinity,
                                ease: "linear",
                                repeatDelay: 0.5,
                            }}
                        >
                            <div className="h-full w-full bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12" />
                        </motion.div>

                        {/* Top highlight */}
                        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-white/10 to-transparent" />
                    </div>

                    {/* Bottom shadow */}
                    <div className="h-[1px] bg-gradient-to-b from-black/5 to-transparent" />
                </motion.div>
            )}
        </AnimatePresence>
    );
}
