/**
 * Redis Cache Integration Tests
 * Validates cache functionality and performance improvements
 */

import { describe, it, expect, beforeAll, afterAll } from "@jest/globals";

import {
    getRedisClient,
    disconnectRedis,
    isRedisHealthy,
} from "../../cache/redis-client";
import { staticCache } from "../../cache/strategies";

describe("Redis Cache Integration", () => {
    beforeAll(async () => {
        // Ensure Redis is connected
        const client = getRedisClient();

        if (!client) {
            throw new Error("Redis client not available");
        }
    });

    afterAll(async () => {
        // Clean up
        await disconnectRedis();
    });

    describe("Health Check", () => {
        it("should verify Redis connection is healthy", async () => {
            const healthy = await isRedisHealthy();

            expect(healthy).toBe(true);
        });
    });

    describe("Static Cache Strategy", () => {
        it("should cache and retrieve static data", async () => {
            const testData = { id: 1, name: "Test Product" };

            // Set data
            const setResult = await staticCache.setStatic(
                "products",
                "test-1",
                testData,
            );

            expect(setResult).toBe(true);

            // Get data
            const retrieved = await staticCache.get({
                resource: "products",
                identifier: "test-1",
            });

            expect(retrieved).toEqual(testData);
        });
    });
});
