"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";

import { importFoliosFromLocalStorage } from "../actions/migrate";

export function RemissionMigration() {
    const [migrationStatus, setMigrationStatus] = useState<
        "idle" | "checking" | "migrating" | "completed" | "error" | "no_data"
    >("idle");
    const [result, setResult] = useState<any>(null);
    const [localStorageData, setLocalStorageData] = useState<string | null>(
        null,
    );

    // Check if we have localStorage data to migrate
    useEffect(() => {
        setMigrationStatus("checking");

        try {
            const storedFolios = localStorage.getItem(
                "lohari_remission_folios",
            );

            setLocalStorageData(storedFolios);

            if (!storedFolios) {
                setMigrationStatus("no_data");
            } else {
                const data = JSON.parse(storedFolios);

                if (!data.all || data.all.length === 0) {
                    setMigrationStatus("no_data");
                } else {
                    setMigrationStatus("idle");
                }
            }
        } catch (error) {
            // REMOVED: console.error("Error checking localStorage:", error);
            setMigrationStatus("error");
        }
    }, []);

    const handleMigrate = async () => {
        if (!localStorageData) return;

        setMigrationStatus("migrating");
        try {
            const migrationResult =
                await importFoliosFromLocalStorage(localStorageData);

            setResult(migrationResult);
            setMigrationStatus("completed");

            // Clear localStorage after successful migration
            localStorage.removeItem("lohari_remission_folios");
        } catch (error) {
            // REMOVED: console.error("Migration error:", error);
            setMigrationStatus("error");
            setResult(error);
        }
    };

    if (migrationStatus === "checking") {
        return (
            <Card className="p-6 bg-gray-50">
                <h2 className="text-lg font-semibold mb-4">
                    Verificando datos locales...
                </h2>
                <div className="animate-pulse flex space-x-4">
                    <div className="flex-1 space-y-4 py-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4" />
                        <div className="h-4 bg-gray-200 rounded w-1/2" />
                    </div>
                </div>
            </Card>
        );
    }

    if (migrationStatus === "no_data") {
        return (
            <Card className="p-6">
                <h2 className="text-lg font-semibold mb-2">
                    No hay datos para migrar
                </h2>
                <p className="text-gray-600 mb-4">
                    No se encontraron folios de remisión almacenados localmente.
                    No es necesario realizar migración.
                </p>
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <h3 className="font-medium text-blue-700">
                        Sistema de remisiones actualizado
                    </h3>
                    <p className="text-sm mt-1 text-blue-600">
                        Las remisiones ahora se almacenan en la base de datos
                        para facilitar su acceso desde cualquier dispositivo.
                    </p>
                </div>
            </Card>
        );
    }

    if (migrationStatus === "error") {
        return (
            <Card className="p-6">
                <h2 className="text-lg font-semibold mb-2 text-red-600">
                    Error en la migración
                </h2>
                <p className="text-gray-600 mb-4">
                    Ocurrió un error al intentar migrar los datos. Por favor,
                    inténtelo nuevamente o contacte al soporte técnico.
                </p>
                <pre className="bg-red-50 p-4 rounded text-xs overflow-x-auto">
                    {result
                        ? JSON.stringify(result, null, 2)
                        : "Error desconocido"}
                </pre>
                <div className="mt-4">
                    <Button
                        color="danger"
                        variant="flat"
                        onClick={() => setMigrationStatus("idle")}
                    >
                        Reintentar
                    </Button>
                </div>
            </Card>
        );
    }

    if (migrationStatus === "completed") {
        return (
            <Card className="p-6">
                <h2 className="text-lg font-semibold mb-2 text-green-600">
                    Migración completada
                </h2>
                <p className="text-gray-600 mb-4">
                    Los folios de remisión se han migrado correctamente a la
                    base de datos.
                </p>

                {result && result.results && (
                    <div className="bg-green-50 p-4 rounded mb-4">
                        <h3 className="font-medium text-green-700 mb-2">
                            Resumen:
                        </h3>
                        <ul className="space-y-1 text-sm">
                            <li>
                                <span className="font-medium">
                                    Total procesados:
                                </span>{" "}
                                {result.results.total}
                            </li>
                            <li>
                                <span className="font-medium">Válidos:</span>{" "}
                                {result.results.valid}
                            </li>
                            <li>
                                <span className="font-medium">Migrados:</span>{" "}
                                {result.results.migrated}
                            </li>
                            <li>
                                <span className="font-medium">
                                    Omitidos (ya existentes):
                                </span>{" "}
                                {result.results.skipped}
                            </li>
                            <li>
                                <span className="font-medium">Inválidos:</span>{" "}
                                {result.results.invalid}
                            </li>
                            <li>
                                <span className="font-medium">Errores:</span>{" "}
                                {result.results.errors}
                            </li>
                        </ul>
                    </div>
                )}

                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <h3 className="font-medium text-green-700">
                        Sistema de remisiones actualizado
                    </h3>
                    <p className="text-sm mt-1 text-green-600">
                        Las remisiones ahora se almacenan en la base de datos y
                        pueden ser accedidas desde cualquier dispositivo.
                    </p>
                </div>
            </Card>
        );
    }

    return (
        <Card className="p-6">
            <h2 className="text-lg font-semibold mb-2">
                Migración de remisiones
            </h2>
            <p className="text-gray-600 mb-4">
                Se han encontrado{" "}
                {JSON.parse(localStorageData || '{"all":[]}').all.length} folios
                de remisión almacenados localmente. Estos folios solo son
                accesibles desde este dispositivo. Migre los datos para acceder
                a ellos desde cualquier lugar.
            </p>

            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <h3 className="font-medium text-yellow-700">Importante</h3>
                <p className="text-sm mt-1 text-yellow-600">
                    La migración moverá los folios a la base de datos central y
                    los eliminará del almacenamiento local. Este proceso no se
                    puede deshacer.
                </p>
            </div>

            <div className="flex justify-end">
                <Button
                    color="primary"
                    disabled={migrationStatus === "migrating"}
                    variant="solid"
                    onClick={handleMigrate}
                >
                    {migrationStatus === "migrating"
                        ? "Migrando datos..."
                        : "Iniciar migración"}
                </Button>
            </div>
        </Card>
    );
}
