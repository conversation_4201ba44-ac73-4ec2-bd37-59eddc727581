"use client";

import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Di<PERSON>r,
} from "@heroui/react";
import {
    TrashIcon,
    PencilIcon,
    UserIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";

export interface Note {
    id: string;
    content: string;
    authorId?: string;
    importanceId?: string;
    statusId?: string;
    orderId?: string;
    createdAt: Date | string;
    updatedAt?: Date | string;
    author?: {
        id?: string;
        name?: string;
        email?: string;
        image?: string;
    };
}

export interface NoteCardProps {
    note: Note;
    orderId?: string;
    importanceOptions: { id: string; name: string; color?: string | null }[];
    statusOptions: { id: string; name: string; color?: string | null }[];
    onDelete?: () => void;
    onUpdate?: () => void;
    canEdit?: boolean;
    isEditable?: boolean;
    isDeletable?: boolean;
    commentCount?: number;
    currentUserId?: string;
    userRole?: string;
    className?: string;
}

export function NoteCard({
    note,
    orderId,
    importanceOptions = [],
    statusOptions = [],
    onDelete,
    onUpdate,
    canEdit = false,
    isEditable,
    isDeletable,
    commentCount,
    currentUserId,
    userRole,
    className,
}: NoteCardProps) {
    return (
        <Card className={`w-full ${className || ""}`}>
            <CardHeader className="flex justify-between pb-2">
                <div className="flex items-center gap-2">
                    <Avatar
                        icon={<UserIcon className="w-4 h-4" />}
                        name={note.author?.name || "Usuario"}
                        size="sm"
                        src={note.author?.image || undefined}
                    />
                    <div>
                        <span className="text-sm font-medium">
                            {note.author?.name || "Usuario"}
                        </span>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                            <ClockIcon className="w-3 h-3" />
                            <span>
                                {new Date(note.createdAt).toLocaleDateString(
                                    "es-ES",
                                    {
                                        day: "2-digit",
                                        month: "2-digit",
                                        year: "numeric",
                                    },
                                )}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="flex gap-1">
                    {/* Importancia */}
                    {note.importanceId && importanceOptions?.length > 0 && (
                        <Chip
                            size="sm"
                            style={{
                                backgroundColor:
                                    importanceOptions.find(
                                        (opt) => opt.id === note.importanceId,
                                    )?.color || "#9CA3AF",
                                color: "white",
                            }}
                        >
                            {importanceOptions.find(
                                (opt) => opt.id === note.importanceId,
                            )?.name || "Normal"}
                        </Chip>
                    )}

                    {/* Estado */}
                    {note.statusId && statusOptions?.length > 0 && (
                        <Chip
                            size="sm"
                            style={{
                                backgroundColor:
                                    statusOptions.find(
                                        (opt) => opt.id === note.statusId,
                                    )?.color || "#9CA3AF",
                                color: "white",
                            }}
                            variant="flat"
                        >
                            {statusOptions.find(
                                (opt) => opt.id === note.statusId,
                            )?.name || "Pendiente"}
                        </Chip>
                    )}
                </div>
            </CardHeader>

            <Divider />

            <CardBody className="py-2">
                <p className="whitespace-pre-wrap">{note.content}</p>
            </CardBody>

            {(isEditable ?? canEdit) && (
                <CardFooter>
                    <div className="flex justify-end gap-1">
                        {onUpdate && (
                            <Tooltip content="Editar nota">
                                <Button
                                    isIconOnly
                                    aria-label="Editar nota"
                                    color="primary"
                                    size="sm"
                                    variant="light"
                                    onClick={onUpdate}
                                >
                                    <PencilIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>
                        )}

                        {onDelete && isDeletable && (
                            <Tooltip content="Eliminar nota">
                                <Button
                                    isIconOnly
                                    aria-label="Eliminar nota"
                                    color="danger"
                                    size="sm"
                                    variant="light"
                                    onClick={onDelete}
                                >
                                    <TrashIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>
                        )}
                    </div>
                </CardFooter>
            )}
        </Card>
    );
}
