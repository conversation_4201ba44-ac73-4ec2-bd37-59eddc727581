"use server";

import { Prisma } from "@prisma/client";

import { db } from "@/shared/lib/db";
import { handleDbError } from "@/shared/lib/db";

/**
 * Obtiene estadísticas completas de contratistas
 */
export async function getContractorStats(
    options: {
        startDate?: Date;
        endDate?: Date;
    } = {},
) {
    // Usamos handleDbError para gestionar los errores y reintentos automáticamente
    return handleDbError(
        async () => {
            const { startDate, endDate } = options;

            // Filtro por fecha
            const dateFilter: Prisma.AssignmentWhereInput = {};

            if (startDate) {
                dateFilter.createdAt = {
                    ...((dateFilter.createdAt as object) || {}),
                    gte: startDate,
                };
            }
            if (endDate) {
                dateFilter.createdAt = {
                    ...((dateFilter.createdAt as object) || {}),
                    lte: endDate,
                };
            }

            // 1. Estadísticas generales
            const [totalContractors, activeContractors, totalAssignments] =
                await Promise.all([
                    db.contractor.count(),
                    db.contractor.count({
                        where: {
                            assignments: {
                                some: {},
                            },
                        },
                    }),
                    db.assignment.count({
                        where: dateFilter,
                    }),
                ]);

            // 2. Top contratistas por asignaciones
            const topContractors = await db.contractor.findMany({
                where: {
                    assignments: {
                        some: dateFilter,
                    },
                },
                include: {
                    _count: {
                        select: {
                            assignments: true,
                        },
                    },
                    assignments: {
                        where: dateFilter,
                        select: {
                            quantity: true,
                            isCompleted: true,
                            defects: true,
                        },
                    },
                },
                orderBy: {
                    assignments: {
                        _count: "desc",
                    },
                },
                take: 10,
            });

            // Transformar datos de contratistas
            const formattedTopContractors = topContractors.map((contractor) => {
                // Calcular métricas
                const totalAssigned = contractor.assignments.reduce(
                    (sum, a) => sum + a.quantity,
                    0,
                );
                const totalDefects = contractor.assignments.reduce(
                    (sum, a) => sum + (a.defects || 0),
                    0,
                );
                const completedAssignments = contractor.assignments.filter(
                    (a) => a.isCompleted,
                ).length;
                const completionRate =
                    contractor._count.assignments > 0
                        ? (completedAssignments /
                              contractor._count.assignments) *
                          100
                        : 0;

                return {
                    id: contractor.id,
                    name: contractor.firstName
                        ? `${contractor.firstName} ${contractor.lastName || ""}`
                        : contractor.name,
                    assignmentCount: contractor._count.assignments,
                    totalAssigned,
                    totalDefects,
                    defectRate:
                        totalAssigned > 0
                            ? (totalDefects / totalAssigned) * 100
                            : 0,
                    completionRate,
                };
            });

            // 3. Tendencias de asignaciones por mes
            // Obtener todos los assignments en el rango de tiempo
            const assignments = await db.assignment.findMany({
                where: dateFilter,
                select: {
                    id: true,
                    createdAt: true,
                    isCompleted: true,
                    quantity: true,
                },
                orderBy: {
                    createdAt: "asc",
                },
            });

            // Agrupar por mes
            const monthlyData: Record<
                string,
                { assigned: number; completed: number }
            > = {};

            assignments.forEach((assignment) => {
                const monthKey = assignment.createdAt.toISOString().slice(0, 7); // YYYY-MM

                if (!monthlyData[monthKey]) {
                    monthlyData[monthKey] = { assigned: 0, completed: 0 };
                }

                monthlyData[monthKey].assigned += assignment.quantity;
                if (assignment.isCompleted) {
                    monthlyData[monthKey].completed += assignment.quantity;
                }
            });

            // Formatear datos para gráficos
            const trendData = Object.entries(monthlyData)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([month, data]) => ({
                    month,
                    ...data,
                }));

            return {
                summary: {
                    totalContractors,
                    activeContractors,
                    inactiveContractors: totalContractors - activeContractors,
                    activityRate:
                        totalContractors > 0
                            ? (activeContractors / totalContractors) * 100
                            : 0,
                    totalAssignments,
                    avgAssignmentsPerContractor:
                        activeContractors > 0
                            ? totalAssignments / activeContractors
                            : 0,
                },
                topContractors: formattedTopContractors,
                trends: trendData,
                updatedAt: new Date().toISOString(),
            };
        },
        "Error obteniendo estadísticas de contratistas",
        3, // Aumentamos a 3 reintentos máximos
    );
}
