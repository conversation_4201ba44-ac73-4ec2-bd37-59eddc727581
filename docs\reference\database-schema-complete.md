# Esquema de Base de Datos Completo - Proyecto Lohari

## 🗄️ Información General

- **Base de Datos**: PostgreSQL
- **ORM**: Prisma 6.8.2
- **Provider**: Supabase
- **Conexiones**: URL directa y pool de conexiones

## 📊 Diagrama de Relaciones Principales

```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   Customer  │────▶│    Order     │◀────│ OrderStatus │
└─────────────┘     └──────────────┘     └─────────────┘
                           │
                    ┌──────┴──────┬──────────┬────────────┐
                    ▼             ▼          ▼            ▼
             ┌──────────┐  ┌──────────┐ ┌────────┐  ┌──────────┐
             │ Garment  │  │OrderPart │ │  Note  │  │Assignment│
             └──────────┘  └──────────┘ └────────┘  └──────────┘
                    │                                      │
             ┌──────┴──────┐                              │
             ▼             ▼                              ▼
       ┌───────────┐ ┌────────────┐              ┌──────────────┐
       │GarmentSize│ │GarmentModel│              │  Contractor  │
       └───────────┘ └────────────┘              └──────────────┘
```

## 🔐 Modelos de Autenticación

### Account
```prisma
model Account {
  id                String   @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  // ... más campos OAuth
  user              User     @relation(...)
  
  @@unique([provider, providerAccountId])
}
```
**Propósito**: Cuentas OAuth/Social login vinculadas a usuarios

### Session
```prisma
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(...)
}
```
**Propósito**: Sesiones activas de usuarios

### User
```prisma
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  password      String?
  roleId        String
  role          Role      @relation(...)
  // Relaciones con actividad
  notes         Note[]
  noteComments  NoteComment[]
  noteLikes     NoteLike[]
  // ... más relaciones
}
```
**Propósito**: Usuarios del sistema con rol asignado

### Role
```prisma
model Role {
  id       String   @id @default(cuid())
  name     String   @unique
  iconName String?
  color    String?
  users    User[]
}
```
**Propósito**: Roles de usuario (admin, user)

## 📦 Modelos de Negocio Principal

### Customer
```prisma
model Customer {
  id        String   @id @default(cuid())
  name      String   @unique
  orders    Order[]
}
```
**Propósito**: Clientes que realizan órdenes

### Order
```prisma
model Order {
  id                    String      @id @default(cuid())
  transferNumber        String?     // Número de transferencia
  cutOrder              String?     // Orden de corte
  batch                 String?     // Lote
  receivedDate          DateTime    @default(now())
  estimatedDeliveryDate DateTime?
  deliveryDate          DateTime?
  customerId            String
  statusId              String
  // Relaciones
  customer              Customer    @relation(...)
  status                OrderStatus @relation(...)
  garments              Garment[]
  parts                 OrderPart[]
  assignments           Assignment[]
  notes                 Note[]
  histories             OrderStatusHistory[]
}
```
**Propósito**: Órdenes de producción central del sistema

### OrderPart
```prisma
model OrderPart {
  id      String @id @default(cuid())
  code    String // Código de parte (ej: "635487")
  orderId String
  order   Order  @relation(...)
}
```
**Propósito**: Partes/partidas específicas de una orden

### OrderStatus
```prisma
model OrderStatus {
  id       String  @id @default(cuid())
  name     String  @unique
  iconName String?
  color    String?
  orders   Order[]
}
```
**Propósito**: Estados posibles de las órdenes

## 👕 Modelos de Inventario

### GarmentModel
```prisma
model GarmentModel {
  id          String    @id @default(cuid())
  code        String    @unique
  description String
  basePrice   Decimal?  @db.Decimal(10, 2)  // Añadido en 2025
  garments    Garment[]
}
```
**Propósito**: Modelos/diseños de prendas con precio base opcional

### Color
```prisma
model Color {
  id       String    @id @default(cuid())
  name     String    @unique
  hexCode  String?
  garments Garment[]
}
```
**Propósito**: Catálogo de colores con código HEX

### Size
```prisma
model Size {
  id       String        @id @default(cuid())
  code     String        @unique
  garments GarmentSize[]
}
```
**Propósito**: Catálogo de tallas (S, M, L, etc.)

### Garment
```prisma
model Garment {
  id      String        @id @default(cuid())
  modelId String
  colorId String
  orderId String
  model   GarmentModel  @relation(...)
  color   Color         @relation(...)
  order   Order         @relation(...)
  sizes   GarmentSize[]
}
```
**Propósito**: Prenda específica (modelo + color) en una orden

### GarmentSize
```prisma
model GarmentSize {
  id            String       @id @default(cuid())
  totalQuantity Int          // Cantidad total
  usedQuantity  Int @default(0) // Cantidad asignada
  sizeId        String
  garmentId     String
  size          Size         @relation(...)
  garment       Garment      @relation(...)
  assignments   Assignment[]
}
```
**Propósito**: Cantidad por talla de una prenda

## 👷 Modelos de Producción

### Contractor
```prisma
model Contractor {
  id             String       @id @default(cuid())
  name           String       @unique
  notes          String?
  firstName      String?
  lastName       String?
  email          String?
  phone          String?
  assignments    Assignment[]
  remissions     Remission[]
}
```
**Propósito**: Contratistas que realizan trabajo

### Assignment
```prisma
model Assignment {
  id            String     @id @default(cuid())
  folio         String?    @unique // Folio único
  contractorId  String
  garmentSizeId String
  quantity      Int
  defects       Int?       @default(0)
  isCompleted   Boolean    @default(false)
  orderId       String
  version       Int        @default(1)
  status        String     @default("ACTIVE")
  // Relaciones
  contractor    Contractor @relation(...)
  garmentSize   GarmentSize @relation(...)
  order         Order      @relation(...)
  progress      AssignmentProgress[]
  remissions    RemissionAssignment[]
  
  @@index([version])
  @@index([status])
}
```
**Propósito**: Asignación de trabajo a contratistas

### AssignmentProgress
```prisma
model AssignmentProgress {
  id           String     @id @default(cuid())
  type         String     // Tipo de progreso
  completed    Int        // Cantidad completada
  assignmentId String
  assignment   Assignment @relation(...)
}
```
**Propósito**: Tracking de progreso en asignaciones

## 📄 Modelos de Remisiones

### Remission
```prisma
model Remission {
  id           String    @id @default(cuid())
  folio        String    @unique // Folio único
  contractorId String
  notes        String?
  orderDetails Json?     // Detalles de orden
  printedAt    DateTime? // Fecha de impresión
  status       String    @default("ACTIVE")
  // Relaciones
  contractor   Contractor @relation(...)
  assignments  RemissionAssignment[]
  items        RemissionItem[]
  history      RemissionHistory[]
}
```
**Propósito**: Documento de entrega/remisión

### RemissionAssignment
```prisma
model RemissionAssignment {
  id           String @id @default(cuid())
  remissionId  String
  assignmentId String
  remission    Remission  @relation(...)
  assignment   Assignment @relation(...)
  
  @@unique([remissionId, assignmentId])
  @@index([remissionId])
  @@index([assignmentId])
}
```
**Propósito**: Vincula asignaciones con remisiones

### RemissionItem
```prisma
model RemissionItem {
  id          String    @id @default(cuid())
  remissionId String
  modelCode   String
  colorName   String
  sizeCode    String
  quantity    Int
  remission   Remission @relation(...)
}
```
**Propósito**: Detalle de items en remisión

### FolioSequence
```prisma
model FolioSequence {
  id           String   @id @default("single")
  lastDate     String   // Última fecha usada
  lastSequence Int      // Último número secuencial
  updatedAt    DateTime @updatedAt
}
```
**Propósito**: Generación de folios únicos secuenciales

## 📝 Modelos de Notas

### Note
```prisma
model Note {
  id           String         @id @default(cuid())
  content      String
  statusId     String
  authorId     String
  orderId      String
  importanceId String
  mentions     String[]       @default([]) // Menciones @usuario
  tags         String[]       @default([]) // Hashtags
  // Relaciones
  author       User           @relation(...)
  importance   NoteImportance @relation(...)
  order        Order          @relation(...)
  status       NoteStatus     @relation(...)
  comments     NoteComment[]
  likes        NoteLike[]
  
  @@index([orderId, createdAt])
  @@index([authorId])
  @@index([importanceId])
  @@index([statusId])
}
```
**Propósito**: Notas/observaciones en órdenes

### NoteComment
```prisma
model NoteComment {
  id        String        @id @default(cuid())
  content   String
  noteId    String
  authorId  String
  parentId  String?       // Para respuestas anidadas
  author    User          @relation(...)
  note      Note          @relation(...)
  parent    NoteComment?  @relation("CommentReplies", ...)
  replies   NoteComment[] @relation("CommentReplies")
  
  @@index([noteId])
  @@index([authorId])
  @@index([parentId])
}
```
**Propósito**: Comentarios en notas con anidación

### NoteLike
```prisma
model NoteLike {
  id     String @id @default(cuid())
  noteId String
  userId String
  note   Note   @relation(...)
  user   User   @relation(...)
  
  @@unique([noteId, userId])
  @@index([noteId])
  @@index([userId])
}
```
**Propósito**: Likes en notas (uno por usuario)

### NoteStatus
```prisma
model NoteStatus {
  id       String @id @default(cuid())
  name     String @unique
  iconName String?
  color    String?
  notes    Note[]
}
```
**Propósito**: Estados de notas

### NoteImportance
```prisma
model NoteImportance {
  id       String @id @default(cuid())
  name     String @unique
  iconName String?
  color    String?
  notes    Note[]
}
```
**Propósito**: Niveles de importancia de notas

## 📊 Modelos de Auditoría

### OperationLog
```prisma
model OperationLog {
  id            String    @id @default(cuid())
  operationType String    // Tipo de operación
  entityIds     String[]  // IDs afectados
  userId        String    // Quién inició
  status        String    // Estado de operación
  metadata      Json      // Datos específicos
  error         String?   // Errores si falló
  startedAt     DateTime  @default(now())
  completedAt   DateTime?
  compensatedAt DateTime? // Para rollback
  user          User      @relation(...)
  
  @@index([status, startedAt])
  @@index([operationType, status])
  @@index([userId])
  @@index([startedAt])
}
```
**Propósito**: Log de operaciones para integridad y auditoría

### OrderStatusHistory
```prisma
model OrderStatusHistory {
  id        String      @id @default(cuid())
  orderId   String
  statusId  String
  userId    String
  createdAt DateTime    @default(now())
  order     Order       @relation(...)
  status    OrderStatus @relation(...)
  changedBy User        @relation(...)
  
  @@index([createdAt])
}
```
**Propósito**: Historial de cambios de estado en órdenes

## 🔧 Características del Esquema

### Campos Comunes
- `id`: CUID por defecto
- `createdAt`: Timestamp de creación
- `updatedAt`: Timestamp de actualización
- `deletedAt`: Para soft deletes (donde aplica)

### Índices
- Optimizados para queries frecuentes
- Índices compuestos para relaciones
- Índices en campos de búsqueda

### Constraints
- `@unique` para campos únicos
- `@@unique` para combinaciones únicas
- Cascada en deletes donde corresponde

### Relaciones
- One-to-many predominante
- Many-to-many a través de tablas pivot
- Self-relations para comentarios anidados

## 🚀 Optimizaciones

1. **Índices estratégicos**: En campos de búsqueda y filtrado
2. **Selects específicos**: Evitar cargar datos innecesarios
3. **Soft deletes**: Mantener historial sin perder datos
4. **JSON fields**: Para datos flexibles (orderDetails, metadata)
5. **Counts agregados**: Uso de `_count` sin cargar relaciones

## 📈 Estadísticas del Esquema

- **Total de modelos**: 27
- **Modelos principales**: Order, Assignment, Remission
- **Modelos de catálogo**: 9 (Status, Color, Size, etc.)
- **Modelos de auditoría**: 2 (OperationLog, OrderStatusHistory)
- **Relaciones más complejas**: Order (8 relaciones directas)