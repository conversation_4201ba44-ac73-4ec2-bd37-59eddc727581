import React from "react";
import {
    UserIcon,
    ClipboardDocumentListIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";

import { Tabs, Tab, Badge } from "@/shared/components/ui/hero-ui-client";

import { TabType } from "../types/contractor-modal.types";

interface ContractorModalTabsProps {
    activeTab: TabType;
    onTabChange: (tab: TabType) => void;
    assignmentCount: number;
    remissionCount: number;
}

export default function ContractorModalTabs({
    activeTab,
    onTabChange,
    assignmentCount,
    remissionCount,
}: ContractorModalTabsProps) {
    return (
        <div className="px-6 bg-gray-50 dark:bg-gray-900">
            <Tabs
                classNames={{
                    tabList:
                        "gap-6 w-full relative rounded-none p-0 border-b border-gray-200 dark:border-gray-700 bg-transparent",
                    cursor: "w-full bg-primary dark:bg-primary shadow-sm",
                    tab: "max-w-fit px-3 h-12",
                    tabContent:
                        "group-data-[selected=true]:text-primary-foreground group-data-[selected=true]:font-medium transition-colors",
                }}
                selectedKey={activeTab}
                onSelectionChange={(key) => onTabChange(key as TabType)}
            >
                <Tab
                    key="general"
                    title={
                        <div className="flex items-center gap-2">
                            <UserIcon className="w-4 h-4" />
                            <span>Información General</span>
                        </div>
                    }
                />
                <Tab
                    key="assignments"
                    title={
                        <div className="flex items-center gap-2">
                            <ClipboardDocumentListIcon className="w-4 h-4" />
                            <span>Asignaciones</span>
                            {assignmentCount > 0 && (
                                <Badge
                                    className="ml-1"
                                    color="primary"
                                    size="sm"
                                >
                                    {assignmentCount}
                                </Badge>
                            )}
                        </div>
                    }
                />
                <Tab
                    key="history"
                    title={
                        <div className="flex items-center gap-2">
                            <ClockIcon className="w-4 h-4" />
                            <span>Historial</span>
                        </div>
                    }
                />
            </Tabs>
        </div>
    );
}
