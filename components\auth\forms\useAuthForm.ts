"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";

import { loginSchema, registerSchema } from "@/lib/auth/validation";
import { refreshCSRFToken } from "@/lib/auth/csrf-helper";

type FormType = "login" | "register";

interface UseAuthFormOptions {
    type: FormType;
    callbackUrl?: string;
    onSuccess?: () => void;
    onError?: (error: Error) => void;
}

// Función para traducir errores de NextAuth a mensajes amigables
function getErrorMessage(error: string): string {
    const errorMessages: Record<string, string> = {
        CredentialsSignin: "Correo electrónico o contraseña incorrectos",
        OAuthSignin: "Error al iniciar sesión con el proveedor",
        OAuthCallback: "Error al procesar la respuesta del proveedor",
        OAuthCreateAccount: "Error al crear la cuenta",
        EmailCreateAccount:
            "Error al crear la cuenta con el correo electrónico",
        Callback: "Error en el proceso de autenticación",
        OAuthAccountNotLinked:
            "Este correo ya está registrado con otro método de inicio de sesión",
        EmailSignin: "Error al enviar el correo de verificación",
        CredentialsSignup: "Error al crear la cuenta",
        SessionRequired: "Debes iniciar sesión para acceder a esta página",
        MissingCSRF:
            "Error de seguridad. Por favor, recarga la página e intenta nuevamente",
        Default: "Ha ocurrido un error inesperado",
    };

    return errorMessages[error] || errorMessages["Default"];
}

export function useAuthForm({
    type,
    callbackUrl = "/dashboard",
    onSuccess,
    onError,
}: UseAuthFormOptions) {
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();

    const schema = type === "login" ? loginSchema : registerSchema;

    const form = useForm({
        resolver: zodResolver(schema),
        mode: "onBlur",
        defaultValues:
            type === "login"
                ? { email: "", password: "", rememberMe: false }
                : ({
                      name: "",
                      email: "",
                      password: "",
                      confirmPassword: "",
                      acceptTerms: false,
                  } as any),
    });

    const onSubmit = async (data: z.infer<typeof schema>) => {
        setIsLoading(true);

        // Limpiar errores previos
        form.clearErrors("root");

        try {
            if (type === "login") {
                // Primero obtener el token CSRF
                const csrfToken = await refreshCSRFToken();

                console.log("🔐 Iniciando proceso de login...");
                console.log("📧 Email:", (data as any).email);
                console.log("🔑 CSRF Token:", csrfToken);

                // Usar NextAuth signIn para login con el token CSRF incluido
                let result = await signIn("credentials", {
                    email: (data as any).email,
                    password: (data as any).password,
                    redirect: false,
                    csrfToken: csrfToken || undefined,
                });

                // Debug log mejorado
                console.log("📋 SignIn result:", result);
                console.log("✅ SignIn ok?:", result?.ok);
                console.log("❌ SignIn error?:", result?.error);
                console.log("🔗 SignIn url?:", result?.url);

                // Si hay error de CSRF, intentar refrescar el token y reintentar una vez
                if (result?.error === "MissingCSRF") {
                    console.error("CSRF Error Details:", {
                        error: result.error,
                        url: window.location.href,
                        cookies: document.cookie,
                        timestamp: new Date().toISOString(),
                    });

                    // Intentar refrescar el token CSRF
                    console.log("Attempting to refresh CSRF token...");
                    const newToken = await refreshCSRFToken();

                    if (newToken) {
                        console.log("CSRF token refreshed, retrying login...");
                        // Reintentar el login una vez con el nuevo token
                        const retryResult = await signIn("credentials", {
                            email: (data as any).email,
                            password: (data as any).password,
                            redirect: false,
                            csrfToken: newToken,
                        });

                        if (!retryResult?.error) {
                            // Si el reintento fue exitoso, continuar con el flujo normal
                            console.log(
                                "Login exitoso después de reintento CSRF, redirigiendo a:",
                                callbackUrl,
                            );
                            onSuccess?.();

                            setTimeout(() => {
                                window.location.href = callbackUrl;
                            }, 100);

                            return;
                        }

                        // Si el reintento también falló, usar ese resultado
                        result = retryResult;
                    }
                }

                if (result?.error) {
                    // Traducir el error técnico a un mensaje amigable
                    const friendlyMessage = getErrorMessage(result.error);

                    // Establecer el error en el formulario de manera más robusta
                    form.setError("root", {
                        type: "manual",
                        message: friendlyMessage,
                    });

                    // Asegurar que el error se muestre
                    console.error(
                        "Login error:",
                        result.error,
                        "→",
                        friendlyMessage,
                    );

                    // Llamar al callback de error si existe
                    if (onError) {
                        onError(new Error(friendlyMessage));
                    }

                    return; // Importante: no continuar con el flujo
                }

                // Solo redirigir si el login fue exitoso
                if (result?.ok) {
                    console.log("Login exitoso, redirigiendo a:", callbackUrl);
                    // Llamar al callback de éxito
                    onSuccess?.();

                    // Pequeño delay para asegurar que la sesión se actualice
                    setTimeout(() => {
                        // Usar window.location para forzar una navegación completa
                        // Esto asegura que el middleware se ejecute correctamente
                        window.location.href = callbackUrl;
                    }, 100);
                }
            } else {
                // Para registro, primero crear el usuario via API
                const response = await fetch("/api/auth/register", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify(data),
                });

                if (!response.ok) {
                    const error = await response.json();

                    throw new Error(error.message || "Error al registrarse");
                }

                // Obtener token CSRF para el login automático
                const csrfToken = await refreshCSRFToken();

                // Luego hacer login automático con NextAuth
                const loginResult = await signIn("credentials", {
                    email: (data as any).email,
                    password: (data as any).password,
                    redirect: false,
                    csrfToken: csrfToken || undefined,
                });

                if (loginResult?.error) {
                    throw new Error(loginResult.error);
                }

                // Redirección después del registro exitoso
                console.log(
                    "Registro y login exitoso, redirigiendo a:",
                    callbackUrl,
                );
                onSuccess?.();

                setTimeout(() => {
                    window.location.href = callbackUrl;
                }, 100);
            }
        } catch (error: any) {
            console.error("Auth error (catch block):", error);

            // Determinar el mensaje de error apropiado
            let errorMessage = "Ha ocurrido un error inesperado";

            if (error instanceof Error) {
                errorMessage = error.message;
            } else if (typeof error === "string") {
                errorMessage = getErrorMessage(error);
            } else if (error?.message) {
                errorMessage = error.message;
            }

            // Establecer el error en el formulario de manera robusta
            form.setError("root", {
                type: "manual",
                message: errorMessage,
            });

            // Forzar actualización del estado del formulario
            form.trigger();

            if (onError) {
                onError(
                    error instanceof Error ? error : new Error(errorMessage),
                );
            }
        } finally {
            setIsLoading(false);
        }
    };

    return {
        form,
        onSubmit: form.handleSubmit(onSubmit),
        isLoading,
        errors: form.formState.errors,
    };
}
