/**
 * Request Deduplication Utility
 * Prevents duplicate API requests within a time window
 */

interface PendingRequest {
    promise: Promise<any>;
    timestamp: number;
}

class RequestDeduplicator {
    private pendingRequests = new Map<string, PendingRequest>();
    private readonly TTL = 16; // 16ms window for batching (matches DataLoader)

    /**
     * Deduplicate a request
     * @param key Unique key for the request
     * @param fetcher Function that performs the actual request
     * @returns Promise with the result
     */
    async deduplicate<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
        // Check if there's a pending request
        const pending = this.pendingRequests.get(key);

        if (pending && Date.now() - pending.timestamp < this.TTL) {
            // Return existing promise
            return pending.promise as Promise<T>;
        }

        // Create new request
        const promise = fetcher().finally(() => {
            // Clean up after request completes
            setTimeout(() => {
                this.pendingRequests.delete(key);
            }, this.TTL);
        });

        // Store the pending request
        this.pendingRequests.set(key, {
            promise,
            timestamp: Date.now(),
        });

        return promise;
    }

    /**
     * Clear all pending requests
     */
    clear() {
        this.pendingRequests.clear();
    }
}

// Export singleton instance
export const requestDeduplicator = new RequestDeduplicator();
