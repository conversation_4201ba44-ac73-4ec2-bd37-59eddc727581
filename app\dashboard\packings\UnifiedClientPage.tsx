"use client";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, Card, CardBody, useDisclosure } from "@heroui/react";
import {
    DocumentPlusIcon,
    TruckIcon,
    CubeIcon,
    CalendarDaysIcon,
    ClockIcon,
    ViewColumnsIcon,
    Squares2X2Icon,
} from "@heroicons/react/24/outline";
import { format, startOfMonth } from "date-fns";
import { es } from "date-fns/locale";

import {
    PackingGrid,
    PackingFilters,
    PackingDetailModal,
    PackingDocument,
} from "@/features/packings/components";
import { usePackings, usePackingPDF } from "@/features/packings/hooks";
import { DashboardHeader } from "@/shared/components/dashboard";

// Simulación de estados de packing
const packingStatuses = [
    { id: "1", name: "Pendiente" },
    { id: "2", name: "En Proceso" },
    { id: "3", name: "Listo para Entrega" },
    { id: "4", name: "<PERSON><PERSON><PERSON>" },
    { id: "5", name: "Cancelado" },
];

export default function UnifiedPackingsPage() {
    const router = useRouter();
    const [filters, setFilters] = useState({});
    const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
    const [selectedPacking, setSelectedPacking] = useState<any>(null);
    const { isOpen, onOpen, onClose } = useDisclosure();
    const { printPacking, isGenerating } = usePackingPDF();

    const { packings, isLoading, mutate } = usePackings(filters);

    // Calcular métricas
    const metrics = useMemo(() => {
        if (!packings)
            return {
                total: 0,
                pending: 0,
                delivered: 0,
                thisMonth: 0,
            };

        const firstDayOfMonth = startOfMonth(new Date());

        return {
            total: packings.length,
            pending: packings.filter((p) => p.status.name === "Pendiente")
                .length,
            delivered: packings.filter((p) => p.status.name === "Entregado")
                .length,
            thisMonth: packings.filter(
                (p) => new Date(p.createdAt) >= firstDayOfMonth,
            ).length,
        };
    }, [packings]);

    const handlePrint = async (packing: any) => {
        setSelectedPacking(packing);
        setTimeout(async () => {
            await printPacking(packing.id);
            mutate();
        }, 100);
    };

    const renderMetrics = () => (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[
                {
                    label: "Total Packings",
                    value: metrics.total,
                    icon: CubeIcon,
                    color: "primary",
                    trend: "+12%",
                },
                {
                    label: "Pendientes",
                    value: metrics.pending,
                    icon: ClockIcon,
                    color: "warning",
                    trend:
                        metrics.pending > 0
                            ? `${metrics.pending} activos`
                            : "Sin pendientes",
                },
                {
                    label: "Entregados",
                    value: metrics.delivered,
                    icon: TruckIcon,
                    color: "success",
                    trend: "95% completados",
                },
                {
                    label: "Este Mes",
                    value: metrics.thisMonth,
                    icon: CalendarDaysIcon,
                    color: "secondary",
                    trend: format(new Date(), "MMMM", { locale: es }),
                },
            ].map((metric, index) => (
                <motion.div
                    key={metric.label}
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ delay: index * 0.1 }}
                >
                    <Card>
                        <CardBody className="flex flex-row items-center justify-between p-4">
                            <div className="space-y-1">
                                <p className="text-sm text-gray-500">
                                    {metric.label}
                                </p>
                                <p className="text-2xl font-bold">
                                    {metric.value}
                                </p>
                                <p className="text-xs text-gray-400">
                                    {metric.trend}
                                </p>
                            </div>
                            <div
                                className={`p-3 rounded-full bg-${metric.color}/10`}
                            >
                                <metric.icon
                                    className={`w-6 h-6 text-${metric.color}`}
                                />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            ))}
        </div>
    );

    return (
        <div className="space-y-6">
            <DashboardHeader
                actions={
                    <div className="flex items-center gap-2">
                        <div className="flex gap-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
                            <Button
                                isIconOnly
                                size="sm"
                                variant={
                                    viewMode === "grid" ? "solid" : "light"
                                }
                                onPress={() => setViewMode("grid")}
                            >
                                <Squares2X2Icon className="w-4 h-4" />
                            </Button>
                            <Button
                                isIconOnly
                                size="sm"
                                variant={
                                    viewMode === "list" ? "solid" : "light"
                                }
                                onPress={() => setViewMode("list")}
                            >
                                <ViewColumnsIcon className="w-4 h-4" />
                            </Button>
                        </div>

                        <Button
                            color="primary"
                            startContent={
                                <DocumentPlusIcon className="w-5 h-5" />
                            }
                            onPress={() =>
                                router.push("/dashboard/packings/new")
                            }
                        >
                            Nuevo Packing
                        </Button>
                    </div>
                }
                subtitle="Administra los packing lists de tus entregas"
                title="Gestión de Packings"
            />

            {renderMetrics()}

            <PackingFilters
                defaultFilters={filters}
                packingStatuses={packingStatuses}
                onFiltersChange={setFilters}
            />

            <PackingGrid
                isLoading={isLoading}
                packings={packings}
                viewMode={viewMode}
                onPrint={handlePrint}
                onViewDetails={(packing) => {
                    setSelectedPacking(packing);
                    onOpen();
                }}
            />

            {selectedPacking && (
                <>
                    <PackingDetailModal
                        isOpen={isOpen}
                        packing={selectedPacking}
                        onClose={onClose}
                        onPrint={() => handlePrint(selectedPacking)}
                        onUpdateStatus={() => {
                            router.push(
                                `/dashboard/packings/${selectedPacking.id}/edit`,
                            );
                        }}
                    />

                    {/* Documento oculto para impresión */}
                    <div className="hidden">
                        <PackingDocument packing={selectedPacking} />
                    </div>
                </>
            )}
        </div>
    );
}
