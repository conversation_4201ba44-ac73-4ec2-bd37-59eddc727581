"use client";

import { Card, CardBody, Chip, Avatar, Skeleton } from "@heroui/react";
import {
    UserCircleIcon,
    PhoneIcon,
    EnvelopeIcon,
    MapPinIcon,
    SparklesIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import { useMemo } from "react";
import { motion } from "framer-motion";

import { useContractor } from "@/features/contractors/hooks/useContractor";

interface ContractorInfoProps {
    contractorId: string;
    isChip?: boolean;
    showFullInfo?: boolean;
}

export function ContractorInfo({
    contractorId,
    isChip = false,
    showFullInfo = false,
}: ContractorInfoProps) {
    // No renderizar nada si no hay contractorId
    if (!contractorId) {
        return isChip ? (
            <motion.div
                animate={{ scale: 1, opacity: 1 }}
                initial={{ scale: 0.9, opacity: 0 }}
                transition={{ type: "spring", duration: 0.3 }}
            >
                <Chip
                    classNames={{
                        base: "h-auto py-2 px-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 border border-gray-300 dark:border-gray-600",
                        content: "flex items-center gap-2 font-medium",
                    }}
                    variant="flat"
                >
                    <UserCircleIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-300">
                        Sin contratista
                    </span>
                </Chip>
            </motion.div>
        ) : (
            <motion.div
                animate={{ opacity: 1 }}
                className="flex items-center justify-center p-8"
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
            >
                <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-full flex items-center justify-center">
                        <UserCircleIcon className="w-8 h-8 text-gray-500 dark:text-gray-400" />
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 font-medium">
                        No se ha seleccionado un contratista
                    </p>
                </div>
            </motion.div>
        );
    }

    const { contractor, isLoading, error } = useContractor(contractorId);

    // Memoizar el nombre del contratista para evitar recálculos
    const contractorName = useMemo(() => {
        if (isLoading || error || !contractor) {
            return isLoading ? "Cargando..." : "Contratista";
        }

        return `${contractor.firstName || ""} ${contractor.lastName || ""}`;
    }, [contractor, isLoading, error]);

    if (isChip) {
        return (
            <motion.div
                animate={{ scale: 1, opacity: 1 }}
                initial={{ scale: 0.9, opacity: 0 }}
                transition={{ type: "spring", duration: 0.3 }}
                whileHover={{ scale: 1.05 }}
            >
                <Chip
                    classNames={{
                        base: "h-auto py-2 px-3 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-200 dark:border-blue-700 shadow-md hover:shadow-lg transition-all",
                        content: "flex items-center gap-2 font-medium",
                    }}
                    variant="flat"
                >
                    <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <UserCircleIcon className="w-4 h-4 text-white" />
                    </div>
                    <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold">
                        {contractorName}
                    </span>
                </Chip>
            </motion.div>
        );
    }

    if (isLoading) {
        return (
            <motion.div
                animate={{ opacity: 1 }}
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                        <UserCircleIcon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Contratista
                    </h3>
                </div>
                <Card className="backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 border border-gray-200/50 dark:border-gray-700/50 shadow-xl">
                    <CardBody className="p-6">
                        <div className="flex items-center gap-4">
                            <Skeleton className="w-16 h-16 rounded-full" />
                            <div className="flex-1 space-y-2">
                                <Skeleton className="h-6 w-48 rounded-lg" />
                                <Skeleton className="h-4 w-32 rounded-lg" />
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>
        );
    }

    if (error) {
        return (
            <motion.div
                animate={{ opacity: 1 }}
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-gradient-to-br from-red-500 to-orange-600 rounded-xl shadow-lg">
                        <UserCircleIcon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-red-600 dark:text-red-400">
                        Contratista
                    </h3>
                </div>
                <Card className="backdrop-blur-xl bg-red-50/80 dark:bg-red-900/20 border border-red-200 dark:border-red-800 shadow-xl">
                    <CardBody className="p-6">
                        <div
                            className="flex items-center gap-3 text-red-600 dark:text-red-400"
                            role="alert"
                        >
                            <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-full">
                                <ExclamationCircleIcon className="w-5 h-5" />
                            </div>
                            <p className="font-medium">
                                Error al cargar datos del contratista
                            </p>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>
        );
    }

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.4, type: "spring" }}
        >
            {!showFullInfo && (
                <div className="flex items-center gap-3 mb-4">
                    <motion.div
                        className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg"
                        transition={{ type: "spring", stiffness: 300 }}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                        <UserCircleIcon className="w-6 h-6 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Contratista
                    </h3>
                </div>
            )}
            {contractor && (
                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    initial={{ opacity: 0, scale: 0.95 }}
                    transition={{ delay: 0.1, type: "spring" }}
                >
                    <Card className="backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 border border-gray-200/50 dark:border-gray-700/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 pointer-events-none rounded-xl" />
                        <CardBody className="relative z-10 p-6">
                            <div className="flex items-start gap-4">
                                <motion.div
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.1, rotate: -5 }}
                                >
                                    <Avatar
                                        className="w-16 h-16 text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-xl"
                                        name={
                                            contractor.firstName?.charAt(0) ||
                                            "C"
                                        }
                                    />
                                </motion.div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <p className="text-xl font-bold text-gray-800 dark:text-gray-100">
                                            {contractor.firstName}{" "}
                                            {contractor.middleName}{" "}
                                            {contractor.lastName}{" "}
                                            {contractor.secondLastName}
                                        </p>
                                        <motion.div
                                            animate={{ scale: 1 }}
                                            initial={{ scale: 0 }}
                                            transition={{
                                                delay: 0.3,
                                                type: "spring",
                                            }}
                                        >
                                            <Chip
                                                className="bg-gradient-to-r from-green-400 to-emerald-500 text-white font-medium"
                                                size="sm"
                                                variant="flat"
                                            >
                                                <SparklesIcon className="w-3 h-3 mr-1" />
                                                Activo
                                            </Chip>
                                        </motion.div>
                                    </div>

                                    {showFullInfo && (
                                        <motion.div
                                            animate={{
                                                opacity: 1,
                                                height: "auto",
                                            }}
                                            className="space-y-2 mt-3"
                                            initial={{ opacity: 0, height: 0 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            {contractor.email && (
                                                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                                    <EnvelopeIcon className="w-4 h-4" />
                                                    <span className="text-sm">
                                                        {contractor.email}
                                                    </span>
                                                </div>
                                            )}
                                            {contractor.phone && (
                                                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                                    <PhoneIcon className="w-4 h-4" />
                                                    <span className="text-sm">
                                                        {contractor.phone}
                                                    </span>
                                                </div>
                                            )}
                                            {(contractor as any).address && (
                                                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                                    <MapPinIcon className="w-4 h-4" />
                                                    <span className="text-sm">
                                                        {
                                                            (contractor as any)
                                                                .address
                                                        }
                                                    </span>
                                                </div>
                                            )}
                                            {contractor.notes && (
                                                <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
                                                    <p className="text-sm text-gray-700 dark:text-gray-300">
                                                        {contractor.notes}
                                                    </p>
                                                </div>
                                            )}
                                        </motion.div>
                                    )}
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            )}
        </motion.div>
    );
}
