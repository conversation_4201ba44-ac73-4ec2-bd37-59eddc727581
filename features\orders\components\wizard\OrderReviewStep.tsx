"use client";

import React from "react";
import {
    CheckCircleIcon,
    DocumentIcon,
    ShoppingBagIcon,
    UserIcon,
    CalendarIcon,
    TagIcon,
    PencilIcon,
} from "@heroicons/react/24/outline";

import {
    <PERSON>,
    CardBody,
    CardHeader,
    Chip,
    Button,
    Divider,
} from "@/shared/components/ui/hero-ui-client";
import { IconWrapper } from "@/shared/components/ui/IconWrapper";

interface OrderReviewStepProps {
    formData: {
        customerId: string;
        statusId: string;
        transferNumber: string;
        cutOrder: string;
        batch: string;
        parts: Array<{ code: string }>;
        garments: Array<{
            modelId: string;
            colorId: string;
            sizes: Array<{ sizeId: string; quantity: string }>;
        }>;
    };
    receivedDate: string;
    estimatedDeliveryDate: string;
    customers: Array<{ id: string; name: string }>;
    orderStatuses: Array<{ id: string; name: string; color?: string }>;
    models: Array<{ id: string; code: string; description?: string }>;
    colors: Array<{ id: string; name: string; code: string }>;
    sizesOptions: Array<{ id: string; code: string }>;
    onEditStep: (step: number) => void;
}

export function OrderReviewStep({
    formData,
    receivedDate,
    estimatedDeliveryDate,
    customers,
    orderStatuses,
    models,
    colors,
    sizesOptions,
    onEditStep,
}: OrderReviewStepProps) {
    // Get display names
    const customer = customers.find((c) => c.id === formData.customerId);
    const status = orderStatuses.find((s) => s.id === formData.statusId);

    // Calculate totals
    const totalParts = formData.parts.filter((p) => p.code.trim()).length;
    const totalGarments = formData.garments.length;
    const totalPieces = formData.garments.reduce((total, garment) => {
        return (
            total +
            garment.sizes.reduce(
                (sum, size) => sum + (parseInt(size.quantity) || 0),
                0,
            )
        );
    }, 0);

    return (
        <div className="space-y-6">
            {/* Success Message */}
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center gap-3">
                    <IconWrapper color="success" size="md" variant="soft">
                        <CheckCircleIcon className="w-5 h-5" />
                    </IconWrapper>
                    <div>
                        <h3 className="font-semibold text-green-800 dark:text-green-200">
                            ¡Todo listo!
                        </h3>
                        <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                            Revisa los detalles de la orden antes de crearla.
                        </p>
                    </div>
                </div>
            </div>

            {/* Basic Information */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <IconWrapper color="primary" size="sm" variant="soft">
                            <TagIcon className="w-4 h-4" />
                        </IconWrapper>
                        <h3 className="font-semibold">Información Básica</h3>
                    </div>
                    <Button
                        size="sm"
                        startContent={<PencilIcon className="w-3 h-3" />}
                        variant="light"
                        onPress={() => onEditStep(0)}
                    >
                        Editar
                    </Button>
                </CardHeader>
                <CardBody className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center gap-3">
                            <UserIcon className="w-4 h-4 text-gray-400" />
                            <div>
                                <p className="text-xs text-gray-500">Cliente</p>
                                <p className="font-medium">
                                    {customer?.name || "No seleccionado"}
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            <div
                                className="w-4 h-4 rounded-full"
                                style={{
                                    backgroundColor: status?.color || "#gray",
                                }}
                            />
                            <div>
                                <p className="text-xs text-gray-500">Estado</p>
                                <p className="font-medium">
                                    {status?.name || "No seleccionado"}
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            <CalendarIcon className="w-4 h-4 text-gray-400" />
                            <div>
                                <p className="text-xs text-gray-500">
                                    Fecha de recepción
                                </p>
                                <p className="font-medium">
                                    {new Date(
                                        receivedDate,
                                    ).toLocaleDateString()}
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            <CalendarIcon className="w-4 h-4 text-gray-400" />
                            <div>
                                <p className="text-xs text-gray-500">
                                    Entrega estimada
                                </p>
                                <p className="font-medium">
                                    {new Date(
                                        estimatedDeliveryDate,
                                    ).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                    </div>

                    {(formData.transferNumber ||
                        formData.cutOrder ||
                        formData.batch) && (
                        <>
                            <Divider />
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {formData.transferNumber && (
                                    <div>
                                        <p className="text-xs text-gray-500">
                                            Núm. Transferencia
                                        </p>
                                        <p className="font-medium">
                                            {formData.transferNumber}
                                        </p>
                                    </div>
                                )}
                                {formData.cutOrder && (
                                    <div>
                                        <p className="text-xs text-gray-500">
                                            Orden de Corte
                                        </p>
                                        <p className="font-medium">
                                            {formData.cutOrder}
                                        </p>
                                    </div>
                                )}
                                {formData.batch && (
                                    <div>
                                        <p className="text-xs text-gray-500">
                                            Lote
                                        </p>
                                        <p className="font-medium">
                                            {formData.batch}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </>
                    )}
                </CardBody>
            </Card>

            {/* Parts */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <IconWrapper color="primary" size="sm" variant="soft">
                            <DocumentIcon className="w-4 h-4" />
                        </IconWrapper>
                        <h3 className="font-semibold">
                            Partidas ({totalParts})
                        </h3>
                    </div>
                    <Button
                        size="sm"
                        startContent={<PencilIcon className="w-3 h-3" />}
                        variant="light"
                        onPress={() => onEditStep(1)}
                    >
                        Editar
                    </Button>
                </CardHeader>
                <CardBody>
                    <div className="flex flex-wrap gap-2">
                        {formData.parts
                            .filter((p) => p.code.trim())
                            .map((part, index) => (
                                <Chip
                                    key={index}
                                    color="primary"
                                    variant="flat"
                                >
                                    {part.code}
                                </Chip>
                            ))}
                    </div>
                </CardBody>
            </Card>

            {/* Garments */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <IconWrapper color="primary" size="sm" variant="soft">
                            <ShoppingBagIcon className="w-4 h-4" />
                        </IconWrapper>
                        <h3 className="font-semibold">
                            Prendas ({totalGarments})
                        </h3>
                    </div>
                    <Button
                        size="sm"
                        startContent={<PencilIcon className="w-3 h-3" />}
                        variant="light"
                        onPress={() => onEditStep(2)}
                    >
                        Editar
                    </Button>
                </CardHeader>
                <CardBody className="space-y-4">
                    {formData.garments.map((garment, index) => {
                        const model = models.find(
                            (m) => m.id === garment.modelId,
                        );
                        const color = colors.find(
                            (c) => c.id === garment.colorId,
                        );
                        const garmentTotal = garment.sizes.reduce(
                            (sum, size) => sum + (parseInt(size.quantity) || 0),
                            0,
                        );

                        return (
                            <div
                                key={index}
                                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                            >
                                <div className="flex items-start justify-between mb-3">
                                    <div className="space-y-1">
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">
                                                Modelo:
                                            </span>
                                            <span>
                                                {model?.code ||
                                                    "No seleccionado"}
                                            </span>
                                            {model?.description && (
                                                <span className="text-xs text-gray-500">
                                                    ({model.description})
                                                </span>
                                            )}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">
                                                Color:
                                            </span>
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-4 h-4 rounded border border-gray-300"
                                                    style={{
                                                        backgroundColor:
                                                            color?.code ||
                                                            "#gray",
                                                    }}
                                                />
                                                <span>
                                                    {color?.name ||
                                                        "No seleccionado"}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <Chip
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {garmentTotal} piezas
                                    </Chip>
                                </div>

                                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
                                    {garment.sizes
                                        .filter(
                                            (s) =>
                                                s.sizeId &&
                                                parseInt(s.quantity) > 0,
                                        )
                                        .map((size, sIndex) => {
                                            const sizeOption =
                                                sizesOptions.find(
                                                    (so) =>
                                                        so.id === size.sizeId,
                                                );

                                            return (
                                                <div
                                                    key={sIndex}
                                                    className="bg-gray-50 dark:bg-gray-800 rounded px-3 py-2 text-center"
                                                >
                                                    <p className="text-xs text-gray-500">
                                                        {sizeOption?.code ||
                                                            "N/A"}
                                                    </p>
                                                    <p className="font-semibold">
                                                        {size.quantity}
                                                    </p>
                                                </div>
                                            );
                                        })}
                                </div>
                            </div>
                        );
                    })}

                    <Divider />

                    <div className="flex items-center justify-between">
                        <span className="font-semibold text-lg">
                            Total General:
                        </span>
                        <Chip color="primary" size="lg" variant="solid">
                            {totalPieces} piezas
                        </Chip>
                    </div>
                </CardBody>
            </Card>

            {/* Final Message */}
            <div className="text-center p-6">
                <p className="text-gray-600 dark:text-gray-400">
                    Presiona <strong>Guardar</strong> para crear la orden.
                </p>
            </div>
        </div>
    );
}
