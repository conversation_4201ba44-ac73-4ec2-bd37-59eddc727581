/**
 * DataLoader Pattern Implementation
 * Batches multiple requests into a single API call
 */

type BatchLoadFn<K, V> = (keys: K[]) => Promise<V[]>;

interface DataLoaderOptions {
    batchingWindowMs?: number;
    maxBatchSize?: number;
    cache?: boolean;
}

export class DataLoader<K, V> {
    private batchLoadFn: BatchLoadFn<K, V>;
    private options: Required<DataLoaderOptions>;
    private batch: Array<{
        key: K;
        resolve: (value: V) => void;
        reject: (error: any) => void;
    }> = [];
    private batchTimer: NodeJS.Timeout | null = null;
    private cache = new Map<K, Promise<V>>();

    constructor(
        batchLoadFn: BatchLoadFn<K, V>,
        options: DataLoaderOptions = {},
    ) {
        this.batchLoadFn = batchLoadFn;
        this.options = {
            batchingWindowMs: options.batchingWindowMs ?? 16,
            maxBatchSize: options.maxBatchSize ?? 100,
            cache: options.cache ?? true,
        };
    }

    async load(key: K): Promise<V> {
        // Check cache first
        if (this.options.cache && this.cache.has(key)) {
            return this.cache.get(key)!;
        }

        // Create promise for this key
        const promise = new Promise<V>((resolve, reject) => {
            this.batch.push({ key, resolve, reject });

            // Schedule batch execution
            if (!this.batchTimer) {
                this.batchTimer = setTimeout(
                    () => this.executeBatch(),
                    this.options.batchingWindowMs,
                );
            }

            // Execute immediately if batch is full
            if (this.batch.length >= this.options.maxBatchSize) {
                this.executeBatch();
            }
        });

        // Cache the promise
        if (this.options.cache) {
            this.cache.set(key, promise);
        }

        return promise;
    }

    private async executeBatch() {
        // Clear timer
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }

        // Extract current batch
        const currentBatch = this.batch;

        this.batch = [];

        if (currentBatch.length === 0) return;

        try {
            // Extract keys
            const keys = currentBatch.map((item) => item.key);

            // Call batch load function
            const values = await this.batchLoadFn(keys);

            // Resolve promises
            currentBatch.forEach((item, index) => {
                item.resolve(values[index]);
            });
        } catch (error) {
            // Reject all promises
            currentBatch.forEach((item) => {
                item.reject(error);
            });
        }
    }

    clearCache() {
        this.cache.clear();
    }

    clear(key: K) {
        this.cache.delete(key);
    }
}

// Example usage for orders
export const ordersLoader = new DataLoader<string, any>(
    async (ids: string[]) => {
        const response = await fetch(`/api/orders/batch?ids=${ids.join(",")}`);
        const data = await response.json();

        return ids.map((id) => data.find((order: any) => order.id === id));
    },
);
