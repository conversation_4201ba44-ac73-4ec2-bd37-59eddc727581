---
description: Visual Process Map para ARCHIVE Mode con auto-indexing
globs: archive-mode-map.mdc
alwaysApply: false
---
# 📚 ARCHIVE MODE - VISUAL PROCESS MAP

> Modo de documentación y preservación del conocimiento

## 📊 MAPA DE PROCESO COMPLETO

```mermaid
graph TD
    Start["📚 AUTO-FLOW ARCHIVE"] --> Check{"✅ Reflect<br>Completo?"}
    
    Check -->|No| Error["❌ Completar<br>REFLECT primero"]
    Check -->|Sí| Load["📂 Cargar todos<br>los documentos"]
    
    Load --> Collect["📑 Recopilar<br>Artefactos"]
    Collect --> Code["💻 Código Final"]
    Collect --> Docs["📄 Documentación"]
    Collect --> Decisions["🎨 Creative Decisions"]
    Collect --> Metrics["📊 Métricas Finales"]
    
    Code & Docs & Decisions & Metrics --> Structure["🏗️ Estructurar<br>Archivo"]
    
    Structure --> TOC["📑 Generar<br>Table of Contents"]
    TOC --> Summary["📝 Executive<br>Summary"]
    Summary --> Technical["🔧 Technical<br>Documentation"]
    Technical --> Lessons["💡 Lessons &<br>Patterns"]
    
    Lessons --> Index["🔍 Crear índice<br>🧠 visualreasoning"]
    Index --> Cross["🔗 Cross-reference<br>🧠 structuredargumentation"]
    
    Cross --> Generate["📄 Generar<br>archive-[project].md"]
    Generate --> Compress["🗜️ Comprimir<br>Artefactos"]
    
    Compress --> Update["💾 Update final<br>Memory Bank"]
    Update --> Cleanup["🧹 Limpiar<br>archivos temporales"]
    
    Cleanup --> Ready["✅ ARCHIVE Complete<br>→ Nuevo proyecto"]
    
    %% Clear Thought Tools
    Index -.-> CT1["🧠 visualreasoning<br>Crear diagramas finales"]
    Cross -.-> CT2["🧠 structuredargumentation<br>Documentar decisiones"]
    
    style Start fill:#d9b3ff
    style Check fill:#d94dbb
    style Ready fill:#5fd94d
    style Error fill:#ff5555
```

## 📄 ESTRUCTURA DEL ARCHIVO

```markdown
# 📚 Project Archive - [Project Name]
**Archived**: [ISO timestamp]
**Version**: [X.Y.Z]
**Complexity**: Level [1-4]
**Duration**: [Total time]

## 📑 Table of Contents
1. [Executive Summary](#executive-summary)
2. [Project Overview](#project-overview)
3. [Technical Architecture](#technical-architecture)
4. [Implementation Details](#implementation-details)
5. [Design Decisions](#design-decisions)
6. [Testing & Quality](#testing-quality)
7. [Metrics & Performance](#metrics-performance)
8. [Lessons Learned](#lessons-learned)
9. [Future Recommendations](#future-recommendations)
10. [Appendices](#appendices)

## 📝 Executive Summary
[1-2 paragraph summary of the entire project]

### Key Achievements
- ✅ [Achievement 1]
- ✅ [Achievement 2]
- ✅ [Achievement 3]

### Key Metrics
- **Time to Complete**: [X] hours ([Y]% of estimate)
- **Test Coverage**: [Z]%
- **Performance**: [Metrics]
- **Clear Thought Tools Used**: [Count] times

## 🎯 Project Overview
[Detailed project description, objectives, and scope]

## 🏗️ Technical Architecture
[Architecture diagrams and explanations]

### System Diagram
```mermaid
[Architecture diagram]
```

### Technology Stack
- Frontend: [Technologies]
- Backend: [Technologies]
- Database: [Technologies]
- Tools: [Technologies]

## 📋 SECCIONES DETALLADAS

### Implementation Details
```markdown
## 💻 Implementation Details

### Phase 1: [Name]
**Duration**: [Time]
**Components**: [List]

#### Key Code Snippets
```typescript
// Ejemplo de implementación clave
[Código representativo]
```

[Descripción de la implementación]

### Phase 2: [Name]
[Similar structure]
```

### Design Decisions Archive
```markdown
## 🎨 Design Decisions

### Decision 1: [Component Name]
**Date**: [Timestamp]
**Type**: [Architecture | Algorithm | UI/UX]

#### Options Considered
1. **Option A**: [Description]
   - Pros: [List]
   - Cons: [List]

2. **Option B**: [Description]
   - Pros: [List]
   - Cons: [List]

#### Selected Approach
**Choice**: Option [X]
**Rationale**: [Detailed reasoning]

#### Outcome
[How it worked in practice]
```

### Metrics Section
```markdown
## 📊 Metrics & Performance

### Development Metrics
| Metric | Target | Actual | Variance |
|--------|--------|--------|----------|
| Duration | X hours | Y hours | +/-Z% |
| Test Coverage | 80% | X% | +/-Y% |
| Code Quality | A | B | - |

### Performance Metrics
| Metric | Target | Actual | Status |
|--------|--------|--------|---------|
| Load Time | <3s | Xs | ✅/❌ |
| Memory Usage | <100MB | XMB | ✅/❌ |
| API Response | <200ms | Xms | ✅/❌ |

### Clear Thought Tool Effectiveness
| Tool | Uses | Effectiveness | Best For |
|------|------|--------------|----------|
| [tool1] | X | ⭐⭐⭐⭐⭐ | [Use case] |
| [tool2] | Y | ⭐⭐⭐⭐ | [Use case] |
```

## 🗃️ PROCESO DE ARCHIVADO

```typescript
// Generar visualizaciones finales
const finalDiagrams = await createWithTool('visualreasoning', {
    operation: 'create',
    diagramType: 'graph',
    elements: [
        projectArchitecture,
        workflowDiagram,
        metricsVisualization
    ]
});

// Documentar argumentaciones finales
const finalArguments = await documentWithTool('structuredargumentation', {
    claims: majorDecisions,
    premises: decisionRationales,
    conclusions: projectOutcomes,
    strengths: whatWorkedWell,
    weaknesses: areasForImprovement
});
```

## 📦 ARCHIVOS GENERADOS

```
.claude/archives/
├── [project-name]/
│   ├── archive-[project]-[timestamp].md    # Documento principal
│   ├── code-snapshot.zip                   # Código final
│   ├── creative-decisions/                 # Todas las decisiones
│   │   └── *.md
│   ├── metrics/                           # Datos de métricas
│   │   ├── performance.json
│   │   ├── quality.json
│   │   └── time-tracking.json
│   └── diagrams/                          # Diagramas finales
│       └── *.svg
```

## 🔍 SISTEMA DE INDEXACIÓN

```typescript
interface ArchiveIndex {
    projectId: string;
    timestamp: Date;
    tags: string[];
    technologies: string[];
    patterns: {
        successful: Pattern[];
        avoided: Pattern[];
    };
    clearThoughtTools: {
        [tool: string]: {
            frequency: number;
            effectiveness: number;
            useCases: string[];
        }
    };
    searchableContent: {
        decisions: SearchableItem[];
        code: SearchableItem[];
        lessons: SearchableItem[];
    };
}
```

## 🧹 LIMPIEZA Y FINALIZACIÓN

```typescript
// Limpiar archivos temporales
async function cleanupProject() {
    // Mover a archivo
    moveToArchive([
        'creative-*.md',
        'reflect-*.md',
        'snapshots/*'
    ]);
    
    // Resetear Memory Bank para próximo proyecto
    resetMemoryBank({
        preservePatterns: true,
        preserveToolMetrics: true,
        clearActiveContext: true
    });
    
    // Generar reporte final
    generateFinalReport({
        location: 'archives/[project]/final-report.pdf',
        includeMetrics: true,
        includeCode: false,
        includeDiagrams: true
    });
}
```

## ✅ CHECKLIST DE VERIFICACIÓN

### Pre-Archive
- [ ] REFLECT mode completado
- [ ] Todos los documentos recopilados
- [ ] Métricas finales calculadas
- [ ] Tests finales pasando

### Archive Process
- [ ] Estructura de archivo creada
- [ ] Table of contents generado
- [ ] Executive summary escrito
- [ ] Secciones técnicas documentadas
- [ ] Design decisions archivadas
- [ ] Métricas consolidadas
- [ ] Lessons learned capturadas
- [ ] Diagramas finales creados
- [ ] Cross-references verificadas

### Post-Archive
- [ ] Archivo principal guardado
- [ ] Código snapshot creado
- [ ] Índice searchable generado
- [ ] Archivos temporales limpiados
- [ ] Memory Bank reseteado
- [ ] Listo para nuevo proyecto

---

**ARCHIVE Mode completo - Conocimiento preservado**
