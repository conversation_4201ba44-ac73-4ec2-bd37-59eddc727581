-- <PERSON><PERSON>t para limpiar roles duplicados
-- <PERSON><PERSON><PERSON> solo los roles en inglés (ADMIN, EMPLOYEE, CONTRACTOR, GUEST)

-- <PERSON><PERSON>, verificar qué usuarios están usando los roles en español
SELECT r.name as role_name, COUNT(u.id) as user_count
FROM "Role" r
LEFT JOIN "User" u ON u."roleId" = r.id
WHERE r.name IN ('Administrador', 'Empleado', '<PERSON><PERSON><PERSON>', 'Invitado')
GROUP BY r.name;

-- Si no hay usuarios con roles en español, eliminarlos
DELETE FROM "Role" 
WHERE name IN ('Administrador', 'Empleado', '<PERSON>tra<PERSON>', 'Invitado')
AND id NOT IN (SELECT DISTINCT "roleId" FROM "User");

-- Verificar resultado final
SELECT id, name FROM "Role" ORDER BY name;