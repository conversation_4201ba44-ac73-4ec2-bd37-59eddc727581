import { ConfigCache } from "../shared/lib/config-cache.js";

async function reloadCache() {
    console.log("🔄 Recargando ConfigCache...");
    
    try {
        const cache = await ConfigCache.getInstance();
        await cache.reload();
        
        console.log("✅ Cache recargado exitosamente");
        
        // Verificar los estados de OrderStatus
        const orderStatuses = await cache.getAll("orderStatus");
        console.log("\n📊 Estados de OrderStatus en cache:");
        
        orderStatuses.forEach(status => {
            console.log(`- ${status.name}: iconName="${status.iconName}", color="${status.color}"`);
        });
        
        // Verificar específicamente Nuevo y En producción
        const nuevo = await cache.getByName("orderStatus", "Nuevo");
        const enProduccion = await cache.getByName("orderStatus", "En producción");
        
        console.log("\n🔍 Verificación específica:");
        console.log("Nuevo:", nuevo);
        console.log("En producción:", enProduccion);
        
    } catch (error) {
        console.error("❌ Error al recargar cache:", error);
    }
    
    process.exit(0);
}

reloadCache();
