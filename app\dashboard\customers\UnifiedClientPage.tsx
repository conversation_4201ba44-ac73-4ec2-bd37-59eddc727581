"use client";

import type { SortOption } from "@/shared/components/dashboard";
import type { Customer as BaseCustomer } from "@/features/customers/types";

import React, { useState, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    UserGroupIcon,
    CheckCircleIcon,
    UserPlusIcon,
    CurrencyDollarIcon,
    DocumentTextIcon,
    ShoppingBagIcon,
    CalendarDaysIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow, startOfMonth, subMonths } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import {
    useCustomers,
    useDeleteCustomer,
} from "@/features/customers/hooks/useCustomer";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Badge,
} from "@/shared/components/ui/hero-ui-client";
import { DeleteCustomerModal } from "@/features/customers/components";

interface Customer extends BaseCustomer {
    // Campos simulados por ahora
    lastOrderDate?: string;
    totalOrderValue?: number;
}

// Función para obtener color consistente por ID
const getAvatarColor = (id: string) => {
    const colors = [
        "primary",
        "secondary",
        "success",
        "warning",
        "danger",
    ] as const;
    const index = id.charCodeAt(0) % colors.length;

    return colors[index];
};

export default function UnifiedCustomersPage() {
    const router = useRouter();
    const { customers = [], isLoading, mutate } = useCustomers();
    const { deleteCustomer } = useDeleteCustomer();

    // Estado para el modal de eliminación
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(
        null,
    );

    // Función para manejar la confirmación de eliminación
    const handleDeleteConfirm = useCallback(
        async (customerId: string) => {
            const result = await deleteCustomer(customerId);

            if (result.success) {
                mutate();
                setDeleteModalOpen(false);
                setCustomerToDelete(null);
            } else {
                throw new Error(result.error || "Error al eliminar");
            }
        },
        [deleteCustomer, mutate],
    );

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "name-asc",
        label: "Nombre A-Z",
        field: "name",
        direction: "asc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;

    // Calcular métricas de negocio
    const businessMetrics = useMemo(() => {
        const currentDate = new Date();
        const firstDayOfMonth = startOfMonth(currentDate);
        const firstDayOfLastMonth = startOfMonth(subMonths(currentDate, 1));

        const active = customers.filter(
            (c: Customer) => c._count?.orders && c._count.orders > 0,
        );
        const newThisMonth = customers.filter(
            (c: Customer) => new Date(c.createdAt) >= firstDayOfMonth,
        );
        const newLastMonth = customers.filter((c: Customer) => {
            const createdDate = new Date(c.createdAt);

            return (
                createdDate >= firstDayOfLastMonth &&
                createdDate < firstDayOfMonth
            );
        });

        // Calcular valor promedio simulado
        const totalValue = customers.reduce(
            (sum: number, c: Customer) => sum + (c.totalOrderValue || 0),
            0,
        );
        const avgOrderValue =
            active.length > 0 ? totalValue / active.length : 0;

        // Calcular crecimiento mes a mes
        const growth =
            newLastMonth.length > 0
                ? ((newThisMonth.length - newLastMonth.length) /
                      newLastMonth.length) *
                  100
                : newThisMonth.length * 100;

        return {
            total: customers.length,
            active: active.length,
            activePercentage:
                customers.length > 0
                    ? (active.length / customers.length) * 100
                    : 0,
            newThisMonth: newThisMonth.length,
            monthOverMonthGrowth: growth,
            avgOrderValue,
        };
    }, [customers]);

    // Estadísticas para las tarjetas
    const stats = useMemo(
        () => [
            {
                title: "Total Clientes",
                value: businessMetrics.total,
                icon: <UserGroupIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Registrados en total",
            },
            {
                title: "Clientes Activos",
                value: businessMetrics.active,
                icon: <CheckCircleIcon className="w-6 h-6" />,
                color: "success" as const,
                description: `${businessMetrics.activePercentage.toFixed(0)}% del total`,
                change: businessMetrics.activePercentage,
                changeLabel: "con órdenes",
            },
            {
                title: "Nuevos este Mes",
                value: businessMetrics.newThisMonth,
                icon: <UserPlusIcon className="w-6 h-6" />,
                color: "primary" as const,
                change: businessMetrics.monthOverMonthGrowth,
                changeLabel: "vs. mes anterior",
                changeType:
                    businessMetrics.monthOverMonthGrowth >= 0
                        ? "positive"
                        : "negative",
            },
            {
                title: "Valor Promedio",
                value: `$${businessMetrics.avgOrderValue.toLocaleString("es-MX", { maximumFractionDigits: 0 })}`,
                icon: <CurrencyDollarIcon className="w-6 h-6" />,
                color: "warning" as const,
                description: "por cliente activo",
            },
        ],
        [businessMetrics],
    );

    // Columnas de la tabla mejoradas
    const columns = [
        {
            key: "customer",
            label: "Cliente",
            sortable: true,
            render: (customer: Customer) => (
                <div className="flex items-center gap-3">
                    <Avatar
                        color={getAvatarColor(customer.id)}
                        getInitials={(name) => {
                            const parts = name.split(" ");

                            if (parts.length >= 2) {
                                return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
                            }

                            return name.slice(0, 2).toUpperCase();
                        }}
                        name={customer.name}
                        size="sm"
                    />
                    <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                            {(customer as any).displayName || customer.name}
                        </p>
                        {(customer as any).displayName &&
                            (customer as any).displayName !== customer.name && (
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                    {customer.name}
                                </p>
                            )}
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                            ID: {customer.id.slice(0, 8)}
                        </p>
                    </div>
                </div>
            ),
        },
        {
            key: "orders",
            label: "Órdenes",
            sortable: true,
            render: (customer: Customer) => {
                const orderCount = customer._count?.orders || 0;
                const hasOrders = orderCount > 0;

                return (
                    <div className="flex items-center gap-2">
                        <Badge
                            color={hasOrders ? "success" : "warning"}
                            size="sm"
                            variant="flat"
                        >
                            {orderCount}{" "}
                            {orderCount === 1 ? "orden" : "órdenes"}
                        </Badge>
                        {hasOrders && (
                            <Tooltip content="Ver órdenes">
                                <ShoppingBagIcon
                                    className="w-4 h-4 text-gray-400 hover:text-gray-600 cursor-pointer"
                                    onClick={() =>
                                        router.push(
                                            `/dashboard/orders?customerId=${customer.id}`,
                                        )
                                    }
                                />
                            </Tooltip>
                        )}
                    </div>
                );
            },
        },
        {
            key: "lastOrder",
            label: "Última Orden",
            render: (customer: Customer) => {
                if (!customer.lastOrderDate) {
                    return (
                        <span className="text-sm text-gray-400">
                            Sin órdenes
                        </span>
                    );
                }

                return (
                    <div className="text-sm">
                        <div className="flex items-center gap-1">
                            <CalendarDaysIcon className="w-4 h-4 text-gray-400" />
                            <span>
                                {formatDistanceToNow(
                                    new Date(customer.lastOrderDate),
                                    {
                                        addSuffix: true,
                                        locale: es,
                                    },
                                )}
                            </span>
                        </div>
                    </div>
                );
            },
        },
        {
            key: "value",
            label: "Valor Total",
            sortable: true,
            render: (customer: Customer) => {
                const value = customer.totalOrderValue || 0;
                const hasValue = value > 0;

                return (
                    <div
                        className={`text-right font-medium ${hasValue ? "text-gray-900 dark:text-white" : "text-gray-400"}`}
                    >
                        ${value.toLocaleString("es-MX")}
                    </div>
                );
            },
        },
        {
            key: "status",
            label: "Estado",
            render: (customer: Customer) => {
                const hasOrders =
                    customer._count?.orders && customer._count.orders > 0;
                const isNew =
                    new Date(customer.createdAt) > subMonths(new Date(), 1);

                if (isNew) {
                    return (
                        <Chip color="secondary" size="sm" variant="flat">
                            Nuevo
                        </Chip>
                    );
                }

                return (
                    <Chip
                        color={hasOrders ? "success" : "default"}
                        size="sm"
                        variant="flat"
                    >
                        {hasOrders ? "Activo" : "Inactivo"}
                    </Chip>
                );
            },
        },
        {
            key: "createdAt",
            label: "Registro",
            sortable: true,
            render: (customer: Customer) => (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                    {format(new Date(customer.createdAt), "dd MMM yyyy", {
                        locale: es,
                    })}
                </div>
            ),
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (customer: Customer) => {
                router.push(`/dashboard/customers/${customer.id}/details`);
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (customer: Customer) => {
                router.push(`/dashboard/customers/${customer.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Ver Órdenes",
            icon: <DocumentTextIcon className="w-4 h-4" />,
            onClick: (customer: Customer) => {
                router.push(`/dashboard/orders?customerId=${customer.id}`);
            },
            color: "primary" as const,
            isHidden: (customer: Customer) =>
                !customer._count?.orders || customer._count.orders === 0,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (customer: Customer) => {
                if (customer._count?.orders && customer._count.orders > 0) {
                    addToast({
                        title: "No se puede eliminar",
                        description: "Este cliente tiene órdenes asociadas",
                        color: "warning",
                    });

                    return;
                }

                setCustomerToDelete(customer);
                setDeleteModalOpen(true);
            },
            color: "danger" as const,
            isDisabled: (customer: Customer) =>
                !!(customer._count?.orders && customer._count.orders > 0),
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "active", label: "Activos (con órdenes)" },
                { value: "inactive", label: "Inactivos (sin órdenes)" },
                { value: "new", label: "Nuevos (30 días)" },
            ],
        },
        {
            key: "orderCount",
            label: "Número de Órdenes",
            type: "select" as const,
            placeholder: "Todas las órdenes",
            options: [
                { value: "all", label: "Todos" },
                { value: "0", label: "Sin órdenes" },
                { value: "1-5", label: "1-5 órdenes" },
                { value: "6-10", label: "6-10 órdenes" },
                { value: "10+", label: "Más de 10" },
            ],
        },
        {
            key: "registrationPeriod",
            label: "Periodo de Registro",
            type: "select" as const,
            placeholder: "Todo el tiempo",
            options: [
                { value: "all", label: "Todo el tiempo" },
                { value: "7", label: "Últimos 7 días" },
                { value: "30", label: "Últimos 30 días" },
                { value: "90", label: "Últimos 90 días" },
                { value: "365", label: "Último año" },
            ],
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "name-asc",
            label: "Nombre A-Z",
            field: "name",
            direction: "asc" as const,
        },
        {
            key: "name-desc",
            label: "Nombre Z-A",
            field: "name",
            direction: "desc" as const,
        },
        {
            key: "orders-desc",
            label: "Más órdenes",
            field: "_count.orders",
            direction: "desc" as const,
        },
        {
            key: "orders-asc",
            label: "Menos órdenes",
            field: "_count.orders",
            direction: "asc" as const,
        },
        {
            key: "value-desc",
            label: "Mayor valor",
            field: "totalOrderValue",
            direction: "desc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
    ];

    // Filtrar datos
    const filteredData = useMemo(() => {
        let filtered = [...customers];

        // Búsqueda
        if (searchValue) {
            filtered = filtered.filter(
                (customer) =>
                    customer.name
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()) ||
                    customer.id
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()),
            );
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            switch (filterValues.status) {
                case "active":
                    filtered = filtered.filter(
                        (c) => c._count?.orders && c._count.orders > 0,
                    );
                    break;
                case "inactive":
                    filtered = filtered.filter(
                        (c) => !c._count?.orders || c._count.orders === 0,
                    );
                    break;
                case "new":
                    const thirtyDaysAgo = subMonths(new Date(), 1);

                    filtered = filtered.filter(
                        (c) => new Date(c.createdAt) > thirtyDaysAgo,
                    );
                    break;
            }
        }

        // Filtro por número de órdenes
        if (filterValues.orderCount && filterValues.orderCount !== "all") {
            const orderCount = (c: Customer) => c._count?.orders || 0;

            switch (filterValues.orderCount) {
                case "0":
                    filtered = filtered.filter((c) => orderCount(c) === 0);
                    break;
                case "1-5":
                    filtered = filtered.filter(
                        (c) => orderCount(c) >= 1 && orderCount(c) <= 5,
                    );
                    break;
                case "6-10":
                    filtered = filtered.filter(
                        (c) => orderCount(c) >= 6 && orderCount(c) <= 10,
                    );
                    break;
                case "10+":
                    filtered = filtered.filter((c) => orderCount(c) > 10);
                    break;
            }
        }

        // Filtro por periodo de registro
        if (
            filterValues.registrationPeriod &&
            filterValues.registrationPeriod !== "all"
        ) {
            const days = parseInt(filterValues.registrationPeriod);
            const dateLimit = new Date();

            dateLimit.setDate(dateLimit.getDate() - days);
            filtered = filtered.filter(
                (c) => new Date(c.createdAt) > dateLimit,
            );
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [customers, searchValue, filterValues, currentSort]);

    // Componente de top clientes
    const TopCustomers = () => {
        const topCustomers = useMemo(() => {
            return [...customers]
                .filter((c) => c._count?.orders && c._count.orders > 0)
                .sort(
                    (a, b) => (b._count?.orders || 0) - (a._count?.orders || 0),
                )
                .slice(0, 5);
        }, []);

        if (topCustomers.length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
                initial={{ opacity: 0, y: 20 }}
            >
                <h3 className="text-lg font-semibold mb-4">
                    Top 5 Clientes Más Activos
                </h3>
                <div className="space-y-3">
                    {topCustomers.map((customer, index) => (
                        <div
                            key={customer.id}
                            className="flex items-center justify-between"
                        >
                            <div className="flex items-center gap-3">
                                <span
                                    className={`text-sm font-bold ${index === 0 ? "text-yellow-600" : index === 1 ? "text-gray-500" : index === 2 ? "text-orange-600" : "text-gray-600"}`}
                                >
                                    #{index + 1}
                                </span>
                                <Avatar
                                    color={
                                        index === 0
                                            ? "warning"
                                            : getAvatarColor(customer.id)
                                    }
                                    name={customer.name}
                                    size="sm"
                                />
                                <div>
                                    <p className="font-medium">
                                        {customer.name}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        Cliente desde{" "}
                                        {format(
                                            new Date(customer.createdAt),
                                            "MMM yyyy",
                                            { locale: es },
                                        )}
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="text-lg font-semibold text-blue-600">
                                    {customer._count?.orders || 0}
                                </p>
                                <p className="text-xs text-gray-500">órdenes</p>
                            </div>
                        </div>
                    ))}
                </div>
            </motion.div>
        );
    };

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={columns}
                currentSort={currentSort}
                filterValues={filterValues}
                isLoading={isLoading}
                page={page}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Gestión de clientes y análisis de negocio"
                title="Clientes"
                totalPages={Math.ceil(filteredData.length / rowsPerPage)}
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onPageChange={setPage}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={actions}
                // Create
                createRoute="/dashboard/customers/new"
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key] && filterValues[key] !== "all",
                    ).length
                }
                // Pagination
                breadcrumbs={[{ label: "Clientes" }]}
                // Stats
                stats={stats}
                createLabel="Nuevo Cliente"
                // Filters
                filters={filters}
                data={filteredData}
                // Table
                emptyContent="No hay clientes registrados"
            />

            {/* Top clientes */}
            {customers.length > 0 && <TopCustomers />}

            {/* Modal de eliminación */}
            <DeleteCustomerModal
                customer={customerToDelete}
                isOpen={deleteModalOpen}
                onClose={() => {
                    setDeleteModalOpen(false);
                    setCustomerToDelete(null);
                }}
                onConfirm={handleDeleteConfirm}
            />
        </>
    );
}
