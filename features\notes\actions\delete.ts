"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
    revalidateData,
} from "@/shared/utils/actions-utils";

/**
 * Elimina una nota existente
 * @param noteId - ID de la nota a eliminar
 */
export async function deleteNote(
    noteId: string,
): Promise<ActionResponse<void>> {
    // REMOVED: console.log(`🗑️ Deleting note with ID: ${noteId}`);

    try {
        // Verificar sesión del usuario
        const user = await getCurrentUser();

        if (!user) {
            // REMOVED: console.log("❌ Delete note failed: No authenticated user");

            return {
                success: false,
                error: "No autorizado: Debes iniciar sesión",
            };
        }

        // Obtener la nota para verificar permisos y obtener orderId para revalidación
        const existingNote = await prisma.note.findUnique({
            where: { id: noteId },
            select: {
                id: true,
                authorId: true,
                orderId: true,
            },
        });

        if (!existingNote) {
            // REMOVED: console.log(`❌ Delete note failed: Note ${noteId} not found`);

            return {
                success: false,
                error: "Nota no encontrada",
            };
        }

        // Verificar si el usuario es el autor de la nota o un administrador
        const isAuthor = existingNote.authorId === user.id;
        const isAdmin = (user.role as any)?.name === "ADMIN";

        if (!isAuthor && !isAdmin) {
            // REMOVED: console.log - Delete note failed: User not authorized

            return {
                success: false,
                error: "No tienes permiso para eliminar esta nota",
            };
        }

        // Eliminar primero los comentarios asociados a la nota
        await prisma.noteComment.deleteMany({
            where: {
                noteId,
            },
        });

        // Eliminar la nota
        await prisma.note.delete({
            where: {
                id: noteId,
            },
        });

        // REMOVED: console.log(`✅ Note ${noteId} deleted successfully`);

        // Revalidar rutas relacionadas
        if (existingNote.orderId) {
            revalidateData([
                "/dashboard/notes",
                `/dashboard/orders/${existingNote.orderId}`,
            ]);
        }

        return {
            success: true,
        };
    } catch (error) {
        // REMOVED: console.error("❌ Error deleting note:", error);

        return createErrorResponse(error);
    }
}
