import { StyleSheet } from "@react-pdf/renderer";

export const remissionStyles = StyleSheet.create({
    // Page
    page: {
        fontFamily: "Helvetica",
        fontSize: 10,
        padding: 20,
        backgroundColor: "#ffffff",
    },

    // Header Styles
    header: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 20,
        paddingBottom: 10,
        borderBottomWidth: 2,
        borderBottomColor: "#1e40af",
    },

    logo: {
        width: 60,
        height: 60,
    },

    headerCenter: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },

    title: {
        fontSize: 18,
        fontWeight: "bold",
        color: "#1e40af",
        marginBottom: 4,
    },

    subtitle: {
        fontSize: 10,
        color: "#6b7280",
        fontStyle: "italic",
    },

    folio: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#1f2937",
    },

    headerRight: {
        alignItems: "flex-end",
    },

    date: {
        fontSize: 9,
        color: "#6b7280",
    },

    // Section Styles
    section: {
        marginBottom: 15,
        padding: 10,
        backgroundColor: "#f9fafb",
        borderRadius: 4,
    },

    sectionTitle: {
        fontSize: 11,
        fontWeight: "bold",
        color: "#1e40af",
        marginBottom: 5,
        flexDirection: "row",
        alignItems: "center",
    },

    row: {
        flexDirection: "row",
        marginBottom: 3,
    },

    label: {
        fontSize: 9,
        color: "#6b7280",
        width: 80,
    },

    value: {
        fontSize: 10,
        color: "#1f2937",
        flex: 1,
    },

    // Table Styles
    table: {
        marginVertical: 10,
    },

    tableHeader: {
        flexDirection: "row",
        backgroundColor: "#1e40af",
        padding: 5,
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
    },

    tableHeaderCell: {
        color: "#ffffff",
        fontSize: 9,
        fontWeight: "bold",
        textAlign: "center",
        paddingVertical: 2,
        paddingHorizontal: 4,
    },

    tableRow: {
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
        padding: 5,
    },

    tableRowAlternate: {
        backgroundColor: "#f9fafb",
    },

    tableCell: {
        fontSize: 9,
        textAlign: "center",
        paddingVertical: 2,
        paddingHorizontal: 4,
    },

    tableCellModel: {
        flex: 2,
        textAlign: "left",
    },

    tableCellColor: {
        flex: 2,
        textAlign: "left",
    },

    tableCellSize: {
        flex: 1,
    },

    tableCellQuantity: {
        flex: 1,
        fontWeight: "bold",
    },

    tableFooter: {
        flexDirection: "row",
        backgroundColor: "#1e40af",
        padding: 8,
        marginTop: -1,
        borderBottomLeftRadius: 4,
        borderBottomRightRadius: 4,
    },

    totalLabel: {
        fontSize: 10,
        fontWeight: "bold",
        color: "#ffffff",
        flex: 1,
        textAlign: "right",
        paddingRight: 10,
    },

    totalValue: {
        fontSize: 11,
        fontWeight: "bold",
        color: "#ffffff",
    },

    // Notes Styles
    notesSection: {
        marginTop: 15,
        padding: 10,
        backgroundColor: "#f9fafb",
        borderRadius: 4,
    },

    notesText: {
        fontSize: 9,
        color: "#4b5563",
        lineHeight: 1.4,
    },

    // Footer Styles
    footer: {
        marginTop: 30,
        paddingTop: 20,
        borderTopWidth: 1,
        borderTopColor: "#e5e7eb",
    },

    signatureSection: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 20,
    },

    signatureBox: {
        width: "30%",
        alignItems: "center",
    },

    signatureLine: {
        width: "100%",
        borderBottomWidth: 1,
        borderBottomColor: "#9ca3af",
        marginBottom: 5,
        height: 40,
    },

    signatureLabel: {
        fontSize: 8,
        color: "#6b7280",
        textAlign: "center",
    },

    footerInfo: {
        marginTop: 20,
        alignItems: "center",
    },

    footerText: {
        fontSize: 8,
        color: "#9ca3af",
        textAlign: "center",
        marginBottom: 2,
    },

    // Utility Styles
    bold: {
        fontWeight: "bold",
    },

    textCenter: {
        textAlign: "center",
    },

    mt10: {
        marginTop: 10,
    },

    mb5: {
        marginBottom: 5,
    },
});

// Export column widths for consistent table layout
export const tableColumnWidths = {
    orderNumber: "15%",
    model: "20%",
    color: "20%",
    quantity: "10%",
    delivered: "10%",
    pending: "10%",
    unitPrice: "15%",
};
