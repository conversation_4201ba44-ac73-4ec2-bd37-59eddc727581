# 🚀 Guía de Deployment y Operaciones - Sistema Lohari

## 📋 Tabla de Contenidos

1. [Instalación Local](#-instalación-local)
2. [Configuración de Entorno](#-configuración-de-entorno)
3. [Deployment en Producción](#-deployment-en-producción)
4. [Operaciones y Mantenimiento](#-operaciones-y-mantenimiento)
5. [Monitoreo y Logs](#-monitoreo-y-logs)
6. [Backup y Recuperación](#-backup-y-recuperación)
7. [Troubleshooting](#-troubleshooting)
8. [Scripts Útiles](#-scripts-útiles)

---

## 💻 Instalación Local

### Requisitos del Sistema

- **Node.js**: 18.17 o superior
- **npm**: 9.0 o superior (incluido con Node.js)
- **PostgreSQL**: 14.0 o superior (o cuenta Supabase)
- **Git**: 2.0 o superior
- **RAM**: <PERSON>ín<PERSON> 4GB, recomendado 8GB
- **Espacio en disco**: 2GB libre

### Instalación Paso a Paso

#### 1. Clonar el Repositorio

```bash
# Clonar el repositorio
git clone https://github.com/tu-organizacion/lohari.git

# Entrar al directorio
cd lohari

# Verificar la rama
git branch
# Output: * main
```

#### 2. Instalar Dependencias

```bash
# Instalar dependencias con npm
npm install

# O con yarn
yarn install

# O con pnpm
pnpm install
```

#### 3. Configurar Base de Datos

**Opción A: PostgreSQL Local**

```bash
# Crear base de datos
createdb lohari_dev

# O usando psql
psql -U postgres
CREATE DATABASE lohari_dev;
\q
```

**Opción B: Supabase (Recomendado)**

1. Crear cuenta en [supabase.com](https://supabase.com)
2. Crear nuevo proyecto
3. Copiar las credenciales de conexión

#### 4. Configurar Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar con tu editor favorito
nano .env
# o
code .env
```

#### 5. Ejecutar Migraciones

```bash
# Generar cliente Prisma
npx prisma generate

# Ejecutar migraciones
npx prisma migrate deploy

# Sembrar datos iniciales
npm run db:seed
```

#### 6. Iniciar el Servidor de Desarrollo

```bash
# Iniciar en modo desarrollo
npm run dev

# Con Turbopack (más rápido)
npm run dev:turbo
```

Abrir navegador en [http://localhost:3000](http://localhost:3000)

### Verificación de Instalación

```bash
# Verificar que todo funciona
curl http://localhost:3000/api/health
# Output: {"status":"healthy","checks":{...}}
```

---

## 🔧 Configuración de Entorno

### Variables de Entorno Requeridas

```env
# ===== BASE DE DATOS =====
# URL principal de PostgreSQL
DATABASE_URL="************************************/database?schema=public"

# URL directa (sin pooling) para migraciones
DIRECT_URL="************************************/database?schema=public"

# ===== AUTENTICACIÓN =====
# URL de la aplicación
NEXTAUTH_URL="http://localhost:3000"

# Secret para JWT (generar con: openssl rand -base64 32)
NEXTAUTH_SECRET="tu-secret-super-seguro-de-32-caracteres-minimo"

# ===== OPCIONAL: SUPABASE =====
# Solo si usas Supabase para storage
NEXT_PUBLIC_SUPABASE_URL="https://tuproyecto.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="tu-anon-key"

# ===== OPCIONAL: MONITORING =====
# Sentry para error tracking
SENTRY_DSN="https://...@sentry.io/..."

# ===== OPCIONAL: REDIS =====
# Para caché avanzado
REDIS_URL="redis://localhost:6379"

# ===== DESARROLLO =====
# Modo de desarrollo
NODE_ENV="development"

# Puerto del servidor (default: 3000)
PORT="3000"
```

### Generar Secrets Seguros

```bash
# Generar NEXTAUTH_SECRET
openssl rand -base64 32

# O con Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

### Configuración por Ambiente

#### Development (.env.development)
```env
NODE_ENV=development
DATABASE_URL=postgresql://postgres:password@localhost:5432/lohari_dev
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=dev-secret-not-for-production
```

#### Staging (.env.staging)
```env
NODE_ENV=production
DATABASE_URL=postgresql://user:<EMAIL>:5432/lohari_staging
NEXTAUTH_URL=https://staging.lohari.com
NEXTAUTH_SECRET=staging-secret-32-characters-minimum
```

#### Production (.env.production)
```env
NODE_ENV=production
DATABASE_URL=postgresql://user:<EMAIL>:5432/lohari_prod
NEXTAUTH_URL=https://lohari.com
NEXTAUTH_SECRET=production-secret-super-secure-32-chars
SENTRY_DSN=https://...@sentry.io/...
```

---

## 🚀 Deployment en Producción

### Opción 1: Vercel (Recomendado)

#### Preparación

1. **Crear cuenta en Vercel**
   - Ir a [vercel.com](https://vercel.com)
   - Conectar con GitHub

2. **Importar proyecto**
   ```
   Dashboard → New Project → Import Git Repository
   ```

3. **Configurar variables de entorno**
   ```
   Settings → Environment Variables
   ```
   Agregar todas las variables de `.env.production`

4. **Configurar dominio**
   ```
   Settings → Domains → Add Domain
   ```

#### Deployment

```bash
# Instalar Vercel CLI
npm i -g vercel

# Login
vercel login

# Deploy
vercel --prod

# O push a main para auto-deploy
git push origin main
```

#### Configuración Vercel

`vercel.json`:
```json
{
  "buildCommand": "prisma generate && next build",
  "devCommand": "next dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["iad1"],
  "env": {
    "PRISMA_GENERATE_DATAPROXY": "@prisma_generate_dataproxy"
  }
}
```

### Opción 2: Railway

#### Setup

1. **Crear proyecto en Railway**
   ```bash
   # Instalar Railway CLI
   npm i -g @railway/cli
   
   # Login
   railway login
   
   # Inicializar
   railway init
   ```

2. **Agregar PostgreSQL**
   ```bash
   railway add postgresql
   ```

3. **Deploy**
   ```bash
   railway up
   ```

#### railway.toml
```toml
[build]
builder = "NIXPACKS"
buildCommand = "npm run build"

[deploy]
startCommand = "npm start"
healthcheckPath = "/api/health"
healthcheckTimeout = 100
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10
```

### Opción 3: Docker

#### Dockerfile
```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npx prisma generate
RUN npm run build

# Production stage
FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy built files
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma

# Switch to non-root user
USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: ${DATABASE_URL}
      NEXTAUTH_URL: ${NEXTAUTH_URL}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
    depends_on:
      - postgres
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: lohari
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
```

#### Build y Deploy

```bash
# Build
docker build -t lohari:latest .

# Run con docker-compose
docker-compose up -d

# Ver logs
docker-compose logs -f app

# Ejecutar migraciones
docker-compose exec app npx prisma migrate deploy
```

### Configuración de Dominio

#### DNS Records
```
Type    Name    Value
A       @       76.76.21.21
A       www     76.76.21.21
CNAME   *       cname.vercel-dns.com
```

#### SSL/TLS
- Vercel: Automático con Let's Encrypt
- Railway: Automático
- Docker: Usar Nginx + Certbot

---

## 🔧 Operaciones y Mantenimiento

### Tareas de Mantenimiento

#### Diarias
- [ ] Revisar logs de errores
- [ ] Verificar uso de recursos
- [ ] Monitorear métricas de negocio
- [ ] Revisar alertas de seguridad

#### Semanales
- [ ] Backup de base de datos
- [ ] Actualizar dependencias menores
- [ ] Revisar performance metrics
- [ ] Limpiar logs antiguos

#### Mensuales
- [ ] Actualizar dependencias mayores
- [ ] Auditoría de seguridad
- [ ] Optimización de base de datos
- [ ] Revisión de costos de infraestructura

### Scripts de Mantenimiento

#### Limpiar datos antiguos
```typescript
// scripts/cleanup-old-data.ts
import { prisma } from '@/lib/prisma'
import { subMonths } from 'date-fns'

async function cleanup() {
  const sixMonthsAgo = subMonths(new Date(), 6)
  
  // Eliminar logs antiguos
  await prisma.operationLog.deleteMany({
    where: {
      createdAt: { lt: sixMonthsAgo }
    }
  })
  
  // Archivar órdenes completadas
  await prisma.order.updateMany({
    where: {
      status: { name: 'DELIVERED' },
      deliveryDate: { lt: sixMonthsAgo }
    },
    data: { isArchived: true }
  })
  
  console.log('Limpieza completada')
}

cleanup()
```

#### Optimizar base de datos
```sql
-- scripts/optimize-db.sql
-- Reindexar tablas principales
REINDEX TABLE "Order";
REINDEX TABLE "Assignment";
REINDEX TABLE "Remission";

-- Actualizar estadísticas
ANALYZE;

-- Vacuum para recuperar espacio
VACUUM ANALYZE;
```

### Actualización de Dependencias

```bash
# Ver dependencias desactualizadas
npm outdated

# Actualizar dependencias menores
npm update

# Actualizar dependencia específica
npm install package@latest

# Actualizar todas las dependencias (cuidado!)
npx npm-check-updates -u
npm install

# Verificar vulnerabilidades
npm audit

# Fix automático de vulnerabilidades
npm audit fix
```

### Migraciones de Base de Datos

#### Crear nueva migración
```bash
# Modificar schema.prisma primero
nano prisma/schema.prisma

# Crear migración
npx prisma migrate dev --name descripcion_del_cambio

# Revisar SQL generado
cat prisma/migrations/*/migration.sql
```

#### Aplicar migraciones en producción
```bash
# Backup primero!
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Aplicar migraciones
npx prisma migrate deploy

# Verificar
npx prisma db pull
```

---

## 📊 Monitoreo y Logs

### Configuración de Monitoreo

#### Sentry (Error Tracking)

1. **Instalar Sentry**
```bash
npm install @sentry/nextjs
npx @sentry/wizard -i nextjs
```

2. **Configurar sentry.client.config.ts**
```typescript
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  beforeSend(event) {
    // Filtrar información sensible
    if (event.request?.cookies) {
      delete event.request.cookies
    }
    return event
  }
})
```

#### Application Insights / Google Analytics

```typescript
// lib/analytics.ts
export function trackEvent(name: string, properties?: any) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', name, properties)
  }
}

// Uso
trackEvent('order_created', {
  order_id: order.id,
  customer_id: order.customerId,
  value: order.totalValue
})
```

### Logs Estructurados

#### Winston Logger
```typescript
// lib/logger.ts
import winston from 'winston'

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ 
      filename: 'error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'combined.log' 
    })
  ]
})

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }))
}

export default logger
```

#### Uso de Logs
```typescript
import logger from '@/lib/logger'

// En Server Actions
export async function createOrder(data: OrderInput) {
  logger.info('Creating order', { 
    customerId: data.customerId,
    timestamp: new Date()
  })
  
  try {
    const order = await prisma.order.create({ data })
    logger.info('Order created successfully', { 
      orderId: order.id 
    })
    return { success: true, order }
  } catch (error) {
    logger.error('Failed to create order', {
      error: error.message,
      stack: error.stack,
      data
    })
    throw error
  }
}
```

### Health Checks

#### Endpoint de salud
```typescript
// app/api/health/route.ts
export async function GET() {
  const checks = {
    server: 'healthy',
    database: 'unhealthy',
    redis: 'unhealthy',
    timestamp: new Date().toISOString()
  }
  
  // Check database
  try {
    await prisma.$queryRaw`SELECT 1`
    checks.database = 'healthy'
  } catch (error) {
    logger.error('Database health check failed', { error })
  }
  
  // Check Redis
  try {
    await redis.ping()
    checks.redis = 'healthy'
  } catch {
    // Redis es opcional
    checks.redis = 'not configured'
  }
  
  const isHealthy = checks.database === 'healthy'
  
  return NextResponse.json(checks, {
    status: isHealthy ? 200 : 503
  })
}
```

#### Monitoreo con UptimeRobot
```yaml
# Configuración UptimeRobot
Monitor Type: HTTP(s)
URL: https://tu-dominio.com/api/health
Check Interval: 5 minutes
Alert Contacts: <EMAIL>
```

---

## 💾 Backup y Recuperación

### Estrategia de Backup

#### Backup Automático (Supabase)
Supabase realiza backups automáticos:
- **Point-in-time recovery**: Últimos 7 días (Pro plan)
- **Daily backups**: Últimos 30 días

#### Backup Manual

**Script de backup**
```bash
#!/bin/bash
# scripts/backup.sh

# Configuración
DB_URL=$DATABASE_URL
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/lohari_backup_$DATE.sql"

# Crear directorio si no existe
mkdir -p $BACKUP_DIR

# Realizar backup
echo "Iniciando backup..."
pg_dump $DB_URL > $BACKUP_FILE

# Comprimir
gzip $BACKUP_FILE

# Subir a S3 (opcional)
aws s3 cp "$BACKUP_FILE.gz" s3://mi-bucket/backups/

# Limpiar backups antiguos (mantener últimos 30)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completado: $BACKUP_FILE.gz"
```

**Cron job para backup diario**
```bash
# Editar crontab
crontab -e

# Agregar backup diario a las 2 AM
0 2 * * * /home/<USER>/lohari/scripts/backup.sh >> /var/log/backup.log 2>&1
```

### Procedimiento de Recuperación

#### Desde backup SQL
```bash
# Descomprimir backup
gunzip lohari_backup_20240115.sql.gz

# Restaurar
psql $DATABASE_URL < lohari_backup_20240115.sql

# O con pg_restore si es formato custom
pg_restore -d $DATABASE_URL lohari_backup_20240115.dump
```

#### Recuperación de emergencia
```bash
# 1. Detener aplicación
docker-compose stop app

# 2. Backup corrupto actual (por si acaso)
pg_dump $DATABASE_URL > corrupted_backup.sql

# 3. Drop y recreate database
psql $DATABASE_URL -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"

# 4. Restaurar último backup bueno
psql $DATABASE_URL < last_good_backup.sql

# 5. Aplicar migraciones faltantes
npx prisma migrate deploy

# 6. Verificar integridad
npx prisma db pull
npx prisma validate

# 7. Reiniciar aplicación
docker-compose start app
```

---

## 🐛 Troubleshooting

### Problemas Comunes

#### Error: "PrismaClientKnownRequestError"
**Síntoma**: Error P2002, P2025, etc.

**Solución**:
```bash
# Regenerar cliente Prisma
npx prisma generate

# Verificar esquema
npx prisma validate

# Sincronizar con BD
npx prisma db pull
```

#### Error: "NEXTAUTH_URL is not set"
**Síntoma**: Error 500 al hacer login

**Solución**:
```bash
# Verificar variable
echo $NEXTAUTH_URL

# Setear correctamente
export NEXTAUTH_URL="https://tu-dominio.com"

# En Vercel/Railway, agregar en dashboard
```

#### Error: "Too many connections"
**Síntoma**: Error de base de datos

**Solución**:
```typescript
// lib/prisma.ts
import { PrismaClient } from '@prisma/client'

const globalForPrisma = global as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  log: process.env.NODE_ENV === 'development' 
    ? ['query', 'error', 'warn'] 
    : ['error']
})

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma
}
```

#### Performance Issues

**Diagnóstico**:
```bash
# Analizar bundle size
npm run analyze

# Verificar queries lentos
npx prisma studio
# Ver logs de queries
```

**Optimizaciones**:
```typescript
// 1. Lazy loading
const OrderModal = dynamic(
  () => import('@/components/OrderModal'),
  { loading: () => <Skeleton /> }
)

// 2. Image optimization
<Image
  src={product.image}
  alt={product.name}
  width={400}
  height={400}
  placeholder="blur"
  quality={85}
/>

// 3. Query optimization
const orders = await prisma.order.findMany({
  select: {
    id: true,
    folio: true,
    customer: {
      select: { name: true }
    }
  },
  take: 20
})
```

### Logs de Debug

#### Habilitar logs detallados
```env
# .env
DEBUG=*
PRISMA_LOG_LEVEL=query
NODE_OPTIONS="--trace-warnings"
```

#### Ver logs en producción
```bash
# Vercel
vercel logs --follow

# Railway
railway logs

# Docker
docker logs -f lohari_app_1

# PM2
pm2 logs app
```

---

## 📜 Scripts Útiles

### package.json Scripts

```json
{
  "scripts": {
    // Desarrollo
    "dev": "next dev",
    "dev:turbo": "next dev --turbopack",
    
    // Build
    "build": "prisma generate && next build",
    "start": "next start",
    
    // Base de datos
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:deploy": "prisma migrate deploy",
    "db:seed": "tsx prisma/seed.ts",
    "db:reset": "prisma migrate reset",
    "db:validate": "prisma validate",
    
    // Testing
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "e2e": "playwright test",
    
    // Linting
    "lint": "eslint . --fix",
    "lint:check": "eslint .",
    "type-check": "tsc --noEmit",
    
    // Utilidades
    "analyze": "ANALYZE=true next build",
    "clean": "rm -rf .next node_modules",
    "fresh": "npm run clean && npm install && npm run db:generate",
    
    // Mantenimiento
    "update:deps": "npx npm-check-updates -u",
    "security:check": "npm audit",
    "backup": "./scripts/backup.sh",
    
    // Performance
    "perf:baseline": "tsx scripts/performance/measure-baseline.ts",
    "perf:test": "tsx scripts/performance/benchmark-queries.ts"
  }
}
```

### Scripts Personalizados

#### check-env.js
```javascript
// scripts/check-env.js
const required = [
  'DATABASE_URL',
  'NEXTAUTH_URL',
  'NEXTAUTH_SECRET'
]

const missing = required.filter(key => !process.env[key])

if (missing.length > 0) {
  console.error('❌ Missing environment variables:')
  missing.forEach(key => console.error(`   - ${key}`))
  process.exit(1)
}

console.log('✅ All required environment variables are set')
```

#### seed-demo-data.ts
```typescript
// scripts/seed-demo-data.ts
import { prisma } from '@/lib/prisma'
import { faker } from '@faker-js/faker'

async function seedDemoData() {
  // Crear clientes demo
  const customers = await Promise.all(
    Array(10).fill(null).map(() =>
      prisma.customer.create({
        data: {
          name: faker.company.name()
        }
      })
    )
  )
  
  // Crear órdenes demo
  for (const customer of customers) {
    await prisma.order.create({
      data: {
        customerId: customer.id,
        transferNumber: `TRF-${faker.number.int({ min: 1000, max: 9999 })}`,
        estimatedDeliveryDate: faker.date.future(),
        statusId: 'status-received'
      }
    })
  }
  
  console.log('✅ Demo data seeded')
}

seedDemoData()
```

#### performance-check.ts
```typescript
// scripts/performance-check.ts
import { performance } from 'perf_hooks'
import { prisma } from '@/lib/prisma'

async function checkPerformance() {
  console.log('🏃 Running performance checks...\n')
  
  // Test 1: Order query
  const start1 = performance.now()
  const orders = await prisma.order.findMany({
    take: 100,
    include: {
      customer: true,
      garments: true
    }
  })
  const time1 = performance.now() - start1
  console.log(`✓ Order query (100 records): ${time1.toFixed(2)}ms`)
  
  // Test 2: Complex aggregation
  const start2 = performance.now()
  const stats = await prisma.order.aggregate({
    _count: true,
    _sum: {
      garments: true
    }
  })
  const time2 = performance.now() - start2
  console.log(`✓ Aggregation query: ${time2.toFixed(2)}ms`)
  
  // Recommendations
  if (time1 > 1000) {
    console.log('\n⚠️  Order query is slow. Consider:')
    console.log('   - Adding indexes')
    console.log('   - Reducing includes')
    console.log('   - Implementing pagination')
  }
}

checkPerformance()
```

---

## 🎯 Checklist de Deployment

### Pre-Deployment
- [ ] Todas las pruebas pasan (`npm test`)
- [ ] No hay errores de TypeScript (`npm run type-check`)
- [ ] No hay errores de lint (`npm run lint:check`)
- [ ] Variables de entorno configuradas
- [ ] Base de datos migrada
- [ ] Backup reciente realizado
- [ ] Changelog actualizado
- [ ] Versión bumpeada en package.json

### Deployment
- [ ] Build exitoso
- [ ] Health check respondiendo
- [ ] Sin errores en logs
- [ ] SSL/TLS funcionando
- [ ] Dominio resolviendo correctamente

### Post-Deployment
- [ ] Verificar funcionalidades críticas
  - [ ] Login/Logout
  - [ ] Crear orden
  - [ ] Generar remisión
  - [ ] Exportar PDF
- [ ] Monitoreo activo
- [ ] Alertas configuradas
- [ ] Backup post-deployment
- [ ] Comunicar al equipo

---

## 📞 Contactos de Emergencia

### Equipo Técnico
- **DevOps Lead**: <EMAIL>
- **DBA**: <EMAIL>
- **Security**: <EMAIL>
- **On-Call**: +52 555 xxx xxxx

### Proveedores
- **Vercel Support**: support.vercel.com
- **Supabase Support**: support.supabase.com
- **Railway Support**: railway.app/help

### Escalación
1. Intentar solución con documentación
2. Consultar con equipo técnico
3. Abrir ticket con proveedor
4. Escalación a CTO si crítico

---

**Última actualización**: Enero 2025  
**Versión**: 1.0.0
**Mantenido por**: Equipo DevOps Lohari