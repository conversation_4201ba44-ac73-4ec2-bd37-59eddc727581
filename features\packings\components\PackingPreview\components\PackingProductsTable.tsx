"use client";

import type { PackingProductsTableProps } from "../types";

import React, { useMemo } from "react";
import {
    Table,
    TableHeader,
    TableBody,
    TableColumn,
    TableRow,
    TableCell,
} from "@heroui/table";
import { Card, CardBody } from "@heroui/card";

import styles from "../PackingPreview.module.css";

export const PackingProductsTable: React.FC<PackingProductsTableProps> = ({
    items,
    editMode,
    onEdit,
}) => {
    // Get all unique sizes across all items
    const allSizes = useMemo(() => {
        const sizeSet = new Set<string>();

        items.forEach((item) => {
            item.sizes.forEach((size) => sizeSet.add(size.size));
        });

        return Array.from(sizeSet).sort();
    }, [items]);

    // Calculate totals
    const totals = useMemo(() => {
        const sizeQuantities: Record<string, number> = {};

        allSizes.forEach((size) => {
            sizeQuantities[size] = 0;
        });

        let totalPieces = 0;
        let totalBoxes = 0;
        let totalBags = 0;
        let totalLoosePieces = 0;

        items.forEach((item) => {
            totalPieces += item.totals.pieces;
            totalBoxes += item.totals.boxes;
            totalBags += item.totals.bags;
            totalLoosePieces += item.totals.loosePieces;

            item.sizes.forEach((sizeDetail) => {
                sizeQuantities[sizeDetail.size] += sizeDetail.quantity;
            });
        });

        return {
            sizeQuantities,
            totalPieces,
            totalBoxes,
            totalBags,
            totalLoosePieces,
        };
    }, [items, allSizes]);

    // Get size quantity for an item
    const getSizeQuantity = (item: (typeof items)[0], size: string): number => {
        const sizeDetail = item.sizes.find((s) => s.size === size);

        return sizeDetail ? sizeDetail.quantity : 0;
    };

    // Columns configuration
    const columns = [
        { key: "model", label: "MODELO" },
        { key: "color", label: "COLOR" },
        { key: "part", label: "PARTE" },
        ...allSizes.map((size) => ({ key: `size-${size}`, label: size })),
        { key: "totalPieces", label: "TOTAL PZS" },
        { key: "boxes", label: "CAJAS" },
        { key: "bags", label: "BOLSAS" },
        { key: "loosePieces", label: "SUELTAS" },
    ];

    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.currentTarget.textContent);
            }
        };

    return (
        <Card className={styles.tableCard}>
            <CardBody className={styles.tableCardBody}>
                <h3 className={styles.sectionTitle}>Detalle de Productos</h3>

                {/* For print optimization, we'll use a regular table */}
                <div className={styles.tableWrapper}>
                    <table className={styles.packingTable}>
                        <thead>
                            <tr>
                                <th>MODELO</th>
                                <th>COLOR</th>
                                <th>PARTE</th>
                                {allSizes.map((size) => (
                                    <th
                                        key={size}
                                        className={styles.sizeColumn}
                                    >
                                        {size}
                                    </th>
                                ))}
                                <th className={styles.totalColumn}>
                                    TOTAL PZS
                                </th>
                                <th className={styles.numberColumn}>CAJAS</th>
                                <th className={styles.numberColumn}>BOLSAS</th>
                                <th className={styles.numberColumn}>SUELTAS</th>
                            </tr>
                        </thead>
                        <tbody>
                            {items.map((item, index) => (
                                <tr key={index}>
                                    <td>{item.model}</td>
                                    <td>{item.color}</td>
                                    <td>{item.part || "-"}</td>
                                    {allSizes.map((size) => (
                                        <td
                                            key={size}
                                            className={styles.textCenter}
                                        >
                                            {getSizeQuantity(item, size) || "-"}
                                        </td>
                                    ))}
                                    <td
                                        className={`${styles.textCenter} ${styles.fontBold}`}
                                    >
                                        {item.totals.pieces.toLocaleString(
                                            "es-MX",
                                        )}
                                    </td>
                                    <td className={styles.textCenter}>
                                        {item.totals.boxes || "-"}
                                    </td>
                                    <td className={styles.textCenter}>
                                        {item.totals.bags || "-"}
                                    </td>
                                    <td className={styles.textCenter}>
                                        {item.totals.loosePieces || "-"}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                        <tfoot>
                            <tr className={styles.totalRow}>
                                <td className={styles.textRight} colSpan={3}>
                                    <strong>TOTALES:</strong>
                                </td>
                                {allSizes.map((size) => (
                                    <td
                                        key={size}
                                        className={`${styles.textCenter} ${styles.fontBold}`}
                                    >
                                        {totals.sizeQuantities[
                                            size
                                        ].toLocaleString("es-MX")}
                                    </td>
                                ))}
                                <td
                                    className={`${styles.textCenter} ${styles.fontBold}`}
                                >
                                    {totals.totalPieces.toLocaleString("es-MX")}
                                </td>
                                <td
                                    className={`${styles.textCenter} ${styles.fontBold}`}
                                >
                                    {totals.totalBoxes.toLocaleString("es-MX")}
                                </td>
                                <td
                                    className={`${styles.textCenter} ${styles.fontBold}`}
                                >
                                    {totals.totalBags.toLocaleString("es-MX")}
                                </td>
                                <td
                                    className={`${styles.textCenter} ${styles.fontBold}`}
                                >
                                    {totals.totalLoosePieces.toLocaleString(
                                        "es-MX",
                                    )}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                {/* Alternative HeroUI Table for screen display (hidden in print) */}
                <div className={`${styles.screenOnly} ${styles.heroUITable}`}>
                    <Table
                        aria-label="Tabla de productos del packing"
                        classNames={{
                            wrapper: styles.tableWrapper,
                            th: styles.tableHeader,
                            td: styles.tableCell,
                        }}
                    >
                        <TableHeader columns={columns}>
                            {(column) => (
                                <TableColumn
                                    key={column.key}
                                    align={
                                        column.key.includes("size") ||
                                        column.key.includes("total") ||
                                        column.key.includes("boxes") ||
                                        column.key.includes("bags") ||
                                        column.key.includes("loose")
                                            ? "center"
                                            : "start"
                                    }
                                >
                                    {column.label}
                                </TableColumn>
                            )}
                        </TableHeader>
                        <TableBody items={items}>
                            {(item) => (
                                <TableRow
                                    key={`${item.model}-${item.color}-${item.part || "NA"}`}
                                >
                                    <TableCell>{item.model}</TableCell>
                                    <TableCell>{item.color}</TableCell>
                                    <TableCell>{item.part || "-"}</TableCell>
                                    {
                                        allSizes.map((size) => (
                                            <TableCell key={size}>
                                                {getSizeQuantity(item, size) ||
                                                    "-"}
                                            </TableCell>
                                        )) as any
                                    }
                                    <TableCell className={styles.fontBold}>
                                        {item.totals.pieces.toLocaleString(
                                            "es-MX",
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        {item.totals.boxes || "-"}
                                    </TableCell>
                                    <TableCell>
                                        {item.totals.bags || "-"}
                                    </TableCell>
                                    <TableCell>
                                        {item.totals.loosePieces || "-"}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            </CardBody>
        </Card>
    );
};
