"use client";

import React, { useState, useEffect } from "react";
import {
    ChevronLeft,
    ChevronRight,
    Search,
    MoreHorizontal,
} from "lucide-react";
import { Skeleton, Button, Input, Card } from "@heroui/react";

import { useOrder, useOrderStatuses } from "@/features/orders/hooks/useOrder";
import EmptyState from "@/shared/components/ui/EmptyState";
import DateDisplay from "@/features/orders/components/DateDisplay";

interface OrderTableProps {
    customerId?: string;
    initialStatusId?: string;
    className?: string;
}

export default function OrderTable({
    customerId,
    initialStatusId = "all",
    className = "",
}: OrderTableProps) {
    // Estados para filtros y paginación
    const [statusId, setStatusId] = useState<string>(initialStatusId);
    const [search, setSearch] = useState<string>("");
    const [page, setPage] = useState<number>(1);
    const [_perPage, _setPerPage] = useState<number>(10);
    const [sortBy, setSortBy] = useState<string>("createdAt");
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

    // Restablecer la página cuando cambian los filtros
    useEffect(() => {
        setPage(1);
    }, [statusId, search]);

    // Hooks para obtener datos
    const { orders, total, totalPages, isLoading, isValidating } = useOrder({
        statusId: statusId === "all" ? undefined : statusId,
        customerId,
        search,
        page,
        limit: _perPage,
        sortBy,
        sortDirection,
    });

    const { orderStatuses } = useOrderStatuses();

    // Calcular rango de registros mostrados
    const firstItem = (page - 1) * _perPage + 1;
    const lastItem = Math.min(page * _perPage, total);

    // Función para cambiar el ordenamiento
    const handleSort = (field: string) => {
        if (sortBy === field) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc");
        } else {
            setSortBy(field);
            setSortDirection("desc");
        }
    };

    // Obtener un badge para el estado
    const getStatusBadge = (statusId: string) => {
        const status = orderStatuses.find((s) => s.id === statusId);

        if (!status) return null;

        return (
            <span
                className="px-2 py-1 text-xs rounded-full"
                style={{
                    backgroundColor: status.color,
                    color: getLuminance(status.color) > 0.5 ? "#000" : "#fff",
                }}
            >
                {status.name}
            </span>
        );
    };

    // Calcular si un color es claro u oscuro para determinar el color del texto
    const getLuminance = (hex: string): number => {
        // Convertir hex a RGB
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;

        // Calcular luminancia
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    // Renderizar estado de carga
    if (isLoading && !orders.length) {
        return <OrderTableSkeleton />;
    }

    // Renderizar estado vacío
    if (!isLoading && !orders.length) {
        return (
            <EmptyState
                buttonText="Limpiar filtros"
                description={
                    search
                        ? "No se encontraron pedidos que coincidan con tu búsqueda."
                        : "No hay pedidos disponibles en este momento."
                }
                title="Sin pedidos"
                onAction={() => {
                    setSearch("");
                    setStatusId("all");
                }}
            />
        );
    }

    return (
        <Card className={`w-full ${className}`}>
            <div className="p-4 pb-2">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                    <h2 className="text-xl font-bold">Pedidos</h2>
                    <div className="flex flex-col sm:flex-row w-full md:w-auto gap-2">
                        {/* Búsqueda */}
                        <div className="relative w-full sm:w-64">
                            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                className="pl-8"
                                placeholder="Buscar pedidos..."
                                value={search}
                                onChange={(e) => setSearch(e.target.value)}
                            />
                        </div>

                        {/* Filtro de estado - Reemplazado por select nativo */}
                        <select
                            className="w-full sm:w-40 rounded-md border border-gray-300 bg-white py-2 px-3 text-sm"
                            value={statusId}
                            onChange={(e) => setStatusId(e.target.value)}
                        >
                            <option value="all">Todos los estados</option>
                            {orderStatuses.map((status) => (
                                <option key={status.id} value={status.id}>
                                    {status.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
            </div>

            <div className="p-4">
                <div className="rounded-md border overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="w-full divide-y divide-gray-200 table-fixed">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th
                                        className="px-4 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        scope="col"
                                        onClick={() => handleSort("id")}
                                    >
                                        ID
                                        {sortBy === "id" && (
                                            <span className="ml-1">
                                                {sortDirection === "asc"
                                                    ? "↑"
                                                    : "↓"}
                                            </span>
                                        )}
                                    </th>
                                    <th
                                        className="px-4 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        scope="col"
                                        onClick={() =>
                                            handleSort("customer.name")
                                        }
                                    >
                                        Cliente
                                        {sortBy === "customer.name" && (
                                            <span className="ml-1">
                                                {sortDirection === "asc"
                                                    ? "↑"
                                                    : "↓"}
                                            </span>
                                        )}
                                    </th>
                                    <th
                                        className="px-4 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        scope="col"
                                        onClick={() =>
                                            handleSort("receivedDate")
                                        }
                                    >
                                        Fecha recibido
                                        {sortBy === "receivedDate" && (
                                            <span className="ml-1">
                                                {sortDirection === "asc"
                                                    ? "↑"
                                                    : "↓"}
                                            </span>
                                        )}
                                    </th>
                                    <th
                                        className="px-4 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        scope="col"
                                        onClick={() =>
                                            handleSort("estimatedDeliveryDate")
                                        }
                                    >
                                        Entrega estimada
                                        {sortBy === "estimatedDeliveryDate" && (
                                            <span className="ml-1">
                                                {sortDirection === "asc"
                                                    ? "↑"
                                                    : "↓"}
                                            </span>
                                        )}
                                    </th>
                                    <th
                                        className="px-4 py-3.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        scope="col"
                                        onClick={() =>
                                            handleSort("status.name")
                                        }
                                    >
                                        Estado
                                        {sortBy === "status.name" && (
                                            <span className="ml-1">
                                                {sortDirection === "asc"
                                                    ? "↑"
                                                    : "↓"}
                                            </span>
                                        )}
                                    </th>
                                    <th
                                        className="px-4 py-3.5 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                        scope="col"
                                    >
                                        Acciones
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {orders.map((order: any) => (
                                    <tr
                                        key={order.id}
                                        className="hover:bg-gray-50"
                                    >
                                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {order.transferNumber || "N/A"}
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {order.customer.name}
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <DateDisplay
                                                date={order.receivedDate}
                                            />
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {order.estimatedDeliveryDate ? (
                                                <DateDisplay
                                                    date={
                                                        order.estimatedDeliveryDate
                                                    }
                                                />
                                            ) : (
                                                <span className="text-gray-400">
                                                    No definida
                                                </span>
                                            )}
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {getStatusBadge(order.status.id)}
                                        </td>
                                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            {/* Reemplazado DropdownMenu con un menú básico */}
                                            <div className="relative inline-block text-left">
                                                <Button
                                                    size="sm"
                                                    variant="ghost"
                                                    onClick={() => {
                                                        const id = `dropdown-${order.id}`;
                                                        const el =
                                                            document.getElementById(
                                                                id,
                                                            );

                                                        if (el) {
                                                            el.classList.toggle(
                                                                "hidden",
                                                            );
                                                        }
                                                    }}
                                                >
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                                <div
                                                    className="hidden absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                                                    id={`dropdown-${order.id}`}
                                                >
                                                    <div className="py-1">
                                                        <a
                                                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                                            href={`/orders/${order.id}`}
                                                        >
                                                            Ver detalles
                                                        </a>
                                                        <a
                                                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                                            href={`/orders/${order.id}/edit`}
                                                        >
                                                            Editar
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Paginación */}
                    <div className="px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                        <div className="flex flex-col sm:flex-row justify-between items-center">
                            <div className="text-sm text-gray-700 mb-2 sm:mb-0">
                                Mostrando {firstItem} a {lastItem} de {total}{" "}
                                resultados
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button
                                    disabled={page <= 1 || isValidating}
                                    size="sm"
                                    variant="bordered"
                                    onClick={() =>
                                        setPage(page > 1 ? page - 1 : 1)
                                    }
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <div className="text-sm">
                                    Página {page} de {totalPages || 1}
                                </div>
                                <Button
                                    disabled={
                                        page >= totalPages || isValidating
                                    }
                                    size="sm"
                                    variant="bordered"
                                    onClick={() =>
                                        setPage(
                                            page < totalPages
                                                ? page + 1
                                                : totalPages,
                                        )
                                    }
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
}

// Componente de esqueleto para estado de carga
function OrderTableSkeleton() {
    return (
        <Card className="w-full">
            <div className="p-4">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                    <Skeleton className="h-8 w-32" />
                    <div className="flex flex-col sm:flex-row w-full md:w-auto gap-2">
                        <Skeleton className="h-10 w-full sm:w-64" />
                        <Skeleton className="h-10 w-full sm:w-40" />
                    </div>
                </div>

                <div className="rounded-md border mt-4">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead>
                                <tr>
                                    {Array(6)
                                        .fill(0)
                                        .map((_, i) => (
                                            <th key={i} className="px-4 py-3.5">
                                                <Skeleton className="h-4 w-full" />
                                            </th>
                                        ))}
                                </tr>
                            </thead>
                            <tbody>
                                {Array(5)
                                    .fill(0)
                                    .map((_, rowIndex) => (
                                        <tr key={rowIndex}>
                                            {Array(6)
                                                .fill(0)
                                                .map((_, colIndex) => (
                                                    <td
                                                        key={colIndex}
                                                        className="px-4 py-4"
                                                    >
                                                        <Skeleton className="h-5 w-full" />
                                                    </td>
                                                ))}
                                        </tr>
                                    ))}
                            </tbody>
                        </table>
                    </div>

                    <div className="px-4 py-3 border-t border-gray-200">
                        <div className="flex flex-col sm:flex-row justify-between items-center">
                            <Skeleton className="h-5 w-48 mb-2 sm:mb-0" />
                            <div className="flex items-center space-x-2">
                                <Skeleton className="h-8 w-8" />
                                <Skeleton className="h-5 w-20" />
                                <Skeleton className="h-8 w-8" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
}
