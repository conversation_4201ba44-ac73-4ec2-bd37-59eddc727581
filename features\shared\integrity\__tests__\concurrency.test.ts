import { IntegrityManager } from "../IntegrityManager";

import {
    setupIntegrityManager,
    simulateConcurrentOperations,
    createMockOperationLog,
    createVersionMismatchError,
    mockTransactionBehavior,
} from "./test-utils";

describe("IntegrityManager - Concurrency Tests", () => {
    let mockPrisma: any;
    let integrityManager: IntegrityManager;

    beforeEach(() => {
        const setup = setupIntegrityManager();

        mockPrisma = setup.prisma;
        integrityManager = setup.integrityManager;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Concurrent Assignment Creation", () => {
        it("should handle multiple users creating assignments simultaneously", async () => {
            // Arrange
            const users = ["user-1", "user-2", "user-3"];
            let operationCounter = 0;

            mockPrisma.operationLog.create.mockImplementation(() => {
                operationCounter++;

                return Promise.resolve(
                    createMockOperationLog({ id: `op-${operationCounter}` }),
                );
            });

            mockPrisma.operationLog.update.mockResolvedValue({});
            mockPrisma.assignment.createMany.mockResolvedValue({ count: 5 });
            mockTransactionBehavior(mockPrisma, "success");

            // Act - Simulate concurrent operations
            const operations = users.map(
                (userId) => () =>
                    integrityManager.executeWithIntegrity(
                        {
                            type: "ASSIGNMENT_BATCH",
                            userId,
                            metadata: { assignmentCount: 5 },
                        },
                        async () => {
                            // Simulate some work
                            await new Promise((resolve) =>
                                setTimeout(resolve, 100),
                            );

                            return { success: true, userId };
                        },
                        jest.fn(),
                    ),
            );

            const results = await simulateConcurrentOperations(operations);

            // Assert
            expect(results).toHaveLength(3);
            expect(results.every((r) => r.success)).toBe(true);
            expect(mockPrisma.operationLog.create).toHaveBeenCalledTimes(3);
            expect(operationCounter).toBe(3);
        });

        it("should handle concurrent updates to same garment size", async () => {
            // Arrange
            const garmentSizeId = "size-123";
            let currentVersion = 1;
            let updateAttempts = 0;

            mockPrisma.garmentSize.findUnique.mockImplementation(() => {
                return Promise.resolve({
                    id: garmentSizeId,
                    version: currentVersion,
                    availableQuantity: 100,
                });
            });

            mockPrisma.garmentSize.update.mockImplementation((args: any) => {
                updateAttempts++;
                if (updateAttempts < 3 && Math.random() > 0.5) {
                    // Simulate occasional version conflicts
                    throw createVersionMismatchError();
                }
                currentVersion++;

                return Promise.resolve({
                    id: garmentSizeId,
                    version: currentVersion,
                    availableQuantity: args.data.availableQuantity.decrement
                        ? 100 - args.data.availableQuantity.decrement
                        : 100,
                });
            });

            // Act - Multiple concurrent updates
            const operations = Array.from(
                { length: 5 },
                (_, i) => () =>
                    integrityManager
                        .checkVersion(
                            { id: garmentSizeId, version: currentVersion },
                            mockPrisma.garmentSize,
                        )
                        .then(() =>
                            mockPrisma.garmentSize.update({
                                where: { id: garmentSizeId },
                                data: { availableQuantity: { decrement: 10 } },
                            }),
                        ),
            );

            const results = await simulateConcurrentOperations(operations, 50); // Small delay

            // Assert
            expect(results).toHaveLength(5);
            expect(updateAttempts).toBeGreaterThanOrEqual(5); // Some retries due to conflicts
        });
    });

    describe("Operation Isolation", () => {
        it("should isolate operations from different users", async () => {
            // Arrange
            const user1Operations: string[] = [];
            const user2Operations: string[] = [];

            mockPrisma.operationLog.create.mockImplementation((args: any) => {
                const userId = args.data.userId;
                const opId = `op-${userId}-${Date.now()}`;

                if (userId === "user-1") {
                    user1Operations.push(opId);
                } else if (userId === "user-2") {
                    user2Operations.push(opId);
                }

                return Promise.resolve(
                    createMockOperationLog({ id: opId, userId }),
                );
            });

            mockPrisma.operationLog.update.mockResolvedValue({});
            mockTransactionBehavior(mockPrisma, "success");

            // Act - Concurrent operations from different users
            const operations = [
                () =>
                    integrityManager.executeWithIntegrity(
                        {
                            type: "ASSIGNMENT_BATCH",
                            userId: "user-1",
                            metadata: {},
                        },
                        async () => ({ userId: "user-1" }),
                        jest.fn(),
                    ),
                () =>
                    integrityManager.executeWithIntegrity(
                        {
                            type: "ASSIGNMENT_BATCH",
                            userId: "user-2",
                            metadata: {},
                        },
                        async () => ({ userId: "user-2" }),
                        jest.fn(),
                    ),
            ];

            await simulateConcurrentOperations(operations);

            // Assert
            expect(user1Operations).toHaveLength(1);
            expect(user2Operations).toHaveLength(1);
            expect(user1Operations[0]).toContain("user-1");
            expect(user2Operations[0]).toContain("user-2");
        });
    });

    describe("Deadlock Prevention", () => {
        it("should prevent deadlocks by consistent resource ordering", async () => {
            // Arrange
            const resources = ["size-1", "size-2", "size-3"];
            const updateOrder: string[] = [];

            mockPrisma.garmentSize.update.mockImplementation((args: any) => {
                updateOrder.push(args.where.id);

                return Promise.resolve({ id: args.where.id });
            });

            mockTransactionBehavior(mockPrisma, "success");

            // Act - Multiple transactions updating same resources
            const operations = [
                () =>
                    mockPrisma.$transaction(async (tx: any) => {
                        // Always update in sorted order
                        for (const id of resources.sort()) {
                            await tx.garmentSize.update({
                                where: { id },
                                data: { availableQuantity: { decrement: 5 } },
                            });
                        }
                    }),
                () =>
                    mockPrisma.$transaction(async (tx: any) => {
                        // Same resources, same order
                        for (const id of resources.sort()) {
                            await tx.garmentSize.update({
                                where: { id },
                                data: { availableQuantity: { decrement: 3 } },
                            });
                        }
                    }),
            ];

            await simulateConcurrentOperations(operations);

            // Assert - Resources always accessed in same order
            expect(updateOrder).toEqual([
                "size-1",
                "size-2",
                "size-3", // First transaction
                "size-1",
                "size-2",
                "size-3", // Second transaction
            ]);
        });
    });

    describe("Race Conditions", () => {
        it("should handle race condition in assignment count validation", async () => {
            // Arrange
            let availableQuantity = 50;
            const requestedQuantities = [30, 25]; // Total 55, but only 50 available

            mockPrisma.garmentSize.findUnique.mockImplementation(() => {
                return Promise.resolve({
                    id: "size-123",
                    availableQuantity,
                    version: 1,
                });
            });

            mockPrisma.garmentSize.update.mockImplementation((args: any) => {
                const decrement = args.data.availableQuantity.decrement;

                if (availableQuantity - decrement < 0) {
                    throw new Error("Insufficient quantity");
                }
                availableQuantity -= decrement;

                return Promise.resolve({
                    id: "size-123",
                    availableQuantity,
                });
            });

            // Act - Race to claim quantities
            const operations = requestedQuantities.map(
                (quantity) => async () => {
                    const size = await mockPrisma.garmentSize.findUnique({
                        where: { id: "size-123" },
                    });

                    if (size.availableQuantity >= quantity) {
                        return mockPrisma.garmentSize.update({
                            where: { id: "size-123" },
                            data: {
                                availableQuantity: { decrement: quantity },
                            },
                        });
                    }
                    throw new Error("Insufficient quantity");
                },
            );

            const results = await simulateConcurrentOperations(operations);

            // Assert - Only one should succeed, other should fail
            const successes = results.filter((r) => !(r instanceof Error));
            const failures = results.filter((r) => r instanceof Error);

            expect(successes).toHaveLength(1); // Only one transaction succeeds
            expect(failures).toHaveLength(1); // One fails due to insufficient quantity
            expect(availableQuantity).toBe(20); // 50 - 30 = 20
        });
    });

    describe("Stuck Operation Recovery", () => {
        it("should handle concurrent recovery attempts", async () => {
            // Arrange
            const stuckOperation = createMockOperationLog({
                id: "stuck-op-1",
                status: "IN_PROGRESS",
                startedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
            });

            let recoveryAttempts = 0;

            mockPrisma.operationLog.findMany.mockResolvedValue([
                stuckOperation,
            ]);
            mockPrisma.operationLog.update.mockImplementation(() => {
                recoveryAttempts++;
                if (recoveryAttempts === 1) {
                    return Promise.resolve({
                        ...stuckOperation,
                        status: "FAILED",
                        failedAt: new Date(),
                    });
                }
                throw new Error("Operation already processed");
            });

            // Act - Multiple recovery processes running
            const operations = Array.from({ length: 3 }, () => async () => {
                const stuck = await integrityManager.findStuckOperations(15);

                if (stuck.length > 0) {
                    return mockPrisma.operationLog.update({
                        where: { id: stuck[0].id },
                        data: {
                            status: "FAILED",
                            failedAt: new Date(),
                            errorMessage:
                                "Timeout - marked as failed by recovery process",
                        },
                    });
                }
            });

            const results = await simulateConcurrentOperations(operations);

            // Assert - Only one recovery should succeed
            const successes = results.filter((r) => !(r instanceof Error));

            expect(successes).toHaveLength(1);
            expect(recoveryAttempts).toBeGreaterThanOrEqual(2); // Multiple attempts
        });
    });
});
