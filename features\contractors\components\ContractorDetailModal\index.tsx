"use client";

import React from "react";
import {
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

import {
    Modal,
    ModalContent,
    ModalBody,
    <PERSON>dal<PERSON>ooter,
    <PERSON><PERSON>,
    Spinner,
    addToast,
} from "@/shared/components/ui/hero-ui-client";

import ContractorModalHeader from "./components/ContractorModalHeader";
import ContractorModalTabs from "./components/ContractorModalTabs";
import ContractorInfoSection from "./components/ContractorInfoSection";
import ContractorAssignmentsList from "./components/ContractorAssignmentsList";
import ContractorHistorySection from "./components/ContractorHistorySection";
import { useContractorModal } from "./hooks/useContractorModal";
import { ContractorModalProps } from "./types/contractor-modal.types";

export default function ContractorDetailModal({
    isOpen,
    onClose,
    contractorId,
    onEdit,
    onDelete,
    currentUserId,
    userRole,
}: ContractorModalProps) {
    const {
        contractor,
        isLoading,
        activeTab,
        setActiveTab,
        isDeleting,
        setIsDeleting,
    } = useContractorModal({ contractorId });

    const handleDelete = async () => {
        if (!contractorId || !onDelete) return;

        try {
            setIsDeleting(true);
            await onDelete(contractorId);
            addToast({
                title: "Éxito",
                description: "Contratista eliminado exitosamente",
                color: "success",
                icon: <CheckCircleIcon className="w-5 h-5" />,
            });
            onClose();
        } catch (error) {
            addToast({
                title: "Error",
                description:
                    "No se pudo eliminar el contratista. Puede tener asignaciones activas.",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <Modal
            backdrop="blur"
            classNames={{
                backdrop: "bg-black/50 backdrop-blur-sm",
                base: "bg-background max-h-[95vh] max-w-7xl",
                body: "p-0 overflow-y-auto",
                wrapper: "overflow-y-hidden",
            }}
            isDismissable={false}
            isOpen={isOpen}
            shouldBlockScroll={true}
            size="5xl"
            onClose={onClose}
        >
            <ModalContent>
                {(onCloseModal) => (
                    <>
                        {isLoading ? (
                            <div className="flex flex-col items-center justify-center py-20">
                                <Spinner color="primary" size="lg" />
                                <p className="mt-4 text-gray-500 dark:text-gray-400">
                                    Cargando información del contratista...
                                </p>
                            </div>
                        ) : contractor ? (
                            <>
                                {/* Header */}
                                <ContractorModalHeader
                                    contractor={contractor}
                                    isDeleting={isDeleting}
                                    onDelete={handleDelete}
                                    onEdit={onEdit}
                                />

                                {/* Tabs */}
                                <ContractorModalTabs
                                    activeTab={activeTab}
                                    assignmentCount={
                                        contractor._count?.assignments || 0
                                    }
                                    remissionCount={
                                        contractor._count?.remissions || 0
                                    }
                                    onTabChange={setActiveTab}
                                />

                                {/* Content */}
                                <ModalBody className="px-6 py-4">
                                    {activeTab === "general" && (
                                        <ContractorInfoSection
                                            contractor={contractor}
                                        />
                                    )}
                                    {activeTab === "assignments" && (
                                        <ContractorAssignmentsList
                                            assignments={
                                                contractor.assignments || []
                                            }
                                            contractorName={contractor.name}
                                        />
                                    )}
                                    {activeTab === "history" && (
                                        <ContractorHistorySection
                                            contractorId={contractorId}
                                        />
                                    )}
                                </ModalBody>

                                {/* Footer */}
                                <ModalFooter className="border-t border-gray-200 dark:border-gray-700">
                                    <Button
                                        variant="flat"
                                        onPress={onCloseModal}
                                    >
                                        Cerrar
                                    </Button>
                                </ModalFooter>
                            </>
                        ) : (
                            <div className="text-center py-20">
                                <p className="text-gray-500">
                                    No se encontró información del contratista
                                </p>
                            </div>
                        )}
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
