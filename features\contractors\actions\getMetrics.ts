"use server";

import { prisma } from "@/shared/lib/prisma";

/**
 * Obtiene métricas generales de todos los contratistas
 */
export async function getContractorsOverallMetrics() {
    try {
        const [
            totalContractors,
            totalActiveAssignments,
            overallCompletionRate,
        ] = await Promise.all([
            // Total de contratistas disponibles
            prisma.contractor.count(),

            // Total de asignaciones activas en el sistema
            prisma.assignment.count({
                where: {
                    status: "ACTIVE",
                    cancelledAt: null,
                },
            }),

            // Tasa de cumplimiento general (asignaciones completadas vs total)
            prisma.assignment
                .groupBy({
                    by: ["isCompleted"],
                    _count: true,
                })
                .then((results) => {
                    const completed =
                        results.find((r) => r.isCompleted)?._count || 0;
                    const total = results.reduce((sum, r) => sum + r._count, 0);

                    return total > 0
                        ? Math.round((completed / total) * 100)
                        : 0;
                }),
        ]);

        return {
            success: true,
            data: {
                totalContractors,
                totalActiveAssignments,
                overallCompletionRate,
            },
        };
    } catch (error) {
        console.error("Error al obtener métricas generales:", error);

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener métricas generales",
        };
    }
}

/**
 * Obtiene métricas específicas de un contratista
 */
export async function getContractorMetrics(contractorId: string) {
    try {
        const [activeOrders, lastDelivery, totalCompleted, contractor] =
            await Promise.all([
                // Órdenes activas del contratista
                prisma.assignment.count({
                    where: {
                        contractorId,
                        status: "ACTIVE",
                        cancelledAt: null,
                    },
                }),

                // Última entrega (remisión más reciente)
                prisma.remission.findFirst({
                    where: { contractorId },
                    orderBy: { createdAt: "desc" },
                    select: { createdAt: true },
                }),

                // Total de piezas completadas históricamente
                prisma.assignment.aggregate({
                    where: {
                        contractorId,
                        isCompleted: true,
                    },
                    _sum: {
                        quantity: true,
                    },
                }),

                // Información básica del contratista
                prisma.contractor.findUnique({
                    where: { id: contractorId },
                    select: {
                        id: true,
                        name: true,
                        _count: {
                            select: {
                                assignments: {
                                    where: {
                                        status: "ACTIVE",
                                    },
                                },
                            },
                        },
                    },
                }),
            ]);

        // Calcular días desde la última entrega
        let daysSinceLastDelivery = null;

        if (lastDelivery) {
            const now = new Date();
            const diffTime = Math.abs(
                now.getTime() - lastDelivery.createdAt.getTime(),
            );

            daysSinceLastDelivery = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        }

        return {
            success: true,
            data: {
                contractorId,
                contractorName: contractor?.name || "Desconocido",
                activeOrders,
                lastDeliveryDate: lastDelivery?.createdAt || null,
                daysSinceLastDelivery,
                totalCompletedPieces: totalCompleted._sum.quantity || 0,
                activeAssignments: contractor?._count.assignments || 0,
            },
        };
    } catch (error) {
        console.error("Error al obtener métricas del contratista:", error);

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener métricas del contratista",
        };
    }
}

/**
 * Obtiene métricas de rendimiento de múltiples contratistas
 */
export async function getContractorsPerformanceMetrics(
    contractorIds?: string[],
) {
    try {
        const whereClause = contractorIds?.length
            ? { id: { in: contractorIds } }
            : {};

        const contractors = await prisma.contractor.findMany({
            where: whereClause,
            select: {
                id: true,
                name: true,
                _count: {
                    select: {
                        assignments: {
                            where: {
                                status: "ACTIVE",
                                cancelledAt: null,
                            },
                        },
                        remissions: true,
                    },
                },
                assignments: {
                    select: {
                        quantity: true,
                        isCompleted: true,
                        createdAt: true,
                        updatedAt: true,
                    },
                },
                remissions: {
                    orderBy: { createdAt: "desc" },
                    take: 1,
                    select: { createdAt: true },
                },
            },
        });

        const metricsData = contractors.map((contractor) => {
            // Calcular métricas individuales
            const totalAssignments = contractor.assignments.length;
            const completedAssignments = contractor.assignments.filter(
                (a) => a.isCompleted,
            ).length;
            const completionRate =
                totalAssignments > 0
                    ? Math.round(
                          (completedAssignments / totalAssignments) * 100,
                      )
                    : 0;

            const totalPieces = contractor.assignments.reduce(
                (sum, a) => sum + a.quantity,
                0,
            );
            const completedPieces = contractor.assignments
                .filter((a) => a.isCompleted)
                .reduce((sum, a) => sum + a.quantity, 0);

            // Última actividad
            const lastRemission = contractor.remissions[0];
            let daysSinceLastActivity = null;

            if (lastRemission) {
                const now = new Date();
                const diffTime = Math.abs(
                    now.getTime() - lastRemission.createdAt.getTime(),
                );

                daysSinceLastActivity = Math.ceil(
                    diffTime / (1000 * 60 * 60 * 24),
                );
            }

            return {
                id: contractor.id,
                name: contractor.name,
                activeOrders: contractor._count.assignments,
                totalRemissions: contractor._count.remissions,
                completionRate,
                totalPieces,
                completedPieces,
                lastActivityDays: daysSinceLastActivity,
            };
        });

        return {
            success: true,
            data: metricsData,
        };
    } catch (error) {
        console.error("Error al obtener métricas del contratista:", error);

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener métricas del contratista",
        };
    }
}
