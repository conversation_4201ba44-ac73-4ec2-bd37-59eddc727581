"use client";

import { Card } from "@heroui/card";

import { useOrdersInfinite } from "../../hooks/useOrdersOptimized";

import { VirtualizedList } from "./VirtualizedList";

interface OptimizedOrderTableProps {
    statusId?: string;
    customerId?: string;
}

export function OptimizedOrderTable({
    statusId,
    customerId,
}: OptimizedOrderTableProps) {
    const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } =
        useOrdersInfinite({ statusId, customerId });

    const orders =
        (data as any)?.pages?.flatMap((page: any) => page.items) ?? [];

    const renderOrder = (
        order: any,
        index: number,
        style: React.CSSProperties,
    ) => (
        <Card isHoverable isPressable className="mb-2 mx-2" style={style}>
            <div className="p-4 flex items-center justify-between">
                <div className="flex-1">
                    <h4 className="font-semibold">
                        {order.transferNumber || `Order ${index + 1}`}
                    </h4>
                    <p className="text-sm text-gray-600">
                        {order.customer.name}
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <span
                        className="px-2 py-1 rounded text-sm"
                        style={{
                            backgroundColor: order.status.color || "#gray",
                        }}
                    >
                        {order.status.name}
                    </span>
                    <span className="text-sm text-gray-500">
                        {order._count.parts} parts
                    </span>
                </div>
            </div>
        </Card>
    );

    if (isLoading) {
        return <div className="p-8 text-center">Loading orders...</div>;
    }

    return (
        <div className="w-full">
            <VirtualizedList
                height={600}
                itemHeight={80}
                items={orders}
                renderItem={renderOrder}
                threshold={50}
                onEndReached={() => {
                    if (hasNextPage && !isFetchingNextPage) {
                        fetchNextPage();
                    }
                }}
            />
            {isFetchingNextPage && (
                <div className="text-center p-4">Loading more...</div>
            )}
        </div>
    );
}
