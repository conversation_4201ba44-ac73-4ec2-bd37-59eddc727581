# 🔐 Auth Feature

## 📋 Descripción
Autenticación, autorización y gestión de usuarios con NextAuth.

## 🏗️ Componentes

### LoginForm
Formulario de inicio de sesión.

### UserProfile
Perfil de usuario.

### WebAuthnButton
Autenticación biométrica.

## 🪝 Hooks

### useUser
Hook de usuario actual.
```tsx
const { user, isLoading, isAuthenticated } = useUser()
```

### useAuth
Funciones de autenticación.
```tsx
const { signIn, signOut, signUp } = useAuth()
```

## 🎯 Uso

```tsx
import { useUser, LoginForm } from '@/features/auth'

export default function Header() {
  const { user, isAuthenticated } = useUser()
  
  if (!isAuthenticated) {
    return <LoginForm />
  }
  
  return <div>Welcome, {user.name}</div>
}
```