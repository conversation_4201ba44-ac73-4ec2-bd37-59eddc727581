/**
 * Sistema de generación de folios para remisiones
 *
 * Formato: LOH-MMDD-XXX
 * - LOH: Prefijo de la empresa
 * - MMDD: Mes y día
 * - XXX: Secuencial del día (001, 002...)
 */

const COMPANY_PREFIX = "LOH";
const STORAGE_KEY = "lohari_remission_folios";

interface StoredFolios {
    lastDate: string;
    lastSequence: number;
    all: string[];
}

/**
 * Obtiene los folios almacenados o inicializa el almacenamiento
 */
function getStoredFolios(): StoredFolios {
    // Si estamos en el servidor, devolvemos valores por defecto
    if (typeof window === "undefined") {
        return {
            lastDate: "",
            lastSequence: 0,
            all: [],
        };
    }

    const stored = localStorage.getItem(STORAGE_KEY);

    if (!stored) {
        const initialData = {
            lastDate: "",
            lastSequence: 0,
            all: [],
        };

        localStorage.setItem(STORAGE_KEY, JSON.stringify(initialData));

        return initialData;
    }

    return JSON.parse(stored);
}

/**
 * Actualiza los folios almacenados
 */
function updateStoredFolios(folios: StoredFolios): void {
    if (typeof window === "undefined") return;
    localStorage.setItem(STORAGE_KEY, JSON.stringify(folios));
}

/**
 * Genera un nuevo folio de remisión
 */
export function generateFolio(): string {
    // Obtener fecha actual en formato MMDD
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const dateStr = `${month}${day}`;

    // Para comparación necesitamos la fecha completa
    const fullDateStr = now.getFullYear().toString() + month + day;

    // Obtener datos almacenados
    const storedFolios = getStoredFolios();

    // Determinar secuencia
    let sequence = 1;

    if (storedFolios.lastDate === fullDateStr) {
        sequence = storedFolios.lastSequence + 1;
    }

    // Formatear secuencia como XXX (3 dígitos)
    const sequenceStr = sequence.toString().padStart(3, "0");

    // Generar folio simplificado
    const fullFolio = `${COMPANY_PREFIX}-${dateStr}-${sequenceStr}`;

    // Actualizar almacenamiento
    storedFolios.lastDate = fullDateStr;
    storedFolios.lastSequence = sequence;
    storedFolios.all.push(fullFolio);
    updateStoredFolios(storedFolios);

    return fullFolio;
}

/**
 * Verifica si un folio ya existe
 */
export function folioExists(folio: string): boolean {
    const storedFolios = getStoredFolios();

    return storedFolios.all.includes(folio);
}

/**
 * Valida si un folio tiene el formato correcto
 */
export function validateFolio(folio: string): boolean {
    // Comprobar formato: LOH-MMDD-XXX
    const regex = /^LOH-\d{4}-\d{3}$/;

    return regex.test(folio);
}

/**
 * Exporta todos los folios para respaldo
 */
export function exportFolios(): string {
    const storedFolios = getStoredFolios();

    return JSON.stringify(storedFolios);
}

/**
 * Importa folios desde un respaldo
 */
export function importFolios(data: string): boolean {
    try {
        const parsedData = JSON.parse(data) as StoredFolios;

        // Verificar estructura básica
        if (
            !parsedData.lastDate ||
            typeof parsedData.lastSequence !== "number" ||
            !Array.isArray(parsedData.all)
        ) {
            return false;
        }

        // Verificar cada folio
        const allValid = parsedData.all.every(validateFolio);

        if (!allValid) return false;

        // Actualizar almacenamiento
        updateStoredFolios(parsedData);

        return true;
    } catch (error) {
        // REMOVED: console.error("Error al importar folios:", error);

        return false;
    }
}
