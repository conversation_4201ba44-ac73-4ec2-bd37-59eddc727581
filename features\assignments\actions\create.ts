"use server";

import { db } from "@/shared/lib/db";

import { createAssignmentsSchema, AssignmentResponse } from "../schemas/schema";

import { revalidateAssignmentPaths } from "./revalidate";

/**
 * <PERSON><PERSON> múltiples asignaciones en una transacción
 */
export async function createAssignments(data: {
    contractorId: string;
    assignments: {
        orderId: string;
        garmentSizeId: string;
        quantity: number;
    }[];
}): Promise<AssignmentResponse> {
    try {
        // Validar datos con Zod
        const validationResult = createAssignmentsSchema.safeParse(data);

        if (!validationResult.success) {
            return {
                success: false,
                error: "Datos de asignación inválidos",
            };
        }

        const { contractorId, assignments } = validationResult.data;

        // Verificar que el contratista exista
        const contractor = await db.contractor.findUnique({
            where: { id: contractorId },
        });

        if (!contractor) {
            return {
                success: false,
                error: "El contratista seleccionado no existe",
            };
        }

        // Validar que todas las asignaciones tengan cantidades válidas
        const validationPromises = assignments.map(async (assignment) => {
            const garmentSize = await db.garmentSize.findUnique({
                where: { id: assignment.garmentSizeId },
                select: {
                    totalQuantity: true,
                    usedQuantity: true,
                    garment: {
                        select: {
                            orderId: true,
                            model: {
                                select: {
                                    code: true,
                                },
                            },
                            color: {
                                select: {
                                    name: true,
                                },
                            },
                        },
                    },
                    size: {
                        select: {
                            code: true,
                        },
                    },
                },
            });

            if (!garmentSize) {
                return {
                    valid: false,
                    error: `La talla con ID ${assignment.garmentSizeId} no existe`,
                };
            }

            // Validar que la talla pertenezca a la orden especificada
            if (garmentSize.garment.orderId !== assignment.orderId) {
                return {
                    valid: false,
                    error: `La talla no pertenece a la orden especificada`,
                    details: {
                        garmentSizeId: assignment.garmentSizeId,
                        orderId: assignment.orderId,
                        actualOrderId: garmentSize.garment.orderId,
                    },
                };
            }

            // Calcular disponibilidad
            const availableQuantity =
                garmentSize.totalQuantity - garmentSize.usedQuantity;

            // Validar que haya suficiente disponibilidad
            if (assignment.quantity > availableQuantity) {
                return {
                    valid: false,
                    error: `Cantidad insuficiente disponible para ${garmentSize.garment.model.code} ${garmentSize.garment.color.name} talla ${garmentSize.size.code}`,
                    details: {
                        requested: assignment.quantity,
                        available: availableQuantity,
                        garmentSizeId: assignment.garmentSizeId,
                    },
                };
            }

            return {
                valid: true,
                garmentSize,
            };
        });

        const validationResults = await Promise.all(validationPromises);
        const invalidAssignments = validationResults.filter(
            (result) => !result.valid,
        );

        // Si hay asignaciones inválidas, retornar error
        if (invalidAssignments.length > 0) {
            return {
                success: false,
                error: "Algunas asignaciones no son válidas",
                data: {
                    errors: invalidAssignments,
                },
            };
        }

        // Crear todas las asignaciones en una transacción
        const assignmentsResult = await db.$transaction(async (tx) => {
            // Define the type using the return type of assignment.create
            const createdAssignments: Awaited<
                ReturnType<typeof tx.assignment.create>
            >[] = [];

            for (const assignment of assignments) {
                // Crear la asignación
                const newAssignment = await tx.assignment.create({
                    data: {
                        contractorId,
                        garmentSizeId: assignment.garmentSizeId,
                        orderId: assignment.orderId,
                        quantity: assignment.quantity,
                        isCompleted: false,
                    },
                    include: {
                        garmentSize: {
                            include: {
                                size: true,
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                            },
                        },
                    },
                });

                // Actualizar el contador de usedQuantity en la talla
                await tx.garmentSize.update({
                    where: { id: assignment.garmentSizeId },
                    data: {
                        usedQuantity: {
                            increment: assignment.quantity,
                        },
                    },
                });

                createdAssignments.push(newAssignment);
            }

            return createdAssignments;
        });

        // Revalidar rutas afectadas
        revalidateAssignmentPaths();

        // Retornar solo las asignaciones creadas
        // La remisión se creará en una página separada
        return {
            success: true,
            data: {
                assignments: assignmentsResult,
                assignmentIds: assignmentsResult.map((a) => a.id),
                contractorId,
            } as any,
        };
    } catch (error) {
        // REMOVED: console.error("Error al crear asignaciones:", error);

        return {
            success: false,
            error: "Error al crear las asignaciones",
        };
    }
}

/**
 * Validar la disponibilidad de una talla para asignación
 */
export async function validateAssignmentAvailability(
    garmentSizeId: string,
    quantity: number,
): Promise<AssignmentResponse> {
    try {
        const garmentSize = await db.garmentSize.findUnique({
            where: { id: garmentSizeId },
            select: {
                totalQuantity: true,
                usedQuantity: true,
                size: {
                    select: {
                        code: true,
                    },
                },
                garment: {
                    select: {
                        model: {
                            select: {
                                code: true,
                            },
                        },
                        color: {
                            select: {
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        if (!garmentSize) {
            return {
                success: false,
                error: `La talla con ID ${garmentSizeId} no existe`,
            };
        }

        const availableQuantity =
            garmentSize.totalQuantity - garmentSize.usedQuantity;

        // Validar que haya suficiente disponibilidad
        if (quantity > availableQuantity) {
            return {
                success: false,
                error: `Cantidad insuficiente disponible. Máximo: ${availableQuantity}`,
                data: {
                    requested: quantity,
                    available: availableQuantity,
                    garmentSizeId,
                    modelCode: garmentSize.garment.model.code,
                    colorName: garmentSize.garment.color.name,
                    sizeCode: garmentSize.size.code,
                } as any,
            };
        }

        return {
            success: true,
            data: {
                valid: true,
                requested: quantity,
                available: availableQuantity,
                garmentSizeId,
                modelCode: garmentSize.garment.model.code,
                colorName: garmentSize.garment.color.name,
                sizeCode: garmentSize.size.code,
            } as any,
        };
    } catch (error) {
        // REMOVED: console.error("Error al validar disponibilidad:", error);

        return {
            success: false,
            error: "Error al validar la disponibilidad",
        };
    }
}
