/**
 * Sizes Store (Zustand)
 *
 * Centralized state management for the sizes module.
 * Provides reactive state updates and computed values.
 */

// import { create } from "zustand"; // Commented out - zustand not installed
// import { devtools } from "zustand/middleware";
// import { immer } from "zustand/middleware/immer";
// import { subscribeWithSelector } from "zustand/middleware";

import {
    SizeResponseDTO,
    ListSizesQueryDTO,
    CreateSizeDTO,
    UpdateSizeDTO,
} from "../../application/dto/size.dto";

interface SizesState {
    // State
    sizes: SizeResponseDTO[];
    selectedSize: SizeResponseDTO | null;
    isLoading: boolean;
    error: string | null;
    filters: ListSizesQueryDTO;
    total: number;
    hasMore: boolean;

    // Computed values
    activeSizes: () => SizeResponseDTO[];
    sizesByCategory: (
        category: "numeric" | "alpha" | "custom",
    ) => SizeResponseDTO[];
    getSizeById: (id: string) => SizeResponseDTO | undefined;
    getSizeByName: (name: string) => SizeResponseDTO | undefined;

    // Actions
    setSizes: (sizes: SizeResponseDTO[]) => void;
    addSize: (size: SizeResponseDTO) => void;
    updateSize: (id: string, updates: Partial<SizeResponseDTO>) => void;
    removeSize: (id: string) => void;
    selectSize: (size: SizeResponseDTO | null) => void;
    setLoading: (isLoading: boolean) => void;
    setError: (error: string | null) => void;
    setFilters: (filters: Partial<ListSizesQueryDTO>) => void;
    setPagination: (total: number, hasMore: boolean) => void;

    // Async actions (would call use cases in real implementation)
    fetchSizes: (filters?: ListSizesQueryDTO) => Promise<void>;
    createSize: (data: CreateSizeDTO) => Promise<void>;
    updateSizeAction: (id: string, data: UpdateSizeDTO) => Promise<void>;
    deleteSize: (id: string) => Promise<void>;
}

const initialState = {
    sizes: [],
    selectedSize: null,
    isLoading: false,
    error: null,
    filters: {
        orderBy: "createdAt" as const,
        orderDirection: "desc" as const,
    },
    total: 0,
    hasMore: false,
};

// Commented out the entire zustand store implementation since zustand is not installed
// export const useSizesStore = create<SizesState>()(...);

// Simple fallback export to prevent import errors
export const useSizesStore = {} as any;
