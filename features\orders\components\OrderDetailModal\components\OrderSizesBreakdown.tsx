"use client";

import type { OrderGarment } from "../types/order-modal.types";

import React, { useMemo } from "react";
import { Card, CardBody, Badge, Tooltip, Progress, Chip } from "@heroui/react";
import { ChartBarSquareIcon, SparklesIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface OrderSizesBreakdownProps {
    garments: OrderGarment[];
}

export default function OrderSizesBreakdown({
    garments,
}: OrderSizesBreakdownProps) {
    // Calcular métricas para cada prenda
    const garmentMetrics = useMemo(() => {
        return garments.map((garment) => {
            const sizes = garment.sizes || [];
            const totalQuantity = sizes.reduce(
                (sum, size) => sum + size.totalQuantity,
                0,
            );
            const totalUsed = sizes.reduce(
                (sum, size) => sum + (size.usedQuantity || 0),
                0,
            );
            const availabilityRate =
                totalQuantity > 0
                    ? ((totalQuantity - totalUsed) / totalQuantity) * 100
                    : 100;

            // Encontrar la talla con mayor cantidad
            const popularSize = sizes.reduce(
                (prev, current) =>
                    current.totalQuantity > (prev?.totalQuantity || 0)
                        ? current
                        : prev,
                sizes[0],
            );

            return {
                ...garment,
                totalQuantity,
                totalUsed,
                availabilityRate,
                popularSizeCode: popularSize?.size?.code,
                sizesData: sizes.map((size) => ({
                    ...size,
                    availabilityPercent:
                        size.totalQuantity > 0
                            ? ((size.totalQuantity - (size.usedQuantity || 0)) /
                                  size.totalQuantity) *
                              100
                            : 100,
                })),
            };
        });
    }, [garments]);

    const getAvailabilityColor = (percent: number) => {
        if (percent >= 80) return "success";
        if (percent >= 50) return "warning";
        if (percent >= 20) return "danger";

        return "default";
    };

    const getIntensityClass = (quantity: number, maxQuantity: number) => {
        if (maxQuantity === 0) return "";
        const ratio = quantity / maxQuantity;

        if (ratio >= 0.8) return "bg-primary/20 border-primary/60";
        if (ratio >= 0.6) return "bg-primary/15 border-primary/40";
        if (ratio >= 0.4) return "bg-primary/10 border-primary/30";
        if (ratio >= 0.2) return "bg-primary/5 border-primary/20";

        return "";
    };

    if (!garments || garments.length === 0) {
        return null;
    }

    return (
        <Card className="border border-default-200">
            <CardBody className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                            <ChartBarSquareIcon className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-foreground">
                                Desglose de Tallas y Cantidades
                            </h3>
                            <p className="text-sm text-default-500">
                                {garments.length} modelo
                                {garments.length !== 1 ? "s" : ""} •{" "}
                                {garmentMetrics.reduce(
                                    (sum, g) => sum + g.totalQuantity,
                                    0,
                                )}{" "}
                                unidades totales
                            </p>
                        </div>
                    </div>
                    <Tooltip content="Vista detallada con indicadores de disponibilidad">
                        <SparklesIcon className="w-5 h-5 text-warning" />
                    </Tooltip>
                </div>

                {/* Garments List */}
                <div className="space-y-6">
                    {garmentMetrics.map((garment, garmentIndex) => {
                        const maxQuantity = Math.max(
                            ...garment.sizesData.map((s) => s.totalQuantity),
                        );

                        return (
                            <motion.div
                                key={garment.id || garmentIndex}
                                animate={{ opacity: 1, y: 0 }}
                                className="space-y-4"
                                initial={{ opacity: 0, y: 20 }}
                                transition={{
                                    duration: 0.3,
                                    delay: garmentIndex * 0.1,
                                }}
                            >
                                {/* Garment Header */}
                                <div className="flex items-center justify-between flex-wrap gap-2">
                                    <div className="flex items-center gap-3">
                                        <Badge
                                            color="primary"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {garment.model?.code}
                                        </Badge>
                                        <span className="text-sm font-medium">
                                            {garment.model?.description}
                                        </span>
                                        <div className="flex items-center gap-2">
                                            <div
                                                className="w-4 h-4 rounded-full border-2 border-default-300"
                                                style={{
                                                    backgroundColor:
                                                        garment.color
                                                            ?.hexCode ||
                                                        "#94a3b8",
                                                }}
                                            />
                                            <span className="text-sm text-default-600">
                                                {garment.color?.name}
                                            </span>
                                        </div>
                                    </div>
                                    <Chip
                                        color={getAvailabilityColor(
                                            garment.availabilityRate,
                                        )}
                                        size="sm"
                                        variant="flat"
                                    >
                                        {Math.round(garment.availabilityRate)}%
                                        disponible
                                    </Chip>
                                </div>

                                {/* Sizes Grid */}
                                <div className="bg-default-50 dark:bg-default-100 rounded-lg p-4">
                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3">
                                        {garment.sizesData.map(
                                            (sizeData, sizeIndex) => (
                                                <motion.div
                                                    key={
                                                        sizeData.id || sizeIndex
                                                    }
                                                    animate={{
                                                        scale: 1,
                                                        opacity: 1,
                                                    }}
                                                    className="relative"
                                                    initial={{
                                                        scale: 0.9,
                                                        opacity: 0,
                                                    }}
                                                    transition={{
                                                        delay: sizeIndex * 0.05,
                                                    }}
                                                    whileHover={{ scale: 1.05 }}
                                                >
                                                    <Tooltip
                                                        content={
                                                            <div className="text-sm space-y-1">
                                                                <p className="font-medium">
                                                                    Talla{" "}
                                                                    {
                                                                        sizeData
                                                                            .size
                                                                            ?.code
                                                                    }
                                                                </p>
                                                                <p>
                                                                    Total:{" "}
                                                                    {
                                                                        sizeData.totalQuantity
                                                                    }
                                                                </p>
                                                                <p>
                                                                    Asignadas:{" "}
                                                                    {sizeData.usedQuantity ||
                                                                        0}
                                                                </p>
                                                                <p>
                                                                    Disponibles:{" "}
                                                                    {sizeData.totalQuantity -
                                                                        (sizeData.usedQuantity ||
                                                                            0)}
                                                                </p>
                                                            </div>
                                                        }
                                                    >
                                                        <div
                                                            className={`p-3 rounded-lg border-2 hover:border-primary transition-all cursor-default ${getIntensityClass(sizeData.totalQuantity, maxQuantity)}`}
                                                        >
                                                            {/* Popular size indicator */}
                                                            {sizeData.size
                                                                ?.code ===
                                                                garment.popularSizeCode && (
                                                                <div className="absolute -top-1 -right-1">
                                                                    <Badge
                                                                        classNames={{
                                                                            badge: "border-0 text-xs px-1",
                                                                        }}
                                                                        color="warning"
                                                                        placement="top-right"
                                                                        size="sm"
                                                                    >
                                                                        👑
                                                                    </Badge>
                                                                </div>
                                                            )}

                                                            <div className="text-center space-y-1">
                                                                <p className="text-xs font-medium text-default-600">
                                                                    {
                                                                        sizeData
                                                                            .size
                                                                            ?.code
                                                                    }
                                                                </p>
                                                                <p className="text-xl font-bold text-foreground">
                                                                    {
                                                                        sizeData.totalQuantity
                                                                    }
                                                                </p>

                                                                {/* Mini progress bar */}
                                                                <Progress
                                                                    classNames={{
                                                                        base: "max-w-full",
                                                                        track: "h-1",
                                                                        indicator:
                                                                            "h-1",
                                                                    }}
                                                                    color={getAvailabilityColor(
                                                                        sizeData.availabilityPercent,
                                                                    )}
                                                                    size="sm"
                                                                    value={
                                                                        100 -
                                                                        sizeData.availabilityPercent
                                                                    }
                                                                />

                                                                <p className="text-xs text-default-500">
                                                                    {Math.round(
                                                                        sizeData.availabilityPercent,
                                                                    )}
                                                                    %
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </Tooltip>
                                                </motion.div>
                                            ),
                                        )}

                                        {/* Total card */}
                                        <div className="p-3 rounded-lg bg-primary/10 border-2 border-primary/20">
                                            <div className="text-center space-y-1">
                                                <p className="text-xs font-medium text-primary">
                                                    Total
                                                </p>
                                                <p className="text-xl font-bold text-primary">
                                                    {garment.totalQuantity}
                                                </p>
                                                <div className="flex items-center justify-center gap-1">
                                                    <div className="w-2 h-2 rounded-full bg-success" />
                                                    <p className="text-xs text-default-600">
                                                        {garment.totalQuantity -
                                                            garment.totalUsed}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Distribution visualization */}
                                    <div className="mt-3 pt-3 border-t border-default-200">
                                        <div className="flex items-center gap-2">
                                            <span className="text-xs text-default-500">
                                                Distribución:
                                            </span>
                                            <div className="flex gap-1 flex-1">
                                                {garment.sizesData.map(
                                                    (size, idx) => (
                                                        <Tooltip
                                                            key={idx}
                                                            content={`${size.size?.code}: ${size.totalQuantity} unidades`}
                                                        >
                                                            <div
                                                                className="h-3 bg-primary rounded transition-all hover:opacity-100"
                                                                style={{
                                                                    width: `${(size.totalQuantity / garment.totalQuantity) * 100}%`,
                                                                    opacity:
                                                                        0.3 +
                                                                        (size.totalQuantity /
                                                                            maxQuantity) *
                                                                            0.7,
                                                                }}
                                                            />
                                                        </Tooltip>
                                                    ),
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </motion.div>
                        );
                    })}
                </div>

                {/* Summary Stats */}
                <motion.div
                    animate={{ opacity: 1 }}
                    className="mt-6 p-4 bg-default-50 dark:bg-default-100 rounded-lg"
                    initial={{ opacity: 0 }}
                    transition={{ delay: 0.5 }}
                >
                    <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p className="text-2xl font-bold text-primary">
                                {garmentMetrics.reduce(
                                    (sum, g) => sum + g.totalQuantity,
                                    0,
                                )}
                            </p>
                            <p className="text-xs text-default-500">
                                Unidades Totales
                            </p>
                        </div>
                        <div>
                            <p className="text-2xl font-bold text-success">
                                {garmentMetrics.reduce(
                                    (sum, g) =>
                                        sum + (g.totalQuantity - g.totalUsed),
                                    0,
                                )}
                            </p>
                            <p className="text-xs text-default-500">
                                Disponibles
                            </p>
                        </div>
                        <div>
                            <p className="text-2xl font-bold text-warning">
                                {garmentMetrics.length > 0
                                    ? Math.round(
                                          garmentMetrics.reduce(
                                              (sum, g) =>
                                                  sum + g.availabilityRate,
                                              0,
                                          ) / garmentMetrics.length,
                                      )
                                    : 0}
                                %
                            </p>
                            <p className="text-xs text-default-500">
                                Disponibilidad Promedio
                            </p>
                        </div>
                    </div>
                </motion.div>
            </CardBody>
        </Card>
    );
}
