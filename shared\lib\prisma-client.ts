// Este archivo es mantenido por compatibilidad
// Redirige todo al nuevo cliente unificado

export { prisma as default } from "./prisma";
export { prisma } from "./prisma";
export { executeWithRetry as withRetry } from "./prisma";
export type { PrismaClient } from "@prisma/client";

// Log de deprecación en desarrollo
if (process.env.NODE_ENV === "development") {
    // REMOVED: console.warn("[Deprecation] prisma-client.ts está deprecado. Use './prisma' directamente.");
}
