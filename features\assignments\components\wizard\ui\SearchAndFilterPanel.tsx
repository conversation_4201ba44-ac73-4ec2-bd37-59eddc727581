"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Input, <PERSON><PERSON>, Chip, Badge } from "@heroui/react";
import {
    MagnifyingGlassIcon,
    XMarkIcon,
    SparklesIcon,
    FunnelIcon,
} from "@heroicons/react/24/outline";

interface SearchAndFilterPanelProps {
    onSearch: (query: string) => void;
    onReset: () => void;
    loading?: boolean;
    totalResults?: number;
    filterActive?: boolean;
}

export const SearchAndFilterPanel = ({
    onSearch,
    onReset,
    loading = false,
    totalResults = 0,
    filterActive = false,
}: SearchAndFilterPanelProps) => {
    const [query, setQuery] = useState("");
    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);

    // Esperar antes de realizar la búsqueda para evitar demasiadas solicitudes
    useEffect(() => {
        const handler = setTimeout(() => {
            onSearch(query);
        }, 300);

        return () => {
            clearTimeout(handler);
        };
    }, [query, onSearch]);

    // Reiniciar búsqueda
    const handleReset = () => {
        setQuery("");
        onReset();
        inputRef.current?.focus();
    };

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="rounded-2xl border border-white/20 dark:border-gray-700/50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl p-4 shadow-xl"
            initial={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, type: "spring", stiffness: 300 }}
        >
            <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-grow">
                    <motion.div
                        animate={{
                            scale: isFocused ? 1.02 : 1,
                            boxShadow: isFocused
                                ? "0 10px 30px -10px rgba(0, 0, 0, 0.2)"
                                : "0 4px 12px -4px rgba(0, 0, 0, 0.1)",
                        }}
                        className="w-full relative"
                        initial={false}
                        transition={{
                            type: "spring",
                            stiffness: 400,
                            damping: 30,
                        }}
                    >
                        {/* Glow effect when focused */}
                        <AnimatePresence>
                            {isFocused && (
                                <motion.div
                                    animate={{ opacity: 0.4 }}
                                    className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur-md opacity-40"
                                    exit={{ opacity: 0 }}
                                    initial={{ opacity: 0 }}
                                />
                            )}
                        </AnimatePresence>
                        <Input
                            ref={inputRef}
                            aria-label="Buscar órdenes"
                            classNames={{
                                base: "max-w-full relative z-10",
                                mainWrapper: "h-12",
                                input: "text-sm font-medium",
                                inputWrapper:
                                    "bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-white/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200",
                            }}
                            endContent={
                                <AnimatePresence mode="wait">
                                    {query ? (
                                        <motion.div
                                            key="clear"
                                            animate={{ opacity: 1, scale: 1 }}
                                            exit={{ opacity: 0, scale: 0.8 }}
                                            initial={{ opacity: 0, scale: 0.8 }}
                                            transition={{ duration: 0.2 }}
                                        >
                                            <Button
                                                isIconOnly
                                                aria-label="Limpiar búsqueda"
                                                className="mr-0.5 bg-red-500/10 hover:bg-red-500/20 text-red-500 dark:text-red-400"
                                                size="sm"
                                                variant="flat"
                                                onPress={handleReset}
                                            >
                                                <XMarkIcon className="w-4 h-4" />
                                            </Button>
                                        </motion.div>
                                    ) : (
                                        <motion.div
                                            key="sparkles"
                                            animate={{ opacity: 1 }}
                                            initial={{ opacity: 0 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            <SparklesIcon className="w-5 h-5 text-purple-400 dark:text-purple-300" />
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            }
                            placeholder="Buscar por orden de corte, remisión, cliente..."
                            startContent={
                                <motion.div
                                    animate={{ rotate: loading ? 360 : 0 }}
                                    transition={{
                                        duration: 1,
                                        repeat: loading ? Infinity : 0,
                                        ease: "linear",
                                    }}
                                >
                                    <MagnifyingGlassIcon className="w-5 h-5 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                                </motion.div>
                            }
                            type="text"
                            value={query}
                            onBlur={() => setIsFocused(false)}
                            onChange={(e) => setQuery(e.target.value)}
                            onFocus={() => setIsFocused(true)}
                        />
                    </motion.div>
                </div>

                <div className="flex items-center gap-2">
                    <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <Button
                            className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-400/20 dark:to-purple-400/20 border-blue-500/30 dark:border-blue-400/30 hover:from-blue-500/20 hover:to-purple-500/20 dark:hover:from-blue-400/30 dark:hover:to-purple-400/30 transition-all duration-200"
                            isDisabled={loading}
                            size="md"
                            startContent={<FunnelIcon className="w-4 h-4" />}
                            variant="flat"
                        >
                            Filtros
                            {filterActive && (
                                <Badge
                                    className="ml-1 bg-purple-500 text-white"
                                    size="sm"
                                >
                                    !
                                </Badge>
                            )}
                        </Button>
                    </motion.div>

                    <AnimatePresence>
                        {filterActive && (
                            <motion.div
                                animate={{ opacity: 1, scale: 1, x: 0 }}
                                exit={{ opacity: 0, scale: 0.8, x: -10 }}
                                initial={{ opacity: 0, scale: 0.8, x: -10 }}
                                transition={{ type: "spring", stiffness: 400 }}
                            >
                                <Button
                                    className="whitespace-nowrap bg-red-500/10 hover:bg-red-500/20 text-red-500 dark:text-red-400 border-red-500/30 dark:border-red-400/30"
                                    size="sm"
                                    variant="flat"
                                    onPress={handleReset}
                                >
                                    Limpiar filtros
                                </Button>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </div>

            {/* Resultados y filtros activos con animaciones */}
            <AnimatePresence mode="wait">
                <motion.div
                    key={`${loading}-${totalResults}`}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-between mt-3 px-2"
                    exit={{ opacity: 0, y: -10 }}
                    initial={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                >
                    <div className="flex items-center gap-2">
                        {loading ? (
                            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                                <motion.div
                                    animate={{ rotate: 360 }}
                                    className="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 border-t-blue-500 dark:border-t-blue-400 rounded-full"
                                    transition={{
                                        duration: 1,
                                        repeat: Infinity,
                                        ease: "linear",
                                    }}
                                />
                                <span className="font-medium">Buscando</span>
                                <motion.span
                                    animate={{ opacity: [0, 1, 0] }}
                                    transition={{
                                        duration: 1.5,
                                        repeat: Infinity,
                                    }}
                                >
                                    ...
                                </motion.span>
                            </div>
                        ) : (
                            <motion.div
                                animate={{ scale: 1 }}
                                className="flex items-center gap-2"
                                initial={{ scale: 0.9 }}
                            >
                                <Badge
                                    className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-500/30 dark:border-blue-400/30"
                                    variant="flat"
                                >
                                    <span className="font-bold text-blue-600 dark:text-blue-400">
                                        {totalResults}
                                    </span>
                                </Badge>
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    {totalResults === 1
                                        ? "resultado"
                                        : "resultados"}
                                </span>
                            </motion.div>
                        )}
                    </div>

                    {filterActive && query && (
                        <motion.div
                            animate={{ opacity: 1, x: 0 }}
                            className="flex gap-2 items-center"
                            initial={{ opacity: 0, x: 20 }}
                            transition={{ delay: 0.1 }}
                        >
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                Filtros activos:
                            </span>
                            <Chip
                                className="text-xs bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-400/20 dark:to-purple-400/20 border-blue-500/30 dark:border-blue-400/30"
                                size="sm"
                                startContent={
                                    <MagnifyingGlassIcon className="w-3 h-3" />
                                }
                                variant="flat"
                                onClose={handleReset}
                            >
                                {query}
                            </Chip>
                        </motion.div>
                    )}
                </motion.div>
            </AnimatePresence>
        </motion.div>
    );
};

// Export styles for shimmer effect if needed
export const searchPanelStyles = `
    @keyframes glow {
        0%, 100% { opacity: 0.4; }
        50% { opacity: 0.8; }
    }
`;
