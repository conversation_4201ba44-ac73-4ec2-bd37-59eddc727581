"use client";

import { useState, useCallback, useEffect } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";

import { useOrderStatuses } from "./useOrder";

/**
 * Hook personalizado para manejar el filtrado por estado de órdenes
 * @param defaultStatusId El ID del estado por defecto (opcional)
 * @param syncWithUrl Si debe sincronizar el estado con la URL (true por defecto)
 * @param additionalStatuses Estados adicionales para incluir al principio (como "Todos")
 * @returns Un objeto con los estados, el estado seleccionado, función para cambiar estado, etc.
 */
export default function useOrderStatusFilter({
    defaultStatusId = "all",
    syncWithUrl = true,
    additionalStatuses = [
        { id: "all", name: "Todos los estados", iconName: null, color: null },
    ],
}: {
    defaultStatusId?: string;
    syncWithUrl?: boolean;
    additionalStatuses?: {
        id: string;
        name: string;
        iconName: string | null;
        color: string | null;
    }[];
} = {}) {
    // Obtener los estados de órdenes
    const { orderStatuses, isLoading, error } = useOrderStatuses();

    // Estado local para el estado seleccionado
    const [selectedStatusId, setSelectedStatusId] =
        useState<string>(defaultStatusId);

    // Navegación y parámetros de URL
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Cargar el estado desde la URL al inicio si syncWithUrl está habilitado
    useEffect(() => {
        if (syncWithUrl && searchParams) {
            const statusFromUrl = searchParams.get("status");

            if (statusFromUrl) {
                setSelectedStatusId(statusFromUrl);
            }
        }
    }, [syncWithUrl, searchParams]);

    // Función para actualizar el estado seleccionado
    const handleStatusChange = useCallback(
        (statusId: string) => {
            setSelectedStatusId(statusId);

            // Actualizar la URL si syncWithUrl está habilitado
            if (syncWithUrl) {
                const newParams = new URLSearchParams(searchParams.toString());

                if (statusId === "all") {
                    newParams.delete("status");
                } else {
                    newParams.set("status", statusId);
                }

                // Actualizar la URL
                router.push(`${pathname}?${newParams.toString()}`);
            }
        },
        [syncWithUrl, router, pathname, searchParams],
    );

    // Combinar los estados adicionales con los estados de la base de datos
    const allStatuses = [...additionalStatuses, ...(orderStatuses || [])];

    // Encontrar el estado seleccionado
    const selectedStatus = allStatuses.find(
        (status) => status.id === selectedStatusId,
    );

    return {
        statuses: allStatuses,
        rawStatuses: orderStatuses, // Estados sin los adicionales
        selectedStatusId,
        selectedStatus,
        handleStatusChange,
        isLoading,
        error,
    };
}
