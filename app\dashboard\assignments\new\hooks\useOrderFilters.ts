"use client";

import { useState, useMemo, useCallback } from "react";

export interface OrderFilters {
    searchQuery: string;
    status: string[];
    clientId: string | null;
    dateRange: {
        from: Date | null;
        to: Date | null;
    };
    urgency: "all" | "urgent" | "normal";
    modelIds: string[];
    minQuantity: number | null;
    hasInventory: boolean;
}

const defaultFilters: OrderFilters = {
    searchQuery: "",
    status: [],
    clientId: null,
    dateRange: { from: null, to: null },
    urgency: "all",
    modelIds: [],
    minQuantity: null,
    hasInventory: false,
};

interface Order {
    id: string;
    code?: string | null;
    cutOrder?: string | null;
    transferNumber?: string | null;
    batch?: string | null;
    status:
        | string
        | {
              id: string;
              name: string;
              iconName?: string | null;
              color?: string | null;
          };
    customer?: {
        id: string;
        name: string;
    } | null;
    estimatedDeliveryDate?: string | Date | null;
    deliveryDate?: string | Date | null;
    receivedDate?: string | Date | null;
    _count?: { garments: number };
    garments?: Array<{
        id?: string;
        model: {
            id: string;
            code?: string | null;
        };
        color: {
            id?: string;
            name?: string | null;
            hexCode?: string | null;
        };
        sizes: Array<{
            id?: string;
            size?: {
                id?: string;
                code: string;
            };
            totalQuantity: number;
            usedQuantity: number;
        }>;
    }>;
    parts?: Array<{
        id: string;
        code?: string;
        [key: string]: any;
    }>;
}

export function useOrderFilters(
    orders: Order[],
    initialFilters?: Partial<OrderFilters>,
) {
    const [filters, setFilters] = useState<OrderFilters>({
        ...defaultFilters,
        ...initialFilters,
    });

    // Función para actualizar un filtro
    const updateFilter = useCallback(
        <K extends keyof OrderFilters>(key: K, value: OrderFilters[K]) => {
            setFilters((prev) => ({ ...prev, [key]: value }));
        },
        [],
    );

    // Función para limpiar todos los filtros
    const clearFilters = useCallback(() => {
        setFilters(defaultFilters);
    }, []);

    // Aplicar filtros a las órdenes
    const filteredOrders = useMemo(() => {
        let filtered = [...orders];

        // Filtro por estado
        if (filters.status.length > 0) {
            filtered = filtered.filter((order) => {
                const statusValue =
                    typeof order.status === "string"
                        ? order.status
                        : order.status.name;

                return filters.status.includes(statusValue);
            });
        }

        // Filtro por cliente
        if (filters.clientId) {
            filtered = filtered.filter(
                (order) => order.customer?.id === filters.clientId,
            );
        }

        // Filtro por rango de fechas
        if (filters.dateRange.from || filters.dateRange.to) {
            filtered = filtered.filter((order) => {
                const dateValue =
                    order.estimatedDeliveryDate || order.deliveryDate;

                if (!dateValue) return false;
                const deliveryDate = new Date(dateValue);

                if (
                    filters.dateRange.from &&
                    deliveryDate < filters.dateRange.from
                ) {
                    return false;
                }

                if (
                    filters.dateRange.to &&
                    deliveryDate > filters.dateRange.to
                ) {
                    return false;
                }

                return true;
            });
        }

        // Filtro por urgencia
        if (filters.urgency !== "all") {
            const today = new Date();

            today.setHours(0, 0, 0, 0);

            filtered = filtered.filter((order) => {
                const dateValue =
                    order.estimatedDeliveryDate || order.deliveryDate;

                if (!dateValue) return false;
                const deliveryDate = new Date(dateValue);
                const diffDays = Math.ceil(
                    (deliveryDate.getTime() - today.getTime()) /
                        (1000 * 60 * 60 * 24),
                );

                if (filters.urgency === "urgent") {
                    return diffDays <= 3;
                } else {
                    return diffDays > 3;
                }
            });
        }

        // Filtro por modelos
        if (filters.modelIds.length > 0) {
            filtered = filtered.filter((order) =>
                order.garments?.some((g) =>
                    filters.modelIds.includes(g.model.id),
                ),
            );
        }

        // Filtro por cantidad mínima
        if (filters.minQuantity) {
            filtered = filtered.filter((order) => {
                const totalQuantity =
                    order.garments?.reduce(
                        (sum, g) =>
                            sum +
                            g.sizes.reduce(
                                (sizeSum, s) => sizeSum + s.totalQuantity,
                                0,
                            ),
                        0,
                    ) || 0;

                return totalQuantity >= filters.minQuantity!;
            });
        }

        // Filtro por inventario disponible
        if (filters.hasInventory) {
            filtered = filtered.filter((order) =>
                order.garments?.some((g) =>
                    g.sizes.some((s) => s.totalQuantity > s.usedQuantity),
                ),
            );
        }

        // Búsqueda unificada
        if (filters.searchQuery) {
            const searchTerm = filters.searchQuery.toLowerCase();

            filtered = filtered.filter((order) => {
                // Buscar en código de orden
                if (order.code?.toLowerCase().includes(searchTerm)) return true;

                // Buscar en orden de corte
                if (order.cutOrder?.toLowerCase().includes(searchTerm))
                    return true;

                // Buscar en nombre del cliente
                if (order.customer?.name?.toLowerCase().includes(searchTerm))
                    return true;

                // Buscar en partidas
                if (
                    order.parts?.some((part) =>
                        part.code?.toLowerCase().includes(searchTerm),
                    )
                )
                    return true;

                // Buscar en modelos
                if (
                    order.garments?.some((g) =>
                        g.model?.code?.toLowerCase().includes(searchTerm),
                    )
                )
                    return true;

                // Buscar en colores (para mayor cobertura)
                if (
                    order.garments?.some((g) =>
                        g.color?.name?.toLowerCase().includes(searchTerm),
                    )
                )
                    return true;

                return false;
            });
        }

        return filtered;
    }, [orders, filters]);

    // Contar filtros activos
    const activeFiltersCount = useMemo(() => {
        let count = 0;

        if (filters.searchQuery) count++;
        if (filters.status.length > 0) count++;
        if (filters.clientId) count++;
        if (filters.dateRange.from || filters.dateRange.to) count++;
        if (filters.urgency !== "all") count++;
        if (filters.modelIds.length > 0) count++;
        if (filters.minQuantity) count++;
        if (filters.hasInventory) count++;

        return count;
    }, [filters]);

    // Guardar preset de filtros
    const savePreset = useCallback(
        (name: string) => {
            const presets = JSON.parse(
                localStorage.getItem("order-filter-presets") || "{}",
            );

            presets[name] = filters;
            localStorage.setItem(
                "order-filter-presets",
                JSON.stringify(presets),
            );
        },
        [filters],
    );

    // Cargar preset de filtros
    const loadPreset = useCallback((name: string) => {
        const presets = JSON.parse(
            localStorage.getItem("order-filter-presets") || "{}",
        );

        if (presets[name]) {
            setFilters(presets[name]);
        }
    }, []);

    // Obtener lista de presets
    const getPresets = useCallback(() => {
        const presets = JSON.parse(
            localStorage.getItem("order-filter-presets") || "{}",
        );

        return Object.keys(presets);
    }, []);

    return {
        filters,
        updateFilter,
        clearFilters,
        filteredOrders,
        activeFiltersCount,
        savePreset,
        loadPreset,
        getPresets,
        applyFilters: () => {}, // Para compatibilidad
    };
}
