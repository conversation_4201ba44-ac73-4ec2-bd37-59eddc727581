"use client";

import type { SortOption } from "@/shared/components/dashboard";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    ChartBarIcon,
    FireIcon,
    ArrowTrendingUpIcon,
    ScaleIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import { useSizes, useDeleteSize } from "@/features/sizes/hooks";
import { Chip, Progress } from "@/shared/components/ui/hero-ui-client";

interface Size {
    id: string;
    code: string;
    createdAt: string;
    updatedAt: string;
    _count?: {
        garments?: number;
    };
}

export default function UnifiedSizesPage() {
    const router = useRouter();
    const { sizes = [], isLoading, mutate } = useSizes();
    const { deleteSize } = useDeleteSize();

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "code-asc",
        label: "Código A-Z",
        field: "code",
        direction: "asc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;

    // Calcular distribución de tallas
    const sizeDistribution = useMemo(() => {
        const total = sizes.reduce(
            (sum: number, size: any) => sum + (size._count?.garments || 0),
            0,
        );

        return sizes
            .map((size: any) => ({
                code: size.code,
                count: size._count?.garments || 0,
                percentage:
                    total > 0
                        ? ((size._count?.garments || 0) / total) * 100
                        : 0,
            }))
            .sort((a: any, b: any) => b.count - a.count);
    }, [sizes]);

    // Calcular estadísticas
    const stats = useMemo(() => {
        const usedSizes = sizes.filter(
            (s: any) => s._count?.garments && s._count.garments > 0,
        );
        const mostUsed = sizeDistribution[0];
        const highDemand = sizeDistribution.find((s: any) => s.percentage > 30);

        // Calcular rotación promedio (simulado)
        const avgRotation = usedSizes.length > 0 ? 2.5 : 0;

        return [
            {
                title: "Total Tallas",
                value: sizes.length,
                icon: <ScaleIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "En sistema",
            },
            {
                title: "Más Vendida",
                value: mostUsed?.code || "-",
                description: mostUsed
                    ? `${mostUsed.percentage.toFixed(0)}% del total`
                    : "",
                icon: <FireIcon className="w-6 h-6" />,
                color: "success" as const,
            },
            {
                title: "Mayor Demanda",
                value: highDemand?.code || mostUsed?.code || "-",
                icon: <ArrowTrendingUpIcon className="w-6 h-6" />,
                color: "warning" as const,
                change: 15,
                changeLabel: "vs. mes anterior",
            },
            {
                title: "Rotación",
                value: `${avgRotation.toFixed(1)}x`,
                description: "promedio mensual",
                icon: <ChartBarIcon className="w-6 h-6" />,
                color: "default" as const,
            },
        ];
    }, [sizes, sizeDistribution]);

    // Columnas de la tabla con gráfico de distribución
    const columns = [
        {
            key: "code",
            label: "Código",
            sortable: true,
            render: (size: Size) => (
                <span className="font-bold text-lg">{size.code}</span>
            ),
        },
        {
            key: "distribution",
            label: "Distribución",
            render: (size: Size) => {
                const dist = sizeDistribution.find(
                    (d: any) => d.code === size.code,
                );
                const percentage = dist?.percentage || 0;
                const count = dist?.count || 0;

                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-3">
                            <Progress
                                className="w-32"
                                color={
                                    percentage > 30
                                        ? "success"
                                        : percentage > 15
                                          ? "warning"
                                          : "default"
                                }
                                size="sm"
                                value={percentage}
                            />
                            <span className="text-sm font-medium">
                                {percentage.toFixed(1)}%
                            </span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            {count} {count === 1 ? "prenda" : "prendas"}
                        </span>
                    </div>
                );
            },
        },
        {
            key: "usage",
            label: "Uso",
            render: (size: Size) => {
                const count = size._count?.garments || 0;
                const isUsed = count > 0;

                return (
                    <Chip
                        color={isUsed ? "success" : "default"}
                        size="sm"
                        variant="flat"
                    >
                        {isUsed ? "En uso" : "Sin usar"}
                    </Chip>
                );
            },
        },
        {
            key: "demand",
            label: "Demanda",
            render: (size: Size) => {
                const dist = sizeDistribution.find(
                    (d: any) => d.code === size.code,
                );
                const percentage = dist?.percentage || 0;

                let demandLevel = "Baja";
                let color: "default" | "warning" | "danger" = "default";

                if (percentage > 30) {
                    demandLevel = "Muy Alta";
                    color = "danger";
                } else if (percentage > 20) {
                    demandLevel = "Alta";
                    color = "warning";
                } else if (percentage > 10) {
                    demandLevel = "Media";
                    color = "warning";
                }

                return (
                    <Chip color={color} size="sm" variant="dot">
                        {demandLevel}
                    </Chip>
                );
            },
        },
        {
            key: "dates",
            label: "Fechas",
            render: (size: Size) => (
                <div className="text-sm space-y-1">
                    <div className="text-gray-600 dark:text-gray-400">
                        Creado:{" "}
                        {format(new Date(size.createdAt), "dd/MM/yyyy", {
                            locale: es,
                        })}
                    </div>
                    {size.updatedAt !== size.createdAt && (
                        <div className="text-gray-500 dark:text-gray-500 text-xs">
                            Actualizado:{" "}
                            {format(new Date(size.updatedAt), "dd/MM/yyyy", {
                                locale: es,
                            })}
                        </div>
                    )}
                </div>
            ),
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (size: Size) => {
                router.push(`/dashboard/sizes/${size.id}/details`);
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (size: Size) => {
                router.push(`/dashboard/sizes/${size.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (size: Size) => {
                if (size._count?.garments && size._count.garments > 0) {
                    addToast({
                        title: "No se puede eliminar",
                        description: "Esta talla está siendo usada en prendas",
                        color: "warning",
                    });

                    return;
                }

                if (
                    confirm(`¿Estás seguro de eliminar la talla ${size.code}?`)
                ) {
                    const result = await deleteSize(size.id);

                    if (result.success) {
                        mutate();
                        addToast({
                            title: "Talla eliminada",
                            description:
                                "La talla ha sido eliminada correctamente",
                            color: "success",
                        });
                    }
                }
            },
            color: "danger" as const,
            isDisabled: (size: Size) =>
                !!(size._count?.garments && size._count.garments > 0),
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "used", label: "En uso" },
                { value: "unused", label: "Sin usar" },
            ],
        },
        {
            key: "demand",
            label: "Nivel de Demanda",
            type: "select" as const,
            placeholder: "Todos los niveles",
            options: [
                { value: "all", label: "Todos" },
                { value: "high", label: "Alta (>20%)" },
                { value: "medium", label: "Media (10-20%)" },
                { value: "low", label: "Baja (<10%)" },
            ],
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "code-asc",
            label: "Código A-Z",
            field: "code",
            direction: "asc" as const,
        },
        {
            key: "code-desc",
            label: "Código Z-A",
            field: "code",
            direction: "desc" as const,
        },
        {
            key: "usage-desc",
            label: "Más usado",
            field: "_count.garments",
            direction: "desc" as const,
        },
        {
            key: "usage-asc",
            label: "Menos usado",
            field: "_count.garments",
            direction: "asc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
    ];

    // Filtrar y ordenar datos
    const filteredData = useMemo(() => {
        let filtered = [...sizes];

        // Búsqueda
        if (searchValue) {
            filtered = filtered.filter((size) =>
                size.code.toLowerCase().includes(searchValue.toLowerCase()),
            );
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            if (filterValues.status === "used") {
                filtered = filtered.filter(
                    (s: any) => s._count?.garments && s._count.garments > 0,
                );
            } else if (filterValues.status === "unused") {
                filtered = filtered.filter(
                    (s: any) => !s._count?.garments || s._count.garments === 0,
                );
            }
        }

        // Filtro por demanda
        if (filterValues.demand && filterValues.demand !== "all") {
            const dist = sizeDistribution;

            if (filterValues.demand === "high") {
                filtered = filtered.filter((s: any) => {
                    const d = dist.find((d: any) => d.code === s.code);

                    return d && d.percentage > 20;
                });
            } else if (filterValues.demand === "medium") {
                filtered = filtered.filter((s: any) => {
                    const d = dist.find((d: any) => d.code === s.code);

                    return d && d.percentage >= 10 && d.percentage <= 20;
                });
            } else if (filterValues.demand === "low") {
                filtered = filtered.filter((s: any) => {
                    const d = dist.find((d: any) => d.code === s.code);

                    return !d || d.percentage < 10;
                });
            }
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [sizes, searchValue, filterValues, sizeDistribution, currentSort]);

    // Componente adicional: Gráfico de distribución
    const DistributionChart = () => (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
            initial={{ opacity: 0, y: 20 }}
        >
            <h3 className="text-lg font-semibold mb-4">
                Distribución de Tallas
            </h3>
            <div className="space-y-3">
                {sizeDistribution
                    .slice(0, 5)
                    .map((item: any, index: number) => (
                        <div
                            key={item.code}
                            className="flex items-center gap-3"
                        >
                            <span className="w-12 font-medium">
                                {item.code}
                            </span>
                            <div className="flex-1">
                                <Progress
                                    color={
                                        index === 0
                                            ? "success"
                                            : index < 3
                                              ? "warning"
                                              : "default"
                                    }
                                    size="md"
                                    value={item.percentage}
                                />
                            </div>
                            <span className="text-sm w-16 text-right">
                                {item.percentage.toFixed(1)}%
                            </span>
                        </div>
                    ))}
            </div>
        </motion.div>
    );

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={columns}
                currentSort={currentSort}
                filterValues={filterValues}
                isLoading={isLoading}
                page={page}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Control de tallas y medidas"
                title="Tallas"
                totalPages={Math.ceil(filteredData.length / rowsPerPage)}
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onPageChange={setPage}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={actions}
                // Create
                createRoute="/dashboard/sizes/new"
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key] && filterValues[key] !== "all",
                    ).length
                }
                // Pagination
                breadcrumbs={[{ label: "Tallas" }]}
                // Stats
                stats={stats}
                createLabel="Nueva Talla"
                // Filters
                filters={filters}
                data={filteredData}
                // Table
                emptyContent="No hay tallas registradas"
            />

            {/* Gráfico de distribución adicional */}
            {sizes.length > 0 && <DistributionChart />}
        </>
    );
}
