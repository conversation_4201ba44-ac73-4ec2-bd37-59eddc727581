"use client";

import React from "react";
import {
    <PERSON>,
    Card<PERSON>eader,
    CardBody,
    CardFooter,
    Checkbox,
    Avatar,
    Chip,
    Button,
    Divider,
    Tooltip,
    type PressEvent,
    Badge,
} from "@heroui/react";
import {
    UserIcon,
    ClockIcon,
    PencilIcon,
    TrashIcon,
    EyeIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";

import { useNotesSelection } from "@/shared/context/notes-selection-context";

// Using any types for quick build fix
type NoteData = any;
type NoteOption = any;

export interface SelectableNoteCardProps {
    note: NoteData;
    orderId: string;
    importanceOptions?: NoteOption[];
    statusOptions?: NoteOption[];
    onDelete?: () => void;
    onUpdate?: () => void;
    onNoteClick?: (note: NoteData) => void;
    canEdit?: boolean;
    isEditable?: boolean;
    isDeletable?: boolean;
    selectable?: boolean;
    currentUserId?: string;
    userRole?: string;
}

export function SelectableNoteCard({
    note,
    orderId,
    importanceOptions,
    statusOptions,
    onDelete,
    onUpdate,
    onNoteClick,
    canEdit = false,
    isEditable,
    isDeletable,
    selectable = true,
    currentUserId,
    userRole,
    ...props
}: SelectableNoteCardProps) {
    const { isSelectionMode, toggleSelection, isSelected } =
        useNotesSelection();

    // Manejar selección de nota
    const handleSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation();
        if (isSelectionMode && selectable) {
            toggleSelection(note.id);
        }
    };

    // En modo selección, todos los clics deberían seleccionar
    // Si no estamos en modo selección, usar onNoteClick si está definido
    const handleCardClick = () => {
        if (isSelectionMode && selectable) {
            toggleSelection(note.id);
        } else if (onNoteClick && !isSelectionMode) {
            onNoteClick(note);
        }
    };

    // Evitar edición en modo selección
    const handleEdit = (e: PressEvent) => {
        if (!isSelectionMode && onUpdate) {
            onUpdate();
        }
    };

    // Evitar eliminación en modo selección
    const handleDelete = (e: PressEvent) => {
        if (!isSelectionMode && onDelete) {
            onDelete();
        }
    };

    const selected = isSelected(note.id);

    // Formatear fecha para mostrar fecha y hora separadas
    const noteDate = new Date(note.createdAt);
    const formattedDate = noteDate.toLocaleDateString("es-ES", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
    });

    const formattedTime = noteDate.toLocaleTimeString("es-ES", {
        hour: "2-digit",
        minute: "2-digit",
    });

    // Determinar si la tarjeta es interactiva
    const isInteractive = !isSelectionMode && !!onNoteClick;

    // Verificar permisos
    const canEditNote = isEditable || note.author?.id === currentUserId;
    const canDeleteNote =
        isDeletable ||
        userRole === "ADMIN" ||
        note.author?.id === currentUserId;

    // Obtener datos de importancia y estado para estilos y visualización
    const importanceData =
        note.importanceId && importanceOptions
            ? importanceOptions.find((opt) => opt.id === note.importanceId)
            : null;

    const statusData =
        note.statusId && statusOptions
            ? statusOptions.find((opt) => opt.id === note.statusId)
            : null;

    return (
        <Card
            className={`w-full mb-4 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden ${
                isInteractive ? "cursor-pointer" : ""
            } ${selected ? "ring-2 ring-primary-500" : ""} ${
                isSelectionMode ? "bg-gray-50/80 dark:bg-gray-800/30" : ""
            }`}
            isPressable={false}
            onClick={handleCardClick}
            {...props}
        >
            {/* Borde superior con color de importancia */}
            {importanceData?.color && (
                <div
                    className="h-3 w-full"
                    style={{ backgroundColor: importanceData.color }}
                />
            )}

            <CardHeader className="flex items-start justify-between gap-3 p-4 pb-3">
                <div className="flex items-center gap-3">
                    {/* Checkbox de selección */}
                    {selectable && isSelectionMode && (
                        <Checkbox
                            className="mr-1"
                            isSelected={selected}
                            size="md"
                            onChange={handleSelect}
                            onClick={(e) => e.stopPropagation()}
                        />
                    )}

                    {/* Avatar del autor con diseño mejorado */}
                    <Avatar
                        className="border-2 border-white shadow-sm"
                        icon={<UserIcon />}
                        name={note.author?.name || "Usuario"}
                        size="md"
                        src={note.author?.image || ""}
                    />

                    <div className="flex flex-col">
                        <span className="text-base font-semibold">
                            {note.author?.name || "Usuario"}
                        </span>

                        {/* Fecha y hora con iconos */}
                        <div className="flex items-center gap-3 mt-1">
                            <span className="text-xs text-gray-500 flex items-center gap-1">
                                <CalendarIcon className="w-3.5 h-3.5" />
                                {formattedDate}
                            </span>
                            <span className="text-xs text-gray-500 flex items-center gap-1">
                                <ClockIcon className="w-3.5 h-3.5" />
                                {formattedTime}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Acciones y estados en el lado derecho */}
                <div className="flex flex-col items-end gap-2">
                    {/* Etiquetas de estado e importancia con diseño mejorado */}
                    <div className="flex flex-wrap gap-1.5 justify-end">
                        {statusData && (
                            <Chip
                                className="px-2 text-xs font-medium"
                                size="sm"
                                startContent={
                                    <span className="flex items-center mr-1">
                                        <Badge
                                            className="mr-1"
                                            color="primary"
                                            size="sm"
                                        >
                                            •
                                        </Badge>
                                    </span>
                                }
                                style={{
                                    backgroundColor:
                                        statusData.color || "#9CA3AF",
                                    color: "white",
                                }}
                                variant="flat"
                            >
                                {statusData.name || "Pendiente"}
                            </Chip>
                        )}

                        {importanceData && (
                            <Chip
                                className="px-2 text-xs font-medium"
                                size="sm"
                                startContent={
                                    <span className="flex items-center mr-1">
                                        <Badge
                                            className="mr-1"
                                            color="primary"
                                            size="sm"
                                        >
                                            •
                                        </Badge>
                                    </span>
                                }
                                style={{
                                    backgroundColor:
                                        importanceData.color || "#9CA3AF",
                                    color: "white",
                                }}
                            >
                                {importanceData.name || "Normal"}
                            </Chip>
                        )}
                    </div>

                    {/* Botones de acción */}
                    {!isSelectionMode && (
                        <div className="flex gap-1.5">
                            {canEditNote && onUpdate && (
                                <Tooltip content="Editar nota">
                                    <Button
                                        isIconOnly
                                        className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                                        size="sm"
                                        variant="flat"
                                        onPress={handleEdit}
                                    >
                                        <PencilIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>
                            )}

                            {canDeleteNote && onDelete && (
                                <Tooltip content="Eliminar nota">
                                    <Button
                                        isIconOnly
                                        className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                                        size="sm"
                                        variant="flat"
                                        onPress={handleDelete}
                                    >
                                        <TrashIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>
                            )}
                        </div>
                    )}
                </div>
            </CardHeader>

            <CardBody className="py-4 px-5">
                {/* Contenido de la nota con tipografía mejorada */}
                <p className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 leading-relaxed text-base">
                    {note.content}
                </p>
            </CardBody>

            <Divider />

            {/* Footer simplificado */}
            <CardFooter className="p-3 flex justify-between bg-gray-50/70 dark:bg-gray-800/20">
                <div className="flex items-center gap-2">
                    <Chip
                        className="bg-gray-100 dark:bg-gray-800"
                        size="sm"
                        variant="flat"
                    >
                        <span className="text-xs">
                            OT: {orderId.substring(0, 8)}
                        </span>
                    </Chip>
                </div>

                <div className="flex items-center">
                    {/* Botón visualizador */}
                    {onNoteClick && !isSelectionMode && (
                        <Tooltip content="Ver detalles de la nota">
                            <Button
                                isIconOnly
                                className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 hover:bg-primary-200 dark:hover:bg-primary-800/40"
                                size="sm"
                                variant="flat"
                                onPress={() => onNoteClick(note)}
                            >
                                <EyeIcon className="w-4 h-4" />
                            </Button>
                        </Tooltip>
                    )}
                </div>
            </CardFooter>
        </Card>
    );
}
