"use client";

import React from "react";
import {
    InformationCircleIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

export interface InfoMessageProps {
    type: "info" | "success" | "warning";
    title: string;
    description?: string;
    className?: string;
}

export function ModelInfoMessage({
    type,
    title,
    description,
    className = "",
}: InfoMessageProps) {
    const getIcon = () => {
        switch (type) {
            case "success":
                return <CheckCircleIcon className="w-5 h-5" />;
            case "warning":
                return <ExclamationTriangleIcon className="w-5 h-5" />;
            default:
                return <InformationCircleIcon className="w-5 h-5" />;
        }
    };

    const getStyles = () => {
        switch (type) {
            case "success":
                return {
                    container:
                        "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800",
                    icon: "text-green-600 dark:text-green-400",
                    text: "text-green-800 dark:text-green-200",
                };
            case "warning":
                return {
                    container:
                        "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800",
                    icon: "text-yellow-600 dark:text-yellow-400",
                    text: "text-yellow-800 dark:text-yellow-200",
                };
            default:
                return {
                    container:
                        "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",
                    icon: "text-blue-600 dark:text-blue-400",
                    text: "text-blue-800 dark:text-blue-200",
                };
        }
    };

    const styles = getStyles();

    return (
        <div
            className={`border rounded-lg p-4 ${styles.container} ${className}`}
        >
            <div className="flex gap-3">
                <div className={`flex-shrink-0 ${styles.icon}`}>
                    {getIcon()}
                </div>
                <div className="flex-1">
                    <p className={`font-medium ${styles.text}`}>{title}</p>
                    {description && (
                        <p className={`mt-1 text-sm ${styles.text} opacity-90`}>
                            {description}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
}

// Mensajes predefinidos para el formulario de modelos
export const modelFormMessages = {
    codeFormat: {
        type: "info" as const,
        title: "Formato del código",
        description:
            "Use 2-10 caracteres. Solo letras, números y guiones (-). Se convertirá automáticamente a mayúsculas.",
    },
    descriptionRequired: {
        type: "info" as const,
        title: "Descripción detallada",
        description:
            "Proporcione una descripción clara del modelo de prenda (mínimo 5 caracteres).",
    },
    basePriceInfo: {
        type: "info" as const,
        title: "Precio base de confección",
        description:
            "Este precio se usará como referencia mínima para la contabilidad. El precio final puede variar según el contratista.",
    },
    duplicateCode: {
        type: "warning" as const,
        title: "Código duplicado",
        description:
            "Ya existe un modelo con este código. Por favor, use un código diferente.",
    },
    formComplete: {
        type: "success" as const,
        title: "¡Formulario completo!",
        description:
            "Todos los campos requeridos están completos. Puede guardar el modelo.",
    },
};
