"use client";

import { useMemo } from "react";
import {
    Card,
    CardBody,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Chip,
    <PERSON>ltip,
    <PERSON><PERSON>,
} from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";
import {
    CubeIcon,
    SwatchIcon,
    ScissorsIcon,
    TrashIcon,
    SparklesIcon,
    ChartBarIcon,
} from "@heroicons/react/24/outline";

export interface Assignment {
    id?: string;
    orderId: string;
    garmentSizeId: string;
    quantity: number;
    defects?: number;
    modelCode: string;
    colorName: string;
    sizeCode: string;
    // Campos adicionales para remisiones
    cutOrder?: string;
    parts?: Array<{
        id: string;
        code: string;
    }>;
}

interface AssignmentTableProps {
    assignments: Assignment[];
    onRemove?: (index: number) => void;
}

export function AssignmentTable({
    assignments,
    onRemove,
}: AssignmentTableProps) {
    // Animación para las filas
    const rowVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: (i: number) => ({
            opacity: 1,
            y: 0,
            transition: {
                delay: i * 0.05,
                duration: 0.3,
                ease: "easeOut",
            },
        }),
        exit: {
            opacity: 0,
            x: -20,
            transition: { duration: 0.2 },
        },
    };
    // Process and group assignments by order
    const orderAssignments = useMemo(() => {
        const groupedByOrder: Record<string, Assignment[]> = {};

        // Group assignments by orderId
        assignments.forEach((assignment) => {
            if (!groupedByOrder[assignment.orderId]) {
                groupedByOrder[assignment.orderId] = [];
            }
            groupedByOrder[assignment.orderId].push(assignment);
        });

        // Convert to array with calculated totals
        return Object.entries(groupedByOrder).map(([orderId, items]) => ({
            orderId,
            assignments: items,
            totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
        }));
    }, [assignments]);

    return (
        <motion.div
            animate={{ opacity: 1 }}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                        <ChartBarIcon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Detalle de asignaciones
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            {assignments.length} productos asignados
                        </p>
                    </div>
                </div>
                {assignments.length > 0 && (
                    <motion.div
                        animate={{ scale: 1 }}
                        className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full"
                        initial={{ scale: 0 }}
                        transition={{ delay: 0.2, type: "spring" }}
                    >
                        <SparklesIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                            Total:{" "}
                            {assignments.reduce(
                                (sum, item) => sum + item.quantity,
                                0,
                            )}{" "}
                            unidades
                        </span>
                    </motion.div>
                )}
            </div>

            <AnimatePresence mode="popLayout">
                {orderAssignments.map((order, orderIndex) => (
                    <motion.div
                        key={order.orderId}
                        animate={{ opacity: 1, scale: 1 }}
                        className="mb-6"
                        exit={{ opacity: 0, scale: 0.95 }}
                        initial={{ opacity: 0, scale: 0.95 }}
                        transition={{ delay: orderIndex * 0.1 }}
                    >
                        <Card className="overflow-hidden backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 border border-gray-200/50 dark:border-gray-700/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 pointer-events-none" />
                            <CardBody className="relative z-10">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-md">
                                            <CubeIcon className="w-5 h-5 text-white" />
                                        </div>
                                        <div>
                                            <p className="font-bold text-lg text-gray-800 dark:text-gray-100">
                                                Orden: {order.orderId}
                                            </p>
                                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                                {order.assignments.length}{" "}
                                                {order.assignments.length === 1
                                                    ? "producto"
                                                    : "productos"}
                                            </p>
                                        </div>
                                    </div>
                                    <Chip
                                        className="bg-gradient-to-r from-green-400 to-emerald-500 text-white font-semibold"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {order.totalQuantity} unidades
                                    </Chip>
                                </div>

                                <div className="overflow-hidden rounded-xl border border-gray-200/50 dark:border-gray-700/50">
                                    <Table
                                        removeWrapper
                                        aria-label={`Tabla de asignaciones para orden ${order.orderId}`}
                                        className="min-w-full"
                                    >
                                        <TableHeader>
                                            <TableColumn className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 font-bold text-gray-700 dark:text-gray-300">
                                                <div className="flex items-center gap-2">
                                                    <CubeIcon className="w-4 h-4" />
                                                    MODELO
                                                </div>
                                            </TableColumn>
                                            <TableColumn className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 font-bold text-gray-700 dark:text-gray-300">
                                                <div className="flex items-center gap-2">
                                                    <SwatchIcon className="w-4 h-4" />
                                                    COLOR
                                                </div>
                                            </TableColumn>
                                            <TableColumn className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 font-bold text-gray-700 dark:text-gray-300">
                                                <div className="flex items-center gap-2">
                                                    <ScissorsIcon className="w-4 h-4" />
                                                    TALLA
                                                </div>
                                            </TableColumn>
                                            <TableColumn className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 font-bold text-gray-700 dark:text-gray-300 text-right">
                                                CANTIDAD
                                            </TableColumn>
                                            <TableColumn className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 font-bold text-gray-700 dark:text-gray-300 text-right">
                                                {onRemove ? "ACCIONES" : ""}
                                            </TableColumn>
                                        </TableHeader>
                                        <TableBody>
                                            <AnimatePresence>
                                                {order.assignments.map(
                                                    (assignment, index) => (
                                                        <TableRow
                                                            key={index}
                                                            className="hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-200"
                                                        >
                                                            <motion.td
                                                                animate="visible"
                                                                className="p-0"
                                                                custom={index}
                                                                exit="exit"
                                                                initial="hidden"
                                                                variants={
                                                                    rowVariants
                                                                }
                                                            >
                                                                <TableCell>
                                                                    <Tooltip
                                                                        content="Modelo del producto"
                                                                        placement="left"
                                                                    >
                                                                        <motion.div
                                                                            whileHover={{
                                                                                scale: 1.05,
                                                                            }}
                                                                            whileTap={{
                                                                                scale: 0.95,
                                                                            }}
                                                                        >
                                                                            <Chip
                                                                                classNames={{
                                                                                    base: "bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 border border-blue-200 dark:border-blue-700",
                                                                                    content:
                                                                                        "font-bold text-blue-700 dark:text-blue-300",
                                                                                }}
                                                                                variant="flat"
                                                                            >
                                                                                {
                                                                                    assignment.modelCode
                                                                                }
                                                                            </Chip>
                                                                        </motion.div>
                                                                    </Tooltip>
                                                                </TableCell>
                                                            </motion.td>
                                                            <TableCell>
                                                                <div className="flex items-center gap-2">
                                                                    <div
                                                                        className="w-6 h-6 rounded-full border-2 border-gray-300 dark:border-gray-600 shadow-sm"
                                                                        style={{
                                                                            backgroundColor:
                                                                                assignment.colorName.toLowerCase(),
                                                                        }}
                                                                    />
                                                                    <span className="font-medium text-gray-700 dark:text-gray-300">
                                                                        {
                                                                            assignment.colorName
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                <motion.div
                                                                    whileHover={{
                                                                        scale: 1.05,
                                                                    }}
                                                                    whileTap={{
                                                                        scale: 0.95,
                                                                    }}
                                                                >
                                                                    <Chip
                                                                        classNames={{
                                                                            base: "bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 border border-purple-200 dark:border-purple-700",
                                                                            content:
                                                                                "font-bold text-purple-700 dark:text-purple-300",
                                                                        }}
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        {
                                                                            assignment.sizeCode
                                                                        }
                                                                    </Chip>
                                                                </motion.div>
                                                            </TableCell>
                                                            <TableCell className="text-right">
                                                                <motion.div
                                                                    animate={{
                                                                        scale: 1,
                                                                    }}
                                                                    className="inline-flex items-center gap-2"
                                                                    initial={{
                                                                        scale: 0,
                                                                    }}
                                                                    transition={{
                                                                        delay:
                                                                            index *
                                                                                0.05 +
                                                                            0.2,
                                                                        type: "spring",
                                                                    }}
                                                                >
                                                                    <span className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                                                                        {
                                                                            assignment.quantity
                                                                        }
                                                                    </span>
                                                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                                                        uds
                                                                    </span>
                                                                </motion.div>
                                                            </TableCell>
                                                            <TableCell className="text-right">
                                                                {onRemove && (
                                                                    <motion.div
                                                                        whileHover={{
                                                                            scale: 1.05,
                                                                        }}
                                                                        whileTap={{
                                                                            scale: 0.95,
                                                                        }}
                                                                    >
                                                                        <Button
                                                                            isIconOnly
                                                                            className="text-red-500 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/20"
                                                                            size="sm"
                                                                            variant="light"
                                                                            onPress={() =>
                                                                                onRemove(
                                                                                    index,
                                                                                )
                                                                            }
                                                                        >
                                                                            <TrashIcon className="w-4 h-4" />
                                                                        </Button>
                                                                    </motion.div>
                                                                )}
                                                            </TableCell>
                                                        </TableRow>
                                                    ),
                                                )}
                                            </AnimatePresence>
                                        </TableBody>
                                    </Table>
                                </div>

                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg"
                                    initial={{ opacity: 0, y: 10 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <SparklesIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Resumen de la orden
                                            </span>
                                        </div>
                                        <p className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                            {order.totalQuantity} unidades
                                            totales
                                        </p>
                                    </div>
                                </motion.div>
                            </CardBody>
                        </Card>
                    </motion.div>
                ))}
            </AnimatePresence>
        </motion.div>
    );
}
