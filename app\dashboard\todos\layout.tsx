import { ReactNode } from "react";

interface TodosLayoutProps {
    children: ReactNode;
}

export default function TodosLayout({ children }: TodosLayoutProps) {
    return (
        <section>
            <div className="bg-slate-50 dark:bg-slate-900 py-4 px-6 mb-6 rounded-lg">
                <h1 className="text-3xl font-bold">Tasks Management</h1>
                <p className="text-slate-600 dark:text-slate-400 mt-1">
                    Track and manage tasks across your manufacturing workflow
                </p>
            </div>
            {children}
        </section>
    );
}
