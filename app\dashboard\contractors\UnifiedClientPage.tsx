"use client";

import type { SortOption } from "@/shared/components/dashboard";
import type { Contractor } from "@/features/contractors/schemas/schema";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    UserGroupIcon,
    ChartBarIcon,
    AcademicCapIcon,
    EnvelopeIcon,
    PhoneIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import {
    useContractors,
    useDeleteContractor,
} from "@/features/contractors/hooks/useContractor";
import {
    Chip,
    Avatar,
    useDisclosure,
} from "@/shared/components/ui/hero-ui-client";
import ContractorDetailModal from "@/features/contractors/components/ContractorDetailModal";

export default function UnifiedContractorsPage() {
    const router = useRouter();
    const { contractors = [], isLoading, refetch } = useContractors();
    const { remove: deleteContractor } = useDeleteContractor();

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "name-asc",
        label: "Nombre A-Z",
        field: "name",
        direction: "asc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;

    // Modal state
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [selectedContractorId, setSelectedContractorId] = useState<
        string | null
    >(null);

    // Calcular métricas agregadas
    const aggregatedMetrics = useMemo(() => {
        const active = contractors.filter(
            (c) => c._count?.assignments && c._count.assignments > 0,
        );
        const totalAssignments = contractors.reduce(
            (sum, c) => sum + (c._count?.assignments || 0),
            0,
        );
        const avgAssignments =
            contractors.length > 0 ? totalAssignments / contractors.length : 0;

        // Calcular tasa promedio de completado (simulado por ahora)
        // Por ahora usamos una tasa promedio simulada
        const avgCompletionRate = 85;

        // Actividad de este mes (simulado)
        const thisMonthActivity = Math.floor(totalAssignments * 0.3);

        return {
            total: contractors.length,
            active: active.length,
            activeRate:
                contractors.length > 0
                    ? (active.length / contractors.length) * 100
                    : 0,
            avgAssignments,
            avgCompletionRate,
            thisMonthActivity,
        };
    }, [contractors]);

    // Calcular estadísticas para las tarjetas
    const stats = useMemo(
        () => [
            {
                title: "Total Contratistas",
                value: aggregatedMetrics.total,
                icon: <UserGroupIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Registrados",
            },
            {
                title: "Tasa Activa",
                value: `${aggregatedMetrics.activeRate.toFixed(0)}%`,
                description: `${aggregatedMetrics.active} con asignaciones`,
                icon: <ChartBarIcon className="w-6 h-6" />,
                color: "success" as const,
                change: aggregatedMetrics.active,
                changeLabel: "activos",
            },
            {
                title: "Promedio Asignaciones",
                value: aggregatedMetrics.avgAssignments.toFixed(1),
                description: "por contratista",
                icon: <AcademicCapIcon className="w-6 h-6" />,
                color: "warning" as const,
            },
            {
                title: "Actividad del Mes",
                value: aggregatedMetrics.thisMonthActivity,
                icon: <CalendarIcon className="w-6 h-6" />,
                color: "default" as const,
                changeLabel: "nuevas asignaciones",
            },
        ],
        [aggregatedMetrics],
    );

    // Obtener el nombre completo del contratista
    const getFullName = (contractor: Contractor) => {
        const parts = [
            contractor.firstName,
            contractor.middleName,
            contractor.lastName,
            contractor.secondLastName,
        ].filter(Boolean);

        return parts.length > 0 ? parts.join(" ") : contractor.name;
    };

    // Columnas de la tabla mejoradas
    const columns = [
        {
            key: "contractor",
            label: "Contratista",
            sortable: true,
            render: (contractor: Contractor) => (
                <div className="flex items-center gap-3">
                    <Avatar
                        color="primary"
                        getInitials={(name) => {
                            const parts = name.split(" ");

                            if (parts.length >= 2) {
                                return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
                            }

                            return name.slice(0, 2).toUpperCase();
                        }}
                        name={getFullName(contractor)}
                        size="sm"
                    />
                    <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                            {contractor.name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            {getFullName(contractor)}
                        </p>
                    </div>
                </div>
            ),
        },
        {
            key: "contact",
            label: "Contacto",
            render: (contractor: Contractor) => (
                <div className="space-y-1">
                    {contractor.email && (
                        <div className="flex items-center gap-1 text-sm">
                            <EnvelopeIcon className="w-4 h-4 text-gray-400" />
                            <a
                                className="text-blue-600 hover:underline"
                                href={`mailto:${contractor.email}`}
                            >
                                {contractor.email}
                            </a>
                        </div>
                    )}
                    {contractor.phone && (
                        <div className="flex items-center gap-1 text-sm">
                            <PhoneIcon className="w-4 h-4 text-gray-400" />
                            <a
                                className="text-blue-600 hover:underline"
                                href={`tel:${contractor.phone}`}
                            >
                                {contractor.phone}
                            </a>
                        </div>
                    )}
                    {!contractor.email && !contractor.phone && (
                        <span className="text-sm text-gray-400">
                            Sin contacto
                        </span>
                    )}
                </div>
            ),
        },
        {
            key: "assignments",
            label: "Asignaciones",
            sortable: true,
            render: (contractor: Contractor) => {
                const count = contractor._count?.assignments || 0;
                const hasAssignments = count > 0;

                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <span
                                className={`text-lg font-semibold ${hasAssignments ? "text-gray-900 dark:text-white" : "text-gray-400"}`}
                            >
                                {count}
                            </span>
                            {hasAssignments && (
                                <Chip color="success" size="sm" variant="flat">
                                    Activo
                                </Chip>
                            )}
                        </div>
                        {contractor._count?.remissions &&
                            contractor._count.remissions > 0 && (
                                <span className="text-xs text-gray-500">
                                    {contractor._count.remissions} remisiones
                                </span>
                            )}
                    </div>
                );
            },
        },
        {
            key: "remissions",
            label: "Remisiones",
            sortable: true,
            render: (contractor: Contractor) => {
                const count = contractor._count?.remissions || 0;

                return (
                    <div className="text-center">
                        <span
                            className={`text-lg font-semibold ${count > 0 ? "text-gray-900 dark:text-white" : "text-gray-400"}`}
                        >
                            {count}
                        </span>
                    </div>
                );
            },
        },
        {
            key: "createdAt",
            label: "Fecha Registro",
            sortable: true,
            render: (contractor: Contractor) => {
                return (
                    <div className="text-sm">
                        <div className="flex items-center gap-1">
                            <CalendarIcon className="w-4 h-4 text-gray-400" />
                            <span>
                                {format(
                                    new Date(contractor.createdAt),
                                    "dd/MM/yyyy",
                                    { locale: es },
                                )}
                            </span>
                        </div>
                    </div>
                );
            },
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (contractor: Contractor) => {
                setSelectedContractorId(contractor.id);
                onOpen();
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (contractor: Contractor) => {
                router.push(`/dashboard/contractors/${contractor.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (contractor: Contractor) => {
                if (
                    contractor._count?.assignments &&
                    contractor._count.assignments > 0
                ) {
                    addToast({
                        title: "No se puede eliminar",
                        description:
                            "Este contratista tiene asignaciones activas",
                        color: "warning",
                    });

                    return;
                }

                if (
                    confirm(
                        `¿Estás seguro de eliminar al contratista ${contractor.name}?`,
                    )
                ) {
                    const result = await deleteContractor(contractor.id);

                    if (result.success) {
                        refetch();
                        addToast({
                            title: "Contratista eliminado",
                            description:
                                "El contratista ha sido eliminado correctamente",
                            color: "success",
                        });
                    }
                }
            },
            color: "danger" as const,
            isDisabled: (contractor: Contractor) =>
                !!(
                    contractor._count?.assignments &&
                    contractor._count.assignments > 0
                ),
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "active", label: "Con asignaciones" },
                { value: "inactive", label: "Sin asignaciones" },
                { value: "has-contact", label: "Con contacto" },
                { value: "no-contact", label: "Sin contacto" },
            ],
        },
        {
            key: "remissions",
            label: "Remisiones",
            type: "select" as const,
            placeholder: "Todas",
            options: [
                { value: "all", label: "Todas" },
                { value: "with-remissions", label: "Con remisiones" },
                { value: "no-remissions", label: "Sin remisiones" },
            ],
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "name-asc",
            label: "Nombre A-Z",
            field: "name",
            direction: "asc" as const,
        },
        {
            key: "name-desc",
            label: "Nombre Z-A",
            field: "name",
            direction: "desc" as const,
        },
        {
            key: "assignments-desc",
            label: "Más asignaciones",
            field: "_count.assignments",
            direction: "desc" as const,
        },
        {
            key: "assignments-asc",
            label: "Menos asignaciones",
            field: "_count.assignments",
            direction: "asc" as const,
        },
        {
            key: "remissions-desc",
            label: "Más remisiones",
            field: "_count.remissions",
            direction: "desc" as const,
        },
        {
            key: "remissions-asc",
            label: "Menos remisiones",
            field: "_count.remissions",
            direction: "asc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
    ];

    // Filtrar datos
    const filteredData = useMemo(() => {
        let filtered = [...contractors];

        // Búsqueda
        if (searchValue) {
            filtered = filtered.filter((contractor) => {
                const fullName = getFullName(contractor).toLowerCase();
                const search = searchValue.toLowerCase();

                return (
                    contractor.name.toLowerCase().includes(search) ||
                    fullName.includes(search) ||
                    contractor.email?.toLowerCase().includes(search) ||
                    contractor.phone?.includes(search)
                );
            });
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            switch (filterValues.status) {
                case "active":
                    filtered = filtered.filter(
                        (c) =>
                            c._count?.assignments && c._count.assignments > 0,
                    );
                    break;
                case "inactive":
                    filtered = filtered.filter(
                        (c) =>
                            !c._count?.assignments ||
                            c._count.assignments === 0,
                    );
                    break;
                case "has-contact":
                    filtered = filtered.filter((c) => c.email || c.phone);
                    break;
                case "no-contact":
                    filtered = filtered.filter((c) => !c.email && !c.phone);
                    break;
            }
        }

        // Filtro por remisiones
        if (filterValues.remissions && filterValues.remissions !== "all") {
            switch (filterValues.remissions) {
                case "with-remissions":
                    filtered = filtered.filter(
                        (c) => c._count?.remissions && c._count.remissions > 0,
                    );
                    break;
                case "no-remissions":
                    filtered = filtered.filter(
                        (c) =>
                            !c._count?.remissions || c._count.remissions === 0,
                    );
                    break;
            }
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [contractors, searchValue, filterValues, currentSort]);

    // Componente de contratistas más activos
    const TopContractors = () => {
        const topContractors = useMemo(() => {
            return [...contractors]
                .filter(
                    (c) => c._count?.assignments && c._count.assignments > 0,
                )
                .sort(
                    (a, b) =>
                        (b._count?.assignments || 0) -
                        (a._count?.assignments || 0),
                )
                .slice(0, 5);
        }, [contractors]);

        if (topContractors.length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
                initial={{ opacity: 0, y: 20 }}
            >
                <h3 className="text-lg font-semibold mb-4">
                    Top 5 Contratistas Más Activos
                </h3>
                <div className="space-y-3">
                    {topContractors.map((contractor, index) => (
                        <div
                            key={contractor.id}
                            className="flex items-center justify-between"
                        >
                            <div className="flex items-center gap-3">
                                <span
                                    className={`text-sm font-bold ${index === 0 ? "text-yellow-600" : index === 1 ? "text-gray-500" : index === 2 ? "text-orange-600" : "text-gray-600"}`}
                                >
                                    #{index + 1}
                                </span>
                                <Avatar
                                    color={index === 0 ? "warning" : "default"}
                                    name={getFullName(contractor)}
                                    size="sm"
                                />
                                <div>
                                    <p className="font-medium">
                                        {contractor.name}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {contractor._count?.remissions || 0}{" "}
                                        remisiones
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="text-lg font-semibold text-blue-600">
                                    {contractor._count?.assignments || 0}
                                </p>
                                <p className="text-xs text-gray-500">
                                    asignaciones
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </motion.div>
        );
    };

    // Handle modal actions
    const handleEdit = (contractorId: string) => {
        onClose();
        router.push(`/dashboard/contractors/${contractorId}/edit`);
    };

    const handleDelete = async (contractorId: string) => {
        const result = await deleteContractor(contractorId);

        if (result.success) {
            refetch();
            onClose();
            addToast({
                title: "Contratista eliminado",
                description: "El contratista ha sido eliminado correctamente",
                color: "success",
            });
        }
    };

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={columns}
                currentSort={currentSort}
                filterValues={filterValues}
                isLoading={isLoading}
                page={page}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Gestión de contratistas y su desempeño"
                title="Contratistas"
                totalPages={Math.ceil(filteredData.length / rowsPerPage)}
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onPageChange={setPage}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={actions}
                // Create
                createRoute="/dashboard/contractors/new"
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key] && filterValues[key] !== "all",
                    ).length
                }
                // Pagination
                breadcrumbs={[{ label: "Contratistas" }]}
                // Stats
                stats={stats}
                createLabel="Nuevo Contratista"
                // Filters
                filters={filters}
                data={filteredData}
                // Table
                emptyContent="No hay contratistas registrados"
            />

            {/* Top contractors */}
            {contractors.length > 0 && <TopContractors />}

            {/* Modal de detalles */}
            {selectedContractorId && (
                <ContractorDetailModal
                    contractorId={selectedContractorId}
                    isOpen={isOpen}
                    onClose={onClose}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                />
            )}
        </>
    );
}
