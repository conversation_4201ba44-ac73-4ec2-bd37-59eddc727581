"use client";

import useS<PERSON> from "swr";
import { addToast } from "@heroui/react";

import { getPackings, getPackingById, getPackingsByCustomer } from "../actions";

interface UsePackingsParams {
    customerId?: string;
    subCustomerId?: string;
    statusId?: string;
    search?: string;
    page?: number;
    limit?: number;
}

export function usePackings(params: UsePackingsParams = {}) {
    const key = ["packings", params];

    const { data, error, mutate } = useSWR(
        key,
        async () => {
            const result = await getPackings(params);

            if (!result.success) {
                throw new Error(result.error);
            }

            return result.data;
        },
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        },
    );

    return {
        packings: data?.packings || [],
        pagination: data?.pagination,
        isLoading: !error && !data,
        isError: error,
        mutate,
    };
}

export function usePacking(id: string) {
    const { data, error, mutate } = useSWR(
        id ? ["packing", id] : null,
        async () => {
            const result = await getPackingById(id);

            if (!result.success) {
                throw new Error(result.error);
            }

            return result.data;
        },
        {
            revalidateOnFocus: false,
        },
    );

    return {
        packing: data,
        isLoading: !error && !data,
        isError: error,
        mutate,
    };
}

export function useCustomerPackings(
    customerId: string,
    includeSubCustomers = true,
) {
    const { data, error, mutate } = useSWR(
        customerId
            ? ["customer-packings", customerId, includeSubCustomers]
            : null,
        async () => {
            const result = await getPackingsByCustomer(
                customerId,
                includeSubCustomers,
            );

            if (!result.success) {
                throw new Error(result.error);
            }

            return result.data;
        },
        {
            revalidateOnFocus: false,
        },
    );

    return {
        packings: data || [],
        isLoading: !error && !data,
        isError: error,
        mutate,
    };
}

export function useCreatePacking() {
    const createPacking = async (data: any) => {
        try {
            const { createPacking } = await import("../actions");
            const result = await createPacking(data);

            if (result.success) {
                addToast({
                    title: "Packing creado",
                    description: `Se creó el packing ${result.data?.folio || "N/A"} exitosamente`,
                    color: "success",
                });
            } else {
                addToast({
                    title: "Error al crear packing",
                    description: result.error,
                    color: "danger",
                });
            }

            return result;
        } catch (error) {
            addToast({
                title: "Error",
                description: "Ocurrió un error inesperado",
                color: "danger",
            });

            return { success: false, error: "Error inesperado" };
        }
    };

    return { createPacking };
}

export function useUpdatePackingStatus() {
    const updateStatus = async (
        packingId: string,
        statusId: string,
        notes?: string,
    ) => {
        try {
            const { updatePackingStatus } = await import("../actions");
            const result = await updatePackingStatus({
                packingId,
                statusId,
                notes,
            });

            if (result.success) {
                addToast({
                    title: "Estado actualizado",
                    description:
                        "El estado del packing se actualizó correctamente",
                    color: "success",
                });
            } else {
                addToast({
                    title: "Error al actualizar",
                    description: result.error,
                    color: "danger",
                });
            }

            return result;
        } catch (error) {
            addToast({
                title: "Error",
                description: "Ocurrió un error inesperado",
                color: "danger",
            });

            return { success: false, error: "Error inesperado" };
        }
    };

    return { updateStatus };
}

export function useDeletePacking() {
    const deletePacking = async (packingId: string) => {
        try {
            const { deletePacking } = await import("../actions");
            const result = await deletePacking(packingId);

            if (result.success) {
                addToast({
                    title: "Packing eliminado",
                    description: "El packing se eliminó correctamente",
                    color: "success",
                });
            } else {
                addToast({
                    title: "Error al eliminar",
                    description: result.error,
                    color: "danger",
                });
            }

            return result;
        } catch (error) {
            addToast({
                title: "Error",
                description: "Ocurrió un error inesperado",
                color: "danger",
            });

            return { success: false, error: "Error inesperado" };
        }
    };

    return { deletePacking };
}
