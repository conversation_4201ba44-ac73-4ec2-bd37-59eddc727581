"use client";

import React from "react";
import { Spinner, Progress, Card, CardBody } from "@heroui/react";
import {
    CheckCircleIcon,
    DocumentTextIcon,
    CloudArrowDownIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { RemissionGenerationState } from "@/hooks/useRemissionGeneration";

interface ProcessingPanelProps {
    state: RemissionGenerationState;
    className?: string;
}

const stepConfig = {
    idle: {
        icon: DocumentTextIcon,
        label: "Preparando remisión",
        color: "default" as const,
    },
    creating: {
        icon: DocumentTextIcon,
        label: "Creando remisión",
        color: "primary" as const,
    },
    generating: {
        icon: DocumentTextIcon,
        label: "Generando PDF",
        color: "primary" as const,
    },
    downloading: {
        icon: CloudArrowDownIcon,
        label: "Descargando PDF",
        color: "success" as const,
    },
    complete: {
        icon: CheckCircleIcon,
        label: "Remisión lista",
        color: "success" as const,
    },
};

export function ProcessingPanel({
    state,
    className = "",
}: ProcessingPanelProps) {
    const { currentStep, progress, isGenerating, isDownloading, error } = state;
    const config = stepConfig[currentStep];
    const Icon = config.icon;

    if (!isGenerating && !isDownloading && currentStep === "idle") {
        return null;
    }

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={className}
            exit={{ opacity: 0, y: 20 }}
            initial={{ opacity: 0, y: 20 }}
        >
            <Card className="w-full">
                <CardBody className="gap-4">
                    <div className="flex items-center gap-3">
                        {isGenerating || isDownloading ? (
                            <Spinner color={config.color} size="sm" />
                        ) : (
                            <Icon
                                className={`h-5 w-5 ${currentStep === "complete" ? "text-success" : "text-primary"}`}
                            />
                        )}
                        <span className="text-sm font-medium">
                            {config.label}
                        </span>
                    </div>

                    {(isGenerating || isDownloading) && (
                        <Progress
                            className="max-w-md"
                            color={config.color}
                            label="Progreso"
                            showValueLabel={true}
                            size="sm"
                            value={progress}
                        />
                    )}

                    {error && (
                        <div className="rounded-md bg-danger-50 p-3">
                            <p className="text-sm text-danger">{error}</p>
                        </div>
                    )}

                    {currentStep === "complete" && (
                        <motion.div
                            animate={{ opacity: 1 }}
                            className="flex items-center gap-2 text-sm text-success"
                            initial={{ opacity: 0 }}
                            transition={{ delay: 0.3 }}
                        >
                            <CheckCircleIcon className="h-4 w-4" />
                            <span>Remisión generada exitosamente</span>
                        </motion.div>
                    )}
                </CardBody>
            </Card>
        </motion.div>
    );
}
