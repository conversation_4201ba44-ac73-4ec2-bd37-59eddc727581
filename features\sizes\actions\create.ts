"use server";

import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";

import { sizeSchema } from "../schemas/schema";

import { validateSizeCode } from "./validate";
import { revalidateSizeCache } from "./revalidate";

/**
 * Crea una nueva talla
 */
export async function createSize(data: { code: string }) {
    try {
        // Validar datos
        const validatedData = sizeSchema.parse(data);

        // Verificar si el código ya existe
        const codeValidation = await validateSizeCode(validatedData.code);

        if (!codeValidation || !codeValidation.success) {
            return {
                success: false,
                error:
                    codeValidation?.error || "Error al validar código de talla",
            };
        }

        // Verificar el resultado de la validación
        if (!codeValidation.data || !(codeValidation.data as any).isValid) {
            return {
                success: false,
                error: "El código de talla ya existe en el sistema",
            };
        }

        // Crear la talla en la base de datos
        const size = await db.size.create({
            data: { code: validatedData.code },
        });

        // Revalidar caché
        await revalidateSizeCache(size.id);

        return { success: true, data: size };
    } catch (error) {
        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos de talla inválidos",
            };
        }

        // Usar el helper para manejar otros errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al crear talla");
    }
}
