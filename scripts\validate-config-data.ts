import { PrismaClient } from "@prisma/client";
import { CONFIG_NAMES } from "../constants/config-names";

const prisma = new PrismaClient();

/**
 * Script de validación para verificar que todos los datos de configuración
 * existen correctamente en la base de datos con nombres en español
 */
async function validateConfigData() {
    console.log("🔍 Iniciando validación de datos de configuración...\n");
    
    let hasErrors = false;
    
    // Validar OrderStatus
    console.log("📦 Validando OrderStatus:");
    for (const [key, name] of Object.entries(CONFIG_NAMES.orderStatus)) {
        const exists = await prisma.orderStatus.findUnique({
            where: { name }
        });
        if (exists) {
            console.log(`  ✅ ${name}`);
        } else {
            console.log(`  ❌ Falta: ${name} (key: ${key})`);
            hasErrors = true;
        }
    }
    
    // Validar NoteStatus
    console.log("\n📝 Validando NoteStatus:");
    for (const [key, name] of Object.entries(CONFIG_NAMES.noteStatus)) {
        const exists = await prisma.noteStatus.findUnique({
            where: { name }
        });
        if (exists) {
            console.log(`  ✅ ${name}`);
        } else {
            console.log(`  ❌ Falta: ${name} (key: ${key})`);
            hasErrors = true;
        }
    }
    
    // Validar NoteImportance
    console.log("\n⚡ Validando NoteImportance:");
    for (const [key, name] of Object.entries(CONFIG_NAMES.noteImportance)) {
        const exists = await prisma.noteImportance.findUnique({
            where: { name }
        });
        if (exists) {
            console.log(`  ✅ ${name}`);
        } else {
            console.log(`  ❌ Falta: ${name} (key: ${key})`);
            hasErrors = true;
        }
    }
    
    // Validar PackingStatus
    console.log("\n📦 Validando PackingStatus:");
    for (const [key, name] of Object.entries(CONFIG_NAMES.packingStatus)) {
        const exists = await prisma.packingStatus.findUnique({
            where: { name }
        });
        if (exists) {
            console.log(`  ✅ ${name}`);
        } else {
            console.log(`  ❌ Falta: ${name} (key: ${key})`);
            hasErrors = true;
        }
    }
    
    // Validar RejectionReason
    console.log("\n🚫 Validando RejectionReason:");
    for (const [key, name] of Object.entries(CONFIG_NAMES.rejectionReason)) {
        const exists = await prisma.rejectionReason.findUnique({
            where: { name }
        });
        if (exists) {
            console.log(`  ✅ ${name}`);
        } else {
            console.log(`  ❌ Falta: ${name} (key: ${key})`);
            hasErrors = true;
        }
    }
    
    // Validar Role
    console.log("\n👥 Validando Role:");
    for (const [key, name] of Object.entries(CONFIG_NAMES.role)) {
        const exists = await prisma.role.findUnique({
            where: { name }
        });
        if (exists) {
            console.log(`  ✅ ${name}`);
        } else {
            console.log(`  ❌ Falta: ${name} (key: ${key})`);
            hasErrors = true;
        }
    }
    
    // Resumen
    console.log("\n" + "=".repeat(50));
    if (hasErrors) {
        console.log("❌ Se encontraron errores. Ejecuta 'npm run db:seed' para insertar los datos faltantes.");
    } else {
        console.log("✅ Todos los datos de configuración están correctos!");
    }
}

// Ejecutar validación
validateConfigData()
    .catch((e) => {
        console.error("Error durante la validación:", e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
