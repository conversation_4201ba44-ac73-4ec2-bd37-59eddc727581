"use server";

import { prisma } from "@/shared/lib/prisma";

import { ActionResponse, createErrorResponse, revalidateCache } from "./utils";

/**
 * Verifica y crea partidas automáticas para una orden si no tiene ninguna
 */
export async function checkAndCreateOrderParts(
    id: string,
): Promise<ActionResponse<any>> {
    try {
        // REMOVED: console.log(`[checkAndCreateOrderParts] Verificando orden ${id}`);

        // Buscar la orden con sus partes y prendas
        const order = await prisma.order.findUnique({
            where: { id },
            include: {
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                    },
                },
            },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${id}`,
            };
        }

        // Si ya tiene partidas, no hacer nada
        if (order.parts && order.parts.length > 0) {
            // La orden ya tiene partidas - comentario removido

            return {
                success: true,
                data: {
                    message: `La orden ya tiene ${order.parts.length} partidas`,
                    partsCount: order.parts.length,
                },
            };
        }

        // Si no tiene prendas, no se pueden generar partidas
        if (!order.garments || order.garments.length === 0) {
            // La orden no tiene prendas para generar partidas - comentario removido

            return {
                success: false,
                error: "La orden no tiene prendas para generar partidas automáticamente",
            };
        }

        // Agrupar prendas por modelo y color
        const garmentsByModelColor = new Map<
            string,
            {
                model: { id: string; code: string };
                color: { id: string; name: string };
            }
        >();

        for (const garment of order.garments) {
            const key = `${garment.modelId}_${garment.colorId}`;

            garmentsByModelColor.set(key, {
                model: {
                    id: garment.modelId,
                    code: garment.model.code,
                },
                color: {
                    id: garment.colorId,
                    name: garment.color.name,
                },
            });
        }

        // Generando partidas para combinaciones modelo/color - comentario removido

        // Crear partidas basadas en las combinaciones modelo/color
        let partCode = 1;
        const partsToCreate = Array.from(garmentsByModelColor.values()).map(
            (data) => ({
                orderId: id,
                code: `P${partCode++}`,
                // Puedes añadir información adicional como:
                // description: `${data.model.code} - ${data.color.name}`
            }),
        );

        // Crear las partidas en la base de datos
        await prisma.orderPart.createMany({
            data: partsToCreate,
        });

        // Partidas creadas para la orden - comentario removido

        // Revalidar caché para esta orden
        revalidateCache(id);

        return {
            success: true,
            data: {
                message: `Se generaron ${partsToCreate.length} partidas automáticamente`,
                partsCount: partsToCreate.length,
                parts: partsToCreate,
            },
        };
    } catch (error) {
        // REMOVED: console.error("[checkAndCreateOrderParts] Error:", error);

        return createErrorResponse(error);
    }
}

/**
 * Crea una nueva partida para una orden
 */
export async function createOrderPart(
    orderId: string,
    data: { code: string },
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id: orderId },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${orderId}`,
            };
        }

        // Crear la partida
        const part = await prisma.orderPart.create({
            data: {
                orderId,
                code: data.code,
            },
        });

        // Revalidar caché para esta orden
        revalidateCache(orderId);

        return {
            success: true,
            data: part,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Actualiza una partida existente
 */
export async function updateOrderPart(
    id: string,
    data: { code: string },
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la partida exista
        const part = await prisma.orderPart.findUnique({
            where: { id },
            include: { order: true },
        });

        if (!part) {
            return {
                success: false,
                error: `No se encontró la partida con ID ${id}`,
            };
        }

        // Actualizar la partida
        const updatedPart = await prisma.orderPart.update({
            where: { id },
            data: { code: data.code },
        });

        // Revalidar caché para la orden relacionada
        revalidateCache(part.orderId);

        return {
            success: true,
            data: updatedPart,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Elimina una partida
 */
export async function deleteOrderPart(
    id: string,
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la partida exista
        const part = await prisma.orderPart.findUnique({
            where: { id },
        });

        if (!part) {
            return {
                success: false,
                error: `No se encontró la partida con ID ${id}`,
            };
        }

        // Guardar el ID de la orden para revalidar después
        const orderId = part.orderId;

        // Eliminar la partida
        await prisma.orderPart.delete({
            where: { id },
        });

        // Revalidar caché para la orden relacionada
        revalidateCache(orderId);

        return {
            success: true,
            data: { id, orderId },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Función para migrar todos los pedidos existentes y asegurarse de que tengan partidas
 * Esta función puede ejecutarse manualmente desde un endpoint o script de administración
 */
export async function migrateAllOrdersToHaveParts(): Promise<
    ActionResponse<any>
> {
    try {
        // Obtener todos los pedidos que no tienen partidas
        const ordersWithoutParts = await prisma.order.findMany({
            where: {
                parts: {
                    none: {},
                },
            },
            select: {
                id: true,
            },
        });

        if (ordersWithoutParts.length === 0) {
            return {
                success: true,
                data: {
                    message: "Todos los pedidos ya tienen partidas",
                    processed: 0,
                },
            };
        }

        // Procesar cada pedido sin partidas
        let successCount = 0;
        let errorCount = 0;
        const errors: string[] = [];

        for (const order of ordersWithoutParts) {
            try {
                const result = await checkAndCreateOrderParts(order.id);

                if (result.success) {
                    successCount++;
                } else {
                    errorCount++;
                    errors.push(`Error en pedido ${order.id}: ${result.error}`);
                }
            } catch (error) {
                errorCount++;
                errors.push(
                    `Excepción en pedido ${order.id}: ${error instanceof Error ? error.message : "Error desconocido"}`,
                );
            }
        }

        return {
            success: true,
            data: {
                message: `Migración completada: ${successCount} pedidos actualizados con éxito, ${errorCount} errores.`,
                totalProcessed: ordersWithoutParts.length,
                successCount,
                errorCount,
                errors: errors.length > 0 ? errors : undefined,
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
