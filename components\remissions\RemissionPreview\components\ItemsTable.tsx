"use client";

import React from "react";
import {
    Table,
    TableHeader,
    TableBody,
    TableColumn,
    TableRow,
    TableCell,
} from "@heroui/table";
import { Card, CardBody } from "@heroui/card";

interface RemissionItem {
    orderNumber: string;
    model: string;
    color: string;
    quantity: number;
    quantityDelivered: number;
    pendingQuantity: number;
    unitPrice: number;
    totalPrice: number;
}

interface ItemsTableProps {
    items: RemissionItem[];
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

export const ItemsTable: React.FC<ItemsTableProps> = ({
    items,
    editMode,
    onEdit,
}) => {
    const handleEdit =
        (index: number, field: string) =>
        (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                const value =
                    field.includes("Price") ||
                    field.includes("quantity") ||
                    field.includes("Quantity")
                        ? parseFloat(e.currentTarget.textContent || "0")
                        : e.currentTarget.textContent;

                onEdit(`items.${index}.${field}`, value);
            }
        };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat("es-MX", {
            style: "currency",
            currency: "MXN",
        }).format(amount);
    };

    const totals = items.reduce(
        (acc, item) => ({
            quantity: acc.quantity + item.quantity,
            delivered: acc.delivered + item.quantityDelivered,
            pending: acc.pending + item.pendingQuantity,
            total: acc.total + item.totalPrice,
        }),
        { quantity: 0, delivered: 0, pending: 0, total: 0 },
    );

    return (
        <Card className="mb-6">
            <CardBody>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    Detalle de Productos
                </h3>

                <Table aria-label="Tabla de productos">
                    <TableHeader>
                        <TableColumn>PEDIDO</TableColumn>
                        <TableColumn>MODELO</TableColumn>
                        <TableColumn>COLOR</TableColumn>
                        <TableColumn align="center">CANTIDAD</TableColumn>
                        <TableColumn align="center">ENTREGADO</TableColumn>
                        <TableColumn align="center">PENDIENTE</TableColumn>
                        <TableColumn align="end">P. UNITARIO</TableColumn>
                        <TableColumn align="end">TOTAL</TableColumn>
                    </TableHeader>

                    <TableBody>
                        {items.map((item, index) => (
                            <TableRow key={index}>
                                <TableCell>{item.orderNumber}</TableCell>
                                <TableCell>{item.model}</TableCell>
                                <TableCell>{item.color}</TableCell>
                                <TableCell align="center">
                                    <span
                                        suppressContentEditableWarning
                                        className={
                                            editMode ? "editable-field" : ""
                                        }
                                        contentEditable={editMode}
                                        onBlur={handleEdit(index, "quantity")}
                                    >
                                        {item.quantity}
                                    </span>
                                </TableCell>
                                <TableCell align="center">
                                    <span
                                        suppressContentEditableWarning
                                        className={
                                            editMode ? "editable-field" : ""
                                        }
                                        contentEditable={editMode}
                                        onBlur={handleEdit(
                                            index,
                                            "quantityDelivered",
                                        )}
                                    >
                                        {item.quantityDelivered}
                                    </span>
                                </TableCell>
                                <TableCell align="center">
                                    {item.pendingQuantity}
                                </TableCell>
                                <TableCell align="right">
                                    {formatCurrency(item.unitPrice)}
                                </TableCell>
                                <TableCell align="right">
                                    {formatCurrency(item.totalPrice)}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>

                {/* Totals Row */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-8 gap-2 font-semibold">
                        <div className="col-span-3 text-right">Totales:</div>
                        <div className="text-center">{totals.quantity}</div>
                        <div className="text-center">{totals.delivered}</div>
                        <div className="text-center">{totals.pending}</div>
                        <div />
                        <div className="text-right">
                            {formatCurrency(totals.total)}
                        </div>
                    </div>
                </div>
            </CardBody>
        </Card>
    );
};
