# SWR to React Query Migration Guide

## Overview
This guide helps migrate from SWR to React Query with performance optimizations.

## Key Differences

| Feature | SWR | React Query |
|---------|-----|-------------|
| Cache Key | String/Array | Array only |
| Stale Time | `refreshInterval` | `staleTime` |
| Cache Time | `dedupingInterval` | `gcTime` |
| Background Refetch | `revalidateOnFocus` | `refetchOnWindowFocus` |
| Pagination | Manual | Built-in `useInfiniteQuery` |

## Basic Migration

### Before (SWR)
```typescript
import useSWR from 'swr';

const fetcher = (url: string) => fetch(url).then(res => res.json());

function useOrders() {
  const { data, error, mutate } = useSWR('/api/orders', fetcher, {
    refreshInterval: 5 * 60 * 1000,
    revalidateOnFocus: false,
  });

  return {
    orders: data,
    isLoading: !error && !data,
    isError: error,
    refresh: mutate
  };
}
```

### After (React Query)
```typescript
import { useOptimizedQuery } from '@/features/performance-monitoring/hooks/useOptimizedQuery';

function useOrders() {
  const { data, isLoading, isError, refetch } = useOptimizedQuery(
    ['orders'],
    async () => {
      const response = await fetch('/api/orders');
      return response.json();
    }
  );

  return {
    orders: data,
    isLoading,
    isError,
    refresh: refetch
  };
}
```

## Pagination Migration

### Before (SWR with manual pagination)
```typescript
function useOrdersPaginated(page: number) {
  const { data, error } = useSWR(
    `/api/orders?page=${page}`,
    fetcher
  );
  
  return {
    orders: data?.items,
    totalPages: data?.totalPages,
    isLoading: !error && !data,
  };
}
```

### After (React Query with infinite query)
```typescript
import { useOptimizedInfiniteQuery } from '@/features/performance-monitoring/hooks/useOptimizedQuery';

function useOrdersPaginated() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading
  } = useOptimizedInfiniteQuery(
    ['orders'],
    async ({ pageParam }) => {
      const response = await fetch(
        `/api/orders?cursor=${pageParam || ''}`
      );
      return response.json();
    }
  );

  return {
    orders: data?.pages.flatMap(page => page.items) ?? [],
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading
  };
}
```

## Performance Features

### 1. Request Deduplication
```typescript
import { requestDeduplicator } from '@/features/performance-monitoring/utils/request-deduplicator';

async function fetchOrder(id: string) {
  return requestDeduplicator.deduplicate(
    `order-${id}`,
    async () => {
      const response = await fetch(`/api/orders/${id}`);
      return response.json();
    }
  );
}
```

### 2. DataLoader Pattern
```typescript
import { ordersLoader } from '@/features/performance-monitoring/utils/data-loader';

// Batches multiple order fetches
async function fetchOrders(ids: string[]) {
  return Promise.all(ids.map(id => ordersLoader.load(id)));
}
```

## Migration Checklist

- [ ] Install React Query dependencies
- [ ] Add OptimizedQueryProvider to app
- [ ] Identify all useSWR hooks
- [ ] Migrate hooks one by one
- [ ] Update error handling
- [ ] Test cache behavior
- [ ] Remove SWR dependencies
