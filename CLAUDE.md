# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🎯 Core Commands

### Development
```bash
npm run dev              # Start development server
npm run dev:turbo        # Start with Turbopack (faster HMR)
npm run build           # Build for production (includes Prisma generate)
npm run start           # Start production server
```

### Code Quality
```bash
npm run lint            # Lint and auto-fix issues
npm run lint:errors     # Show only errors (quiet mode)
npm run lint:check      # Check without fixing
npm run lint:any        # Find @typescript-eslint/no-explicit-any violations
```

### Database Operations  
```bash
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema changes (dev only)
npm run db:reset        # Reset database (WARNING: data loss)
npm run db:seed         # Seed database with initial data
npm run db:validate     # Validate configuration data
```

## 🏗️ Architecture Overview

### Tech Stack
- **Framework**: Next.js 14.2.3 with App Router
- **UI Library**: HeroUI v2.7.5 + TailwindCSS
- **Database**: PostgreSQL via Prisma 6.12.0
- **Authentication**: NextAuth v4.24.7
- **Form Validation**: Zod 3.25.48
- **Data Fetching**: SWR 2.3.3 (client), Server Actions (mutations)
- **PDF Generation**: @react-pdf/renderer + jspdf

### Project Structure
```
/app                    # Next.js App Router pages
  /(auth)              # Auth layout group
  /dashboard           # Main application pages
/features              # Feature-based modules ⭐
  /[module]/
    /actions           # Server Actions (mutations)
    /components        # React components  
    /hooks            # Custom React hooks
    /types            # TypeScript types
    /schemas          # Zod validation schemas
/shared                # Cross-feature utilities
  /components/dashboard # Unified dashboard components
  /lib                # Core utilities (db, auth, etc)
  /templates          # CRUD templates
/docs                  # Architecture documentation
```

## 🔑 Key Development Patterns

### 1. Server Actions Pattern
All mutations use Server Actions with consistent structure:
```typescript
"use server";
import { z } from "zod";
import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

const { revalidateCache } = createServerRevalidation("entity");

export async function createEntity(data: EntityInput) {
  try {
    // 1. Zod validation
    const validated = schema.parse(data);
    
    // 2. Business validation (uniqueness, etc)
    const validation = await validateEntity(validated);
    if (!validation.success) {
      return { success: false, error: validation.error };
    }
    
    // 3. Database operation
    const entity = await db.entity.create({ data: validated });
    
    // 4. Cache revalidation
    revalidateCache(entity.id);
    
    return { success: true, data: entity };
  } catch (error) {
    // 5. Typed error handling
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0]?.message || "Invalid data" };
    }
    return handleDbError(() => { throw error; }, "Operation failed");
  }
}
```

### 2. Unified Response Format
All server actions return:
```typescript
type ActionResult<T> = 
  | { success: true; data: T }
  | { success: false; error: string }
```

### 3. Component Patterns
- **Server Components by default** - Client Components only when needed
- **Unified dashboard components** from `/shared/components/dashboard`
- **500 lines max per file** - split larger files into modules
- **Feature isolation** - each feature is self-contained in `/features`

### 4. Data Fetching Strategy
- **Mutations**: Server Actions (no API routes)
- **Queries**: Server Components + Prisma for initial load
- **Client refetch**: SWR hooks when reactivity needed
- **Revalidation**: Automatic cache invalidation after mutations

### 5. Error Handling
Centralized error handling via `handleDbError`:
- Prisma error code mapping to user-friendly messages
- Automatic retries for transient errors
- Consistent error response format
- Production-safe error messages

## 📋 Important Conventions

### Spanish Naming
- Database models and fields use **Spanish names**
- Comments and documentation in **Spanish**
- UI strings in **Spanish**
- Code (variables, functions) in **English**

### Import Organization
```typescript
// 1. External imports
import { useState } from "react";
import { z } from "zod";

// 2. Shared/lib imports  
import { db } from "@/shared/lib/db";

// 3. Feature imports
import { createOrder } from "@/features/orders/actions";

// 4. Relative imports
import { OrderForm } from "./components/OrderForm";
```

### File Size Limits
- Components: 500 lines max
- Server actions: Group related actions, split if >500 lines
- Use barrel exports (`index.ts`) for cleaner imports

## 🎨 UI/UX Guidelines

### Design System
Follow the unified design system (`/docs/design-system.md`):
- Use `DashboardLayout` wrapper for all dashboard pages
- Implement `DashboardTable` for data tables
- Apply `DashboardFilters` for search/filter UI
- Show metrics with `DashboardStats` cards

### HeroUI Components
Primary UI library - use HeroUI components:
- `Button`, `Input`, `Select` for forms
- `Table` with built-in features
- `Modal` for dialogs
- `Card` for content sections

### Responsive Design
- Mobile-first approach
- Test on 320px minimum width
- Use Tailwind responsive utilities

## 🚨 Critical Rules

1. **NEVER create files unless absolutely necessary** - prefer editing existing files
2. **NEVER create documentation files** unless explicitly requested
3. **Do only what has been asked** - nothing more, nothing less
4. **Always use Server Actions** for mutations (no API routes)
5. **Validate with Zod** before any database operation
6. **Follow existing patterns** - check similar features before implementing
7. **Preserve existing functionality** when making changes
8. **Use the feature-based structure** - keep features isolated

## 🔧 Common Tasks

### Creating a New CRUD Module
1. Copy template from `/shared/templates/Crud*Template.tsx`
2. Create feature folder: `/features/[module]/`
3. Implement actions, components, types, schemas
4. Add to dashboard navigation
5. Follow existing patterns from sizes, colors, models

### Adding Server Actions
1. Create in `/features/[module]/actions/`
2. Use `"use server"` directive
3. Implement consistent error handling
4. Add cache revalidation
5. Return standardized response format

### Implementing Data Tables
1. Use `DashboardTable` component
2. Define columns with proper formatting
3. Add row actions (edit, delete, view)
4. Implement pagination
5. Add filters and search

## 📚 Key Files to Reference

- `/features/sizes/` - Simple CRUD example
- `/features/orders/` - Complex module with relationships
- `/features/packings/` - Print-optimized components
- `/shared/lib/db.ts` - Database utilities and error handling
- `/shared/components/dashboard/` - Unified UI components
- `/docs/development-patterns.md` - Detailed patterns guide
- `/docs/design-system.md` - Complete UI/UX guidelines