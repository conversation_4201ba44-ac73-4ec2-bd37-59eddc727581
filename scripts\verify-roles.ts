import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyRoles() {
    try {
        console.log('Verificando roles en la base de datos...\n');

        const roles = await prisma.role.findMany({
            orderBy: { name: 'asc' }
        });

        if (roles.length === 0) {
            console.log('❌ No se encontraron roles en la base de datos');
        } else {
            console.log(`✅ Se encontraron ${roles.length} roles:\n`);
            
            roles.forEach(role => {
                console.log(`  - ${role.name}:`);
                console.log(`    ID: ${role.id}`);
                console.log(`    Icono: ${role.iconName || 'N/A'}`);
                console.log(`    Color: ${role.color || 'N/A'}`);
                console.log(`    Creado: ${role.createdAt.toLocaleString()}\n`);
            });
        }

        // Verificar si existe el rol GUEST específicamente
        const guestRole = await prisma.role.findUnique({
            where: { name: 'GUEST' }
        });

        if (guestRole) {
            console.log('✅ Rol GUEST encontrado - Los usuarios pueden registrarse');
        } else {
            console.log('❌ Rol GUEST NO encontrado - Los registros fallarán');
        }

    } catch (error) {
        console.error('Error al verificar roles:', error);
    } finally {
        await prisma.$disconnect();
    }
}

verifyRoles();
