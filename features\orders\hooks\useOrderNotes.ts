"use client";

import { useState, useCallback } from "react";
import useS<PERSON> from "swr";
import { Note } from "@prisma/client";

import { createNote as createNoteAction } from "@/features/notes/actions/create";
import { updateNote as updateNoteAction } from "@/features/notes/actions/update";
import { deleteNote as deleteNoteAction } from "@/features/notes/actions/delete";
import { getNotesByOrder } from "@/features/notes/actions/modal-notes";
import { cacheKeys } from "@/shared/utils/cacheKeys";
import { bulkUpdateNoteStatus } from "@/features/notes/actions/bulk-update-status";
import { bulkUpdateNoteImportance } from "@/features/notes/actions/bulk-update-importance";
import { bulkDeleteNotes as bulkDeleteNotesAction } from "@/features/notes/actions/bulk-delete";

// Tipos internos para el hook
interface UseOrderNotesFilters {
    importanceId?: string;
    statusId?: string;
    authorId?: string;
    searchText?: string;
    page?: number;
    pageSize?: number;
}

interface UseOrderNotesProps {
    orderId: string;
    initialFilters?: UseOrderNotesFilters;
}

interface NewNoteData {
    title?: string;
    content: string;
    importanceId: string;
    statusId: string;
}

interface UpdateNoteData {
    title?: string;
    content?: string;
    statusId?: string;
    importanceId?: string;
}

interface NoteResult {
    success: boolean;
    data?: Note;
    error?: string;
}

// Simple cache revalidation function
const createRevalidationUtil = () => {
    const revalidateKeys = async (keys: string[]) => {
        // REMOVED: console.log("Revalidating keys:", keys);

        return true;
    };

    return { revalidateKeys };
};

/**
 * Hook para gestionar notas de una orden con SWR
 */
export function useOrderNotes({
    orderId,
    initialFilters = {},
}: UseOrderNotesProps) {
    // Estado para filtros
    const [filters, setFilters] = useState<UseOrderNotesFilters>({
        ...initialFilters,
        page: initialFilters.page || 1,
        pageSize: initialFilters.pageSize || 10,
    });

    // Estados para seguimiento de operaciones
    const [isCreating, setIsCreating] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    // SWR key para la caché (cambia cuando cambian los filtros)
    const cacheKey = JSON.stringify({
        type: "orderNotes",
        orderId,
        filters,
    });

    // Use our simple revalidation utility
    const { revalidateKeys } = createRevalidationUtil();

    // Fetcher function para SWR, usando la acción del servidor en lugar de la API
    const fetcher = useCallback(async () => {
        try {
            // Usar directamente la server action y pasar los filtros
            const result = await getNotesByOrder(orderId, filters);

            if (!result.success) {
                throw new Error(
                    (result as any).error || "Failed to fetch notes",
                );
            }

            return (result as any).data;
        } catch (error) {
            // REMOVED: console.error("Error fetching notes:", error);
            throw error;
        }
    }, [orderId, filters]);

    // Hook SWR principal
    const {
        data,
        error,
        isLoading,
        mutate: mutateNotes,
    } = useSWR(orderId ? cacheKey : null, fetcher);

    // Crear nueva nota
    const createOrderNote = async (noteData: {
        title?: string;
        content: string;
        statusId: string;
        importanceId: string;
    }): Promise<NoteResult> => {
        if (!orderId) {
            return { success: false, error: "No order ID provided" };
        }

        setIsCreating(true);

        try {
            // REMOVED: console.log("Creating note with data:", { ...noteData, orderId });
            // Fix: Add an empty title if none provided
            const dataToSend = {
                ...noteData,
                title: noteData.title || "",
                orderId,
            };

            const result = await createNoteAction(dataToSend);

            if (result.success) {
                // Optimistic update
                const newNote = result.data;
                const updatedNotes = data?.notes
                    ? [...data.notes, newNote]
                    : [newNote];

                // Update local cache
                mutateNotes(
                    {
                        ...data,
                        notes: updatedNotes,
                        totalCount: (data?.totalCount || 0) + 1,
                    },
                    false,
                );

                // Revalidate to ensure consistency
                revalidateKeys([cacheKeys.orderNotes(orderId)]);

                return { success: true, data: newNote };
            } else {
                // REMOVED: console.error("Failed to create note:", result.error);

                return { success: false, error: result.error };
            }
        } catch (err: any) {
            // REMOVED: console.error("Error creating note:", err);

            return {
                success: false,
                error: err.message || "Unknown error occurred",
            };
        } finally {
            setIsCreating(false);
        }
    };

    // Actualizar nota existente - fix: take a single object parameter to match usage
    const updateOrderNote = async (noteParams: {
        noteId: string;
        content: string;
        statusId: string;
        importanceId: string;
    }): Promise<NoteResult> => {
        if (!orderId) {
            return { success: false, error: "No order ID provided" };
        }

        const { noteId, ...updateData } = noteParams;

        setIsUpdating(true);
        // REMOVED: console.log("Updating note:", noteId, "with data:", updateData);

        try {
            const result = await updateNoteAction({
                id: noteId,
                ...updateData,
                orderId,
            });

            if (result.success && result.data) {
                // Optimistic update
                const updatedNote = result.data;
                const updatedNotes = data?.notes?.map((note: any) =>
                    note.id === noteId ? updatedNote : note,
                );

                // Update local cache
                mutateNotes(
                    {
                        ...data,
                        notes: updatedNotes || [],
                    },
                    false,
                );

                // Revalidate to ensure consistency
                revalidateKeys([
                    cacheKeys.orderNotes(orderId),
                    cacheKeys.note(noteId),
                ]);

                return { success: true, data: updatedNote };
            } else {
                // REMOVED: console.error("Failed to update note:", result.error);

                return { success: false, error: result.error };
            }
        } catch (err: any) {
            // REMOVED: console.error("Error updating note:", err);

            return {
                success: false,
                error: err.message || "Unknown error occurred",
            };
        } finally {
            setIsUpdating(false);
        }
    };

    // Eliminar nota existente
    const deleteOrderNote = async (noteId: string): Promise<NoteResult> => {
        if (!orderId) {
            return { success: false, error: "No order ID provided" };
        }

        setIsDeleting(true);

        try {
            const result = await deleteNoteAction(noteId);

            if (result.success) {
                // Optimistic update
                const updatedNotes = data?.notes?.filter(
                    (note: any) => note.id !== noteId,
                );

                // Update local cache
                mutateNotes(
                    {
                        ...data,
                        notes: updatedNotes || [],
                        totalCount: Math.max((data?.totalCount || 0) - 1, 0),
                    },
                    false,
                );

                // Revalidate to ensure consistency
                revalidateKeys([cacheKeys.orderNotes(orderId)]);

                return { success: true };
            } else {
                // REMOVED: console.error("Failed to delete note:", result.error);

                return { success: false, error: result.error };
            }
        } catch (err: any) {
            // REMOVED: console.error("Error deleting note:", err);

            return {
                success: false,
                error: err.message || "Unknown error occurred",
            };
        } finally {
            setIsDeleting(false);
        }
    };

    // Add updateMultipleNotes function that was missing
    const updateMultipleNotes = async (
        noteIds: string[],
        updateData: { statusId?: string; importanceId?: string },
    ): Promise<NoteResult> => {
        if (!noteIds.length) {
            return { success: false, error: "No notes selected" };
        }

        setIsUpdating(true);

        try {
            let result;

            // Call the appropriate bulk update action based on what's being updated
            if (updateData.statusId) {
                result = await bulkUpdateNoteStatus({
                    noteIds,
                    statusId: updateData.statusId,
                });
            } else if (updateData.importanceId) {
                result = await bulkUpdateNoteImportance({
                    noteIds,
                    importanceId: updateData.importanceId,
                });
            } else {
                return { success: false, error: "No update data provided" };
            }

            if (result.success) {
                // Reload data instead of optimistic update for bulk operations
                await mutateNotes();

                return { success: true };
            } else {
                // REMOVED: console.error("Failed to update notes:", result.error);

                return { success: false, error: result.error };
            }
        } catch (err: any) {
            // REMOVED: console.error("Error updating notes:", err);

            return {
                success: false,
                error: err.message || "Unknown error occurred",
            };
        } finally {
            setIsUpdating(false);
        }
    };

    // Cambiar filtros
    const changeFilters = useCallback(
        (newFilters: Partial<UseOrderNotesFilters>) => {
            setFilters((prev) => {
                // Si hay cambios en los criterios (no en paginación), resetear a página 1
                const shouldResetPage =
                    newFilters.searchText !== undefined ||
                    newFilters.importanceId !== undefined ||
                    newFilters.statusId !== undefined ||
                    newFilters.authorId !== undefined;

                return {
                    ...prev,
                    ...newFilters,
                    // Resetear página a 1 si cambian los criterios de búsqueda
                    page: shouldResetPage ? 1 : newFilters.page || prev.page,
                };
            });
        },
        [],
    );

    // Recargar datos
    const refreshNotes = useCallback(() => {
        return mutateNotes();
    }, [mutateNotes]);

    // Cargar más notas (paginación)
    const loadMoreNotes = useCallback(() => {
        if (!data || !data.hasMore) return;

        setFilters((prev) => ({
            ...prev,
            page: (prev.page || 1) + 1,
        }));
    }, [data]);

    // Bulk operations for selected notes
    const bulkUpdateStatus = async (
        noteIds: string[],
        statusId: string,
    ): Promise<boolean> => {
        const result = await bulkUpdateNoteStatus({ noteIds, statusId });

        return result.success;
    };

    const bulkUpdateImportance = async (
        noteIds: string[],
        importanceId: string,
    ): Promise<boolean> => {
        const result = await bulkUpdateNoteImportance({
            noteIds,
            importanceId,
        });

        return result.success;
    };

    const bulkDeleteNotes = async (noteIds: string[]): Promise<boolean> => {
        const result = await bulkDeleteNotesAction({ noteIds });

        return result.success;
    };

    return {
        notes: data?.notes || [],
        totalCount: data?.totalCount || 0,
        totalPages: data?.totalPages || 0,
        currentPage: data?.page || 1,
        pageSize: data?.pageSize || 10,
        hasMore: data?.hasMore || false,

        isLoading,
        isCreating,
        isUpdating,
        isDeleting,
        error,

        filters,
        changeFilters,
        createNote: createOrderNote,
        updateNote: updateOrderNote,
        deleteNote: deleteOrderNote,
        updateMultipleNotes,
        bulkUpdateStatus,
        bulkUpdateImportance,
        bulkDeleteNotes,
        refreshNotes,
        loadMoreNotes,
    };
}
