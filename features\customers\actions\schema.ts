import { z } from "zod";

// Esquema para validación de datos
export const customerSchema = z.object({
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres"),
    displayName: z
        .string()
        .max(100, "El nombre para mostrar no puede exceder los 100 caracteres")
        .optional()
        .nullable(),
});

// Esquema para crear subcliente
export const createSubcustomerSchema = z.object({
    name: z.string().min(1, "El nombre es requerido"),
    parentId: z.string().min(1, "El cliente padre es requerido"),
    displayName: z.string().optional(),
});

// Esquema para actualizar subcliente
export const updateSubcustomerSchema = z.object({
    id: z.string().min(1, "ID es requerido"),
    name: z.string().min(1, "El nombre es requerido"),
    displayName: z.string().optional(),
});

// Esquema para eliminar subcliente
export const deleteSubcustomerSchema = z.object({
    id: z.string().min(1, "ID es requerido"),
});

// Esquema para actualizar jerarquía
export const updateHierarchySchema = z.object({
    customerId: z.string().min(1, "El ID del cliente es requerido"),
    parentId: z.string().nullable(),
});
