/**
 * Archivo de exportación central para todas las constantes del proyecto
 * Facilita las importaciones en cualquier parte de la aplicación
 */

// Exportamos todas las constantes de manera individual para evitar errores de importación
// Este enfoque es más seguro mientras que TypeScript indexa los archivos correctamente
export const HOME = "/";
export const DASHBOARD = "/dashboard";
export const ORDERS = "/dashboard/orders";
export const CUSTOMERS = "/dashboard/customers";

// Exportamos colores base de la aplicación
export const COLORS = {
    primary: {
        light: "#60a5fa",
        DEFAULT: "#3b82f6",
        dark: "#2563eb",
    },
    success: {
        light: "#4ade80",
        DEFAULT: "#22c55e",
        dark: "#16a34a",
    },
    error: {
        light: "#f87171",
        DEFAULT: "#ef4444",
        dark: "#dc2626",
    },
};

// Constantes de API
export const API_BASE_URL = "/api";
export const API_ENDPOINTS = {
    orders: "/api/orders",
    customers: "/api/customers",
    contractors: "/api/contractors",
};

// Cuando TypeScript indexe correctamente los archivos, podemos volver a:
// export * from './routes';
// export * from './ui';
// export * from './api';
