# Agent OS - Lohari

Sistema de gestión de producción textil con Agent OS integrado.

## 🎯 Visión del Producto

Lohari implementa los 4 principios fundamentales de la administración (Planear, Organizar, Dirigir y **CONTROLAR**) para empresas textiles, permitiendo control integral de pedidos, contratistas, clientes y entregas.

## 🚀 Quick Start con Agent OS

### Crear un nuevo feature
```
@create-spec Quiero implementar [descripción del feature]
```

### Mejorar código existente
```
@analyze-product Revisar el módulo de [módulo] para [objetivo]
```

### Planificar desarrollo
```
@plan-product ¿Qué deberíamos priorizar para [periodo]?
```

## 📁 Estructura Agent OS

```
.agent-os/
├── product/           # Documentación del producto
│   ├── overview.md    # Visión y arquitectura
│   ├── roadmap.md     # Plan de desarrollo
│   └── decisions.md   # Decisiones técnicas
├── standards/         # Estándares de desarrollo
│   ├── code-style.md  # Convenciones de código
│   └── best-practices.md # Mejores prácticas
└── specs/            # Especificaciones de features
    └── YYYY-MM-DD-[nombre]/
        └── spec.md
```

## 🔑 Principios Clave

1. **Control del Negocio**: Todo feature debe mejorar el control y visibilidad
2. **Stack Moderno**: Next.js + Server Actions + Prisma + PostgreSQL
3. **Código Bilingüe**: Inglés para código, Español para datos/UI
4. **Escalabilidad**: Diseñado para crecer de pequeña a gran empresa

## 🎯 Prioridades Actuales

1. **Refinamiento de Packing** - Alinear con objetivos reales del negocio
2. **Gestión de Subclientes** - Implementar jerarquía cliente/subcliente
3. **Dashboard de Control** - Métricas y reportes gerenciales

## 🛠️ Comandos Útiles

```bash
# Desarrollo
npm run dev           # Servidor de desarrollo
npm run build        # Build de producción

# Base de datos
npm run db:generate  # Generar cliente Prisma
npm run db:push     # Push de cambios a BD
npm run db:seed     # Seed inicial

# Calidad
npm run lint        # Linting y auto-fix
npm run lint:check  # Solo verificar
```

## 📚 Documentación

- **Técnica**: Ver `/docs/` para arquitectura y patrones
- **Producto**: Ver `.agent-os/product/` para visión y roadmap
- **Standards**: Ver `.agent-os/standards/` para convenciones

## 🤖 Uso con AI Agents

Agent OS permite que los agentes de AI (Claude Code, Cursor, etc.) entiendan:
- Tu visión del producto
- Estándares de código
- Decisiones técnicas
- Roadmap y prioridades

Esto resulta en código que sigue tus patrones y cumple tus objetivos de negocio.

---

*Lohari + Agent OS = Control Total de tu Producción Textil* 🧵