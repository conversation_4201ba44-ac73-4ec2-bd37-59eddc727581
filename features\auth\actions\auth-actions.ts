"use server";

import { isStrongPassword } from "@/features/auth/utils";
import { roleService } from "@/shared/services/role-service";

import { createUser } from "./users";

/**
 * Action para registrar un nuevo usuario
 * Esta función se expone para ser llamada desde el componente de registro
 */
export async function registerUser(formData: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
}) {
    try {
        // Validaciones básicas
        if (
            !formData.name ||
            !formData.email ||
            !formData.password ||
            !formData.confirmPassword
        ) {
            return {
                success: false,
                error: "Todos los campos son obligatorios",
            };
        }

        // Verificar que las contraseñas coincidan
        if (formData.password !== formData.confirmPassword) {
            return {
                success: false,
                error: "Las contraseñas no coinciden",
            };
        }

        // Verificar fortaleza de contraseña
        if (!isStrongPassword(formData.password)) {
            return {
                success: false,
                error: "La contraseña no cumple con los requisitos de seguridad",
            };
        }

        // Obtener o crear el rol GUEST utilizando el servicio
        try {
            const guestRole = await roleService.getOrCreateDefaultRole("GUEST");

            // Crear usuario a través de la acción existente con el nombre del rol
            const response = await createUser({
                name: formData.name,
                email: formData.email,
                password: formData.password,
                role: "GUEST", // Pasar el nombre del rol, no el ID
            });

            // Devolver respuesta de createUser
            return response;
        } catch (roleError) {
            // REMOVED: console.error("[registerUser] Error al gestionar rol:", roleError);

            return {
                success: false,
                error: "Error al configurar el rol de usuario. Contacte al administrador.",
            };
        }
    } catch (error) {
        // REMOVED: console.error("[registerUser] Error:", error);

        // Manejar errores comunes
        if (error instanceof Error) {
            if (
                error.message.includes("unique constraint") ||
                error.message.includes("Unique constraint")
            ) {
                return {
                    success: false,
                    error: "Este correo electrónico ya está registrado",
                };
            }

            return {
                success: false,
                error: error.message,
            };
        }

        return {
            success: false,
            error: "Ocurrió un error al registrar el usuario",
        };
    }
}
