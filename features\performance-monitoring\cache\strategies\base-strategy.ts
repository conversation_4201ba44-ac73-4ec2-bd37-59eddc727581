/**
 * Base Cache Strategy
 * Implements common caching operations
 */

import { getRedisClient } from "../redis-client";
import {
    CacheType,
    CacheTTL,
    generateCacheKey,
    CacheKeyOptions,
} from "../cache-types";
import { performanceTracker } from "../../utils/api-metrics";

export abstract class BaseCacheStrategy {
    protected cacheType: CacheType;
    protected defaultTTL: number;

    constructor(cacheType: CacheType) {
        this.cacheType = cacheType;
        this.defaultTTL = CacheTTL[cacheType];
    }

    /**
     * Get data from cache
     */
    async get<T>(keyOptions: CacheKeyOptions): Promise<T | null> {
        if (this.cacheType === CacheType.REAL_TIME) {
            return null; // No caching for real-time data
        }

        const client = getRedisClient();

        if (!client) return null;

        const key = generateCacheKey(keyOptions);
        const startTime = performance.now();

        try {
            const data = await client.get(key);
            const duration = performance.now() - startTime;

            performanceTracker.trackQuery({
                query: `cache:get:${this.cacheType}`,
                duration: Math.round(duration),
                timestamp: Date.now(),
            });

            if (data) {
                return JSON.parse(data);
            }

            return null;
        } catch (error) {
            console.error(`Cache get error for ${key}:`, error);

            return null;
        }
    }

    /**
     * Set data in cache
     */
    async set<T>(
        keyOptions: CacheKeyOptions,
        data: T,
        ttl?: number,
    ): Promise<boolean> {
        if (this.cacheType === CacheType.REAL_TIME) {
            return true; // No caching for real-time data
        }

        const client = getRedisClient();

        if (!client) return false;

        const key = generateCacheKey(keyOptions);
        const expiry = ttl ?? this.defaultTTL;
        const startTime = performance.now();

        try {
            await client.setex(key, expiry, JSON.stringify(data));
            const duration = performance.now() - startTime;

            performanceTracker.trackQuery({
                query: `cache:set:${this.cacheType}`,
                duration: Math.round(duration),
                timestamp: Date.now(),
            });

            // Add to type-specific set for bulk operations
            await this.addToTypeSet(key);

            return true;
        } catch (error) {
            console.error(`Cache set error for ${key}:`, error);

            return false;
        }
    }

    /**
     * Delete from cache
     */
    async delete(keyOptions: CacheKeyOptions): Promise<boolean> {
        const client = getRedisClient();

        if (!client) return false;

        const key = generateCacheKey(keyOptions);

        try {
            await client.del(key);
            await this.removeFromTypeSet(key);

            return true;
        } catch (error) {
            console.error(`Cache delete error for ${key}:`, error);

            return false;
        }
    }

    /**
     * Add key to type-specific set for tracking
     */
    protected async addToTypeSet(key: string): Promise<void> {
        const client = getRedisClient();

        if (!client) return;

        const setKey = `cache:type:${this.cacheType}`;

        await client.sadd(setKey, key);
    }

    /**
     * Remove key from type-specific set
     */
    protected async removeFromTypeSet(key: string): Promise<void> {
        const client = getRedisClient();

        if (!client) return;

        const setKey = `cache:type:${this.cacheType}`;

        await client.srem(setKey, key);
    }

    /**
     * Clear all cache entries of this type
     */
    async clearAll(): Promise<number> {
        const client = getRedisClient();

        if (!client) return 0;

        const setKey = `cache:type:${this.cacheType}`;
        const keys = await client.smembers(setKey);

        if (keys.length > 0) {
            await client.del(...keys);
            await client.del(setKey);
        }

        return keys.length;
    }
}
