import React from "react";
import { Chip } from "@heroui/react";
import {
    CheckCircleIcon,
    ClockIcon,
    ExclamationCircleIcon,
    InboxIcon,
    CogIcon,
    CheckBadgeIcon,
    CubeIcon,
    TruckIcon,
    XCircleIcon,
    SparklesIcon,
} from "@heroicons/react/24/outline";
import { cn } from "@heroui/react";

import { OrderStatus } from "../types/orders";

// Mapa de iconos disponibles - actualizado para coincidir con BD
const iconMap: Record<string, React.ReactNode> = {
    // Mapeo antiguo para compatibilidad
    check: <CheckCircleIcon className="w-3 h-3" />,
    clock: <ClockIcon className="w-3 h-3" />,
    warning: <ExclamationCircleIcon className="w-3 h-3" />,

    // Mapeo nuevo con nombres completos de la BD
    CheckCircleIcon: <CheckCircleIcon className="w-3 h-3" />,
    ClockIcon: <ClockIcon className="w-3 h-3" />,
    ExclamationCircleIcon: <ExclamationCircleIcon className="w-3 h-3" />,
    InboxIcon: <InboxIcon className="w-3 h-3" />,
    CogIcon: <CogIcon className="w-3 h-3" />,
    CheckBadgeIcon: <CheckBadgeIcon className="w-3 h-3" />,
    CubeIcon: <CubeIcon className="w-3 h-3" />,
    TruckIcon: <TruckIcon className="w-3 h-3" />,
    XCircleIcon: <XCircleIcon className="w-3 h-3" />,
    SparklesIcon: <SparklesIcon className="w-3 h-3" />,
};

export interface OrderStatusChipProps {
    status: OrderStatus;
    size?: "sm" | "md" | "lg";
    variant?: "flat" | "solid" | "bordered" | "dot";
    className?: string;
    showIcon?: boolean;
    style?: React.CSSProperties;
}

/**
 * Componente reutilizable para mostrar el estado de una orden
 * con estilos y colores consistentes en toda la aplicación
 */
export function OrderStatusChip({
    status,
    size = "sm",
    variant = "flat",
    className,
    showIcon = true,
    style,
}: OrderStatusChipProps) {
    // Verificar que el estado sea válido
    if (!status || !status.name) {
        return null;
    }

    // Obtener el icono del mapa si está disponible
    const icon = status.iconName && iconMap[status.iconName];

    // Mapeo de respaldo por nombre si no se encuentra por iconName
    const iconBackup: Record<string, React.ReactNode> = {
        Nuevo: <SparklesIcon className="w-3 h-3" />,
        "En producción": <CogIcon className="w-3 h-3" />,
        Empaquetando: <CubeIcon className="w-3 h-3" />,
        "Listo para entregar": <TruckIcon className="w-3 h-3" />,
        Entregado: <CheckCircleIcon className="w-3 h-3" />,
        Recibido: <InboxIcon className="w-3 h-3" />,
        "Control de calidad": <CheckBadgeIcon className="w-3 h-3" />,
        Rechazado: <ExclamationCircleIcon className="w-3 h-3" />,
        Cancelado: <XCircleIcon className="w-3 h-3" />,
    };

    const finalIcon = icon || (status.name && iconBackup[status.name]) || null;

    // Normalizar el color (quitar espacios, convertir a minúsculas)
    const normalizedColor = status.color.trim().toLowerCase();

    // Convertir a clase de Tailwind si es un color primario
    const isPrimaryColor = [
        "primary",
        "secondary",
        "success",
        "warning",
        "danger",
        "default",
    ].includes(normalizedColor);

    // Si no es un color primario, generar estilos personalizados
    let customStyles: React.CSSProperties = {};

    if (!isPrimaryColor && variant === "solid") {
        customStyles = {
            backgroundColor: `${normalizedColor}`,
            color: getContrastColor(normalizedColor),
            ...style,
        };
    } else if (!isPrimaryColor && variant === "bordered") {
        customStyles = {
            borderColor: `${normalizedColor}`,
            color: `${normalizedColor}`,
            ...style,
        };
    } else if (!isPrimaryColor && variant === "flat") {
        const lightColor = lightenColor(normalizedColor, 0.85);

        customStyles = {
            backgroundColor: lightColor,
            color: normalizedColor,
            ...style,
        };
    } else if (!isPrimaryColor && variant === "dot") {
        customStyles = {
            color: normalizedColor,
            ...style,
        };
    }

    return (
        <Chip
            className={cn(className)}
            color={isPrimaryColor ? (normalizedColor as any) : undefined}
            size={size}
            startContent={showIcon && finalIcon ? finalIcon : undefined}
            style={customStyles}
            variant={variant}
        >
            {status.name}
        </Chip>
    );
}

// Función auxiliar para calcular color de contraste
function getContrastColor(hexColor: string): string {
    // Si el color no empieza con #, asumir que es un nombre de color
    if (!hexColor.startsWith("#")) {
        return "#ffffff"; // Por defecto, texto blanco
    }

    // Convertir hex a RGB
    const r = parseInt(hexColor.substring(1, 3), 16);
    const g = parseInt(hexColor.substring(3, 5), 16);
    const b = parseInt(hexColor.substring(5, 7), 16);

    // Calcular luminosidad
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Si es oscuro, usar texto blanco; si es claro, usar texto negro
    return luminance > 0.5 ? "#000000" : "#ffffff";
}

// Función auxiliar para aclarar un color
function lightenColor(hexColor: string, factor: number): string {
    // Si el color no empieza con #, devolver un color claro predeterminado
    if (!hexColor.startsWith("#")) {
        return "#f3f4f6"; // Gris claro por defecto
    }

    // Convertir hex a RGB
    const r = parseInt(hexColor.substring(1, 3), 16);
    const g = parseInt(hexColor.substring(3, 5), 16);
    const b = parseInt(hexColor.substring(5, 7), 16);

    // Aclarar cada componente
    const newR = Math.min(255, r + (255 - r) * factor);
    const newG = Math.min(255, g + (255 - g) * factor);
    const newB = Math.min(255, b + (255 - b) * factor);

    // Convertir de nuevo a hex
    return `#${Math.round(newR).toString(16).padStart(2, "0")}${Math.round(newG).toString(16).padStart(2, "0")}${Math.round(newB).toString(16).padStart(2, "0")}`;
}
