import { NextResponse } from "next/server";
import { cookies } from "next/headers";

import { auth } from "@/lib/auth-helpers";

export async function GET() {
    try {
        // Get session from auth
        const session = await auth();

        // Get all cookies
        const cookieStore = cookies();
        const allCookies = cookieStore.getAll();

        // Filter auth-related cookies
        const authCookies = allCookies.filter(
            (cookie) =>
                cookie.name.includes("auth") ||
                cookie.name.includes("next-auth") ||
                cookie.name.includes("session"),
        );

        return NextResponse.json({
            session,
            authCookies: authCookies.map((c) => ({
                name: c.name,
                value: c.value.substring(0, 20) + "...",
            })),
            timestamp: new Date().toISOString(),
        });
    } catch (error) {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            {
                error: "Error getting session",
                message:
                    error instanceof Error ? error.message : "Unknown error",
            },
            { status: 500 },
        );
    }
}
