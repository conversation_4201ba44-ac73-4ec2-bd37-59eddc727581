"use client";

import useS<PERSON> from "swr";
import { useState } from "react";
import { Role } from "@prisma/client";

import {
    getUsers,
    getUser,
    validateUserEmail,
    createUser,
    updateUser,
    deleteUser,
} from "@/features/auth/actions/users";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

export interface User {
    id: string;
    name: string;
    email: string;
    role: Role;
    createdAt: string;
    _count?: {
        notes: number;
        contractors: number;
    };
}

export interface UsersResponse {
    users: User[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

// Obtener helpers de revalidación para el usuario
const { revalidateData, useRevalidationListener } =
    createClientRevalidation("user");

// Tipado para usuarios
export interface UserBasic {
    id: string;
    name: string;
    email: string;
    role: string;
    createdAt?: string;
    image?: string;
}

// Hook simple para obtener todos los usuarios
export function useUsers(options = {}) {
    // REMOVED: console.log(" Inicializando hook useUsers");
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [users, setUsers] = useState<UserBasic[]>([]);

    // Usar SWR para obtener los usuarios
    const { data, mutate } = useSWR(
        "users",
        async () => {
            try {
                // REMOVED: console.log(" Ejecutando fetcher de usuarios");
                setIsLoading(true);
                // Recuperar 100 usuarios para asegurar que obtenemos todos
                const response = await getUsers({
                    limit: 100,
                    order: "asc",
                    sortBy: "name",
                });

                // REMOVED: console.log(" Respuesta completa:", response);

                // Verificar que la respuesta exista y tenga el formato esperado
                if (
                    response &&
                    response.success &&
                    response.data &&
                    Array.isArray((response.data as any).users)
                ) {
                    // Imprimir datos para depuración
                    // REMOVED: console.log(" Datos de usuarios recibidos:", response.data);

                    // Formatear los datos para el componente
                    const formattedUsers = (response.data as any).users.map(
                        (user: any) => ({
                            id: user.id,
                            name: user.name,
                            email: user.email || "",
                            role: user.role,
                            createdAt: user.createdAt,
                        }),
                    );

                    // REMOVED: console.log(" Usuarios formateados:", formattedUsers);
                    setUsers(formattedUsers);

                    return formattedUsers;
                } else {
                    // Imprimir la respuesta completa para depuración
                    // REMOVED: console.error(" Respuesta inválida o vacía:", response);

                    // Extraer el mensaje de error si existe o mostrar uno genérico
                    const errorMsg =
                        response && "error" in response
                            ? response.error
                            : "Error al cargar usuarios";

                    setError(
                        typeof errorMsg === "string"
                            ? errorMsg
                            : "Error desconocido",
                    );

                    // Devolver array vacío para evitar undefined
                    return [];
                }
            } catch (err) {
                // REMOVED: console.error(" Error en el fetcher:", err);
                const errorMessage =
                    err instanceof Error
                        ? err.message
                        : "Error desconocido al cargar usuarios";

                setError(errorMessage);
                // REMOVED: console.error("Error cargando usuarios:", err);

                // Devolver array vacío para evitar undefined
                return [];
            } finally {
                setIsLoading(false);
            }
        },
        {
            revalidateOnFocus: false,
            dedupingInterval: 5000, // Reducir a 5 segundos para pruebas
            // Si hay un error, revalidar automáticamente después de 5 segundos
            errorRetryInterval: 5000,
        },
    );

    const refreshUsers = async () => {
        // REMOVED: console.log(" Refrescando datos de usuarios...");
        setIsLoading(true);
        try {
            await mutate();
        } catch (err) {
            // REMOVED: console.error("Error al refrescar usuarios:", err);
        } finally {
            setIsLoading(false);
        }
    };

    return {
        users: users,
        isLoading,
        error,
        refreshUsers,
    };
}

// Hook para obtener un usuario específico
export function useUser(userId: string) {
    const { data, error, isLoading, mutate } = useSWR(
        userId ? ["user", userId] : null,
        async () => {
            if (!userId) return null;

            const response = await getUser(userId);

            if (response?.success && response?.data) {
                return response.data;
            }

            throw new Error(response?.error || "Error al cargar usuario");
        },
        {
            revalidateOnFocus: false,
            dedupingInterval: 30000, // 30 segundos
        },
    );

    return {
        user: data,
        isLoading,
        error,
        refresh: mutate,
    };
}

/**
 * Hook para comprobar si un email de usuario es válido
 * @param email Email a validar
 * @param excludeId ID del usuario a excluir de la validación (para edición)
 * @returns Objeto con resultado de validación y estado
 */
export function useValidateUserEmail(email: string | null, excludeId?: string) {
    // Utilizar Server Action como fetcher
    const { data, error, isLoading } = useSWR(
        email && email.length >= 3
            ? ["validateUserEmail", email, excludeId]
            : null,
        async () =>
            email && email.length >= 3
                ? validateUserEmail(email, excludeId)
                : null,
        {
            revalidateOnFocus: false,
            dedupingInterval: 10000,
        },
    );

    return {
        isValid: data?.isValid || false,
        message: data?.message || "",
        isValidating: isLoading,
        error: error,
    };
}

/**
 * Hook para crear un nuevo usuario
 * @returns Función para crear un usuario y estado de la operación
 */
export function useCreateUser() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createUserFn = async (data: {
        name: string;
        email: string;
        role: string;
        password: string;
    }) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await createUser(data);

            // Si se creó correctamente, revalidar datos
            if (result?.success) {
                // Revalidar datos mediante la función genérica
                if ((result.data as any)?.id) {
                    await revalidateData((result.data as any).id);
                }
            } else {
                // Extraer el mensaje de error de forma segura
                let errorMessage = "Error al crear el usuario";

                if (
                    result &&
                    "error" in result &&
                    typeof result.error === "string"
                ) {
                    errorMessage = result.error;
                } else if (
                    result &&
                    "message" in result &&
                    typeof result.message === "string"
                ) {
                    errorMessage = result.message;
                }
                setError(errorMessage);
            }

            return result;
        } catch (e) {
            const errorMessage =
                e instanceof Error ? e.message : "Error al crear el usuario";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createUser: createUserFn,
        isCreating: isLoading,
        createError: error,
    };
}

/**
 * Hook para actualizar un usuario existente
 * @returns Función para actualizar un usuario y estado de la operación
 */
export function useUpdateUser() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateUserFn = async (
        id: string,
        data: {
            name?: string;
            email?: string;
            role?: string;
            password?: string;
        },
    ) => {
        setIsLoading(true);
        setError(null);

        try {
            const updatedData: {
                name?: string;
                email?: string;
                role?: string;
                password?: string;
            } = { ...data };

            const result = await updateUser(id, updatedData);

            if (result?.success) {
                // Revalidar datos mediante la función genérica
                await revalidateData(id);
            } else {
                // Extraer el mensaje de error de forma segura
                let errorMessage = "Error al actualizar el usuario";

                if (
                    result &&
                    "error" in result &&
                    typeof result.error === "string"
                ) {
                    errorMessage = result.error;
                } else if (
                    result &&
                    "message" in result &&
                    typeof result.message === "string"
                ) {
                    errorMessage = result.message;
                }
                setError(errorMessage);
            }

            return result;
        } catch (e) {
            const errorMessage =
                e instanceof Error
                    ? e.message
                    : "Error al actualizar el usuario";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        updateUser: updateUserFn,
        isUpdating: isLoading,
        updateError: error,
    };
}

/**
 * Hook para eliminar un usuario
 * @returns Función para eliminar un usuario y estado de la operación
 */
export function useDeleteUser() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteUserFn = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await deleteUser(id);

            // Si se eliminó correctamente, revalidar datos
            if (result?.success) {
                // Revalidar datos mediante la función genérica
                await revalidateData();
            } else {
                // Extraer el mensaje de error de forma segura
                let errorMessage = "Error al eliminar el usuario";

                if (
                    result &&
                    "error" in result &&
                    typeof result.error === "string"
                ) {
                    errorMessage = result.error;
                } else if (
                    result &&
                    "message" in result &&
                    typeof result.message === "string"
                ) {
                    errorMessage = result.message;
                }
                setError(errorMessage);
            }

            return result;
        } catch (e) {
            const errorMessage =
                e instanceof Error ? e.message : "Error al eliminar el usuario";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        deleteUser: deleteUserFn,
        isDeleting: isLoading,
        deleteError: error,
    };
}
