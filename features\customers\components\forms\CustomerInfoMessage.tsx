"use client";

import React from "react";
import {
    InformationCircleIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    XCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

type MessageType = "info" | "warning" | "success" | "error";

interface CustomerInfoMessageProps {
    type: MessageType;
    title: string;
    message: string;
    show?: boolean;
    className?: string;
}

const messageStyles: Record<
    MessageType,
    {
        bg: string;
        border: string;
        icon: React.ReactNode;
        iconColor: string;
        titleColor: string;
        textColor: string;
    }
> = {
    info: {
        bg: "bg-blue-50 dark:bg-blue-900/20",
        border: "border-blue-200 dark:border-blue-800/50",
        icon: <InformationCircleIcon className="w-5 h-5" />,
        iconColor: "text-blue-600 dark:text-blue-400",
        titleColor: "text-blue-800 dark:text-blue-300",
        textColor: "text-blue-700 dark:text-blue-400",
    },
    warning: {
        bg: "bg-amber-50 dark:bg-amber-900/20",
        border: "border-amber-200 dark:border-amber-800/50",
        icon: <ExclamationTriangleIcon className="w-5 h-5" />,
        iconColor: "text-amber-600 dark:text-amber-400",
        titleColor: "text-amber-800 dark:text-amber-300",
        textColor: "text-amber-700 dark:text-amber-400",
    },
    success: {
        bg: "bg-green-50 dark:bg-green-900/20",
        border: "border-green-200 dark:border-green-800/50",
        icon: <CheckCircleIcon className="w-5 h-5" />,
        iconColor: "text-green-600 dark:text-green-400",
        titleColor: "text-green-800 dark:text-green-300",
        textColor: "text-green-700 dark:text-green-400",
    },
    error: {
        bg: "bg-red-50 dark:bg-red-900/20",
        border: "border-red-200 dark:border-red-800/50",
        icon: <XCircleIcon className="w-5 h-5" />,
        iconColor: "text-red-600 dark:text-red-400",
        titleColor: "text-red-800 dark:text-red-300",
        textColor: "text-red-700 dark:text-red-400",
    },
};

export function CustomerInfoMessage({
    type,
    title,
    message,
    show = true,
    className = "",
}: CustomerInfoMessageProps) {
    const styles = messageStyles[type];

    return (
        <AnimatePresence>
            {show && (
                <motion.div
                    animate={{ opacity: 1, height: "auto", marginTop: 16 }}
                    className={className}
                    exit={{ opacity: 0, height: 0, marginTop: 0 }}
                    initial={{ opacity: 0, height: 0, marginTop: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <div
                        className={`${styles.bg} p-4 rounded-lg border ${styles.border} flex gap-3`}
                    >
                        <div
                            className={`${styles.iconColor} flex-shrink-0 mt-0.5`}
                        >
                            {styles.icon}
                        </div>
                        <div className="flex-1">
                            <h4
                                className={`text-sm font-medium ${styles.titleColor}`}
                            >
                                {title}
                            </h4>
                            <p className={`text-xs ${styles.textColor} mt-1`}>
                                {message}
                            </p>
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
