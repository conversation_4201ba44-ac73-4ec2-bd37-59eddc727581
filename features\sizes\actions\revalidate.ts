"use server";

import { revalidatePath, revalidateTag } from "next/cache";

import { SIZE_TAGS, SIZE_PATHS } from "./constants";

/**
 * Función para revalidar todas las rutas y tags relacionados con tallas
 * @param id ID opcional de una talla específica
 */
export async function revalidateSizeCache(id?: string) {
    // // REMOVED: console.log(
    //     `[Server] 🔄 Revalidando caché de tallas${id ? ` (ID: ${id})` : ""}`,
    // );

    // 1. Revalidar rutas específicas
    revalidatePath(SIZE_PATHS.list);
    revalidatePath(SIZE_PATHS.new);
    revalidatePath(SIZE_PATHS.api);

    if (id) {
        revalidatePath(SIZE_PATHS.detail(id));
    }

    // 2. Revalidar tags para la caché del cliente (SWR)
    revalidateTag(SIZE_TAGS.all);
    revalidateTag(SIZE_TAGS.client); // Tag especial para SWR

    if (id) {
        revalidateTag(SIZE_TAGS.detail(id));
    }

    // 3. Revalidar tags relacionados (si las tallas se usan en otros módulos)
    revalidateTag("garments");
    revalidateTag("products");

    // console.log(`[Server] ✅ Caché de tallas revalidada correctamente`);
}
