import { z } from "zod";

// Esquema para validación de datos
export const sizeSchema = z.object({
    code: z
        .string()
        .min(1, "El código es requerido")
        .max(10, "El código no puede exceder 10 caracteres")
        .regex(
            /^[a-zA-Z0-9\-\/]+$/,
            "Solo se permiten letras, números, guiones y barras",
        )
        .transform((val) => val.toUpperCase()),
});

// Tipo inferido del esquema
export type SizeFormData = z.infer<typeof sizeSchema>;
