"use client";

import { useRef, useEffect } from "react";

/**
 * Component for debugging purposes that shows how many times a component renders
 */
export function RenderCounter({ componentName }: { componentName: string }) {
    const renderCount = useRef(0);

    useEffect(() => {
        renderCount.current += 1;
    });

    if (process.env.NODE_ENV === "production") {
        return null;
    }

    return (
        <div
            style={{
                position: "absolute",
                top: "3px",
                right: "3px",
                fontSize: "10px",
                padding: "2px 4px",
                backgroundColor: "#ffcccc",
                borderRadius: "3px",
                zIndex: 1000,
            }}
        >
            {componentName}: {renderCount.current} renders
        </div>
    );
}
