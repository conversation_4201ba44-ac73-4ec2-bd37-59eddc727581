"use client";

import { useRef, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@heroui/react";

import { RemissionDocument } from "@/features/remissions/components/RemissionDocument";
import { markRemissionPrinted } from "@/features/remissions/actions";

export default function RemissionPrintPage() {
    const { id } = useParams();
    const remissionId = Array.isArray(id) ? id[0] : id;
    const remissionRef = useRef<HTMLDivElement>(null);

    // Auto-print when the page loads
    useEffect(() => {
        // Short delay to ensure the document has fully rendered
        const timer = setTimeout(() => {
            window.print();

            // Mark the remission as printed
            if (remissionId) {
                markRemissionPrinted(remissionId).catch(console.error);
            }
        }, 1000);

        return () => clearTimeout(timer);
    }, [remissionId]);

    return (
        <div className="print-container">
            <div className="print-controls print-hidden p-4 bg-gray-100 mb-4 flex justify-between items-center">
                <h1 className="text-xl font-bold">Vista de impresión</h1>
                <div className="space-x-2">
                    <Button color="primary" onClick={() => window.print()}>
                        Imprimir
                    </Button>
                    <Button
                        color="secondary"
                        variant="flat"
                        onClick={() => window.history.back()}
                    >
                        Volver
                    </Button>
                </div>
            </div>

            <div className="mx-auto max-w-4xl bg-white shadow-lg print-shadow-none">
                <RemissionDocument
                    ref={remissionRef}
                    remissionId={remissionId}
                />
            </div>

            <style global jsx>{`
                @media print {
                    .print-hidden {
                        display: none !important;
                    }

                    .print-shadow-none {
                        box-shadow: none !important;
                    }

                    body {
                        background: white;
                    }

                    @page {
                        size: A4;
                        margin: 0;
                    }
                }
            `}</style>
        </div>
    );
}
