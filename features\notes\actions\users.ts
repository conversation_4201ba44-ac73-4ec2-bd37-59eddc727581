"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
} from "@/shared/utils/actions-utils";

interface SearchUsersParams {
    query: string;
    limit?: number;
}

interface UserSearchResult {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
}

/**
 * Server Action para buscar usuarios por nombre o email
 * Útil para autocompletado de menciones
 */
export async function searchUsers(
    params: SearchUsersParams,
): Promise<ActionResponse<UserSearchResult[]>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        const { query, limit = 10 } = params;

        // Validación básica
        if (!query || query.trim().length < 2) {
            return {
                success: false,
                error: "La búsqueda debe tener al menos 2 caracteres",
            };
        }

        // Buscar usuarios por nombre o email
        const users = await prisma.user.findMany({
            where: {
                OR: [
                    {
                        name: {
                            contains: query,
                            mode: "insensitive",
                        },
                    },
                    {
                        email: {
                            contains: query,
                            mode: "insensitive",
                        },
                    },
                ],
            },
            select: {
                id: true,
                name: true,
                email: true,
                image: true,
            },
            take: limit,
            orderBy: {
                name: "asc",
            },
        });

        return {
            success: true,
            data: users,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Server Action para obtener usuarios mencionados en una nota
 */
/**
 * Server Action para obtener todos los usuarios
 */
export async function getUsers(): Promise<ActionResponse<UserSearchResult[]>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        // Obtener todos los usuarios
        const users = await prisma.user.findMany({
            select: {
                id: true,
                name: true,
                email: true,
                image: true,
            },
            orderBy: {
                name: "asc",
            },
        });

        return {
            success: true,
            data: users,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

export async function getMentionedUsers(
    mentions: string[],
): Promise<ActionResponse<UserSearchResult[]>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        if (!mentions || mentions.length === 0) {
            return {
                success: true,
                data: [],
            };
        }

        // Buscar usuarios por nombre exacto
        const users = await prisma.user.findMany({
            where: {
                OR: mentions.map((mention) => ({
                    name: {
                        equals: mention,
                        mode: "insensitive",
                    },
                })),
            },
            select: {
                id: true,
                name: true,
                email: true,
                image: true,
            },
        });

        return {
            success: true,
            data: users,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
