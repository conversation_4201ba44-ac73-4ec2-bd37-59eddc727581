# Patrones de Desarrollo - Proyecto Lohari

Este documento documenta los patrones consistentes encontrados en el proyecto Lohari, basado en el análisis del código existente.

## 1. Patrones de Server Actions

### Estructura Básica
```typescript
"use server";

import { z } from "zod";
import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";
import { schema } from "../schemas/schema";

// Helper de revalidación específico por entidad
const { revalidateCache } = createServerRevalidation("entity");

export async function createEntity(data: EntityInput) {
  try {
    // 1. Validación con Zod
    const validatedData = schema.parse(data);
    
    // 2. Validaciones de negocio (unicidad, etc.)
    const validation = await validateEntity(validatedData);
    if (!validation.success) {
      return { success: false, error: validation.error };
    }
    
    // 3. Operación de base de datos
    const entity = await db.entity.create({
      data: validatedData
    });
    
    // 4. Revalidación de caché
    revalidateCache(entity.id);
    
    return { success: true, data: entity };
  } catch (error) {
    // 5. Manejo de errores
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors[0]?.message || "Datos inválidos"
      };
    }
    
    return handleDbError(
      () => { throw error; }, 
      "Error al crear entidad"
    );
  }
}
```

### Patrones Observados

1. **Directiva "use server"**: Todas las server actions inician con esta directiva
2. **Validación en capas**: Primero Zod, luego validaciones de negocio
3. **Respuesta consistente**: Siempre retornan `{ success: boolean, data?: T, error?: string }`
4. **Revalidación automática**: Uso de `revalidateCache` después de mutaciones
5. **Manejo de errores diferenciado**: Errores de Zod vs errores de DB

## 2. Manejo de Errores

### Función handleDbError
```typescript
export async function handleDbError<T>(
  fnOrError: (() => Promise<T>) | unknown,
  errorMessage: string = "Error en la operación de base de datos",
  maxRetries: number = 2,
): Promise<DbOperationResult<T>>
```

### Características
- Manejo unificado de errores de Prisma
- Mensajes de error amigables al usuario
- Reintentos automáticos para errores transitorios
- Logging condicional basado en NODE_ENV
- Formateo de errores específicos por código Prisma

### Códigos de Error Comunes
```typescript
export const PrismaErrorCodes = {
  UNIQUE_CONSTRAINT: "P2002",
  FOREIGN_KEY_CONSTRAINT: "P2003",
  RECORD_NOT_FOUND: "P2025",
  CONNECTION_ERROR: "P1017",
  // ...más códigos
};
```

## 3. Validación con Zod

### Estructura de Schemas
```typescript
import { z } from "zod";

export const entitySchema = z.object({
  name: z
    .string()
    .min(2, "El nombre debe tener al menos 2 caracteres")
    .max(50, "El nombre no puede exceder los 50 caracteres"),
  email: z
    .string()
    .email("Email inválido")
    .optional(),
  // Campos con transformaciones
  hexCode: z
    .string()
    .regex(/^#/, "El código HEX debe comenzar con #")
    .optional(),
});

// Inferir tipos desde el schema
export type EntityInput = z.infer<typeof entitySchema>;
```

### Patrones de Validación
1. **Mensajes en español**: Todos los mensajes de error están en español
2. **Validaciones específicas**: Regex para formatos específicos (hex, etc.)
3. **Campos opcionales**: Uso de `.optional()` para campos no requeridos
4. **Inferencia de tipos**: Uso de `z.infer<>` para tipos TypeScript

## 4. Componentes React

### Estructura de Componente
```typescript
"use client";

import { useState, useCallback } from "react";
import { Card, CardBody, CardHeader } from "@heroui/react";
import { motion } from "framer-motion";
import { useCustomHook } from "../hooks/useCustomHook";

interface ComponentProps {
  data: DataType;
  className?: string;
  onAction?: (id: string) => void;
}

export function Component({ 
  data, 
  className = "", 
  onAction 
}: ComponentProps) {
  const [state, setState] = useState(false);
  const { items, isLoading } = useCustomHook();
  
  const handleAction = useCallback(() => {
    // Lógica del handler
  }, [dependency]);
  
  return (
    <Card className={className}>
      {/* Contenido */}
    </Card>
  );
}
```

### Patrones Observados
1. **Directiva "use client"**: Para componentes interactivos
2. **HeroUI como librería UI principal**: Uso consistente de componentes HeroUI
3. **Props tipadas con interfaces**: No usar `type` para props
4. **Valores por defecto para props opcionales**: `className = ""`
5. **Hooks optimizados**: `useCallback` para handlers, `useMemo` para cálculos

## 5. Hooks Personalizados

### Estructura con SWR
```typescript
"use client";

import useSWR from "swr";
import { useCallback } from "react";
import { serverAction } from "../actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

const { revalidateData } = createClientRevalidation("entity");

// Configuración optimizada de SWR
const DEFAULT_SWR_CONFIG = {
  revalidateIfStale: false,
  revalidateOnMount: true,
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  errorRetryCount: 2,
  dedupingInterval: 5000,
  keepPreviousData: true,
};

export function useEntity(options = {}) {
  const { data, error, isLoading, mutate } = useSWR(
    ["entity", JSON.stringify(options)],
    async () => serverAction(options),
    DEFAULT_SWR_CONFIG
  );
  
  const createEntity = useCallback(async (newData) => {
    // Actualización optimista
    await mutate(
      async (currentData) => {
        const result = await serverAction(newData);
        if (result.success) {
          await revalidateData(result.data.id);
        }
        return result;
      },
      { 
        optimisticData: tempData,
        rollbackOnError: true 
      }
    );
  }, [mutate]);
  
  return {
    data: data?.data || [],
    isLoading,
    error: data?.error || error,
    createEntity,
    mutate
  };
}
```

### Patrones de Hooks
1. **SWR para fetching**: Uso consistente de SWR para datos
2. **Claves de caché estructuradas**: Arrays con tipo y opciones serializadas
3. **Configuración optimizada**: Desactivar revalidaciones automáticas innecesarias
4. **Actualizaciones optimistas**: Para mejor UX
5. **Funciones de acción encapsuladas**: `create`, `update`, `delete` dentro del hook

## 6. Tipos TypeScript

### Estructura de Tipos
```typescript
// Tipos base para entidades
export interface Entity {
  id: string;
  name: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
}

// Tipos para relaciones
export interface EntityWithRelations extends Entity {
  customer?: {
    id: string;
    name: string;
  };
  status?: {
    id: string;
    name: string;
    color: string;
    iconName?: string;
  };
}

// Tipos para queries
export interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  search?: string;
  statusId?: string;
}

// Tipos para respuestas
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    total: number;
    currentPage: number;
    lastPage: number;
  };
}

// Tipos para formularios (input)
export interface EntityCreateData {
  name: string;
  email?: string | null;
  metadata?: Record<string, unknown>;
}
```

### Patrones de Tipos
1. **Interfaces sobre types**: Preferencia por `interface` para objetos
2. **Campos opcionales con `?`**: Para propiedades que pueden no existir
3. **Union types para flexibilidad**: `string | Date` para fechas
4. **Tipos genéricos para respuestas**: `ApiResponse<T>`
5. **Separación de tipos**: Input vs Output vs Query

## 7. Patrones de Prisma

### Consultas Optimizadas
```typescript
// Consulta con relaciones selectivas
const entity = await db.entity.findUnique({
  where: { id },
  select: {
    id: true,
    name: true,
    // Relaciones específicas
    customer: {
      select: {
        id: true,
        name: true,
      }
    },
    // Conteos agregados
    _count: {
      select: {
        items: true,
        notes: true,
      }
    }
  }
});

// Consultas con paginación
const entities = await db.entity.findMany({
  where: { 
    deletedAt: null,
    ...(search && {
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } }
      ]
    })
  },
  orderBy: { [sortBy]: sortDirection },
  skip: (page - 1) * limit,
  take: limit,
  include: {
    _count: true
  }
});
```

### Transacciones
```typescript
// Transacción para operaciones complejas
const result = await db.$transaction(async (tx) => {
  // 1. Crear entidad principal
  const entity = await tx.entity.create({ data });
  
  // 2. Crear registros relacionados
  await tx.relatedEntity.createMany({
    data: items.map(item => ({
      entityId: entity.id,
      ...item
    }))
  });
  
  // 3. Actualizar contadores
  await tx.counter.update({
    where: { type: 'entity' },
    data: { count: { increment: 1 } }
  });
  
  return entity;
});
```

### Patrones de Base de Datos
1. **Soft deletes**: Campo `deletedAt` para eliminación lógica
2. **Auditoría**: Campos `createdAt`, `updatedAt`, `createdBy`, `updatedBy`
3. **Select específicos**: Evitar `include: true` genérico
4. **Búsquedas case-insensitive**: `mode: 'insensitive'`
5. **Conteos con `_count`**: Para obtener totales sin cargar datos

## 8. Estructura de Carpetas por Feature

```
features/
└── entity/
    ├── actions/          # Server actions
    │   ├── create.ts
    │   ├── update.ts
    │   ├── delete.ts
    │   ├── query.ts
    │   ├── validate.ts
    │   └── index.ts
    ├── components/       # Componentes React
    │   ├── EntityList.tsx
    │   ├── EntityForm.tsx
    │   └── index.ts
    ├── hooks/           # Hooks personalizados
    │   ├── useEntity.ts
    │   └── index.ts
    ├── schemas/         # Validación Zod
    │   └── schema.ts
    ├── types/          # Tipos TypeScript
    │   └── index.ts
    ├── utils/          # Utilidades
    │   └── helpers.ts
    └── index.ts        # Export público
```

## 9. Patrones de Rendimiento

1. **Deduplicación de requests**: `dedupingInterval` en SWR
2. **Datos previos mientras carga**: `keepPreviousData: true`
3. **Prefetch de siguiente página**: En hooks de listados
4. **Caché específico por vista**: Configuraciones diferentes para dashboard vs calendario
5. **Revalidación selectiva**: Solo revalidar datos afectados

## 10. Mejores Prácticas Observadas

1. **Separación de concerns**: Lógica de negocio en actions, UI en components
2. **Reutilización mediante hooks**: Encapsular lógica compleja
3. **Validación en múltiples capas**: Cliente (forms) y servidor (actions)
4. **Errores amigables**: Mensajes claros en español para el usuario

## 11. Patrones de UI/UX (Actualizados 2025)

### Componentes de Formulario Reutilizables

#### FormHeader Pattern
```tsx
// features/[module]/components/forms/[Module]FormHeader.tsx
export function ColorFormHeader({ 
  title, 
  description, 
  icon 
}: FormHeaderProps) {
  return (
    <div className="flex items-center gap-3 mb-6">
      <div className="p-2 bg-primary/10 rounded-lg">
        {icon}
      </div>
      <div>
        <h1 className="text-2xl font-bold">{title}</h1>
        {description && (
          <p className="text-default-500">{description}</p>
        )}
      </div>
    </div>
  );
}
```

#### FormProgress Pattern
```tsx
// features/[module]/components/forms/[Module]FormProgress.tsx
export function ColorFormProgress({ 
  currentStep, 
  totalSteps, 
  steps 
}: FormProgressProps) {
  return (
    <div className="mb-8">
      <Progress 
        value={(currentStep / totalSteps) * 100}
        className="mb-2"
      />
      <div className="flex justify-between text-sm">
        {steps?.map((step, index) => (
          <span 
            key={index}
            className={step.completed ? "text-success" : "text-default-400"}
          >
            {step.label}
          </span>
        ))}
      </div>
    </div>
  );
}
```

### Patrón de Modales de Confirmación

#### Reemplazo de Alerts Nativos
```tsx
// ❌ Evitar
if (!confirm("¿Estás seguro?")) return;

// ✅ Preferir
const { isOpen, onOpen, onClose } = useDisclosure();

// En el JSX
<DeleteColorModal
  isOpen={isOpen}
  onClose={onClose}
  onConfirm={handleDelete}
  itemName={item.name}
/>
```

#### Estructura de Modal de Eliminación
```tsx
export function DeleteColorModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  itemName 
}: DeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
      onClose();
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      classNames={{
        base: "bg-white dark:bg-gray-900",
        // ... estilos consistentes
      }}
    >
      <ModalContent>
        <ModalHeader>Confirmar eliminación</ModalHeader>
        <ModalBody>
          ¿Estás seguro de eliminar "{itemName}"?
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onPress={onClose}>
            Cancelar
          </Button>
          <Button 
            color="danger" 
            onPress={handleConfirm}
            isLoading={isDeleting}
          >
            Eliminar
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
```

### Simplificación de Formularios

#### Layout de Una Columna
```tsx
// ❌ Evitar - múltiples columnas
<div className="grid grid-cols-2 gap-6">
  <Input label="Campo 1" />
  <Input label="Campo 2" />
  <Input label="Campo 3" />
  <Input label="Campo 4" />
</div>

// ✅ Preferir - una columna
<div className="space-y-6">
  <Input label="Campo 1" />
  <Input label="Campo 2" />
  <Input label="Campo 3" />
  <Input label="Campo 4" />
</div>
```

#### Campos Esenciales vs Opcionales
```tsx
// Schema simplificado - solo campos esenciales
export const colorFormSchema = z.object({
  name: z.string().min(1, "Requerido"),
  hexCode: z.string().regex(/^#[0-9A-F]{6}$/i),
  active: z.boolean().default(true)
});

// Campos removidos por ser poco usados:
// - accessibility[]
// - contexts[]
// - pantoneCode
// - cmykValues
```

### Patrón de Página de Formulario

```tsx
export default function ColorFormPage() {
  const [hasChanges, setHasChanges] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const handleCancel = () => {
    if (hasChanges) {
      onOpen(); // Abre modal de confirmación
    } else {
      router.push('/dashboard/colors');
    }
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      <Card>
        <CardHeader>
          <ColorFormHeader 
            title="Nuevo Color"
            description="Registra un nuevo color"
            icon={<Palette />}
          />
        </CardHeader>
        
        <CardBody>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Campos del formulario */}
          </form>
        </CardBody>
        
        <CardFooter>
          <Button variant="flat" onPress={handleCancel}>
            Cancelar
          </Button>
          <Button type="submit" color="primary">
            Guardar
          </Button>
        </CardFooter>
      </Card>
      
      {/* Modal de confirmación */}
      <Modal isOpen={isOpen} onClose={onClose}>
        {/* ... */}
      </Modal>
    </motion.div>
  );
}
```
5. **TypeScript estricto**: Tipos para todo, evitar `any`
6. **Optimización progresiva**: Empezar simple, optimizar según necesidad