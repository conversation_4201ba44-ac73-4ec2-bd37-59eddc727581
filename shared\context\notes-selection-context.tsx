"use client";

import React, {
    createContext,
    useContext,
    useState,
    useCallback,
    ReactNode,
} from "react";

// Definir la interfaz del contexto
interface NotesSelectionContextType {
    selectedNotes: string[];
    isSelectionMode: boolean;
    startSelectionMode: () => void;
    exitSelectionMode: () => void;
    toggleSelection: (noteId: string) => void;
    isSelected: (noteId: string) => boolean;
    selectAll: (noteIds: string[]) => void;
    clearSelection: () => void;
}

// Crear el contexto
const NotesSelectionContext = createContext<
    NotesSelectionContextType | undefined
>(undefined);

// Props para el provider
interface NotesSelectionProviderProps {
    children: ReactNode;
}

// Provider del contexto
export function NotesSelectionProvider({
    children,
}: NotesSelectionProviderProps) {
    const [selectedNotes, setSelectedNotes] = useState<string[]>([]);
    const [isSelectionMode, setIsSelectionMode] = useState(false);

    // Iniciar modo selección
    const startSelectionMode = useCallback(() => {
        setIsSelectionMode(true);
    }, []);

    // Salir del modo selección
    const exitSelectionMode = useCallback(() => {
        setIsSelectionMode(false);
        setSelectedNotes([]);
    }, []);

    // Alternar selección de una nota
    const toggleSelection = useCallback((noteId: string) => {
        setSelectedNotes((prev) =>
            prev.includes(noteId)
                ? prev.filter((id) => id !== noteId)
                : [...prev, noteId],
        );
    }, []);

    // Verificar si una nota está seleccionada
    const isSelected = useCallback(
        (noteId: string) => {
            return selectedNotes.includes(noteId);
        },
        [selectedNotes],
    );

    // Seleccionar todas las notas
    const selectAll = useCallback((noteIds: string[]) => {
        setSelectedNotes(noteIds);
    }, []);

    // Limpiar selección
    const clearSelection = useCallback(() => {
        setSelectedNotes([]);
    }, []);

    // Valor del contexto
    const value = {
        selectedNotes,
        isSelectionMode,
        startSelectionMode,
        exitSelectionMode,
        toggleSelection,
        isSelected,
        selectAll,
        clearSelection,
    };

    return (
        <NotesSelectionContext.Provider value={value}>
            {children}
        </NotesSelectionContext.Provider>
    );
}

// Hook para usar el contexto
export function useNotesSelection(): NotesSelectionContextType {
    const context = useContext(NotesSelectionContext);

    if (context === undefined) {
        throw new Error(
            "useNotesSelection debe usarse dentro de un NotesSelectionProvider",
        );
    }

    return context;
}
