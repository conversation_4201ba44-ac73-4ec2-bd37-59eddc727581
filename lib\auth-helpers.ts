import type { Session } from "next-auth";

import { getServerSession } from "next-auth/next";
import { redirect } from "next/navigation";

import { authConfig } from "@/auth";

/**
 * Get the current session
 * This replaces the auth() function from NextAuth v5
 */
export async function auth(): Promise<Session | null> {
    return await getServerSession(authConfig);
}

/**
 * Sign in function
 * This replaces the signIn() function from NextAuth v5
 */
export async function signIn(
    provider?: string,
    options?: { redirectTo?: string; redirect?: boolean },
    authorizationParams?: any,
) {
    const callbackUrl = options?.redirectTo || "/dashboard";
    const searchParams = new URLSearchParams({
        callbackUrl,
    });

    if (authorizationParams) {
        Object.entries(authorizationParams).forEach(([key, value]) => {
            searchParams.append(key, value as string);
        });
    }

    const signInUrl = `/api/auth/signin${provider ? `/${provider}` : ""}?${searchParams}`;

    if (options?.redirect !== false) {
        redirect(signInUrl);
    }

    return signInUrl;
}

/**
 * Sign out function
 * This replaces the signOut() function from NextAuth v5
 */
export async function signOut(options?: {
    redirectTo?: string;
    redirect?: boolean;
}) {
    const callbackUrl = options?.redirectTo || "/login";
    const signOutUrl = `/api/auth/signout?callbackUrl=${encodeURIComponent(callbackUrl)}`;

    if (options?.redirect !== false) {
        redirect(signOutUrl);
    }

    return signOutUrl;
}
