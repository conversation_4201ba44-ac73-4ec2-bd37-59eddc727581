"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";

import { checkServerSession } from "@/lib/auth/debug-session";
import { LoginForm } from "@/components/auth/forms";

export default function LoginContent() {
    const searchParams = useSearchParams();
    const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";
    const { data: session, status } = useSession();

    useEffect(() => {
        // Check server session on mount
        checkServerSession();
    }, []);

    useEffect(() => {
        // If authenticated, redirect to callback URL
        if (status === "authenticated" && session) {
            // User is authenticated, redirecting...
            // Add a small delay to prevent redirect loops
            const timer = setTimeout(() => {
                // Use replace to prevent back button issues
                window.location.replace(callbackUrl);
            }, 1000);

            return () => clearTimeout(timer);
        }
    }, [status, session, callbackUrl]);

    // Show loading state while checking authentication
    if (status === "loading") {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600" />
            </div>
        );
    }

    // Only show login form if not authenticated
    if (status === "unauthenticated") {
        return (
            <LoginForm
                onSuccess={() => {
                    // Login successful, redirecting...
                    // Force a hard redirect to ensure middleware runs
                    window.location.href = callbackUrl;
                }}
            />
        );
    }

    // If authenticated, show loading while redirecting
    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4" />
                <p className="text-neutral-600 mb-4">Redirigiendo...</p>
                <p className="text-sm text-neutral-500 mb-4">
                    Si no eres redirigido automáticamente:
                </p>
                <button
                    className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                    onClick={() => {
                        // Manual redirect
                        window.location.href = callbackUrl;
                    }}
                >
                    Continuar al Dashboard
                </button>
            </div>
        </div>
    );
}
