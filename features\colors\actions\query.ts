"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

// Obtener helpers de revalidación para la entidad "color"
export const { revalidateCache } = createServerRevalidation("color");

/**
 * Obtiene todos los colores con opciones de filtrado y paginación
 */
export async function getColors(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) {
    const {
        search,
        orderBy = "name",
        order = "asc",
        page = 1,
        perPage = 50,
    } = options;

    return await handleDbError(async () => {
        // Construir filtros
        const where: Prisma.ColorWhereInput = search
            ? {
                  name: {
                      contains: search,
                      mode: "insensitive" as Prisma.QueryMode,
                  },
              }
            : {};

        // Obtener datos con paginación
        const colors = await db.color.findMany({
            where,
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
            include: {
                _count: {
                    select: { garments: true },
                },
            },
        });

        // Obtener total para paginación
        const total = await db.color.count({ where });

        return {
            colors,
            pagination: {
                total,
                currentPage: page,
                lastPage: Math.ceil(total / perPage),
            },
        };
    }, "Error al obtener colores");
}

/**
 * Obtiene un color por ID
 */
export async function getColor(id: string | null | undefined) {
    if (!id) return { success: false, error: "ID no válido" };

    return await handleDbError(async () => {
        const color = await db.color.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { garments: true },
                },
            },
        });

        if (!color) {
            throw new Error("Color no encontrado");
        }

        return color;
    }, "Error al obtener color");
}
