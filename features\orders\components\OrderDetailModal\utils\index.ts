/**
 * Barrel export for all OrderDetailModal utilities
 */

// Theme helpers
export {
    useThemeSafe,
    getThemeColors,
    isLightColor,
    getContrastingTextColor,
} from "./theme-helpers";

// Order data helpers
export {
    // Type guards
    isValidOrder,
    hasValidParts,
    hasValidAssignments,
    hasValidGarments,

    // Safe getters
    safeGet,
    getOrderDisplayName,
    getCustomerName,
    getCustomerAvatar,
    formatDate,

    // Calculations
    calculateTotalParts,
    calculateCompletedParts,
    calculateTotalAssignments,
    calculateProgressPercentage,

    // Status helpers
    getStatusColor,
    getStatusName,
    isOrderOverdue,
} from "./order-helpers";
