import { prisma } from "@/shared/lib/prisma";

import { IntegrityManager, checkVersion } from "../IntegrityManager";

// Mock Prisma
jest.mock("@/shared/lib/prisma", () => ({
    prisma: {
        operationLog: {
            create: jest.fn(),
            update: jest.fn(),
            findMany: jest.fn(),
            count: jest.fn(),
        },
        assignment: {
            findUnique: jest.fn(),
            updateMany: jest.fn(),
        },
        remission: {
            updateMany: jest.fn(),
            deleteMany: jest.fn(),
        },
    },
}));

describe("IntegrityManager", () => {
    let integrityManager: IntegrityManager;

    beforeEach(() => {
        integrityManager = new IntegrityManager();
        jest.clearAllMocks();
    });

    describe("executeWithIntegrity", () => {
        it("should create operation log and mark as completed on success", async () => {
            const mockLog = { id: "log-1", status: "IN_PROGRESS" };
            const mockResult = { id: "result-1", data: "test" };

            (prisma.operationLog.create as jest.Mock).mockResolvedValue(
                mockLog,
            );
            (prisma.operationLog.update as jest.Mock).mockResolvedValue({});

            const operation = jest.fn().mockResolvedValue(mockResult);

            const result = await integrityManager.executeWithIntegrity(
                { type: "TEST_OPERATION", userId: "user-1" },
                operation,
            );

            expect(result).toEqual(mockResult);
            expect(prisma.operationLog.create).toHaveBeenCalledWith({
                data: {
                    operationType: "TEST_OPERATION",
                    userId: "user-1",
                    status: "IN_PROGRESS",
                    metadata: {},
                    entityIds: [],
                },
            });
            expect(prisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "log-1" },
                data: {
                    status: "COMPLETED",
                    completedAt: expect.any(Date),
                    entityIds: ["result-1"],
                },
            });
        });

        it("should execute compensation on failure", async () => {
            const mockLog = { id: "log-1", status: "IN_PROGRESS" };
            const error = new Error("Operation failed");

            (prisma.operationLog.create as jest.Mock).mockResolvedValue(
                mockLog,
            );
            (prisma.operationLog.update as jest.Mock).mockResolvedValue({});

            const operation = jest.fn().mockRejectedValue(error);
            const compensate = jest.fn().mockResolvedValue(undefined);

            await expect(
                integrityManager.executeWithIntegrity(
                    { type: "TEST_OPERATION", userId: "user-1" },
                    operation,
                    compensate,
                ),
            ).rejects.toThrow("Operation failed");

            expect(compensate).toHaveBeenCalledWith(mockLog);
            expect(prisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "log-1" },
                data: {
                    status: "COMPENSATED",
                    compensatedAt: expect.any(Date),
                },
            });
        });
    });

    describe("checkVersion", () => {
        it("should pass when versions match", async () => {
            const mockModel = {
                findUnique: jest.fn().mockResolvedValue({ version: 2 }),
            };

            await expect(
                checkVersion(mockModel, "entity-1", 2),
            ).resolves.not.toThrow();
        });

        it("should throw OptimisticLockError when versions mismatch", async () => {
            const mockModel = {
                findUnique: jest.fn().mockResolvedValue({ version: 3 }),
            };

            await expect(
                checkVersion(mockModel, "entity-1", 2),
            ).rejects.toThrow("Version mismatch: expected 2, got 3");
        });
    });

    describe("findStuckOperations", () => {
        it("should find operations stuck in IN_PROGRESS", async () => {
            const stuckOps = [
                { id: "op-1", status: "IN_PROGRESS" },
                { id: "op-2", status: "IN_PROGRESS" },
            ];

            (prisma.operationLog.findMany as jest.Mock).mockResolvedValue(
                stuckOps,
            );

            const result = await integrityManager.findStuckOperations();

            expect(result).toEqual(stuckOps);
            expect(prisma.operationLog.findMany).toHaveBeenCalledWith({
                where: {
                    status: "IN_PROGRESS",
                    startedAt: {
                        lt: expect.any(Date),
                    },
                },
                orderBy: {
                    startedAt: "asc",
                },
            });
        });
    });
});
