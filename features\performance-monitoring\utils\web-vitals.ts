/**
 * Web Vitals Performance Monitoring
 * Tracks Core Web Vitals metrics for the Lohari application
 */

import { onCLS, onFCP, onINP, onLCP, onTTFB, Metric } from "web-vitals";

export interface PerformanceMetrics {
    CLS?: number; // Cumulative Layout Shift
    FCP?: number; // First Contentful Paint
    INP?: number; // Interaction to Next Paint
    LCP?: number; // Largest Contentful Paint
    TTFB?: number; // Time to First Byte
    timestamp: number;
    url: string;
    userAgent: string;
}

// Store for baseline metrics
const baselineMetrics: PerformanceMetrics = {
    timestamp: Date.now(),
    url: "",
    userAgent: typeof navigator !== "undefined" ? navigator.userAgent : "",
};

/**
 * Reports performance metrics to console and stores baseline
 */
function reportMetric(metric: Metric) {
    const value = Math.round(metric.value);

    // Store in baseline metrics
    (baselineMetrics as any)[metric.name] = value;
    baselineMetrics.url = window.location.href;

    // Log to console in development
    if (process.env.NODE_ENV === "development") {
        console.log(`[Performance] ${metric.name}:`, value, "ms");

        // Color-coded warnings for poor performance
        if (metric.name === "LCP" && value > 2500) {
            console.warn(`⚠️ LCP is above target (${value}ms > 2500ms)`);
        }
        if (metric.name === "INP" && value > 200) {
            console.warn(`⚠️ INP is above target (${value}ms > 200ms)`);
        }
        if (metric.name === "CLS" && value > 0.1) {
            console.warn(`⚠️ CLS is above target (${value} > 0.1)`);
        }
    }
}

/**
 * Initialize Web Vitals tracking
 */
export function initWebVitals() {
    onCLS(reportMetric);
    onFCP(reportMetric);
    onINP(reportMetric);
    onLCP(reportMetric);
    onTTFB(reportMetric);
}

/**
 * Get current baseline metrics
 */
export function getBaselineMetrics(): PerformanceMetrics {
    return { ...baselineMetrics };
}

/**
 * Track custom performance marks
 */
export function measurePerformance(markName: string) {
    const startMark = `${markName}-start`;
    const endMark = `${markName}-end`;
    const measureName = `${markName}-duration`;

    return {
        start: () => {
            if (typeof window !== "undefined" && window.performance) {
                window.performance.mark(startMark);
            }
        },
        end: () => {
            if (typeof window !== "undefined" && window.performance) {
                window.performance.mark(endMark);
                window.performance.measure(measureName, startMark, endMark);

                const measures =
                    window.performance.getEntriesByName(measureName);
                const duration = measures[measures.length - 1]?.duration;

                if (process.env.NODE_ENV === "development" && duration) {
                    console.log(
                        `[Performance] ${markName}:`,
                        Math.round(duration),
                        "ms",
                    );
                }

                // Clean up marks
                window.performance.clearMarks(startMark);
                window.performance.clearMarks(endMark);
                window.performance.clearMeasures(measureName);

                return duration;
            }
        },
    };
}

/**
 * Performance observer for resource loading
 */
export function observeResourceTiming() {
    if (typeof window === "undefined" || !window.PerformanceObserver) {
        return;
    }

    const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
            if (entry.entryType === "resource") {
                const resourceEntry = entry as PerformanceResourceTiming;

                if (resourceEntry.duration > 1000) {
                    console.warn(
                        `⚠️ Slow resource load: ${resourceEntry.name} (${Math.round(resourceEntry.duration)}ms)`,
                    );
                }
            }
        }
    });

    observer.observe({ entryTypes: ["resource"] });
}
