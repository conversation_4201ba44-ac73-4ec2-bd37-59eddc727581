---
description: Visual Process Map para CREATIVE Mode con Think methodology
globs: creative-mode-map.mdc
alwaysApply: false
---
# 🎨 CREATIVE MODE - VISUAL PROCESS MAP

> Modo de diseño basado en <PERSON>'s Think methodology

## 📊 MAPA DE PROCESO THINK-BASED

```mermaid
graph TD
    Start["🎨 AUTO-FLOW CREATIVE"] --> Load["📂 Cargar Creative Flags<br>desde tasks.md"]
    
    Load --> Components["🧩 Componentes que<br>requieren diseño"]
    
    Components --> Loop{"🔄 Para cada<br>componente"}
    
    Loop --> Entry["🎨🎨🎨 ENTERING<br>CREATIVE PHASE"]
    
    Entry --> Problem["1️⃣ PROBLEM<br>Definir alcance<br>🧠 mentalmodel"]
    Problem --> Options["2️⃣ OPTIONS<br>Explorar alternativas<br>🧠 designpattern"]
    Options --> Analysis["3️⃣ ANALYSIS<br>Comparar opciones<br>🧠 structuredargumentation"]
    Analysis --> Decision["4️⃣ DECISION<br>Seleccionar approach<br>🧠 decisionframework"]
    Decision --> Guidelines["5️⃣ GUIDELINES<br>Documentar implementación<br>🧠 collaborativereasoning"]
    
    Guidelines --> Exit["🎨🎨🎨 EXITING<br>CREATIVE PHASE"]
    
    Exit --> SaveDoc["💾 Guardar<br>creative-[timestamp].md"]
    SaveDoc --> UpdateTasks["📝 Actualizar<br>tasks.md"]
    
    UpdateTasks --> More{"¿Más<br>componentes?"}
    More -->|Sí| Loop
    More -->|No| Consolidate["📋 Consolidar<br>Decisiones"]
    
    Consolidate --> Ready["✅ CREATIVE Complete<br>→ IMPLEMENT"]
    
    %% Clear Thought Tools
    Problem -.-> CT1["🧠 mentalmodel<br>Principios fundamentales"]
    Options -.-> CT2["🧠 designpattern<br>Patrones aplicables"]
    Analysis -.-> CT3["🧠 structuredargumentation<br>Pros vs Cons"]
    Decision -.-> CT4["🧠 decisionframework<br>Matriz de decisión"]
    Guidelines -.-> CT5["🧠 collaborativereasoning<br>Perspectivas múltiples"]
    
    style Start fill:#d971ff
    style Entry fill:#f5d9f0
    style Exit fill:#f5d9f0
    style Ready fill:#5fd94d
```

## 🧠 PROCESO THINK DETALLADO

### Fase 1: PROBLEM DEFINITION
```typescript
// Activar mentalmodel para principios fundamentales
const problemAnalysis = await analyzeWithTool('mentalmodel', {
    modelName: 'first_principles',
    problem: componentDescription,
    steps: [
        'Identificar requisitos fundamentales',
        'Eliminar suposiciones',
        'Definir constraints reales',
        'Establecer criterios de éxito'
    ]
});
```

### Fase 2: OPTIONS EXPLORATION
```typescript
// Activar designpattern para explorar patrones
const designOptions = await exploreWithTool('designpattern', {
    patternName: determinePatternType(component),
    context: componentRequirements,
    implementation: generateMultipleApproaches(2, 4),
    benefits: evaluateBenefits(),
    tradeoffs: identifyTradeoffs()
});
```

### Fase 3: ANALYSIS (Matriz Comparativa)
```markdown
| Criterio | Opción A | Opción B | Opción C |
|----------|----------|----------|----------|
| Performance | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| Mantenibilidad | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Escalabilidad | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Complejidad | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| Tiempo | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
```

### Fase 4: DECISION
```typescript
// Activar decisionframework para decisión estructurada
const decision = await makeDecisionWithTool('decisionframework', {
    analysisType: 'weighted-criteria',
    options: designOptions,
    criteria: evaluationCriteria,
    constraints: projectConstraints,
    recommendation: selectOptimalApproach()
});
```

### Fase 5: IMPLEMENTATION GUIDELINES
```markdown
## Guías de Implementación - [Componente]

### Approach Seleccionado: [Nombre]
[Descripción detallada del approach]

### Pasos de Implementación
1. **Setup inicial**
   ```typescript
   // Código ejemplo o pseudocódigo
   ```

2. **Implementación core**
   - Punto clave 1
   - Punto clave 2

3. **Integración**
   - Con componente X
   - Con sistema Y

### Consideraciones Especiales
- [Consideración 1]
- [Consideración 2]

### Métricas de Validación
- [ ] Performance: < Xms
- [ ] Tests: 100% coverage
- [ ] Accesibilidad: WCAG AA
```

## 📄 TEMPLATE CREATIVE DOCUMENT

```markdown
# 🎨 Creative Phase: [Component Name]
**Timestamp**: [ISO timestamp]
**Type**: [Architecture | Algorithm | UI/UX]

## 🎨🎨🎨 ENTERING CREATIVE PHASE

### 1️⃣ PROBLEM DEFINITION
[Clear problem statement and requirements]

### 2️⃣ OPTIONS EXPLORED
#### Option A: [Name]
- **Approach**: [Description]
- **Pros**: [List]
- **Cons**: [List]

#### Option B: [Name]
[Similar structure]

### 3️⃣ ANALYSIS MATRIX
[Comparison table]

### 4️⃣ DECISION
**Selected**: Option [X]
**Rationale**: [Detailed justification]

### 5️⃣ IMPLEMENTATION GUIDELINES
[Detailed implementation steps]

## 🎨🎨🎨 EXITING CREATIVE PHASE

**Next Steps**: Implementation in IMPLEMENT mode
**Estimated Time**: [X hours]
```

## 💾 MEMORY BANK UPDATES

```typescript
// Guardar decisión creativa
saveCreativeDecision({
    filename: `creative-${component}-${timestamp}.md`,
    component: componentName,
    selectedApproach: decision.recommendation,
    implementationGuide: guidelines,
    alternativesConsidered: options,
    decisionRationale: decision.rationale
});

// Actualizar tasks.md
updateTasksWithCreativeDecision({
    componentId: component.id,
    status: 'creative-complete',
    selectedApproach: decision.recommendation,
    readyForImplementation: true
});
```

## ✅ CHECKLIST DE VERIFICACIÓN

- [ ] Todos los componentes creative procesados
- [ ] Problema definido con mentalmodel
- [ ] Opciones exploradas con designpattern (2-4 opciones)
- [ ] Análisis completado con structuredargumentation
- [ ] Decisión tomada con decisionframework
- [ ] Guidelines creadas con collaborativereasoning
- [ ] Documentos creative-*.md guardados
- [ ] Tasks.md actualizado con decisiones
- [ ] Listo para IMPLEMENT mode

---

**CREATIVE Mode completo - Decisiones documentadas**
