"use client";

import { useState } from "react";
import { addToast } from "@heroui/react";

import { bulkDeleteNotes } from "@/features/notes/actions/bulk-delete";
import { bulkUpdateNoteStatus } from "@/features/notes/actions/bulk-update-status";
import { bulkUpdateNoteImportance } from "@/features/notes/actions/bulk-update-importance";

export const useNoteBulkActions = () => {
    const [isDeleting, setIsDeleting] = useState(false);
    const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
    const [isUpdatingImportance, setIsUpdatingImportance] = useState(false);

    /**
     * Elimina múltiples notas en una sola operación
     */
    const handleBulkDelete = async (noteIds: string[]) => {
        if (!noteIds.length) return;

        setIsDeleting(true);
        try {
            const result = await bulkDeleteNotes({ noteIds });

            if (result.success) {
                addToast({
                    title: "Notas eliminadas",
                    description: result.data
                        ? `Se eliminaron ${result.data.deletedCount} notas correctamente`
                        : "Notas eliminadas correctamente",
                    color: "success",
                });

                return true;
            } else {
                addToast({
                    title: "Error al eliminar notas",
                    description:
                        result.error || "No se pudieron eliminar las notas",
                    color: "danger",
                });

                return false;
            }
        } catch (error) {
            // REMOVED: console.error("Error en bulk delete:", error);
            addToast({
                title: "Error al eliminar notas",
                description: "Ocurrió un error inesperado",
                color: "danger",
            });

            return false;
        } finally {
            setIsDeleting(false);
        }
    };

    /**
     * Actualiza el estado de múltiples notas en una sola operación
     */
    const handleBulkUpdateStatus = async (
        noteIds: string[],
        statusId: string,
    ) => {
        if (!noteIds.length) return;

        setIsUpdatingStatus(true);
        try {
            const result = await bulkUpdateNoteStatus({ noteIds, statusId });

            if (result.success) {
                addToast({
                    title: "Estado actualizado",
                    description: result.data
                        ? `Se actualizó el estado de ${result.data.updatedCount} notas`
                        : "Estados actualizados correctamente",
                    color: "success",
                });

                return true;
            } else {
                addToast({
                    title: "Error al actualizar estado",
                    description:
                        result.error || "No se pudo actualizar el estado",
                    color: "danger",
                });

                return false;
            }
        } catch (error) {
            // REMOVED: console.error("Error en bulk update status:", error);
            addToast({
                title: "Error al actualizar estado",
                description: "Ocurrió un error inesperado",
                color: "danger",
            });

            return false;
        } finally {
            setIsUpdatingStatus(false);
        }
    };

    /**
     * Actualiza la importancia de múltiples notas en una sola operación
     */
    const handleBulkUpdateImportance = async (
        noteIds: string[],
        importanceId: string,
    ) => {
        if (!noteIds.length) return;

        setIsUpdatingImportance(true);
        try {
            const result = await bulkUpdateNoteImportance({
                noteIds,
                importanceId,
            });

            if (result.success) {
                addToast({
                    title: "Importancia actualizada",
                    description: result.data
                        ? `Se actualizó la importancia de ${result.data.updatedCount} notas`
                        : "Importancia actualizada correctamente",
                    color: "success",
                });

                return true;
            } else {
                addToast({
                    title: "Error al actualizar importancia",
                    description:
                        result.error || "No se pudo actualizar la importancia",
                    color: "danger",
                });

                return false;
            }
        } catch (error) {
            // REMOVED: console.error("Error en bulk update importance:", error);
            addToast({
                title: "Error al actualizar importancia",
                description: "Ocurrió un error inesperado",
                color: "danger",
            });

            return false;
        } finally {
            setIsUpdatingImportance(false);
        }
    };

    return {
        handleBulkDelete,
        handleBulkUpdateStatus,
        handleBulkUpdateImportance,
        isDeleting,
        isUpdatingStatus,
        isUpdatingImportance,
    };
};
