"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import {
    ArrowLeftIcon,
    ArrowRightIcon,
    CheckIcon,
    PrinterIcon,
    SparklesIcon,
    RocketLaunchIcon,
    UserCircleIcon,
    CubeIcon,
    ChartBarIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import {
    useWizard,
    WizardStep,
} from "@/app/dashboard/assignments/new/wizard-context";
import ContractorStep from "@/features/assignments/components/wizard/steps/ContractorStep";
import OrdersStep from "@/features/assignments/components/wizard/steps/OrdersStep";
import QuantitiesStep from "@/features/assignments/components/wizard/steps/QuantitiesStep";
import SummaryStep from "@/features/assignments/components/wizard/steps/SummaryStep";
import AssignmentRemission from "@/features/remissions/components/AssignmentRemission";
import { getOrderDetailsForAssignment } from "@/features/assignments/actions";

import { StepIndicator } from "./ui/StepIndicator";

interface AssignmentWizardProps {
    onComplete?: () => void;
}

export default function AssignmentWizard({
    onComplete,
}: AssignmentWizardProps) {
    const {
        state,
        nextStep: goToNextStep,
        prevStep: goToPreviousStep,
        canGoNext,
        canGoBack,
    } = useWizard();

    const currentStep = state.currentStep;
    const isSubmitting = state.isSubmitting;

    const [isSuccess, setIsSuccess] = useState(false);
    const [showRemission, setShowRemission] = useState(false);
    const [previousStep, setPreviousStep] = useState<WizardStep | null>(null);
    const [createdAssignments, setCreatedAssignments] = useState<any[]>([]);
    const [orderData, setOrderData] = useState<any>(null);

    // Al cambiar de paso, guardar el paso anterior para la animación
    const handleStepChange = (direction: "next" | "previous") => {
        setPreviousStep(currentStep);
        if (direction === "next") {
            goToNextStep();
        } else {
            goToPreviousStep();
        }
    };

    // Renderizar el paso actual
    const renderCurrentStep = () => {
        switch (currentStep) {
            case "contractor":
                return <ContractorStep />;
            case "orders":
                return <OrdersStep />;
            case "quantities":
                return <QuantitiesStep />;
            case "summary":
                return (
                    <SummaryStep
                        onSuccess={(data?: any) => {
                            if (data?.assignments) {
                                setCreatedAssignments(data.assignments);
                            }
                            setIsSuccess(true);
                            if (onComplete) onComplete();
                        }}
                    />
                );
            default:
                return null;
        }
    };

    // Textos según el paso actual
    const getStepTitle = (step: WizardStep): string => {
        switch (step) {
            case "contractor":
                return "Seleccionar Contratista";
            case "orders":
                return "Seleccionar Órdenes";
            case "quantities":
                return "Asignar Cantidades";
            case "summary":
                return "Resumen y Confirmación";
            default:
                return "";
        }
    };

    const getNextButtonText = (): string => {
        switch (currentStep) {
            case "contractor":
            case "orders":
            case "quantities":
                return "Continuar";
            case "summary":
                return "Confirmar Asignaciones";
            default:
                return "Continuar";
        }
    };

    // Determinar la dirección de la transición
    const getDirectionForStep = (
        current: WizardStep,
        previous: WizardStep | null,
    ): number => {
        if (!previous) return 0;

        const stepOrder: WizardStep[] = [
            "contractor",
            "orders",
            "quantities",
            "summary",
        ];
        const currentIndex = stepOrder.indexOf(current);
        const previousIndex = stepOrder.indexOf(previous);

        return currentIndex > previousIndex ? 1 : -1;
    };

    // Variantes de animación para el contenido
    const contentVariants = {
        enter: (direction: number) => ({
            x: direction > 0 ? 100 : -100,
            opacity: 0,
        }),
        center: {
            x: 0,
            opacity: 1,
        },
        exit: (direction: number) => ({
            x: direction > 0 ? -100 : 100,
            opacity: 0,
        }),
    };

    // Obtener detalles de la orden cuando se muestra la remisión
    useEffect(() => {
        if (showRemission && state.selectedOrders[0]) {
            getOrderDetailsForAssignment(state.selectedOrders[0])
                .then((result) => {
                    if (
                        result.success &&
                        result.data &&
                        (result.data as any).order
                    ) {
                        setOrderData({
                            id: (result.data as any).order.id,
                            cutOrder: (result.data as any).order.cutOrder,
                            creationDate: (result.data as any).order
                                .receivedDate,
                            parts: (result.data as any).order.parts || [],
                        });
                    }
                })
                .catch((error) => {
                    console.error("Error fetching order details:", error);
                });
        }
    }, [showRemission, state.selectedOrders]);

    if (isSuccess) {
        return (
            <motion.div
                animate={{ scale: 1, opacity: 1 }}
                className="w-full max-w-4xl mx-auto"
                initial={{ scale: 0.95, opacity: 0 }}
                transition={{ duration: 0.5, type: "spring" }}
            >
                <Card className="backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border border-gray-200/50 dark:border-gray-700/50 shadow-2xl">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 rounded-xl pointer-events-none" />
                    <CardBody className="py-10 px-8 relative z-10">
                        {showRemission ? (
                            <AssignmentRemission
                                assignments={
                                    createdAssignments.length > 0
                                        ? createdAssignments
                                        : state.assignments || []
                                }
                                contractorId={state.contractor || ""}
                                orderData={orderData}
                                orderId={state.selectedOrders[0] || ""}
                                onSuccess={() => setShowRemission(false)}
                            />
                        ) : (
                            <motion.div
                                animate={{ scale: 1, opacity: 1 }}
                                className="flex flex-col items-center justify-center space-y-6"
                                initial={{ scale: 0.8, opacity: 0 }}
                                transition={{ duration: 0.5, type: "spring" }}
                            >
                                <motion.div
                                    animate={{
                                        scale: [1, 1.1, 1],
                                        rotate: [0, 10, -10, 0],
                                    }}
                                    className="relative"
                                    transition={{
                                        duration: 2,
                                        repeat: Infinity,
                                        repeatDelay: 3,
                                    }}
                                >
                                    <div className="w-24 h-24 bg-gradient-to-br from-green-400 to-emerald-600 rounded-full flex items-center justify-center shadow-2xl">
                                        <motion.div
                                            animate={{ scale: 1 }}
                                            initial={{ scale: 0 }}
                                            transition={{
                                                delay: 0.3,
                                                duration: 0.5,
                                                type: "spring",
                                            }}
                                        >
                                            <CheckIcon className="w-14 h-14 text-white" />
                                        </motion.div>
                                    </div>
                                    <motion.div
                                        animate={{
                                            scale: [1, 1.2, 1],
                                            opacity: [0.5, 0.8, 0.5],
                                        }}
                                        className="absolute -inset-2 bg-gradient-to-br from-green-400/20 to-emerald-600/20 rounded-full blur-xl"
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                        }}
                                    />
                                </motion.div>

                                <div className="text-center space-y-3">
                                    <motion.h2
                                        animate={{ y: 0, opacity: 1 }}
                                        className="text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"
                                        initial={{ y: 20, opacity: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        ¡Asignaciones creadas con éxito!
                                    </motion.h2>
                                    <motion.p
                                        animate={{ y: 0, opacity: 1 }}
                                        className="text-gray-600 dark:text-gray-300 max-w-md mx-auto"
                                        initial={{ y: 20, opacity: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        Las asignaciones han sido creadas
                                        correctamente y los contratistas ya
                                        pueden comenzar a trabajar en ellas.
                                    </motion.p>
                                </div>
                                <motion.div
                                    animate={{ y: 0, opacity: 1 }}
                                    className="flex flex-col sm:flex-row gap-4 mt-8"
                                    initial={{ y: 20, opacity: 0 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <Button
                                        className="bg-gradient-to-r from-green-500 to-emerald-600 text-white font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all"
                                        size="lg"
                                        startContent={
                                            <PrinterIcon className="w-5 h-5" />
                                        }
                                        onPress={() => setShowRemission(true)}
                                    >
                                        Ver e Imprimir Remisión
                                    </Button>
                                    <Button
                                        className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all"
                                        size="lg"
                                        startContent={
                                            <SparklesIcon className="w-5 h-5" />
                                        }
                                        onPress={() =>
                                            (window.location.href =
                                                "/dashboard/assignments")
                                        }
                                    >
                                        Ver Asignaciones
                                    </Button>
                                    <Button
                                        className="border-2 border-gray-300 dark:border-gray-600 hover:border-purple-500 dark:hover:border-purple-400 transition-all"
                                        size="lg"
                                        startContent={
                                            <RocketLaunchIcon className="w-5 h-5" />
                                        }
                                        variant="bordered"
                                        onPress={() => {
                                            setIsSuccess(false);
                                            window.location.href =
                                                "/dashboard/assignments/new";
                                        }}
                                    >
                                        Crear Nuevas Asignaciones
                                    </Button>
                                </motion.div>
                            </motion.div>
                        )}
                    </CardBody>
                </Card>
            </motion.div>
        );
    }

    const direction = getDirectionForStep(currentStep, previousStep);

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="w-full max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, type: "spring" }}
        >
            <Card className="backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border border-gray-200/50 dark:border-gray-700/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 rounded-xl pointer-events-none" />
                <CardBody className="relative z-10">
                    {/* Nuevo indicador de pasos */}
                    <StepIndicator
                        allowNavigation={true}
                        currentStep={
                            currentStep === "contractor"
                                ? 1
                                : currentStep === "orders"
                                  ? 2
                                  : currentStep === "quantities"
                                    ? 3
                                    : currentStep === "summary"
                                      ? 4
                                      : 1
                        }
                        onStepClick={(stepId) => {
                            const stepMap: Record<number, WizardStep> = {
                                1: "contractor",
                                2: "orders",
                                3: "quantities",
                                4: "summary",
                            };

                            const targetStep = stepMap[stepId];

                            if (targetStep) {
                                setPreviousStep(currentStep);
                                goToNextStep();
                            }
                        }}
                    />

                    <div className="py-6">
                        <div className="flex items-center justify-between mb-8">
                            <motion.div
                                key={`title-${currentStep}`}
                                animate={{ opacity: 1, x: 0 }}
                                className="flex items-center gap-3"
                                initial={{ opacity: 0, x: -20 }}
                                transition={{ duration: 0.3 }}
                            >
                                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                                    {currentStep === "contractor" ? (
                                        <UserCircleIcon className="w-6 h-6 text-white" />
                                    ) : currentStep === "orders" ? (
                                        <CubeIcon className="w-6 h-6 text-white" />
                                    ) : currentStep === "quantities" ? (
                                        <ChartBarIcon className="w-6 h-6 text-white" />
                                    ) : (
                                        <CheckCircleIcon className="w-6 h-6 text-white" />
                                    )}
                                </div>
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                    {getStepTitle(currentStep)}
                                </h2>
                            </motion.div>

                            <motion.div
                                animate={{ opacity: 1, scale: 1 }}
                                className="hidden sm:flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full"
                                initial={{ opacity: 0, scale: 0.8 }}
                                transition={{ delay: 0.2 }}
                            >
                                <SparklesIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                                    Paso{" "}
                                    {currentStep === "contractor"
                                        ? 1
                                        : currentStep === "orders"
                                          ? 2
                                          : currentStep === "quantities"
                                            ? 3
                                            : 4}{" "}
                                    de 4
                                </span>
                            </motion.div>
                        </div>

                        {/* Contenido del paso actual con animación */}
                        <AnimatePresence custom={direction} mode="wait">
                            <motion.div
                                key={currentStep}
                                animate="center"
                                custom={direction}
                                exit="exit"
                                initial="enter"
                                transition={{
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 30,
                                }}
                                variants={contentVariants}
                            >
                                {renderCurrentStep()}
                            </motion.div>
                        </AnimatePresence>
                    </div>
                </CardBody>

                <CardFooter className="flex justify-between items-center p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
                    <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <Button
                            className="font-semibold hover:bg-gray-200 dark:hover:bg-gray-700"
                            isDisabled={!canGoBack() || isSubmitting}
                            size="lg"
                            startContent={<ArrowLeftIcon className="w-5 h-5" />}
                            variant="flat"
                            onPress={() => handleStepChange("previous")}
                        >
                            Atrás
                        </Button>
                    </motion.div>

                    {currentStep !== "summary" && (
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Button
                                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold shadow-lg hover:shadow-xl transform transition-all"
                                endContent={
                                    isSubmitting ? (
                                        <Spinner color="white" size="sm" />
                                    ) : (
                                        <ArrowRightIcon className="w-5 h-5" />
                                    )
                                }
                                isDisabled={!canGoNext() || isSubmitting}
                                size="lg"
                                onPress={() => handleStepChange("next")}
                            >
                                {getNextButtonText()}
                            </Button>
                        </motion.div>
                    )}
                </CardFooter>
            </Card>
        </motion.div>
    );
}

// Orden de los pasos para navegación
const STEPS_ORDER: WizardStep[] = [
    "contractor",
    "orders",
    "quantities",
    "summary",
];
