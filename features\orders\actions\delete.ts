"use server";

import { prisma } from "@/shared/lib/prisma";
import { auth } from "@/lib/auth-helpers";

import { ActionResponse, createErrorResponse, revalidateCache } from "./utils";

/**
 * Elimina una orden por su ID
 */
export async function deleteOrder(id: string): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const session = await auth();

        if (!session || !session.user) {
            return {
                success: false,
                error: "Usuario no autenticado. Por favor inicie sesión para eliminar órdenes.",
            };
        }

        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${id}`,
            };
        }

        // Eliminar la orden y todas sus relaciones (cascada)
        await prisma.order.delete({
            where: { id },
        });

        // Revalidar rutas y tags relacionados con órdenes
        revalidateCache();

        return {
            success: true,
            data: { id },
        };
    } catch (error) {
        // Si el error es por restricciones de integridad referencial (FK)
        if (
            error instanceof Error &&
            error.message.includes("foreign key constraint")
        ) {
            return {
                success: false,
                error: "No se puede eliminar esta orden porque tiene elementos relacionados. Por favor, elimine primero esos elementos.",
            };
        }

        return createErrorResponse(error);
    }
}
