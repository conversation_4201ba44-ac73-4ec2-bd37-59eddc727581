/**
 * Redis Connection and Configuration
 * Implements the cache layer for performance optimization
 */

import Redis from "ioredis";

import { featureFlags } from "../feature-flags";

// Redis connection options
const redisOptions = {
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379"),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || "0"),
    keyPrefix: process.env.REDIS_KEY_PREFIX || "lohari:",
    retryStrategy: (times: number) => {
        const delay = Math.min(times * 50, 2000);

        return delay;
    },
    reconnectOnError: (err: Error) => {
        const targetError = "READONLY";

        if (err.message.includes(targetError)) {
            // Only reconnect when the error contains "READONLY"
            return true;
        }

        return false;
    },
};

// Create Redis client
let redisClient: Redis | null = null;

/**
 * Get or create Redis client
 */
export function getRedisClient(): Redis | null {
    if (!featureFlags.ENABLE_REDIS) {
        return null;
    }

    if (!redisClient) {
        try {
            redisClient = new Redis(redisOptions);

            redisClient.on("connect", () => {
                console.log("✅ Redis connected successfully");
            });

            redisClient.on("error", (err) => {
                console.error("❌ Redis connection error:", err);
            });

            redisClient.on("ready", () => {
                console.log("✅ Redis ready to accept commands");
            });
        } catch (error) {
            console.error("❌ Failed to create Redis client:", error);

            return null;
        }
    }

    return redisClient;
}

/**
 * Gracefully disconnect Redis
 */
export async function disconnectRedis(): Promise<void> {
    if (redisClient) {
        await redisClient.quit();
        redisClient = null;
    }
}

/**
 * Health check for Redis connection
 */
export async function isRedisHealthy(): Promise<boolean> {
    const client = getRedisClient();

    if (!client) return false;

    try {
        const result = await client.ping();

        return result === "PONG";
    } catch (error) {
        return false;
    }
}
