export interface Size {
    id: string;
    code: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    deletedAt?: Date | string | null;
    _count?: {
        garments: number;
    };
}

export interface SizesResponse {
    sizes: Size[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

export interface CreateSizeInput {
    code: string;
}

export interface UpdateSizeInput {
    code: string;
}

export interface SizeFilters {
    search?: string;
    orderBy?: string;
    order?: "asc" | "desc";
    page?: number;
    perPage?: number;
}
