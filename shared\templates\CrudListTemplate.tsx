"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { PlusIcon } from "@heroicons/react/24/outline";

import { Button } from "@/shared/components/ui/hero-ui-client";
import {
    DashboardLayout,
    DashboardStats,
    DashboardFilters,
    DashboardTable,
    type Column,
    type TableAction,
    type FilterOption,
    type SortOption,
    type StatCard,
} from "@/shared/components/dashboard";

export interface CrudListTemplateProps<T> {
    // Layout props
    title: string;
    subtitle?: string;
    breadcrumbs?: Array<{
        label: string;
        href?: string;
        icon?: React.ReactNode;
    }>;

    // Stats
    stats?: StatCard[];

    // Table props
    columns: Column<T>[];
    data: T[];
    isLoading?: boolean;
    emptyContent?: string;

    // Actions
    actions?: TableAction<T>[];
    createRoute?: string;
    createLabel?: string;

    // Filters and Search
    filters?: FilterOption[];
    sortOptions?: SortOption[];
    searchValue?: string;
    onSearchChange?: (value: string) => void;
    filterValues?: Record<string, any>;
    onFilterChange?: (key: string, value: any) => void;
    onSortChange?: (option: SortOption) => void;
    currentSort?: SortOption;
    onClearFilters?: () => void;
    activeFiltersCount?: number;

    // Pagination
    page?: number;
    totalPages?: number;
    onPageChange?: (page: number) => void;

    // Selection
    selectionMode?: "none" | "single" | "multiple";
    selectedKeys?: Set<React.Key>;
    onSelectionChange?: (keys: Set<React.Key>) => void;

    // Additional actions
    additionalActions?: React.ReactNode;
}

export function CrudListTemplate<T extends { id: string | number }>({
    // Layout
    title,
    subtitle,
    breadcrumbs = [],

    // Stats
    stats,

    // Table
    columns,
    data,
    isLoading = false,
    emptyContent = "No hay datos disponibles",

    // Actions
    actions,
    createRoute,
    createLabel = "Crear nuevo",

    // Filters
    filters,
    sortOptions,
    searchValue,
    onSearchChange,
    filterValues,
    onFilterChange,
    onSortChange,
    currentSort,
    onClearFilters,
    activeFiltersCount = 0,

    // Pagination
    page = 1,
    totalPages = 1,
    onPageChange,

    // Selection
    selectionMode = "none",
    selectedKeys,
    onSelectionChange,

    // Additional
    additionalActions,
}: CrudListTemplateProps<T>) {
    const router = useRouter();

    const handleCreate = () => {
        if (createRoute) {
            router.push(createRoute);
        }
    };

    const headerActions = (
        <>
            {additionalActions}
            {createRoute && (
                <Button
                    className="shadow-md"
                    color="primary"
                    startContent={<PlusIcon className="w-4 h-4" />}
                    onPress={handleCreate}
                >
                    {createLabel}
                </Button>
            )}
        </>
    );

    return (
        <DashboardLayout
            actions={headerActions}
            breadcrumbs={breadcrumbs}
            stats={stats && <DashboardStats stats={stats} />}
            subtitle={subtitle}
            title={title}
        >
            <div className="space-y-6">
                {/* Filters Section */}
                {(filters || sortOptions || onSearchChange) && (
                    <DashboardFilters
                        activeFiltersCount={activeFiltersCount}
                        currentSort={currentSort}
                        filterValues={filterValues}
                        filters={filters}
                        searchValue={searchValue}
                        sortOptions={sortOptions}
                        onClearFilters={onClearFilters}
                        onFilterChange={onFilterChange}
                        onSearchChange={onSearchChange}
                        onSortChange={onSortChange}
                    />
                )}

                {/* Table Section */}
                <DashboardTable
                    actions={actions as any}
                    columns={columns as any}
                    data={data}
                    emptyContent={emptyContent}
                    isLoading={isLoading}
                    page={page}
                    selectedKeys={selectedKeys}
                    selectionMode={selectionMode}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                    onSelectionChange={onSelectionChange}
                />
            </div>
        </DashboardLayout>
    );
}
