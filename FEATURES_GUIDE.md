# 📦 Guía de Features y Módulos - Sistema Lohari

## 📋 Índice de Módulos

1. [Orders (Órdenes)](#-orders-órdenes)
2. [Customers (Clientes)](#-customers-clientes)
3. [Contractors (Contratistas)](#-contractors-contratistas)
4. [Assignments (Asignaciones)](#-assignments-asignaciones)
5. [Remissions (Remisiones)](#-remissions-remisiones)
6. [Notes (Notas)](#-notes-notas)
7. [Packings (Empaquetado)](#-packings-empaquetado)
8. [Auth (Autenticación)](#-auth-autenticación)
9. [Colors (Colores)](#-colors-colores)
10. [Sizes (Tallas)](#-sizes-tallas)
11. [Models (Modelos)](#-models-modelos)

---

## 📦 Orders (Órdenes)

### Descripción
Módulo central del sistema que gestiona todo el ciclo de vida de las órdenes de producción.

### Estructura del Módulo
```
features/orders/
├── actions/
│   ├── create.ts         # Crear nueva orden
│   ├── update.ts         # Actualizar orden
│   ├── delete.ts         # Eliminar orden
│   ├── query.ts          # Consultar órdenes
│   ├── batch-actions.ts  # Acciones masivas
│   ├── assignments.ts    # Gestión de asignaciones
│   ├── notes.ts          # Notas de orden
│   └── parts.ts          # Partes/partidas
├── components/
│   ├── OrderTable.tsx          # Tabla principal
│   ├── OrderDetailModal.tsx    # Modal de detalle
│   ├── OrderStatusChip.tsx     # Chip de estado
│   ├── OrderDashboard.tsx      # Dashboard completo
│   ├── CalendarView.tsx        # Vista calendario
│   ├── MetricsView.tsx         # Vista métricas
│   └── wizard/                 # Wizard de creación
│       ├── BasicInfoStep.tsx
│       ├── GarmentsStep.tsx
│       ├── PartsStep.tsx
│       └── ReviewStep.tsx
├── hooks/
│   ├── useOrders.ts            # Hook principal
│   ├── useOrdersData.ts        # Gestión de datos
│   ├── useOrdersParams.ts      # Parámetros URL
│   ├── useOrdersState.ts       # Estado local
│   └── useOrderEdit.ts         # Edición de orden
└── types/
    └── orders.ts               # Tipos TypeScript
```

### Componentes Principales

#### OrderDashboard
Dashboard completo con 3 tabs:
```tsx
<OrderDashboard
  activeTab="orders" // "orders" | "metrics" | "calendar"
  onTabChange={setActiveTab}
>
  {/* Contenido del tab activo */}
</OrderDashboard>
```

#### OrderTable
Tabla con funcionalidades avanzadas:
```tsx
<OrderTable
  orders={orders}
  columns={customColumns}
  onEdit={handleEdit}
  onDelete={handleDelete}
  onBatchAction={handleBatchAction}
  selectable={true}
  loading={isLoading}
/>
```

#### OrderWizard
Wizard multi-paso para creación:
```tsx
<OrderWizard
  onComplete={handleOrderCreated}
  initialData={draftData}
  customers={customersList}
  models={modelsList}
/>
```

### Hooks Disponibles

#### useOrders
Hook principal para gestión de órdenes:
```typescript
const {
  orders,
  loading,
  error,
  filters,
  setFilters,
  pagination,
  setPagination,
  refresh,
  createOrder,
  updateOrder,
  deleteOrder
} = useOrders()
```

#### useOrdersData
Hook optimizado para datos con SWR:
```typescript
const {
  data,
  isLoading,
  isValidating,
  error,
  mutate
} = useOrdersData({
  page: 1,
  limit: 20,
  status: 'active',
  search: 'cliente'
})
```

### Server Actions

#### createOrder
```typescript
import { createOrder } from '@/features/orders/actions'

const result = await createOrder({
  customerId: 'cuid...',
  estimatedDeliveryDate: new Date('2024-12-31'),
  garments: [
    {
      modelId: 'model-1',
      colorId: 'color-1',
      sizes: {
        'S': 10,
        'M': 20,
        'L': 15
      }
    }
  ],
  parts: ['P001', 'P002'],
  notes: 'Orden urgente'
})
```

#### batchUpdateStatus
```typescript
import { batchUpdateStatus } from '@/features/orders/actions'

await batchUpdateStatus({
  orderIds: ['order-1', 'order-2'],
  statusId: 'status-completed'
})
```

### Estados y Flujos

#### Estados de Orden
1. `RECEIVED` - Recibida
2. `IN_PRODUCTION` - En producción
3. `QUALITY_CHECK` - Control de calidad
4. `COMPLETED` - Completada
5. `DELIVERED` - Entregada
6. `CANCELLED` - Cancelada

#### Flujo Principal
```
Crear Orden → Asignar → Producir → QC → Completar → Entregar
     ↓           ↓         ↓        ↓       ↓         ↓
   Borrador   Contratista  WIP    Check  Remisión  Archivo
```

### Métricas del Dashboard

El módulo incluye 8 tarjetas de métricas:
- Órdenes Activas
- Prendas en Producción
- Entregas Esta Semana
- Tasa de Cumplimiento
- Órdenes en Riesgo
- Productividad Mensual
- Valor Total en Proceso
- Tiempo Promedio de Ciclo

---

## 👥 Customers (Clientes)

### Descripción
Gestión completa de la base de clientes con soporte para jerarquías.

### Estructura del Módulo
```
features/customers/
├── actions/
│   ├── create.ts              # Crear cliente
│   ├── update.ts              # Actualizar
│   ├── delete.ts              # Eliminar
│   ├── query.ts               # Consultar
│   ├── create-subcustomer.ts  # Sub-clientes
│   └── update-hierarchy.ts    # Jerarquía
├── components/
│   ├── forms/
│   │   ├── CustomerForm.tsx
│   │   └── CustomerFormHeader.tsx
│   ├── hierarchy/
│   │   ├── CustomerHierarchy.tsx
│   │   └── SubCustomerList.tsx
│   └── modals/
│       └── DeleteCustomerModal.tsx
└── hooks/
    ├── useCustomer.ts
    └── useCustomers.ts
```

### Características Especiales

#### Jerarquía de Clientes
Soporte para clientes principales y sub-clientes:
```typescript
interface Customer {
  id: string
  name: string
  parentId?: string          // Cliente padre
  parent?: Customer         // Relación padre
  subCustomers: Customer[]  // Sub-clientes
  displayName?: string      // "Padre - Hijo"
}
```

#### Componente de Jerarquía
```tsx
<CustomerHierarchy
  customerId={customerId}
  onSelectCustomer={handleSelect}
  showMetrics={true}
/>
```

### Hooks

#### useCustomers
```typescript
const {
  customers,
  loading,
  error,
  searchCustomers,
  getCustomerWithOrders,
  getCustomerHierarchy
} = useCustomers()
```

---

## 👷 Contractors (Contratistas)

### Descripción
Administración de contratistas/maquilas con métricas de rendimiento.

### Estructura del Módulo
```
features/contractors/
├── actions/
│   ├── create.ts
│   ├── update.ts
│   ├── delete.ts
│   ├── query.ts
│   ├── getMetrics.ts    # Métricas de rendimiento
│   └── stats.ts         # Estadísticas
├── components/
│   ├── ContractorDetailModal/
│   └── forms/
└── hooks/
    ├── useContractor.ts
    └── useContractors.ts
```

### Métricas de Contratista

#### getContractorMetrics
```typescript
const metrics = await getContractorMetrics(contractorId)
// Returns:
{
  totalAssignments: 150,
  completedAssignments: 145,
  completionRate: 96.7,
  averageDefectRate: 2.3,
  onTimeDeliveryRate: 94.5,
  currentActiveAssignments: 5,
  totalGarmentsProduced: 15000,
  lastDeliveryDate: Date
}
```

### Componentes

#### ContractorDetailModal
Modal con información completa y métricas:
```tsx
<ContractorDetailModal
  contractorId={contractorId}
  isOpen={isOpen}
  onClose={handleClose}
  showMetrics={true}
  showHistory={true}
/>
```

---

## 📋 Assignments (Asignaciones)

### Descripción
Sistema avanzado de distribución de trabajo con wizard multi-paso.

### Estructura del Módulo
```
features/assignments/
├── actions/
│   ├── create-v2.ts           # Creación mejorada
│   ├── update-with-locking.ts # Actualización con bloqueo
│   ├── getAssignments.ts
│   └── validation.ts
├── components/
│   └── wizard/
│       ├── AssignmentWizard.tsx
│       └── EnhancedAssignmentWizard.tsx
└── hooks/
    ├── useAssignments.ts
    ├── useDraftPersistence.ts   # Persistencia de borrador
    ├── useOptimisticValidation.ts
    └── useProgressTracker.ts
```

### Wizard de Asignación

#### EnhancedAssignmentWizard
Wizard avanzado con 4 pasos:
```tsx
<EnhancedAssignmentWizard
  orderId={orderId}
  onComplete={handleComplete}
  initialDraft={savedDraft}
  features={{
    optimisticValidation: true,
    draftPersistence: true,
    progressTracking: true,
    undoRedo: true
  }}
/>
```

### Características Avanzadas

#### Persistencia de Borrador
```typescript
const { 
  draft, 
  saveDraft, 
  loadDraft, 
  clearDraft 
} = useDraftPersistence('assignment-wizard')
```

#### Validación Optimista
```typescript
const {
  validate,
  errors,
  isValidating
} = useOptimisticValidation({
  schema: assignmentSchema,
  debounceMs: 300
})
```

#### Control de Versiones
```typescript
await updateAssignmentWithLocking({
  id: assignmentId,
  data: updates,
  expectedVersion: currentVersion
})
```

### Folios Únicos
Sistema de generación de folios secuenciales:
```
2024-001, 2024-002, 2024-003...
```

---

## 📄 Remissions (Remisiones)

### Descripción
Generación y gestión de documentos de entrega con PDF profesional.

### Estructura del Módulo
```
features/remissions/
├── actions/
│   ├── create.ts
│   ├── update.ts
│   ├── delete.ts
│   ├── migrate.ts         # Migración de datos
│   └── remission-utils.ts
├── components/
│   ├── RemissionCard.tsx
│   ├── RemissionDocument.tsx      # Vista previa
│   ├── RemissionPrintModal.tsx    # Modal de impresión
│   └── RemissionGrid.tsx
├── services/
│   └── pdf-service.ts            # Generación PDF
└── hooks/
    ├── useRemission.ts
    └── useRemissionPDF.ts
```

### Generación de PDF

#### RemissionPDF Service
```typescript
import { generateRemissionPDF } from '@/features/remissions/services'

const pdfBlob = await generateRemissionPDF({
  remissionId: 'rem-123',
  includeSignatures: true,
  includeQR: true,
  format: 'letter' // 'letter' | 'a4'
})

// Download
const url = URL.createObjectURL(pdfBlob)
const a = document.createElement('a')
a.href = url
a.download = `remision-${folio}.pdf`
a.click()
```

### Componentes

#### RemissionDocument
Vista previa del documento:
```tsx
<RemissionDocument
  remission={remission}
  showToolbar={true}
  onPrint={handlePrint}
  onDownload={handleDownload}
  className="shadow-lg"
/>
```

#### RemissionPrintModal
Modal de impresión con opciones:
```tsx
<RemissionPrintModal
  remissionId={remissionId}
  isOpen={isOpen}
  onClose={handleClose}
  onPrinted={handlePrinted}
  options={{
    copies: 2,
    includeOriginal: true,
    includeCopy: true
  }}
/>
```

### Estados de Remisión
- `DRAFT` - Borrador
- `CONFIRMED` - Confirmada
- `PRINTED` - Impresa
- `DELIVERED` - Entregada
- `CANCELLED` - Cancelada

---

## 📝 Notes (Notas)

### Descripción
Sistema robusto de comunicación con comentarios, likes y menciones.

### Estructura del Módulo
```
features/notes/
├── actions/
│   ├── create.ts
│   ├── update.ts
│   ├── delete.ts
│   ├── bulk-actions.ts      # Acciones masivas
│   ├── comments/            # Sistema de comentarios
│   │   ├── create.ts
│   │   ├── delete.ts
│   │   └── list.ts
│   └── likes/              # Sistema de likes
│       ├── toggle.ts
│       └── get-count.ts
├── components/
│   ├── NoteCard.tsx
│   ├── EnhancedNoteCard.tsx
│   ├── CommentList.tsx
│   ├── CommentForm.tsx
│   ├── BulkActionsToolbar.tsx
│   └── SelectableNoteCard.tsx
└── hooks/
    ├── useNotes.ts
    ├── useNoteComments.ts
    ├── useNoteLikes.ts
    └── useNoteBulkActions.ts
```

### Características Avanzadas

#### Sistema de Comentarios
```tsx
<CommentList
  noteId={noteId}
  onReply={handleReply}
  onDelete={handleDelete}
  maxDepth={3}
  showLikes={true}
/>
```

#### Sistema de Likes
```typescript
const { 
  likes, 
  hasLiked, 
  toggleLike 
} = useNoteLikes(noteId)
```

#### Bulk Actions
```typescript
const {
  selectedNotes,
  selectNote,
  selectAll,
  clearSelection,
  bulkDelete,
  bulkUpdateImportance,
  bulkUpdateStatus
} = useNoteBulkActions()
```

#### Niveles de Importancia
- `LOW` - Baja (gris)
- `MEDIUM` - Media (azul)
- `HIGH` - Alta (amarillo)
- `URGENT` - Urgente (rojo)

### Menciones y Tags
Sistema de menciones con `@usuario` y tags con `#etiqueta`:
```typescript
const processedContent = parseNoteMentions(content)
// @juan → <span class="mention">@juan</span>
// #urgente → <span class="tag">#urgente</span>
```

---

## 📦 Packings (Empaquetado)

### Descripción
Módulo nuevo para gestión de empaquetado y distribución.

### Estructura del Módulo
```
features/packings/
├── actions/
│   ├── create-packing-enhanced-v3.ts
│   ├── calculate-packaging.ts
│   ├── quality-check-actions.ts
│   ├── transport-actions.ts
│   └── generate-folio.ts
├── components/
│   ├── EnhancedPackingDocument.tsx
│   ├── QualityCheckModal.tsx
│   ├── wizard/
│   │   ├── BasicInfoStep.tsx
│   │   ├── OrderSelectionStep.tsx
│   │   ├── QualityDistributionStep.tsx
│   │   └── ReviewStep.tsx
│   └── documents/
│       └── ProfessionalPackingDocument.tsx
└── hooks/
    ├── usePackings.ts
    └── usePackingWizardStore.ts
```

### Wizard de Empaquetado

#### EnhancedPackingWizardV3
Wizard avanzado con cálculo automático:
```tsx
<EnhancedPackingWizardV3
  onComplete={handleComplete}
  features={{
    autoCalculation: true,
    qualityCheck: true,
    transportAssignment: true,
    documentGeneration: true
  }}
/>
```

### Cálculo de Empaquetado
```typescript
const packagingPlan = await calculatePackaging({
  orders: selectedOrders,
  constraints: {
    maxBoxWeight: 25, // kg
    maxBoxVolume: 100, // litros
    itemsPerBox: 50
  }
})
```

### Control de Calidad
```typescript
await performQualityCheck({
  packingId,
  checkedBy: userId,
  results: {
    totalItems: 500,
    defectiveItems: 3,
    qualityScore: 99.4
  }
})
```

---

## 🔐 Auth (Autenticación)

### Descripción
Sistema completo de autenticación con NextAuth.

### Estructura del Módulo
```
features/auth/
├── actions/
│   ├── auth-actions.ts
│   └── users.ts
├── components/
│   ├── LoginForm.tsx
│   ├── RegisterForm.tsx
│   └── WebAuthnButton.tsx
├── config/
│   └── nextauth-config.ts
└── hooks/
    ├── useUser.ts
    └── useAuthForm.ts
```

### Configuración NextAuth
```typescript
// auth.ts
export const authOptions = {
  providers: [
    CredentialsProvider({
      credentials: {
        email: { type: "email" },
        password: { type: "password" },
        remember: { type: "checkbox" }
      },
      authorize: async (credentials) => {
        // Validación y autenticación
      }
    })
  ],
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.role = user.role
      }
      return token
    }
  }
}
```

### Hooks de Autenticación

#### useUser
```typescript
const { 
  user, 
  isLoading, 
  isAuthenticated,
  hasRole,
  canAccess 
} = useUser()

if (hasRole('ADMIN')) {
  // Mostrar opciones admin
}
```

#### useAuthForm
```typescript
const {
  register,
  handleSubmit,
  errors,
  isSubmitting,
  login,
  signup
} = useAuthForm()
```

---

## 🎨 Colors (Colores)

### Descripción
Gestión de paleta de colores con análisis de accesibilidad.

### Estructura del Módulo
```
features/colors/
├── actions/
├── components/
│   ├── ColorPreview.tsx
│   ├── ColorVariations.tsx
│   ├── ColorAccessibility.tsx
│   └── ContrastIndicator.tsx
├── utils/
│   └── colorUtils.ts
└── hooks/
    └── useColor.ts
```

### Utilidades de Color

#### Análisis de Accesibilidad
```typescript
import { analyzeColorAccessibility } from '@/features/colors/utils'

const analysis = analyzeColorAccessibility('#006FEE', '#FFFFFF')
// Returns:
{
  contrastRatio: 4.5,
  wcagAA: true,
  wcagAAA: false,
  recommendations: [...]
}
```

#### Generación de Variaciones
```typescript
const variations = generateColorVariations('#006FEE')
// Returns lighter and darker shades
```

---

## 📏 Sizes (Tallas)

### Descripción
Catálogo simple de tallas con contadores de uso.

### Estructura del Módulo
```
features/sizes/
├── actions/
├── components/
└── hooks/
    ├── useSize.ts
    └── useSizes.ts
```

### Modelo de Datos
```typescript
interface Size {
  id: string
  code: string      // "S", "M", "L", "XL"
  createdAt: Date
  updatedAt: Date
  garments: GarmentSize[]  // Relación con prendas
}
```

---

## 👔 Models (Modelos)

### Descripción
Catálogo de modelos/diseños de prendas.

### Estructura del Módulo
```
features/models/
├── actions/
├── components/
│   └── forms/
└── hooks/
    └── useModel.ts
```

### Modelo de Datos
```typescript
interface GarmentModel {
  id: string
  code: string          // Código único
  description: string   // Descripción
  basePrice?: number   // Precio base
  createdAt: Date
  updatedAt: Date
  garments: Garment[]  // Prendas de este modelo
}
```

---

## 🏃 Guía de Uso Rápido

### Crear una Nueva Orden
```typescript
// 1. Import actions
import { createOrder } from '@/features/orders/actions'

// 2. En un Server Component o Server Action
const newOrder = await createOrder({
  customerId: 'customer-123',
  estimatedDeliveryDate: new Date('2024-12-31'),
  garments: [
    {
      modelId: 'model-shirt-001',
      colorId: 'color-blue',
      sizes: {
        'S': 20,
        'M': 30,
        'L': 25,
        'XL': 15
      }
    }
  ],
  notes: 'Orden urgente para evento especial'
})
```

### Asignar Trabajo a Contratistas
```typescript
// Usando el wizard
<EnhancedAssignmentWizard
  orderId={orderId}
  onComplete={async (assignments) => {
    console.log('Asignaciones creadas:', assignments)
    router.push('/dashboard/assignments')
  }}
/>
```

### Generar Remisión
```typescript
import { createRemission } from '@/features/remissions/actions'

const remission = await createRemission({
  contractorId: 'contractor-123',
  assignmentIds: ['assign-1', 'assign-2'],
  notes: 'Entrega parcial del lote'
})

// Generar PDF
const pdf = await generateRemissionPDF(remission.id)
```

### Sistema de Notas
```typescript
// Crear nota
const note = await createNote({
  content: 'Revisar calidad del lote @supervisor #urgente',
  orderId: 'order-123',
  importanceId: 'importance-high'
})

// Agregar comentario
await createComment({
  noteId: note.id,
  content: 'Revisado, encontramos 3 defectos menores',
  parentId: null // null para comentario raíz
})

// Toggle like
await toggleLike(note.id)
```

---

**Última actualización**: Enero 2025  
**Versión**: 0.0.1