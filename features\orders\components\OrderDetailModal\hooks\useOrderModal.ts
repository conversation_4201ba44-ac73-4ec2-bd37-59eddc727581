import { useState, useMemo } from "react";

import { Order } from "../types/order-modal.types";

interface UseOrderModalProps {
    order: Order | null;
    initialTab?: string;
}

export function useOrderModal({
    order,
    initialTab = "general",
}: UseOrderModalProps) {
    const [activeTab, setActiveTab] = useState(initialTab);
    const [isDeleting, setIsDeleting] = useState(false);

    // Calculate counts for tabs
    const counts = useMemo(
        () => ({
            notesCount: 0, // Will be implemented later
            assignmentsCount:
                (order?.assignments || order?.OrderAssignment)?.length || 0,
        }),
        [order],
    );

    // Handle tab change
    const handleTabChange = (tab: string) => {
        setActiveTab(tab);
    };

    return {
        // State
        activeTab,
        isDeleting,
        counts,

        // Actions
        setActiveTab: handleTabChange,
        setIsDeleting,
    };
}
