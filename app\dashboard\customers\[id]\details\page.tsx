"use client";

import type { Customer } from "@/features/customers/types";

import React, { useState } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    <PERSON>,
    CardHeader,
    CardBody,
    CardFooter,
    <PERSON><PERSON>,
    Tooltip,
    Link,
    Spinner,
    Badge,
    <PERSON>,
    Di<PERSON>r,
    Ta<PERSON>,
    Tab,
} from "@heroui/react";
import {
    UserGroupIcon,
    ArrowLeftIcon,
    HomeIcon,
    ChevronRightIcon,
    PencilSquareIcon,
    TrashIcon,
    CalendarIcon,
    ClipboardDocumentCheckIcon,
    CheckBadgeIcon,
    InformationCircleIcon,
    ShoppingCartIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";

import {
    useCustomer,
    useDeleteCustomer,
} from "@/features/customers/hooks/useCustomer";
import { useSubCustomers } from "@/features/customers/hooks/useSubCustomers";
import { triggerRevalidation } from "@/shared/providers/swr-provider";
import { SubCustomerList } from "@/features/customers/components/hierarchy/SubCustomerList";
import {
    SubcustomerModal,
    useSubcustomerModal,
} from "@/features/customers/components/modals/SubcustomerModal";

// Variantes para animaciones
const pageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            staggerChildren: 0.07,
            ease: [0.22, 1, 0.36, 1],
        },
    },
};

const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.4, ease: [0.22, 1, 0.36, 1] },
    },
};

export default function CustomerDetailsPage() {
    const router = useRouter();
    const params = useParams();
    const customerId = params.id as string;

    // Estados para la UI
    const [isDeleting, setIsDeleting] = useState(false);
    const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
    const [selectedTab, setSelectedTab] = useState("information");

    // Obtener datos del cliente
    const { customer, isLoading, error } = useCustomer(customerId);
    const typedCustomer = customer as Customer | null;
    const { deleteCustomer } = useDeleteCustomer();

    // Obtener subclientes
    const {
        subCustomers,
        isLoading: subCustomersLoading,
        mutate: mutateSubCustomers,
    } = useSubCustomers(customerId);

    // Modal para subclientes
    const subcustomerModal = useSubcustomerModal();

    // Manejar eliminación de cliente
    const handleDelete = async () => {
        if (!typedCustomer || !typedCustomer.id) return;

        setIsDeleting(true);
        try {
            await deleteCustomer(typedCustomer.id);
            triggerRevalidation("customers");
            router.push("/dashboard/customers");
        } catch (error) {
            // REMOVED: console.error("Error al eliminar el cliente:", error);
            setIsDeleting(false);
        }
    };

    // Manejar success del modal de subclientes
    const handleSubcustomerSuccess = () => {
        mutateSubCustomers();
        triggerRevalidation("customers");
    };

    // Si está cargando, mostrar spinner
    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="text-center">
                    <Spinner className="mb-4" color="primary" size="lg" />
                    <p className="text-gray-600 dark:text-gray-400">
                        Cargando cliente...
                    </p>
                </div>
            </div>
        );
    }

    // Si hay error o no hay cliente, mostrar mensaje
    if (error || !customer) {
        return (
            <div className="text-center p-8">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">
                    No se pudo cargar el cliente
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                    {error?.message ||
                        "El cliente solicitado no existe o ha sido eliminado."}
                </p>
                <Button
                    as={Link}
                    color="primary"
                    href="/dashboard/customers"
                    startContent={<ArrowLeftIcon className="h-4 w-4" />}
                >
                    Volver a clientes
                </Button>
            </div>
        );
    }

    return (
        <motion.div
            animate="visible"
            className="container mx-auto px-4 py-6"
            initial="hidden"
            variants={pageVariants}
        >
            {/* Navegación */}
            <motion.div className="mb-6" variants={itemVariants}>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <Button
                        as="a"
                        className="gap-1 text-sm font-normal"
                        href="/dashboard"
                        variant="light"
                    >
                        <HomeIcon className="h-4 w-4" />
                        Inicio
                    </Button>
                    <ChevronRightIcon className="h-3 w-3" />
                    <Button
                        as="a"
                        className="gap-1 text-sm font-normal"
                        href="/dashboard/customers"
                        variant="light"
                    >
                        <UserGroupIcon className="h-4 w-4" />
                        Clientes
                    </Button>
                    <ChevronRightIcon className="h-3 w-3" />
                    <span className="text-gray-700 dark:text-gray-300 font-medium flex items-center">
                        <InformationCircleIcon className="h-4 w-4 mr-1" />
                        Detalles de cliente
                    </span>
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                            Detalles de Cliente:{" "}
                            {(typedCustomer as any)?.displayName ||
                                typedCustomer?.name}
                        </h1>
                        <p className="text-gray-500 dark:text-gray-400 mt-1 max-w-xl">
                            Información detallada sobre el cliente seleccionado
                        </p>
                    </div>

                    <div className="flex flex-wrap gap-2 justify-end">
                        <Button
                            as={Link}
                            className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
                            href="/dashboard/customers"
                            startContent={<ArrowLeftIcon className="h-4 w-4" />}
                            variant="flat"
                        >
                            Volver
                        </Button>
                        <Button
                            as={Link}
                            className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white"
                            color="primary"
                            href={`/dashboard/customers/${typedCustomer?.id}/edit`}
                            startContent={
                                <PencilSquareIcon className="h-4 w-4" />
                            }
                        >
                            Editar
                        </Button>
                        <Button
                            className="bg-gradient-to-r from-red-600 to-pink-600 text-white"
                            color="danger"
                            isDisabled={
                                (typedCustomer?._count?.orders || 0) > 0
                            }
                            startContent={<TrashIcon className="h-4 w-4" />}
                            onPress={() => setShowDeleteConfirmation(true)}
                        >
                            Eliminar
                        </Button>
                    </div>
                </div>
            </motion.div>

            {/* Tabs con detalles y subclientes */}
            <motion.div variants={itemVariants}>
                <Tabs
                    aria-label="Información del cliente"
                    classNames={{
                        tabList:
                            "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                        cursor: "w-full bg-primary",
                        tab: "max-w-fit px-0 h-12",
                        tabContent: "group-data-[selected=true]:text-primary",
                    }}
                    selectedKey={selectedTab}
                    variant="underlined"
                    onSelectionChange={(key) => setSelectedTab(key as string)}
                >
                    <Tab
                        key="information"
                        title={
                            <div className="flex items-center space-x-2">
                                <InformationCircleIcon className="w-5 h-5" />
                                <span>Información</span>
                            </div>
                        }
                    >
                        <Card className="shadow-xl border-none bg-white dark:bg-gray-900/95 overflow-hidden">
                            <CardHeader className="bg-gradient-to-r from-blue-50 via-primary-50 to-indigo-50 dark:from-blue-900/20 dark:via-primary-900/20 dark:to-indigo-900/20 py-6 relative overflow-hidden">
                                <div className="absolute right-0 top-0 opacity-10 pointer-events-none">
                                    <UserGroupIcon className="h-32 w-32 -rotate-12" />
                                </div>
                                <div className="relative z-10">
                                    <div className="flex items-center">
                                        <Badge
                                            className="mb-2"
                                            color="primary"
                                            content={
                                                <CheckBadgeIcon className="h-3 w-3" />
                                            }
                                            placement="top-right"
                                            size="lg"
                                            variant="solid"
                                        >
                                            <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                                                <UserGroupIcon className="h-8 w-8 text-primary" />
                                            </div>
                                        </Badge>
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-2">
                                        Cliente:{" "}
                                        {(typedCustomer as any)?.displayName ||
                                            typedCustomer?.name}
                                    </h2>
                                    <p className="text-gray-500 dark:text-gray-400 mt-1">
                                        ID: {typedCustomer?.id}
                                    </p>
                                </div>
                            </CardHeader>

                            <CardBody className="p-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Información básica */}
                                    <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl border border-gray-100 dark:border-gray-700/50">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                                            <InformationCircleIcon className="h-5 w-5 mr-2 text-primary" />
                                            Información Básica
                                        </h3>
                                        <div className="space-y-4">
                                            <div>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Nombre
                                                </p>
                                                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                                    {(typedCustomer as any)
                                                        ?.displayName ||
                                                        typedCustomer?.name}
                                                </p>
                                            </div>
                                            <Divider />
                                            <div>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Fecha de registro
                                                </p>
                                                <div className="flex items-center gap-2">
                                                    <CalendarIcon className="h-4 w-4 text-gray-400" />
                                                    <p className="text-gray-700 dark:text-gray-300">
                                                        {format(
                                                            new Date(
                                                                typedCustomer?.createdAt ||
                                                                    new Date(),
                                                            ),
                                                            "PPP",
                                                            { locale: es },
                                                        )}
                                                    </p>
                                                </div>
                                                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {formatDistanceToNow(
                                                        new Date(
                                                            typedCustomer?.createdAt ||
                                                                new Date(),
                                                        ),
                                                        {
                                                            addSuffix: true,
                                                            locale: es,
                                                        },
                                                    )}
                                                </p>
                                            </div>
                                            <Divider />
                                            <div>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Última actualización
                                                </p>
                                                <div className="flex items-center gap-2">
                                                    <CalendarIcon className="h-4 w-4 text-gray-400" />
                                                    <p className="text-gray-700 dark:text-gray-300">
                                                        {format(
                                                            new Date(
                                                                typedCustomer?.updatedAt ||
                                                                    new Date(),
                                                            ),
                                                            "PPP",
                                                            { locale: es },
                                                        )}
                                                    </p>
                                                </div>
                                                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {formatDistanceToNow(
                                                        new Date(
                                                            typedCustomer?.updatedAt ||
                                                                new Date(),
                                                        ),
                                                        {
                                                            addSuffix: true,
                                                            locale: es,
                                                        },
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Uso y estadísticas */}
                                    <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl border border-gray-100 dark:border-gray-700/50">
                                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                                            <ClipboardDocumentCheckIcon className="h-5 w-5 mr-2 text-primary" />
                                            Uso y Estadísticas
                                        </h3>
                                        <div className="space-y-4">
                                            <div>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Órdenes asociadas
                                                </p>
                                                <div className="mt-2">
                                                    {(typedCustomer?._count
                                                        ?.orders || 0) > 0 ? (
                                                        <div className="flex items-center gap-3">
                                                            <Chip
                                                                className="bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-400 border border-green-200/50 dark:border-green-800/30 shadow-sm px-3 py-1.5"
                                                                size="lg"
                                                                variant="flat"
                                                            >
                                                                <div className="flex items-center gap-1.5">
                                                                    <ShoppingCartIcon className="h-4 w-4" />
                                                                    <span className="font-medium">
                                                                        {typedCustomer
                                                                            ?._count
                                                                            ?.orders ||
                                                                            0}
                                                                    </span>
                                                                    <span>
                                                                        órdenes
                                                                        registradas
                                                                    </span>
                                                                </div>
                                                            </Chip>
                                                        </div>
                                                    ) : (
                                                        <Chip
                                                            className="bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30 text-amber-700 dark:text-amber-400 border border-amber-200/50 dark:border-amber-800/30 shadow-sm px-3 py-1.5"
                                                            size="lg"
                                                            variant="flat"
                                                        >
                                                            <div className="flex items-center gap-1.5">
                                                                <InformationCircleIcon className="h-4 w-4" />
                                                                <span className="font-medium">
                                                                    Sin órdenes
                                                                    asociadas
                                                                </span>
                                                            </div>
                                                        </Chip>
                                                    )}
                                                </div>
                                            </div>
                                            <Divider />
                                            <div>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    Estado
                                                </p>
                                                <div className="mt-2">
                                                    {(typedCustomer?._count
                                                        ?.orders || 0) > 0 ? (
                                                        <Chip
                                                            className="bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 text-blue-700 dark:text-blue-400 border border-blue-200/50 dark:border-blue-800/30 shadow-sm"
                                                            color="primary"
                                                            variant="flat"
                                                        >
                                                            Activo
                                                        </Chip>
                                                    ) : (
                                                        <Chip
                                                            className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200/50 dark:border-gray-700/30 shadow-sm"
                                                            variant="flat"
                                                        >
                                                            Inactivo
                                                        </Chip>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardBody>

                            <CardFooter className="bg-gray-50 dark:bg-gray-800/30 border-t border-gray-100 dark:border-gray-800 p-6">
                                <div className="flex flex-wrap justify-between items-center w-full gap-4">
                                    <Button
                                        as={Link}
                                        className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
                                        href="/dashboard/customers"
                                        startContent={
                                            <ArrowLeftIcon className="h-4 w-4" />
                                        }
                                        variant="flat"
                                    >
                                        Volver a clientes
                                    </Button>
                                    <div className="flex gap-2">
                                        <Button
                                            as={Link}
                                            className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white"
                                            color="primary"
                                            href={`/dashboard/customers/${typedCustomer?.id}/edit`}
                                            startContent={
                                                <PencilSquareIcon className="h-4 w-4" />
                                            }
                                        >
                                            Editar cliente
                                        </Button>
                                        <Tooltip
                                            content={
                                                (typedCustomer?._count
                                                    ?.orders || 0) > 0
                                                    ? "No se puede eliminar porque tiene órdenes asociadas"
                                                    : "Eliminar este cliente"
                                            }
                                        >
                                            <Button
                                                className="bg-gradient-to-r from-red-600 to-pink-600 text-white"
                                                color="danger"
                                                isDisabled={
                                                    (typedCustomer?._count
                                                        ?.orders || 0) > 0
                                                }
                                                startContent={
                                                    <TrashIcon className="h-4 w-4" />
                                                }
                                                onPress={() =>
                                                    setShowDeleteConfirmation(
                                                        true,
                                                    )
                                                }
                                            >
                                                Eliminar
                                            </Button>
                                        </Tooltip>
                                    </div>
                                </div>
                            </CardFooter>
                        </Card>
                    </Tab>

                    <Tab
                        key="subclientes"
                        title={
                            <div className="flex items-center space-x-2">
                                <UserGroupIcon className="w-5 h-5" />
                                <span>Subclientes</span>
                                {subCustomers && subCustomers.length > 0 && (
                                    <span className="bg-primary/20 text-primary text-xs px-2 py-0.5 rounded-full">
                                        {subCustomers.length}
                                    </span>
                                )}
                            </div>
                        }
                    >
                        <div className="py-6">
                            {subCustomersLoading ? (
                                <div className="flex justify-center items-center py-8">
                                    <Spinner color="primary" size="md" />
                                </div>
                            ) : (
                                <SubCustomerList
                                    parentId={customerId}
                                    subCustomers={subCustomers || []}
                                    onAddSubCustomer={
                                        subcustomerModal.openCreate
                                    }
                                />
                            )}
                        </div>
                    </Tab>
                </Tabs>
            </motion.div>

            {/* Modal para subclientes */}
            <SubcustomerModal
                editData={subcustomerModal.editData}
                isOpen={subcustomerModal.isOpen}
                parentId={customerId}
                onOpenChange={subcustomerModal.onOpenChange}
                onSuccess={handleSubcustomerSuccess}
            />

            {/* Modal de confirmación de eliminación */}
            <AnimatePresence>
                {showDeleteConfirmation && (
                    <motion.div
                        animate={{ opacity: 1 }}
                        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                        exit={{ opacity: 0 }}
                        initial={{ opacity: 0 }}
                    >
                        <motion.div
                            animate={{ scale: 1, opacity: 1 }}
                            className="bg-white dark:bg-gray-900 rounded-xl shadow-xl max-w-md w-full p-6"
                            exit={{ scale: 0.95, opacity: 0 }}
                            initial={{ scale: 0.95, opacity: 0 }}
                        >
                            <div className="text-center">
                                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
                                    <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    ¿Eliminar cliente permanentemente?
                                </h3>
                                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                    El cliente{" "}
                                    <span className="font-semibold text-gray-700 dark:text-gray-300">
                                        {typedCustomer?.name}
                                    </span>{" "}
                                    será eliminado permanentemente del sistema.
                                </p>
                            </div>
                            <div className="flex gap-3 justify-center mt-6">
                                <Button
                                    className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                                    variant="flat"
                                    onPress={() =>
                                        setShowDeleteConfirmation(false)
                                    }
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    className="bg-gradient-to-r from-red-600 to-pink-600 text-white"
                                    color="danger"
                                    isLoading={isDeleting}
                                    onPress={handleDelete}
                                >
                                    Confirmar Eliminación
                                </Button>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
}
