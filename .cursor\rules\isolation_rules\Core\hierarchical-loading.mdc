---
description: Sistema de carga jerárquica para optimización de tokens
globs: hierarchical-loading.mdc
alwaysApply: false
---
# 🧠 SISTEMA DE CARGA JERÁRQUICA V6

> Reduce el uso de tokens en 65-70% cargando solo lo necesario

## 📊 ARQUITECTURA DE CARGA

```mermaid
graph TD
    Init["Inicialización"] --> Check{"¿Core<br>cargado?"}
    Check -->|No| LoadCore["Cargar Core<br>15K tokens"]
    Check -->|Sí| Skip["Usar caché"]
    
    LoadCore --> LoadMode["Cargar Modo<br>5K tokens"]
    Skip --> LoadMode
    
    LoadMode --> LoadVisual["Cargar Visual Map<br>2K tokens"]
    LoadVisual --> RegisterLazy["Registrar Lazy Loaders<br>0 tokens"]
    
    RegisterLazy --> Ready["Sistema Listo<br>~22K tokens total"]
    
    Ready -.-> OnDemand["Carga bajo demanda<br>+10K cuando necesario"]
```

## 🔧 IMPLEMENTACIÓN

```typescript
export class HierarchicalLoader {
    private cache: RuleCache = {
        core: new Map(),
        mode: new Map(),
        specialized: new Map(),
        visualMaps: new Map()
    };
    
    private lazyLoaders: Map<string, LazyLoader> = new Map();
    private tokenCount: number = 0;
    
    async initializeMode(mode: ModeName, complexity: number): Promise<ModeContext> {
        // 1. Core Rules (cached between modes)
        if (!this.cache.core.has('main')) {
            await this.loadCoreRules();
        }
        
        // 2. Mode Essential Rules
        await this.loadModeEssentials(mode);
        
        // 3. Visual Map
        await this.loadVisualMap(mode);
        
        // 4. Register Lazy Loaders
        this.registerLazyLoaders(mode, complexity);
        
        // 5. Configure Clear Thought
        const clearThought = this.configureClearThought(mode, complexity);
        
        return {
            mode,
            complexity,
            tokensUsed: this.tokenCount,
            clearThoughtTools: clearThought,
            lazyLoaders: Array.from(this.lazyLoaders.keys())
        };
    }
    
    private async loadCoreRules(): Promise<void> {
        const coreRules = [
            'platform-awareness',
            'file-operations',
            'mode-transitions',
            'memory-bank-core'
        ];
        
        for (const rule of coreRules) {
            const content = await this.loadRule(`core/${rule}.mdc`);
            this.cache.core.set(rule, content);
            this.tokenCount += this.estimateTokens(content);
        }
    }
    
    private registerLazyLoaders(mode: ModeName, complexity: number): void {
        this.lazyLoaders.clear();
        
        // Mode-specific lazy loaders
        switch (mode) {
            case 'CREATIVE':
                this.lazyLoaders.set('architecture', 
                    () => this.loadRule('modes/creative/architecture-patterns.mdc'));
                this.lazyLoaders.set('algorithms',
                    () => this.loadRule('modes/creative/algorithm-design.mdc'));
                this.lazyLoaders.set('ui-patterns',
                    () => this.loadRule('modes/creative/ui-patterns.mdc'));
                break;
                
            case 'IMPLEMENT':
                this.lazyLoaders.set('testing',
                    () => this.loadRule('modes/implement/testing-strategies.mdc'));
                this.lazyLoaders.set('refactoring',
                    () => this.loadRule('modes/implement/refactoring-patterns.mdc'));
                break;
                
            case 'PLAN':
                if (complexity >= 3) {
                    this.lazyLoaders.set('comprehensive',
                        () => this.loadRule('modes/plan/comprehensive-planning.mdc'));
                }
                break;
        }
        
        // Complexity-based lazy loaders
        if (complexity >= 3) {
            this.lazyLoaders.set('advanced-patterns',
                () => this.loadRule('complexity/level3-patterns.mdc'));
        }
        
        if (complexity === 4) {
            this.lazyLoaders.set('enterprise-patterns',
                () => this.loadRule('complexity/level4-enterprise.mdc'));
        }
    }
    
    async loadOnDemand(loaderKey: string): Promise<string | null> {
        const loader = this.lazyLoaders.get(loaderKey);
        if (!loader) return null;
        
        // Check cache first
        if (this.cache.specialized.has(loaderKey)) {
            return this.cache.specialized.get(loaderKey)!;
        }
        
        // Load and cache
        const content = await loader();
        this.cache.specialized.set(loaderKey, content);
        this.tokenCount += this.estimateTokens(content);
        
        return content;
    }
}
```

## 📈 MÉTRICAS DE EFICIENCIA

| Escenario | Sistema Original | Sistema Híbrido | Reducción |
|-----------|-----------------|-----------------|-----------|
| Inicio VAN | 70K tokens | 22K tokens | 68% |
| CREATIVE completo | 85K tokens | 32K tokens | 62% |
| Cambio de modo | +70K tokens | +5K tokens | 93% |
| **Promedio** | **75K tokens** | **25K tokens** | **67%** |

## 🔄 REGLAS DE CACHÉ

1. **Core Rules**: Permanecen en caché durante toda la sesión
2. **Mode Rules**: Se mantienen hasta cambio de modo
3. **Visual Maps**: Caché por 30 minutos
4. **Specialized Rules**: LRU cache con límite de 5 reglas
5. **Clear Thought State**: Persiste entre modos

---

**Carga optimizada lista para máxima eficiencia**
