"use client";

import React from "react";
import { motion } from "framer-motion";

import { Card } from "@/shared/components/ui/hero-ui-client";

import { DashboardHeader } from "./DashboardHeader";

interface DashboardLayoutProps {
    children: React.ReactNode;
    title: string;
    subtitle?: string;
    breadcrumbs?: Array<{
        label: string;
        href?: string;
        icon?: React.ReactNode;
    }>;
    stats?: React.ReactNode;
    actions?: React.ReactNode;
    className?: string;
}

export function DashboardLayout({
    children,
    title,
    subtitle,
    breadcrumbs = [],
    stats,
    actions,
    className = "",
}: DashboardLayoutProps) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
            {/* Background pattern */}
            <div className="fixed inset-0 pointer-events-none">
                <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.02]" />
            </div>

            {/* Main content */}
            <div className="relative">
                <div className="mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Header Section */}
                    <DashboardHeader
                        actions={actions}
                        breadcrumbs={breadcrumbs}
                        subtitle={subtitle}
                        title={title}
                    />

                    {/* Stats Section */}
                    {stats && (
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className="mb-8"
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                        >
                            {stats}
                        </motion.div>
                    )}

                    {/* Main Content Area */}
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className={`pb-12 ${className}`}
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                    >
                        <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 shadow-lg">
                            <div className="p-6">{children}</div>
                        </Card>
                    </motion.div>
                </div>
            </div>
        </div>
    );
}
