# Node y Next.js
node_modules/
.next/
.next-local/
out/
.vercel
*.tsbuildinfo
next-env.d.ts

# Otros lock files (mantener package-lock.json)
yarn.lock
pnpm-lock.yaml

# Archivos de entorno
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Archivos especiales de Windows
nul

# Python
venv
venv_linux
__pycache__
.ruff_cache
.pytest_cache
dontlookinhere/
INITIAL.md
php-agentic-framework/
logs/

# Archivos temporales
*.bak
*.tmp
*.log
*.sync-conflict-*
lint-report.txt

# Directorios obsoletos
memory-bank/
backup/
tasks/
.roo/
.taskmaster/

# Scripts de migración antiguos
scripts/migration/
scripts/sync-conflict/

# Archivos de configuración antiguos
claude_desktop_config.json
*.bat

# Documentación obsoleta
SUGGESTED_PROJECT_INSTRUCTIONS.md
FREE_MIGRATION_GUIDE.md
SUPABASE_SETUP.md
CSRF_ERROR_FIX.md
PRISMA_MIGRATION_WORKFLOW.md