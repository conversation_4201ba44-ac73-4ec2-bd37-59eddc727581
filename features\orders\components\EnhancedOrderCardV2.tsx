"use client";

import React, { useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>er,
    Card<PERSON>ooter,
    Chip,
    Avatar,
    Button,
    Tooltip,
    Badge,
    Divider,
    Progress,
} from "@heroui/react";
import {
    CalendarIcon,
    ClipboardDocumentListIcon,
    UserGroupIcon,
    EyeIcon,
    ExclamationTriangleIcon,
    ClockIcon,
    CubeIcon,
    ChevronDownIcon,
    ChevronUpIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";

import {
    adjustTimezoneOffset,
    getDaysDifference,
} from "@/shared/utils/dateUtils";
import { Order } from "@/features/orders/types/orders";

import { getStatusConfig } from "../utils/statusIcons";
import { calculateRiskScore } from "../utils/riskCalculator";

import RiskIndicator from "./RiskIndicator";

interface OrderCardProps {
    order: Order;
    onView: (order: Order) => void;
    viewMode?: "grid" | "list";
}

// Helper functions
const calculateTotalAssigned = (order: Order): number => {
    if (!order.assignments || order.assignments.length === 0) return 0;

    return order.assignments.reduce(
        (sum, assignment) => sum + (assignment.quantity || 0),
        0,
    );
};

const calculateTotalPieces = (order: Order): number => {
    if ((order as any).totalQuantity) return (order as any).totalQuantity;

    if (order.garments && order.garments.length > 0) {
        const total = order.garments.reduce((sum, garment) => {
            const garmentTotal =
                garment.sizes?.reduce((sizeSum, size) => {
                    return sizeSum + (size.totalQuantity || 0);
                }, 0) || 0;

            return sum + garmentTotal;
        }, 0);

        if (total > 0) return total;
    }

    if (order.assignments && order.assignments.length > 0) {
        return order.assignments.reduce(
            (sum, assignment) => sum + (assignment.quantity || 0),
            0,
        );
    }

    return 0;
};

const calculateAssignmentPercentage = (order: Order): number => {
    const total = calculateTotalPieces(order);

    if (total === 0) return 0;
    const assigned = calculateTotalAssigned(order);

    return Math.round((assigned / total) * 100);
};

export function EnhancedOrderCardV2({
    order,
    onView,
    viewMode = "grid",
}: OrderCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    // Calcular información de entrega
    const deliveryDateRaw = order.estimatedDeliveryDate || order.deliveryDate;
    const deliveryDate = deliveryDateRaw
        ? adjustTimezoneOffset(deliveryDateRaw)
        : null;
    const daysLeft = deliveryDate ? getDaysDifference(deliveryDate) : null;
    const isOverdue =
        daysLeft !== null && daysLeft < 0 && order.status !== "delivered";
    const isUrgent =
        daysLeft !== null && daysLeft <= 3 && order.status !== "delivered";

    // Configuración de estado
    const statusConfig = getStatusConfig(order.status);

    // Colores según estado
    const statusColors: Record<string, any> = {
        pending: "warning",
        in_progress: "primary",
        in_production: "secondary",
        delivered: "success",
        cancelled: "danger",
    };

    // Información de asignación
    const totalPieces = calculateTotalPieces(order);
    const totalAssigned = calculateTotalAssigned(order);
    const assignmentPercentage = calculateAssignmentPercentage(order);

    // Obtener icono de urgencia
    const getUrgencyIcon = () => {
        if (isOverdue)
            return <ExclamationTriangleIcon className="w-5 h-5 text-danger" />;
        if (isUrgent) return <ClockIcon className="w-5 h-5 text-warning" />;

        return <CalendarIcon className="w-5 h-5 text-gray-500" />;
    };

    // Obtener texto de urgencia
    const getUrgencyText = () => {
        if (!daysLeft) return null;
        if (isOverdue) return `${Math.abs(daysLeft)} días de retraso`;
        if (daysLeft === 0) return "Vence hoy";
        if (daysLeft === 1) return "Vence mañana";

        return `${daysLeft} días restantes`;
    };

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
        >
            <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="pb-3">
                    <div className="flex justify-between items-start w-full">
                        <div className="flex-1">
                            {/* Información principal de la orden */}
                            <div className="mb-2">
                                <div className="flex items-center gap-2 mb-1">
                                    <ClipboardDocumentListIcon className="w-5 h-5 text-primary" />
                                    <span className="font-bold text-lg">
                                        {order.cutOrder ||
                                            `ORD-${order.id.substring(0, 8)}`}
                                    </span>
                                    <Chip
                                        color={
                                            statusColors[order.status as any] ||
                                            "default"
                                        }
                                        size="sm"
                                        startContent={
                                            statusConfig.icon ? (
                                                <statusConfig.icon className="w-3 h-3" />
                                            ) : null
                                        }
                                        variant="flat"
                                    >
                                        {statusConfig.label}
                                    </Chip>
                                </div>

                                {/* Cliente */}
                                {order.customer && (
                                    <div className="flex items-center gap-2 ml-7">
                                        <span className="text-sm text-gray-600">
                                            Cliente:
                                        </span>
                                        <Chip
                                            color="default"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {order.customer.name}
                                        </Chip>
                                    </div>
                                )}
                            </div>

                            {/* Indicador de urgencia */}
                            {deliveryDate && (
                                <div
                                    className={`flex items-center gap-2 ${isOverdue ? "text-danger" : isUrgent ? "text-warning" : "text-gray-600"}`}
                                >
                                    {getUrgencyIcon()}
                                    <div className="flex flex-col">
                                        <span className="text-sm font-medium">
                                            {format(
                                                deliveryDate,
                                                "dd MMM yyyy",
                                                { locale: es },
                                            )}
                                        </span>
                                        {getUrgencyText() && (
                                            <span
                                                className={`text-xs ${isOverdue ? "font-bold" : ""}`}
                                            >
                                                {getUrgencyText()}
                                            </span>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Botón de expandir/colapsar */}
                        <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => setIsExpanded(!isExpanded)}
                        >
                            {isExpanded ? (
                                <ChevronUpIcon className="w-5 h-5" />
                            ) : (
                                <ChevronDownIcon className="w-5 h-5" />
                            )}
                        </Button>
                    </div>
                </CardHeader>

                <Divider />

                <CardBody className="py-4">
                    {/* Información siempre visible */}
                    <div className="space-y-3">
                        {/* Contratistas principales */}
                        <div className="flex items-center gap-2">
                            <UserGroupIcon className="w-4 h-4 text-gray-500" />
                            <span className="text-sm font-medium">
                                Contratistas:
                            </span>
                            {!order.assignments ||
                            order.assignments.length === 0 ? (
                                <span className="text-sm text-gray-400 italic">
                                    Sin asignar
                                </span>
                            ) : (
                                <div className="flex items-center gap-1">
                                    {order.assignments
                                        .slice(0, 3)
                                        .map((assignment, idx) => (
                                            <Tooltip
                                                key={idx}
                                                content={
                                                    <div>
                                                        <p className="font-medium">
                                                            {assignment
                                                                .contractor
                                                                ?.name ||
                                                                "Sin nombre"}
                                                        </p>
                                                        <p className="text-sm">
                                                            {assignment.quantity ||
                                                                0}{" "}
                                                            piezas
                                                        </p>
                                                    </div>
                                                }
                                            >
                                                <Avatar
                                                    className="cursor-help"
                                                    color="primary"
                                                    name={
                                                        assignment.contractor
                                                            ?.name || "?"
                                                    }
                                                    size="sm"
                                                />
                                            </Tooltip>
                                        ))}
                                    {order.assignments.length > 3 && (
                                        <Chip
                                            color="default"
                                            size="sm"
                                            variant="flat"
                                        >
                                            +{order.assignments.length - 3}
                                        </Chip>
                                    )}
                                </div>
                            )}
                        </div>

                        {/* Progreso de asignación */}
                        <div className="space-y-1">
                            <div className="flex justify-between items-center text-sm">
                                <span className="text-gray-600">
                                    Asignación:
                                </span>
                                <span
                                    className={`font-medium ${
                                        assignmentPercentage === 100
                                            ? "text-success"
                                            : assignmentPercentage >= 50
                                              ? "text-warning"
                                              : "text-danger"
                                    }`}
                                >
                                    {totalAssigned}/{totalPieces} pzs (
                                    {assignmentPercentage}%)
                                </span>
                            </div>
                            <Progress
                                className="w-full"
                                color={
                                    assignmentPercentage === 100
                                        ? "success"
                                        : assignmentPercentage >= 50
                                          ? "warning"
                                          : "danger"
                                }
                                size="sm"
                                value={assignmentPercentage}
                            />
                        </div>

                        {/* Mostrar partidas principales */}
                        {order.parts && order.parts.length > 0 && (
                            <div className="flex items-center gap-2">
                                <CubeIcon className="w-4 h-4 text-gray-500" />
                                <span className="text-sm font-medium">
                                    Partidas:
                                </span>
                                <div className="flex flex-wrap gap-1">
                                    {order.parts
                                        .slice(0, 3)
                                        .map((part: any) => (
                                            <Badge
                                                key={part.id}
                                                color="primary"
                                                size="sm"
                                                variant="flat"
                                            >
                                                {part.code}
                                            </Badge>
                                        ))}
                                    {order.parts.length > 3 && (
                                        <Badge
                                            color="default"
                                            size="sm"
                                            variant="flat"
                                        >
                                            +{order.parts.length - 3}
                                        </Badge>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Información expandible */}
                    <AnimatePresence>
                        {isExpanded && (
                            <motion.div
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                initial={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.2 }}
                            >
                                <Divider className="my-3" />
                                <div className="space-y-3">
                                    {/* Modelo y color */}
                                    {order.garments &&
                                        order.garments.length > 0 && (
                                            <div className="space-y-2">
                                                <span className="text-sm font-medium text-gray-700">
                                                    Productos:
                                                </span>
                                                {order.garments.map(
                                                    (garment, idx) => (
                                                        <div
                                                            key={idx}
                                                            className="ml-4 text-sm"
                                                        >
                                                            <span className="font-medium">
                                                                {garment.model
                                                                    ?.code ||
                                                                    "Sin modelo"}
                                                            </span>
                                                            {garment.color && (
                                                                <span className="text-gray-600">
                                                                    {" "}
                                                                    -{" "}
                                                                    {
                                                                        garment
                                                                            .color
                                                                            .name
                                                                    }
                                                                </span>
                                                            )}
                                                        </div>
                                                    ),
                                                )}
                                            </div>
                                        )}

                                    {/* Lista completa de partidas */}
                                    {order.parts && order.parts.length > 3 && (
                                        <div className="space-y-2">
                                            <span className="text-sm font-medium text-gray-700">
                                                Todas las partidas:
                                            </span>
                                            <div className="flex flex-wrap gap-1 ml-4">
                                                {order.parts.map(
                                                    (part: any) => (
                                                        <Badge
                                                            key={part.id}
                                                            color="primary"
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            {part.code}
                                                        </Badge>
                                                    ),
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Detalles de contratistas */}
                                    {order.assignments &&
                                        order.assignments.length > 0 && (
                                            <div className="space-y-2">
                                                <span className="text-sm font-medium text-gray-700">
                                                    Detalles de asignación:
                                                </span>
                                                <div className="ml-4 space-y-1">
                                                    {order.assignments.map(
                                                        (assignment, idx) => (
                                                            <div
                                                                key={idx}
                                                                className="flex justify-between items-center text-sm"
                                                            >
                                                                <span>
                                                                    {assignment
                                                                        .contractor
                                                                        ?.name ||
                                                                        "Sin nombre"}
                                                                </span>
                                                                <Badge
                                                                    color="default"
                                                                    size="sm"
                                                                    variant="flat"
                                                                >
                                                                    {assignment.quantity ||
                                                                        0}{" "}
                                                                    piezas
                                                                </Badge>
                                                            </div>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                    {/* Indicador de riesgo */}
                                    <div className="pt-2">
                                        <RiskIndicator
                                            assessment={calculateRiskScore(
                                                order,
                                            )}
                                            showDetails={true}
                                            size="sm"
                                        />
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </CardBody>

                <Divider />

                <CardFooter className="justify-between">
                    {/* Información adicional */}
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                        <span>{order.garments?.length || 0} prendas</span>
                        <span>•</span>
                        <span>
                            Creada{" "}
                            {formatDistanceToNow(
                                new Date(order.createdAt || new Date()),
                                {
                                    locale: es,
                                    addSuffix: true,
                                },
                            )}
                        </span>
                    </div>

                    {/* Acciones */}
                    <Button
                        color="primary"
                        size="sm"
                        startContent={<EyeIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={() => onView(order)}
                    >
                        Ver Detalles
                    </Button>
                </CardFooter>
            </Card>
        </motion.div>
    );
}
