# Redis Caching Implementation Guide (Phase 4)

## Overview
This phase implements a multi-layer Redis caching strategy with:
1. Type-based cache strategies (STAT<PERSON>, USER_SPECIFIC, COMPUTED)
2. Tag-based invalidation
3. Automatic API route caching
4. TTL management

## Setup

### 1. Install Redis Locally

Using Docker:
```bash
docker run -d -p 6379:6379 --name redis-lohari redis:alpine
```

Or install Redis directly:
```bash
# Windows: Use WSL or Redis for Windows
# Mac: brew install redis
# Linux: sudo apt-get install redis-server
```

### 2. Environment Configuration

Add to `.env.local`:
```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=lohari:

# Enable Redis caching
NEXT_PUBLIC_ENABLE_REDIS=true
```

## Cache Strategies

### STATIC Cache (24h TTL)
For data that rarely changes:
```typescript
import { staticCache } from '@/features/performance-monitoring/cache/strategies';

// Set static data
await staticCache.setStatic('products', 'catalog', productData);

// Get static data
const products = await staticCache.get({
  resource: 'products',
  identifier: 'catalog'
});
```

### USER_SPECIFIC Cache (5min TTL)
For per-user data:
```typescript
import { userCache } from '@/features/performance-monitoring/cache/strategies';

// Set user data
await userCache.setUserData(userId, 'preferences', userData);

// Get user data
const prefs = await userCache.getUserData(userId, 'preferences');
```

### COMPUTED Cache (1h TTL)
For expensive calculations:
```typescript
import { computedCache } from '@/features/performance-monitoring/cache/strategies';

// Get or compute
const report = await computedCache.getOrCompute(
  'reports',
  'monthly-sales',
  async () => generateExpensiveReport(),
  3600 // 1 hour TTL
);
```

## API Route Integration

### Automatic Caching with Middleware
```typescript
// app/api/orders/route.ts
import { withRedisCache } from '@/features/performance-monitoring/cache/middleware/cache-middleware';

export async function GET(request: NextRequest) {
  return withRedisCache(request, async () => {
    const orders = await getOrders();
    return NextResponse.json({ data: orders });
  });
}
```

### Manual Cache Control
```typescript
import { cacheInvalidator, CacheTags } from '@/features/performance-monitoring/cache/cache-invalidator';

// Invalidate on mutation
export async function POST(request: NextRequest) {
  const order = await createOrder(data);
  
  // Invalidate related caches
  await cacheInvalidator.invalidateTag(CacheTags.ORDERS);
  
  return NextResponse.json({ data: order });
}
```

## Tag-Based Invalidation

### Using Cache Tags
```typescript
// Tag cache entries
await cacheInvalidator.tagKey(cacheKey, [
  CacheTags.ORDERS,
  CacheTags.CUSTOMER(customerId)
]);

// Invalidate by tag
await cacheInvalidator.invalidateTag(CacheTags.ORDERS);

// Invalidate multiple tags
await cacheInvalidator.invalidateTags([
  CacheTags.ORDERS,
  CacheTags.CUSTOMERS
]);
```

### Predefined Tags
```typescript
CacheTags.ORDERS         // All orders
CacheTags.CUSTOMERS      // All customers
CacheTags.PRODUCTS       // All products
CacheTags.ASSIGNMENTS    // All assignments
CacheTags.USER(userId)   // Specific user
CacheTags.ORDER(orderId) // Specific order
```

## Cache Key Pattern

Keys follow the pattern: `{tenant}:{resource}:{identifier}:{version}`

Examples:
- `default:orders:list:v1`
- `user123:preferences:settings:v1`
- `tenant1:products:catalog:v2`

## Performance Monitoring

Cache operations are automatically tracked:
```typescript
// View cache metrics in performance dashboard
import { performanceTracker } from '@/features/performance-monitoring/utils/api-metrics';

const stats = performanceTracker.getQueryStats();
// Includes cache:get and cache:set metrics
```

## Testing Cache

### Health Check
```typescript
import { isRedisHealthy } from '@/features/performance-monitoring/cache/redis-client';

const healthy = await isRedisHealthy();
console.log('Redis status:', healthy ? 'Connected' : 'Disconnected');
```

### Clear Cache
```typescript
// Clear specific type
await staticCache.clearAll();

// Clear by tag
await cacheInvalidator.invalidateTag(CacheTags.ORDERS);
```

## Best Practices

1. **Cache Warming**: Pre-populate static data on startup
2. **TTL Strategy**: Use appropriate TTLs based on data volatility
3. **Tag Strategy**: Use hierarchical tags for granular invalidation
4. **Error Handling**: Cache failures should not break functionality
5. **Monitoring**: Track cache hit/miss ratios

## Expected Improvements

With Redis caching enabled:
- **API Response Time**: < 50ms for cached data
- **Database Load**: 70-80% reduction
- **Cache Hit Rate**: > 80% after warm-up
- **Throughput**: 5-10x increase

## Rollback

To disable Redis caching:
```env
NEXT_PUBLIC_ENABLE_REDIS=false
```

All cache operations will return null/false, and the system will fall back to direct database queries.
