"use client";

import React, { useState, useEffect } from "react";
import { Select, SelectItem, Avatar, Chip } from "@heroui/react";
import { BuildingOfficeIcon, UserGroupIcon } from "@heroicons/react/24/outline";

import { getAllCustomersWithHierarchy } from "@/features/customers/actions";

interface Customer {
    id: string;
    name: string;
    displayName?: string | null;
    parentId?: string | null;
    subCustomers?: Customer[];
    _count?: {
        orders: number;
        packings: number;
        subCustomers: number;
    };
}

interface CustomerSelectorProps {
    value?: {
        customerId?: string;
        subCustomerId?: string;
    };
    onChange: (value: { customerId: string; subCustomerId?: string }) => void;
    label?: string;
    placeholder?: string;
    isRequired?: boolean;
    isDisabled?: boolean;
    includeSubCustomers?: boolean;
}

export function CustomerSelector({
    value,
    onChange,
    label = "Cliente",
    placeholder = "Seleccionar cliente",
    isRequired = false,
    isDisabled = false,
    includeSubCustomers = true,
}: CustomerSelectorProps) {
    const [customers, setCustomers] = useState<Customer[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedCustomerId, setSelectedCustomerId] = useState(
        value?.customerId || "",
    );
    const [selectedSubCustomerId, setSelectedSubCustomerId] = useState(
        value?.subCustomerId || "",
    );

    useEffect(() => {
        loadCustomers();
    }, []);

    useEffect(() => {
        if (value?.customerId !== selectedCustomerId) {
            setSelectedCustomerId(value?.customerId || "");
            setSelectedSubCustomerId(value?.subCustomerId || "");
        }
    }, [value]);

    const loadCustomers = async () => {
        try {
            const result = await getAllCustomersWithHierarchy();

            if (result.success && result.data) {
                setCustomers(result.data);
            }
        } catch (error) {
            console.error("Error loading customers:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const getSelectedCustomer = () => {
        return customers.find((c) => c.id === selectedCustomerId);
    };

    const handleCustomerChange = (customerId: string) => {
        setSelectedCustomerId(customerId);
        setSelectedSubCustomerId("");
        onChange({ customerId });
    };

    const handleSubCustomerChange = (subCustomerId: string) => {
        setSelectedSubCustomerId(subCustomerId);
        onChange({
            customerId: selectedCustomerId,
            subCustomerId: subCustomerId || undefined,
        });
    };

    const selectedCustomer = getSelectedCustomer();

    return (
        <div className="space-y-4">
            <Select
                isDisabled={isDisabled}
                isLoading={isLoading}
                isRequired={isRequired}
                label={label}
                placeholder={placeholder}
                selectedKeys={selectedCustomerId ? [selectedCustomerId] : []}
                startContent={
                    <BuildingOfficeIcon className="w-4 h-4 text-gray-400" />
                }
                onSelectionChange={(keys) => {
                    const key = Array.from(keys)[0] as string;

                    handleCustomerChange(key);
                }}
            >
                {customers.map((customer) => (
                    <SelectItem
                        key={customer.id}
                        endContent={
                            <div className="flex items-center gap-2">
                                {customer._count?.subCustomers ? (
                                    <Chip size="sm" variant="flat">
                                        {customer._count.subCustomers} sub
                                    </Chip>
                                ) : null}
                            </div>
                        }
                        startContent={
                            <Avatar
                                className="w-6 h-6"
                                name={customer.name}
                                size="sm"
                            />
                        }
                        textValue={customer.displayName || customer.name}
                    >
                        <div>
                            <div className="font-medium">
                                {customer.displayName || customer.name}
                            </div>
                            {customer.displayName &&
                                customer.displayName !== customer.name && (
                                    <div className="text-xs text-gray-400">
                                        {customer.name}
                                    </div>
                                )}
                            {customer._count && (
                                <div className="text-xs text-gray-500">
                                    {customer._count.orders} órdenes •{" "}
                                    {customer._count.packings} packings
                                </div>
                            )}
                        </div>
                    </SelectItem>
                ))}
            </Select>

            {includeSubCustomers &&
                selectedCustomer?.subCustomers &&
                selectedCustomer.subCustomers.length > 0 && (
                    <Select
                        isDisabled={isDisabled}
                        label="Subcliente (Opcional)"
                        placeholder="Seleccionar subcliente"
                        selectedKeys={
                            selectedSubCustomerId ? [selectedSubCustomerId] : []
                        }
                        startContent={
                            <UserGroupIcon className="w-4 h-4 text-gray-400" />
                        }
                        onSelectionChange={(keys) => {
                            const key = Array.from(keys)[0] as string;

                            handleSubCustomerChange(key);
                        }}
                    >
                        <SelectItem key="" textValue="Sin subcliente">
                            <div className="text-gray-500">
                                Sin subcliente específico
                            </div>
                        </SelectItem>
                        {
                            selectedCustomer.subCustomers.map((subCustomer) => (
                                <SelectItem
                                    key={subCustomer.id}
                                    startContent={
                                        <Avatar
                                            className="w-6 h-6"
                                            color="secondary"
                                            name={subCustomer.name}
                                            size="sm"
                                        />
                                    }
                                    textValue={
                                        subCustomer.displayName ||
                                        subCustomer.name
                                    }
                                >
                                    <div>
                                        <div className="font-medium">
                                            {subCustomer.displayName ||
                                                subCustomer.name}
                                        </div>
                                        {subCustomer.displayName &&
                                            subCustomer.displayName !==
                                                subCustomer.name && (
                                                <div className="text-xs text-gray-400">
                                                    {subCustomer.name}
                                                </div>
                                            )}
                                        {subCustomer._count && (
                                            <div className="text-xs text-gray-500">
                                                {subCustomer._count.orders}{" "}
                                                órdenes •{" "}
                                                {subCustomer._count.packings}{" "}
                                                packings
                                            </div>
                                        )}
                                    </div>
                                </SelectItem>
                            )) as any
                        }
                    </Select>
                )}
        </div>
    );
}
