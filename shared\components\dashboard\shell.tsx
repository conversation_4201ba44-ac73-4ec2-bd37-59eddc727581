"use client";

import React from "react";

/**
 * Componente Shell para el dashboard
 * Contiene la estructura principal de las páginas del dashboard
 */
export function DashboardShell({ children }: { children: React.ReactNode }) {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                    <div className="p-6">{children}</div>
                </div>
            </div>
        </div>
    );
}
