# Order Modal Schema Alignment - Migration Guide

## Overview

This migration aligns the OrderDetailModal component with the actual Prisma database schema, removing fields that don't exist in the database and preparing for future enhancements.

## What Changed

### 1. Removed Non-Existent Fields
The following fields were being displayed but don't exist in the Customer model:
- ❌ `phone` 
- ❌ `address`
- ❌ `company`

These have been removed from the UI to prevent confusion.

### 2. Fixed Property Names
Corrected property names to match Prisma schema:
- `OrderGarment` → `garments`
- `OrderAssignment` → `assignments`
- `deliveryDate` → `estimatedDeliveryDate`

### 3. Added "Coming Soon" Card
A informative card now appears in the modal indicating that additional customer information (phone, address, company) will be available in a future release.

### 4. Deprecation Warnings
Accessing removed fields now triggers deprecation warnings in development mode to help identify code that needs updating.

## For Developers

### Breaking Changes
1. Any code accessing `customer.phone`, `customer.address`, or `customer.company` will now return `undefined`
2. Properties `OrderGarment` and `OrderAssignment` no longer exist - use `garments` and `assignments`

### Console Warnings
In development mode, you'll see warnings like:
```
⚠️ DEPRECATED: OrderCustomer.phone
Use This field will be available after DB migration instead.
Will be removed after 2025-07-01.
See: https://github.com/lohari/docs/migrations/customer-fields
```

### Feature Flags
Test extended fields (when implemented) using:
```bash
NEXT_PUBLIC_EXTENDED_CUSTOMER=true npm run dev
```

Control rollout percentage:
```bash
NEXT_PUBLIC_ROLLOUT=50 npm run dev  # 50% of users
```

## Migration Steps

### For Existing Code
1. Search for uses of removed fields:
   ```bash
   grep -r "customer.*phone" src/
   grep -r "customer.*address" src/
   grep -r "customer.*company" src/
   ```

2. Replace with appropriate handling:
   ```typescript
   // Before
   const phone = order.customer.phone || "No registrado";
   
   // After
   const phone = undefined; // Will be available after DB migration
   ```

3. Update property names:
   ```typescript
   // Before
   order.OrderGarment.length
   
   // After
   order.garments.length
   ```

### For New Features
When the database is updated to include these fields:

1. Run Prisma migration to add fields
2. Enable feature flag: `CUSTOMER_EXTENDED_FIELDS=true`
3. Remove deprecation warnings from code
4. Update UI components to display new fields

## Rollback Instructions

If you need to rollback this change:

```bash
# Return to the state before migration
git checkout pre-schema-alignment

# Or revert specific commits
git revert checkpoint-1-types..checkpoint-6-docs
```

## Testing

Run tests to ensure everything works:
```bash
npm test -- OrderDetailModal
npm test -- useOrderModalAdapter
```

## Future Database Migration

When ready to add the missing fields:

```prisma
// prisma/schema.prisma
model Customer {
  id        String   @id @default(cuid())
  name      String   @unique
  phone     String?  // NEW
  email     String?  // NEW
  address   String?  // NEW
  company   String?  // NEW
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orders    Order[]
}
```

Then run:
```bash
npx prisma migrate dev --name add-customer-fields
```

## Questions?

Contact the development team or create an issue in the repository.
