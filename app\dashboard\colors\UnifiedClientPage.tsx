"use client";

import type { SortOption } from "@/shared/components/dashboard";

import React, { useState, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    SwatchIcon,
    SunIcon,
    SparklesIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import {
    useColors,
    useDeleteColor,
    Color,
} from "@/features/colors/hooks/useColor";
import { Chip, Progress, Tooltip } from "@/shared/components/ui/hero-ui-client";
import { DeleteColorModal } from "@/features/colors/components";

export default function UnifiedColorsPage() {
    const router = useRouter();
    const { colors = [], isLoading, mutate } = useColors();
    const { deleteColor } = useDeleteColor();

    // Estado para el modal de eliminación
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [colorToDelete, setColorToDelete] = useState<Color | null>(null);

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "name-asc",
        label: "Nombre A-Z",
        field: "name",
        direction: "asc",
    });

    // Función para manejar la confirmación de eliminación
    const handleDeleteConfirm = async (colorId: string) => {
        const result = await deleteColor(colorId);

        if (result.success) {
            mutate();
            setDeleteModalOpen(false);
            setColorToDelete(null);
        } else {
            throw new Error(result.error || "Error al eliminar");
        }
    };

    // Calcular estadísticas
    const stats = useMemo(() => {
        const usedColors = colors.filter(
            (c: Color) => c._count?.garments && c._count.garments > 0,
        );
        const unusedColors = colors.filter(
            (c: Color) => !c._count?.garments || c._count.garments === 0,
        );

        // Encontrar color más popular
        const mostPopular = colors.reduce((prev: Color, current: Color) => {
            const prevCount = prev._count?.garments || 0;
            const currentCount = current._count?.garments || 0;

            return currentCount > prevCount ? current : prev;
        }, colors[0]);

        // Colores agregados recientemente
        const recentColors = colors.filter((c: Color) => {
            const createdDate = new Date(c.createdAt);
            const oneWeekAgo = new Date();

            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

            return createdDate > oneWeekAgo;
        });

        return [
            {
                title: "Total Colores",
                value: colors.length,
                icon: <SwatchIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "En catálogo",
            },
            {
                title: "En Uso",
                value: usedColors.length,
                icon: <SunIcon className="w-6 h-6" />,
                color: "success" as const,
                change: Math.round((usedColors.length / colors.length) * 100),
                changeLabel: "del total",
            },
            {
                title: "Más Popular",
                value: mostPopular?.name || "-",
                description: mostPopular
                    ? `${mostPopular._count?.garments || 0} prendas`
                    : "",
                color: "warning" as const,
                icon: <SparklesIcon className="w-6 h-6" />,
            },
            {
                title: "Nuevos",
                value: recentColors.length,
                icon: <ClockIcon className="w-6 h-6" />,
                color: "default" as const,
                changeLabel: "esta semana",
            },
        ];
    }, [colors]);

    // Calcular porcentaje de uso
    const calculateUsagePercentage = useCallback(
        (color: Color) => {
            if (!color._count?.garments) return 0;
            const maxUsage = Math.max(
                ...colors.map((c: Color) => c._count?.garments || 0),
            );

            return maxUsage > 0 ? (color._count.garments / maxUsage) * 100 : 0;
        },
        [colors],
    );

    // Columnas de la tabla
    const columns = [
        {
            key: "preview",
            label: "Color",
            render: (color: Color) => (
                <div className="flex items-center gap-3">
                    <Tooltip content={color.hexCode || color.name}>
                        <div
                            className="w-10 h-10 rounded-lg border-2 border-gray-300 dark:border-gray-600 shadow-sm"
                            style={{ backgroundColor: color.hexCode || "#ccc" }}
                        />
                    </Tooltip>
                    <span className="font-medium">{color.name}</span>
                </div>
            ),
        },
        {
            key: "hex",
            label: "Código HEX",
            render: (color: Color) => (
                <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {color.hexCode || "-"}
                </code>
            ),
        },
        {
            key: "usage",
            label: "Uso",
            render: (color: Color) => {
                const percentage = calculateUsagePercentage(color);
                const count = color._count?.garments || 0;

                return (
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <Progress
                                className="max-w-[100px]"
                                color={
                                    percentage > 50
                                        ? "success"
                                        : percentage > 20
                                          ? "warning"
                                          : "default"
                                }
                                size="sm"
                                value={percentage}
                            />
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                {count} {count === 1 ? "prenda" : "prendas"}
                            </span>
                        </div>
                    </div>
                );
            },
        },
        {
            key: "createdAt",
            label: "Creado",
            sortable: true,
            render: (color: Color) => (
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    {format(new Date(color.createdAt), "dd MMM yyyy", {
                        locale: es,
                    })}
                </span>
            ),
        },
        {
            key: "status",
            label: "Estado",
            render: (color: Color) => {
                const isUsed =
                    color._count?.garments && color._count.garments > 0;

                return (
                    <Chip
                        color={isUsed ? "success" : "default"}
                        size="sm"
                        variant="flat"
                    >
                        {isUsed ? "En uso" : "Sin usar"}
                    </Chip>
                );
            },
        },
    ];

    // Acciones de la tabla
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (color: Color) => {
                router.push(`/dashboard/colors/${color.id}/details`);
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (color: Color) => {
                router.push(`/dashboard/colors/${color.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (color: Color) => {
                if (color._count?.garments && color._count.garments > 0) {
                    addToast({
                        title: "No se puede eliminar",
                        description: "Este color está siendo usado en prendas",
                        color: "warning",
                    });

                    return;
                }

                setColorToDelete(color);
                setDeleteModalOpen(true);
            },
            color: "danger" as const,
            isDisabled: (color: Color) =>
                !!(color._count?.garments && color._count.garments > 0),
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "used", label: "En uso" },
                { value: "unused", label: "Sin usar" },
            ],
        },
        {
            key: "createdAt",
            label: "Fecha de Creación",
            type: "date" as const,
            placeholder: "Seleccionar fecha",
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "name-asc",
            label: "Nombre A-Z",
            field: "name",
            direction: "asc" as const,
        },
        {
            key: "name-desc",
            label: "Nombre Z-A",
            field: "name",
            direction: "desc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Más recientes",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguos",
            field: "createdAt",
            direction: "asc" as const,
        },
        {
            key: "usage-desc",
            label: "Más usado",
            field: "_count.garments",
            direction: "desc" as const,
        },
        {
            key: "usage-asc",
            label: "Menos usado",
            field: "_count.garments",
            direction: "asc" as const,
        },
    ];

    // Filtrar datos
    const filteredData = useMemo(() => {
        let filtered = [...colors];

        // Búsqueda
        if (searchValue) {
            filtered = filtered.filter(
                (color) =>
                    color.name
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()) ||
                    color.hexCode
                        ?.toLowerCase()
                        .includes(searchValue.toLowerCase()),
            );
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            if (filterValues.status === "used") {
                filtered = filtered.filter(
                    (c: Color) => c._count?.garments && c._count.garments > 0,
                );
            } else if (filterValues.status === "unused") {
                filtered = filtered.filter(
                    (c: Color) =>
                        !c._count?.garments || c._count.garments === 0,
                );
            }
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [colors, searchValue, filterValues, currentSort]);

    return (
        <>
            <CrudListTemplate
                // Layout
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key] && filterValues[key] !== "all",
                    ).length
                }
                columns={columns}
                emptyContent="No hay colores registrados"
                filterValues={filterValues}
                isLoading={isLoading}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Gestión de paleta de colores"
                title="Colores"
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={actions}
                // Create
                createRoute="/dashboard/colors/new"
                breadcrumbs={[{ label: "Colores" }]}
                // Stats
                stats={stats}
                createLabel="Nuevo Color"
                // Filters
                filters={filters}
                currentSort={currentSort}
                // Table
                data={filteredData}
            />

            {/* Modal de eliminación */}
            <DeleteColorModal
                color={colorToDelete}
                isOpen={deleteModalOpen}
                onClose={() => {
                    setDeleteModalOpen(false);
                    setColorToDelete(null);
                }}
                onConfirm={handleDeleteConfirm}
            />
        </>
    );
}
