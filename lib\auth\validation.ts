import { z } from "zod";

// Schema para login
export const loginSchema = z.object({
    email: z.string().min(1, "El email es requerido").email("Email inválido"),
    password: z
        .string()
        .min(1, "La contraseña es requerida")
        .min(6, "La contraseña debe tener al menos 6 caracteres"),
    rememberMe: z.boolean().optional(),
});

// Schema para registro
export const registerSchema = z
    .object({
        name: z
            .string()
            .min(1, "El nombre es requerido")
            .min(2, "El nombre debe tener al menos 2 caracteres"),
        email: z
            .string()
            .min(1, "El email es requerido")
            .email("Email inválido"),
        password: z
            .string()
            .min(1, "La contraseña es requerida")
            .min(8, "La contraseña debe tener al menos 8 caracteres")
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                "La contraseña debe contener al menos una mayúscula, una minúscula y un número",
            ),
        confirmPassword: z.string().min(1, "Confirma tu contraseña"),
        acceptTerms: z.boolean().refine((val) => val === true, {
            message: "Debes aceptar los términos y condiciones",
        }),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Las contraseñas no coinciden",
        path: ["confirmPassword"],
    });

// Schema para recuperar contraseña
export const forgotPasswordSchema = z.object({
    email: z.string().min(1, "El email es requerido").email("Email inválido"),
});

// Schema para resetear contraseña
export const resetPasswordSchema = z
    .object({
        password: z
            .string()
            .min(1, "La contraseña es requerida")
            .min(8, "La contraseña debe tener al menos 8 caracteres")
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                "La contraseña debe contener al menos una mayúscula, una minúscula y un número",
            ),
        confirmPassword: z.string().min(1, "Confirma tu contraseña"),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Las contraseñas no coinciden",
        path: ["confirmPassword"],
    });

// Types inferidos de los schemas
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
