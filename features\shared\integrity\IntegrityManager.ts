import type { OperationLog, Prisma } from "@prisma/client";

import { prisma } from "@/shared/lib/prisma";

// Types
interface OperationContext {
    type: string;
    userId: string;
    metadata?: Record<string, any>;
}

interface OptimisticLockError extends Error {
    name: "OptimisticLockError";
    expectedVersion: number;
    actualVersion: number;
}

// Helper function to extract entity IDs from operation results
function extractEntityIds(result: any): string[] {
    const ids: string[] = [];

    if (Array.isArray(result)) {
        ids.push(...result.map((item) => item.id || item));
    } else if (result && typeof result === "object") {
        if (result.id) ids.push(result.id);
        if (result.assignmentId) ids.push(`assignment_${result.assignmentId}`);
        if (result.remissionId) ids.push(`remission_${result.remissionId}`);
    }

    return ids.filter(Boolean);
}

// Version control helper for optimistic locking
export async function checkVersion<T extends { id: string; version: number }>(
    model: any,
    id: string,
    expectedVersion: number,
): Promise<void> {
    const current = await model.findUnique({
        where: { id },
        select: { version: true },
    });

    if (!current) {
        throw new Error(`Entity with id ${id} not found`);
    }

    if (current.version !== expectedVersion) {
        const error = new Error(
            `Version mismatch: expected ${expectedVersion}, got ${current.version}`,
        ) as OptimisticLockError;

        error.name = "OptimisticLockError";
        error.expectedVersion = expectedVersion;
        error.actualVersion = current.version;
        throw error;
    }
}

/**
 * IntegrityManager provides transaction integrity and audit trail capabilities
 * for complex operations that may span multiple database transactions.
 */
export class IntegrityManager {
    /**
     * Execute an operation with integrity guarantees
     * @param context - Operation context including type, user, and metadata
     * @param operation - The main operation to execute
     * @param compensate - Optional compensation function if operation fails
     * @returns The result of the operation
     */
    async executeWithIntegrity<T>(
        context: OperationContext,
        operation: () => Promise<T>,
        compensate?: (log: OperationLog) => Promise<void>,
    ): Promise<T> {
        // Create operation log to track the operation
        const log = await prisma.operationLog.create({
            data: {
                operationType: context.type,
                userId: context.userId,
                status: "IN_PROGRESS",
                metadata: context.metadata || {},
                entityIds: [],
            },
        });

        try {
            // Execute the main operation
            const result = await operation();

            // Extract entity IDs from the result
            const entityIds = extractEntityIds(result);

            // Mark operation as completed
            await prisma.operationLog.update({
                where: { id: log.id },
                data: {
                    status: "COMPLETED",
                    completedAt: new Date(),
                    entityIds,
                },
            });

            return result;
        } catch (error) {
            // Mark operation as failed
            await prisma.operationLog.update({
                where: { id: log.id },
                data: {
                    status: "FAILED",
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                },
            });

            // Execute compensation if provided
            if (compensate) {
                try {
                    await compensate(log);

                    // Mark as compensated
                    await prisma.operationLog.update({
                        where: { id: log.id },
                        data: {
                            status: "COMPENSATED",
                            compensatedAt: new Date(),
                        },
                    });
                } catch (compError) {
                    console.error("Compensation failed:", compError);
                    // Alert monitoring system
                    // In production, this would trigger alerts

                    // Update log with compensation failure
                    await prisma.operationLog.update({
                        where: { id: log.id },
                        data: {
                            error: `${error instanceof Error ? error.message : "Unknown error"} | Compensation failed: ${compError instanceof Error ? compError.message : "Unknown compensation error"}`,
                        },
                    });
                }
            }

            // Re-throw the original error
            throw error;
        }
    }

    /**
     * Find operations that are stuck in IN_PROGRESS state
     * @param stuckThresholdMs - Time in milliseconds to consider an operation stuck (default: 5 minutes)
     */
    async findStuckOperations(
        stuckThresholdMs: number = 5 * 60 * 1000,
    ): Promise<OperationLog[]> {
        const threshold = new Date(Date.now() - stuckThresholdMs);

        return await prisma.operationLog.findMany({
            where: {
                status: "IN_PROGRESS",
                startedAt: {
                    lt: threshold,
                },
            },
            orderBy: {
                startedAt: "asc",
            },
        });
    }

    /**
     * Get operation statistics for monitoring
     */
    async getOperationStats(
        operationType?: string,
        timeRangeHours: number = 24,
    ) {
        const since = new Date(Date.now() - timeRangeHours * 60 * 60 * 1000);

        const where: Prisma.OperationLogWhereInput = {
            startedAt: { gte: since },
        };

        if (operationType) {
            where.operationType = operationType;
        }

        const [total, completed, failed, compensated, inProgress] =
            await Promise.all([
                prisma.operationLog.count({ where }),
                prisma.operationLog.count({
                    where: { ...where, status: "COMPLETED" },
                }),
                prisma.operationLog.count({
                    where: { ...where, status: "FAILED" },
                }),
                prisma.operationLog.count({
                    where: { ...where, status: "COMPENSATED" },
                }),
                prisma.operationLog.count({
                    where: { ...where, status: "IN_PROGRESS" },
                }),
            ]);

        return {
            total,
            completed,
            failed,
            compensated,
            inProgress,
            successRate: total > 0 ? (completed / total) * 100 : 0,
            compensationRate: failed > 0 ? (compensated / failed) * 100 : 0,
        };
    }
}

// Export a singleton instance
export const integrityManager = new IntegrityManager();

// Compensation strategies
export const compensationStrategies = {
    /**
     * Compensate for failed assignment batch creation
     */
    async compensateAssignmentBatch(log: OperationLog): Promise<void> {
        const assignmentIds = log.entityIds
            .filter((id) => id.startsWith("assignment_"))
            .map((id) => id.replace("assignment_", ""));

        if (assignmentIds.length === 0) return;

        // Soft delete created assignments
        await prisma.assignment.updateMany({
            where: {
                id: { in: assignmentIds },
                createdAt: { gte: log.startedAt },
            },
            data: {
                status: "CANCELLED",
                cancelledAt: new Date(),
                cancelReason: "Transaction rollback",
            },
        });

        // Cancel any created remissions
        await prisma.remission.updateMany({
            where: {
                // assignmentId: { in: assignmentIds }, // TODO: Fix schema relationship
                createdAt: { gte: log.startedAt },
            },
            data: {
                status: "CANCELLED",
            },
        });
    },

    /**
     * Compensate for failed remission creation
     */
    async compensateRemissionCreation(log: OperationLog): Promise<void> {
        const remissionIds = log.entityIds
            .filter((id) => id.startsWith("remission_"))
            .map((id) => id.replace("remission_", ""));

        if (remissionIds.length === 0) return;

        // Delete created remissions
        await prisma.remission.deleteMany({
            where: {
                id: { in: remissionIds },
                createdAt: { gte: log.startedAt },
            },
        });
    },
};
