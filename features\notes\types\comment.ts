import { z } from "zod";
import { NoteComment, User } from "@prisma/client";

/**
 * Schema de validación para crear un comentario
 */
export const createCommentSchema = z.object({
    content: z
        .string()
        .min(1, "El contenido es requerido")
        .max(1000, "El comentario no puede exceder 1000 caracteres"),
    noteId: z.string().cuid("ID de nota inválido"),
    parentId: z.string().cuid("ID de comentario padre inválido").optional(),
});

/**
 * Schema de validación para actualizar un comentario
 */
export const updateCommentSchema = z.object({
    commentId: z.string().cuid("ID de comentario inválido"),
    content: z
        .string()
        .min(1, "El contenido es requerido")
        .max(1000, "El comentario no puede exceder 1000 caracteres"),
});

/**
 * Tipo para los datos de entrada al crear un comentario
 */
export type CreateCommentInput = z.infer<typeof createCommentSchema>;

/**
 * Tipo para los datos de entrada al actualizar un comentario
 */
export type UpdateCommentInput = z.infer<typeof updateCommentSchema>;

/**
 * Tipo extendido de comentario con información del autor
 */
export interface CommentWithAuthor extends NoteComment {
    author: Pick<User, "id" | "name" | "email" | "image">;
    replies?: CommentWithAuthor[];
    _count?: {
        replies: number;
    };
}

/**
 * Resultado de una operación de comentario
 */
export interface CommentActionResult {
    success: boolean;
    data?: CommentWithAuthor | { deleted: boolean };
    error?: string;
    errorCode?: string;
}

/**
 * Filtros para listar comentarios
 */
export interface CommentFilters {
    noteId: string;
    page?: number;
    pageSize?: number;
    includeReplies?: boolean;
    sortOrder?: "asc" | "desc";
}

/**
 * Resultado paginado de comentarios
 */
export interface PaginatedComments {
    comments: CommentWithAuthor[];
    totalCount: number;
    hasMore: boolean;
    page: number;
    pageSize: number;
    totalPages: number;
}
