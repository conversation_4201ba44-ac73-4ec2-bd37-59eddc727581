"use client";

import { useState, useEffect, useMemo } from "react";
import { Autocomplete, AutocompleteItem, Avatar } from "@heroui/react";
import { Building2, ChevronRight } from "lucide-react";

import { CustomerWithChildren } from "@/lib/types/packing";

import { getCustomersWithSubClients } from "../actions/customer-actions";

interface CustomerSubClientSelectorProps {
    value?: { customerId?: string; subCustomerId?: string };
    onChange: (value: { customerId: string; subCustomerId?: string }) => void;
    isRequired?: boolean;
    isDisabled?: boolean;
    error?: string;
}

export function CustomerSubClientSelector({
    value,
    onChange,
    isRequired = false,
    isDisabled = false,
    error,
}: CustomerSubClientSelectorProps) {
    const [customers, setCustomers] = useState<CustomerWithChildren[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedKey, setSelectedKey] = useState<string>("");

    useEffect(() => {
        loadCustomers();
    }, []);

    useEffect(() => {
        if (value?.customerId) {
            const key = value.subCustomerId
                ? `${value.customerId}:${value.subCustomerId}`
                : value.customerId;

            setSelectedKey(key);
        }
    }, [value]);

    const loadCustomers = async () => {
        try {
            setLoading(true);
            const data = await getCustomersWithSubClients();

            setCustomers(data);
        } catch (error) {
            console.error("Error loading customers:", error);
        } finally {
            setLoading(false);
        }
    };

    const items = useMemo(() => {
        const result: Array<{
            key: string;
            label: string;
            customerId: string;
            subCustomerId?: string;
            isParent: boolean;
        }> = [];

        customers.forEach((customer) => {
            // Add parent customer
            result.push({
                key: customer.id,
                label: customer.name,
                customerId: customer.id,
                isParent: true,
            });

            // Add sub-clients
            customer.subCustomers?.forEach((child) => {
                result.push({
                    key: `${customer.id}:${child.id}`,
                    label: `${customer.name} > ${child.name}`,
                    customerId: customer.id,
                    subCustomerId: child.id,
                    isParent: false,
                });
            });
        });

        return result;
    }, [customers]);

    const handleSelectionChange = (key: React.Key | null) => {
        if (!key) {
            onChange({ customerId: "" });

            return;
        }

        const keyString = String(key);
        const item = items.find((i) => i.key === keyString);

        if (item) {
            onChange({
                customerId: item.customerId,
                subCustomerId: item.subCustomerId,
            });
        }
    };

    return (
        <Autocomplete
            className="w-full"
            errorMessage={error}
            isDisabled={isDisabled || loading}
            isLoading={loading}
            isRequired={isRequired}
            label="Cliente / Subcliente"
            placeholder="Seleccione el cliente"
            selectedKey={selectedKey}
            startContent={<Building2 className="w-4 h-4 text-gray-400" />}
            onSelectionChange={handleSelectionChange}
        >
            {items.map((item) => (
                <AutocompleteItem
                    key={item.key}
                    startContent={
                        <Avatar
                            classNames={{
                                base: item.isParent
                                    ? "bg-primary"
                                    : "bg-secondary",
                                icon: "text-white",
                            }}
                            icon={<Building2 className="w-4 h-4" />}
                            size="sm"
                        />
                    }
                    textValue={item.label}
                >
                    <div className="flex items-center gap-1">
                        {!item.isParent && (
                            <ChevronRight className="w-3 h-3 text-gray-400 ml-2" />
                        )}
                        <span className={item.isParent ? "font-semibold" : ""}>
                            {item.isParent
                                ? item.label
                                : item.label.split(" > ")[1]}
                        </span>
                    </div>
                </AutocompleteItem>
            ))}
        </Autocomplete>
    );
}
