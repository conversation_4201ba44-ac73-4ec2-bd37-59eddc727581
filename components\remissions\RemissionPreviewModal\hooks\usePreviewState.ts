"use client";

import { useState, useCallback, useMemo } from "react";

import { RemissionPreview } from "@/types/remission";

export function usePreviewState(initialData: RemissionPreview) {
    const [editMode, setEditMode] = useState(false);
    const [orientation, setOrientation] = useState<"portrait" | "landscape">(
        "portrait",
    );
    const [zoom, setZoom] = useState(100);
    const [changes, setChanges] = useState<Partial<RemissionPreview>>({});

    const handleEdit = useCallback((field: string, value: any) => {
        setChanges((prev) => {
            const newChanges = { ...prev };

            // Handle nested fields (e.g., 'contractor.name')
            if (field.includes(".")) {
                const [parent, child] = field.split(".");

                newChanges[parent as keyof RemissionPreview] = {
                    ...((prev[parent as keyof RemissionPreview] as any) || {}),
                    [child]: value,
                };
            } else {
                (newChanges as any)[field] = value;
            }

            return newChanges;
        });
    }, []);

    const hasChanges = useMemo(() => {
        return Object.keys(changes).length > 0;
    }, [changes]);

    const resetChanges = useCallback(() => {
        setChanges({});
    }, []);

    const mergedData = useMemo(() => {
        if (!hasChanges) return initialData;

        // Deep merge changes with initial data
        return {
            ...initialData,
            ...changes,
            contractor: {
                ...initialData.contractor,
                ...(changes.contractor || {}),
            },
            items: changes.items || initialData.items,
            internalNotes:
                (changes as any).internalNotes ??
                (initialData as any).internalNotes,
            externalNotes:
                (changes as any).externalNotes ??
                (initialData as any).externalNotes,
        } as RemissionPreview;
    }, [initialData, changes, hasChanges]);

    return {
        editMode,
        setEditMode,
        orientation,
        setOrientation,
        zoom,
        setZoom,
        changes,
        handleEdit,
        hasChanges,
        resetChanges,
        mergedData,
    };
}
