/**
 * Size Repository Interface
 *
 * Defines the contract for size data access.
 * This interface belongs to the domain layer and has no knowledge of infrastructure.
 */

import { Size } from "../entities/size.entity";

export interface FindSizesOptions {
    isActive?: boolean;
    category?: "numeric" | "alpha" | "custom";
    includeDeleted?: boolean;
    orderBy?: "order" | "name" | "createdAt" | "updatedAt";
    orderDirection?: "asc" | "desc";
    limit?: number;
    offset?: number;
}

export interface SizeRepositoryInterface {
    /**
     * Find a size by its ID
     */
    findById(id: string): Promise<Size | null>;

    /**
     * Find a size by its name
     */
    findByName(name: string): Promise<Size | null>;

    /**
     * Find all sizes matching the given criteria
     */
    findAll(options?: FindSizesOptions): Promise<Size[]>;

    /**
     * Find sizes by category
     */
    findByCategory(category: "numeric" | "alpha" | "custom"): Promise<Size[]>;

    /**
     * Find active sizes ordered by their order value
     */
    findActiveOrdered(): Promise<Size[]>;

    /**
     * Check if a size name already exists
     */
    exists(name: string, excludeId?: string): Promise<boolean>;

    /**
     * Save a size (create or update)
     */
    save(size: Size): Promise<Size>;

    /**
     * Save multiple sizes
     */
    saveMany(sizes: Size[]): Promise<Size[]>;

    /**
     * Delete a size by ID (soft delete)
     */
    delete(id: string): Promise<boolean>;

    /**
     * Permanently delete a size
     */
    hardDelete(id: string): Promise<boolean>;

    /**
     * Count sizes matching criteria
     */
    count(options?: FindSizesOptions): Promise<number>;

    /**
     * Get the next available order value
     */
    getNextOrderValue(): Promise<number>;

    /**
     * Reorder sizes based on new order values
     */
    reorder(orderMap: Map<string, number>): Promise<boolean>;

    /**
     * Begin a transaction
     */
    beginTransaction(): Promise<void>;

    /**
     * Commit a transaction
     */
    commit(): Promise<void>;

    /**
     * Rollback a transaction
     */
    rollback(): Promise<void>;
}
