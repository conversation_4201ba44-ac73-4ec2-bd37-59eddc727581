-- Add parentId column to Customer table for hierarchy support
ALTER TABLE "Customer" 
ADD COLUMN IF NOT EXISTS "parentId" TEXT;

-- Add foreign key constraint
ALTER TABLE "Customer" 
ADD CONSTRAINT "Customer_parentId_fkey" 
FOREIGN KEY ("parentId") REFERENCES "Customer"("id") 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS "Customer_parentId_idx" ON "Customer"("parentId");