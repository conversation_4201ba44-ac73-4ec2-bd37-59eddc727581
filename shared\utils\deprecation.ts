import { FEATURES } from "@/shared/config/features";

/**
 * Logs a deprecation warning to the console in development mode
 * @param feature - The deprecated feature name
 * @param alternative - Suggested alternative to use
 * @param removalDate - Expected removal date
 */
export function deprecatedWarning(
    feature: string,
    alternative?: string,
    removalDate?: string,
): void {
    if (!FEATURES.SHOW_DEPRECATION_WARNINGS) return;

    const message = [
        `⚠️ DEPRECATED: ${feature}`,
        alternative && `Use ${alternative} instead.`,
        removalDate && `Will be removed after ${removalDate}.`,
        "See: https://github.com/lohari/docs/migrations/customer-fields",
    ]
        .filter(Boolean)
        .join("\n");

    // REMOVED: console.warn(message);

    // Track deprecation usage if analytics available
    if (typeof window !== "undefined" && "gtag" in window) {
        (window as any).gtag("event", "deprecated_field_access", {
            field_name: feature,
            timestamp: new Date().toISOString(),
        });
    }
}

/**
 * Creates a deprecated property getter with warning
 * @param propertyName - Name of the deprecated property
 * @param alternative - Alternative to suggest
 * @param removalDate - Expected removal date
 * @returns Property descriptor for Object.defineProperty
 */
export function createDeprecatedGetter(
    propertyName: string,
    alternative?: string,
    removalDate?: string,
) {
    return {
        get(): undefined {
            deprecatedWarning(propertyName, alternative, removalDate);

            return undefined;
        },
        enumerable: false,
        configurable: true,
    };
}
