import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function verifyNullableOrderId() {
  console.log("🔍 Verificando que orderId puede ser null...\n");

  try {
    await prisma.$connect();
    console.log("✅ Conectado a la base de datos");

    // Intentar crear un packing con orderId null directamente
    const testPacking = await prisma.packing.create({
      data: {
        folio: `TEST-${Date.now()}`,
        code: `TEST-${Date.now()}`,
        customerId: "test-customer-id", // Este será validado pero es solo para prueba
        orderId: null, // Esto debería funcionar ahora
        deliveryDate: new Date(),
        statusId: "test-status-id",
        totalBoxes: 0,
        totalBags: 0,
        packingType: "test"
      }
    }).catch(err => {
      if (err.message.includes("orderId")) {
        console.error("❌ Error: orderId todavía no acepta valores null");
        console.error("Detalle:", err.message);
        return null;
      }
      // Si es otro error (como foreign key), significa que orderId null funcionó
      console.log("✅ orderId acepta valores null (error fue por otra razón)");
      return "success";
    });

    if (testPacking === "success" || testPacking?.id) {
      console.log("\n✅ ¡Éxito! La migración se aplicó correctamente.");
      console.log("La columna orderId ahora acepta valores NULL.");
      
      // Limpiar registro de prueba si se creó
      if (testPacking?.id) {
        await prisma.packing.delete({ where: { id: testPacking.id } });
      }
    }

    // Verificar packings existentes con orderId null
    const nullOrderPackings = await prisma.packing.count({
      where: { orderId: null }
    });

    console.log(`\n📊 Packings con orderId null en la base de datos: ${nullOrderPackings}`);

  } catch (error) {
    console.error("\n❌ Error durante la verificación:", error.message);
  } finally {
    await prisma.$disconnect();
    console.log("\n✅ Verificación completada");
  }
}

verifyNullableOrderId();