"use client";

import type { ExtendedOrder } from "../types/order-modal.types";

import React, { useState } from "react";
import {
    Card,
    CardBody,
    Chip,
    Accordion,
    AccordionItem,
    Badge,
} from "@heroui/react";
import {
    CubeIcon,
    HashtagIcon,
    Squares2X2Icon,
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { useThemeSafe, getThemeColors } from "../utils";

interface OrderPartsGridProps {
    order: ExtendedOrder;
}

export default function OrderPartsGrid({ order }: OrderPartsGridProps) {
    const theme = useThemeSafe();
    const colors = getThemeColors(theme);
    const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set());

    const orderGarments = order.OrderGarment || [];
    const hasMany = orderGarments.length > 20;

    const garmentTotals = orderGarments.map((og) => {
        const parts = og.OrderGarmentPart || [];
        const totalParts = parts.reduce(
            (sum: number, part: any) => sum + (part.quantity || 0),
            0,
        );
        const completedParts = parts
            .filter((p: any) => p.completed)
            .reduce((sum: number, part: any) => sum + (part.quantity || 0), 0);

        return {
            ...og,
            totalParts,
            completedParts,
            percentage:
                totalParts > 0
                    ? Math.round((completedParts / totalParts) * 100)
                    : 0,
        };
    });

    const renderGarmentCard = (garmentData: any, index: number) => {
        const {
            garment,
            quantity,
            totalParts,
            completedParts,
            percentage,
            OrderGarmentPart,
        } = garmentData;
        const isCompleted = percentage === 100;

        return (
            <motion.div
                key={garmentData.id}
                animate={{ opacity: 1, scale: 1 }}
                initial={{ opacity: 0, scale: 0.9 }}
                transition={{ delay: index * 0.05 }}
            >
                <Card className="border border-default-200 hover:border-primary-300 transition-colors">
                    <CardBody className="p-4">
                        {" "}
                        <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-2">
                                <CubeIcon className="w-5 h-5 text-primary-500" />
                                <h4 className="font-semibold text-foreground">
                                    {garment?.name || "Sin nombre"}
                                </h4>
                            </div>
                            <Chip
                                color={isCompleted ? "success" : "warning"}
                                size="sm"
                                variant="flat"
                            >
                                {percentage}%
                            </Chip>
                        </div>
                        <div className="space-y-2 text-sm">
                            <div className="flex items-center gap-2">
                                <HashtagIcon className="w-4 h-4 text-default-400" />
                                <span className="text-default-600">
                                    Cantidad:
                                </span>
                                <span className="font-medium text-foreground">
                                    {quantity || 0}
                                </span>
                            </div>

                            <div className="flex items-center gap-2">
                                <Squares2X2Icon className="w-4 h-4 text-default-400" />
                                <span className="text-default-600">
                                    Piezas:
                                </span>
                                <span className="font-medium text-foreground">
                                    {totalParts}
                                </span>
                            </div>

                            <div className="flex items-center gap-2">
                                {isCompleted ? (
                                    <CheckCircleIcon className="w-4 h-4 text-success" />
                                ) : (
                                    <ExclamationCircleIcon className="w-4 h-4 text-warning" />
                                )}
                                <span className="text-default-600">
                                    Completadas:
                                </span>
                                <span className="font-medium">
                                    {completedParts} / {totalParts}
                                </span>
                            </div>
                        </div>
                        {OrderGarmentPart && OrderGarmentPart.length > 0 && (
                            <div className="mt-3 pt-3 border-t border-default-100">
                                <p className="text-xs text-default-500 mb-2">
                                    Partes:
                                </p>
                                <div className="flex flex-wrap gap-1">
                                    {OrderGarmentPart.slice(0, 3).map(
                                        (part: any) => (
                                            <Badge
                                                key={part.id}
                                                color={
                                                    part.completed
                                                        ? "success"
                                                        : "default"
                                                }
                                                content={part.quantity}
                                                size="sm"
                                            >
                                                <Chip
                                                    className="text-xs"
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    {part.part?.name || "Parte"}
                                                </Chip>
                                            </Badge>
                                        ),
                                    )}{" "}
                                    {OrderGarmentPart.length > 3 && (
                                        <Chip
                                            className="text-xs"
                                            size="sm"
                                            variant="flat"
                                        >
                                            +{OrderGarmentPart.length - 3} más
                                        </Chip>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardBody>
                </Card>
            </motion.div>
        );
    };

    if (orderGarments.length === 0) {
        return (
            <Card className="border border-default-200">
                <CardBody className="p-8 text-center">
                    <CubeIcon className="w-12 h-12 mx-auto mb-4 text-default-300" />
                    <p className="text-default-500">
                        No hay prendas registradas en esta orden
                    </p>
                </CardBody>
            </Card>
        );
    }

    if (hasMany) {
        // Modo acordeón para muchas prendas
        return (
            <Accordion variant="bordered">
                {garmentTotals.map((garmentData, index) => (
                    <AccordionItem
                        key={garmentData.id}
                        aria-label={garmentData.garment?.name || "Prenda"}
                        title={
                            <div className="flex items-center justify-between w-full pr-4">
                                <span className="font-medium">
                                    {garmentData.garment?.name}
                                </span>
                                <Chip
                                    color={
                                        garmentData.percentage === 100
                                            ? "success"
                                            : "warning"
                                    }
                                    size="sm"
                                >
                                    {garmentData.percentage}%
                                </Chip>
                            </div>
                        }
                    >
                        {renderGarmentCard(garmentData, index)}
                    </AccordionItem>
                ))}
            </Accordion>
        );
    }

    // Modo grid para pocas prendas
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {garmentTotals.map(renderGarmentCard)}
        </div>
    );
}
