// Redis Integration Test
// Validates Redis connectivity and caching capabilities

import Redis from 'ioredis';

interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

// Proposed Redis configuration
export const redisConfig: CacheConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
};

// Cache wrapper for Lohari
export class LohariCache {
  private redis: Redis;
  private defaultTTL = 300; // 5 minutes

  constructor(config: CacheConfig) {
    this.redis = new Redis(config);
  }

  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const serialized = JSON.stringify(value);
    if (ttl || this.defaultTTL) {
      await this.redis.setex(key, ttl || this.defaultTTL, serialized);
    } else {
      await this.redis.set(key, serialized);
    }
  }

  async invalidate(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}

// Test script
export async function testRedisConnection() {
  console.log('Testing Redis connection...');
  
  try {
    const cache = new LohariCache(redisConfig);
    
    // Test set/get
    await cache.set('test:key', { data: 'test value' }, 60);
    const result = await cache.get('test:key');
    
    console.log('✅ Redis connection successful');
    console.log('✅ Cache operations working:', result);
    
    return true;
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    return false;
  }
}
