// Tipos para el sistema de autenticación

export interface User {
    id: string;
    email: string;
    name: string;
    image?: string;
    role?: "user" | "admin";
    createdAt: Date;
    updatedAt: Date;
}

export interface AuthState {
    user: User | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;
}

export interface LoginCredentials {
    email: string;
    password: string;
    rememberMe?: boolean;
}

export interface RegisterData {
    name: string;
    email: string;
    password: string;
}

export interface AuthResponse {
    user: User;
    accessToken: string;
    refreshToken?: string;
}

export interface AuthError {
    message: string;
    code?: string;
    field?: string;
}
