"use server";

import { revalidatePath } from "next/cache";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/utils/prisma-helpers";

import {
    createCommentSchema,
    type CreateCommentInput,
    type CommentActionResult,
} from "../../types/comment";

/**
 * Crea un nuevo comentario en una nota
 */
export async function createComment(
    input: CreateCommentInput,
): Promise<CommentActionResult> {
    try {
        // Validar sesión
        const session = await auth();
        const userId = session?.user?.id;

        if (!userId) {
            return { success: false, error: "No autorizado" };
        }

        // Validar input
        const validatedData = createCommentSchema.parse(input);

        // Verificar que la nota existe
        const noteExists = await db.note.findUnique({
            where: { id: validatedData.noteId },
            select: { id: true, orderId: true },
        });

        if (!noteExists) {
            return { success: false, error: "La nota no existe" };
        }

        // Si hay parentId, verificar que el comentario padre existe
        if (validatedData.parentId) {
            const parentExists = await db.noteComment.findUnique({
                where: { id: validatedData.parentId },
                select: { id: true, noteId: true },
            });

            if (!parentExists) {
                return {
                    success: false,
                    error: "El comentario padre no existe",
                };
            }

            // Verificar que el padre pertenece a la misma nota
            if (parentExists.noteId !== validatedData.noteId) {
                return {
                    success: false,
                    error: "El comentario padre no pertenece a esta nota",
                };
            }
        }

        // Crear el comentario
        const comment = await db.noteComment.create({
            data: {
                content: validatedData.content,
                noteId: validatedData.noteId,
                authorId: userId,
                parentId: validatedData.parentId,
            },
            include: {
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
                _count: {
                    select: {
                        replies: true,
                    },
                },
            },
        });

        // Revalidar las rutas afectadas
        revalidatePath(`/dashboard/orders/${noteExists.orderId}`);
        revalidatePath("/dashboard/notes");

        return {
            success: true,
            data: comment as any,
        };
    } catch (error) {
        return handlePrismaError(error, "Error al crear el comentario");
    }
}
