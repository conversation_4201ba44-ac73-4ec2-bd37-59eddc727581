/**
 * Query Performance Benchmark
 * Compares original vs optimized queries
 */

import { performance } from 'perf_hooks';
import { prisma } from '@/shared/lib/prisma';
// import { optimizedOrderSelect } from '../optimizations/prisma-selects'; // Comment out - module doesn't exist
const optimizedOrderSelect = {}; // Fallback

interface BenchmarkResult {
  queryName: string;
  originalTime: number;
  optimizedTime: number;
  improvement: number;
  dataSizeOriginal: number;
  dataSizeOptimized: number;
}

/**
 * Measure query execution time
 */
async function measureQuery<T>(
  name: string,
  queryFn: () => Promise<T>
): Promise<{ time: number; data: T; size: number }> {
  const start = performance.now();
  const data = await queryFn();
  const time = performance.now() - start;
  const size = JSON.stringify(data).length;
  
  return { time, data, size };
}

/**
 * Benchmark order queries
 */
export async function benchmarkOrderQueries(): Promise<BenchmarkResult> {
  console.log('🏃 Running Order Query Benchmark...\n');
  
  // Original query with full includes
  const original = await measureQuery('Original Order Query', async () => {
    return await prisma.order.findMany({
      take: 25,
      include: {
        customer: true,
        status: true,
        parts: true,
        _count: {
          select: {
            garments: true,
            notes: true,
            packings: true,
          },
        },
      },
    });
  });

  // Optimized query with selective loading
  const optimized = await measureQuery('Optimized Order Query', async () => {
    return await prisma.order.findMany({
      take: 25,
      select: optimizedOrderSelect,
    });
  });
  
  const improvement = ((original.time - optimized.time) / original.time) * 100;
  const sizeReduction = ((original.size - optimized.size) / original.size) * 100;
  
  console.log('📊 Benchmark Results:');
  console.log('====================');
  console.log(`Original Query Time: ${original.time.toFixed(2)}ms`);
  console.log(`Optimized Query Time: ${optimized.time.toFixed(2)}ms`);
  console.log(`Time Improvement: ${improvement.toFixed(1)}% faster`);
  console.log(`\nOriginal Data Size: ${(original.size / 1024).toFixed(2)}KB`);
  console.log(`Optimized Data Size: ${(optimized.size / 1024).toFixed(2)}KB`);
  console.log(`Size Reduction: ${sizeReduction.toFixed(1)}%`);
  
  return {
    queryName: 'Order Query',
    originalTime: original.time,
    optimizedTime: optimized.time,
    improvement,
    dataSizeOriginal: original.size,
    dataSizeOptimized: optimized.size,
  };
}

/**
 * Run all benchmarks
 */
export async function runAllBenchmarks() {
  const results: BenchmarkResult[] = [];
  
  try {
    // Benchmark order queries
    const orderResult = await benchmarkOrderQueries();
    results.push(orderResult);
    
    // Additional benchmarks can be added here
    
    // Summary
    console.log('\n📈 Overall Performance Summary:');
    console.log('==============================');
    const avgImprovement = results.reduce((sum, r) => sum + r.improvement, 0) / results.length;
    console.log(`Average Query Time Improvement: ${avgImprovement.toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Benchmark failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run benchmarks if called directly
if (require.main === module) {
  runAllBenchmarks();
}
