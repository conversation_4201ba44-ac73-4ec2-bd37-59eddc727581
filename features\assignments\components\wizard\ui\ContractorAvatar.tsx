"use client";

import { motion } from "framer-motion";
import { UserIcon, SparklesIcon } from "@heroicons/react/24/outline";

interface ContractorAvatarProps {
    name: string;
    className?: string;
    size?: "sm" | "md" | "lg" | "xl";
    showStatus?: boolean;
    status?: "online" | "offline" | "busy";
    isAnimated?: boolean;
    showInitialsOnly?: boolean;
}

// Enhanced gradient colors for avatars
const avatarGradients = [
    "from-blue-400 to-indigo-600",
    "from-purple-400 to-pink-600",
    "from-emerald-400 to-teal-600",
    "from-amber-400 to-orange-600",
    "from-rose-400 to-pink-600",
    "from-indigo-400 to-purple-600",
    "from-pink-400 to-rose-600",
    "from-teal-400 to-cyan-600",
    "from-orange-400 to-red-600",
    "from-lime-400 to-green-600",
    "from-cyan-400 to-blue-600",
    "from-fuchsia-400 to-purple-600",
];

// Status colors
const statusColors = {
    online: "bg-green-500",
    offline: "bg-gray-400",
    busy: "bg-amber-500",
};

// Enhanced avatar component with animations and modern styling
export const ContractorAvatar = ({
    name,
    className = "",
    size = "md",
    showStatus = false,
    status = "offline",
    isAnimated = true,
    showInitialsOnly = true,
}: ContractorAvatarProps) => {
    // Enhanced size classes with container and text sizes
    const sizeClasses = {
        sm: {
            container: "w-8 h-8",
            text: "text-xs font-semibold",
            icon: "w-4 h-4",
            status: "w-2 h-2",
            ring: "ring-2",
        },
        md: {
            container: "w-12 h-12",
            text: "text-sm font-bold",
            icon: "w-6 h-6",
            status: "w-3 h-3",
            ring: "ring-3",
        },
        lg: {
            container: "w-16 h-16",
            text: "text-lg font-bold",
            icon: "w-8 h-8",
            status: "w-4 h-4",
            ring: "ring-4",
        },
        xl: {
            container: "w-20 h-20",
            text: "text-2xl font-bold",
            icon: "w-10 h-10",
            status: "w-5 h-5",
            ring: "ring-4",
        },
    };

    // Evitar errores si name no está definido
    if (!name) {
        return (
            <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className={`relative ${className}`}
                initial={{ opacity: 0, scale: 0.8 }}
            >
                <div
                    className={`${sizeClasses[size].container} rounded-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center`}
                >
                    <UserIcon
                        className={`${sizeClasses[size].icon} text-white`}
                    />
                </div>
            </motion.div>
        );
    }

    // Generar gradiente basado en el nombre
    const gradientIndex = name.charCodeAt(0) % avatarGradients.length;
    const bgGradient = avatarGradients[gradientIndex];

    // Obtener iniciales (hasta 2 caracteres)
    const initials = name
        .split(" ")
        .map((part) => part[0])
        .join("")
        .substring(0, 2)
        .toUpperCase();

    const currentSize = sizeClasses[size];

    return (
        <motion.div
            animate={isAnimated ? { opacity: 1, scale: 1 } : {}}
            className={`relative ${className}`}
            initial={isAnimated ? { opacity: 0, scale: 0.8 } : {}}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
            whileHover={isAnimated ? { scale: 1.05 } : {}}
        >
            {/* Main avatar container */}
            <div className="relative">
                {/* Glow effect */}
                {isAnimated && (
                    <motion.div
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 0.3, 0.5],
                        }}
                        className={`absolute inset-0 ${currentSize.container} rounded-full bg-gradient-to-br ${bgGradient} blur-lg opacity-50`}
                        transition={{
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut",
                        }}
                    />
                )}

                {/* Avatar */}
                <motion.div
                    aria-label={`Avatar de ${name}`}
                    className={`
                        relative ${currentSize.container} 
                        rounded-full bg-gradient-to-br ${bgGradient}
                        flex items-center justify-center
                        shadow-xl ${currentSize.ring} ring-white/20
                        overflow-hidden
                    `}
                    title={name}
                    whileTap={isAnimated ? { scale: 0.95 } : {}}
                >
                    {/* Background pattern */}
                    <div className="absolute inset-0 opacity-20">
                        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent" />
                        <div className="absolute bottom-0 right-0 w-full h-full bg-gradient-to-tl from-black/20 to-transparent" />
                    </div>

                    {/* Content */}
                    <motion.div
                        animate={isAnimated ? { y: 0, opacity: 1 } : {}}
                        className="relative z-10"
                        initial={isAnimated ? { y: 10, opacity: 0 } : {}}
                        transition={{ delay: 0.1 }}
                    >
                        {showInitialsOnly ? (
                            <span
                                className={`${currentSize.text} text-white drop-shadow-md`}
                            >
                                {initials}
                            </span>
                        ) : (
                            <UserIcon
                                className={`${currentSize.icon} text-white drop-shadow-md`}
                            />
                        )}
                    </motion.div>

                    {/* Sparkle effect */}
                    {isAnimated && (
                        <motion.div
                            animate={{
                                opacity: [0, 1, 0],
                                scale: [0.5, 1, 0.5],
                                rotate: [0, 180, 360],
                            }}
                            className="absolute top-1 right-1"
                            transition={{
                                duration: 3,
                                repeat: Infinity,
                                delay: Math.random() * 2,
                            }}
                        >
                            <SparklesIcon className="w-3 h-3 text-white/60" />
                        </motion.div>
                    )}
                </motion.div>

                {/* Status indicator */}
                {showStatus && (
                    <motion.div
                        animate={{ scale: 1 }}
                        className={`
                            absolute bottom-0 right-0
                            ${currentSize.status} rounded-full
                            ${statusColors[status]}
                            ring-2 ring-white dark:ring-gray-800
                            shadow-lg
                        `}
                        initial={{ scale: 0 }}
                        transition={{ delay: 0.2, type: "spring" }}
                    >
                        {status === "online" && (
                            <motion.div
                                animate={{
                                    scale: [1, 1.5, 1],
                                    opacity: [1, 0, 1],
                                }}
                                className={`absolute inset-0 rounded-full ${statusColors[status]}`}
                                transition={{ duration: 2, repeat: Infinity }}
                            />
                        )}
                    </motion.div>
                )}
            </div>
        </motion.div>
    );
};
