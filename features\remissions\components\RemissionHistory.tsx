"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react";
import {
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
    DocumentPlusIcon,
    PrinterIcon,
    TruckIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { formatDate } from "@/shared/utils/formatters";

interface HistoryEntry {
    id: string;
    action: string;
    timestamp: string | Date;
    metadata?: Record<string, any>;
    userId?: string;
    user?: {
        name: string;
        email: string;
    };
}

interface RemissionHistoryProps {
    history: HistoryEntry[];
    showCard?: boolean;
}

const actionIcons: Record<string, React.ComponentType<any>> = {
    CREATED: DocumentPlusIcon,
    PRINTED: PrinterIcon,
    STATUS_CHANGED_TO_COMPLETED: CheckCircleIcon,
    STATUS_CHANGED_TO_CANCELLED: XCircleIcon,
    STATUS_CHANGED_TO_PENDING: ClockI<PERSON>,
    STATUS_CHANGED_TO_ACTIVE: TruckI<PERSON>,
    DELIVERED: TruckIcon,
};

const actionLabels: Record<string, string> = {
    CREATED: "Remisión creada",
    PRINTED: "Documento impreso",
    STATUS_CHANGED_TO_COMPLETED: "Marcado como entregado",
    STATUS_CHANGED_TO_CANCELLED: "Cancelado",
    STATUS_CHANGED_TO_PENDING: "Marcado como pendiente",
    STATUS_CHANGED_TO_ACTIVE: "Activado",
    DELIVERED: "Entregado al contratista",
};

const actionColors: Record<
    string,
    "success" | "warning" | "danger" | "primary" | "default"
> = {
    CREATED: "primary",
    PRINTED: "default",
    STATUS_CHANGED_TO_COMPLETED: "success",
    STATUS_CHANGED_TO_CANCELLED: "danger",
    STATUS_CHANGED_TO_PENDING: "warning",
    STATUS_CHANGED_TO_ACTIVE: "primary",
    DELIVERED: "success",
};

export default function RemissionHistory({
    history,
    showCard = true,
}: RemissionHistoryProps) {
    const sortedHistory = [...history].sort(
        (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );

    const content = (
        <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700" />

            <div className="space-y-6">
                {sortedHistory.map((entry, index) => {
                    const Icon = actionIcons[entry.action] || ClockIcon;
                    const label = actionLabels[entry.action] || entry.action;
                    const color = actionColors[entry.action] || "default";

                    return (
                        <motion.div
                            key={entry.id}
                            animate={{ opacity: 1, x: 0 }}
                            className="flex gap-4 relative"
                            initial={{ opacity: 0, x: -20 }}
                            transition={{
                                type: "spring",
                                stiffness: 300,
                                damping: 30,
                                delay: index * 0.1,
                            }}
                        >
                            {/* Icon circle */}
                            <div className="flex-shrink-0 z-10">
                                <div
                                    className={`
                                    w-10 h-10 rounded-full flex items-center justify-center
                                    ${color === "success" ? "bg-green-100 dark:bg-green-900/30" : ""}
                                    ${color === "warning" ? "bg-yellow-100 dark:bg-yellow-900/30" : ""}
                                    ${color === "danger" ? "bg-red-100 dark:bg-red-900/30" : ""}
                                    ${color === "primary" ? "bg-blue-100 dark:bg-blue-900/30" : ""}
                                    ${color === "default" ? "bg-gray-100 dark:bg-gray-800" : ""}
                                `}
                                >
                                    <Icon
                                        className={`
                                        w-5 h-5
                                        ${color === "success" ? "text-green-600 dark:text-green-400" : ""}
                                        ${color === "warning" ? "text-yellow-600 dark:text-yellow-400" : ""}
                                        ${color === "danger" ? "text-red-600 dark:text-red-400" : ""}
                                        ${color === "primary" ? "text-blue-600 dark:text-blue-400" : ""}
                                        ${color === "default" ? "text-gray-600 dark:text-gray-400" : ""}
                                    `}
                                    />
                                </div>
                            </div>

                            {/* Content */}
                            <div className="flex-1 pb-6">
                                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                                    <div className="flex items-start justify-between mb-2">
                                        <div>
                                            <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                                                {label}
                                            </h4>
                                            {entry.user && (
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    Por: {entry.user.name}
                                                </p>
                                            )}
                                        </div>
                                        <Chip
                                            color={color}
                                            size="sm"
                                            variant="flat"
                                        >
                                            {formatDate(entry.timestamp)}
                                        </Chip>
                                    </div>

                                    {entry.metadata &&
                                        Object.keys(entry.metadata).length >
                                            0 && (
                                            <div className="mt-3 space-y-1">
                                                {Object.entries(
                                                    entry.metadata,
                                                ).map(([key, value]) => (
                                                    <div
                                                        key={key}
                                                        className="flex items-center gap-2 text-sm"
                                                    >
                                                        <span className="text-gray-500 dark:text-gray-400 capitalize">
                                                            {key.replace(
                                                                /_/g,
                                                                " ",
                                                            )}
                                                            :
                                                        </span>
                                                        <span className="text-gray-700 dark:text-gray-300">
                                                            {typeof value ===
                                                            "object"
                                                                ? JSON.stringify(
                                                                      value,
                                                                  )
                                                                : String(value)}
                                                        </span>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                </div>
                            </div>
                        </motion.div>
                    );
                })}
            </div>
        </div>
    );

    if (!showCard) {
        return content;
    }

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center gap-2">
                    <ClockIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    <h3 className="text-lg font-semibold">
                        Historial de Cambios
                    </h3>
                </div>
            </CardHeader>
            <Divider />
            <CardBody className="p-6">{content}</CardBody>
        </Card>
    );
}
