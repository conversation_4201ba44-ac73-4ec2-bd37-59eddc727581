"use client";

import * as React from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { cva, type VariantProps } from "class-variance-authority";

// import { cn } from "@/shared/utils/cn"; // Comment out - module doesn't exist
const cn = (...classes: any[]) => classes.filter(Boolean).join(" "); // Simple fallback

const modalVariants = cva(
    "fixed left-[50%] top-[50%] z-[var(--z-modal)] grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-lg",
    {
        variants: {
            size: {
                sm: "max-w-[400px]",
                md: "max-w-[600px]",
                lg: "max-w-[800px]",
                xl: "max-w-[1200px]",
                full: "max-w-[90vw] max-h-[90vh]",
            },
        },
        defaultVariants: {
            size: "md",
        },
    },
);

interface ModalProps
    extends React.ComponentPropsWithoutRef<typeof Dialog.Root> {
    size?: VariantProps<typeof modalVariants>["size"];
}

const Modal = Dialog.Root;

const ModalTrigger = Dialog.Trigger;

const ModalPortal = Dialog.Portal;

const ModalClose = Dialog.Close;

const ModalOverlay = React.forwardRef<
    React.ElementRef<typeof Dialog.Overlay>,
    React.ComponentPropsWithoutRef<typeof Dialog.Overlay>
>(({ className, ...props }, ref) => (
    <Dialog.Overlay
        ref={ref}
        className={cn(
            "fixed inset-0 z-[var(--z-modal-backdrop)] bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            className,
        )}
        {...props}
    />
));

ModalOverlay.displayName = Dialog.Overlay.displayName;
