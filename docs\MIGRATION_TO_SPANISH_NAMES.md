# 🔄 Migración a Nombres en Español y Sistema Dinámico

Este documento describe la migración del sistema de configuración de LOHARI de IDs hardcodeados a un sistema dinámico con nombres en español.

## 📋 Resumen de Cambios

### Antes
- Nombres en inglés: `IN_PRODUCTION`, `PENDING`, etc.
- IDs hardcodeados que se rompían al resetear la BD
- Dependencia frágil en constantes estáticas

### Después
- Nombres en español: `"En producción"`, `"Pendiente"`, etc.
- Sistema de cache dinámico con ConfigCache
- Búsqueda por nombre, no por ID

## 🚀 Pasos de Migración

### 1. Ejecutar los seeds actualizados

```bash
# Resetear la BD con los nuevos datos en español
npm run db:reset

# O solo ejecutar los seeds si la BD ya existe
npm run db:seed
```

### 2. Validar los datos

```bash
# Verificar que todos los datos existen correctamente
npm run db:validate
```

Deberías ver algo como:
```
📦 Validando OrderStatus:
  ✅ Recibido
  ✅ En producción
  ✅ Control de calidad
  ...
```

### 3. Actualizar tu código

#### Antes (❌ No usar):
```typescript
import { noteImportanceIds, NoteImportance } from "@/constants/noteImportance";

// Usando ID hardcodeado
importanceId: noteImportanceIds[NoteImportance.MEDIUM]
```

#### Después (✅ Usar):
```typescript
import { getNoteImportanceByName } from "@/shared/lib/config-cache";
import { CONFIG_NAMES } from "@/constants/config-names";

// Usando búsqueda por nombre
const importance = await getNoteImportanceByName(CONFIG_NAMES.noteImportance.MEDIUM);
importanceId: importance.id
```

## 📚 Nuevas APIs

### ConfigCache

```typescript
// Obtener instancia del cache (singleton)
const cache = await ConfigCache.getInstance();

// Obtener por nombre
const status = await cache.getByName("orderStatus", "En producción");

// Obtener todos los valores de una tabla
const allStatuses = await cache.getAll("orderStatus");
```

### Funciones Helper

```typescript
// Funciones específicas para cada tipo
await getOrderStatusByName("Recibido");
await getNoteStatusByName("Pendiente");
await getNoteImportanceByName("Medio");
await getPackingStatusByName("En progreso");
await getRejectionReasonByName("Calidad");
await getRoleByName("Administrador");
```

### Constantes de Nombres

```typescript
import { CONFIG_NAMES } from "@/constants/config-names";

// Acceder a nombres con type safety
CONFIG_NAMES.orderStatus.RECEIVED // "Recibido"
CONFIG_NAMES.noteImportance.HIGH // "Alto"
```

## 🔧 Casos de Uso Comunes

### Crear una nota con importancia media

```typescript
const mediumImportance = await getNoteImportanceByName(CONFIG_NAMES.noteImportance.MEDIUM);

await prisma.note.create({
  data: {
    content: "Mi nota",
    importanceId: mediumImportance.id,
    // ... otros campos
  }
});
```

### Buscar órdenes por estado

```typescript
const receivedStatus = await getOrderStatusByName("Recibido");

const orders = await prisma.order.findMany({
  where: {
    statusId: receivedStatus.id
  }
});
```

## ⚠️ Consideraciones Importantes

1. **Cache Automático**: ConfigCache se inicializa automáticamente la primera vez que se usa
2. **Performance**: Las búsquedas son O(1) después de la carga inicial
3. **Sincronización**: Si cambias datos en la BD, usa `cache.reload()` para actualizar
4. **Fallback**: Los adaptadores de migración permiten compatibilidad temporal

## 🐛 Solución de Problemas

### Error: "No se encontró la importancia 'Medio'"

**Causa**: Los datos iniciales no están en la BD  
**Solución**: Ejecutar `npm run db:seed`

### Error: "noteImportanceIds is not defined"

**Causa**: Estás usando el código antiguo  
**Solución**: Actualizar a las nuevas funciones helper

### El cache no refleja cambios recientes

**Causa**: El cache no se ha actualizado  
**Solución**: 
```typescript
const cache = await ConfigCache.getInstance();
await cache.reload();
```

## 📌 Checklist de Migración

- [ ] Ejecutar seeds con nombres en español
- [ ] Validar datos con `npm run db:validate`
- [ ] Actualizar imports en archivos de acciones
- [ ] Reemplazar usos de IDs hardcodeados
- [ ] Probar funcionalidad de notas
- [ ] Verificar UI muestra nombres correctos
- [ ] Eliminar referencias a noteImportanceIds
