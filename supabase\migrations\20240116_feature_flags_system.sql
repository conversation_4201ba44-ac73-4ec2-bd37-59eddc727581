-- Feature Flags System for Zero-Budget Migration
-- This creates all necessary tables for the migration system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS feature_flag_audit CASCADE;
DROP TABLE IF EXISTS migration_progress CASCADE;
DROP TABLE IF EXISTS performance_metrics CASCADE;
DROP TABLE IF EXISTS feature_flags CASCADE;

-- 1. Feature flags table
CREATE TABLE feature_flags (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  key VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  enabled BOOLEAN DEFAULT false,
  rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
  enabled_for_users JSONB DEFAULT '[]'::jsonb,
  enabled_for_roles JSONB DEFAULT '[]'::jsonb,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Feature flag audit log for tracking changes
CREATE TABLE feature_flag_audit (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  flag_key VARCHAR(255) NOT NULL,
  action VARCHAR(50) NOT NULL CHECK (action IN ('created', 'updated', 'deleted', 'toggled')),
  old_value JSONB,
  new_value JSONB,
  user_id UUID,
  user_email VARCHAR(255),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Migration progress tracking
CREATE TABLE migration_progress (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  phase VARCHAR(100) NOT NULL,
  status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed', 'rolled_back')),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  duration_ms INTEGER,
  metrics JSONB DEFAULT '{}'::jsonb,
  errors JSONB DEFAULT '[]'::jsonb,
  rollback_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Performance metrics for monitoring
CREATE TABLE performance_metrics (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  metric_name VARCHAR(100) NOT NULL,
  metric_value NUMERIC NOT NULL,
  metric_unit VARCHAR(50),
  page_path VARCHAR(500),
  user_id UUID,
  session_id VARCHAR(255),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_feature_flags_key ON feature_flags(key);
CREATE INDEX idx_feature_flags_enabled ON feature_flags(enabled);
CREATE INDEX idx_migration_progress_phase ON migration_progress(phase);
CREATE INDEX idx_migration_progress_status ON migration_progress(status);
CREATE INDEX idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX idx_performance_metrics_created ON performance_metrics(created_at DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON feature_flags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_migration_progress_updated_at BEFORE UPDATE ON migration_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Audit trigger for feature flags
CREATE OR REPLACE FUNCTION audit_feature_flag_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO feature_flag_audit (flag_key, action, new_value, user_id)
        VALUES (NEW.key, 'created', row_to_json(NEW), current_setting('app.current_user_id', true)::uuid);
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO feature_flag_audit (flag_key, action, old_value, new_value, user_id)
        VALUES (NEW.key, 'updated', row_to_json(OLD), row_to_json(NEW), current_setting('app.current_user_id', true)::uuid);
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO feature_flag_audit (flag_key, action, old_value, user_id)
        VALUES (OLD.key, 'deleted', row_to_json(OLD), current_setting('app.current_user_id', true)::uuid);
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER audit_feature_flags
    AFTER INSERT OR UPDATE OR DELETE ON feature_flags
    FOR EACH ROW EXECUTE FUNCTION audit_feature_flag_changes();

-- Row Level Security (RLS)
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flag_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE migration_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Feature flags: admins can do everything, others can only read
CREATE POLICY "Feature flags are viewable by everyone" ON feature_flags
    FOR SELECT USING (true);

CREATE POLICY "Only admins can modify feature flags" ON feature_flags
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'role' = 'service_role'
    );

-- Audit logs: admins only
CREATE POLICY "Only admins can view audit logs" ON feature_flag_audit
    FOR SELECT USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'role' = 'service_role'
    );

-- Migration progress: admins can modify, others can read
CREATE POLICY "Migration progress viewable by everyone" ON migration_progress
    FOR SELECT USING (true);

CREATE POLICY "Only admins can modify migration progress" ON migration_progress
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'role' = 'service_role'
    );

-- Performance metrics: anyone can insert, admins can read
CREATE POLICY "Anyone can insert performance metrics" ON performance_metrics
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Only admins can view performance metrics" ON performance_metrics
    FOR SELECT USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'role' = 'service_role'
    );

-- Insert default feature flags for migration
INSERT INTO feature_flags (key, name, description, enabled, rollout_percentage, metadata) VALUES
-- Repository Pattern Flags
('use_repository_pattern', 'Repository Pattern - Global', 'Enable repository pattern for data access layer', false, 0, 
 '{"owner": "architecture-team", "risk": "medium", "dependencies": []}'::jsonb),
('use_repository_sizes', 'Repository Pattern - Sizes Module', 'Enable repository pattern for sizes module (POC)', false, 0,
 '{"owner": "architecture-team", "risk": "low", "dependencies": ["use_repository_pattern"]}'::jsonb),
('use_repository_orders', 'Repository Pattern - Orders Module', 'Enable repository pattern for orders module', false, 0,
 '{"owner": "architecture-team", "risk": "high", "dependencies": ["use_repository_pattern"]}'::jsonb),

-- State Management Flags
('use_zustand_store', 'Zustand State Management', 'Enable Zustand for unified state management', false, 0,
 '{"owner": "frontend-team", "risk": "medium"}'::jsonb),
('use_zustand_sizes', 'Zustand - Sizes Module', 'Enable Zustand store for sizes module', false, 0,
 '{"owner": "frontend-team", "risk": "low", "dependencies": ["use_zustand_store"]}'::jsonb),

-- Performance Flags
('enable_client_cache', 'Client-side Caching', 'Enable localStorage/sessionStorage caching', false, 0,
 '{"owner": "platform-team", "risk": "low"}'::jsonb),
('enable_query_optimization', 'Database Query Optimization', 'Enable optimized database queries', false, 0,
 '{"owner": "backend-team", "risk": "medium"}'::jsonb),

-- Migration Control Flags
('migration_safe_mode', 'Migration Safe Mode', 'Enable extra validation during migration', true, 100,
 '{"owner": "devops-team", "risk": "low"}'::jsonb),
('enable_rollback_hooks', 'Automatic Rollback', 'Enable automatic rollback on errors', true, 100,
 '{"owner": "devops-team", "risk": "low"}'::jsonb);

-- Create a view for easier flag management
CREATE OR REPLACE VIEW feature_flags_status AS
SELECT 
    f.key,
    f.name,
    f.enabled,
    f.rollout_percentage,
    f.metadata->>'risk' as risk_level,
    f.metadata->>'owner' as owner_team,
    f.updated_at,
    (SELECT COUNT(*) FROM feature_flag_audit WHERE flag_key = f.key) as change_count
FROM feature_flags f
ORDER BY f.created_at;