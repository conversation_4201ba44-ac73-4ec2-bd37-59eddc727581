"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Obtiene todas las tallas con opciones de filtrado y paginación
 */
export async function getSizes(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) {
    const {
        search,
        orderBy = "code",
        order = "asc",
        page = 1,
        perPage = 50,
    } = options;

    return await handleDbError(async () => {
        // Construir filtros
        const where: Prisma.SizeWhereInput = search
            ? {
                  code: {
                      contains: search,
                      mode: "insensitive" as Prisma.QueryMode,
                  },
              }
            : {};

        // Obtener datos con paginación
        const sizes = await db.size.findMany({
            where,
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
            include: {
                _count: {
                    select: { garments: true },
                },
            },
        });

        // Obtener total para paginación
        const total = await db.size.count({ where });

        return {
            sizes,
            pagination: {
                total,
                currentPage: page,
                lastPage: Math.ceil(total / perPage),
            },
        };
    }, "Error al obtener tallas");
}

/**
 * Obtiene una talla por ID
 */
export async function getSize(id: string | null | undefined) {
    if (!id) return { success: false, error: "ID no válido" };

    return await handleDbError(async () => {
        const size = await db.size.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { garments: true },
                },
            },
        });

        if (!size) {
            throw new Error("Talla no encontrada");
        }

        return size;
    }, "Error al obtener talla");
}
