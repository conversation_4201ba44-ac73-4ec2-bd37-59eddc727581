"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
} from "@/shared/utils/actions-utils";

/**
 * Server Action para obtener todos los estados de notas
 */
export async function getNoteStatuses(): Promise<ActionResponse<any>> {
    return getStatuses();
}

/**
 * Server Action para obtener todos los estados de notas (alias)
 */
export async function getStatuses(): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        // Obtener todos los estados de notas
        const statuses = await prisma.noteStatus.findMany({
            orderBy: {
                name: "asc",
            },
        });

        return {
            success: true,
            data: statuses,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
