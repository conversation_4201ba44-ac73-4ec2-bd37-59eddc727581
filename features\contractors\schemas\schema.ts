import { z } from "zod";

// Schema Zod para validación de contratistas
export const contractorSchema = z.object({
    firstName: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres"),
    middleName: z
        .string()
        .max(50, "El segundo nombre no puede exceder los 50 caracteres")
        .optional()
        .nullable(),
    lastName: z
        .string()
        .min(2, "El apellido paterno debe tener al menos 2 caracteres")
        .max(50, "El apellido paterno no puede exceder los 50 caracteres"),
    secondLastName: z
        .string()
        .max(50, "El apellido materno no puede exceder los 50 caracteres")
        .optional()
        .nullable(),
    email: z.string().email("Email inválido").optional().nullable(),
    phone: z.string().optional().nullable(),
    notes: z
        .string()
        .max(500, "Las notas no pueden exceder los 500 caracteres")
        .nullable()
        .optional(),
});

// Interfaces para la tipificación de respuestas API
export interface Contractor {
    id: string;
    name: string;
    firstName: string | null;
    middleName: string | null;
    lastName: string | null;
    secondLastName: string | null;
    email: string | null;
    phone: string | null;
    notes: string | null;
    status: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    deletedAt?: Date | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    _count: {
        assignments: number;
        remissions?: number;
    };
}

// Ya no necesitamos la interfaz extendida, pero la mantenemos por compatibilidad
export interface ContractorWithUser extends Contractor {}

// Formato de respuesta para un solo contratista
export interface ContractorResponse {
    success: boolean;
    data?: Contractor;
    error?: string;
}

// Formato de respuesta para una lista de contratistas
export interface ContractorsListResponse {
    success: boolean;
    data?: Contractor[];
    pagination?: {
        page: number;
        perPage: number;
        total: number;
        totalPages: number;
        hasMore: boolean;
    };
    error?: string;
}

// Interfaz para resultados de validación de nombre completo
export interface NameValidationResult {
    isValid: boolean;
    message?: string;
}
