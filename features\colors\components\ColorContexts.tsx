"use client";

import { useState } from "react";
import {
    <PERSON>,
    CardBody,
    CardHeader,
    Accordion,
    AccordionItem,
    Toolt<PERSON>,
} from "@heroui/react";
import { motion } from "framer-motion";

import { isColorDark } from "../utils/colorUtils";

import { ThemeToggle } from "./ThemeToggle";

interface ColorContextsProps {
    hexCode: string;
    className?: string;
}

export function ColorContexts({ hexCode, className = "" }: ColorContextsProps) {
    const [previewTheme, setPreviewTheme] = useState<"light" | "dark">("light");
    const isDark = isColorDark(hexCode);

    // Determina el color de texto basado en el color y el tema
    const getTextColor = (forBackground = false) => {
        if (forBackground) {
            // Si es para un elemento con el color como fondo
            return isDark ? "text-white" : "text-gray-900";
        }

        // Si es para el color como texto
        return `text-[${hexCode}]`;
    };

    // Determina las clases de fondo según el tema
    const getBgClass = () => {
        return previewTheme === "dark" ? "bg-gray-900" : "bg-white";
    };

    // Aplica el tema apropiado para ejemplos
    const getThemeContainerClass = () => {
        return previewTheme === "dark"
            ? "bg-gray-800 border-gray-700"
            : "bg-white border-gray-200";
    };

    return (
        <Card className={`overflow-hidden shadow-lg ${className}`}>
            <CardHeader className="flex justify-between items-center px-4 py-3 bg-gray-50 dark:bg-gray-800/50">
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">
                    Contextos de uso
                </h3>
                <ThemeToggle
                    defaultTheme="light"
                    onChange={(theme) => setPreviewTheme(theme)}
                />
            </CardHeader>

            <CardBody className="p-0">
                <Accordion
                    className="px-0"
                    itemClasses={{
                        base: "py-0",
                        title: "text-sm font-medium text-gray-700 dark:text-gray-300 px-4 py-3",
                        trigger:
                            "data-[hover=true]:bg-gray-50/50 dark:data-[hover=true]:bg-gray-800/50",
                        content: "px-4 pb-4 pt-0",
                    }}
                >
                    {/* Categoría: Contenido */}
                    <AccordionItem
                        key="contenido"
                        aria-label="Ejemplos de contenido"
                        indicator={({ isOpen }) => (
                            <motion.div
                                animate={{ rotate: isOpen ? 180 : 0 }}
                                className="text-gray-500"
                                initial={false}
                            >
                                ▼
                            </motion.div>
                        )}
                        title="Contenido"
                    >
                        <div
                            className={`p-4 rounded-lg border ${getThemeContainerClass()}`}
                        >
                            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                                {/* Ejemplo de texto */}
                                <Tooltip content="Texto con el color aplicado">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <p
                                                className="text-sm"
                                                style={{ color: hexCode }}
                                            >
                                                Texto de ejemplo
                                            </p>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Texto
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Ejemplo de título */}
                                <Tooltip content="Título con el color aplicado">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <h4
                                                className="text-sm font-bold"
                                                style={{ color: hexCode }}
                                            >
                                                Título destacado
                                            </h4>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Título
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Ejemplo de enlace */}
                                <Tooltip content="Enlace con el color aplicado">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <a
                                                className="text-sm cursor-pointer underline"
                                                href="#"
                                                style={{ color: hexCode }}
                                                onClick={(e) =>
                                                    e.preventDefault()
                                                }
                                            >
                                                Enlace de ejemplo
                                            </a>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Enlace
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Ejemplo de fondo con texto */}
                                <Tooltip content="Fondo con el color aplicado">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`p-3 rounded-md border`}
                                            style={{ backgroundColor: hexCode }}
                                        >
                                            <p
                                                className={`text-sm ${isDark ? "text-white" : "text-gray-900"}`}
                                            >
                                                Texto sobre fondo
                                            </p>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Fondo
                                        </span>
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                    </AccordionItem>

                    {/* Categoría: Acciones */}
                    <AccordionItem
                        key="acciones"
                        aria-label="Ejemplos de acciones"
                        indicator={({ isOpen }) => (
                            <motion.div
                                animate={{ rotate: isOpen ? 180 : 0 }}
                                className="text-gray-500"
                                initial={false}
                            >
                                ▼
                            </motion.div>
                        )}
                        title="Acciones"
                    >
                        <div
                            className={`p-4 rounded-lg border ${getThemeContainerClass()}`}
                        >
                            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                                {/* Botón primario */}
                                <Tooltip content="Botón primario con el color aplicado">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <button
                                                className="px-3 py-1 text-sm font-medium rounded-md text-white"
                                                style={{
                                                    backgroundColor: hexCode,
                                                }}
                                            >
                                                Botón primario
                                            </button>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Botón primario
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Botón secundario */}
                                <Tooltip content="Botón secundario con borde del color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <button
                                                className="px-3 py-1 text-sm font-medium rounded-md"
                                                style={{
                                                    borderWidth: "1px",
                                                    borderStyle: "solid",
                                                    borderColor: hexCode,
                                                    color: hexCode,
                                                }}
                                            >
                                                Botón secundario
                                            </button>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Botón secundario
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Botón con ícono */}
                                <Tooltip content="Botón con ícono usando el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <button
                                                className="p-1 text-sm font-medium rounded-full"
                                                style={{
                                                    backgroundColor: hexCode,
                                                    color: isDark
                                                        ? "white"
                                                        : "black",
                                                }}
                                            >
                                                <svg
                                                    className="h-5 w-5"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                    />
                                                </svg>
                                            </button>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Ícono
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Botón deshabilitado */}
                                <Tooltip content="Botón deshabilitado con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <button
                                                disabled
                                                className="px-3 py-1 text-sm font-medium rounded-md opacity-50 cursor-not-allowed"
                                                style={{
                                                    backgroundColor: hexCode,
                                                    color: isDark
                                                        ? "white"
                                                        : "black",
                                                }}
                                            >
                                                Deshabilitado
                                            </button>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Deshabilitado
                                        </span>
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                    </AccordionItem>

                    {/* Categoría: Navegación */}
                    <AccordionItem
                        key="navegacion"
                        aria-label="Ejemplos de navegación"
                        indicator={({ isOpen }) => (
                            <motion.div
                                animate={{ rotate: isOpen ? 180 : 0 }}
                                className="text-gray-500"
                                initial={false}
                            >
                                ▼
                            </motion.div>
                        )}
                        title="Navegación"
                    >
                        <div
                            className={`p-4 rounded-lg border ${getThemeContainerClass()}`}
                        >
                            <div className="grid grid-cols-2 gap-3">
                                {/* Tab activo */}
                                <Tooltip content="Tab activo con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`w-full p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <div className="flex border-b">
                                                <div
                                                    className="px-3 py-2 text-sm font-medium border-b-2"
                                                    style={{
                                                        borderColor: hexCode,
                                                        color: hexCode,
                                                    }}
                                                >
                                                    Tab activo
                                                </div>
                                                <div className="px-3 py-2 text-sm font-medium text-gray-500">
                                                    Tab inactivo
                                                </div>
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Tabs
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Breadcrumb */}
                                <Tooltip content="Breadcrumb con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`w-full p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <div className="flex items-center text-sm">
                                                <span className="text-gray-500">
                                                    Inicio
                                                </span>
                                                <span className="mx-2 text-gray-400">
                                                    /
                                                </span>
                                                <span className="text-gray-500">
                                                    Sección
                                                </span>
                                                <span className="mx-2 text-gray-400">
                                                    /
                                                </span>
                                                <span
                                                    style={{ color: hexCode }}
                                                >
                                                    Actual
                                                </span>
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Breadcrumb
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Barra de navegación */}
                                <Tooltip content="Navbar con el color de fondo">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`w-full rounded-md border overflow-hidden`}
                                        >
                                            <div
                                                className={`w-full p-3 flex justify-between items-center`}
                                                style={{
                                                    backgroundColor: hexCode,
                                                    color: isDark
                                                        ? "white"
                                                        : "black",
                                                }}
                                            >
                                                <span className="text-sm font-bold">
                                                    Logotipo
                                                </span>
                                                <div className="flex gap-3">
                                                    <span className="text-sm">
                                                        Enlace 1
                                                    </span>
                                                    <span className="text-sm">
                                                        Enlace 2
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Navbar
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Paginación */}
                                <Tooltip content="Paginación con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`w-full p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <div className="flex justify-center gap-1">
                                                <span className="px-2 py-1 text-xs text-gray-500 rounded border">
                                                    1
                                                </span>
                                                <span
                                                    className="px-2 py-1 text-xs rounded border"
                                                    style={{
                                                        backgroundColor:
                                                            hexCode,
                                                        color: isDark
                                                            ? "white"
                                                            : "black",
                                                        borderColor: hexCode,
                                                    }}
                                                >
                                                    2
                                                </span>
                                                <span className="px-2 py-1 text-xs text-gray-500 rounded border">
                                                    3
                                                </span>
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Paginación
                                        </span>
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                    </AccordionItem>

                    {/* Categoría: Componentes */}
                    <AccordionItem
                        key="componentes"
                        aria-label="Ejemplos de componentes"
                        indicator={({ isOpen }) => (
                            <motion.div
                                animate={{ rotate: isOpen ? 180 : 0 }}
                                className="text-gray-500"
                                initial={false}
                            >
                                ▼
                            </motion.div>
                        )}
                        title="Componentes"
                    >
                        <div
                            className={`p-4 rounded-lg border ${getThemeContainerClass()}`}
                        >
                            <div className="grid grid-cols-2 gap-3">
                                {/* Badge */}
                                <Tooltip content="Badge con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <span
                                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                style={{
                                                    backgroundColor: hexCode,
                                                    color: isDark
                                                        ? "white"
                                                        : "black",
                                                }}
                                            >
                                                Badge
                                            </span>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Badge
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Alert */}
                                <Tooltip content="Alerta con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <div
                                                className="w-full p-2 text-xs rounded-md"
                                                style={{
                                                    backgroundColor: `${hexCode}20`, // Color con opacidad
                                                    borderLeft: `3px solid ${hexCode}`,
                                                    color: hexCode,
                                                }}
                                            >
                                                Mensaje de alerta
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Alerta
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Card */}
                                <Tooltip content="Card con el color en header">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <div className="w-full overflow-hidden rounded-md border">
                                                <div
                                                    className="p-2 text-xs font-medium"
                                                    style={{
                                                        backgroundColor:
                                                            hexCode,
                                                        color: isDark
                                                            ? "white"
                                                            : "black",
                                                    }}
                                                >
                                                    Header
                                                </div>
                                                <div className="p-2 text-xs">
                                                    Contenido de card
                                                </div>
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Card
                                        </span>
                                    </div>
                                </Tooltip>

                                {/* Progress */}
                                <Tooltip content="Barra de progreso con el color">
                                    <div className="flex flex-col items-center gap-2">
                                        <div
                                            className={`flex justify-center p-3 rounded-md border ${getBgClass()}`}
                                        >
                                            <div className="w-full">
                                                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                                                    <div
                                                        className="h-full rounded-full"
                                                        style={{
                                                            width: "70%",
                                                            backgroundColor:
                                                                hexCode,
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            Progreso
                                        </span>
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                    </AccordionItem>
                </Accordion>
            </CardBody>
        </Card>
    );
}
