"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button, Avatar, Textarea, <PERSON><PERSON><PERSON>, Spinner } from "@heroui/react";
import {
    PaperAirplaneIcon,
    PencilIcon,
    TrashIcon,
    XMarkIcon,
    CheckIcon,
} from "@heroicons/react/24/outline";

import { springConfig } from "@/shared/utils/ui/animations";

import { useNoteComments } from "../hooks/useNoteComments";

import { TimeAgo } from "./TimeAgo";

interface Comment {
    id: string;
    content: string;
    createdAt: Date | string;
    updatedAt?: Date | string;
    authorId: string;
    author?: {
        id: string;
        name?: string | null;
        email?: string | null;
        image?: string | null;
    };
    _count?: {
        replies: number;
    };
}

interface NoteCommentsSectionProps {
    noteId: string;
    currentUserId?: string;
    userRole?: string;
    currentUserName?: string;
}

export function NoteCommentsSection({
    noteId,
    currentUserId,
    userRole,
    currentUserName,
}: NoteCommentsSectionProps) {
    const [newComment, setNewComment] = useState("");
    const [editingCommentId, setEditingCommentId] = useState<string | null>(
        null,
    );
    const [editingContent, setEditingContent] = useState("");

    const {
        comments = [],
        total = 0,
        hasMore = false,
        isLoading = false,
        isCreating = false,
        createComment,
        updateComment,
        deleteComment,
        loadMore,
    } = useNoteComments(noteId);

    const handleSubmitComment = async () => {
        if (!newComment.trim()) return;

        const result = await createComment(newComment.trim());

        if (result.success) {
            setNewComment("");
        }
    };

    const handleEditComment = (comment: Comment) => {
        setEditingCommentId(comment.id);
        setEditingContent(comment.content);
    };

    const handleUpdateComment = async () => {
        if (!editingContent.trim() || !editingCommentId) return;

        const result = await updateComment(
            editingCommentId,
            editingContent.trim(),
        );

        if (result.success) {
            setEditingCommentId(null);
            setEditingContent("");
        }
    };

    const handleCancelEdit = () => {
        setEditingCommentId(null);
        setEditingContent("");
    };

    const handleDeleteComment = async (commentId: string) => {
        if (window.confirm("¿Estás seguro de eliminar este comentario?")) {
            await deleteComment(commentId);
        }
    };

    const getInitials = (name?: string | null): string => {
        if (!name) return "U";
        const parts = name.trim().split(" ");

        if (parts.length === 1) return parts[0].charAt(0).toUpperCase();

        return (
            parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
        ).toUpperCase();
    };

    return (
        <motion.div
            animate={{ opacity: 1, height: "auto" }}
            className="border-t border-gray-200/50 dark:border-gray-700/50 pt-3"
            exit={{ opacity: 0, height: 0 }}
            initial={{ opacity: 0, height: 0 }}
            transition={springConfig.gentle}
        >
            {/* Nuevo comentario */}
            <div className="px-4 pb-3">
                <div className="flex gap-2 items-start">
                    <Avatar
                        className="flex-shrink-0"
                        fallback={
                            <div className="font-semibold w-full h-full flex items-center justify-center text-sm select-none text-black dark:text-white">
                                {getInitials(currentUserName || "Usuario")}
                            </div>
                        }
                        size="sm"
                    />
                    <div className="flex-1 space-y-2">
                        <Textarea
                            classNames={{
                                input: "text-sm",
                                inputWrapper: "bg-gray-50 dark:bg-gray-800/50",
                            }}
                            endContent={
                                <Button
                                    isIconOnly
                                    className="mb-1"
                                    color="primary"
                                    isDisabled={!newComment.trim()}
                                    isLoading={isCreating}
                                    size="sm"
                                    variant="flat"
                                    onClick={handleSubmitComment}
                                >
                                    <PaperAirplaneIcon className="h-4 w-4" />
                                </Button>
                            }
                            maxRows={3}
                            minRows={1}
                            placeholder="Escribe un comentario..."
                            value={newComment}
                            onChange={(e) => setNewComment(e.target.value)}
                        />
                    </div>
                </div>
            </div>

            {/* Lista de comentarios */}
            <div className="max-h-60 overflow-y-auto">
                {isLoading ? (
                    <div className="flex justify-center py-4">
                        <Spinner size="sm" />
                    </div>
                ) : comments.length === 0 ? (
                    <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
                        Sé el primero en comentar
                    </div>
                ) : (
                    <div className="space-y-3 px-4 pb-3">
                        <AnimatePresence mode="popLayout">
                            {Array.isArray(comments) &&
                                comments.map((comment: Comment) => (
                                    <motion.div
                                        key={comment.id}
                                        layout
                                        animate={{ opacity: 1, y: 0 }}
                                        className="flex gap-2 items-start group"
                                        exit={{ opacity: 0, y: -10 }}
                                        initial={{ opacity: 0, y: 10 }}
                                        transition={springConfig.gentle}
                                    >
                                        <Avatar
                                            className="flex-shrink-0"
                                            fallback={
                                                <div className="font-semibold w-full h-full flex items-center justify-center text-xs select-none text-black dark:text-white">
                                                    {getInitials(
                                                        comment.author?.name,
                                                    )}
                                                </div>
                                            }
                                            size="sm"
                                            src={
                                                comment.author?.image ||
                                                undefined
                                            }
                                        />
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                                    {comment.author?.name ||
                                                        "Usuario"}
                                                </span>
                                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                                    <TimeAgo
                                                        date={comment.createdAt}
                                                    />
                                                </span>
                                                {comment.authorId ===
                                                    currentUserId && (
                                                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <Tooltip
                                                            content="Editar"
                                                            placement="top"
                                                        >
                                                            <Button
                                                                isIconOnly
                                                                className="h-6 w-6 min-w-6"
                                                                size="sm"
                                                                variant="light"
                                                                onClick={() =>
                                                                    handleEditComment(
                                                                        comment,
                                                                    )
                                                                }
                                                            >
                                                                <PencilIcon className="h-3 w-3" />
                                                            </Button>
                                                        </Tooltip>
                                                        <Tooltip
                                                            content="Eliminar"
                                                            placement="top"
                                                        >
                                                            <Button
                                                                isIconOnly
                                                                className="h-6 w-6 min-w-6"
                                                                color="danger"
                                                                size="sm"
                                                                variant="light"
                                                                onClick={() =>
                                                                    handleDeleteComment(
                                                                        comment.id,
                                                                    )
                                                                }
                                                            >
                                                                <TrashIcon className="h-3 w-3" />
                                                            </Button>
                                                        </Tooltip>
                                                    </div>
                                                )}
                                            </div>
                                            {editingCommentId === comment.id ? (
                                                <div className="space-y-2">
                                                    <Textarea
                                                        classNames={{
                                                            input: "text-xs",
                                                            inputWrapper:
                                                                "bg-gray-50 dark:bg-gray-800/50",
                                                        }}
                                                        maxRows={3}
                                                        minRows={1}
                                                        value={editingContent}
                                                        onChange={(e) =>
                                                            setEditingContent(
                                                                e.target.value,
                                                            )
                                                        }
                                                    />
                                                    <div className="flex gap-1">
                                                        <Button
                                                            className="h-7"
                                                            color="primary"
                                                            isDisabled={
                                                                !editingContent.trim()
                                                            }
                                                            size="sm"
                                                            variant="flat"
                                                            onClick={
                                                                handleUpdateComment
                                                            }
                                                        >
                                                            <CheckIcon className="h-3 w-3" />
                                                            Guardar
                                                        </Button>
                                                        <Button
                                                            className="h-7"
                                                            size="sm"
                                                            variant="flat"
                                                            onClick={
                                                                handleCancelEdit
                                                            }
                                                        >
                                                            <XMarkIcon className="h-3 w-3" />
                                                            Cancelar
                                                        </Button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <p className="text-sm text-gray-700 dark:text-gray-300 break-words">
                                                    {comment.content}
                                                </p>
                                            )}
                                        </div>
                                    </motion.div>
                                ))}
                        </AnimatePresence>

                        {hasMore && (
                            <div className="text-center pt-2">
                                <Button
                                    className="text-xs"
                                    size="sm"
                                    variant="flat"
                                    onClick={loadMore}
                                >
                                    Cargar más comentarios
                                </Button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </motion.div>
    );
}
