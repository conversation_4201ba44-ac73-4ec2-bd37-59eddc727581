import { notFound } from "next/navigation";

import { getRemission } from "@/features/remissions/actions";

import RemissionEditClient from "./RemissionEditClient";

export default async function RemissionEditPage({
    params,
}: {
    params: Promise<{ id: string }>;
}) {
    const { id } = await params;
    const remission = await getRemission(id);

    if (!remission) {
        notFound();
    }

    return <RemissionEditClient remission={remission as any} />;
}

export async function generateMetadata({
    params,
}: {
    params: Promise<{ id: string }>;
}) {
    const { id } = await params;
    const remission = await getRemission(id);

    if (!remission) {
        return {
            title: "Remisión no encontrada",
        };
    }

    return {
        title: `Editar Remisión ${remission.folio}`,
        description: `Editar remisión ${remission.folio} para ${remission.contractor?.name}`,
    };
}
