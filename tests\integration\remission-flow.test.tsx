/**
 * @jest-environment jsdom
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { act } from 'react-dom/test-utils'
import { useRouter } from 'next/navigation'
import { SummaryStep } from '@/features/assignments/components/wizard/steps/SummaryStep'
import { RemissionPreviewModal } from '@/components/remissions/RemissionPreviewModal'
import { createAssignments } from '@/features/assignments/actions/create'
import { downloadRemissionPDF } from '@/lib/pdf/generator'
import { toast } from 'sonner'

// Mocks
jest.mock('next/navigation')
jest.mock('@/features/assignments/actions/create')
jest.mock('@/lib/pdf/generator')
jest.mock('sonner')

// Mock de datos de prueba
const mockFormData = {
  orderNumber: 'ORD-2025-001',
  contractorId: 'contractor-123',
  productionLineIds: ['line-1', 'line-2'],
  workType: 'CORTE',
  workIds: ['work-1', 'work-2'],
  parts: [
    {
      id: 'part-1',
      name: 'Frontal',
      quantity: 100,
      unitPrice: 25.50,
      subtotal: 2550,
    },
    {
      id: 'part-2',
      name: 'Trasero',
      quantity: 100,
      unitPrice: 30.00,
      subtotal: 3000,
    },
  ],
  startDate: new Date('2025-06-03'),
  endDate: new Date('2025-06-10'),
  paymentType: 'PIEZA',
  externalNotes: 'Entregar en empaque especial',
  internalNotes: 'Cliente preferencial',
  contractorInfo: {
    name: 'Juan Pérez',
    rfc: 'PERJ850101ABC',
    address: 'Calle Principal 123',
    phone: '+52 ************',
  },
}

const mockRemissionData = {
  id: 'remission-123',
  folio: 'REM-2025-001',
  date: new Date('2025-06-03'),
  contractor: mockFormData.contractorInfo,
  items: mockFormData.parts,
  subtotal: 5550,
  tax: 888,
  total: 6438,
  externalNotes: mockFormData.externalNotes,
  internalNotes: mockFormData.internalNotes,
  status: 'active' as const,
}

describe('Flujo de Remisión Automática E2E', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }

  const user = userEvent.setup()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(createAssignments as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        assignments: [{ id: 'assignment-1' }, { id: 'assignment-2' }],
        remission: mockRemissionData,
      },
    })
    ;(downloadRemissionPDF as jest.Mock).mockResolvedValue(undefined)
  })

  describe('Caso de Éxito: Flujo Completo', () => {
    it('debe crear asignaciones y mostrar automáticamente el preview de remisión', async () => {
      const onBack = jest.fn()
      const onRestart = jest.fn()

      render(
        <SummaryStep
          formData={mockFormData}
          onBack={onBack}
          onRestart={onRestart}
        />
      )

      // Verificar que el resumen se muestra correctamente
      expect(screen.getByText('Resumen de la Asignación')).toBeInTheDocument()
      expect(screen.getByText('ORD-2025-001')).toBeInTheDocument()
      expect(screen.getByText('Juan Pérez')).toBeInTheDocument()

      // Hacer clic en el botón de crear asignación
      const createButton = screen.getByRole('button', { name: /crear asignación/i })
      await user.click(createButton)

      // Verificar que se muestra el estado de procesamiento
      await waitFor(() => {
        expect(screen.getByText(/creando asignación/i)).toBeInTheDocument()
      })

      // Verificar que se llamó a createAssignments
      expect(createAssignments).toHaveBeenCalledWith(mockFormData)

      // Esperar a que se muestre el modal de remisión
      await waitFor(() => {
        expect(screen.getByText('Vista Previa de Remisión')).toBeInTheDocument()
        expect(screen.getByText('REM-2025-001')).toBeInTheDocument()
      })

      // Verificar que se muestra el toast de éxito
      expect(toast.success).toHaveBeenCalledWith(
        expect.stringContaining('Asignación creada'),
        expect.any(Object)
      )
    })

    it('debe permitir editar el preview de remisión antes de generar el PDF', async () => {
      render(<RemissionPreviewModal
        isOpen={true}
        onClose={jest.fn()}
        remissionData={mockRemissionData}
        onGeneratePDF={downloadRemissionPDF}
      />)

      // Verificar que el modal está abierto
      expect(screen.getByText('Vista Previa de Remisión')).toBeInTheDocument()

      // Activar modo edición
      const editButton = screen.getByRole('button', { name: /editar/i })
      await user.click(editButton)

      // Verificar que los campos son editables
      const folioInput = screen.getByDisplayValue('REM-2025-001')
      expect(folioInput).toHaveAttribute('contenteditable', 'true')

      // Editar el folio
      await user.clear(folioInput)
      await user.type(folioInput, 'REM-2025-002')

      // Verificar advertencia de cambios sin guardar
      expect(screen.getByText(/cambios sin guardar/i)).toBeInTheDocument()
    })

    it('debe generar y descargar el PDF correctamente', async () => {
      render(<RemissionPreviewModal
        isOpen={true}
        onClose={jest.fn()}
        remissionData={mockRemissionData}
        onGeneratePDF={downloadRemissionPDF}
      />)

      // Hacer clic en generar PDF
      const pdfButton = screen.getByRole('button', { name: /generar pdf/i })
      await user.click(pdfButton)

      // Verificar que se llamó a downloadRemissionPDF
      await waitFor(() => {
        expect(downloadRemissionPDF).toHaveBeenCalledWith(mockRemissionData)
      })

      // Verificar notificación de éxito
      expect(toast.success).toHaveBeenCalledWith(
        'PDF descargado exitosamente',
        expect.any(Object)
      )
    })
  })

  describe('Casos de Error', () => {
    it('debe manejar errores al crear asignaciones', async () => {
      ;(createAssignments as jest.Mock).mockRejectedValue(
        new Error('Error de conexión')
      )

      const onBack = jest.fn()
      const onRestart = jest.fn()

      render(
        <SummaryStep
          formData={mockFormData}
          onBack={onBack}
          onRestart={onRestart}
        />
      )

      const createButton = screen.getByRole('button', { name: /crear asignación/i })
      await user.click(createButton)

      // Verificar mensaje de error
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'Error al crear las asignaciones',
          expect.objectContaining({
            description: 'Error de conexión',
          })
        )
      })

      // Verificar que no se muestra el modal de remisión
      expect(screen.queryByText('Vista Previa de Remisión')).not.toBeInTheDocument()
    })

    it('debe manejar errores al generar el PDF', async () => {
      ;(downloadRemissionPDF as jest.Mock).mockRejectedValue(
        new Error('Error al generar PDF')
      )

      render(<RemissionPreviewModal
        isOpen={true}
        onClose={jest.fn()}
        remissionData={mockRemissionData}
        onGeneratePDF={downloadRemissionPDF}
      />)

      const pdfButton = screen.getByRole('button', { name: /generar pdf/i })
      await user.click(pdfButton)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          'Error al descargar el PDF',
          expect.objectContaining({
            description: 'Error al generar PDF',
          })
        )
      })
    })

    it('debe permitir reintentar después de un error', async () => {
      // Primero falla, luego tiene éxito
      ;(createAssignments as jest.Mock)
        .mockRejectedValueOnce(new Error('Error temporal'))
        .mockResolvedValueOnce({
          success: true,
          data: {
            assignments: [{ id: 'assignment-1' }],
            remission: mockRemissionData,
          },
        })

      const onBack = jest.fn()
      const onRestart = jest.fn()

      render(
        <SummaryStep
          formData={mockFormData}
          onBack={onBack}
          onRestart={onRestart}
        />
      )

      // Primer intento - falla
      const createButton = screen.getByRole('button', { name: /crear asignación/i })
      await user.click(createButton)

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalled()
      })

      // Segundo intento - éxito
      await user.click(createButton)

      await waitFor(() => {
        expect(screen.getByText('Vista Previa de Remisión')).toBeInTheDocument()
      })
    })
  })

  describe('Integración con ProcessingPanel', () => {
    it('debe mostrar el progreso durante la generación', async () => {
      // Simular demora en la creación
      ;(createAssignments as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          success: true,
          data: {
            assignments: [{ id: 'assignment-1' }],
            remission: mockRemissionData,
          },
        }), 1000))
      )

      const onBack = jest.fn()
      const onRestart = jest.fn()

      render(
        <SummaryStep
          formData={mockFormData}
          onBack={onBack}
          onRestart={onRestart}
        />
      )

      const createButton = screen.getByRole('button', { name: /crear asignación/i })
      await user.click(createButton)

      // Verificar estados del proceso
      expect(screen.getByText(/creando asignación/i)).toBeInTheDocument()
      
      // Esperar a que se complete
      await waitFor(() => {
        expect(screen.getByText(/remisión lista/i)).toBeInTheDocument()
      }, { timeout: 2000 })
    })
  })
})
