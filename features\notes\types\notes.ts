// Definición de tipos para el sistema de notas

// Tipo para el autor de una nota
export interface NoteAuthor {
    id: string;
    name?: string;
    image?: string | null;
}

// Tipo para opciones de importancia o estado
export interface NoteOption {
    id: string;
    name: string;
    color?: string;
}

// Tipo para los datos de una nota
export interface NoteData {
    id: string;
    content: string;
    createdAt: string | Date;
    updatedAt: string | Date;
    importanceId?: string;
    statusId?: string;
    author?: NoteAuthor;
    orderId?: string;
    _count?: {
        comments?: number;
    };
}

// Tipo para comentarios en una nota
export interface NoteComment {
    id: string;
    content: string;
    createdAt: string | Date;
    author?: NoteAuthor;
    authorId?: string; // ID del autor para verificación de permisos
    noteId: string;
    parentId?: string | null;
    _count?: {
        replies?: number;
    };
}

// Tipo para comentarios con sus respuestas anidadas
export interface NoteCommentWithReplies extends NoteComment {
    replies?: NoteCommentWithReplies[];
}

// Tipo para los filtros de notas
export interface NoteFilters {
    searchText?: string;
    importanceId?: string;
    statusId?: string;
    authorId?: string;
    fromDate?: Date;
    toDate?: Date;
}

// Tipo para los datos necesarios para crear o actualizar una nota
export interface NoteFormData {
    content: string;
    orderId: string;
    statusId: string;
    importanceId: string;
}

// Tipo para la actualización parcial de una nota
export interface NoteUpdateData {
    noteId: string;
    content?: string;
    statusId?: string;
    importanceId?: string;
}
