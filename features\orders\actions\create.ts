"use server";

import { CONFIG_NAMES } from "@/constants/config-names";
import { getNoteImportanceByName } from "@/shared/lib/config-cache";
import { prisma } from "@/shared/lib/prisma";

import {
    ActionResponse,
    createErrorResponse,
    revalidateCache,
    parseDate,
    getCurrentUser,
} from "./utils";

/**
 * Prepara los datos de talla para una prenda
 */
function prepareGarmentSizeData(
    garmentId: string,
    size: { sizeId: string; quantity: number },
) {
    return {
        garmentId,
        sizeId: size.sizeId,
        totalQuantity: size.quantity, // Campo requerido por el esquema
    };
}

/**
 * Crea una nueva orden
 */
export async function createOrder(data: {
    customerId: string;
    subCustomerId?: string | null;
    statusId: string;
    receivedDate: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
    batch?: string | null;
    estimatedDeliveryDate?: string | null;
}): Promise<ActionResponse<any>> {
    try {
        // Validación básica de datos
        if (!data.customerId) {
            return {
                success: false,
                error: "El cliente es requerido",
            };
        }

        if (!data.statusId) {
            return {
                success: false,
                error: "El estado es requerido",
            };
        }

        if (!data.receivedDate) {
            return {
                success: false,
                error: "La fecha de recepción es requerida",
            };
        }

        // Crear la orden en la base de datos
        const order = await prisma.order.create({
            data: {
                customerId: data.customerId,
                subCustomerId: data.subCustomerId || null,
                statusId: data.statusId,
                receivedDate: new Date(data.receivedDate),
                transferNumber: data.transferNumber || null,
                cutOrder: data.cutOrder || null,
                batch: data.batch || null,
                estimatedDeliveryDate: await parseDate(
                    data.estimatedDeliveryDate,
                ),
            },
        });

        // Revalidar rutas y tags relacionados con órdenes
        revalidateCache(order.id);

        return {
            success: true,
            data: order,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Función completa para crear una nueva orden con todos sus componentes
 * Esta es una función pública que puede ser llamada desde el cliente
 */
export async function submitOrder(data: {
    customerId: string;
    subCustomerId?: string | null;
    statusId: string;
    receivedDate: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
    batch?: string | null;
    estimatedDeliveryDate?: string | null;
    parts?: { code: string }[];
    garments?: {
        modelId: string;
        colorId: string;
        sizes: { sizeId: string; quantity: number }[];
    }[];
    notes?: { content: string; statusId: string }[];
}): Promise<ActionResponse<any>> {
    try {
        // Iniciando creación de orden con datos completos - comentario removido

        // Validación básica de datos
        if (!data.customerId) {
            return {
                success: false,
                error: "El cliente es requerido",
            };
        }

        if (!data.statusId) {
            return {
                success: false,
                error: "El estado es requerido",
            };
        }

        if (!data.receivedDate) {
            return {
                success: false,
                error: "La fecha de recepción es requerida",
            };
        }

        // Crear la orden en la base de datos
        const order = await prisma.order.create({
            data: {
                customerId: data.customerId,
                subCustomerId: data.subCustomerId || null,
                statusId: data.statusId,
                receivedDate: new Date(data.receivedDate),
                transferNumber: data.transferNumber || null,
                cutOrder: data.cutOrder || null,
                batch: data.batch || null,
                estimatedDeliveryDate: await parseDate(
                    data.estimatedDeliveryDate,
                ),
            },
        });

        // REMOVED: console.log(`[submitOrder] Orden base creada con ID: ${order.id}`);

        // Verificar si se enviaron partidas explícitamente
        const hasParts = data.parts && data.parts.length > 0;
        const garmentsByModelColor = new Map<
            string,
            { model: string; color: string }
        >();

        // Si hay partidas explícitas, crearlas
        if (hasParts) {
            // Creando partidas explícitas - comentario removido
            await prisma.orderPart.createMany({
                data: data.parts!.map((part) => ({
                    orderId: order.id,
                    code: part.code,
                })),
            });
        }

        // Si hay prendas, procesarlas y recopilar información para partidas automáticas si es necesario
        if (data.garments && data.garments.length > 0) {
            // Procesando prendas - comentario removido

            // Crear cada prenda
            for (const garment of data.garments) {
                // Crear la prenda base
                const newGarment = await prisma.garment.create({
                    data: {
                        orderId: order.id,
                        modelId: garment.modelId,
                        colorId: garment.colorId,
                    },
                });

                // Prenda creada - comentario removido

                // Registrar información de modelo/color para partidas automáticas
                if (!hasParts) {
                    const key = `${garment.modelId}_${garment.colorId}`;

                    garmentsByModelColor.set(key, {
                        model: garment.modelId,
                        color: garment.colorId,
                    });
                }

                // Crear tallas asociadas a la prenda
                if (garment.sizes && garment.sizes.length > 0) {
                    // Creando tallas para prenda - comentario removido

                    await prisma.garmentSize.createMany({
                        data: garment.sizes.map((size) =>
                            prepareGarmentSizeData(newGarment.id, size),
                        ),
                    });
                }
            }
        }

        // Si no se especificaron partidas pero hay prendas, crear partidas automáticas basadas en modelo/color
        if (!hasParts && garmentsByModelColor.size > 0) {
            // Generando partidas automáticas - comentario removido

            let partCode = 1;
            const partsToCreate = Array.from(garmentsByModelColor.values()).map(
                (_) => ({
                    orderId: order.id,
                    code: `P${partCode++}`,
                }),
            );

            await prisma.orderPart.createMany({
                data: partsToCreate,
            });
        }

        // Si hay notas, crearlas
        if (data.notes && data.notes.length > 0) {
            // REMOVED: console.log(`[submitOrder] Creando ${data.notes.length} notas`);

            // Obtener el usuario actual para la autoría de las notas
            const user = await getCurrentUser();

            if (!user || !user.id) {
                // No se pudo obtener el usuario actual para las notas - comentario removido
            } else {
                // Para cada nota, crear en la base de datos con el schema correcto
                for (const note of data.notes) {
                    // Obtener el ID de importancia "Medio" usando el nuevo sistema
                    const mediumImportance = await getNoteImportanceByName(
                        CONFIG_NAMES.noteImportance.MEDIUM,
                    );

                    if (!mediumImportance) {
                        throw new Error(
                            "No se encontró la importancia 'Medio' en la base de datos",
                        );
                    }

                    await prisma.note.create({
                        data: {
                            content: note.content,
                            statusId: note.statusId,
                            orderId: order.id,
                            authorId: user.id,
                            importanceId: mediumImportance.id,
                        },
                    });
                }
            }
        }

        // Obtener la orden completa con todos sus componentes
        const completeOrder = await prisma.order.findUnique({
            where: { id: order.id },
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
                notes: {
                    include: {
                        status: true,
                        author: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                image: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
        });

        // Revalidar caché para esta orden
        revalidateCache(order.id);

        // Orden completa creada con éxito - comentario removido

        return {
            success: true,
            data: completeOrder,
        };
    } catch (error) {
        // REMOVED: console.error("[submitOrder] Error:", error);

        return createErrorResponse(error);
    }
}
