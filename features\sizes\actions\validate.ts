"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Valida si un código de talla es único
 */
export async function validateSizeCode(code: string, excludeId?: string) {
    if (!code || code.length < 1) {
        return { success: true, data: { isValid: false } };
    }

    return await handleDbError(async () => {
        const existingSize = await db.size.findFirst({
            where: {
                code: {
                    equals: code,
                    mode: "insensitive" as Prisma.QueryMode,
                },
                id: excludeId ? { not: excludeId } : undefined,
            },
        });

        return { isValid: !existingSize };
    }, "Error al validar código de talla");
}
