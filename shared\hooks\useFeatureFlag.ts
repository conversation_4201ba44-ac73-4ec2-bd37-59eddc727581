"use client";

import { useEffect } from "react";

import { FEATURES, type FeatureFlag } from "@/shared/config/features";
import { trackFeatureFlagEvaluation } from "@/shared/utils/analytics";

/**
 * Hook to check if a feature flag is enabled
 * @param flag - The feature flag to check
 * @returns boolean indicating if the feature is enabled
 */
export function useFeatureFlag(flag: FeatureFlag): boolean {
    const isEnabled = FEATURES[flag];

    // Track feature flag evaluation
    useEffect(() => {
        trackFeatureFlagEvaluation(flag, isEnabled as boolean);
    }, [flag, isEnabled]);

    return isEnabled as boolean;
}

/**
 * Hook to get all feature flags
 * @returns The complete features configuration object
 */
export function useFeatureFlags() {
    return FEATURES;
}
