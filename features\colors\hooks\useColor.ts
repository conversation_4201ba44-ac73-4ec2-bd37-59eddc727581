"use client";

import type { SWRConfiguration } from "swr";

import useS<PERSON> from "swr";
import { useState } from "react";

import {
    getColor,
    getColors,
    createColor as create,
    updateColor as update,
    deleteColor as remove,
} from "@/features/colors/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

// Obtener helpers de revalidación para el cliente
const { revalidateData } = createClientRevalidation("color");

export interface Color {
    id: string;
    name: string;
    hexCode?: string;
    createdAt: string;
    updatedAt: string;
    _count?: {
        garments: number;
    };
}

export interface ColorsResponse {
    colors: Color[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

/**
 * Hook para obtener todos los colores
 * @param options Opciones de filtrado y paginación
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de colores, paginación, estado de carga y errores
 */
export function useColors(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
    config?: SWRConfiguration,
) {
    // Convertir opciones a cadena para clave de caché
    const optionsKey = JSON.stringify(options);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        ["colors", optionsKey],
        async () => getColors(options),
        {
            // Configuración de revalidación mejorada
            revalidateOnFocus: true,
            revalidateIfStale: true,
            revalidateOnReconnect: true,
            dedupingInterval: 0, // Sin deduplicación para esta vista principal
            revalidateOnMount: true,
            keepPreviousData: true,
            // Combinar con configuración personalizada
            ...config,
        },
    );

    // Integrar listener de revalidación
    const { isRevalidating } = useRevalidationListener("colors");

    return {
        colors: (data?.data as any)?.colors || [],
        pagination: (data?.data as any)?.pagination,
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para obtener la información de un color por su ID
 * @param id ID del color a cargar
 * @param config Configuración opcional para SWR
 * @returns Objeto con los datos del color, estado de carga y errores
 */
export function useColor(
    id: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Definir estructura de respuesta de la Server Action
    type ColorResponse =
        | { data: Color; success: true; error?: undefined }
        | { success: false; error: string; data?: undefined };

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR<ColorResponse>(
        id ? ["color", id] : null,
        async () => (id ? getColor(id) : null) as Promise<ColorResponse>,
        {
            // Configuración de revalidación mejorada
            revalidateOnFocus: true,
            revalidateIfStale: true,
            revalidateOnReconnect: true,
            // Combinar con configuración personalizada
            ...config,
        },
    );

    return {
        color: data?.success ? data.data : undefined,
        isLoading,
        isError: !!error || (data && !data.success),
        error: data?.error || error?.message,
        mutate,
    };
}

/**
 * Hook para crear un nuevo color
 * @returns Función para crear un color y estado de la operación
 */
export function useCreateColor() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createColor = async (data: { name: string; hexCode?: string }) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = (await create({
                name: data.name,
                hexCode: data.hexCode,
            })) || { success: false, error: "Error desconocido" };

            // Si se creó correctamente, revalidar todos los datos relacionados
            if (result.success) {
                if (process.env.NODE_ENV !== "production") {
                    // // REMOVED: console.log(
                    //     "Color creado correctamente, revalidando datos...",
                    // );
                }

                // Revalidar datos mediante la función genérica
                if ((result.data as any)?.id) {
                    await revalidateData((result.data as any).id);
                } else {
                    await revalidateData();
                }
            } else {
                setError(result.error || "Error desconocido al crear el color");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al crear el color";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createColor,
        isCreating: isLoading,
        createError: error,
    };
}

/**
 * Hook para actualizar un color existente
 * @returns Función para actualizar un color y estado de la operación
 */
export function useUpdateColor() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateColor = async (
        id: string,
        data: { name: string; hexCode?: string },
    ) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = (await update(id, {
                name: data.name,
                hexCode: data.hexCode,
            })) || { success: false, error: "Error desconocido" };

            // Si se actualizó correctamente, revalidar todos los datos relacionados
            if (result.success) {
                if (process.env.NODE_ENV !== "production") {
                    // // REMOVED: console.log(
                    //     `Color ${id} actualizado correctamente, revalidando datos...`,
                    // );
                }

                // Revalidar datos mediante la función genérica
                await revalidateData(id);
            } else {
                setError(
                    result.error || "Error desconocido al actualizar el color",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al actualizar el color";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        updateColor,
        isUpdating: isLoading,
        updateError: error,
    };
}

/**
 * Hook para eliminar un color
 * @returns Función para eliminar un color y estado de la operación
 */
export function useDeleteColor() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteColor = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = (await remove(id)) || {
                success: false,
                error: "Error desconocido",
            };

            // Si se eliminó correctamente, revalidar todos los datos relacionados
            if (result.success) {
                if (process.env.NODE_ENV !== "production") {
                    // // REMOVED: console.log(
                    //     `Color ${id} eliminado correctamente, revalidando datos...`,
                    // );
                }

                // Revalidar datos mediante la función genérica
                await revalidateData();
            } else {
                setError(
                    result.error || "Error desconocido al eliminar el color",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al eliminar el color";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        deleteColor,
        isDeleting: isLoading,
        deleteError: error,
    };
}
