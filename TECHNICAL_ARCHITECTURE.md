# 🏗️ Arquitectura Técnica - Sistema Lohari

## 📐 Visión Arquitectónica

El sistema Lohari está construido siguiendo principios de arquitectura moderna:

- **Domain-Driven Design (DDD)**: Organización por dominios de negocio
- **Clean Architecture**: Separación clara de responsabilidades  
- **Server-First**: Aprovechando las capacidades de React Server Components
- **Type-Safe**: TypeScript end-to-end con validación runtime
- **Performance-First**: Optimizado para carga rápida y UX fluida

## 🎯 Principios de Diseño

### 1. Separación de Responsabilidades
```
┌─────────────────────────────────────────────────────┐
│                   Presentation Layer                 │
│         (React Components, Pages, UI Logic)          │
├─────────────────────────────────────────────────────┤
│                  Application Layer                   │
│        (Server Actions, API Routes, DTOs)            │
├─────────────────────────────────────────────────────┤
│                   Domain Layer                       │
│         (Business Logic, Entities, Rules)            │
├─────────────────────────────────────────────────────┤
│                Infrastructure Layer                  │
│      (Database, External APIs, File System)         │
└─────────────────────────────────────────────────────┘
```

### 2. Feature-Based Architecture
Cada módulo es autónomo y contiene todas las capas necesarias:

```
features/orders/
├── actions/        # Application Layer
├── components/     # Presentation Layer  
├── schemas/        # Domain Layer
├── types/          # Domain Layer
├── hooks/          # Presentation Layer
└── utils/          # Cross-cutting concerns
```

### 3. Data Flow Unidireccional
```
User Input → Server Action → Database → Revalidation → UI Update
     ↑                                                      ↓
     └─────────────── Optimistic Updates ←─────────────────┘
```

## 🛠️ Stack Tecnológico Detallado

### Frontend Architecture

#### Rendering Strategy
- **Server Components** (default): Para contenido estático y data fetching
- **Client Components**: Solo para interactividad (formularios, modales)
- **Streaming SSR**: Loading states granulares con Suspense
- **ISR**: Para páginas de catálogo semi-estáticas

#### State Management
```typescript
// 1. Server State - SWR
const { data, error, mutate } = useSWR(
  `/api/orders?${params}`,
  fetcher,
  {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000
  }
)

// 2. Client State - React State/Context
const [filters, setFilters] = useState<FilterState>({
  search: '',
  status: 'all',
  dateRange: null
})

// 3. URL State - Next.js Router
const searchParams = useSearchParams()
const router = useRouter()
```

#### Component Architecture
```typescript
// Server Component (default)
export default async function OrdersPage() {
  const orders = await getOrders() // Direct DB call
  return <OrdersList orders={orders} />
}

// Client Component (when needed)
'use client'
export function OrderForm({ order }: Props) {
  const [formData, setFormData] = useState(order)
  // Interactive logic here
}
```

### Backend Architecture

#### Server Actions Pattern
```typescript
// features/orders/actions/create.ts
'use server'

export async function createOrder(data: OrderInput) {
  // 1. Validate input
  const validated = orderSchema.parse(data)
  
  // 2. Business logic
  const folio = await generateOrderFolio()
  
  // 3. Database transaction
  const order = await prisma.$transaction(async (tx) => {
    const order = await tx.order.create({
      data: { ...validated, folio }
    })
    
    await tx.auditLog.create({
      data: { action: 'ORDER_CREATED', orderId: order.id }
    })
    
    return order
  })
  
  // 4. Revalidate cache
  revalidatePath('/dashboard/orders')
  
  return { success: true, order }
}
```

#### API Routes (Minimal Usage)
```typescript
// app/api/remissions/pdf/route.ts
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')
  
  const remission = await getRemission(id)
  const pdf = await generatePDF(remission)
  
  return new Response(pdf, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${remission.folio}.pdf"`
    }
  })
}
```

### Database Architecture

#### Prisma Schema Organization
```prisma
// Base Models
model User {
  id            String   @id @default(cuid())
  email         String   @unique
  // Audit fields
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  // Relations
  role          Role     @relation(...)
  notes         Note[]
}

// Business Models  
model Order {
  id                    String    @id @default(cuid())
  // Business fields
  transferNumber        String?
  // System fields
  version              Int       @default(1)
  isDeleted            Boolean   @default(false)
  // Audit
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  // Relations with cascade rules
  garments             Garment[] @relation(onDelete: Cascade)
}
```

#### Database Patterns

**1. Soft Deletes**
```typescript
// Instead of DELETE
await prisma.order.update({
  where: { id },
  data: { isDeleted: true, deletedAt: new Date() }
})
```

**2. Optimistic Locking**
```typescript
await prisma.order.update({
  where: { id, version: currentVersion },
  data: { 
    ...updates,
    version: { increment: 1 }
  }
})
```

**3. Audit Trail**
```typescript
await prisma.$transaction([
  prisma.order.update({ where: { id }, data }),
  prisma.auditLog.create({
    data: {
      entityType: 'ORDER',
      entityId: id,
      action: 'UPDATE',
      changes: JSON.stringify(changes),
      userId: session.user.id
    }
  })
])
```

## 🔄 Patrones de Diseño

### 1. Repository Pattern
```typescript
// shared/lib/repositories/order.repository.ts
export class OrderRepository {
  async findById(id: string) {
    return prisma.order.findUnique({
      where: { id },
      include: this.defaultIncludes()
    })
  }
  
  async findMany(filters: OrderFilters) {
    return prisma.order.findMany({
      where: this.buildWhereClause(filters),
      include: this.defaultIncludes(),
      orderBy: { createdAt: 'desc' }
    })
  }
  
  private defaultIncludes() {
    return {
      customer: true,
      status: true,
      garments: {
        include: {
          model: true,
          color: true
        }
      }
    }
  }
}
```

### 2. Service Pattern
```typescript
// features/orders/services/order.service.ts
export class OrderService {
  constructor(
    private orderRepo: OrderRepository,
    private notificationService: NotificationService
  ) {}
  
  async createOrder(data: CreateOrderDTO) {
    // Business logic
    const order = await this.orderRepo.create(data)
    
    // Side effects
    await this.notificationService.notifyNewOrder(order)
    
    return order
  }
}
```

### 3. Factory Pattern
```typescript
// features/remissions/factories/pdf.factory.ts
export class PDFFactory {
  static create(type: 'remission' | 'order' | 'report') {
    switch(type) {
      case 'remission':
        return new RemissionPDF()
      case 'order':
        return new OrderPDF()
      case 'report':
        return new ReportPDF()
    }
  }
}
```

### 4. Strategy Pattern
```typescript
// features/orders/strategies/pricing.strategy.ts
interface PricingStrategy {
  calculate(order: Order): number
}

class StandardPricing implements PricingStrategy {
  calculate(order: Order) {
    return order.garments.reduce((sum, g) => sum + g.price, 0)
  }
}

class BulkPricing implements PricingStrategy {
  calculate(order: Order) {
    const total = order.garments.reduce((sum, g) => sum + g.price, 0)
    return total > 1000 ? total * 0.9 : total
  }
}
```

## 🔐 Seguridad

### Authentication Flow
```typescript
// 1. Login Request
POST /api/auth/login
{ email, password, remember }
    ↓
// 2. Validate Credentials
const user = await validateCredentials(email, password)
    ↓
// 3. Generate JWT
const token = jwt.sign(
  { userId: user.id, role: user.role },
  process.env.JWT_SECRET,
  { expiresIn: remember ? '30d' : '24h' }
)
    ↓
// 4. Set HTTP-Only Cookie
cookies().set('auth-token', token, {
  httpOnly: true,
  secure: true,
  sameSite: 'lax'
})
```

### Authorization Middleware
```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  const token = request.cookies.get('auth-token')
  
  if (!token) {
    return NextResponse.redirect('/login')
  }
  
  try {
    const payload = jwt.verify(token.value, process.env.JWT_SECRET)
    
    // Role-based access
    if (request.nextUrl.pathname.startsWith('/admin')) {
      if (payload.role !== 'ADMIN') {
        return NextResponse.redirect('/dashboard')
      }
    }
    
    // Add user to headers
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-user-id', payload.userId)
    requestHeaders.set('x-user-role', payload.role)
    
    return NextResponse.next({
      request: { headers: requestHeaders }
    })
  } catch {
    return NextResponse.redirect('/login')
  }
}
```

### Data Validation
```typescript
// schemas/order.schema.ts
export const orderSchema = z.object({
  customerId: z.string().cuid(),
  deliveryDate: z.coerce.date().min(new Date()),
  garments: z.array(
    z.object({
      modelId: z.string().cuid(),
      colorId: z.string().cuid(),
      sizes: z.record(z.string(), z.number().positive())
    })
  ).min(1),
  notes: z.string().max(500).optional()
})

// Usage in action
export async function createOrder(formData: unknown) {
  try {
    const data = orderSchema.parse(formData)
    // Proceed with validated data
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        errors: error.flatten() 
      }
    }
  }
}
```

## ⚡ Performance Optimization

### 1. Database Query Optimization
```typescript
// ❌ N+1 Query Problem
const orders = await prisma.order.findMany()
for (const order of orders) {
  order.customer = await prisma.customer.findUnique({
    where: { id: order.customerId }
  })
}

// ✅ Eager Loading
const orders = await prisma.order.findMany({
  include: {
    customer: true,
    garments: {
      include: {
        model: true,
        color: true
      }
    }
  }
})
```

### 2. Caching Strategy
```typescript
// Server-side caching with Redis
import { redis } from '@/lib/redis'

export async function getCachedOrders(filters: OrderFilters) {
  const cacheKey = `orders:${JSON.stringify(filters)}`
  
  // Try cache first
  const cached = await redis.get(cacheKey)
  if (cached) return JSON.parse(cached)
  
  // Fetch from DB
  const orders = await prisma.order.findMany(filters)
  
  // Cache for 5 minutes
  await redis.setex(cacheKey, 300, JSON.stringify(orders))
  
  return orders
}
```

### 3. Image Optimization
```typescript
// components/ProductImage.tsx
import Image from 'next/image'

export function ProductImage({ src, alt }: Props) {
  return (
    <Image
      src={src}
      alt={alt}
      width={400}
      height={400}
      quality={85}
      placeholder="blur"
      blurDataURL={generateBlurDataURL()}
      sizes="(max-width: 768px) 100vw, 400px"
      loading="lazy"
    />
  )
}
```

### 4. Bundle Optimization
```typescript
// next.config.js
module.exports = {
  experimental: {
    optimizePackageImports: ['@heroui/react', 'lodash-es']
  },
  modularizeImports: {
    '@heroui/react': {
      transform: '@heroui/{{member}}'
    },
    'lodash': {
      transform: 'lodash/{{member}}'
    }
  }
}
```

## 🧪 Testing Strategy

### Unit Testing
```typescript
// __tests__/order.service.test.ts
describe('OrderService', () => {
  let service: OrderService
  let mockRepo: jest.Mocked<OrderRepository>
  
  beforeEach(() => {
    mockRepo = createMockRepository()
    service = new OrderService(mockRepo)
  })
  
  it('should create order with folio', async () => {
    const input = { customerId: '123', garments: [...] }
    mockRepo.create.mockResolvedValue({ id: '456', folio: '2024-001' })
    
    const result = await service.createOrder(input)
    
    expect(result.folio).toMatch(/^\d{4}-\d{3}$/)
    expect(mockRepo.create).toHaveBeenCalledWith(
      expect.objectContaining({ folio: expect.any(String) })
    )
  })
})
```

### Integration Testing
```typescript
// __tests__/api/orders.test.ts
describe('Orders API', () => {
  beforeEach(async () => {
    await prisma.order.deleteMany()
  })
  
  it('POST /api/orders creates order', async () => {
    const response = await fetch('/api/orders', {
      method: 'POST',
      body: JSON.stringify({
        customerId: 'test-customer',
        garments: [...]
      })
    })
    
    expect(response.status).toBe(201)
    const order = await response.json()
    expect(order).toHaveProperty('id')
    expect(order).toHaveProperty('folio')
  })
})
```

### E2E Testing (Conceptual)
```typescript
// e2e/order-flow.spec.ts
test('complete order flow', async ({ page }) => {
  // Login
  await page.goto('/login')
  await page.fill('[name=email]', '<EMAIL>')
  await page.fill('[name=password]', 'password')
  await page.click('button[type=submit]')
  
  // Create order
  await page.goto('/dashboard/orders/new')
  await page.selectOption('[name=customerId]', 'customer-1')
  await page.fill('[name=deliveryDate]', '2024-12-31')
  
  // Add garments
  await page.click('text=Add Garment')
  await page.selectOption('[name=modelId]', 'model-1')
  await page.selectOption('[name=colorId]', 'color-1')
  await page.fill('[name=quantity]', '100')
  
  // Submit
  await page.click('text=Create Order')
  await page.waitForURL('/dashboard/orders/*')
  
  // Verify
  await expect(page.locator('h1')).toContainText('Order')
})
```

## 📊 Monitoring & Observability

### Application Monitoring
```typescript
// lib/monitoring.ts
import * as Sentry from '@sentry/nextjs'

export function captureError(error: Error, context?: any) {
  console.error(error)
  
  if (process.env.NODE_ENV === 'production') {
    Sentry.captureException(error, {
      extra: context
    })
  }
}

// Usage
try {
  await riskyOperation()
} catch (error) {
  captureError(error, { 
    operation: 'createOrder',
    userId: session.user.id 
  })
}
```

### Performance Monitoring
```typescript
// lib/performance.ts
export function measurePerformance(name: string) {
  const start = performance.now()
  
  return {
    end() {
      const duration = performance.now() - start
      
      // Log to analytics
      if (typeof window !== 'undefined') {
        window.gtag?.('event', 'timing_complete', {
          name,
          value: Math.round(duration)
        })
      }
      
      // Log to console in dev
      if (process.env.NODE_ENV === 'development') {
        console.log(`[PERF] ${name}: ${duration.toFixed(2)}ms`)
      }
    }
  }
}

// Usage
const perf = measurePerformance('order_creation')
await createOrder(data)
perf.end()
```

### Health Checks
```typescript
// app/api/health/route.ts
export async function GET() {
  const checks = {
    database: 'unhealthy',
    redis: 'unhealthy',
    storage: 'healthy'
  }
  
  // Check database
  try {
    await prisma.$queryRaw`SELECT 1`
    checks.database = 'healthy'
  } catch {}
  
  // Check Redis
  try {
    await redis.ping()
    checks.redis = 'healthy'
  } catch {}
  
  const isHealthy = Object.values(checks).every(s => s === 'healthy')
  
  return NextResponse.json(
    { 
      status: isHealthy ? 'healthy' : 'degraded',
      checks,
      timestamp: new Date().toISOString()
    },
    { status: isHealthy ? 200 : 503 }
  )
}
```

## 🚀 Deployment Architecture

### Production Infrastructure
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Cloudflare    │────▶│     Vercel      │────▶│    Supabase     │
│      (CDN)      │     │   (Next.js)     │     │   (Database)    │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                        │
         │                       │                        │
    Static Assets          Edge Functions           PostgreSQL
    DNS Management         Server Rendering         File Storage
    DDoS Protection       API Routes              Vector Search
```

### Environment Configuration
```typescript
// config/env.ts
const envSchema = z.object({
  DATABASE_URL: z.string().url(),
  DIRECT_URL: z.string().url(),
  NEXTAUTH_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(32),
  SENTRY_DSN: z.string().url().optional(),
  REDIS_URL: z.string().url().optional()
})

export const env = envSchema.parse(process.env)
```

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
```

---

**Última actualización**: Enero 2025  
**Arquitecto**: Sistema Lohari Team