"use client";

import React, { useState } from "react";
import {
    Button,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    Divider,
} from "@heroui/react";
import {
    CalendarDaysIcon,
    FunnelIcon,
    UserGroupIcon,
    CheckCircleIcon,
    XCircleIcon,
    ChevronDownIcon,
} from "@heroicons/react/24/outline";
import { DateRangePicker } from "@heroui/react";
import { getLocalTimeZone, today } from "@internationalized/date";

import { OrderStatus } from "@/features/orders/types/orders";
import { cn } from "@/shared/utils";

import { useSelection } from "./SelectionContext";

interface SmartSelectionToolbarProps {
    statuses: OrderStatus[];
    customers: { id: string; name: string }[];
    className?: string;
}

const SmartSelectionToolbar: React.FC<SmartSelectionToolbarProps> = ({
    statuses,
    customers,
    className,
}) => {
    const {
        selectAll,
        deselectAll,
        selectByStatus,
        selectByDateRange,
        selectB<PERSON><PERSON>ust<PERSON>,
        selectBySearch,
        isAllSelected,
        isPartiallySelected,
    } = useSelection();

    const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

    return (
        <div
            className={cn(
                "smart-selection-toolbar flex items-center space-x-2 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                className,
            )}
        >
            {/* Botón de seleccionar/deseleccionar todo */}
            <Button
                className="font-medium"
                color={isAllSelected() ? "primary" : "default"}
                size="sm"
                startContent={
                    isAllSelected() ? (
                        <XCircleIcon className="w-4 h-4" />
                    ) : (
                        <CheckCircleIcon className="w-4 h-4" />
                    )
                }
                variant={isAllSelected() ? "solid" : "flat"}
                onClick={isAllSelected() ? deselectAll : selectAll}
            >
                {isAllSelected()
                    ? "Deseleccionar todo"
                    : isPartiallySelected()
                      ? "Completar selección"
                      : "Seleccionar todo"}
            </Button>

            <Divider className="h-6" orientation="vertical" />

            {/* Selección por estado */}
            <Dropdown>
                <DropdownTrigger>
                    <Button
                        endContent={<ChevronDownIcon className="w-4 h-4" />}
                        size="sm"
                        startContent={<FunnelIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Por estado
                    </Button>
                </DropdownTrigger>
                <DropdownMenu aria-label="Seleccionar por estado">
                    {statuses.map((status) => (
                        <DropdownItem
                            key={status.id}
                            startContent={
                                <div
                                    className="w-3 h-3 rounded-full"
                                    style={{
                                        backgroundColor:
                                            status.color || "#cbd5e1",
                                    }}
                                />
                            }
                            onPress={() => selectByStatus(status.id)}
                        >
                            {status.name}
                        </DropdownItem>
                    ))}
                </DropdownMenu>
            </Dropdown>

            {/* Selección por rango de fechas */}
            <Dropdown
                isOpen={isDatePickerOpen}
                onOpenChange={setIsDatePickerOpen}
            >
                <DropdownTrigger>
                    <Button
                        endContent={<ChevronDownIcon className="w-4 h-4" />}
                        size="sm"
                        startContent={<CalendarDaysIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Por fecha
                    </Button>
                </DropdownTrigger>
                <DropdownMenu aria-label="Seleccionar por rango de fechas">
                    <DateRangePicker
                        className="p-2 min-w-[320px]"
                        defaultValue={{
                            start: today(getLocalTimeZone()),
                            end: today(getLocalTimeZone()),
                        }}
                        onChange={(range) => {
                            if (range && range.start && range.end) {
                                const startDate = new Date(
                                    range.start.toDate(getLocalTimeZone()),
                                );
                                const endDate = new Date(
                                    range.end.toDate(getLocalTimeZone()),
                                );

                                selectByDateRange(startDate, endDate);
                                setIsDatePickerOpen(false);
                            }
                        }}
                    />
                </DropdownMenu>
            </Dropdown>

            {/* Selección por cliente */}
            <Dropdown>
                <DropdownTrigger>
                    <Button
                        endContent={<ChevronDownIcon className="w-4 h-4" />}
                        size="sm"
                        startContent={<UserGroupIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Por cliente
                    </Button>
                </DropdownTrigger>
                <DropdownMenu aria-label="Seleccionar por cliente">
                    {customers.map((customer) => (
                        <DropdownItem
                            key={customer.id}
                            onPress={() => selectByCustomer(customer.id)}
                        >
                            {customer.name}
                        </DropdownItem>
                    ))}
                </DropdownMenu>
            </Dropdown>
        </div>
    );
};

export default SmartSelectionToolbar;
