"use server";

import { revalidatePath, revalidateTag } from "next/cache";

/**
 * Revalida todas las rutas y tags relacionados con una entidad específica
 * @param paths Rutas a revalidar
 * @param tags Tags a revalidar
 * @param id ID opcional de la entidad específica
 * @param entityName Nombre de la entidad para logs
 */
export async function revalidateEntityCache(
    paths: {
        list: string;
        new: string;
        api: string;
        detail: (id: string) => string;
    },
    tags: {
        all: string;
        client: string;
        detail: (id: string) => string;
        related: string[];
    },
    entityName: string,
    id?: string,
) {
    const pluralName = entityName + "s";

    // Revalidando caché de entidad - comentario removido

    // 1. Revalidar rutas específicas
    revalidatePath(paths.list);
    revalidatePath(paths.new);
    revalidatePath(paths.api);

    if (id) {
        revalidatePath(paths.detail(id));
    }

    // 2. Revalidar tags para la caché del cliente (SWR)
    revalidateTag(tags.all);
    revalidateTag(tags.client);

    if (id) {
        revalidateTag(tags.detail(id));
    }

    // 3. Revalidar tags relacionados
    tags.related.forEach((tag) => {
        revalidateTag(tag);
    });

    // REMOVED: console.log(`[Server] ✅ Caché de ${pluralName} revalidada correctamente`);
}
