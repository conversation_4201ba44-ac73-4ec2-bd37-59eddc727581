"use client";

import type { OrderForPacking } from "@/features/packings/actions/select-orders-for-packing";

import React, { useState, useEffect } from "react";
import {
    Card,
    CardBody,
    Button,
    Checkbox,
    Chip,
    Divider,
    Input,
    Accordion,
    AccordionItem,
    Badge,
    Spinner,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
} from "@heroui/react";
import {
    Search,
    Package,
    CheckCircle,
    AlertCircle,
    ChevronRight,
} from "lucide-react";

interface OrderSelectionStepProps {
    customerId: string;
    selectedOrders: string[];
    onOrdersChange: (
        orders: string[],
        orderDetails: Map<string, OrderForPacking>,
    ) => void;
    onNext: () => void;
}

export function OrderSelectionStep({
    customerId,
    selectedOrders,
    onOrdersChange,
    onNext,
}: OrderSelectionStepProps) {
    const [isLoading, setIsLoading] = useState(true);
    const [orders, setOrders] = useState<OrderForPacking[]>([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [expandedOrders, setExpandedOrders] = useState<string[]>([]);
    const [selectedProducts, setSelectedProducts] = useState<
        Map<string, Set<string>>
    >(new Map());
    const [debugInfo, setDebugInfo] = useState<any>(null);

    // Cargar órdenes disponibles
    useEffect(() => {
        console.log(
            "🔄 OrderSelectionStep useEffect - customerId:",
            customerId,
        );
        loadOrders();
    }, [customerId]);

    const loadOrders = async () => {
        console.log("📦 loadOrders llamado con customerId:", customerId);
        setIsLoading(true);
        try {
            const { getOrdersForPacking } = await import(
                "@/features/packings/actions/select-orders-for-packing"
            );
            // Si customerId está vacío, no pasar ningún filtro
            const customerFilter = customerId ? customerId : undefined;

            console.log(
                "🔍 Llamando a getOrdersForPacking con:",
                customerFilter || "SIN FILTRO",
            );
            const result = await getOrdersForPacking(customerFilter);

            console.log("📊 Resultado de getOrdersForPacking:", {
                success: result.success,
                dataLength: result.data?.length,
                debug: result.debug,
                error: result.error,
            });

            if (result.success && result.data) {
                setOrders(result.data);
                console.log("✅ Órdenes establecidas:", result.data.length);
                // Auto-expandir órdenes seleccionadas
                setExpandedOrders(selectedOrders);
                // Guardar información de depuración
                setDebugInfo(result.debug);
            } else {
                console.log("❌ No se obtuvieron órdenes o hubo error");
            }
        } catch (error) {
            console.error("Error loading orders:", error);
        } finally {
            setIsLoading(false);
        }
    };

    // Filtrar órdenes por búsqueda
    const filteredOrders = orders.filter(
        (order) =>
            order.displayName
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
            order.cutOrder?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            order.transferNumber
                ?.toLowerCase()
                .includes(searchTerm.toLowerCase()),
    );

    // Manejar selección de orden
    const handleOrderToggle = (orderId: string, isSelected: boolean) => {
        const newSelected = new Set(selectedOrders);

        if (isSelected) {
            newSelected.add(orderId);
            // Expandir automáticamente al seleccionar
            setExpandedOrders((prev) => [...new Set([...prev, orderId])]);
        } else {
            newSelected.delete(orderId);
            // Limpiar productos seleccionados de esta orden
            selectedProducts.delete(orderId);
            setSelectedProducts(new Map(selectedProducts));
        }

        // Crear mapa de detalles de órdenes
        const orderDetailsMap = new Map<string, OrderForPacking>();

        orders.forEach((order) => {
            if (newSelected.has(order.id)) {
                orderDetailsMap.set(order.id, order);
            }
        });

        onOrdersChange(Array.from(newSelected), orderDetailsMap);
    };

    // Manejar selección de productos dentro de una orden
    const handleProductToggle = (
        orderId: string,
        productKey: string,
        isSelected: boolean,
    ) => {
        const orderProducts = selectedProducts.get(orderId) || new Set();

        if (isSelected) {
            orderProducts.add(productKey);
        } else {
            orderProducts.delete(productKey);
        }

        const newSelectedProducts = new Map(selectedProducts);

        if (orderProducts.size > 0) {
            newSelectedProducts.set(orderId, orderProducts);
        } else {
            newSelectedProducts.delete(orderId);
        }

        setSelectedProducts(newSelectedProducts);
    };

    // Obtener estadísticas de selección
    const getSelectionStats = () => {
        const totalOrders = selectedOrders.length;
        let totalProducts = 0;
        let totalQuantity = 0;

        selectedOrders.forEach((orderId) => {
            const order = orders.find((o) => o.id === orderId);

            if (order) {
                const orderProducts = selectedProducts.get(orderId);

                if (!orderProducts || orderProducts.size === 0) {
                    // Si no hay productos específicos seleccionados, contar todos
                    totalProducts += order.products.length;
                    totalQuantity += order.totalAvailableQuantity;
                } else {
                    totalProducts += orderProducts.size;
                    order.products.forEach((product) => {
                        const productKey = `${product.modelCode}-${product.colorName}-${product.partNumber || ""}`;

                        if (orderProducts.has(productKey)) {
                            totalQuantity += product.totalAvailable;
                        }
                    });
                }
            }
        });

        return { totalOrders, totalProducts, totalQuantity };
    };

    const stats = getSelectionStats();

    if (isLoading) {
        return (
            <div className="flex flex-col items-center justify-center py-12">
                <Spinner color="primary" size="lg" />
                <p className="mt-4 text-default-600">
                    Cargando órdenes disponibles...
                </p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Búsqueda y estadísticas */}
            <div className="flex flex-col md:flex-row gap-4">
                <Input
                    isClearable
                    className="flex-1"
                    placeholder="Buscar por número de orden..."
                    startContent={
                        <Search className="w-4 h-4 text-default-400" />
                    }
                    value={searchTerm}
                    onValueChange={setSearchTerm}
                />

                <div className="flex gap-3">
                    <Chip color="primary" variant="flat">
                        {stats.totalOrders} órdenes
                    </Chip>
                    <Chip color="secondary" variant="flat">
                        {stats.totalProducts} productos
                    </Chip>
                    <Chip color="success" variant="flat">
                        {stats.totalQuantity} piezas
                    </Chip>
                </div>
            </div>

            {/* Lista de órdenes */}
            {filteredOrders.length === 0 ? (
                <Card className="bg-default-50">
                    <CardBody className="text-center py-8">
                        <Package className="w-12 h-12 text-default-400 mx-auto mb-4" />
                        <p className="text-default-600">
                            {searchTerm
                                ? "No se encontraron órdenes que coincidan con la búsqueda"
                                : debugInfo?.message ||
                                  "No hay órdenes disponibles para empacar. Esto puede ocurrir si todas las cantidades ya fueron empacadas."}
                        </p>

                        {/* Información adicional de depuración */}
                        {!searchTerm &&
                            debugInfo &&
                            debugInfo.reason === "no_availability" &&
                            debugInfo.details && (
                                <div className="mt-4 text-sm text-default-500 space-y-1">
                                    <p>
                                        Total de órdenes encontradas:{" "}
                                        {debugInfo.details.totalOrders}
                                    </p>
                                    {debugInfo.details.ordersWithNoGarments >
                                        0 && (
                                        <p>
                                            Órdenes sin productos:{" "}
                                            {
                                                debugInfo.details
                                                    .ordersWithNoGarments
                                            }
                                        </p>
                                    )}
                                    {debugInfo.details
                                        .ordersWithNoAvailability > 0 && (
                                        <p>
                                            Órdenes sin cantidad disponible:{" "}
                                            {
                                                debugInfo.details
                                                    .ordersWithNoAvailability
                                            }
                                        </p>
                                    )}
                                    <Divider className="my-2" />
                                    <p className="text-warning-600 font-medium">
                                        Esto puede ocurrir si todas las
                                        cantidades ya fueron asignadas o
                                        empacadas.
                                    </p>
                                </div>
                            )}

                        {/* Botones de acción */}
                        <div className="flex gap-2 justify-center mt-4">
                            <Button
                                color="primary"
                                size="sm"
                                variant="flat"
                                onPress={() => loadOrders()}
                            >
                                Recargar órdenes
                            </Button>
                            <Button
                                color="secondary"
                                size="sm"
                                variant="flat"
                                onPress={async () => {
                                    console.log(
                                        "🌍 Cargando TODAS las órdenes (sin filtro de cliente)",
                                    );
                                    setIsLoading(true);
                                    try {
                                        const { getOrdersForPacking } =
                                            await import(
                                                "@/features/packings/actions/select-orders-for-packing"
                                            );
                                        const result =
                                            await getOrdersForPacking(); // Sin customerId

                                        console.log(
                                            "📊 Resultado sin filtro:",
                                            {
                                                success: result.success,
                                                dataLength: result.data?.length,
                                            },
                                        );
                                        if (result.success && result.data) {
                                            setOrders(result.data);
                                            setDebugInfo(null);
                                        }
                                    } catch (error) {
                                        console.error("Error:", error);
                                    } finally {
                                        setIsLoading(false);
                                    }
                                }}
                            >
                                Ver todas las órdenes
                            </Button>
                        </div>
                    </CardBody>
                </Card>
            ) : (
                <Accordion
                    selectedKeys={expandedOrders}
                    selectionMode="multiple"
                    onSelectionChange={(keys) =>
                        setExpandedOrders(Array.from(keys) as string[])
                    }
                >
                    {filteredOrders.map((order) => {
                        const isOrderSelected = selectedOrders.includes(
                            order.id,
                        );
                        const orderProducts =
                            selectedProducts.get(order.id) || new Set();
                        const hasSelectedProducts = orderProducts.size > 0;

                        return (
                            <AccordionItem
                                key={order.id}
                                aria-label={order.displayName}
                                className={`${isOrderSelected ? "border-2 border-primary" : ""} ${order.totalAvailableQuantity === 0 ? "opacity-80" : ""}`}
                                startContent={
                                    <Checkbox
                                        aria-label="Seleccionar orden"
                                        isSelected={isOrderSelected}
                                        onValueChange={(checked) =>
                                            handleOrderToggle(order.id, checked)
                                        }
                                    />
                                }
                                title={
                                    <div className="flex items-center justify-between flex-1 pr-4">
                                        <div className="flex flex-col">
                                            <span className="font-semibold">
                                                {order.displayName}
                                            </span>
                                            <div className="flex gap-2 mt-1">
                                                {order.cutOrder && (
                                                    <Chip
                                                        size="sm"
                                                        variant="flat"
                                                    >
                                                        OC: {order.cutOrder}
                                                    </Chip>
                                                )}
                                                {order.transferNumber && (
                                                    <Chip
                                                        size="sm"
                                                        variant="flat"
                                                    >
                                                        Trans:{" "}
                                                        {order.transferNumber}
                                                    </Chip>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <Badge
                                                color="primary"
                                                content={order.totalProducts}
                                                size="sm"
                                            >
                                                <Package className="w-5 h-5 text-default-500" />
                                            </Badge>
                                            <Chip
                                                color={
                                                    order.totalAvailableQuantity >
                                                    0
                                                        ? "success"
                                                        : "danger"
                                                }
                                                size="sm"
                                                variant="flat"
                                            >
                                                {order.totalAvailableQuantity >
                                                0
                                                    ? `${order.totalAvailableQuantity} pzs disponibles`
                                                    : "Sin disponibilidad"}
                                            </Chip>
                                        </div>
                                    </div>
                                }
                            >
                                <div className="space-y-4 pl-8">
                                    {/* Advertencia si la orden no tiene disponibilidad */}
                                    {order.totalAvailableQuantity === 0 &&
                                        isOrderSelected && (
                                            <div className="flex items-center gap-2 p-3 bg-warning-50 text-warning-700 rounded-lg">
                                                <AlertCircle className="w-5 h-5 flex-shrink-0" />
                                                <p className="text-sm">
                                                    Esta orden no tiene cantidad
                                                    disponible. Las cantidades
                                                    ya fueron asignadas o
                                                    empacadas.
                                                </p>
                                            </div>
                                        )}

                                    {/* Opción para seleccionar todos los productos */}
                                    <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                                        <label className="flex items-center gap-2 cursor-pointer">
                                            <Checkbox
                                                isDisabled={!isOrderSelected}
                                                isSelected={
                                                    !hasSelectedProducts &&
                                                    isOrderSelected
                                                }
                                                onValueChange={(checked) => {
                                                    if (checked) {
                                                        // Limpiar selección individual para usar todos
                                                        selectedProducts.delete(
                                                            order.id,
                                                        );
                                                        setSelectedProducts(
                                                            new Map(
                                                                selectedProducts,
                                                            ),
                                                        );
                                                    }
                                                }}
                                            />
                                            <span className="font-medium">
                                                Incluir todos los productos
                                            </span>
                                        </label>
                                        <Chip color="default" size="sm">
                                            {order.totalProducts} productos
                                        </Chip>
                                    </div>

                                    <Divider />

                                    {/* Lista de productos */}
                                    <div className="space-y-3">
                                        <p className="text-sm font-medium text-default-600">
                                            O selecciona productos específicos:
                                        </p>

                                        <div className="overflow-x-auto">
                                            <Table
                                                removeWrapper
                                                aria-label="Productos de la orden"
                                                classNames={{
                                                    base: "max-h-[400px] overflow-y-auto",
                                                    table: "min-w-[600px]",
                                                }}
                                            >
                                                <TableHeader>
                                                    <TableColumn>
                                                        SELECCIONAR
                                                    </TableColumn>
                                                    <TableColumn>
                                                        MODELO
                                                    </TableColumn>
                                                    <TableColumn>
                                                        COLOR
                                                    </TableColumn>
                                                    <TableColumn>
                                                        PARTIDA
                                                    </TableColumn>
                                                    <TableColumn>
                                                        TALLAS
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        DISPONIBLE
                                                    </TableColumn>
                                                </TableHeader>
                                                <TableBody>
                                                    {order.products.map(
                                                        (product) => {
                                                            const productKey = `${product.modelCode}-${product.colorName}-${product.partNumber || ""}`;
                                                            const isProductSelected =
                                                                hasSelectedProducts
                                                                    ? orderProducts.has(
                                                                          productKey,
                                                                      )
                                                                    : isOrderSelected;

                                                            return (
                                                                <TableRow
                                                                    key={
                                                                        productKey
                                                                    }
                                                                >
                                                                    <TableCell>
                                                                        <Checkbox
                                                                            isDisabled={
                                                                                !isOrderSelected
                                                                            }
                                                                            isSelected={
                                                                                isProductSelected
                                                                            }
                                                                            onValueChange={(
                                                                                checked,
                                                                            ) =>
                                                                                handleProductToggle(
                                                                                    order.id,
                                                                                    productKey,
                                                                                    checked,
                                                                                )
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <div>
                                                                            <p className="font-medium">
                                                                                {
                                                                                    product.modelCode
                                                                                }
                                                                            </p>
                                                                            {product.modelDescription && (
                                                                                <p className="text-xs text-default-500">
                                                                                    {
                                                                                        product.modelDescription
                                                                                    }
                                                                                </p>
                                                                            )}
                                                                        </div>
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        {
                                                                            product.colorName
                                                                        }
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        {product.partNumber ||
                                                                            "-"}
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <div className="flex flex-wrap gap-1">
                                                                            {product.sizes.map(
                                                                                (
                                                                                    size,
                                                                                ) => (
                                                                                    <Chip
                                                                                        key={
                                                                                            size.garmentSizeId
                                                                                        }
                                                                                        color={
                                                                                            size.availableQuantity >
                                                                                            0
                                                                                                ? "success"
                                                                                                : "danger"
                                                                                        }
                                                                                        size="sm"
                                                                                        title={`Total: ${size.totalQuantity}, Ya empacado: ${size.usedQuantity}, Disponible: ${size.availableQuantity}`}
                                                                                        variant="flat"
                                                                                    >
                                                                                        {
                                                                                            size.sizeCode
                                                                                        }

                                                                                        :{" "}
                                                                                        {size.availableQuantity >
                                                                                        0
                                                                                            ? size.availableQuantity
                                                                                            : `0/${size.totalQuantity}`}
                                                                                    </Chip>
                                                                                ),
                                                                            )}
                                                                        </div>
                                                                    </TableCell>
                                                                    <TableCell className="text-center">
                                                                        <Chip
                                                                            color={
                                                                                product.totalAvailable >
                                                                                0
                                                                                    ? "success"
                                                                                    : "danger"
                                                                            }
                                                                            variant="flat"
                                                                        >
                                                                            {product.totalAvailable >
                                                                            0
                                                                                ? product.totalAvailable
                                                                                : "Agotado"}
                                                                        </Chip>
                                                                    </TableCell>
                                                                </TableRow>
                                                            );
                                                        },
                                                    )}
                                                </TableBody>
                                            </Table>
                                        </div>
                                    </div>
                                </div>
                            </AccordionItem>
                        );
                    })}
                </Accordion>
            )}

            {/* Resumen y botón siguiente */}
            <Card className="bg-primary-50 dark:bg-primary-900/20">
                <CardBody>
                    <div className="flex items-center justify-between">
                        <div>
                            <h4 className="font-semibold text-lg mb-1">
                                Resumen de Selección
                            </h4>
                            <div className="flex items-center gap-4 text-sm">
                                <span className="flex items-center gap-1">
                                    <CheckCircle className="w-4 h-4 text-success" />
                                    {stats.totalOrders} órdenes
                                </span>
                                <span className="flex items-center gap-1">
                                    <Package className="w-4 h-4 text-primary" />
                                    {stats.totalProducts} productos
                                </span>
                                <span className="flex items-center gap-1">
                                    {stats.totalQuantity} piezas totales
                                </span>
                            </div>
                        </div>

                        <Button
                            color="primary"
                            endContent={<ChevronRight className="w-4 h-4" />}
                            isDisabled={selectedOrders.length === 0}
                            size="lg"
                            onPress={onNext}
                        >
                            Continuar
                        </Button>
                    </div>

                    {selectedOrders.length === 0 && (
                        <div className="mt-3 p-3 bg-warning-100 dark:bg-warning-900/20 rounded-lg">
                            <p className="text-sm text-warning-800 dark:text-warning-200 flex items-center gap-2">
                                <AlertCircle className="w-4 h-4" />
                                Selecciona al menos una orden para continuar
                            </p>
                        </div>
                    )}
                </CardBody>
            </Card>
        </div>
    );
}
