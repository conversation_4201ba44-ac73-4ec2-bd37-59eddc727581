/**
 * API Performance Tracking
 * Monitors API call performance and database query times
 */

export interface APIMetrics {
    endpoint: string;
    method: string;
    duration: number;
    status: number;
    timestamp: number;
    size?: number;
}

export interface QueryMetrics {
    query: string;
    duration: number;
    timestamp: number;
    rowCount?: number;
}

class PerformanceTracker {
    private apiCalls: APIMetrics[] = [];
    private dbQueries: QueryMetrics[] = [];
    private maxStoredMetrics = 100;

    /**
     * Track API call performance
     */
    trackAPI(metrics: APIMetrics) {
        this.apiCalls.push(metrics);

        // Keep only recent metrics
        if (this.apiCalls.length > this.maxStoredMetrics) {
            this.apiCalls.shift();
        }

        // Log slow API calls
        if (metrics.duration > 200) {
            console.warn(
                `⚠️ Slow API call: ${metrics.method} ${metrics.endpoint} (${metrics.duration}ms)`,
            );
        }
    }

    /**
     * Track database query performance
     */
    trackQuery(metrics: QueryMetrics) {
        this.dbQueries.push(metrics);

        // Keep only recent metrics
        if (this.dbQueries.length > this.maxStoredMetrics) {
            this.dbQueries.shift();
        }

        // Log slow queries
        if (metrics.duration > 50) {
            console.warn(
                `⚠️ Slow DB query (${metrics.duration}ms):`,
                metrics.query.substring(0, 100),
            );
        }
    }

    /**
     * Get API performance stats
     */
    getAPIStats() {
        if (this.apiCalls.length === 0) return null;

        const durations = this.apiCalls.map((c) => c.duration);
        const sorted = [...durations].sort((a, b) => a - b);

        return {
            count: this.apiCalls.length,
            average: Math.round(
                durations.reduce((a, b) => a + b, 0) / durations.length,
            ),
            p50: sorted[Math.floor(sorted.length * 0.5)],
            p95: sorted[Math.floor(sorted.length * 0.95)],
            p99: sorted[Math.floor(sorted.length * 0.99)],
            min: Math.min(...durations),
            max: Math.max(...durations),
        };
    }

    /**
     * Get query performance stats
     */
    getQueryStats() {
        if (this.dbQueries.length === 0) return null;

        const durations = this.dbQueries.map((q) => q.duration);
        const sorted = [...durations].sort((a, b) => a - b);

        return {
            count: this.dbQueries.length,
            average: Math.round(
                durations.reduce((a, b) => a + b, 0) / durations.length,
            ),
            p50: sorted[Math.floor(sorted.length * 0.5)],
            p95: sorted[Math.floor(sorted.length * 0.95)],
            p99: sorted[Math.floor(sorted.length * 0.99)],
            min: Math.min(...durations),
            max: Math.max(...durations),
        };
    }

    /**
     * Generate performance report
     */
    generateReport() {
        return {
            timestamp: Date.now(),
            api: this.getAPIStats(),
            database: this.getQueryStats(),
            slowAPICalls: this.apiCalls.filter((c) => c.duration > 200),
            slowQueries: this.dbQueries.filter((q) => q.duration > 50),
        };
    }

    /**
     * Clear all metrics
     */
    clear() {
        this.apiCalls = [];
        this.dbQueries = [];
    }
}

// Singleton instance
export const performanceTracker = new PerformanceTracker();
