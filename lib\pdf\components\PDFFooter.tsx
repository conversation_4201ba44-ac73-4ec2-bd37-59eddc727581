import React from "react";
import { View, Text } from "@react-pdf/renderer";

import { remissionStyles as styles } from "../styles/remission";

interface PDFFooterProps {
    deliveredBy?: string;
    receivedBy?: string;
    authorizedBy?: string;
}

export const PDFFooter: React.FC<PDFFooterProps> = ({
    deliveredBy,
    receivedBy,
    authorizedBy,
}) => {
    return (
        <View style={styles.footer}>
            {/* Signature Section */}
            <View style={styles.signatureSection}>
                <View style={styles.signatureBox}>
                    <View style={styles.signatureLine} />
                    <Text style={styles.signatureLabel}>ENTREGA</Text>
                    {deliveredBy && (
                        <Text
                            style={[
                                styles.signatureLabel,
                                styles.bold,
                                styles.mt10,
                            ]}
                        >
                            {deliveredBy}
                        </Text>
                    )}
                </View>

                <View style={styles.signatureBox}>
                    <View style={styles.signatureLine} />
                    <Text style={styles.signatureLabel}>RECIBE</Text>
                    {receivedBy && (
                        <Text
                            style={[
                                styles.signatureLabel,
                                styles.bold,
                                styles.mt10,
                            ]}
                        >
                            {receivedBy}
                        </Text>
                    )}
                </View>

                <View style={styles.signatureBox}>
                    <View style={styles.signatureLine} />
                    <Text style={styles.signatureLabel}>AUTORIZA</Text>
                    {authorizedBy && (
                        <Text
                            style={[
                                styles.signatureLabel,
                                styles.bold,
                                styles.mt10,
                            ]}
                        >
                            {authorizedBy}
                        </Text>
                    )}
                </View>
            </View>

            {/* Footer Info */}
            <View style={styles.footerInfo}>
                <Text style={styles.footerText}>
                    LOHARI - Sistema de Gestión de Inventario
                </Text>
                <Text style={styles.footerText}>
                    Este documento es un comprobante oficial de entrega de
                    mercancía
                </Text>
            </View>
        </View>
    );
};
