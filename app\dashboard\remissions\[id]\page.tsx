import { Suspense } from "react";
import { notFound } from "next/navigation";
import { Metadata } from "next";

import { DashboardShell } from "@/shared/components/layout/Shell";
import { DashboardHeader } from "@/shared/components/layout/Header";
import { getRemission } from "@/features/remissions/actions";

import RemissionDetailClient from "./RemissionDetailClient";
import RemissionDetailSkeleton from "./RemissionDetailSkeleton";

// Define the page metadata
export async function generateMetadata({
    params,
}: {
    params: Promise<{ id: string }>;
}): Promise<Metadata> {
    const { id } = await params;
    const remission = await getRemission(id);

    if (!remission) {
        return {
            title: "Remisión no encontrada | Lohari",
        };
    }

    return {
        title: `Remisión ${remission.folio} | Lohari`,
        description: `Detalles de la remisión ${remission.folio}`,
    };
}

// Define the page props
interface RemissionDetailPageProps {
    params: Promise<{
        id: string;
    }>;
}

async function RemissionDetailContent({ id }: { id: string }) {
    // Get the remission data
    const remission = await getRemission(id);

    // If the remission doesn't exist, return a 404
    if (!remission) {
        notFound();
    }

    return <RemissionDetailClient remission={remission} />;
}

export default async function RemissionDetailPage({
    params,
}: RemissionDetailPageProps) {
    const { id } = await params;

    return (
        <DashboardShell>
            <DashboardHeader
                heading="Detalle de Remisión"
                text="Visualiza y gestiona los detalles de la remisión"
            />

            <Suspense fallback={<RemissionDetailSkeleton />}>
                <RemissionDetailContent id={id} />
            </Suspense>
        </DashboardShell>
    );
}
