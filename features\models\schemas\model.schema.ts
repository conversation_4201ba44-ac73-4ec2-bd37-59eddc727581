import { z } from "zod";

// Esquema para crear un modelo
export const createModelSchema = z.object({
    code: z
        .string()
        .min(2, "El código debe tener al menos 2 caracteres")
        .max(10, "El código no puede exceder los 10 caracteres")
        .regex(/^[A-Za-z0-9-]+$/, "Solo se permiten letras, números y guiones")
        .transform((val) => val.toUpperCase().trim()),

    description: z
        .string()
        .min(5, "La descripción debe tener al menos 5 caracteres")
        .max(100, "La descripción no puede exceder los 100 caracteres")
        .transform((val) => val.trim()),

    basePrice: z
        .number()
        .min(0, "El precio base no puede ser negativo")
        .optional()
        .default(0),
});

// Esquema para actualizar un modelo
export const updateModelSchema = z.object({
    code: z
        .string()
        .min(2, "El código debe tener al menos 2 caracteres")
        .max(10, "El código no puede exceder los 10 caracteres")
        .regex(/^[A-Za-z0-9-]+$/, "Solo se permiten letras, números y guiones")
        .transform((val) => val.toUpperCase().trim())
        .optional(),

    description: z
        .string()
        .min(5, "La descripción debe tener al menos 5 caracteres")
        .max(100, "La descripción no puede exceder los 100 caracteres")
        .transform((val) => val.trim())
        .optional(),

    basePrice: z
        .number()
        .min(0, "El precio base no puede ser negativo")
        .optional(),
});

// Tipos inferidos
export type CreateModelInput = z.infer<typeof createModelSchema>;
export type UpdateModelInput = z.infer<typeof updateModelSchema>;
