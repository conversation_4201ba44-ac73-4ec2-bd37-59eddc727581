"use client";

import { motion } from "framer-motion";
import { Spinner } from "@heroui/react";
import {
    ExclamationTriangleIcon,
    InboxIcon,
} from "@heroicons/react/24/outline";

interface EmptyStateProps {
    message: string;
    subMessage?: string;
    icon?: React.ReactNode;
}

export const EmptyState = ({
    message,
    subMessage,
    icon = <InboxIcon className="w-12 h-12 text-gray-300 dark:text-gray-600" />,
}: EmptyStateProps) => {
    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="flex flex-col items-center justify-center p-10 bg-white dark:bg-gray-800/30 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm text-center"
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3 }}
        >
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-4 mb-4">
                {icon}
            </div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
                {message}
            </h3>
            {subMessage && (
                <p className="text-sm text-gray-500 dark:text-gray-400 max-w-md">
                    {subMessage}
                </p>
            )}
        </motion.div>
    );
};

interface LoadingStateProps {
    message?: string;
}

export const LoadingState = ({
    message = "Cargando...",
}: LoadingStateProps) => {
    return (
        <motion.div
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center p-10"
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <Spinner className="mb-4" color="primary" size="lg" />
            <p className="text-gray-600 dark:text-gray-300 text-center">
                {message}
            </p>
        </motion.div>
    );
};

interface ErrorStateProps {
    message: string;
    subMessage?: string;
}

export const ErrorState = ({ message, subMessage }: ErrorStateProps) => {
    return (
        <EmptyState
            icon={
                <ExclamationTriangleIcon className="w-12 h-12 text-amber-500" />
            }
            message={message}
            subMessage={subMessage}
        />
    );
};
