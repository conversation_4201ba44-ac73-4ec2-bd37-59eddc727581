/**
 * Performance Optimization Feature Flags
 * Controls gradual rollout of optimization features
 */

export interface FeatureFlags {
    // React Query migration
    ENABLE_REACT_QUERY: boolean;
    ENABLE_REACT_QUERY_ORDERS: boolean;
    ENABLE_REACT_QUERY_CUSTOMERS: boolean;
    ENABLE_REACT_QUERY_ASSIGNMENTS: boolean;
    ENABLE_REACT_QUERY_AUTH: boolean;

    // Performance features
    ENABLE_VIRTUAL_SCROLL: boolean;
    ENABLE_CODE_SPLITTING: boolean;
    ENABLE_LAZY_LOADING: boolean;
    ENABLE_PREFETCH: boolean;

    // Caching features (future phases)
    ENABLE_REDIS: boolean;
    ENABLE_CDN_CACHE: boolean;
}

/**
 * Get feature flags from environment or defaults
 */
export function getFeatureFlags(): FeatureFlags {
    // In production, these would come from environment variables
    // or a feature flag service like LaunchDarkly
    return {
        // React Query migration - gradual rollout
        ENABLE_REACT_QUERY:
            process.env.NEXT_PUBLIC_ENABLE_REACT_QUERY === "true",
        ENABLE_REACT_QUERY_ORDERS:
            process.env.NEXT_PUBLIC_ENABLE_REACT_QUERY_ORDERS === "true",
        ENABLE_REACT_QUERY_CUSTOMERS:
            process.env.NEXT_PUBLIC_ENABLE_REACT_QUERY_CUSTOMERS === "true",
        ENABLE_REACT_QUERY_ASSIGNMENTS:
            process.env.NEXT_PUBLIC_ENABLE_REACT_QUERY_ASSIGNMENTS === "true",
        ENABLE_REACT_QUERY_AUTH:
            process.env.NEXT_PUBLIC_ENABLE_REACT_QUERY_AUTH === "true",

        // Performance features
        ENABLE_VIRTUAL_SCROLL:
            process.env.NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLL === "true",
        ENABLE_CODE_SPLITTING:
            process.env.NEXT_PUBLIC_ENABLE_CODE_SPLITTING === "true",
        ENABLE_LAZY_LOADING:
            process.env.NEXT_PUBLIC_ENABLE_LAZY_LOADING === "true",
        ENABLE_PREFETCH: process.env.NEXT_PUBLIC_ENABLE_PREFETCH === "true",

        // Future phases
        ENABLE_REDIS: false,
        ENABLE_CDN_CACHE: false,
    };
}

// Singleton instance
export const featureFlags = getFeatureFlags();
