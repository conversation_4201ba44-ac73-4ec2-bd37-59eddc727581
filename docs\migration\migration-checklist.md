# ✅ Checklist de Migración Arquitectónica - Lohari

## 📅 Semana 1: Preparación y Limpieza

### Día 1-2: Limpieza de Archivos
- [ ] **Ejecutar script de limpieza de conflictos**
  ```bash
  node scripts/clean-conflicts.js
  ./scripts/cleanup-conflicts.sh
  ```
- [ ] **Eliminar archivos de prueba/temporales**
  - [ ] `components/counter.tsx`
  - [ ] `components/TodoList.tsx`
  - [ ] `lib/fix-verification.ts`
- [ ] **Crear backup del proyecto**
  ```bash
  git checkout -b architecture-migration
  git add .
  git commit -m "chore: backup before architecture migration"
  ```

### Día 3: Configurar Nueva Estructura
- [ ] **Crear estructura base de directorios**
  ```bash
  mkdir -p src/{core/{domain,application,ports},infrastructure,presentation,shared}
  mkdir -p src/core/domain/{order,customer,contractor,assignment,model,garment}
  mkdir -p src/core/ports/{repositories,services}
  mkdir -p src/infrastructure/{persistence/prisma,services,auth}
  mkdir -p src/presentation/{components,features,app}
  mkdir -p src/shared/{types,utils,constants,errors}
  ```

- [ ] **Configurar paths en tsconfig.json**
  ```json
  {
    "compilerOptions": {
      "paths": {
        "@/core/*": ["./src/core/*"],
        "@/infrastructure/*": ["./src/infrastructure/*"],
        "@/presentation/*": ["./src/presentation/*"],
        "@/shared/*": ["./src/shared/*"]
      }
    }
  }
  ```

### Día 4-5: Consolidar Configuración
- [ ] **Unificar configuración de Prisma**
  - [ ] Mantener solo `lib/prisma.ts`
  - [ ] Eliminar archivos duplicados
  - [ ] Mover a `src/infrastructure/persistence/prisma/client.ts`
- [ ] **Crear utilidades compartidas**
  - [ ] Mover `Result` pattern a `src/shared/utils/result.ts`
  - [ ] Crear `src/shared/errors/domain-errors.ts`
  - [ ] Consolidar tipos en `src/shared/types`

## 📅 Semana 2: Implementar Core Domain

### Día 6-7: Crear Entidades de Dominio
- [ ] **Order Domain**
  - [ ] `src/core/domain/order/order.entity.ts`
  - [ ] `src/core/domain/order/order.value-objects.ts`
  - [ ] `src/core/domain/order/order.types.ts`
  - [ ] Tests unitarios

- [ ] **Customer Domain**
  - [ ] `src/core/domain/customer/customer.entity.ts`
  - [ ] Reglas de negocio
  - [ ] Tests

- [ ] **Assignment Domain**
  - [ ] Entidad y reglas de asignación
  - [ ] Validaciones de cantidad
  - [ ] Tests

### Día 8-9: Definir Puertos
- [ ] **Crear interfaces de repositorios**
  ```typescript
  // src/core/ports/repositories/index.ts
  export * from './order.repository';
  export * from './customer.repository';
  export * from './assignment.repository';
  ```

- [ ] **Crear interfaces de servicios**
  ```typescript
  // src/core/ports/services/index.ts
  export * from './email.service';
  export * from './inventory.service';
  export * from './pdf.service';
  ```

### Día 10: Value Objects y Tipos
- [ ] **Implementar Value Objects comunes**
  - [ ] Email
  - [ ] Money
  - [ ] DateRange
  - [ ] Quantity
## 📅 Semana 3: Application Layer

### Día 11-12: Casos de Uso de Orders
- [ ] **Crear casos de uso**
  - [ ] `CreateOrderUseCase`
  - [ ] `UpdateOrderUseCase`
  - [ ] `AssignOrderUseCase`
  - [ ] `RejectOrderUseCase`
  - [ ] `GetOrderDetailsUseCase`

- [ ] **Implementar DTOs y Mappers**
  - [ ] `OrderMapper`
  - [ ] Request/Response DTOs
  - [ ] Validación con Zod

### Día 13-14: Otros Casos de Uso
- [ ] **Assignments**
  - [ ] `CreateAssignmentUseCase`
  - [ ] `UpdateProgressUseCase`
  - [ ] `CompleteAssignmentUseCase`

- [ ] **Remissions**
  - [ ] `CreateRemissionUseCase`
  - [ ] `PrintRemissionUseCase`

### Día 15: Testing
- [ ] **Configurar testing**
  ```bash
  npm install -D vitest @vitest/ui @testing-library/react
  ```
- [ ] **Crear tests para casos de uso**
- [ ] **Mocks de repositorios**

## 📅 Semana 4: Infrastructure & Presentation

### Día 16-17: Implementar Repositorios
- [ ] **Prisma Repositories**
  - [ ] `PrismaOrderRepository`
  - [ ] `PrismaCustomerRepository`
  - [ ] `PrismaAssignmentRepository`
  - [ ] Implementar transacciones

### Día 18: Servicios
- [ ] **Email Service (Nodemailer)**
- [ ] **PDF Service**
- [ ] **AI Services (OpenAI/Anthropic)**

### Día 19-20: Refactorizar Server Actions
- [ ] **Simplificar actions**
  - [ ] Solo validación y orquestación
  - [ ] Delegar a casos de uso
  - [ ] Manejo consistente de errores
### Día 21: Reorganizar Componentes
- [ ] **Mover componentes por feature**
  - [ ] De `components/orders/*` a `src/presentation/features/orders/components/*`
  - [ ] Eliminar duplicaciones
  - [ ] Crear índices de exportación

## 📊 Métricas de Éxito

### Antes de la Migración
- [ ] Documentar métricas actuales:
  - Número de archivos duplicados
  - Líneas de código por módulo
  - Cobertura de tests
  - Tiempo de build

### Después de la Migración
- [ ] **Código más limpio**
  - ✅ Sin archivos `.sync-conflict`
  - ✅ Sin duplicación de componentes
  - ✅ Estructura clara y predecible

- [ ] **Mejor testabilidad**
  - ✅ Lógica de negocio sin dependencias
  - ✅ 80%+ cobertura en casos de uso
  - ✅ Tests unitarios rápidos

- [ ] **Mantenibilidad mejorada**
  - ✅ Fácil encontrar cualquier pieza de código
  - ✅ Cambios aislados por capa
  - ✅ Nuevas features sin afectar existentes

## 🚀 Scripts Útiles

```json
// package.json scripts adicionales
{
  "scripts": {
    "clean:conflicts": "node scripts/clean-conflicts.js",
    "test:unit": "vitest run --dir src/core",
    "test:integration": "vitest run --dir src/infrastructure",
    "lint:architecture": "eslint src --ext .ts,.tsx",
    "analyze:deps": "madge --circular src"
  }
}
```

## 📝 Notas Importantes

1. **Hacer commits frecuentes** - Un commit por cada paso completado
2. **No migrar todo de una vez** - Módulo por módulo
3. **Mantener la app funcionando** - Tests E2E deben pasar siempre
4. **Documentar decisiones** - ADRs para decisiones arquitectónicas
5. **Pair programming** - Para decisiones complejas

---

💡 **Tip**: Comienza con el módulo más simple (ej: Colors o Sizes) para ganar confianza antes de abordar Orders o Assignments.