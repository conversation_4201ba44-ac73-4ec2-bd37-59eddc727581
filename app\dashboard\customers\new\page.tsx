"use client";

import React, { useState, useEffect } from "react";
import {
    Input,
    Textarea,
    Button,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
    Tooltip,
} from "@heroui/react";
import { useRouter } from "next/navigation";
import {
    ChevronLeftIcon,
    UserGroupIcon,
    InformationCircleIcon,
    ArrowPathIcon,
    ExclamationTriangleIcon,
    CheckIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import useSWR from "swr";
import { motion } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    Card,
    CardBody,
    CardHeader,
} from "@/shared/components/ui/hero-ui-client";
import {
    useCreateCustomer,
    useValidateCustomerName,
} from "@/features/customers/hooks/useCustomer";
import {
    CustomerFormProgress,
    calculate<PERSON>reateProgress,
    Customer<PERSON>orm<PERSON>eader,
    CustomerInfoMessage,
} from "@/features/customers/components/forms";

// Esquema de validación
const customerSchema = z.object({
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres")
        .transform((val) => val.trim()),
    displayName: z
        .string()
        .max(100, "El nombre para mostrar no puede exceder los 100 caracteres")
        .transform((val) => val?.trim() || null)
        .optional()
        .nullable(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

// Animaciones
const containerVariants = {
    initial: { opacity: 0, y: 20 },
    animate: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.3,
            staggerChildren: 0.1,
        },
    },
};

const itemVariants = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
};

export default function NewCustomerPage() {
    const router = useRouter();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [formProgress, setFormProgress] = useState(0);
    const { createCustomer } = useCreateCustomer();
    const { mutate: mutateGlobal } = useSWR(null);
    const { mutate: mutateCustomers } = useSWR(["customers", "{}"]);

    const {
        register,
        handleSubmit,
        watch,
        formState: { errors, isDirty, isValid: isFormValid },
        reset,
    } = useForm<CustomerFormData>({
        resolver: zodResolver(customerSchema),
        mode: "onChange",
        defaultValues: {
            name: "",
            displayName: "",
        },
    });

    // Obtener el valor actual del nombre para validación
    const currentName = watch("name");

    // Usar el hook de validación con revalidación forzada para evitar datos en caché desactualizados
    const { isValid, isValidating } = useValidateCustomerName(
        currentName && currentName.length >= 2 ? currentName : null,
        undefined,
        {
            revalidateOnFocus: true,
            dedupingInterval: 0, // Deshabilitar deduplicación para forzar revalidación
            revalidateIfStale: true,
            refreshInterval: 0, // No hacer polling automático
            revalidateOnMount: true, // Revalidar siempre al montar el componente
        },
    );

    // Botón para ignorar validación en caso de falso positivo
    const [bypassValidation, setBypassValidation] = useState(false);

    // Calcular progreso del formulario
    useEffect(() => {
        const progress = calculateCreateProgress(
            currentName,
            isValid,
            isValidating,
        );

        setFormProgress(progress);
    }, [currentName, isValid, isValidating]);

    const onSubmit = async (data: CustomerFormData) => {
        // Validación previa
        if (isValidating) {
            addToast({
                title: "Validando",
                description:
                    "Por favor, espere mientras se valida el nombre del cliente",
                color: "primary",
            });

            return;
        }

        if (isValid === false && !bypassValidation) {
            addToast({
                title: "Error de validación",
                description:
                    "El nombre del cliente ya existe en el sistema o fue eliminado recientemente. Si está seguro de que no existe, puede ignorar esta validación y continuar.",
                color: "warning",
            });

            setBypassValidation(true);

            return;
        }

        setIsSubmitting(true);

        try {
            // Pasar el parámetro forceCreate si se está ignorando la validación
            const result = await createCustomer(
                {
                    name: data.name,
                    displayName: data.displayName || null,
                },
                bypassValidation,
            );

            // Verificar que result existe antes de intentar acceder a sus propiedades
            if (!result) {
                throw new Error(
                    "No se pudo completar la operación de creación",
                );
            }

            if (!result.success) {
                throw new Error(result.error || "Error al crear el cliente");
            }

            // Actualizar datos globalmente para reflejar cambios inmediatamente
            await mutateGlobal(
                (key: string | any[]) =>
                    Array.isArray(key) && key[0] === "customers",
            );
            await mutateCustomers();

            addToast({
                title: "¡Cliente creado exitosamente!",
                description: "El nuevo cliente ha sido añadido a la lista.",
                color: "success",
            });

            // Resetear el formulario
            reset();
            setBypassValidation(false);

            // Esperar un momento para asegurar que la revalidación ocurra antes de redirigir
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Redirigir a la lista de clientes
            router.push("/dashboard/customers");
            router.refresh();
        } catch (err) {
            addToast({
                title: "Error",
                description:
                    err instanceof Error
                        ? err.message
                        : "Error desconocido al crear el cliente",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (isDirty) {
            onOpen();
        } else {
            router.push("/dashboard/customers");
        }
    };

    // Función helper para obtener el color del input
    const getInputColor = () => {
        if (errors.name) return "danger";
        if (currentName?.length >= 2 && isValid === true) return "success";

        return "default";
    };

    return (
        <DashboardLayout
            actions={
                <div className="flex items-center gap-4">
                    <CustomerFormProgress
                        className="hidden md:block w-48"
                        value={formProgress}
                    />
                    <Button
                        startContent={<ChevronLeftIcon className="w-4 h-4" />}
                        variant="light"
                        onPress={() => router.push("/dashboard/customers")}
                    >
                        Volver
                    </Button>
                </div>
            }
            breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Clientes", href: "/dashboard/customers" },
                { label: "Nuevo Cliente" },
            ]}
            subtitle="Registra un nuevo cliente en el sistema"
            title="Nuevo Cliente"
        >
            <motion.div
                animate="animate"
                className="max-w-2xl mx-auto"
                initial="initial"
                variants={containerVariants}
            >
                {/* Progress bar móvil */}
                <div className="mb-6 md:hidden">
                    <CustomerFormProgress value={formProgress} />
                </div>

                <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:translate-y-[-2px]">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 p-6">
                        <CustomerFormHeader
                            subtitle="Complete los datos para registrar un nuevo cliente en el sistema"
                            title="Información del Cliente"
                        />
                    </CardHeader>

                    <CardBody className="p-6 md:p-8">
                        <form
                            className="space-y-8"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            <motion.div
                                className="space-y-2"
                                variants={itemVariants}
                            >
                                <label
                                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
                                    htmlFor="customerName"
                                >
                                    <span>Nombre del Cliente</span>
                                    <Tooltip
                                        content="Nombre completo del cliente. Ejemplo: Juan Pérez, Empresa XYZ, etc."
                                        placement="top"
                                    >
                                        <InformationCircleIcon className="ml-1.5 text-gray-400 h-3.5 w-3.5 hover:text-primary-500 transition-colors" />
                                    </Tooltip>
                                </label>

                                <Input
                                    {...register("name")}
                                    isRequired
                                    classNames={{
                                        base: "max-w-full",
                                        inputWrapper: `
                                            bg-transparent shadow-sm border-2 transition-all duration-200
                                            data-[hover=true]:border-primary-500/50 
                                            data-[focus=true]:border-primary-500
                                        `,
                                        input: "text-lg font-medium",
                                    }}
                                    color={getInputColor()}
                                    description="Este nombre se utilizará para identificar al cliente en el sistema"
                                    endContent={
                                        isValidating ? (
                                            <ArrowPathIcon className="w-4 h-4 text-gray-400 dark:text-gray-500 animate-spin" />
                                        ) : isValid === false &&
                                          currentName.length >= 2 ? (
                                            <ExclamationTriangleIcon className="w-4 h-4 text-warning" />
                                        ) : isValid === true &&
                                          currentName.length >= 2 ? (
                                            <CheckIcon className="w-4 h-4 text-success" />
                                        ) : null
                                    }
                                    errorMessage={errors.name?.message}
                                    id="customerName"
                                    placeholder="Ej: Juan Pérez, Empresa XYZ"
                                    startContent={
                                        <UserGroupIcon className="text-primary-500 h-4 w-4" />
                                    }
                                    variant="bordered"
                                />

                                <CustomerInfoMessage
                                    message="El nombre ya está registrado en el sistema o fue eliminado recientemente. Puedes elegir otro nombre o ignorar esta validación si estás seguro."
                                    show={
                                        isValid === false &&
                                        currentName.length >= 2 &&
                                        !bypassValidation &&
                                        !isValidating
                                    }
                                    title="Este nombre ya existe"
                                    type="warning"
                                />

                                <CustomerInfoMessage
                                    message="Estamos verificando que el nombre esté disponible en el sistema..."
                                    show={
                                        isValidating && currentName.length >= 2
                                    }
                                    title="Validando disponibilidad"
                                    type="info"
                                />
                            </motion.div>

                            {/* Campo de nombre para mostrar */}
                            <motion.div
                                className="space-y-2"
                                variants={itemVariants}
                            >
                                <label
                                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
                                    htmlFor="displayName"
                                >
                                    <span>Nombre para Mostrar (Opcional)</span>
                                    <Tooltip
                                        content="Cómo se mostrará en la interfaz. Si no se especifica, se usará el nombre del cliente."
                                        placement="top"
                                    >
                                        <InformationCircleIcon className="ml-1.5 text-gray-400 h-3.5 w-3.5 hover:text-primary-500 transition-colors" />
                                    </Tooltip>
                                </label>

                                <Textarea
                                    {...register("displayName")}
                                    classNames={{
                                        base: "max-w-full",
                                        inputWrapper: `
                                            bg-transparent shadow-sm border-2 transition-all duration-200
                                            data-[hover=true]:border-primary-500/50 
                                            data-[focus=true]:border-primary-500
                                        `,
                                        input: "text-base",
                                    }}
                                    description="Ej: Empresa ABC - División XYZ"
                                    errorMessage={errors.displayName?.message}
                                    id="displayName"
                                    maxRows={3}
                                    minRows={2}
                                    placeholder="Nombre descriptivo más largo para mostrar en la interfaz"
                                    variant="bordered"
                                />
                            </motion.div>

                            {/* Mensaje informativo */}
                            <motion.div variants={itemVariants}>
                                <CustomerInfoMessage
                                    message="Una vez creado el cliente, podrás asignarle órdenes y gestionar su información desde el panel de clientes."
                                    show={
                                        !errors.name &&
                                        currentName.length >= 2 &&
                                        isValid === true
                                    }
                                    title="Información importante"
                                    type="info"
                                />
                            </motion.div>

                            {/* Botones de acción */}
                            <motion.div
                                className="flex justify-end gap-3 pt-4"
                                variants={itemVariants}
                            >
                                <Button
                                    className="font-medium text-gray-700 dark:text-gray-200"
                                    variant="flat"
                                    onPress={handleCancel}
                                >
                                    Cancelar
                                </Button>

                                {bypassValidation && (
                                    <Button
                                        className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:opacity-90 text-white shadow-md"
                                        color="warning"
                                        onPress={() =>
                                            setBypassValidation(false)
                                        }
                                    >
                                        Reactivar validación
                                    </Button>
                                )}

                                <Button
                                    className="bg-gradient-to-r from-primary-600 to-primary-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-1px]"
                                    isDisabled={
                                        !isDirty ||
                                        !isFormValid ||
                                        isSubmitting ||
                                        (isValid === false && !bypassValidation)
                                    }
                                    isLoading={isSubmitting}
                                    type="submit"
                                >
                                    {isSubmitting
                                        ? "Procesando..."
                                        : bypassValidation
                                          ? "Crear (Ignorando validación)"
                                          : "Crear Cliente"}
                                </Button>
                            </motion.div>
                        </form>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Modal de confirmación de cancelación */}
            <Modal
                backdrop="blur"
                classNames={{
                    backdrop: "bg-gradient-to-t from-black/20 to-black/10",
                    base: "border border-gray-200 dark:border-gray-800 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md",
                    header: "border-b border-gray-100 dark:border-gray-800",
                    footer: "border-t border-gray-100 dark:border-gray-800",
                    closeButton: "hover:bg-gray-100 dark:hover:bg-gray-800",
                }}
                isOpen={isOpen}
                motionProps={{
                    variants: {
                        enter: {
                            y: 0,
                            opacity: 1,
                            transition: {
                                duration: 0.3,
                                ease: "easeOut",
                            },
                        },
                        exit: {
                            y: -20,
                            opacity: 0,
                            transition: {
                                duration: 0.2,
                                ease: "easeIn",
                            },
                        },
                    },
                }}
                placement="center"
                onClose={onClose}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="flex flex-col gap-1">
                                <div className="flex items-center gap-2">
                                    <ExclamationTriangleIcon className="w-5 h-5 text-amber-600" />
                                    <span>Cambios sin guardar</span>
                                </div>
                            </ModalHeader>
                            <ModalBody>
                                <div className="text-center mb-4">
                                    <div className="bg-amber-50 dark:bg-amber-900/20 mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-3">
                                        <ExclamationTriangleIcon className="w-8 h-8 text-amber-600 dark:text-amber-400" />
                                    </div>
                                </div>
                                <p className="text-gray-700 dark:text-gray-300 text-center">
                                    Tienes cambios sin guardar. ¿Estás seguro de
                                    que deseas salir? Los cambios se perderán.
                                </p>
                            </ModalBody>
                            <ModalFooter>
                                <Button
                                    color="primary"
                                    variant="light"
                                    onPress={onClose}
                                >
                                    Continuar editando
                                </Button>
                                <Button
                                    color="danger"
                                    onPress={() => {
                                        onClose();
                                        router.push("/dashboard/customers");
                                    }}
                                >
                                    Salir sin guardar
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </DashboardLayout>
    );
}
