{"name": "lohari", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix", "lint:errors": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix --quiet", "lint:report": "eslint . --ext .ts,.tsx -c .eslintrc.json --output-file lint-report.txt", "lint:check": "eslint . --ext .ts,.tsx -c .eslintrc.json", "lint:any": "eslint . --ext .ts,.tsx -c .eslintrc.json --rule '@typescript-eslint/no-explicit-any: error'", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd --input=scripts/PRD_MVP.txt", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "db:generate": "prisma generate", "db:seed": "prisma db seed", "db:reset": "prisma migrate reset", "db:push": "prisma db push", "db:validate": "tsx scripts/validate-config-data.ts", "postinstall": "prisma generate", "remove-console-logs": "node scripts/remove-console-logs.cjs", "fix-console-syntax": "node scripts/fix-console-syntax.cjs"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@heroicons/react": "^2.2.0", "@heroui/button": "^2.2.10", "@heroui/code": "^2.2.7", "@heroui/input": "^2.4.10", "@heroui/kbd": "^2.2.7", "@heroui/link": "^2.2.8", "@heroui/listbox": "^2.3.10", "@heroui/navbar": "^2.2.9", "@heroui/react": "^2.7.5", "@heroui/snippet": "^2.2.11", "@heroui/switch": "^2.2.9", "@heroui/system": "^2.4.10", "@heroui/theme": "^2.4.9", "@heroui/toast": "^2.0.6", "@hookform/resolvers": "^4.1.3", "@internationalized/date": "^3.7.0", "@modelcontextprotocol/server-sequential-thinking": "^0.6.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.1.2", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.18", "@react-pdf/renderer": "^4.3.0", "@supabase/supabase-js": "^2.49.1", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-table": "^8.21.2", "@types/ioredis": "^4.28.10", "@types/jspdf": "^2.0.0", "@types/lodash": "^4.17.17", "@types/luxon": "^3.6.2", "@types/qrcode": "^1.5.5", "@types/qrcode.react": "^1.0.5", "@types/react-window": "^1.8.8", "axios": "^1.8.3", "bcryptjs": "^3.0.2", "boxen": "^7.1.1", "chalk": "^5.4.1", "chroma-js": "^2.4.2", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.3", "clsx": "^2.1.1", "color": "^5.0.0", "color-namer": "^1.4.0", "commander": "^11.1.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "figlet": "^1.7.0", "framer-motion": "^11.18.2", "gradient-string": "^2.0.2", "html2canvas": "^1.4.1", "intl-messageformat": "^10.5.0", "ioredis": "^5.6.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "lucide-react": "^0.475.0", "luxon": "^3.6.1", "nanoid": "^5.1.5", "next": "^14.2.3", "next-auth": "^4.24.7", "next-themes": "^0.4.4", "nodemailer": "^6.10.0", "openai": "^4.86.1", "ora": "^7.0.1", "pg": "^8.11.3", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.57.0", "react-to-print": "^3.1.0", "react-window": "^1.8.11", "recharts": "^2.15.2", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "web-vitals": "^5.0.2", "zod": "^3.25.48"}, "devDependencies": {"@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.5.7", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "glob": "^11.0.2", "postcss": "8.4.49", "prettier": "3.3.3", "prisma": "^6.8.2", "tailwind-variants": "0.1.20", "tailwindcss": "3.4.16", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.6.3"}, "type": "module", "prisma": {"seed": "tsx prisma/seed.ts"}, "engines": {"node": ">=22.0.0"}}