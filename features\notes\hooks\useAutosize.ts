"use client";

import { useEffect, useRef } from "react";

interface UseAutosizeOptions {
    minHeight?: number;
    maxHeight?: number;
}

/**
 * Hook para hacer que un textarea ajuste su altura automáticamente
 * basándose en su contenido
 */
export function useAutosize(value: string, options: UseAutosizeOptions = {}) {
    const { minHeight = 100, maxHeight = 400 } = options;
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    useEffect(() => {
        const textarea = textareaRef.current;

        if (!textarea) return;

        // Resetear altura para obtener el scrollHeight real
        textarea.style.height = "auto";

        // Calcular nueva altura
        const scrollHeight = textarea.scrollHeight;
        const newHeight = Math.min(
            Math.max(scrollHeight, minHeight),
            maxHeight,
        );

        // Aplicar nueva altura
        textarea.style.height = `${newHeight}px`;

        // Añadir overflow cuando se alcanza maxHeight
        if (scrollHeight > maxHeight) {
            textarea.style.overflowY = "auto";
        } else {
            textarea.style.overflowY = "hidden";
        }
    }, [value, minHeight, maxHeight]);

    return textareaRef;
}
