"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
} from "@/shared/utils/actions-utils";

/**
 * Server Action para obtener todas las importancias de notas
 */
export async function getNoteImportances(): Promise<ActionResponse<any>> {
    return getImportances();
}

/**
 * Server Action para obtener todas las importancias de notas (alias)
 */
export async function getImportances(): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        // Obtener todas las importancias de notas
        const importances = await prisma.noteImportance.findMany({
            orderBy: {
                name: "asc",
            },
        });

        return {
            success: true,
            data: importances,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
