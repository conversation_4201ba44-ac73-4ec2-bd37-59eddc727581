import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, CardHeader, CardBody } from "@heroui/react";
import {
    CheckIcon,
    MagnifyingGlassIcon,
    SparklesIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

// Enhanced loading skeleton component with glassmorphism
export function OrderSelectorSkeleton() {
    return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[700px] h-[calc(100vh-200px)] max-h-[900px]">
            {/* Left panel skeleton with glassmorphism */}
            <Card className="lg:col-span-1 h-full shadow-xl rounded-2xl backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-white/20 dark:border-gray-700/50">
                <CardHeader className="pb-4 pt-6 px-6 bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 dark:from-blue-400/20 dark:via-indigo-400/20 dark:to-purple-400/20 backdrop-blur-xl border-b border-white/20 dark:border-gray-700/50 rounded-t-2xl">
                    <div className="animate-pulse">
                        <div className="h-6 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg w-32 mb-4 shimmer" />
                        <div className="h-10 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg shimmer" />
                    </div>
                </CardHeader>
                <CardBody className="p-0">
                    <div className="divide-y divide-gray-100 dark:divide-gray-800">
                        {[...Array(5)].map((_, i) => (
                            <motion.div
                                key={i}
                                animate={{ opacity: 1, x: 0 }}
                                className="p-5 animate-pulse hover:bg-gray-50/50 dark:hover:bg-gray-800/50 transition-all duration-300"
                                initial={{ opacity: 0, x: -20 }}
                                transition={{ delay: i * 0.1 }}
                            >
                                <div className="flex items-start gap-4">
                                    <div className="w-5 h-5 bg-gradient-to-br from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg shimmer" />
                                    <div className="flex-1">
                                        <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg w-3/4 mb-2 shimmer" />
                                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg w-1/2 mb-3 shimmer" />
                                        <div className="flex gap-4">
                                            <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg w-20 shimmer" />
                                            <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg w-24 shimmer" />
                                        </div>
                                    </div>
                                    <div className="h-6 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-full w-16 shimmer" />
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </CardBody>
            </Card>

            {/* Right panel skeleton with glassmorphism */}
            <Card className="lg:col-span-2 h-full shadow-xl rounded-2xl backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-white/20 dark:border-gray-700/50">
                <CardHeader className="py-6 px-6 bg-gradient-to-br from-gray-500/5 via-slate-500/5 to-zinc-500/5 dark:from-gray-400/10 dark:via-slate-400/10 dark:to-zinc-400/10 backdrop-blur-xl border-b border-white/20 dark:border-gray-700/50 rounded-t-2xl">
                    <div className="animate-pulse">
                        <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-48 mb-2" />
                        <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-32" />
                    </div>
                </CardHeader>
                <CardBody className="p-8">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                        {[...Array(4)].map((_, i) => (
                            <div
                                key={i}
                                className="bg-gray-50 dark:bg-gray-800 rounded-xl p-5 animate-pulse"
                            >
                                <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-20 mb-2" />
                                <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-12" />
                            </div>
                        ))}
                    </div>
                    <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                            <div
                                key={i}
                                className="border dark:border-gray-700 rounded-xl p-5 animate-pulse"
                            >
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center gap-3">
                                        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-700 rounded-full" />
                                        <div>
                                            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-24 mb-2" />
                                            <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-16" />
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-12 mb-1" />
                                        <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded w-16" />
                                    </div>
                                </div>
                                <div className="grid grid-cols-6 gap-2">
                                    {[...Array(6)].map((_, j) => (
                                        <div
                                            key={j}
                                            className="bg-gray-100 dark:bg-gray-800 rounded-lg p-2"
                                        >
                                            <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded w-full mb-1" />
                                            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full" />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </CardBody>
            </Card>
        </div>
    );
}

// Enhanced search suggestions component with glassmorphism
export function SearchSuggestions({
    suggestions,
    onSelect,
}: {
    suggestions: string[];
    onSelect: (value: string) => void;
}) {
    if (suggestions.length === 0) return null;

    return (
        <motion.div
            animate={{ opacity: 1, y: 0, scale: 1 }}
            className="absolute top-full left-0 right-0 mt-2 backdrop-blur-xl bg-white/90 dark:bg-gray-800/90 border border-white/20 dark:border-gray-700/50 rounded-xl shadow-2xl z-10 max-h-48 overflow-y-auto"
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{
                type: "spring",
                stiffness: 500,
                damping: 30,
                duration: 0.2,
            }}
        >
            {suggestions.map((suggestion, index) => (
                <motion.div
                    key={index}
                    animate={{ opacity: 1, x: 0 }}
                    className="px-4 py-3 hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 dark:hover:from-blue-400/20 dark:hover:to-purple-400/20 cursor-pointer transition-all duration-200 group"
                    initial={{ opacity: 0, x: -10 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{ x: 4 }}
                    onClick={() => onSelect(suggestion)}
                >
                    <div className="flex items-center gap-3">
                        <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors" />
                        <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white transition-colors">
                            {suggestion}
                        </span>
                        <SparklesIcon className="w-3 h-3 text-purple-400 dark:text-purple-300 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                </motion.div>
            ))}
        </motion.div>
    );
}

// Enhanced selection animation component with modern styling
export function SelectionConfirmation({ show }: { show: boolean }) {
    return (
        <AnimatePresence>
            {show && (
                <motion.div
                    animate={{ scale: 1, opacity: 1, y: 0 }}
                    className="fixed bottom-8 right-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-2xl shadow-2xl flex items-center gap-3 z-50 backdrop-blur-xl"
                    exit={{ scale: 0.8, opacity: 0, y: 20 }}
                    initial={{ scale: 0.8, opacity: 0, y: 20 }}
                    transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                    }}
                >
                    <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 0.5, ease: "easeInOut" }}
                    >
                        <CheckIcon className="w-6 h-6" />
                    </motion.div>
                    <span className="font-medium">Orden seleccionada</span>
                    <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 0.6, repeat: 2 }}
                    >
                        <SparklesIcon className="w-5 h-5 text-green-200" />
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
} // Keyboard navigation hook
export function useKeyboardNavigation({
    items,
    focusedIndex,
    setFocusedIndex,
    onSelect,
    onToggle,
    isActive = true,
}: {
    items: any[];
    focusedIndex: number;
    setFocusedIndex: (index: number) => void;
    onSelect: (item: any) => void;
    onToggle?: (item: any) => void;
    isActive?: boolean;
}) {
    useEffect(() => {
        if (!isActive) return;

        const handleKeyDown = (e: KeyboardEvent) => {
            switch (e.key) {
                case "ArrowDown":
                    e.preventDefault();
                    setFocusedIndex(
                        Math.min(focusedIndex + 1, items.length - 1),
                    );
                    break;
                case "ArrowUp":
                    e.preventDefault();
                    setFocusedIndex(Math.max(focusedIndex - 1, 0));
                    break;
                case "Enter":
                    e.preventDefault();
                    if (focusedIndex >= 0 && focusedIndex < items.length) {
                        onSelect(items[focusedIndex]);
                    }
                    break;
                case " ":
                    e.preventDefault();
                    if (
                        focusedIndex >= 0 &&
                        focusedIndex < items.length &&
                        onToggle
                    ) {
                        onToggle(items[focusedIndex]);
                    }
                    break;
                case "Home":
                    e.preventDefault();
                    setFocusedIndex(0);
                    break;
                case "End":
                    e.preventDefault();
                    setFocusedIndex(items.length - 1);
                    break;
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [items, focusedIndex, setFocusedIndex, onSelect, onToggle, isActive]);

    // Scroll focused item into view
    useEffect(() => {
        if (focusedIndex >= 0) {
            const element = document.querySelector(
                `[data-order-index="${focusedIndex}"]`,
            );

            element?.scrollIntoView({ behavior: "smooth", block: "nearest" });
        }
    }, [focusedIndex]);
}

// Focus trap component for accessibility
export function FocusTrap({
    children,
    isActive,
}: {
    children: React.ReactNode;
    isActive: boolean;
}) {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!isActive) return;

        const container = containerRef.current;

        if (!container) return;

        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        );
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[
            focusableElements.length - 1
        ] as HTMLElement;

        const handleTabKey = (e: KeyboardEvent) => {
            if (e.key !== "Tab") return;

            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement?.focus();
                }
            } else {
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement?.focus();
                }
            }
        };

        container.addEventListener("keydown", handleTabKey);
        firstElement?.focus();

        return () => {
            container.removeEventListener("keydown", handleTabKey);
        };
    }, [isActive]);

    return <div ref={containerRef}>{children}</div>;
}

// Enhanced animated counter component with visual feedback
export function AnimatedCounter({
    value,
    duration = 1000,
    showChange = true,
}: {
    value: number;
    duration?: number;
    showChange?: boolean;
}) {
    const [displayValue, setDisplayValue] = useState(0);
    const [isIncreasing, setIsIncreasing] = useState<boolean | null>(null);
    const previousValue = useRef(0);

    useEffect(() => {
        const startValue = previousValue.current;
        const endValue = value;
        const startTime = Date.now();

        // Determine if value is increasing or decreasing
        if (showChange && startValue !== endValue) {
            setIsIncreasing(endValue > startValue);
            setTimeout(() => setIsIncreasing(null), 2000);
        }

        const updateValue = () => {
            const now = Date.now();
            const progress = Math.min((now - startTime) / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.round(
                startValue + (endValue - startValue) * easeOutQuart,
            );

            setDisplayValue(currentValue);

            if (progress < 1) {
                requestAnimationFrame(updateValue);
            } else {
                previousValue.current = endValue;
            }
        };

        requestAnimationFrame(updateValue);
    }, [value, duration, showChange]);

    return (
        <span className="relative inline-flex items-center">
            <motion.span
                key={displayValue}
                animate={{ scale: [1, 1.1, 1] }}
                className={`font-bold transition-colors duration-300 ${
                    isIncreasing === true
                        ? "text-green-600 dark:text-green-400"
                        : isIncreasing === false
                          ? "text-red-600 dark:text-red-400"
                          : ""
                }`}
            >
                {displayValue.toLocaleString()}
            </motion.span>
            <AnimatePresence>
                {isIncreasing !== null && (
                    <motion.span
                        animate={{ opacity: 1, y: -20 }}
                        className={`absolute -right-6 text-xs font-medium ${
                            isIncreasing ? "text-green-500" : "text-red-500"
                        }`}
                        exit={{ opacity: 0 }}
                        initial={{ opacity: 0, y: 10 }}
                    >
                        {isIncreasing ? "+" : "-"}
                        {Math.abs(value - previousValue.current)}
                    </motion.span>
                )}
            </AnimatePresence>
        </span>
    );
}

// Enhanced loading dots animation
export function LoadingDots() {
    return (
        <span className="inline-flex items-center gap-1">
            {[0, 1, 2].map((i) => (
                <motion.span
                    key={i}
                    animate={{
                        y: [0, -8, 0],
                        opacity: [0.3, 1, 0.3],
                    }}
                    className="inline-block w-1.5 h-1.5 bg-current rounded-full"
                    transition={{
                        duration: 1.2,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: "easeInOut",
                    }}
                />
            ))}
        </span>
    );
}

// Smooth scroll hook
export function useSmoothScroll() {
    const scrollToElement = (elementId: string) => {
        const element = document.getElementById(elementId);

        if (element) {
            element.scrollIntoView({
                behavior: "smooth",
                block: "center",
                inline: "nearest",
            });
        }
    };

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        });
    };

    return { scrollToElement, scrollToTop };
}

// Enhanced granular loading states with shimmer effect
export function OrderListItemSkeleton() {
    return (
        <motion.div
            animate={{ opacity: 1 }}
            className="p-4 relative overflow-hidden"
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-gradient-to-br from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg shimmer" />
                <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-lg w-3/4 shimmer" />
                    <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg w-1/2 shimmer" />
                    <div className="flex gap-3">
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg w-20 shimmer" />
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg w-24 shimmer" />
                    </div>
                </div>
                <div className="h-6 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-600 rounded-full w-16 shimmer" />
            </div>

            {/* Shimmer overlay effect */}
            <motion.div
                animate={{ translateX: "200%" }}
                className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            />
        </motion.div>
    );
}

// Inline loading state for updates
export function InlineLoadingIndicator({
    text = "Actualizando",
}: {
    text?: string;
}) {
    return (
        <motion.div
            animate={{ opacity: 1 }}
            className="inline-flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
            exit={{ opacity: 0 }}
            initial={{ opacity: 0 }}
        >
            <motion.div
                animate={{ rotate: 360 }}
                className="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full"
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <span>
                {text}
                <LoadingDots />
            </span>
        </motion.div>
    );
}

// Enhanced transition wrapper for smooth animations
export function TransitionWrapper({
    children,
    delay = 0,
    className = "",
}: {
    children: React.ReactNode;
    delay?: number;
    className?: string;
}) {
    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={className}
            exit={{ opacity: 0, y: -20 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                delay,
            }}
        >
            {children}
        </motion.div>
    );
}

// Enhanced selection indicator with ripple effect
export function SelectionIndicator({ isSelected }: { isSelected: boolean }) {
    return (
        <AnimatePresence>
            {isSelected && (
                <motion.div
                    animate={{ scale: 1, opacity: 1 }}
                    className="absolute inset-0 pointer-events-none"
                    exit={{ scale: 0, opacity: 0 }}
                    initial={{ scale: 0, opacity: 0 }}
                >
                    <motion.div
                        animate={{ scale: [1, 1.5, 2], opacity: [0.5, 0.3, 0] }}
                        className="absolute inset-0 bg-green-500 rounded-lg"
                        transition={{ duration: 0.6, ease: "easeOut" }}
                    />
                </motion.div>
            )}
        </AnimatePresence>
    );
}

// Transition animations config
export const pageTransition = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
    transition: { type: "spring", stiffness: 300, damping: 30 },
};

export const listItemTransition = {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
    whileHover: {
        scale: 1.02,
        transition: { type: "spring", stiffness: 400, damping: 30 },
    },
};

// Enhanced focus indicator with glow effect
export function FocusIndicator({ isFocused }: { isFocused: boolean }) {
    return (
        <AnimatePresence>
            {isFocused && (
                <>
                    <motion.div
                        animate={{ opacity: 1 }}
                        className="absolute inset-0 ring-2 ring-offset-2 ring-blue-500 dark:ring-blue-400 rounded-xl pointer-events-none"
                        exit={{ opacity: 0 }}
                        initial={{ opacity: 0 }}
                        transition={{ duration: 0.2 }}
                    />
                    <motion.div
                        animate={{ opacity: [0.4, 0.8, 0.4] }}
                        className="absolute inset-0 bg-blue-500/20 dark:bg-blue-400/20 rounded-xl pointer-events-none blur-xl"
                        transition={{ duration: 2, repeat: Infinity }}
                    />
                </>
            )}
        </AnimatePresence>
    );
}

// Add shimmer effect styles
const shimmerStyles = `
<style>
    .shimmer {
        position: relative;
        overflow: hidden;
    }
    
    .shimmer::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        animation: shimmer 1.5s infinite;
    }
    
    @keyframes shimmer {
        to {
            left: 100%;
        }
    }
</style>
`;
