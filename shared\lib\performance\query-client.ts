/**
 * React Query Configuration
 * Centralizes all React Query settings and providers
 */

import { QueryClient, QueryClientConfig } from "@tanstack/react-query";

/**
 * Default configuration for React Query
 * Based on the creative phase decisions for performance optimization
 */
export const defaultQueryConfig: QueryClientConfig = {
    defaultOptions: {
        queries: {
            // Stale time: 5 minutes (data remains fresh)
            staleTime: 5 * 60 * 1000,

            // Cache time: 10 minutes (data stays in cache)
            gcTime: 10 * 60 * 1000,

            // Retry failed requests 3 times with exponential backoff
            retry: 3,
            retryDelay: (attemptIndex) =>
                Math.min(1000 * 2 ** attemptIndex, 30000),

            // Don't refetch on window focus by default (can be overridden per query)
            refetchOnWindowFocus: false,

            // Always refetch on reconnect
            refetchOnReconnect: "always",

            // Network mode: online first with fallback to cache
            networkMode: "online",
        },
        mutations: {
            // Retry failed mutations once
            retry: 1,

            // Network mode for mutations
            networkMode: "online",
        },
    },
};

/**
 * Create a new QueryClient instance with default configuration
 */
export function createQueryClient(config?: QueryClientConfig): QueryClient {
    return new QueryClient({
        ...defaultQueryConfig,
        ...config,
    });
}

/**
 * Global query client instance (created on demand)
 */
let globalQueryClient: QueryClient | undefined;

/**
 * Get or create the global query client
 */
export function getQueryClient(): QueryClient {
    if (!globalQueryClient) {
        globalQueryClient = createQueryClient();
    }

    return globalQueryClient;
}

/**
 * Feature flags for gradual rollout
 */
export const performanceFlags = {
    useReactQuery: process.env.NEXT_PUBLIC_ENABLE_REACT_QUERY === "true",
    enableRedisCache: process.env.NEXT_PUBLIC_ENABLE_REDIS === "true",
    useVirtualScrolling:
        process.env.NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLL === "true",
    enablePrefetching: process.env.NEXT_PUBLIC_ENABLE_PREFETCH === "true",
};

/**
 * Cache strategies based on data type (from creative phase)
 */
export enum CacheStrategy {
    STATIC = "static", // CDN: 24h TTL
    USER_SPECIFIC = "user", // React Query: 5m TTL
    REAL_TIME = "realtime", // No cache
    COMPUTED = "computed", // Redis: 1h TTL
}

/**
 * Helper to determine cache strategy for a given resource
 */
export function getCacheStrategy(resourceType: string): CacheStrategy {
    // This will be expanded based on actual resource types
    const strategyMap: Record<string, CacheStrategy> = {
        "static-reports": CacheStrategy.STATIC,
        "user-preferences": CacheStrategy.USER_SPECIFIC,
        "active-orders": CacheStrategy.REAL_TIME,
        analytics: CacheStrategy.COMPUTED,
    };

    return strategyMap[resourceType] || CacheStrategy.USER_SPECIFIC;
}
