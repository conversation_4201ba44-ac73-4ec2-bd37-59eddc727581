import { NextRequest, NextResponse } from "next/server";

import { auth } from "@/lib/auth-helpers";
import { RemissionPreview } from "@/types/remission";
import { RemissionPDFService } from "@/features/remissions/services/pdf-service";

/**
 * POST /api/remissions/pdf
 * Generate PDF from remission data
 */
export async function POST(request: NextRequest) {
    try {
        // Check authentication
        const session = await auth();

        if (!session || !session.user) {
            return NextResponse.json(
                { error: "No autorizado" },
                { status: 401 },
            );
        }

        // Parse request body
        const body = await request.json();
        const { remissionData } = body as {
            remissionData: RemissionPreview;
            orientation?: "portrait" | "landscape";
        };

        // Validate remission data
        const validation =
            RemissionPDFService.validateRemissionData(remissionData);

        if (!validation.isValid) {
            return NextResponse.json(
                {
                    error: "Datos de remisión inválidos",
                    details: validation.errors,
                },
                { status: 400 },
            );
        }

        // Generate PDF
        const pdfBlob = await RemissionPDFService.generatePDF(
            remissionData,
            "portrait",
        );

        // Convert blob to buffer
        const arrayBuffer = await pdfBlob.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Return PDF as response
        return new NextResponse(buffer, {
            status: 200,
            headers: {
                "Content-Type": "application/pdf",
                "Content-Disposition": `attachment; filename="remision-${remissionData.folio}.pdf"`,
                "Content-Length": buffer.length.toString(),
            },
        });
    } catch (error) {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            {
                error: "Error al generar PDF",
                message:
                    error instanceof Error
                        ? error.message
                        : "Error desconocido",
            },
            { status: 500 },
        );
    }
}

/**
 * GET /api/remissions/pdf/preview
 * Generate PDF preview (returns base64)
 */
export async function GET(request: NextRequest) {
    try {
        // Check authentication
        const session = await auth();

        if (!session || !session.user) {
            return NextResponse.json(
                { error: "No autorizado" },
                { status: 401 },
            );
        }

        // Get remission ID from query params
        const { searchParams } = new URL(request.url);
        const remissionId = searchParams.get("id");

        if (!remissionId) {
            return NextResponse.json(
                { error: "ID de remisión requerido" },
                { status: 400 },
            );
        }

        // TODO: Fetch remission data from database
        // For now, return error indicating this needs implementation
        return NextResponse.json(
            {
                error: "Endpoint de preview no implementado",
                message:
                    "Este endpoint requiere integración con la base de datos",
            },
            { status: 501 },
        );
    } catch (error) {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            {
                error: "Error al generar preview",
                message:
                    error instanceof Error
                        ? error.message
                        : "Error desconocido",
            },
            { status: 500 },
        );
    }
}
