# Lohari - Sistema de Control de Producción Textil

## Product Overview

Lohari es un sistema de gestión de producción textil basado en los 4 principios fundamentales de la administración: **Planear, Organizar, Dirigir y Controlar**, con especial énfasis en el CONTROL. El sistema permite tener un control integral de pedidos, entregas, contratistas y clientes para empresas de manufactura textil.

## Core Purpose

Resolver el problema de control en empresas textiles mediante:
- **Control de Pedidos**: Registro completo y trazabilidad de órdenes desde la recepción hasta la entrega
- **Gestión de Contratistas**: Asignación de trabajo y seguimiento de producción por contratista
- **Visibilidad Bidireccional**: Ver qué órdenes tiene cada contratista y qué contratistas trabajan en cada orden
- **Proceso de Empaque**: Flujo estructurado para preparar pedidos terminados para entrega
- **Gestión de Clientes y Subclientes**: Manejo de jerarquías complejas (ej: Becktel → Cuidado con el Perro, Cotton, etc.)

## Target Users

**Inicio**: Pequeñas empresas textiles que necesitan control de producción
**Visión**: Escalable para empresas medianas y grandes en el futuro

Usuarios principales:
- **Dueños/Gerentes**: Control total del negocio y toma de decisiones
- **Encargados de Producción**: Asignación y seguimiento de trabajo
- **Personal de Empaque**: Preparación de pedidos para entrega
- **Contratistas**: Visualización de trabajo asignado

## Business Context

- **Modelo Actual**: Single-tenant (una empresa)
- **Visión Futura**: Potencial para multi-tenant (múltiples empresas)
- **Enfoque**: Control integral siguiendo principios administrativos
- **Escalabilidad**: Diseñado para crecer con el negocio

## Technical Foundation

- **Framework**: Next.js 14.2.3 con App Router (decisión técnica clave)
- **Language**: TypeScript con strict mode
- **Database**: PostgreSQL via Prisma ORM (decisión técnica clave)
- **State Management**: Server Actions + SWR (no APIs tradicionales - decisión técnica clave)
- **Authentication**: NextAuth v4
- **UI Library**: HeroUI v2 + TailwindCSS
- **PDF Generation**: @react-pdf/renderer

## Key Features

1. **Orders Module** (`/features/orders`)
   - Create and manage production orders
   - Track order status and progress
   - Assign orders to contractors
   - Monitor delivery dates and risks

2. **Assignments Module** (`/features/assignments`)
   - Distribute work to contractors
   - Track production quantities
   - Monitor assignment progress
   - Manage contractor workload

3. **Packing Module** (`/features/packings`)
   - Create packing lists
   - Generate shipping documents
   - Track quality control
   - Manage transport details

4. **Remissions Module** (`/features/remissions`)
   - Generate remission documents
   - PDF export functionality
   - Digital signature support
   - Document history tracking

5. **Inventory Management**
   - Sizes (`/features/sizes`)
   - Colors (`/features/colors`)
   - Models (`/features/models`)
   - Customers (`/features/customers`)
   - Contractors (`/features/contractors`)

6. **Notes System** (`/features/notes`)
   - Attach notes to any entity
   - Importance levels
   - Comment threads
   - Activity tracking

## Architecture Principles

1. **Feature-Based Structure**: Each module is self-contained with its own actions, components, hooks, and types
2. **Server-First Approach**: Use Server Components by default, Client Components only when needed
3. **Type Safety**: Strict TypeScript configuration with Zod validation
4. **Performance**: Optimized with proper caching and revalidation strategies
5. **Security**: Authentication required, role-based access control ready

## Development Standards

- **Code Style**: English for code, Spanish for data/UI
- **File Organization**: 500 lines max per file
- **Error Handling**: Centralized with user-friendly messages
- **Testing**: Component and integration tests
- **Documentation**: Inline comments in Spanish

## Current Status

El sistema está en desarrollo activo con módulos principales operativos. 

**Trabajo Actual**:
- **Gestión de Subclientes**: Pendiente de implementación completa en:
  - Feature de Clientes: CRUD de subclientes al cliente principal
  - Feature de Orders: Asociación de órdenes con cliente principal y subclientes
  - Feature de Packing: Selección de clientes y subclientes en empaque (aqui la logica de cliente principal esta bien, sin embargo el subliente digamos que le pertenece a la orden, por que cada order puede tener diferenet o mismo subcliente, entonces, se pondra el subcliente de la orden)
- **Módulo de Packing**: En proceso de refinamiento para alinear con objetivos reales del negocio

## Development Philosophy

- **Decisiones Técnicas Clave**:
  - Next.js + Server Actions: Simplicidad y rendimiento sin APIs tradicionales
  - PostgreSQL + Prisma: Type safety y migraciones automáticas
  - SWR en lugar de APIs: Mejor experiencia de desarrollo
  - Todo el stack moderno para facilitar mantenimiento

## Future Roadmap

### Prioridad Inmediata
- [ ] Implementar gestión completa de Subclientes
- [ ] Refinar lógica de Packing para cumplir objetivos reales
- [ ] Mejorar flujo de selección de órdenes en Packing

### Corto Plazo (Q1-Q2 2025)
- [ ] Reportes y analytics de producción
- [ ] Dashboard de control gerencial
- [ ] Optimización de flujos de trabajo

### Largo Plazo
- [ ] Aplicación móvil para contratistas
- [ ] Sistema de notificaciones
- [ ] Multi-tenant para escalar a SaaS
- [ ] Integraciones con sistemas contables