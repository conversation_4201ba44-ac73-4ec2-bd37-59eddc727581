"use client";

import React from "react";
import { <PERSON>, Card<PERSON><PERSON>, But<PERSON> } from "@heroui/react";
import {
    DocumentPlusIcon,
    FolderOpenIcon,
    MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

interface RemissionEmptyStateProps {
    type?: "no-results" | "no-data" | "filtered";
    onClearFilters?: () => void;
}

export default function RemissionEmptyState({
    type = "no-data",
    onClearFilters,
}: RemissionEmptyStateProps) {
    const router = useRouter();

    const configs = {
        "no-results": {
            icon: MagnifyingGlassIcon,
            title: "No se encontraron resultados",
            description: "Intenta ajustar los filtros de búsqueda",
            action: onClearFilters ? (
                <Button
                    color="primary"
                    startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
                    variant="flat"
                    onPress={onClearFilters}
                >
                    Limpiar filtros
                </Button>
            ) : null,
        },
        "no-data": {
            icon: FolderOpenIcon,
            title: "No hay remisiones",
            description: "Crea tu primera remisión para comenzar",
            action: (
                <Button
                    color="primary"
                    startContent={<DocumentPlusIcon className="w-4 h-4" />}
                    onPress={() => router.push("/dashboard/assignments/new")}
                >
                    Crear asignación
                </Button>
            ),
        },
        filtered: {
            icon: MagnifyingGlassIcon,
            title: "No hay remisiones que coincidan",
            description:
                "Prueba con diferentes filtros o crea una nueva remisión",
            action: (
                <div className="flex gap-2">
                    {onClearFilters && (
                        <Button
                            color="default"
                            variant="flat"
                            onPress={onClearFilters}
                        >
                            Limpiar filtros
                        </Button>
                    )}
                    <Button
                        color="primary"
                        startContent={<DocumentPlusIcon className="w-4 h-4" />}
                        onPress={() =>
                            router.push("/dashboard/assignments/new")
                        }
                    >
                        Nueva asignación
                    </Button>
                </div>
            ),
        },
    };

    const config = configs[type];
    const Icon = config.icon;

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-center py-12"
            initial={{ opacity: 0, y: 20 }}
            transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
            }}
        >
            <Card className="max-w-md w-full">
                <CardBody className="text-center py-12">
                    <motion.div
                        animate={{ scale: 1 }}
                        className="mx-auto mb-6"
                        initial={{ scale: 0 }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                            delay: 0.1,
                        }}
                    >
                        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center">
                            <Icon className="w-12 h-12 text-blue-600 dark:text-blue-400" />
                        </div>
                    </motion.div>

                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        initial={{ opacity: 0, y: 10 }}
                        transition={{ delay: 0.2 }}
                    >
                        <h3 className="text-xl font-semibold mb-2">
                            {config.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-6">
                            {config.description}
                        </p>
                        {config.action}
                    </motion.div>
                </CardBody>
            </Card>
        </motion.div>
    );
}
