import React from "react";
import { Chip, ChipProps } from "@heroui/react";

// import { cn } from "@/shared/utils/cn"; // Comment out - module doesn't exist
const cn = (...classes: any[]) => classes.filter(Boolean).join(" "); // Simple fallback

export interface ImprovedBadgeProps
    extends Omit<ChipProps, "color" | "variant"> {
    variant?: "default" | "success" | "warning" | "error" | "info" | "primary";
    contrast?: "high" | "medium" | "low";
}

/**
 * Badge mejorado con mejor contraste y colores semánticos
 * Basado en HeroUI Chip
 */
export const ImprovedBadge = React.forwardRef<
    HTMLDivElement,
    ImprovedBadgeProps
>(({ variant = "default", contrast = "high", className, ...props }, ref) => {
    const variantStyles = {
        default: {
            color: "default" as const,
            className:
                "bg-[var(--color-gray-3)] text-[var(--color-gray-11)] border-[var(--color-gray-4)]",
        },
        success: {
            color: "success" as const,
            className:
                contrast === "high"
                    ? "bg-[var(--color-success-9)] text-white"
                    : "bg-green-100 text-[var(--color-success-11)] border-green-200",
        },
        warning: {
            color: "warning" as const,
            className:
                contrast === "high"
                    ? "bg-[var(--color-warning-9)] text-white"
                    : "bg-amber-100 text-[var(--color-warning-11)] border-amber-200",
        },
        error: {
            color: "danger" as const,
            className:
                contrast === "high"
                    ? "bg-[var(--color-error-9)] text-white"
                    : "bg-red-100 text-[var(--color-error-11)] border-red-200",
        },
        info: {
            color: "primary" as const,
            className:
                contrast === "high"
                    ? "bg-[var(--color-info-9)] text-white"
                    : "bg-blue-100 text-[var(--color-info-11)] border-blue-200",
        },
        primary: {
            color: "primary" as const,
            className: "bg-[var(--color-primary-9)] text-white",
        },
    };

    const config = variantStyles[variant];

    return (
        <Chip
            ref={ref}
            className={cn(config.className, "font-medium", className)}
            color={config.color}
            variant={contrast === "high" ? "solid" : "flat"}
            {...props}
        />
    );
});

ImprovedBadge.displayName = "ImprovedBadge";

export default ImprovedBadge;
