import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

/**
 * ConfigCache - Sistema de cache singleton para configuraciones
 * Carga todas las configuraciones en memoria al inicializarse
 * Evita queries repetitivas para datos que raramente cambian
 */
export class ConfigCache {
    private static instance: ConfigCache;
    private configs = new Map<string, Map<string, any>>();
    private initialized = false;
    private lastLoadTime?: number;

    private constructor() {}

    /**
     * Obtiene la instancia única del ConfigCache
     * NOTA: Para desarrollo, forzamos recarga si han pasado más de 5 minutos
     */
    static async getInstance(): Promise<ConfigCache> {
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;

        if (!ConfigCache.instance) {
            ConfigCache.instance = new ConfigCache();
            ConfigCache.instance.lastLoadTime = now;
            await ConfigCache.instance.loadAll();
        } else if (
            process.env.NODE_ENV === "development" &&
            ConfigCache.instance.lastLoadTime &&
            now - ConfigCache.instance.lastLoadTime > fiveMinutes
        ) {
            // En desarrollo, recargar cada 5 minutos
            // REMOVED: console.log("🔄 Recargando ConfigCache (han pasado más de 5 minutos)");
            await ConfigCache.instance.reload();
            ConfigCache.instance.lastLoadTime = now;
        }

        return ConfigCache.instance;
    }

    /**
     * Carga todas las configuraciones de la base de datos
     */
    private async loadAll(): Promise<void> {
        if (this.initialized) return;

        try {
            // Cargar todas las tablas de configuración
            await Promise.all([
                this.loadTable("orderStatus"),
                this.loadTable("noteStatus"),
                this.loadTable("noteImportance"),
                this.loadTable("packingStatus"),
                this.loadTable("rejectionReason"),
                this.loadTable("role"),
            ]);

            this.initialized = true;
            // REMOVED: console.log("✅ ConfigCache inicializado correctamente");
        } catch (error) {
            // REMOVED: console.error("❌ Error inicializando ConfigCache:", error);
            throw error;
        }
    }

    /**
     * Carga una tabla específica en el cache
     */
    private async loadTable(tableName: string): Promise<void> {
        const data = await (prisma as any)[tableName].findMany();
        const tableMap = new Map();

        for (const item of data) {
            tableMap.set(item.name, item);
        }

        this.configs.set(tableName, tableMap);
    }

    /**
     * Obtiene un registro por nombre de una tabla específica
     */
    async getByName(tableName: string, name: string): Promise<any | null> {
        const tableMap = this.configs.get(tableName);

        return tableMap?.get(name) || null;
    }

    /**
     * Obtiene todos los registros de una tabla
     */
    async getAll(tableName: string): Promise<any[]> {
        const tableMap = this.configs.get(tableName);

        return tableMap ? Array.from(tableMap.values()) : [];
    }

    /**
     * Recarga el cache (útil después de cambios en la BD)
     */
    async reload(): Promise<void> {
        this.initialized = false;
        this.configs.clear();
        await this.loadAll();
    }
}

// Funciones helper específicas para cada tipo de configuración

export async function getOrderStatusByName(name: string) {
    const cache = await ConfigCache.getInstance();

    return cache.getByName("orderStatus", name);
}

export async function getNoteStatusByName(name: string) {
    const cache = await ConfigCache.getInstance();

    return cache.getByName("noteStatus", name);
}

export async function getNoteImportanceByName(name: string) {
    const cache = await ConfigCache.getInstance();

    return cache.getByName("noteImportance", name);
}

export async function getPackingStatusByName(name: string) {
    const cache = await ConfigCache.getInstance();

    return cache.getByName("packingStatus", name);
}

export async function getRejectionReasonByName(name: string) {
    const cache = await ConfigCache.getInstance();

    return cache.getByName("rejectionReason", name);
}

export async function getRoleByName(name: string) {
    const cache = await ConfigCache.getInstance();

    return cache.getByName("role", name);
}
