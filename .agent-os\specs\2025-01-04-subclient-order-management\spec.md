# Subclient Management in Orders Specification

## Overview
This specification outlines the changes needed to move subclient selection from the packing creation process to the order creation/editing process. Currently, subclients can only be selected when creating packings, but the business requirement is to associate subclients directly with orders.

## Current State
- **Orders**: Only have `customerId` field, no subclient selection capability
- **Packings**: Have both `customerId` and `subCustomerId` fields, with CustomerSelector component in the wizard
- **Subclients**: Full CRUD system already implemented in the customers feature

## Proposed Changes

### 1. Database Schema Update

**File**: `prisma/schema.prisma`

Add `subCustomerId` field to the Order model:

```prisma
model Order {
  id                    String               @id @default(cuid())
  transferNumber        String?
  cutOrder              String?
  batch                 String?
  receivedDate          DateTime             @default(now())
  estimatedDeliveryDate DateTime?
  deliveryDate          DateTime?
  customerId            String
  subCustomerId         String?              // NEW FIELD
  statusId              String
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  
  // Relations
  customer              Customer             @relation(fields: [customerId], references: [id])
  subCustomer           Customer?            @relation("OrderSubCustomers", fields: [subCustomerId], references: [id])
  // ... rest of relations
}

// Update Customer model to include the new relation
model Customer {
  // ... existing fields
  orders         Order[]
  orderSubCustomers Order[]  @relation("OrderSubCustomers")  // NEW RELATION
  // ... rest of relations
}
```

### 2. Remove Subclient Selection from Packing

**File**: `app/dashboard/packings/new/PackingNewWizardV2.tsx`

Replace the CustomerSelector component with a simple customer-only selector:

```typescript
// Remove lines 257-276 (CustomerSelector component)
// Replace with simple customer select:
<Select
    isRequired
    label="Cliente"
    placeholder="Seleccione el cliente"
    selectedKeys={customerId ? [customerId] : []}
    onSelectionChange={(key) => setCustomerId(key as string)}
>
    {customers.map((customer) => (
        <SelectItem key={customer.id} value={customer.id}>
            {customer.name}
        </SelectItem>
    ))}
</Select>
```

Remove subclient-related state and logic:
- Line 91: Remove `const [subCustomerId, setSubCustomerId] = useState<string | undefined>();`
- Line 177: Remove `subCustomerId,` from packingData
- Update type imports if needed

### 3. Add Subclient Selection to Orders

#### 3.1 Update OrderBasicInfoStep Component

**File**: `features/orders/components/wizard/OrderBasicInfoStep.tsx`

Update the component props and implementation:

```typescript
interface OrderBasicInfoStepProps {
    formData: {
        customerId: string;
        subCustomerId?: string;  // NEW FIELD
        statusId: string;
        transferNumber: string;
        cutOrder: string;
        batch: string;
    };
    // ... rest of props
}
```

Add CustomerSelector component after the existing customer field:

```typescript
// After the current AutocompleteSelect for customers (around line 90)
<div className="lg:col-span-1">
    <CustomerSelector
        label="Cliente / Subcliente"
        placeholder="Seleccione el destinatario"
        value={{
            customerId: formData.customerId,
            subCustomerId: formData.subCustomerId,
        }}
        onChange={({ customerId, subCustomerId }) => {
            onChange("customerId", customerId);
            onChange("subCustomerId", subCustomerId);
        }}
    />
</div>

// Remove the old AutocompleteSelect for customers
```

Add import at the top:
```typescript
import { CustomerSelector } from "@/features/customers/components/hierarchy/CustomerSelector";
```

#### 3.2 Update Order Types

**File**: `core/types/types.ts`

Update OrderFormData type:

```typescript
export interface OrderFormData {
    transferNumber: string;
    cutOrder: string;
    batch: string;
    customerId: string;
    subCustomerId?: string;  // NEW FIELD
    statusId: string;
    garments: GarmentEntry[];
    parts: Array<{ code: string }>;
    notes: any[];
}
```

#### 3.3 Update Order Creation Page

**File**: `app/dashboard/orders/new/page.tsx`

Update formData state initialization (line 55):

```typescript
const [formData, setFormData] = useState<OrderFormData>({
    transferNumber: "",
    cutOrder: "",
    batch: "1",
    customerId: "",
    subCustomerId: undefined,  // NEW FIELD
    statusId: "",
    garments: [
        { modelId: "", colorId: "", sizes: [{ sizeId: "", quantity: "" }] },
    ],
    parts: [{ code: "" }],
    notes: [],
});
```

Update the submit handler to include subCustomerId (around line 540):

```typescript
const orderData = {
    customerId: formData.customerId,
    subCustomerId: formData.subCustomerId,  // NEW FIELD
    statusId: formData.statusId,
    // ... rest of fields
};
```

#### 3.4 Update Order Edit Page

**File**: `app/dashboard/orders/[id]/edit/page.tsx`

Similar updates as the creation page:

1. Update state initialization (line 59):
```typescript
const [orderFormData, setOrderFormData] = useState<OrderFormData>({
    // ... existing fields
    subCustomerId: undefined,  // NEW FIELD
    // ... rest of fields
});
```

2. Update data loading in useEffect (line 94):
```typescript
setOrderFormData({
    // ... existing fields
    customerId: orderData.customer?.id || "",
    subCustomerId: orderData.subCustomer?.id || undefined,  // NEW FIELD
    // ... rest of fields
});
```

3. Update submit handler (line 536):
```typescript
const orderData = {
    customerId: orderFormData.customerId,
    subCustomerId: orderFormData.subCustomerId,  // NEW FIELD
    // ... rest of fields
};
```

### 4. Update Server Actions

#### 4.1 Create Order Action

**File**: `features/orders/actions/create-order.ts`

Update the input schema and implementation:

```typescript
const createOrderSchema = z.object({
    // ... existing fields
    customerId: z.string().min(1, "Customer is required"),
    subCustomerId: z.string().optional(),  // NEW FIELD
    // ... rest of fields
});

// In the create function, update the Prisma create call:
const newOrder = await db.order.create({
    data: {
        // ... existing fields
        customerId: validated.customerId,
        subCustomerId: validated.subCustomerId || null,  // NEW FIELD
        // ... rest of fields
    },
    // ... includes
});
```

#### 4.2 Update Order Action

**File**: `features/orders/actions/update-order.ts`

Similar updates:

```typescript
const updateOrderSchema = z.object({
    // ... existing fields
    customerId: z.string().min(1, "Customer is required"),
    subCustomerId: z.string().optional(),  // NEW FIELD
    // ... rest of fields
});

// In the update function:
const updatedOrder = await db.order.update({
    where: { id },
    data: {
        // ... existing fields
        customerId: validated.customerId,
        subCustomerId: validated.subCustomerId || null,  // NEW FIELD
        // ... rest of fields
    },
    // ... includes
});
```

#### 4.3 Get Order Actions

Update any get order actions to include the subCustomer relation:

**Files**: 
- `features/orders/actions/get-order.ts`
- `features/orders/actions/get-orders.ts`
- `features/orders/actions/get-form-data.ts`

Add to the include object:
```typescript
include: {
    // ... existing includes
    customer: true,
    subCustomer: true,  // NEW INCLUDE
    // ... rest of includes
}
```

### 5. Update Order List Display (Optional Enhancement)

**File**: `app/dashboard/orders/orderTableColumns.tsx`

Add subclient display to the customer column:

```typescript
{
    key: "customer",
    label: "Cliente",
    render: (order: any) => (
        <div>
            <div>{order.customer?.name || "-"}</div>
            {order.subCustomer && (
                <div className="text-xs text-gray-500">
                    {order.subCustomer.name}
                </div>
            )}
        </div>
    ),
}
```

## Implementation Tasks

### High Priority
1. [ ] Update Prisma schema with subCustomerId field in Order model
2. [ ] Run `npm run db:generate` and `npm run db:push` to update database
3. [ ] Update OrderFormData type to include subCustomerId
4. [ ] Modify OrderBasicInfoStep to use CustomerSelector component
5. [ ] Update create-order.ts server action to handle subCustomerId
6. [ ] Update update-order.ts server action to handle subCustomerId
7. [ ] Modify order creation page to handle subclient state
8. [ ] Modify order edit page to handle subclient state and loading

### Medium Priority
9. [ ] Remove CustomerSelector from PackingNewWizardV2.tsx
10. [ ] Replace with simple customer select in packing wizard
11. [ ] Remove subCustomerId state and logic from packing wizard
12. [ ] Update get-order actions to include subCustomer relation

### Low Priority
13. [ ] Update order list table to display subclient information
14. [ ] Add subclient filtering to order search/filters
15. [ ] Update order detail views to show subclient

## Testing Checklist

- [ ] Create new order with only customer (no subclient)
- [ ] Create new order with customer and subclient
- [ ] Edit existing order to add subclient
- [ ] Edit existing order to change/remove subclient
- [ ] Verify subclient selection updates when customer changes
- [ ] Create packing and verify no subclient selection available
- [ ] Verify existing packings still work with their subclients
- [ ] Check order list displays subclient information correctly

## Migration Considerations

1. **Existing Orders**: All existing orders will have `subCustomerId = null`
2. **Existing Packings**: Will retain their subclient associations
3. **No Data Loss**: This is an additive change, no existing data is affected

## Dependencies

- CustomerSelector component already exists and is fully functional
- Subclient CRUD system is already implemented
- No new UI components needed
- Minimal changes to existing components

## Estimated Effort

- **Database Changes**: 30 minutes
- **Order Form Updates**: 2-3 hours
- **Server Action Updates**: 1-2 hours
- **Packing Simplification**: 1 hour
- **Testing**: 2 hours
- **Total**: 6-8 hours

## Notes

- The CustomerSelector component already handles the customer/subclient hierarchy logic
- When a customer is selected, it automatically loads and filters available subclients
- The component returns both customerId and subCustomerId values
- Subclients are optional, so orders can still be created with just a customer