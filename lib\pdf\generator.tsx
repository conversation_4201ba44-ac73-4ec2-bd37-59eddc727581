import { pdf } from "@react-pdf/renderer";

import { RemissionPDF } from "@/types/remission/pdf";

import { RemissionTemplate } from "./templates/RemissionTemplate";

export interface GeneratePDFOptions {
    data: RemissionPDF;
    orientation?: "portrait" | "landscape";
    fileName?: string;
}

/**
 * Generate a PDF document from remission data
 * @param options - PDF generation options
 * @returns Blob containing the PDF
 */
export async function generateRemissionPDF(
    options: GeneratePDFOptions,
): Promise<Blob> {
    const { data, orientation = "portrait" } = options;

    const document = (
        <RemissionTemplate data={data} orientation={orientation} />
    );
    const blob = await pdf(document).toBlob();

    return blob;
}

/**
 * Generate and download a PDF document
 * @param options - PDF generation options
 */
export async function downloadRemissionPDF(
    options: GeneratePDFOptions,
): Promise<void> {
    const { fileName = `remision-${options.data.folio}.pdf` } = options;

    const blob = await generateRemissionPDF(options);

    // Create a download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.download = fileName;

    // Trigger download
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

/**
 * Generate a PDF and open in new tab
 * @param options - PDF generation options
 */
export async function previewRemissionPDF(
    options: GeneratePDFOptions,
): Promise<void> {
    const blob = await generateRemissionPDF(options);
    const url = URL.createObjectURL(blob);

    window.open(url, "_blank");

    // Cleanup after a delay
    setTimeout(() => URL.revokeObjectURL(url), 60000);
}
