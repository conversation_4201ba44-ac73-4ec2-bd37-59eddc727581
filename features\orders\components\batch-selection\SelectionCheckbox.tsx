"use client";

import React from "react";
import { Checkbox } from "@heroui/react";

import { cn } from "@/shared/utils";

import { useSelection } from "./SelectionContext";

interface SelectionCheckboxProps {
    id: string;
    className?: string;
}

const SelectionCheckbox: React.FC<SelectionCheckboxProps> = ({
    id,
    className,
}) => {
    const { selectedIds, toggle, selectionMode } = useSelection();
    const isSelected = selectedIds.has(id);

    return (
        <div
            aria-checked={isSelected}
            className={cn(
                "selection-checkbox transition-all duration-150",
                isSelected ? "opacity-100" : "opacity-40 hover:opacity-100",
                selectionMode !== "none" ? "visible" : "opacity-40",
                className,
            )}
            role="checkbox"
            tabIndex={0}
            onClick={(e) => {
                e.stopPropagation();
                toggle(id);
            }}
            onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    toggle(id);
                }
            }}
        >
            <Checkbox
                aria-label={
                    isSelected
                        ? `Deseleccionar orden ${id}`
                        : `Seleccionar orden ${id}`
                }
                className="transition-transform duration-150 ease-out"
                color="primary"
                isSelected={isSelected}
                radius="sm"
                size="sm"
            />
        </div>
    );
};

export default SelectionCheckbox;
