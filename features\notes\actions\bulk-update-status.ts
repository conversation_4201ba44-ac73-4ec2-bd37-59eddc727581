"use server";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { revalidateData } from "@/shared/utils/actions-utils";

type BulkUpdateNoteStatusParams = {
    noteIds: string[];
    statusId: string;
};

export async function bulkUpdateNoteStatus({
    noteIds,
    statusId,
}: BulkUpdateNoteStatusParams) {
    // Verificar sesión de usuario
    const session = await auth();

    if (!session || !session.user) {
        // REMOVED: console.error("bulkUpdateNoteStatus: Usuario no autenticado");

        return {
            success: false,
            error: "No estás autorizado para realizar esta acción",
        };
    }

    // REMOVED: console.log - bulkUpdateNoteStatus: Actualizando estatus para notas

    try {
        // Solo Admin puede editar notas que no le pertenecen
        const userRole = session.user.role;
        const userId = session.user.id;

        // Si no es admin, filtrar solo las notas del usuario
        let notesToUpdate = noteIds;

        if ((userRole as any)?.name !== "ADMIN") {
            // Obtener solo las notas que pertenecen al usuario
            const userNotes = await db.note.findMany({
                where: {
                    id: { in: noteIds },
                    authorId: userId,
                },
                select: { id: true },
            });

            notesToUpdate = userNotes.map((note) => note.id);
            // REMOVED: console.log - bulkUpdateNoteStatus: Usuario no admin, filtrando notas

            if (notesToUpdate.length === 0) {
                return {
                    success: false,
                    error: "No tienes permisos para actualizar estas notas",
                };
            }
        }

        // Obtener las órdenes afectadas antes de actualizar
        const affectedOrders = await db.note.findMany({
            where: {
                id: { in: notesToUpdate },
            },
            select: {
                orderId: true,
            },
            distinct: ["orderId"],
        });

        // Actualizar el estatus de las notas
        const result = await db.note.updateMany({
            where: {
                id: { in: notesToUpdate },
            },
            data: {
                statusId: statusId,
            },
        });

        // REMOVED: console.log(`bulkUpdateNoteStatus: Actualizadas ${result.count} notas`);

        // Revalidar rutas
        revalidateData(["/dashboard/notes"]);

        // Revalidar rutas para cada orden afectada
        for (const { orderId } of affectedOrders) {
            if (orderId) {
                revalidateData([`/dashboard/orders/${orderId}`]);
            }
        }

        return {
            success: true,
            data: {
                updatedCount: result.count,
            },
        };
    } catch (error) {
        // REMOVED: console.error - bulkUpdateNoteStatus: Error al actualizar estatus

        return {
            success: false,
            error: "Ocurrió un error al actualizar el estatus de las notas",
        };
    }
}
