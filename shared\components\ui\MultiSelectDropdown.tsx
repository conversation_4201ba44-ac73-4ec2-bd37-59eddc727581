"use client";

import React, { useState } from "react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";

import {
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    Button,
    Checkbox,
    Badge,
    Divider,
} from "@/shared/components/ui/hero-ui-client";

interface Option {
    value: string;
    label: string;
}

interface MultiSelectDropdownProps {
    options: Option[];
    value: string[];
    onChange: (value: string[]) => void;
    label: string;
    placeholder?: string;
    isLoading?: boolean;
}

export default function MultiSelectDropdown({
    options,
    value = [],
    onChange,
    label,
    placeholder = "Seleccionar...",
    isLoading = false,
}: MultiSelectDropdownProps) {
    const [isOpen, setIsOpen] = useState(false);

    const handleSelectAll = () => {
        if (value.length === options.length) {
            onChange([]);
        } else {
            onChange(options.map((opt) => opt.value));
        }
    };

    const handleToggleOption = (optionValue: string) => {
        const newValue = value.includes(optionValue)
            ? value.filter((v) => v !== optionValue)
            : [...value, optionValue];

        onChange(newValue);
    };

    const getButtonText = () => {
        if (isLoading) return "Cargando...";
        if (value.length === 0) return placeholder;
        if (value.length === 1) {
            const selected = options.find((opt) => opt.value === value[0]);

            return selected?.label || placeholder;
        }

        return `${value.length} seleccionados`;
    };

    return (
        <Dropdown
            isOpen={isOpen}
            placement="bottom-start"
            onOpenChange={setIsOpen}
        >
            <DropdownTrigger>
                <Button
                    className="justify-between"
                    endContent={
                        <div className="flex items-center gap-2">
                            {value.length > 0 && (
                                <Badge
                                    color="primary"
                                    content={value.length}
                                    size="sm"
                                >
                                    {value.length}
                                </Badge>
                            )}
                            <ChevronDownIcon className="w-4 h-4" />
                        </div>
                    }
                    variant="flat"
                >
                    <span className="text-left flex-1 truncate">
                        {label}: {getButtonText()}
                    </span>
                </Button>
            </DropdownTrigger>

            <DropdownMenu
                aria-label={label}
                className="max-h-80 overflow-y-auto"
                closeOnSelect={false}
            >
                <DropdownItem
                    key="select-all"
                    className="sticky top-0 bg-background z-10"
                >
                    <div className="flex items-center gap-2 py-1">
                        <Checkbox
                            isIndeterminate={
                                value.length > 0 &&
                                value.length < options.length
                            }
                            isSelected={value.length === options.length}
                            onValueChange={handleSelectAll}
                        >
                            Seleccionar todos
                        </Checkbox>
                    </div>
                </DropdownItem>

                <DropdownItem key="divider" className="p-0">
                    <Divider />
                </DropdownItem>

                {
                    options.map((option) => (
                        <DropdownItem key={option.value}>
                            <div className="flex items-center gap-2 py-1">
                                <Checkbox
                                    isSelected={value.includes(option.value)}
                                    onValueChange={() =>
                                        handleToggleOption(option.value)
                                    }
                                >
                                    {option.label}
                                </Checkbox>
                            </div>
                        </DropdownItem>
                    )) as any
                }
            </DropdownMenu>
        </Dropdown>
    );
}
