import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
} from "@heroui/react";

// import { cn } from "@/shared/utils/cn"; // Comment out - module doesn't exist
const cn = (...classes: any[]) => classes.filter(Boolean).join(" "); // Simple fallback

export interface ImprovedModalProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
    size?:
        | "xs"
        | "sm"
        | "md"
        | "lg"
        | "xl"
        | "2xl"
        | "3xl"
        | "4xl"
        | "5xl"
        | "full";
    backdrop?: "blur" | "opaque" | "transparent";
    children: React.ReactNode;
    footer?: React.ReactNode;
    hideCloseButton?: boolean;
    classNames?: {
        wrapper?: string;
        backdrop?: string;
        base?: string;
        header?: string;
        body?: string;
        footer?: string;
    };
}

/**
 * Modal mejorado con HeroUI que incluye backdrop oscuro con blur
 * y mejor manej<PERSON> de z-index
 */
export const ImprovedModal: React.FC<ImprovedModalProps> = ({
    isOpen,
    onClose,
    title,
    size = "lg",
    backdrop = "blur",
    children,
    footer,
    hideCloseButton = false,
    classNames = {},
}) => {
    return (
        <Modal
            backdrop={backdrop}
            classNames={{
                wrapper: cn("z-[var(--z-modal-backdrop)]", classNames.wrapper),
                backdrop: cn(
                    "bg-black/50 backdrop-blur-sm",
                    "z-[var(--z-backdrop)]",
                    classNames.backdrop,
                ),
                base: cn(
                    "z-[var(--z-modal)]",
                    "bg-white dark:bg-gray-900",
                    "border border-gray-200 dark:border-gray-800",
                    "shadow-xl",
                    classNames.base,
                ),
                header: cn(
                    "border-b border-gray-200 dark:border-gray-700",
                    "text-high-contrast",
                    classNames.header,
                ),
                body: cn("py-4", "text-medium-contrast", classNames.body),
                footer: cn(
                    "border-t border-gray-200 dark:border-gray-700",
                    classNames.footer,
                ),
            }}
            hideCloseButton={hideCloseButton}
            isOpen={isOpen}
            scrollBehavior="inside"
            size={size}
            onClose={onClose}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        {title && (
                            <ModalHeader className="flex flex-col gap-1">
                                {title}
                            </ModalHeader>
                        )}
                        <ModalBody>{children}</ModalBody>
                        {footer && <ModalFooter>{footer}</ModalFooter>}
                    </>
                )}
            </ModalContent>
        </Modal>
    );
};

export default ImprovedModal;
