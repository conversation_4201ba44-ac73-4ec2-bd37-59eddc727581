"use client";

import React from "react";
import { motion } from "framer-motion";
import {
    CheckCircleIcon,
    ClockIcon,
    TruckIcon,
} from "@heroicons/react/24/outline";
import clsx from "clsx";

interface CompactProgressBarProps {
    progress: number;
    status?: string;
}

export default function CompactProgressBar({
    progress = 0,
    status,
}: CompactProgressBarProps) {
    // Color based on progress or status
    const getProgressColor = () => {
        if (status === "completed" || progress >= 100)
            return "bg-gradient-to-r from-green-400 to-green-500";
        if (progress >= 80)
            return "bg-gradient-to-r from-blue-400 to-green-400";
        if (progress >= 50)
            return "bg-gradient-to-r from-amber-400 to-blue-400";

        return "bg-gradient-to-r from-blue-400 to-blue-500";
    };

    const getStatusIcon = () => {
        if (status === "completed" || progress >= 100) {
            return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
        }
        if (progress >= 50) {
            return <TruckIcon className="w-5 h-5 text-blue-500" />;
        }

        return <ClockIcon className="w-5 h-5 text-gray-500" />;
    };

    const getStatusText = () => {
        if (status === "completed" || progress >= 100) return "Completado";
        if (progress >= 80) return "Casi listo";
        if (progress >= 50) return "En proceso";
        if (progress > 0) return "Iniciado";

        return "Pendiente";
    };

    return (
        <div className="w-full space-y-2">
            {/* Header with icon and status */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {getStatusText()}
                    </span>
                </div>
                <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
                    {Math.round(progress)}%
                </span>
            </div>

            {/* Progress bar */}
            <div className="relative w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                    animate={{ width: `${progress}%` }}
                    className={clsx(
                        "absolute inset-y-0 left-0 rounded-full",
                        getProgressColor(),
                        "shadow-sm",
                    )}
                    initial={{ width: 0 }}
                    transition={{
                        type: "spring",
                        stiffness: 100,
                        damping: 20,
                        duration: 1,
                    }}
                />

                {/* Shimmer effect */}
                {progress > 0 && progress < 100 && (
                    <motion.div
                        animate={{
                            x: ["0%", "300%"],
                        }}
                        className="absolute inset-y-0 w-1/3 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                        style={{
                            transform: "skewX(-20deg)",
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "linear",
                        }}
                    />
                )}
            </div>

            {/* Milestone indicators */}
            <div className="relative flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>0%</span>
                <span>25%</span>
                <span>50%</span>
                <span>75%</span>
                <span>100%</span>
            </div>
        </div>
    );
}
