"use server";

// Importar sistema adaptativo de prisma-client
import type { OptimisticValidationResponse } from "../types/types";

import { prisma } from "@/shared/lib/prisma";

// Re-exportar otras funciones
import {
    getOrderDetailsForAssignment,
    checkGarmentSizeAvailability,
    getAvailableOrders,
} from "./query";
import { createAssignments, validateAssignmentAvailability } from "./create-v2";
import { revalidateAssignmentPaths } from "./revalidate";
import { getAssignmentsForRemission } from "./getAssignmentsForRemission";

// Importar las funciones de validación (no re-exportarlas directamente)
import {
    validateOptimistic as _validateOptimistic,
    validateAndReconcile as _validateAndReconcile,
    getQuantitySuggestions as _getQuantitySuggestions,
} from "./validation";

// Re-exportar funciones sin conflicto
export {
    getOrderDetailsForAssignment,
    checkGarmentSizeAvailability,
    getAvailableOrders,
};
export { createAssignments, validateAssignmentAvailability };
export { revalidateAssignmentPaths };
export { getAssignmentsForRemission };
// Importar y re-exportar getAssignments correctamente para "use server"
import { getAssignments as _getAssignments } from "./getAssignments";
export const getAssignments = _getAssignments;

// Re-exportar funciones de validación como funciones async
export async function validateOptimistic(
    garmentSizeId: string,
    quantity: number,
): Promise<OptimisticValidationResponse> {
    return _validateOptimistic(garmentSizeId, quantity);
}

export async function validateAndReconcile(
    garmentSizeId: string,
    quantity: number,
    versionToken?: string,
): Promise<OptimisticValidationResponse> {
    return _validateAndReconcile(garmentSizeId, quantity, versionToken);
}

export async function getQuantitySuggestions(
    garmentSizeId: string,
    contractorId: string,
): Promise<{
    suggestedQuantity: number;
    maxQuantity: number;
    recentAssignments: number[];
}> {
    return _getQuantitySuggestions(garmentSizeId, contractorId);
}

// Implementación simplificada directa con Prisma
export async function getContractorsForSelector() {
    try {
        const contractors = await prisma.contractor.findMany({
            orderBy: { name: "asc" },
            select: {
                id: true,
                name: true,
                firstName: true,
                lastName: true,
            },
        });

        return {
            contractors,
        };
    } catch (error) {
        // REMOVED: console.error("Error al obtener contratistas:", error);
        throw new Error("No se pudieron cargar los contratistas disponibles");
    }
}

export async function getOrdersForSelector() {
    try {
        const orders = await prisma.order.findMany({
            orderBy: { receivedDate: "desc" },
            select: {
                id: true,
                transferNumber: true,
                cutOrder: true,
                batch: true,
                receivedDate: true,
                estimatedDeliveryDate: true,
                status: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        iconName: true,
                    },
                },
                customer: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                garments: {
                    select: {
                        id: true,
                        model: {
                            select: {
                                id: true,
                                code: true,
                                description: true,
                            },
                        },
                        color: {
                            select: {
                                id: true,
                                name: true,
                                hexCode: true,
                            },
                        },
                        sizes: {
                            select: {
                                id: true,
                                totalQuantity: true,
                                usedQuantity: true,
                                size: {
                                    select: {
                                        id: true,
                                        code: true,
                                    },
                                },
                            },
                        },
                    },
                },
                parts: {
                    select: {
                        id: true,
                        code: true,
                    },
                },
                _count: {
                    select: {
                        garments: true,
                    },
                },
            },
        });

        return {
            orders,
        };
    } catch (error) {
        // REMOVED: console.error("Error al obtener órdenes:", error);
        throw new Error("No se pudieron cargar las órdenes disponibles");
    }
}

// Re-exportar tipos necesarios
export type { OrdersQueryFilters, QuantityAssignment } from "../schemas/schema";
