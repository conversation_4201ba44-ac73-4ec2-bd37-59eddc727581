import { Prisma } from "@prisma/client";

import {
    prisma,
    executeWithRetry,
    checkConnection,
    queryMetrics,
} from "./prisma";

// Re-exportar prisma para mantener compatibilidad
export { prisma as db };

// Re-exportar funciones de utilidad
export {
    checkConnection,
    clearPreparedStatements,
    executeWithRetry,
    queryMetrics,
} from "./prisma";

// Tipos para respuestas consistentes
export interface DbOperationResult<T> {
    success: boolean;
    data?: T;
    error?: string;
    details?: unknown;
}

// Tipo para opciones de transacción
export interface TransactionOptions {
    maxWait?: number;
    timeout?: number;
    isolationLevel?: Prisma.TransactionIsolationLevel;
}

/**
 * Códigos de error de Prisma comunes
 */
export const PrismaErrorCodes = {
    UNIQUE_CONSTRAINT: "P2002",
    FOREIGN_KEY_CONSTRAINT: "P2003",
    RECORD_NOT_FOUND: "P2025",
    CONNECTION_ERROR: "P1017",
    POOL_TIMEOUT: "P2024",
    DATABASE_UNREACHABLE: "P1001",
    QUERY_TIMEOUT: "P2024",
    INVALID_DATA: "P2023",
} as const;

/**
 * Formatea mensajes de error de manera consistente y amigable
 */
function formatErrorMessage(error: unknown, defaultMessage: string): string {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
            case PrismaErrorCodes.UNIQUE_CONSTRAINT: {
                const field = error.meta?.target as string[] | undefined;

                return field
                    ? `El campo ${field.join(", ")} ya existe`
                    : "El registro ya existe";
            }
            case PrismaErrorCodes.RECORD_NOT_FOUND:
                return "Registro no encontrado";

            case PrismaErrorCodes.FOREIGN_KEY_CONSTRAINT:
                return "Error de referencia: verifique las relaciones";

            case PrismaErrorCodes.CONNECTION_ERROR:
            case PrismaErrorCodes.DATABASE_UNREACHABLE:
                return "Error de conexión con la base de datos";

            case PrismaErrorCodes.POOL_TIMEOUT:
            case PrismaErrorCodes.QUERY_TIMEOUT:
                return "La operación tardó demasiado tiempo";

            case PrismaErrorCodes.INVALID_DATA:
                return "Los datos proporcionados no son válidos";

            default:
                return error.message || defaultMessage;
        }
    }

    if (error instanceof Prisma.PrismaClientValidationError) {
        return "Error de validación: verifique los datos enviados";
    }

    if (error instanceof Error) {
        return error.message;
    }

    return defaultMessage;
}

/**
 * Maneja errores de base de datos de manera consistente
 * Compatible con Server Actions de Next.js 15
 */
export async function handleDbError<T>(
    fnOrError: (() => Promise<T>) | unknown,
    errorMessage: string = "Error en la operación de base de datos",
    maxRetries: number = 2,
): Promise<DbOperationResult<T>> {
    // Si es una función, ejecutarla con manejo de errores
    if (typeof fnOrError === "function") {
        try {
            // Usar executeWithRetry para operaciones con reintentos automáticos
            const data = await executeWithRetry<T>(
                fnOrError as () => Promise<T>,
                { maxRetries },
            );

            return { success: true, data };
        } catch (error) {
            // eslint-disable-next-line no-console
            // REMOVED: console.error(`[DB Error] ${errorMessage}:`, error);

            return {
                success: false,
                error: formatErrorMessage(error, errorMessage),
                details:
                    process.env.NODE_ENV === "development" ? error : undefined,
            };
        }
    }
    // Si es un error directo, manejarlo
    else {
        // eslint-disable-next-line no-console
        // REMOVED: console.error(`[DB Error] ${errorMessage}:`, fnOrError);

        return {
            success: false,
            error: formatErrorMessage(fnOrError, errorMessage),
            details:
                process.env.NODE_ENV === "development" ? fnOrError : undefined,
        };
    }
}

/**
 * Ejecuta una operación en una transacción con opciones avanzadas
 * Siguiendo las mejores prácticas de Next.js 15
 */
export async function withTransaction<T>(
    operation: (tx: Prisma.TransactionClient) => Promise<T>,
    options?: TransactionOptions,
): Promise<DbOperationResult<T>> {
    try {
        const data = await prisma.$transaction(operation, {
            maxWait: options?.maxWait ?? 2000,
            timeout: options?.timeout ?? 5000,
            isolationLevel:
                options?.isolationLevel ??
                Prisma.TransactionIsolationLevel.ReadCommitted,
        });

        return { success: true, data };
    } catch (error) {
        // eslint-disable-next-line no-console
        // REMOVED: console.error("[Transaction Error]:", error);

        return {
            success: false,
            error: formatErrorMessage(error, "Error en la transacción"),
            details: process.env.NODE_ENV === "development" ? error : undefined,
        };
    }
}

/**
 * Wrapper para operaciones de base de datos con logging y métricas
 * Optimizado para Edge Runtime de Next.js 15
 */
export async function dbOperation<T>(
    name: string,
    operation: () => Promise<T>,
    options?: {
        logResult?: boolean;
        warnThreshold?: number;
    },
): Promise<T> {
    const startTime = Date.now();
    const { logResult = false, warnThreshold = 1000 } = options ?? {};

    try {
        if (process.env.NODE_ENV === "development") {
            // eslint-disable-next-line no-console
            // REMOVED: console.log(`[DB Operation] Iniciando: ${name}`);
        }

        const result = await operation();
        const duration = Date.now() - startTime;

        if (duration > warnThreshold) {
            // eslint-disable-next-line no-console
            // Operación lenta detectada - comentario removido
        }

        if (logResult && process.env.NODE_ENV === "development") {
            // eslint-disable-next-line no-console
            // REMOVED: console.log(`[DB Operation] Completado ${name} en ${duration}ms`);
        }

        return result;
    } catch (error) {
        const duration = Date.now() - startTime;

        // eslint-disable-next-line no-console
        // Error en operación DB - comentario removido
        throw error;
    }
}

/**
 * Ejecuta múltiples operaciones en paralelo con manejo de errores individual
 * Útil para operaciones independientes que pueden fallar parcialmente
 */
export async function batchOperations<
    T extends Record<string, () => Promise<unknown>>,
>(
    operations: T,
): Promise<{ [K in keyof T]: DbOperationResult<Awaited<ReturnType<T[K]>>> }> {
    const entries = Object.entries(operations) as Array<[keyof T, T[keyof T]]>;

    const results = await Promise.all(
        entries.map(async ([key, operation]) => {
            const result = await handleDbError(
                operation,
                `Error en operación ${String(key)}`,
            );

            return [key, result] as const;
        }),
    );

    return Object.fromEntries(results) as {
        [K in keyof T]: DbOperationResult<Awaited<ReturnType<T[K]>>>;
    };
}

/**
 * Helper para crear consultas con paginación
 * Siguiendo las mejores prácticas de Prisma
 */
export interface PaginationOptions {
    page?: number;
    pageSize?: number;
    cursor?: string;
    orderBy?: Record<string, "asc" | "desc">;
}

export interface PaginatedResult<T> {
    data: T[];
    pagination: {
        page: number;
        pageSize: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

/**
 * Aplica paginación a una consulta
 */
export async function paginate<T>(
    model: {
        findMany: (args?: unknown) => Promise<T[]>;
        count: (args?: unknown) => Promise<number>;
    },
    options: PaginationOptions & {
        where?: unknown;
        include?: unknown;
        select?: unknown;
    } = {},
): Promise<PaginatedResult<T>> {
    const {
        page = 1,
        pageSize = 10,
        cursor,
        orderBy = { createdAt: "desc" },
        where,
        include,
        select,
    } = options;

    // Calcular skip para paginación por offset
    const skip = cursor ? undefined : (page - 1) * pageSize;

    // Ejecutar consultas en paralelo
    const [data, total] = await Promise.all([
        model.findMany({
            where,
            include,
            select,
            orderBy,
            take: pageSize,
            skip,
            cursor: cursor ? { id: cursor } : undefined,
        }),
        model.count({ where }),
    ]);

    const totalPages = Math.ceil(total / pageSize);

    return {
        data,
        pagination: {
            page,
            pageSize,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
}

/**
 * Verifica la salud de la conexión a la base de datos
 * Útil para health checks en Next.js
 */
export async function healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    latency: number;
    metrics?: {
        activeQueries: number;
        totalQueries: number;
    };
    error?: string;
}> {
    const startTime = Date.now();

    try {
        const isConnected = await checkConnection();
        const latency = Date.now() - startTime;

        if (isConnected) {
            return {
                status: "healthy",
                latency,
                metrics: {
                    activeQueries: queryMetrics.getActiveQueries(),
                    totalQueries: queryMetrics.getTotalQueries(),
                },
            };
        }

        return {
            status: "unhealthy",
            latency,
            error: "No se pudo conectar a la base de datos",
        };
    } catch (error) {
        return {
            status: "unhealthy",
            latency: Date.now() - startTime,
            error: formatErrorMessage(
                error,
                "Error al verificar la salud de la base de datos",
            ),
        };
    }
}

/**
 * Tipo helper para extraer el tipo de retorno de una operación de Prisma
 */
export type ExtractDbResult<T> =
    T extends DbOperationResult<infer U> ? U : never;

/**
 * Helper para manejar operaciones de upsert de manera segura
 */
export async function safeUpsert<T>(
    model: {
        upsert: (args: {
            where: unknown;
            create: unknown;
            update: unknown;
        }) => Promise<T>;
    },
    {
        where,
        create,
        update,
    }: {
        where: unknown;
        create: unknown;
        update: unknown;
    },
): Promise<DbOperationResult<T>> {
    return handleDbError(async () => {
        return await model.upsert({
            where,
            create,
            update,
        });
    }, "Error al crear o actualizar registro");
}
