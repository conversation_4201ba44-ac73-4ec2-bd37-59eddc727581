/**
 * Computed Data Cache Strategy
 * For expensive calculations and aggregations
 */

import { CacheType } from "../cache-types";

import { BaseCacheStrategy } from "./base-strategy";

export class ComputedCacheStrategy extends BaseCacheStrategy {
    constructor() {
        super(CacheType.COMPUTED);
    }

    /**
     * Get or compute data
     */
    async getOrCompute<T>(
        resource: string,
        identifier: string,
        computeFn: () => Promise<T>,
        ttl?: number,
    ): Promise<T> {
        // Try to get from cache first
        const cached = await this.get<T>({
            resource,
            identifier,
        });

        if (cached !== null) {
            return cached;
        }

        // Compute if not in cache
        const computed = await computeFn();

        // Store in cache
        await this.set({ resource, identifier }, computed, ttl);

        return computed;
    }

    /**
     * Store aggregated data with custom TTL
     */
    async setAggregation<T>(
        resource: string,
        identifier: string,
        data: T,
        ttl: number = 3600, // 1 hour default
    ): Promise<boolean> {
        return this.set({ resource, identifier }, data, ttl);
    }

    /**
     * Invalidate all computed data for a resource
     */
    async invalidateResource(resource: string): Promise<void> {
        const client = await import("../redis-client").then((m) =>
            m.getRedisClient(),
        );

        if (!client) return;

        // Find all keys for this resource
        const pattern = `*:${resource}:*`;
        const keys = await client.keys(pattern);

        if (keys.length > 0) {
            await client.del(...keys);
        }
    }
}

// Singleton instance
export const computedCache = new ComputedCacheStrategy();
