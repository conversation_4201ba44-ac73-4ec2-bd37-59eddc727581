"use client";

import React from "react";
import {
    CheckCircleIcon,
    XCircleIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

export interface RemissionNotificationProps {
    type: "success" | "error" | "info";
    title: string;
    message?: string;
    visible: boolean;
    onClose?: () => void;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}

export function RemissionNotification({
    type,
    title,
    message,
    visible,
    onClose,
    duration = 5000,
    action,
}: RemissionNotificationProps) {
    React.useEffect(() => {
        if (visible && duration > 0) {
            const timer = setTimeout(() => {
                onClose?.();
            }, duration);

            return () => clearTimeout(timer);
        }
    }, [visible, duration, onClose]);

    const icons = {
        success: <CheckCircleIcon className="h-6 w-6 text-green-400" />,
        error: <XCircleIcon className="h-6 w-6 text-red-400" />,
        info: <InformationCircleIcon className="h-6 w-6 text-blue-400" />,
    };

    const colors = {
        success: "bg-green-50 border-green-200",
        error: "bg-red-50 border-red-200",
        info: "bg-blue-50 border-blue-200",
    };

    return (
        <AnimatePresence>
            {visible && (
                <motion.div
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    className="fixed top-4 right-4 z-50"
                    exit={{
                        opacity: 0,
                        scale: 0.5,
                        transition: { duration: 0.2 },
                    }}
                    initial={{ opacity: 0, y: -50, scale: 0.3 }}
                >
                    <div
                        className={`max-w-md rounded-lg border p-4 shadow-lg ${colors[type]}`}
                    >
                        <div className="flex items-start">
                            <div className="flex-shrink-0">{icons[type]}</div>
                            <div className="ml-3 flex-1">
                                <h3 className="text-sm font-medium text-gray-900">
                                    {title}
                                </h3>
                                {message && (
                                    <p className="mt-1 text-sm text-gray-500">
                                        {message}
                                    </p>
                                )}
                                {action && (
                                    <div className="mt-3">
                                        <button
                                            className="text-sm font-medium text-blue-600 hover:text-blue-500"
                                            type="button"
                                            onClick={action.onClick}
                                        >
                                            {action.label}
                                        </button>
                                    </div>
                                )}
                            </div>
                            {onClose && (
                                <div className="ml-auto flex-shrink-0">
                                    <button
                                        className="inline-flex rounded-md p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                                        type="button"
                                        onClick={onClose}
                                    >
                                        <span className="sr-only">Cerrar</span>
                                        <XCircleIcon className="h-5 w-5" />
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
