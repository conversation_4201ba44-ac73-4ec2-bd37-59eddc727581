# 📋 Assignments Feature

## 📋 Descripción
Sistema de asignación de órdenes a contratistas, seguimiento de progreso y fechas de entrega.

## 🏗️ Componentes

### AssignmentBoard
Tablero kanban de asignaciones.

### AssignmentForm
Formulario de asignación.

### AssignmentTimeline
Timeline de progreso.

## 🪝 Hooks

### useAssignments
Hook principal de asignaciones.
```tsx
const { assignments, assignOrder, updateProgress } = useAssignments()
```

### useAssignmentStatus
Estado y progreso de asignaciones.
```tsx
const { status, progress } = useAssignmentStatus(assignmentId)
```

## 🎯 Uso

```tsx
import { AssignmentBoard, useAssignments } from '@/features/assignments'

export default function AssignmentsPage() {
  const { assignments } = useAssignments()
  return <AssignmentBoard data={assignments} />
}
```