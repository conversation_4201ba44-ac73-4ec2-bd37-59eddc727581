/* Import Design System Tokens - DEBE ir antes de utilities */
@import '../shared/styles/design-tokens.css';
@import '../shared/styles/icon-system.css';
@import './design-system.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sistema de Diseño - Variables CSS */
:root {
  /* Usar tokens del design system */
  --text-primary: var(--color-gray-12);      /* Máximo contraste */
  --text-secondary: var(--color-gray-11);    /* Alto contraste */
  --text-tertiary: var(--color-gray-9);      /* Medio contraste */
  
  /* Helpers de Componentes */
  --card-padding: var(--space-5);
  --modal-padding: var(--space-6);
  --table-row-padding: var(--space-3) var(--space-4);
  --input-padding: var(--space-3) var(--space-4);
  
  /* Radios consistentes */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  
  /* Sombras consistentes */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* Sistema de Z-Index para Layout */
  --z-base: 0;
  --z-header: 10;
  --z-sidebar: 10;
  --z-mobile-button: 20;
  --z-backdrop: 25;
  --z-sidebar-mobile: 30;
  --z-modal: 50;
  --z-tooltip: 60;
}

/* Dark mode overrides */
[data-theme="dark"] {
  --text-primary: #F9FAFB;      /* gray-50 */
  --text-secondary: #9CA3AF;    /* gray-400 */
  --text-tertiary: #6B7280;     /* gray-500 */
}

/* Animaciones personalizadas */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

/* Clases de utilidad para el sistema de diseño */
.animate-shimmer {
  animation: shimmer 2s infinite;
}
.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

/* Helpers de espaciado para componentes */
.p-card { padding: var(--card-padding); }
.p-modal { padding: var(--modal-padding); }
.p-section { padding: var(--space-8) var(--space-5); }

/* Gap helpers */
.gap-inline { gap: var(--space-2); }
.gap-stack { gap: var(--space-4); }
.gap-section { gap: var(--space-8); }

/* Z-index utilities para el layout */
.z-header { z-index: var(--z-header); }
.z-sidebar { z-index: var(--z-sidebar); }
.z-sidebar-mobile { z-index: var(--z-sidebar-mobile); }
.z-backdrop { z-index: var(--z-backdrop); }
.z-mobile-button { z-index: var(--z-mobile-button); }

/* Mejoras para tablas */
.table-secondary-text {
  color: var(--text-secondary);
}

.table-row-hover:hover {
  background-color: rgba(0, 0, 0, 0.02);
  transition: background-color 0.15s ease;
}

[data-theme="dark"] .table-row-hover:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

/* Estilos para impresión de documentos */
@media print {
  html, body {
    width: 100% !important;
    height: auto !important;
    background: white !important;
    font-size: 12pt !important;
    color: black !important;
    margin: 0 !important;
    padding: 0 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    overflow: visible !important;
  }
  
  /* Ocultar elementos que no deben imprimirse */
  .print\:hidden {
    display: none !important;
  }
  
  /* Nueva clase más sencilla para ocultar elementos */
  .print-hidden {
    display: none !important;
  }
  
  /* Quitar márgenes, bordes y sombras de todos los elementos padre */
  .print\:border-0 {
    border: none !important;
  }
  
  .print\:shadow-none {
    box-shadow: none !important;
  }
  
  .print\:p-0 {
    padding: 0 !important;
  }
  
  /* Estilos específicos para el documento de remisión */
  .remission-document {
    display: block !important;
    width: 100% !important;
    height: auto !important;
    max-width: none !important;
    padding: 10mm !important;
    margin: 0 auto !important;
    box-shadow: none !important;
    border: none !important;
    border-radius: 0 !important;
    font-size: 12pt !important;
    page-break-inside: avoid !important;
    background-color: white !important;
    overflow: visible !important;
  }
  
  /* Asegurar que las tablas se impriman correctamente */
  .remission-document table {
    width: 100% !important;
    page-break-inside: auto !important;
    border-collapse: collapse !important;
    display: table !important;
  }
  
  .remission-document thead {
    display: table-header-group !important;
  }
  
  .remission-document tbody {
    display: table-row-group !important;
  }
  
  .remission-document tr {
    page-break-inside: avoid !important;
    page-break-after: auto !important;
    display: table-row !important;
  }
  
  .remission-document th,
  .remission-document td {
    padding: 2mm !important;
    display: table-cell !important;
  }
  
  /* Evitar cortes en secciones importantes */
  .remission-document h1,
  .remission-document h2,
  .remission-document h3 {
    page-break-after: avoid !important;
  }
  
  /* Forzar los saltos de página */
  .page-break-after {
    page-break-after: always !important;
  }
  
  .page-break-before {
    page-break-before: always !important;
  }
  
  /* Asegurar que el contenido del iframe de impresión sea visible */
  #print-iframe {
    height: auto !important;
    width: 100% !important;
    display: block !important;
    position: static !important;
    overflow: visible !important;
  }
  
  /* Fix para react-to-print */
  iframe[style*="position: absolute"] {
    height: auto !important;
    width: 100% !important;
    display: block !important;
    position: static !important;
    overflow: visible !important;
  }
  
  /* Arreglo para problemas específicos de Next.js */
  #__next {
    display: block !important;
    overflow: visible !important;
    height: auto !important;
  }
  
  /* Estilos para Card component */
  .print-content {
    display: block !important;
    height: auto !important;
    overflow: visible !important;
  }
}

/* Estilos específicos para PDF */
.pdf-generating {
  font-size: 12pt !important;
  padding: 10mm !important;
  background-color: white !important;
  max-width: 210mm !important;
  width: 210mm !important;
  margin: 0 auto !important;
  box-shadow: none !important;
  border: none !important;
  border-radius: 0 !important;
}

.pdf-generating table {
  width: 100% !important;
  border-collapse: collapse !important;
}

.pdf-generating th,
.pdf-generating td {
  padding: 2mm !important;
}

/* Estilos generales para el documento de remisión */
.remission-document {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.remission-document table {
  border-collapse: collapse;
}

.remission-document th,
.remission-document td {
  padding: 0.5rem;
}

.remission-document th {
  text-align: left;
}

/* Estilo para solucionar problemas de impresión */
.print-ready {
  display: block !important;
  height: auto !important;
  overflow: visible !important;
  background-color: white !important;
}

/* Resto de estilos globales... */
.order-dashboard-header {
  min-width: max-content;
  white-space: nowrap;
}

/* Fix para títulos cortados */
.dashboard-title {
  min-width: max-content;
  white-space: nowrap;
}

/* Fix adicional para títulos cortados en el dashboard */
main h1, 
main h2, 
main h3 {
  min-width: 0;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Asegurar que el título principal sea visible */
main > div:first-child h1,
main > div:first-child h2 {
  display: block;
  width: 100%;
}

/* Custom Scrollbar para la sección de notas */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  /* Padding para evitar que el contenido se pegue al borde */
  scrollbar-gutter: stable;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
  margin: 8px 0;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: padding-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* Dark mode scrollbar */
[data-theme="dark"] .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

[data-theme="dark"] .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.8);
}

/* Estilo para notas con hover mejorado */
.note-hover-container {
  position: relative;
  z-index: 1;
  transition: z-index 0.3s ease;
}

.note-hover-container:hover {
  z-index: 10;
}

/* Container para permitir overflow visible en hover */
.notes-scroll-container {
  position: relative;
  overflow-y: auto;
  overflow-x: visible;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Padding adicional para acomodar sombras */
.notes-content-wrapper {
  padding: 1rem 0.5rem;
}