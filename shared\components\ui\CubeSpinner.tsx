"use client";

import React from "react";

interface CubeSpinnerProps {
    className?: string;
    text?: string;
}

const CubeSpinner = ({ className = "", text }: CubeSpinnerProps) => {
    return (
        <div
            className={`flex flex-col items-center justify-center gap-4 ${className}`}
        >
            <div className="spinner">
                <div />
                <div />
                <div />
                <div />
                <div />
                <div />
            </div>
            {text && (
                <p className="font-medium text-gray-600 dark:text-gray-300 text-sm mt-2">
                    {text}
                </p>
            )}

            <style jsx>{`
                .spinner {
                    width: 44.8px;
                    height: 44.8px;
                    animation: spinner-y0fdc1 2s infinite ease;
                    transform-style: preserve-3d;
                }

                .spinner > div {
                    background-color: rgba(184, 71, 255, 0.2);
                    height: 100%;
                    position: absolute;
                    width: 100%;
                    border: 2.2px solid #b847ff;
                }

                .spinner div:nth-of-type(1) {
                    transform: translateZ(-22.4px) rotateY(180deg);
                }

                .spinner div:nth-of-type(2) {
                    transform: rotateY(-270deg) translateX(50%);
                    transform-origin: top right;
                }

                .spinner div:nth-of-type(3) {
                    transform: rotateY(270deg) translateX(-50%);
                    transform-origin: center left;
                }

                .spinner div:nth-of-type(4) {
                    transform: rotateX(90deg) translateY(-50%);
                    transform-origin: top center;
                }

                .spinner div:nth-of-type(5) {
                    transform: rotateX(-90deg) translateY(50%);
                    transform-origin: bottom center;
                }

                .spinner div:nth-of-type(6) {
                    transform: translateZ(22.4px);
                }

                @keyframes spinner-y0fdc1 {
                    0% {
                        transform: rotate(45deg) rotateX(-25deg) rotateY(25deg);
                    }

                    50% {
                        transform: rotate(45deg) rotateX(-385deg) rotateY(25deg);
                    }

                    100% {
                        transform: rotate(45deg) rotateX(-385deg)
                            rotateY(385deg);
                    }
                }
            `}</style>
        </div>
    );
};

export default CubeSpinner;
