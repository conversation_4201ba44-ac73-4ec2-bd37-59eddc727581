import React from "react";
import { View, Text } from "@react-pdf/renderer";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { remissionStyles as styles } from "../styles/remission";

interface PDFHeaderProps {
    folio: string;
    date: string;
}

export const PDFHeader: React.FC<PDFHeaderProps> = ({ folio, date }) => {
    return (
        <View style={styles.header}>
            {/* Logo placeholder - you can replace with actual logo */}
            <View style={styles.logo}>
                <Text style={[styles.title, { fontSize: 24 }]}>LOHARI</Text>
            </View>

            <View style={styles.headerCenter}>
                <Text style={styles.title}>REMISIÓN DE TRABAJO</Text>
                <Text style={styles.subtitle}>Tu estilo, nuestra pasión</Text>
            </View>

            <View style={styles.headerRight}>
                <Text style={styles.folio}>#{folio}</Text>
                <Text style={styles.date}>
                    {format(new Date(date), "d 'de' MMMM 'de' yyyy", {
                        locale: es,
                    })}
                </Text>
                <Text style={styles.date}>
                    {format(new Date(date), "HH:mm 'hrs'", { locale: es })}
                </Text>
            </View>
        </View>
    );
};
