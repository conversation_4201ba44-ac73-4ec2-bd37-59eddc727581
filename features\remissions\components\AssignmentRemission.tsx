"use client";

import { useState, useRef } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ModalBody,
    ModalFooter,
    useDisclosure,
} from "@heroui/react";
import {
    DocumentDuplicateIcon,
    CheckIcon,
    PrinterIcon,
    InformationCircleIcon,
    ArrowsPointingOutIcon,
    EyeIcon,
} from "@heroicons/react/24/outline";
import { Switch } from "@heroui/react";

import { generatePDF } from "@/shared/services/pdf-generation";
import { Assignment } from "@/features/assignments/components/wizard/components/AssignmentTable";

import { createRemission, markRemissionPrinted } from "../actions";

import { RemissionDocument } from "./RemissionDocument";

interface Props {
    contractorId: string;
    assignments: Assignment[];
    orderId: string;
    orderData?: {
        cutOrder?: string;
        parts?: Array<{
            id: string;
            code: string;
        }>;
    };
    existingRemission?: {
        id: string;
        folio: string;
    };
    onSuccess?: () => void;
}

export default function AssignmentRemission({
    contractorId,
    assignments,
    orderId,
    orderData,
    existingRemission,
    onSuccess,
}: Props) {
    const remissionRef = useRef<HTMLDivElement>(null);
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [remissionId, setRemissionId] = useState<string | null>(
        existingRemission?.id || null,
    );
    const [remissionFolio, setRemissionFolio] = useState<string | null>(
        existingRemission?.folio || null,
    );
    const [notes, setNotes] = useState<string>("");
    const [hasCreatedRemission, setHasCreatedRemission] =
        useState(!!existingRemission);

    // Opciones de exportación
    const [useLandscape, setUseLandscape] = useState<boolean>(true);
    const [useTwoCopies, setUseTwoCopies] = useState<boolean>(true);

    // Modal de previsualización
    const { isOpen, onOpen, onOpenChange } = useDisclosure();

    const handlePreview = async () => {
        setLoading(true);
        setError(null);

        try {
            // Primero intentamos crear la remisión en la base de datos
            if (!remissionId && !hasCreatedRemission) {
                setHasCreatedRemission(true);
                // Detectar si las asignaciones tienen estructura anidada (de BD) o plana (del wizard)
                const items = assignments.map((a: any) => {
                    // Si tiene garmentSize, es una asignación de la BD con estructura anidada
                    if (a.garmentSize) {
                        return {
                            modelCode: a.garmentSize.garment.model.code,
                            colorName: a.garmentSize.garment.color.name,
                            sizeCode: a.garmentSize.size.code,
                            quantity: a.quantity,
                        };
                    }

                    // Si no, es una asignación del wizard con estructura plana
                    return {
                        modelCode: a.modelCode,
                        colorName: a.colorName,
                        sizeCode: a.sizeCode,
                        quantity: a.quantity,
                    };
                });

                const orderDetails = {
                    id: orderId,
                    // Incluir cutOrder si está disponible
                    ...(orderData?.cutOrder
                        ? {
                              cutOrder: orderData.cutOrder,
                          }
                        : assignments.length > 0 && assignments[0].cutOrder
                          ? {
                                cutOrder: assignments[0].cutOrder,
                            }
                          : {}),
                    // Incluir partes de orden si están disponibles
                    ...(orderData?.parts
                        ? {
                              parts: orderData.parts,
                          }
                        : assignments.length > 0 && assignments[0].parts
                          ? {
                                parts: assignments[0].parts,
                            }
                          : {}),
                };

                // Filtrar solo assignments con ID válido
                const validAssignmentIds = assignments
                    .map((a) => a.id)
                    .filter((id): id is string => id != null && id.length > 0);

                if (validAssignmentIds.length === 0) {
                    throw new Error(
                        "No se encontraron asignaciones válidas para crear la remisión",
                    );
                }

                console.log(
                    "Creating remission with assignment IDs:",
                    validAssignmentIds,
                );

                const result = await createRemission({
                    assignmentIds: validAssignmentIds,
                    contractorId,
                    items,
                    orderDetails,
                    notes,
                });

                if (result && result.id) {
                    setRemissionId(result.id);
                    setRemissionFolio(result.folio || result.id);
                    await markRemissionPrinted(result.id);
                } else {
                    throw new Error("No se pudo crear la remisión");
                }
            } else if (remissionId) {
                // Si ya tenemos una remisión, solo abrir el modal
                console.log("Usando remisión existente:", remissionId);
            }

            // Abrir el modal de previsualización
            onOpen();
        } catch (error) {
            // REMOVED: console.error("Error al generar remisión:", error);
            setError(
                typeof error === "object" &&
                    error !== null &&
                    "message" in error
                    ? (error as Error).message
                    : "Error desconocido",
            );
            setSuccess(false);
        } finally {
            setLoading(false);
        }
    };

    const handleGeneratePDF = async () => {
        try {
            // Pequeña pausa para asegurar que el documento esté completamente renderizado
            await new Promise((resolve) => setTimeout(resolve, 500));

            const result = await generatePDF(
                "remission-document-preview",
                "remision-asignacion.pdf",
                {
                    openPdfAfterGeneration: true,
                    quality: 0.92,
                    scale: 3,
                    orientation: useLandscape ? "landscape" : "portrait",
                    twoCopies: useTwoCopies,
                },
            );

            if (result) {
                setSuccess(true);
                onOpenChange();
                if (onSuccess) onSuccess();
            } else {
                throw new Error("Error al generar PDF");
            }
        } catch (error) {
            setError(
                typeof error === "object" &&
                    error !== null &&
                    "message" in error
                    ? (error as Error).message
                    : "Error al generar PDF",
            );
        }
    };

    if (!contractorId || assignments.length === 0) {
        return (
            <Card className="my-4">
                <CardBody>
                    <div className="text-center py-6">
                        <InformationCircleIcon className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-800 mb-2">
                            No hay asignaciones seleccionadas
                        </h3>
                        <p className="text-gray-600">
                            No se puede generar una remisión sin seleccionar
                            asignaciones y un contratista.
                        </p>
                    </div>
                </CardBody>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            <Card className="mb-4">
                <CardBody>
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold text-gray-800">
                            Generar Remisión
                        </h2>
                        {success && (
                            <div className="flex items-center text-green-600">
                                <CheckIcon className="w-5 h-5 mr-1" />
                                <span className="text-sm font-medium">
                                    Remisión generada correctamente
                                </span>
                            </div>
                        )}
                    </div>

                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Notas de entrega (opcional)
                        </label>
                        <textarea
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder="Ingrese notas o comentarios adicionales para esta remisión..."
                            rows={3}
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                        />
                    </div>

                    <div className="flex flex-wrap gap-6 mb-4">
                        <div className="flex items-center gap-2">
                            <Switch
                                color="primary"
                                isSelected={useLandscape}
                                size="sm"
                                onChange={(e) =>
                                    setUseLandscape(e.target.checked)
                                }
                            />
                            <label className="text-sm font-medium text-gray-700 flex items-center">
                                <ArrowsPointingOutIcon className="w-4 h-4 mr-1 text-gray-500" />
                                Formato horizontal
                            </label>
                            <Tooltip content="Permite mejor visualización y más datos en la tabla">
                                <InformationCircleIcon className="w-4 h-4 text-gray-400" />
                            </Tooltip>
                        </div>

                        <div className="flex items-center gap-2">
                            <Switch
                                color="primary"
                                isSelected={useTwoCopies}
                                size="sm"
                                onChange={(e) =>
                                    setUseTwoCopies(e.target.checked)
                                }
                            />
                            <label className="text-sm font-medium text-gray-700 flex items-center">
                                <DocumentDuplicateIcon className="w-4 h-4 mr-1 text-gray-500" />
                                Dos copias por página
                            </label>
                            <Tooltip content="Imprimir dos copias idénticas en una sola página">
                                <InformationCircleIcon className="w-4 h-4 text-gray-400" />
                            </Tooltip>
                        </div>
                    </div>

                    {error && (
                        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
                            {error}
                        </div>
                    )}
                </CardBody>
                <CardFooter className="flex justify-end gap-3">
                    <Button
                        color="primary"
                        isDisabled={loading || success}
                        isLoading={loading}
                        spinner={<Spinner color="white" size="sm" />}
                        startContent={<EyeIcon className="w-5 h-5" />}
                        variant="solid"
                        onClick={handlePreview}
                    >
                        {loading
                            ? "Preparando..."
                            : success
                              ? "Ver remisión"
                              : "Previsualizar remisión"}
                    </Button>
                </CardFooter>
            </Card>

            {/* Modal de previsualización */}
            <Modal
                classNames={{
                    base: "max-h-[90vh]",
                    body: "p-0",
                }}
                isOpen={isOpen}
                scrollBehavior="inside"
                size="5xl"
                onOpenChange={onOpenChange}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="flex flex-col gap-1">
                                <div className="flex items-center justify-between">
                                    <span>Previsualización de Remisión</span>
                                    <div className="flex items-center gap-4">
                                        <Switch
                                            isSelected={useLandscape}
                                            size="sm"
                                            onValueChange={setUseLandscape}
                                        >
                                            Horizontal
                                        </Switch>
                                        <Switch
                                            isSelected={useTwoCopies}
                                            size="sm"
                                            onValueChange={setUseTwoCopies}
                                        >
                                            Dos copias
                                        </Switch>
                                    </div>
                                </div>
                            </ModalHeader>
                            <ModalBody>
                                <div className="bg-gray-100 p-8 overflow-auto">
                                    <div
                                        className="mx-auto shadow-lg"
                                        id="remission-document-preview"
                                        style={{
                                            maxWidth: useLandscape
                                                ? "297mm"
                                                : "210mm",
                                            backgroundColor: "white",
                                        }}
                                    >
                                        <RemissionDocument
                                            ref={remissionRef}
                                            assignmentId={
                                                remissionFolio ||
                                                remissionId ||
                                                undefined
                                            }
                                            assignments={assignments}
                                            contractorId={contractorId}
                                            date={new Date()}
                                            notes={notes}
                                            order={{
                                                id: orderId,
                                                // Incluir cutOrder si está disponible
                                                ...(orderData?.cutOrder
                                                    ? {
                                                          cutOrder:
                                                              orderData.cutOrder,
                                                      }
                                                    : assignments.length > 0 &&
                                                        assignments[0].cutOrder
                                                      ? {
                                                            cutOrder:
                                                                assignments[0]
                                                                    .cutOrder,
                                                        }
                                                      : {}),
                                                // Incluir partes de orden si están disponibles
                                                ...(orderData?.parts
                                                    ? {
                                                          parts: orderData.parts,
                                                      }
                                                    : assignments.length > 0 &&
                                                        assignments[0].parts
                                                      ? {
                                                            parts: assignments[0]
                                                                .parts,
                                                        }
                                                      : {}),
                                            }}
                                            orientation={
                                                useLandscape
                                                    ? "landscape"
                                                    : "portrait"
                                            }
                                        />
                                    </div>
                                </div>
                            </ModalBody>
                            <ModalFooter>
                                <Button
                                    color="danger"
                                    variant="light"
                                    onPress={onClose}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    color="primary"
                                    startContent={
                                        <PrinterIcon className="w-5 h-5" />
                                    }
                                    onPress={handleGeneratePDF}
                                >
                                    Generar PDF
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </div>
    );
}
