"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Divider,
    Tooltip,
    Badge,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
} from "@heroui/react";
import {
    DocumentTextIcon,
    ArrowDownTrayIcon,
    CalendarIcon,
    TruckIcon,
    ArrowLeftIcon,
    PencilSquareIcon,
    TrashIcon,
    EyeIcon,
    SparklesIcon,
    DocumentDuplicateIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";

import { RemissionDocumentCompact } from "@/features/remissions/components";
import { formatDate } from "@/shared/utils/formatters";
import { deleteRemission } from "@/features/remissions/actions/delete";
import RemissionPrintModal from "@/components/remissions/RemissionPrintModal";
import {
    RemissionStatusBadge,
    RemissionContractorInfo,
    RemissionHistory,
    RemissionOrderItems,
} from "@/features/remissions/components";

interface RemissionDetailClientProps {
    remission: any; // TODO: Type this properly
}

export default function RemissionDetailClient({
    remission,
}: RemissionDetailClientProps) {
    const router = useRouter();
    const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const printModal = useDisclosure();

    // Transform remission items to order groups for the new component
    const transformToOrderGroups = () => {
        if (
            !remission.remissionItems ||
            remission.remissionItems.length === 0
        ) {
            return [];
        }

        // Group items by order
        const orderMap = new Map();

        remission.remissionItems.forEach((item: any) => {
            const orderKey = remission.orderDetails?.cutOrder || "N/A";

            if (!orderMap.has(orderKey)) {
                orderMap.set(orderKey, {
                    orderId: remission.id,
                    cutOrder: orderKey,
                    parts: remission.orderDetails?.parts || [],
                    assignments: [],
                });
            }

            // Transform remission item to assignment structure
            const assignment = {
                id: item.id,
                quantity: item.quantity,
                garmentSize: {
                    size: {
                        code: item.sizeCode,
                    },
                    garment: {
                        model: {
                            code: item.modelCode,
                        },
                        color: {
                            name: item.colorName,
                        },
                    },
                },
            };

            orderMap.get(orderKey).assignments.push(assignment);
        });

        return Array.from(orderMap.values());
    };

    const orderGroups = transformToOrderGroups();

    const handlePDFOptions = () => {
        printModal.onOpen();
    };

    const handlePDF = async (layout: "single" | "double") => {
        printModal.onClose();
        await handleGeneratePDF(layout);
    };

    const handlePrint = async (layout: "single" | "double" = "single") => {
        const remissionElement =
            document.getElementById("remission-document") ||
            document.querySelector(".remission-document");

        if (!remissionElement) {
            addToast({
                title: "Error",
                description: "No se encontró el documento de remisión",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });

            return;
        }

        try {
            // Clonar el elemento para no afectar el original
            const clone = remissionElement.cloneNode(true) as HTMLElement;

            // Configurar el clon para captura óptima
            clone.style.width = "297mm";
            clone.style.padding = "5mm 10mm";
            clone.style.margin = "0";
            clone.style.backgroundColor = "#ffffff";
            clone.style.position = "absolute";
            clone.style.left = "-9999px";
            clone.style.top = "0";
            document.body.appendChild(clone);

            // Generate canvas from the cloned document
            const canvas = await html2canvas(clone, {
                scale: 2,
                logging: false,
                useCORS: true,
                allowTaint: true,
                backgroundColor: "#ffffff",
                windowWidth: 1122, // A4 landscape width at 96dpi
                windowHeight: 794, // A4 landscape height at 96dpi
                imageTimeout: 0,
                onclone: (clonedDoc) => {
                    const images = clonedDoc.querySelectorAll("img");

                    images.forEach((img) => {
                        img.style.objectFit = "contain";
                        img.style.width = "auto";
                        img.style.height = "auto";
                        img.style.maxWidth = "100%";
                        img.style.maxHeight = "100%";
                    });
                },
            });

            // Remover el clon
            document.body.removeChild(clone);

            // Create print window
            const printWindow = window.open(
                "",
                "_blank",
                "width=1200,height=800",
            );

            if (!printWindow) {
                addToast({
                    title: "Error",
                    description: "Error al abrir ventana de impresión",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });

                return;
            }

            const isDoublePage = layout === "double";

            // Write HTML with proper layout
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Remisión ${remission.folio}</title>
                    <style>
                        @page {
                            size: A4 ${isDoublePage ? "portrait" : "landscape"};
                            margin: 5mm;
                        }
                        
                        @media print {
                            body {
                                margin: 0;
                                padding: 0;
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                            }
                        }
                        
                        body {
                            margin: 0;
                            padding: 0;
                            font-family: Arial, sans-serif;
                            background: white;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                        }
                        
                        .print-container {
                            ${
                                isDoublePage
                                    ? `display: flex;
                                 flex-direction: column;
                                 gap: 10mm;
                                 padding: 10mm;`
                                    : `display: flex;
                                 justify-content: center;
                                 align-items: center;
                                 width: 100%;
                                 height: 100vh;`
                            }
                        }
                        
                        .remission-copy {
                            ${
                                isDoublePage
                                    ? `width: 190mm;
                                 height: 130mm;
                                 border: 1px solid #ddd;
                                 padding: 5mm;
                                 page-break-inside: avoid;`
                                    : `max-width: 277mm;
                                 max-height: 190mm;`
                            }
                        }
                        
                        img {
                            width: 100%;
                            height: auto;
                            display: block;
                        }
                        
                        .print-hidden {
                            display: none !important;
                        }
                        
                        @media screen {
                            body {
                                padding: 20px;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="print-container">
                        ${
                            isDoublePage
                                ? `<div class="remission-copy">
                                <img src="${canvas.toDataURL("image/png")}" alt="Remisión 1">
                            </div>
                            <div class="remission-copy">
                                <img src="${canvas.toDataURL("image/png")}" alt="Remisión 2">
                            </div>`
                                : `<div class="remission-copy">
                                <img src="${canvas.toDataURL("image/png")}" alt="Remisión">
                            </div>`
                        }
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();

            // Wait for images to load then print
            printWindow.onload = () => {
                setTimeout(() => {
                    printWindow.print();
                }, 500);
            };
        } catch (error) {
            console.error("Error preparing print:", error);
            addToast({
                title: "Error",
                description: "Error al preparar la impresión",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        }
    };

    const handleGeneratePDF = async (
        layout: "single" | "double" = "single",
    ) => {
        setIsGeneratingPDF(true);
        try {
            // Get the remission document element
            const element =
                document.getElementById("remission-document") ||
                document.querySelector(".remission-document");

            if (!element) {
                addToast({
                    title: "Error",
                    description: "No se encontró el documento de remisión",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });

                return;
            }

            // Clonar el elemento para no afectar el original
            const clone = element.cloneNode(true) as HTMLElement;

            // Configurar el clon para captura óptima
            clone.style.width = "297mm";
            clone.style.padding = "5mm 10mm";
            clone.style.margin = "0";
            clone.style.backgroundColor = "#ffffff";
            clone.style.position = "absolute";
            clone.style.left = "-9999px";
            clone.style.top = "0";
            document.body.appendChild(clone);

            const isDoublePage = layout === "double";

            // Generate high quality canvas
            const canvas = await html2canvas(clone, {
                scale: 2, // Alta calidad
                logging: false,
                useCORS: true,
                allowTaint: true,
                backgroundColor: "#ffffff",
                windowWidth: 1122, // A4 landscape width at 96dpi
                windowHeight: 794, // A4 landscape height at 96dpi
                imageTimeout: 0, // Sin timeout para imágenes
                onclone: (clonedDoc) => {
                    // Asegurar que las imágenes mantengan su aspecto
                    const images = clonedDoc.querySelectorAll("img");

                    images.forEach((img) => {
                        img.style.objectFit = "contain";
                        img.style.width = "auto";
                        img.style.height = "auto";
                        img.style.maxWidth = "100%";
                        img.style.maxHeight = "100%";
                    });
                },
            });

            // Remover el clon
            document.body.removeChild(clone);

            if (isDoublePage) {
                // Two copies per page
                // Portrait orientation for 2 copies
                const pdf = new jsPDF({
                    orientation: "portrait",
                    unit: "mm",
                    format: "a4",
                });

                const pageWidth = 210; // A4 width in portrait
                const pageHeight = 297; // A4 height in portrait
                const margin = 5;
                const gutter = 5; // Espacio entre las dos copias
                const copyHeight = (pageHeight - 2 * margin - gutter) / 2;
                const copyWidth = pageWidth - 2 * margin;

                // Calcular dimensiones para ajustar la imagen
                const imgAspectRatio = canvas.width / canvas.height;
                const copyAspectRatio = copyWidth / copyHeight;

                let finalWidth, finalHeight;

                if (imgAspectRatio > copyAspectRatio) {
                    // La imagen es más ancha - ajustar al ancho
                    finalWidth = copyWidth;
                    finalHeight = copyWidth / imgAspectRatio;
                } else {
                    // La imagen es más alta - ajustar a la altura
                    finalHeight = copyHeight;
                    finalWidth = copyHeight * imgAspectRatio;
                }

                // Centrar en cada área de copia
                const xOffset = margin + (copyWidth - finalWidth) / 2;
                const yOffset1 = margin + (copyHeight - finalHeight) / 2;
                const yOffset2 =
                    margin +
                    copyHeight +
                    gutter +
                    (copyHeight - finalHeight) / 2;

                // Agregar primera copia
                pdf.addImage(
                    canvas.toDataURL("image/png"),
                    "PNG",
                    xOffset,
                    yOffset1,
                    finalWidth,
                    finalHeight,
                );

                // Agregar línea de corte
                pdf.setDrawColor(128, 128, 128);
                pdf.setLineDashPattern([3, 3], 0);
                pdf.line(10, pageHeight / 2, pageWidth - 10, pageHeight / 2);

                // Indicador de tijeras
                pdf.setFontSize(10);
                pdf.setTextColor(128, 128, 128);
                pdf.text(
                    "✂- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -",
                    15,
                    pageHeight / 2 + 1,
                );

                // Agregar segunda copia
                pdf.addImage(
                    canvas.toDataURL("image/png"),
                    "PNG",
                    xOffset,
                    yOffset2,
                    finalWidth,
                    finalHeight,
                );

                pdf.save(`remision-${remission.folio}-2copias.pdf`);
            } else {
                // Single copy per page
                // Landscape orientation for single copy
                const pdf = new jsPDF({
                    orientation: "landscape",
                    unit: "mm",
                    format: "a4",
                });

                const pageWidth = 297; // A4 landscape width
                const pageHeight = 210; // A4 landscape height
                const margin = 5;

                // Calcular dimensiones para ajustar a la página
                const maxWidth = pageWidth - 2 * margin;
                const maxHeight = pageHeight - 2 * margin;

                const imgAspectRatio = canvas.width / canvas.height;
                const pageAspectRatio = maxWidth / maxHeight;

                let finalWidth, finalHeight;

                if (imgAspectRatio > pageAspectRatio) {
                    // La imagen es más ancha - ajustar al ancho
                    finalWidth = maxWidth;
                    finalHeight = maxWidth / imgAspectRatio;
                } else {
                    // La imagen es más alta - ajustar a la altura
                    finalHeight = maxHeight;
                    finalWidth = maxHeight * imgAspectRatio;
                }

                // Centrar la imagen en la página
                const xOffset = (pageWidth - finalWidth) / 2;
                const yOffset = (pageHeight - finalHeight) / 2;

                // Agregar la imagen
                pdf.addImage(
                    canvas.toDataURL("image/png"),
                    "PNG",
                    xOffset,
                    yOffset,
                    finalWidth,
                    finalHeight,
                );

                pdf.save(`remision-${remission.folio}.pdf`);
            }

            addToast({
                title: "Éxito",
                description: "PDF descargado exitosamente",
                color: "success",
                icon: <ArrowDownTrayIcon className="w-5 h-5" />,
            });
        } catch (error) {
            console.error("Error generating PDF:", error);
            addToast({
                title: "Error",
                description: "Error al generar el PDF",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        } finally {
            setIsGeneratingPDF(false);
        }
    };

    const handlePreviewPDF = async () => {
        try {
            // Get the remission document element
            const element =
                document.getElementById("remission-document") ||
                document.querySelector(".remission-document");

            if (!element) {
                addToast({
                    title: "Error",
                    description: "No se encontró el documento",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });

                return;
            }

            // Show loading
            const loadingToast = addToast({
                title: "Procesando",
                description: "Generando vista previa...",
                color: "primary",
            });

            // Clonar y preparar el elemento
            const clone = element.cloneNode(true) as HTMLElement;

            clone.style.width = "297mm";
            clone.style.padding = "10mm";
            clone.style.margin = "0";
            clone.style.backgroundColor = "#ffffff";
            clone.style.position = "absolute";
            clone.style.left = "-9999px";
            clone.style.top = "0";
            document.body.appendChild(clone);

            // Generate canvas
            const canvas = await html2canvas(clone, {
                scale: 2,
                logging: false,
                useCORS: true,
                allowTaint: true,
                backgroundColor: "#ffffff",
                windowWidth: 1122,
                windowHeight: 794,
                imageTimeout: 0,
                onclone: (clonedDoc) => {
                    const images = clonedDoc.querySelectorAll("img");

                    images.forEach((img) => {
                        img.style.objectFit = "contain";
                        img.style.width = "auto";
                        img.style.height = "auto";
                        img.style.maxWidth = "100%";
                        img.style.maxHeight = "100%";
                    });
                },
            });

            // Remover el clon
            document.body.removeChild(clone);

            // Create PDF
            const pdf = new jsPDF({
                orientation: "landscape",
                unit: "mm",
                format: "a4",
            });

            const pageWidth = 297;
            const pageHeight = 210;
            const margin = 5;

            // Calcular dimensiones para ajustar a la página
            const maxWidth = pageWidth - 2 * margin;
            const maxHeight = pageHeight - 2 * margin;

            const imgAspectRatio = canvas.width / canvas.height;
            const pageAspectRatio = maxWidth / maxHeight;

            let finalWidth, finalHeight;

            if (imgAspectRatio > pageAspectRatio) {
                finalWidth = maxWidth;
                finalHeight = maxWidth / imgAspectRatio;
            } else {
                finalHeight = maxHeight;
                finalWidth = maxHeight * imgAspectRatio;
            }

            // Centrar en la página
            const xOffset = (pageWidth - finalWidth) / 2;
            const yOffset = (pageHeight - finalHeight) / 2;

            pdf.addImage(
                canvas.toDataURL("image/png"),
                "PNG",
                xOffset,
                yOffset,
                finalWidth,
                finalHeight,
            );

            // Open in new tab
            const pdfBlob = pdf.output("blob");
            const pdfUrl = URL.createObjectURL(pdfBlob);

            window.open(pdfUrl, "_blank");

            // Clean up
            setTimeout(() => URL.revokeObjectURL(pdfUrl), 100);
            // loadingToast se cierra automáticamente al agregar un nuevo toast
            addToast({
                title: "Éxito",
                description: "Vista previa generada",
                color: "success",
                icon: <EyeIcon className="w-5 h-5" />,
            });
        } catch (error) {
            console.error("Error generating preview:", error);
            // loadingToast se cierra automáticamente al agregar un nuevo toast
            addToast({
                title: "Error",
                description: "Error al generar vista previa",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        }
    };

    const handleEdit = () => {
        router.push(`/dashboard/remissions/${remission.id}/edit`);
    };

    const handleDelete = () => {
        onOpen();
    };

    const confirmDelete = async () => {
        try {
            const result = await deleteRemission(remission.id);

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description: "Remisión eliminada exitosamente",
                    color: "success",
                    icon: <TrashIcon className="w-5 h-5" />,
                });
                router.push("/dashboard/remissions");
            } else {
                addToast({
                    title: "Error",
                    description: "Error al eliminar la remisión",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al eliminar la remisión",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        }
        onClose();
    };

    const springAnimation = {
        type: "spring",
        stiffness: 300,
        damping: 30,
    };

    return (
        <div className="space-y-6">
            {/* Header Card with Glassmorphism */}
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: -20 }}
                transition={springAnimation}
            >
                <Card className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-gray-900 dark:to-purple-950/20 border-none shadow-xl">
                    <CardBody className="p-8">
                        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                            <div className="flex items-center gap-4">
                                <motion.div
                                    className="p-4 bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg backdrop-blur-sm"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.05, rotate: 5 }}
                                >
                                    <DocumentTextIcon className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                                </motion.div>
                                <div>
                                    <div className="flex items-center gap-3 mb-2">
                                        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                            {remission.folio}
                                        </h1>
                                        <Badge
                                            className="animate-pulse"
                                            color="success"
                                            content="NEW"
                                            size="sm"
                                        >
                                            <SparklesIcon className="w-5 h-5 text-yellow-500" />
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                        <span className="flex items-center gap-1">
                                            <CalendarIcon className="w-4 h-4" />
                                            {formatDate(remission.createdAt)}
                                        </span>
                                        <RemissionStatusBadge
                                            size="sm"
                                            status={remission.status}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="flex flex-wrap gap-2 print-hidden">
                                <Tooltip content="Descargar PDF">
                                    <Button
                                        isIconOnly
                                        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                                        color="primary"
                                        isLoading={isGeneratingPDF}
                                        variant="flat"
                                        onPress={handlePDFOptions}
                                    >
                                        <ArrowDownTrayIcon className="w-5 h-5" />
                                    </Button>
                                </Tooltip>

                                <Tooltip content="Editar">
                                    <Button
                                        isIconOnly
                                        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                                        color="warning"
                                        variant="flat"
                                        onPress={handleEdit}
                                    >
                                        <PencilSquareIcon className="w-5 h-5" />
                                    </Button>
                                </Tooltip>

                                <Tooltip content="Eliminar">
                                    <Button
                                        isIconOnly
                                        className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                                        color="danger"
                                        variant="flat"
                                        onPress={handleDelete}
                                    >
                                        <TrashIcon className="w-5 h-5" />
                                    </Button>
                                </Tooltip>

                                <Button
                                    className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                                    color="default"
                                    startContent={
                                        <ArrowLeftIcon className="w-4 h-4" />
                                    }
                                    variant="flat"
                                    onPress={() =>
                                        router.push("/dashboard/remissions")
                                    }
                                >
                                    Volver
                                </Button>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Info Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Contractor Card */}
                {remission.contractor && (
                    <RemissionContractorInfo
                        compact={false}
                        contractor={remission.contractor}
                        showCard={true}
                    />
                )}

                {/* Order Info Card */}
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ ...springAnimation, delay: 0.2 }}
                >
                    <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <DocumentDuplicateIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                <h3 className="text-lg font-semibold">
                                    Información de Orden
                                </h3>
                            </div>
                        </CardHeader>
                        <Divider />
                        <CardBody className="pt-4">
                            <div className="space-y-2">
                                {remission.orderDetails && (
                                    <>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">
                                                Orden de Corte:
                                            </span>
                                            <span className="font-semibold">
                                                {remission.orderDetails
                                                    .cutOrder || "N/A"}
                                            </span>
                                        </div>
                                        {remission.orderDetails.parts?.length >
                                            0 && (
                                            <div className="flex justify-between">
                                                <span className="text-gray-600 dark:text-gray-400">
                                                    Partes:
                                                </span>
                                                <span className="font-semibold">
                                                    {remission.orderDetails.parts
                                                        .map((p: any) => p.code)
                                                        .join(", ")}
                                                </span>
                                            </div>
                                        )}
                                    </>
                                )}
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">
                                        Total Items:
                                    </span>
                                    <span className="font-semibold">
                                        {remission.remissionItems?.length || 0}
                                    </span>
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>

                {/* Summary Card */}
                <motion.div
                    animate={{ opacity: 1, x: 0 }}
                    initial={{ opacity: 0, x: 20 }}
                    transition={{ ...springAnimation, delay: 0.3 }}
                >
                    <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <TruckIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                <h3 className="text-lg font-semibold">
                                    Resumen de Entrega
                                </h3>
                            </div>
                        </CardHeader>
                        <Divider />
                        <CardBody className="pt-4">
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">
                                        Total Prendas:
                                    </span>
                                    <span className="font-semibold text-lg">
                                        {remission.remissionItems?.reduce(
                                            (sum: number, item: any) =>
                                                sum + item.quantity,
                                            0,
                                        ) || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">
                                        Modelos:
                                    </span>
                                    <span className="font-semibold">
                                        {new Set(
                                            remission.remissionItems?.map(
                                                (item: any) => item.modelCode,
                                            ),
                                        ).size || 0}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">
                                        Estado:
                                    </span>
                                    <RemissionStatusBadge
                                        showIcon={false}
                                        size="sm"
                                        status={remission.status}
                                    />
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            </div>

            {/* Remission Order Items */}
            {orderGroups.length > 0 && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ ...springAnimation, delay: 0.35 }}
                >
                    <RemissionOrderItems
                        orderGroups={orderGroups}
                        readOnly={true}
                        showRowAnimations={true}
                    />
                </motion.div>
            )}

            {/* Document Preview */}
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                transition={{ ...springAnimation, delay: 0.4 }}
            >
                <Card className="overflow-hidden shadow-xl">
                    <CardHeader className="bg-gray-50 dark:bg-gray-800/50">
                        <div className="flex items-center gap-2">
                            <EyeIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                            <h3 className="text-lg font-semibold">
                                Vista Previa del Documento
                            </h3>
                        </div>
                    </CardHeader>
                    <Divider />
                    <CardBody className="p-0">
                        <div className="relative" id="remission-document">
                            <RemissionDocumentCompact
                                remissionId={remission.id}
                            />

                            {/* Floating action buttons for document actions */}
                            <div className="absolute top-4 right-4 flex gap-2 print-hidden">
                                <Tooltip content="Vista previa PDF">
                                    <Button
                                        isIconOnly
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                        onPress={handlePreviewPDF}
                                    >
                                        <EyeIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* History Timeline */}
            {remission.history && remission.history.length > 0 && (
                <RemissionHistory history={remission.history} showCard={true} />
            )}

            {/* Action Confirmation Modal */}
            <Modal backdrop="blur" isOpen={isOpen} onClose={onClose}>
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader>Eliminar Remisión</ModalHeader>
                            <ModalBody>
                                <p>
                                    ¿Estás seguro de que deseas eliminar esta
                                    remisión? Esta acción no se puede deshacer.
                                </p>
                            </ModalBody>
                            <ModalFooter>
                                <Button
                                    color="default"
                                    variant="flat"
                                    onPress={onClose}
                                >
                                    Cancelar
                                </Button>
                                <Button color="danger" onPress={confirmDelete}>
                                    Eliminar
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>

            {/* PDF Options Modal */}
            <RemissionPrintModal
                action="pdf"
                isOpen={printModal.isOpen}
                loading={isGeneratingPDF}
                onClose={printModal.onClose}
                onConfirm={handlePDF}
            />
        </div>
    );
}
