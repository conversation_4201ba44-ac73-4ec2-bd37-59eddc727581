"use client";

// Re-exportar componentes de HeroUI que necesitan "use client"
// para usar en Server Components sin problemas

export { Card, CardBody, CardHeader, CardFooter } from "@heroui/card";

export { Button, ButtonGroup } from "@heroui/button";

export { Input, Textarea } from "@heroui/input";

export {
    Table,
    TableHeader,
    TableBody,
    TableColumn,
    TableRow,
    TableCell,
} from "@heroui/table";

export {
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
} from "@heroui/modal";

export {
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownSection,
    DropdownItem,
} from "@heroui/dropdown";

export { Select, SelectSection, SelectItem } from "@heroui/select";

export {
    Autocomplete,
    AutocompleteSection,
    AutocompleteItem,
} from "@heroui/autocomplete";

export { Chip } from "@heroui/chip";

export { Badge } from "@heroui/badge";

export { Avatar, AvatarGroup, AvatarIcon } from "@heroui/avatar";

export { Progress } from "@heroui/progress";

export { Spinner } from "@heroui/spinner";

export { Tooltip } from "@heroui/tooltip";

export { Divider } from "@heroui/divider";

export { Pagination } from "@heroui/pagination";

export { Link } from "@heroui/link";

export { Snippet } from "@heroui/snippet";

export { Code } from "@heroui/code";

export { Skeleton } from "@heroui/skeleton";

export { Switch, useSwitch } from "@heroui/switch";

export { Tabs, Tab } from "@heroui/tabs";

export { Checkbox } from "@heroui/checkbox";

export { Kbd } from "@heroui/kbd";

export { Slider } from "@heroui/slider";

export { addToast } from "@heroui/react";
