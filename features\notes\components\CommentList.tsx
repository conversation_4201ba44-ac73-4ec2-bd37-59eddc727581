"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";

import { CommentCard } from "./CommentCard";

interface Comment {
    id: string;
    content: string;
    createdAt: string | Date;
    updatedAt: string | Date;
    authorId: string;
    author: {
        id: string;
        name: string;
        email: string;
        image?: string | null;
    };
    replies?: Comment[];
}

interface CommentListProps {
    comments: Comment[];
    currentUserId?: string;
    userRole?: string;
    onUpdate: (commentId: string, content: string) => Promise<void>;
    onDelete: (commentId: string) => Promise<void>;
    onReply: (parentId: string, content: string) => Promise<void>;
    isLoading?: boolean;
    hasMore?: boolean;
    onLoadMore?: () => void;
}

export function CommentList({
    comments,
    currentUserId,
    userRole,
    onUpdate,
    onDelete,
    onReply,
    isLoading,
    hasMore,
    onLoadMore,
}: CommentListProps) {
    if (isLoading && comments.length === 0) {
        return (
            <div className="flex justify-center py-8">
                <Spinner size="sm" />
            </div>
        );
    }

    if (comments.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500">
                No hay comentarios aún. ¡Sé el primero en comentar!
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {comments.map((comment) => (
                <CommentCard
                    key={comment.id}
                    comment={comment}
                    currentUserId={currentUserId}
                    userRole={userRole}
                    onDelete={onDelete}
                    onReply={onReply}
                    onUpdate={onUpdate}
                />
            ))}

            {hasMore && (
                <div className="flex justify-center pt-4">
                    <Button
                        isLoading={isLoading}
                        size="sm"
                        variant="light"
                        onClick={onLoadMore}
                    >
                        Cargar más comentarios
                    </Button>
                </div>
            )}
        </div>
    );
}
