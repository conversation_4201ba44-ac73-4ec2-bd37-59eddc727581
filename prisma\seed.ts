import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
    console.log("Iniciando seed de la base de datos...");

    // Crear roles
    const roles = [
        { name: "ADMIN", iconName: "ShieldCheckIcon", color: "#EF4444" },
        { name: "EMPLOYEE", iconName: "UserIcon", color: "#3B82F6" },
        { name: "CONTRACTOR", iconName: "BriefcaseIcon", color: "#10B981" },
        { name: "GUEST", iconName: "UserCircleIcon", color: "#9CA3AF" },
    ];

    for (const role of roles) {
        await prisma.role.upsert({
            where: { name: role.name },
            update: {},
            create: role,
        });
        console.log(`✅ Rol ${role.name} creado/actualizado`);
    }

    console.log("✅ Seed completado exitosamente");
}

main()
    .catch((e) => {
        console.error("Error en seed:", e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
