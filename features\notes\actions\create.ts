"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
    revalidateData,
} from "@/shared/utils/actions-utils";
import {
    extractMentions,
    extractTags,
} from "@/features/notes/utils/content-parser";

// Interfaces
export interface CreateNoteData {
    content: string;
    orderId: string;
    statusId: string;
    importanceId: string;
}

/**
 * Server Action para crear una nueva nota
 */
export async function createNote(
    data: CreateNoteData,
): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        // Validaciones básicas
        if (!data.content.trim()) {
            return {
                success: false,
                error: "El contenido de la nota es obligatorio",
            };
        }

        if (!data.orderId) {
            return {
                success: false,
                error: "Debes seleccionar una orden",
            };
        }

        if (!data.statusId) {
            return {
                success: false,
                error: "Debes seleccionar un estado",
            };
        }

        if (!data.importanceId) {
            return {
                success: false,
                error: "Debes seleccionar una importancia",
            };
        }

        // Verificar que la orden existe
        const order = await prisma.order.findUnique({
            where: { id: data.orderId },
        });

        if (!order) {
            return {
                success: false,
                error: "La orden seleccionada no existe",
            };
        }

        // Verificar que el estado existe
        const status = await prisma.noteStatus.findUnique({
            where: { id: data.statusId },
        });

        if (!status) {
            return {
                success: false,
                error: "El estado seleccionado no existe",
            };
        }

        // Verificar que la importancia existe
        const importance = await prisma.noteImportance.findUnique({
            where: { id: data.importanceId },
        });

        if (!importance) {
            return {
                success: false,
                error: "La importancia seleccionada no existe",
            };
        }

        // Extraer menciones y tags del contenido
        const mentions = extractMentions(data.content);
        const tags = extractTags(data.content);

        // Crear la nota
        const newNote = await prisma.note.create({
            data: {
                content: data.content,
                order: { connect: { id: data.orderId } },
                status: { connect: { id: data.statusId } },
                importance: { connect: { id: data.importanceId } },
                author: { connect: { id: user.id } },
                mentions: mentions,
                tags: tags,
            },
            include: {
                order: true,
                status: true,
                importance: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
            },
        });

        // Revalidar rutas para actualizar los datos
        revalidateData([
            "/dashboard/notes",
            `/dashboard/orders/${data.orderId}`,
        ]);

        return {
            success: true,
            data: newNote,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
