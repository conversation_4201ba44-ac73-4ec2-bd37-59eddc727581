/**
 * SizeName Value Object
 *
 * Ensures size names follow business rules and constraints.
 * Immutable object that validates and normalizes size names.
 */

export class SizeName {
    private readonly _value: string;

    constructor(value: string) {
        this.validate(value);
        this._value = this.normalize(value);
    }

    get value(): string {
        return this._value;
    }

    /**
     * Validate the size name according to business rules
     */
    private validate(value: string): void {
        if (!value || value.trim() === "") {
            throw new Error("Size name cannot be empty");
        }

        if (value.length > 10) {
            throw new Error("Size name cannot exceed 10 characters");
        }

        // Only allow alphanumeric characters, hyphens, and forward slashes
        if (!/^[A-Za-z0-9\-\/]+$/.test(value)) {
            throw new Error(
                "Size name can only contain letters, numbers, hyphens, and forward slashes",
            );
        }

        // Prevent SQL injection patterns
        const dangerousPatterns = [
            /[';]/,
            /--/,
            /\/\*/,
            /\*\//,
            /xp_/,
            /script/i,
        ];

        for (const pattern of dangerousPatterns) {
            if (pattern.test(value)) {
                throw new Error("Size name contains invalid characters");
            }
        }
    }

    /**
     * Normalize the size name
     */
    private normalize(value: string): string {
        // Trim whitespace
        let normalized = value.trim();

        // Convert to uppercase for consistency
        normalized = normalized.toUpperCase();

        // Replace multiple hyphens with single hyphen
        normalized = normalized.replace(/-+/g, "-");

        // Remove leading/trailing hyphens
        normalized = normalized.replace(/^-+|-+$/g, "");

        return normalized;
    }

    /**
     * Check equality with another SizeName
     */
    equals(other: SizeName): boolean {
        return this._value === other._value;
    }

    /**
     * Convert to string
     */
    toString(): string {
        return this._value;
    }

    /**
     * Create from string with validation
     */
    static from(value: string): SizeName {
        return new SizeName(value);
    }

    /**
     * Check if a string is a valid size name
     */
    static isValid(value: string): boolean {
        try {
            new SizeName(value);

            return true;
        } catch {
            return false;
        }
    }

    /**
     * Common size name patterns
     */
    static patterns = {
        numeric: /^\d+$/,
        alpha: /^[XS|S|M|L|XL|XXL|XXXL]+$/i,
        alphaNumeric: /^[A-Z]+\d+$/i,
        range: /^\d+-\d+$/,
    };

    /**
     * Check if the size name matches a pattern
     */
    matchesPattern(pattern: RegExp): boolean {
        return pattern.test(this._value);
    }

    /**
     * Check if this is a numeric size
     */
    isNumeric(): boolean {
        return SizeName.patterns.numeric.test(this._value);
    }

    /**
     * Check if this is an alpha size (XS, S, M, L, etc.)
     */
    isAlpha(): boolean {
        return SizeName.patterns.alpha.test(this._value);
    }

    /**
     * Check if this is an alpha-numeric size (e.g., "A1", "B2")
     */
    isAlphaNumeric(): boolean {
        return SizeName.patterns.alphaNumeric.test(this._value);
    }

    /**
     * Check if this is a range size (e.g., "28-30", "32-34")
     */
    isRange(): boolean {
        return SizeName.patterns.range.test(this._value);
    }

    /**
     * Get numeric value if the size is numeric
     */
    getNumericValue(): number | null {
        if (this.isNumeric()) {
            return parseInt(this._value, 10);
        }

        return null;
    }

    /**
     * Get range values if the size is a range
     */
    getRangeValues(): { min: number; max: number } | null {
        if (this.isRange()) {
            const [min, max] = this._value
                .split("-")
                .map((v) => parseInt(v, 10));

            return { min, max };
        }

        return null;
    }
}
