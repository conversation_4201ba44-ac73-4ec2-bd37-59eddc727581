"use client";

import type { SWRConfiguration } from "swr";
import type { Customer } from "../types";

import useSWR from "swr";
import { useState } from "react";

import {
    getCustomer,
    getCustomers,
    validateCustomerName as validateName,
    createCustomer as create,
    updateCustomer as update,
    deleteCustomer as remove,
} from "@/features/customers/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

export interface CustomersResponse {
    customers: Customer[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

// Obtener helpers de revalidación para el cliente
const { revalidateData, useRevalidationListener } =
    createClientRevalidation("customer");

/**
 * Hook para obtener todos los clientes
 * @param options Opciones de filtrado y paginación
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de clientes, paginación, estado de carga y errores
 */
export function useCustomers(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
    config?: SWRConfiguration,
) {
    // Convertir opciones a cadena para clave de caché
    const optionsKey = JSON.stringify(options);

    // Escuchar eventos de revalidación
    const isRevalidating = useRevalidationListener();

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        ["customers", optionsKey],
        async () => getCustomers(options),
        config,
    );

    return {
        customers: (data?.data as any)?.customers || [],
        pagination: (data?.data as any)?.pagination,
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para obtener la información de un cliente por su ID
 * @param id ID del cliente a cargar
 * @param config Configuración opcional para SWR
 * @returns Objeto con los datos del cliente, estado de carga y errores
 */
export function useCustomer(
    id: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Escuchar eventos de revalidación - Convertir id para que sea string o undefined (no null)
    const isRevalidating = useRevalidationListener(id || undefined);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        id ? ["customer", id] : null,
        async () => (id ? getCustomer(id) : null),
        config,
    );

    // Verificar explícitamente si data tiene la propiedad 'data'
    const customerData = data && "data" in data ? data.data : null;

    return {
        customer: customerData,
        isLoading,
        isRevalidating,
        isError: !!error || data?.success === false,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para comprobar si un nombre de cliente es válido
 * @param name Nombre del cliente a validar
 * @param excludeId ID del cliente a excluir de la validación (para edición)
 * @param config Configuración opcional para SWR
 * @returns Objeto con resultado de validación y estado
 */
export function useValidateCustomerName(
    name: string | null,
    excludeId?: string,
    config?: SWRConfiguration,
) {
    // Combinar opciones predeterminadas con las proporcionadas
    const options: SWRConfiguration = {
        revalidateOnFocus: false,
        dedupingInterval: 10000,
        ...config,
    };

    // Utilizar Server Action como fetcher
    const { data, error, isLoading } = useSWR(
        name && name.length >= 2
            ? ["validateCustomerName", name, excludeId]
            : null,
        async () =>
            name && name.length >= 2 ? validateName(name, excludeId) : null,
        options,
    );

    // Manejar los diferentes posibles formatos de respuesta
    let isValid: boolean | undefined = undefined;

    if (data) {
        // Si es el tipo directo { isValid: boolean }
        if ("isValid" in data && typeof data.isValid === "boolean") {
            isValid = data.isValid;
        }
        // Si es la estructura { data: { isValid }, success, error }
        else if (
            data.data &&
            "isValid" in (data.data as any) &&
            typeof (data.data as any).isValid === "boolean"
        ) {
            isValid = (data.data as any).isValid;
        }
    }

    return {
        isValid,
        isValidating: isLoading,
        error: error,
    };
}

/**
 * Hook para crear un nuevo cliente
 * @returns Función para crear un cliente y estado de la operación
 */
export function useCreateCustomer() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createCustomer = async (
        data: { name: string; displayName?: string | null },
        forceCreate = false,
    ) => {
        console.log(
            `[useCreateCustomer] Iniciando creación - Nombre: ${data.name}, displayName: ${data.displayName}, forceCreate: ${forceCreate}`,
        );
        setIsLoading(true);
        setError(null);

        try {
            const result = await create(data, forceCreate);

            console.log(
                `[useCreateCustomer] Resultado recibido de server action:`,
                result,
            );

            // Verificar si result es null o undefined
            if (!result) {
                console.error(
                    `[useCreateCustomer] La server action devolvió null/undefined`,
                );
                const errorMessage =
                    "La operación no pudo completarse - respuesta vacía del servidor";

                setError(errorMessage);

                return { success: false, error: errorMessage };
            }

            // Si se creó correctamente, revalidar todos los datos relacionados
            if (result.success) {
                console.log(
                    `[useCreateCustomer] Cliente creado correctamente, revalidando datos...`,
                );

                // Revalidar datos mediante la función genérica
                await revalidateData((result.data as any)?.id);
                console.log(
                    `[useCreateCustomer] Datos revalidados exitosamente`,
                );
            } else {
                // Extraer el mensaje de error de forma segura
                let errorMessage = "Error al crear el cliente";

                if (
                    result &&
                    "error" in result &&
                    typeof result.error === "string"
                ) {
                    errorMessage = result.error;
                } else if (
                    result &&
                    "message" in result &&
                    typeof result.message === "string"
                ) {
                    errorMessage = result.message;
                }

                console.log(
                    `[useCreateCustomer] Error en la creación: ${errorMessage}`,
                );
                setError(errorMessage);
            }

            return result;
        } catch (e) {
            console.error(`[useCreateCustomer] Excepción capturada:`, e);
            const errorMessage =
                e instanceof Error ? e.message : "Error al crear el cliente";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createCustomer,
        isCreating: isLoading,
        createError: error,
    };
}

/**
 * Hook para actualizar un cliente existente
 * @returns Función para actualizar un cliente y estado de la operación
 */
export function useUpdateCustomer() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateCustomer = async (
        id: string,
        data: { name: string; displayName?: string | null },
        forceUpdate = false,
    ) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await update(id, data, forceUpdate);

            if (result?.success) {
                // Revalidar datos mediante la función genérica
                if ((result.data as any)?.id) {
                    await revalidateData((result.data as any).id);
                } else {
                    await revalidateData(id);
                }
            } else {
                // Extraer el mensaje de error de forma segura
                let errorMessage = "Error al actualizar el cliente";

                if (
                    result &&
                    "error" in result &&
                    typeof result.error === "string"
                ) {
                    errorMessage = result.error;
                } else if (
                    result &&
                    "message" in result &&
                    typeof result.message === "string"
                ) {
                    errorMessage = result.message;
                }
                setError(errorMessage);
            }

            return result;
        } catch (e) {
            const errorMessage =
                e instanceof Error
                    ? e.message
                    : "Error al actualizar el cliente";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        updateCustomer,
        isUpdating: isLoading,
        updateError: error,
    };
}

/**
 * Hook para eliminar un cliente
 * @returns Función para eliminar un cliente y estado de la operación
 */
export function useDeleteCustomer() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteCustomer = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await remove(id);

            if (result?.success) {
                // Revalidar datos mediante la función genérica
                await revalidateData();
            } else {
                // Extraer el mensaje de error de forma segura
                let errorMessage = "Error al eliminar el cliente";

                if (
                    result &&
                    "error" in result &&
                    typeof result.error === "string"
                ) {
                    errorMessage = result.error;
                } else if (
                    result &&
                    "message" in result &&
                    typeof result.message === "string"
                ) {
                    errorMessage = result.message;
                }
                setError(errorMessage);
            }

            return result;
        } catch (e) {
            const errorMessage =
                e instanceof Error ? e.message : "Error al eliminar el cliente";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        deleteCustomer,
        isDeleting: isLoading,
        deleteError: error,
    };
}
