"use client";

import React, { ReactNode, ComponentType } from "react";
import { Card, CardBody, Progress } from "@heroui/react";
import { motion, useSpring, useTransform } from "framer-motion";
import {
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    SparklesIcon,
} from "@heroicons/react/24/outline";

interface EnhancedStatCardProps {
    title: string;
    value: string | number;
    icon?: ComponentType<{ className?: string }> | ReactNode;
    description?: string;
    trend?: {
        value: number;
        isPositive: boolean;
    };
    isLoading?: boolean;
    delay?: number;
    color?:
        | "primary"
        | "secondary"
        | "success"
        | "warning"
        | "danger"
        | "default";
    onClick?: () => void;
}

export const EnhancedStatCard = ({
    title,
    value,
    icon,
    description,
    trend,
    isLoading = false,
    delay = 0,
    color = "primary",
    onClick,
}: EnhancedStatCardProps) => {
    // Animated value
    const numericValue =
        typeof value === "number" ? value : parseInt(value.toString()) || 0;
    const springValue = useSpring(numericValue, {
        stiffness: 300,
        damping: 30,
    });
    const displayValue = useTransform(springValue, (v) => {
        if (typeof value === "number") {
            return Math.round(v);
        }

        return value;
    });

    // Enhanced card colors with gradients
    const getCardColors = () => {
        const colorMap = {
            primary: {
                icon: "from-blue-500/20 to-indigo-500/20",
                iconText: "text-blue-600 dark:text-blue-400",
                gradient: "from-blue-500 to-indigo-500",
                bg: "from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20",
                border: "border-blue-200/50 dark:border-blue-700/50",
                glow: "shadow-blue-500/20",
                trend: {
                    positive: "text-blue-500 dark:text-blue-400",
                    negative: "text-red-500 dark:text-red-400",
                },
            },
            secondary: {
                icon: "from-purple-500/20 to-pink-500/20",
                iconText: "text-purple-600 dark:text-purple-400",
                gradient: "from-purple-500 to-pink-500",
                bg: "from-purple-50/50 to-pink-50/50 dark:from-purple-900/20 dark:to-pink-900/20",
                border: "border-purple-200/50 dark:border-purple-700/50",
                glow: "shadow-purple-500/20",
                trend: {
                    positive: "text-purple-500 dark:text-purple-400",
                    negative: "text-red-500 dark:text-red-400",
                },
            },
            success: {
                icon: "from-green-500/20 to-emerald-500/20",
                iconText: "text-green-600 dark:text-green-400",
                gradient: "from-green-500 to-emerald-500",
                bg: "from-green-50/50 to-emerald-50/50 dark:from-green-900/20 dark:to-emerald-900/20",
                border: "border-green-200/50 dark:border-green-700/50",
                glow: "shadow-green-500/20",
                trend: {
                    positive: "text-green-500 dark:text-green-400",
                    negative: "text-red-500 dark:text-red-400",
                },
            },
            warning: {
                icon: "from-amber-500/20 to-orange-500/20",
                iconText: "text-amber-600 dark:text-amber-400",
                gradient: "from-amber-500 to-orange-500",
                bg: "from-amber-50/50 to-orange-50/50 dark:from-amber-900/20 dark:to-orange-900/20",
                border: "border-amber-200/50 dark:border-amber-700/50",
                glow: "shadow-amber-500/20",
                trend: {
                    positive: "text-amber-500 dark:text-amber-400",
                    negative: "text-red-500 dark:text-red-400",
                },
            },
            danger: {
                icon: "from-red-500/20 to-pink-500/20",
                iconText: "text-red-600 dark:text-red-400",
                gradient: "from-red-500 to-pink-500",
                bg: "from-red-50/50 to-pink-50/50 dark:from-red-900/20 dark:to-pink-900/20",
                border: "border-red-200/50 dark:border-red-700/50",
                glow: "shadow-red-500/20",
                trend: {
                    positive: "text-green-500 dark:text-green-400",
                    negative: "text-red-500 dark:text-red-400",
                },
            },
            default: {
                icon: "from-gray-500/20 to-slate-500/20",
                iconText: "text-gray-600 dark:text-gray-400",
                gradient: "from-gray-500 to-slate-500",
                bg: "from-gray-50/50 to-slate-50/50 dark:from-gray-900/20 dark:to-slate-900/20",
                border: "border-gray-200/50 dark:border-gray-700/50",
                glow: "shadow-gray-500/20",
                trend: {
                    positive: "text-green-500 dark:text-green-400",
                    negative: "text-red-500 dark:text-red-400",
                },
            },
        };

        return colorMap[color];
    };

    const colors = getCardColors();

    // Enhanced loading skeleton
    if (isLoading) {
        return (
            <motion.div
                animate={{ opacity: 1 }}
                className="w-full"
                initial={{ opacity: 0 }}
            >
                <Card className="h-40 overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
                    <CardBody className="p-5">
                        <div className="animate-pulse space-y-3">
                            <div className="flex items-center justify-between">
                                <div className="space-y-2 flex-1">
                                    <div className="h-4 w-24 bg-gray-300 dark:bg-gray-700 rounded-lg" />
                                    <div className="h-10 w-32 bg-gray-300 dark:bg-gray-700 rounded-lg" />
                                    <div className="h-3 w-40 bg-gray-300 dark:bg-gray-700 rounded-lg" />
                                </div>
                                <div className="w-12 h-12 bg-gray-300 dark:bg-gray-700 rounded-xl" />
                            </div>
                            <div className="h-1 w-full bg-gray-300 dark:bg-gray-700 rounded-full" />
                        </div>
                    </CardBody>
                </Card>
            </motion.div>
        );
    }

    return (
        <motion.div
            animate={{ opacity: 1, y: 0, scale: 1 }}
            className="w-full h-full"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{
                duration: 0.5,
                delay: delay * 0.1,
                type: "spring",
                stiffness: 100,
            }}
            whileHover={onClick ? { y: -4 } : {}}
        >
            <Card
                className={`
                    relative overflow-hidden h-full
                    bg-gradient-to-br ${colors.bg}
                    backdrop-blur-xl ${colors.border} border
                    shadow-xl ${colors.glow}
                    transition-all duration-300
                    ${onClick ? "cursor-pointer hover:shadow-2xl" : ""}
                `}
                isPressable={!!onClick}
                onClick={onClick}
            >
                {/* Animated background gradient */}
                <motion.div
                    animate={{
                        scale: [1, 1.2, 1],
                        rotate: [0, 180, 360],
                    }}
                    className={`absolute inset-0 bg-gradient-to-br ${colors.icon} opacity-10`}
                    transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear",
                    }}
                />

                <CardBody className="relative z-10 p-5">
                    <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                                <p className="text-sm font-semibold text-gray-600 dark:text-gray-400">
                                    {title}
                                </p>
                                {trend && (
                                    <motion.div
                                        animate={{ scale: 1 }}
                                        className="flex items-center gap-1"
                                        initial={{ scale: 0 }}
                                        transition={{
                                            delay: delay * 0.1 + 0.3,
                                        }}
                                    >
                                        {trend.isPositive ? (
                                            <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />
                                        ) : (
                                            <ArrowTrendingDownIcon className="w-4 h-4 text-red-500" />
                                        )}
                                        <span
                                            className={`text-xs font-bold ${
                                                trend.isPositive
                                                    ? colors.trend.positive
                                                    : colors.trend.negative
                                            }`}
                                        >
                                            {trend.isPositive ? "+" : ""}
                                            {trend.value}%
                                        </span>
                                    </motion.div>
                                )}
                            </div>

                            <motion.div
                                animate={{ opacity: 1, scale: 1 }}
                                initial={{ opacity: 0, scale: 0.8 }}
                                transition={{
                                    delay: delay * 0.1 + 0.2,
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 20,
                                }}
                            >
                                <motion.h4
                                    className={`text-3xl font-bold bg-gradient-to-r ${colors.gradient} bg-clip-text text-transparent`}
                                >
                                    {
                                        (typeof value === "number"
                                            ? displayValue
                                            : value) as any
                                    }
                                </motion.h4>
                            </motion.div>

                            {description && (
                                <motion.p
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-xs text-gray-600 dark:text-gray-400 mt-2 leading-relaxed"
                                    initial={{ opacity: 0, y: 5 }}
                                    transition={{ delay: delay * 0.1 + 0.3 }}
                                >
                                    {description}
                                </motion.p>
                            )}

                            {/* Progress indicator */}
                            {trend && (
                                <motion.div
                                    animate={{ scaleX: 1 }}
                                    className="mt-3"
                                    initial={{ scaleX: 0 }}
                                    transition={{
                                        delay: delay * 0.1 + 0.4,
                                        type: "spring",
                                    }}
                                >
                                    <Progress
                                        classNames={{
                                            track: "bg-gray-200/50 dark:bg-gray-700/50",
                                            indicator: `bg-gradient-to-r ${colors.gradient}`,
                                        }}
                                        maxValue={100}
                                        size="sm"
                                        value={Math.abs(trend.value)}
                                    />
                                </motion.div>
                            )}
                        </div>

                        {icon && (
                            <motion.div
                                animate={{ scale: 1, rotate: 0 }}
                                className="relative"
                                initial={{ scale: 0, rotate: -180 }}
                                transition={{
                                    delay: delay * 0.1 + 0.1,
                                    type: "spring",
                                    stiffness: 200,
                                }}
                                whileHover={{ scale: 1.1, rotate: 5 }}
                            >
                                <div
                                    className={`p-3 rounded-2xl bg-gradient-to-br ${colors.icon} backdrop-blur-sm shadow-lg`}
                                >
                                    {React.isValidElement(icon)
                                        ? React.cloneElement(
                                              icon as React.ReactElement<any>,
                                              {
                                                  className: `w-6 h-6 ${colors.iconText}`,
                                              },
                                          )
                                        : React.createElement(
                                              icon as ComponentType<{
                                                  className?: string;
                                              }>,
                                              {
                                                  className: `w-6 h-6 ${colors.iconText}`,
                                              },
                                          )}
                                </div>

                                {/* Sparkle effect */}
                                <motion.div
                                    animate={{
                                        opacity: [0, 1, 0],
                                        scale: [0.5, 1, 0.5],
                                    }}
                                    className="absolute -top-1 -right-1"
                                    transition={{
                                        duration: 2,
                                        repeat: Infinity,
                                        delay: 1,
                                    }}
                                >
                                    <SparklesIcon className="w-4 h-4 text-amber-500" />
                                </motion.div>
                            </motion.div>
                        )}
                    </div>

                    {/* Decorative elements */}
                    <motion.div
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.5, 0.8, 0.5],
                        }}
                        className="absolute -bottom-2 -right-2 w-24 h-24 rounded-full bg-gradient-to-br from-white/10 to-white/5 dark:from-white/5 dark:to-white/0 blur-2xl"
                        transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: "easeInOut",
                        }}
                    />
                </CardBody>
            </Card>
        </motion.div>
    );
};
