// types/remission/core.ts
/**
 * Core interfaces for remission data model
 * These interfaces represent the base business logic
 */

export interface RemissionCore {
    id: string;
    folio: string;
    assignmentId: string;
    contractorId: string;
    items: RemissionItem[];
    orderDetails: OrderDetails;
    notes?: string;
    status: RemissionStatus;
    timestamps: RemissionTimestamps;
}

export interface RemissionItem {
    id: string;
    modelCode: string;
    colorName: string;
    sizeCode: string;
    quantity: number;
}

export interface OrderDetails {
    id: string;
    cutOrder?: string;
    creationDate: Date;
    parts?: OrderPart[];
}

export interface OrderPart {
    id: string;
    code: string;
}

export type RemissionStatus = "ACTIVE" | "CANCELLED" | "PRINTED";

export interface RemissionTimestamps {
    created: Date;
    updated: Date;
    printed?: Date;
}
