# Technical Specification

This is the technical specification for the spec detailed in @.agent-os/specs/2025-08-02-subclient-management/spec.md

## Implementation Status & File Structure

### ✅ Archivos Existentes (No Modificar - Solo Usar)

**Backend Actions**:
- `/features/customers/actions/create-subcustomer.ts` - Crear subcliente con validaciones ✅
- `/features/customers/actions/get-subcustomers.ts` - Listar subclientes y jerarquía completa ✅ 
- `/features/customers/actions/update-hierarchy.ts` - Mover clientes entre padres ✅

**Componentes UI**:
- `/features/customers/components/hierarchy/CustomerHierarchy.tsx` - Componente visual de jerarquía ✅
- `/features/customers/components/hierarchy/SubCustomerList.tsx` - Lista de subclientes con botón agregar ✅
- `/features/customers/components/hierarchy/CustomerSelector.tsx` - Selector básico ✅

### 🔧 Archivos a Modificar

**Páginas Dashboard**:
- `/app/dashboard/customers/[id]/details/page.tsx` → Agregar tab "Subclientes"
- `/app/dashboard/customers/UnifiedClientPage.tsx` → Mostrar indicadores de jerarquía
- `/features/customers/actions/index.ts` → Exportar nuevas acciones

**Hooks**:
- `/features/customers/hooks/useCustomer.ts` → Agregar hooks para update/delete

### 🆕 Archivos a Crear

**Server Actions Faltantes**:
- `/features/customers/actions/update-subcustomer.ts`
- `/features/customers/actions/delete-subcustomer.ts`

**Componentes UI Nuevos**:
- `/features/customers/components/modals/SubcustomerModal.tsx` - Modal unificado crear/editar
- `/features/customers/components/forms/SubcustomerForm.tsx` - Formulario reutilizable

## Technical Requirements

### Funcionalidad Faltante

**Operaciones CRUD Faltantes**:
- **Update**: Editar name y displayName de subclientes existentes
- **Delete**: Eliminar con validación de dependencias (orders, packings)
- **UI Integration**: Tab funcional en página de detalles

**Validaciones Avanzadas**:
- Verificar orders asociadas antes de delete
- Verificar packings asociadas antes de delete  
- Mantener validación de nombres únicos globales (ya existe)
- Prevenir referencias circulares (ya existe)

### UI/UX Specifications

**Página de Detalles del Cliente** (`/app/dashboard/customers/[id]/details/page.tsx`):
```typescript
// Agregar tab usando HeroUI Tabs
<Tabs defaultSelectedKey="info" className="w-full">
  <Tab key="info" title="Información">
    {/* Contenido existente */}
  </Tab>
  <Tab key="subclientes" title="Subclientes">
    <SubCustomerList 
      subCustomers={customer.subCustomers} 
      parentId={customer.id}
      onAddSubCustomer={() => onOpenCreate()}
    />
  </Tab>
</Tabs>
```

**Lista Principal de Clientes** (`/app/dashboard/customers/UnifiedClientPage.tsx`):
- Mostrar `→ Reebok` con Chip color="default" size="sm"
- Indentar subclientes con `ml-6` 
- Agregar contador de subclientes con Badge

**Modal de Subcliente** (nuevo):
```typescript
// HeroUI Modal específico
<Modal size="2xl" isOpen={isOpen} onOpenChange={onOpenChange}>
  <ModalContent>
    <ModalHeader>
      <h3>{isEdit ? 'Editar' : 'Crear'} Subcliente</h3>
    </ModalHeader>
    <ModalBody>
      <SubcustomerForm onSubmit={handleSubmit} initialData={editData} />
    </ModalBody>
    <ModalFooter>
      <Button color="danger" variant="light" onPress={onClose}>
        Cancelar
      </Button>
      <Button color="primary" onPress={handleSubmit}>
        {isEdit ? 'Actualizar' : 'Crear'}
      </Button>
    </ModalFooter>
  </ModalContent>
</Modal>
```

### Componentes HeroUI Específicos

**Botones**:
- `Button color="primary" variant="flat"` para acciones principales
- `Button color="danger" variant="light"` para cancelar/eliminar
- `Button size="sm" isIconOnly` para acciones inline

**Cards y Layout**:
- `Card shadow="sm" className="hover:shadow-md transition-shadow"`
- `CardHeader className="pb-3"` con titulo y acciones
- `Divider className="my-4"` para separadores

**Indicadores**:
- `Chip color="success" size="sm"` para contadores 
- `Badge content={count} color="primary"` para números
- `Spinner size="sm"` para loading states

### Patrones de Código del Proyecto

**Server Actions Pattern**:
```typescript
"use server";
import { z } from "zod";
import { db, handleDbError } from "@/shared/lib/db";
import { revalidatePath } from "next/cache";

const updateSubcustomerSchema = z.object({
  id: z.string().min(1, "ID es requerido"),
  name: z.string().min(1, "El nombre es requerido"),
  displayName: z.string().optional(),
});

export async function updateSubcustomer(
  data: z.infer<typeof updateSubcustomerSchema>
): Promise<{ success: true; data: Customer } | { success: false; error: string }> {
  try {
    const validated = updateSubcustomerSchema.parse(data);
    
    // Business validation
    const existing = await db.customer.findFirst({
      where: { name: validated.name, id: { not: validated.id } }
    });
    if (existing) {
      return { success: false, error: "Ya existe un cliente con ese nombre" };
    }
    
    const updated = await db.customer.update({
      where: { id: validated.id },
      data: { name: validated.name, displayName: validated.displayName },
      include: { parent: true, subCustomers: true }
    });
    
    revalidatePath("/dashboard/customers");
    revalidatePath(`/dashboard/customers/${validated.id}/details`);
    
    return { success: true, data: updated };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0]?.message || "Datos inválidos" };
    }
    return handleDbError(() => { throw error; }, "Error al actualizar subcliente");
  }
}
```

**Error Messages (todos en español)**:
- "Subcliente no encontrado"
- "No se puede eliminar: tiene órdenes asociadas"  
- "No se puede eliminar: tiene empaques asociados"
- "El nombre es requerido"
- "Ya existe un cliente con ese nombre"

### Performance Criteria

**Query Optimization**:
- Usar `include: { subCustomers: true, _count: { select: { orders: true, packings: true } } }`
- Paginación offset-based (no cursor) como el resto del proyecto
- Eager loading de relaciones necesarias solo

**UI Responsiveness**:
- Optimistic updates con SWR mutate
- Loading states con Spinner de HeroUI
- Transiciones suaves con Framer Motion (ya usado en el proyecto)
- Error boundaries para manejo de errores