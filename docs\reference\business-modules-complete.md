# Módulos de Negocio - Proyecto Lohari

## 🏭 Visión General del Sistema

Lohari es un sistema integral de gestión para manufactura de prendas de vestir que cubre todo el flujo de trabajo desde la recepción de órdenes hasta la entrega final. El sistema está diseñado para empresas de confección que trabajan con contratistas externos (maquilas).

## 📦 Módulo: Orders (Órdenes)

### Propósito
Centro neurálgico del sistema. Gestiona las órdenes de producción desde su creación hasta la entrega.

### Características Principales
- **Dashboard con 3 tabs**: Órdenes, Métricas, Calendario
- **8 tarjetas de estadísticas** con métricas de negocio
- **Sistema de alertas** con indicadores de riesgo
- **Vista dual**: Grid y Lista
- **Códigos de partida** reales (ej: "635487")
- **Batch selection** para operaciones masivas

### Campos Clave
```typescript
interface Order {
  id: string
  transferNumber?: string      // Número de transferencia
  cutOrder?: string           // Orden de corte
  batch?: string              // Lote
  receivedDate: Date          // Fecha de recepción
  estimatedDeliveryDate?: Date // Fecha estimada de entrega
  deliveryDate?: Date         // Fecha real de entrega
  customerId: string          // Cliente
  statusId: string            // Estado actual
  // Relaciones
  garments: Garment[]         // Prendas de la orden
  parts: OrderPart[]          // Partes/partidas
  assignments: Assignment[]    // Asignaciones a contratistas
  notes: Note[]               // Notas y observaciones
}
```

### Flujo de Trabajo
1. **Creación**: Wizard multi-paso (info básica → prendas → partes → revisión)
2. **Asignación**: Distribuir trabajo a contratistas
3. **Seguimiento**: Monitorear progreso y riesgos
4. **Entrega**: Generar remisiones y cerrar orden

### Métricas del Dashboard
- Órdenes activas/completadas
- Prendas en producción
- Entregas próximas
- Indicadores de riesgo
- Productividad por período
- Tasa de cumplimiento

## 👥 Módulo: Customers (Clientes)

### Propósito
Gestión de la base de clientes que realizan órdenes.

### Características
- CRUD completo con sistema unificado
- Historial de órdenes por cliente
- Métricas de negocio por cliente
- Búsqueda y filtrado avanzado

### Campos
```typescript
interface Customer {
  id: string
  name: string               // Nombre único
  orders: Order[]            // Historial de órdenes
  createdAt: Date
  updatedAt: Date
}
```

### Funcionalidades
- Crear/editar/eliminar clientes
- Ver historial completo de órdenes
- Estadísticas por cliente
- Exportación de datos

## 👷 Módulo: Contractors (Contratistas)

### Propósito
Gestión de contratistas externos que realizan el trabajo de manufactura.

### Características
- Información completa de contacto
- Métricas de rendimiento
- Historial de asignaciones
- Evaluación de desempeño

### Campos
```typescript
interface Contractor {
  id: string
  name: string               // Nombre único
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  notes?: string            // Observaciones
  assignments: Assignment[]  // Trabajos asignados
  remissions: Remission[]   // Entregas realizadas
}
```

### Métricas
- Total de asignaciones
- Tasa de cumplimiento
- Calidad del trabajo
- Tiempos de entrega

## 📋 Módulo: Assignments (Asignaciones)

### Propósito
Distribuir el trabajo de las órdenes entre los contratistas disponibles.

### Características
- **Wizard avanzado** multi-paso
- **Folios únicos** por asignación
- **Tracking de progreso**
- **Validación optimista**
- **Control de versiones**

### Campos
```typescript
interface Assignment {
  id: string
  folio?: string            // Folio único (ej: "2024-001")
  contractorId: string      // Contratista asignado
  garmentSizeId: string     // Prenda y talla
  quantity: int             // Cantidad asignada
  defects?: int             // Defectos reportados
  isCompleted: boolean      // Estado de completado
  orderId: string          // Orden padre
  version: int             // Control de versiones
  status: string           // ACTIVE, CANCELLED, etc.
}
```

### Flujo del Wizard
1. **Selección de orden**: Elegir orden a asignar
2. **Selección de items**: Prendas y cantidades
3. **Asignación**: Distribuir entre contratistas
4. **Revisión**: Confirmar asignaciones

### Estados
- `ACTIVE`: Asignación activa
- `COMPLETED`: Trabajo completado
- `CANCELLED`: Asignación cancelada

## 📄 Módulo: Remissions (Remisiones)

### Propósito
Gestionar la entrega de trabajos completados por los contratistas.

### Características
- **Generación de PDF** profesional
- **Folio secuencial** único
- **Estados de impresión**
- **Historial completo**
- **Vista previa** antes de imprimir

### Campos
```typescript
interface Remission {
  id: string
  folio: string             // Folio único (ej: "2024/12/001")
  contractorId: string      // Contratista
  notes?: string            // Observaciones
  orderDetails?: Json       // Detalles de órdenes
  printedAt?: Date         // Fecha de impresión
  status: string           // Estado
  assignments: RemissionAssignment[] // Asignaciones incluidas
  items: RemissionItem[]   // Items detallados
}
```

### Flujo
1. **Selección**: Elegir asignaciones completadas
2. **Generación**: Crear documento de remisión
3. **Vista previa**: Revisar antes de confirmar
4. **Impresión**: Generar PDF final
5. **Tracking**: Registrar entrega

### PDF Incluye
- Header con logo y folio
- Información del contratista
- Detalle de items entregados
- Totales y observaciones
- Espacio para firmas

## 📝 Módulo: Notes (Notas)

### Propósito
Sistema robusto de comunicación y seguimiento dentro de las órdenes.

### Características
- **Comentarios anidados** ilimitados
- **Sistema de likes**
- **Niveles de importancia**
- **Menciones** (@usuario)
- **Hashtags** (#etiqueta)
- **Bulk actions**

### Campos
```typescript
interface Note {
  id: string
  content: string           // Contenido de la nota
  statusId: string         // Estado de la nota
  authorId: string         // Autor
  orderId: string          // Orden asociada
  importanceId: string     // Nivel de importancia
  mentions: string[]       // Usuarios mencionados
  tags: string[]           // Etiquetas
  comments: NoteComment[]  // Comentarios
  likes: NoteLike[]        // Likes
}
```

### Niveles de Importancia
- `LOW`: Baja - Informativo
- `MEDIUM`: Media - Requiere atención
- `HIGH`: Alta - Urgente
- `CRITICAL`: Crítica - Acción inmediata

### Estados
- `ACTIVE`: Nota activa
- `RESOLVED`: Resuelta
- `ARCHIVED`: Archivada

## 🎨 Módulo: Models (Modelos)

### Propósito
Catálogo de diseños/modelos de prendas que se pueden producir.

### Características
- Códigos únicos por modelo
- Descripciones detalladas
- Relación con prendas producidas
- CRUD completo

### Campos
```typescript
interface GarmentModel {
  id: string
  code: string              // Código único (ej: "M-001")
  description: string       // Descripción del modelo
  garments: Garment[]       // Prendas de este modelo
}
```

## 🌈 Módulo: Colors (Colores)

### Propósito
Gestión del catálogo de colores disponibles para las prendas.

### Características
- **Código HEX** para visualización
- **Preview del color**
- **Utilidades de accesibilidad**
- **Variaciones de color**
- **Contraste checker**

### Campos
```typescript
interface Color {
  id: string
  name: string              // Nombre único
  hexCode?: string          // Código hexadecimal
  garments: Garment[]       // Prendas en este color
}
```

### Funcionalidades Especiales
- Preview visual del color
- Cálculo de contraste
- Paleta de variaciones
- Nombres descriptivos

## 📏 Módulo: Sizes (Tallas)

### Propósito
Catálogo de tallas disponibles para las prendas.

### Características
- Códigos estándar (S, M, L, XL, etc.)
- Gestión simple y eficiente
- Relación con cantidades

### Campos
```typescript
interface Size {
  id: string
  code: string              // Código único (ej: "M", "L", "XL")
  garments: GarmentSize[]   // Relación con prendas
}
```

## 👕 Módulo: Garments (Prendas)

### Propósito
Gestión de las combinaciones específicas de modelo + color dentro de una orden.

### Estructura
```typescript
interface Garment {
  id: string
  modelId: string           // Modelo de la prenda
  colorId: string           // Color de la prenda
  orderId: string           // Orden a la que pertenece
  sizes: GarmentSize[]      // Cantidades por talla
}

interface GarmentSize {
  id: string
  totalQuantity: int        // Cantidad total
  usedQuantity: int         // Cantidad ya asignada
  sizeId: string           // Talla
  garmentId: string        // Prenda padre
}
```

### Flujo
1. Crear prenda (modelo + color)
2. Definir cantidades por talla
3. Trackear asignaciones
4. Controlar inventario

## 📊 Módulo: Dashboard

### Propósito
Vista general del estado del sistema y métricas clave.

### Componentes
- **Stats Cards**: Métricas principales
- **Activity Feed**: Actividad reciente
- **Upcoming Deliveries**: Entregas próximas
- **Risk Indicators**: Alertas de riesgo
- **Performance Charts**: Gráficos de rendimiento

### Métricas Mostradas
- Órdenes activas/completadas
- Prendas en producción
- Entregas esta semana
- Contratistas activos
- Tasa de cumplimiento
- Indicadores de riesgo

## 🔄 Interacciones Entre Módulos

### Flujo Principal de Negocio
```
Customer → Order → Garments → Assignments → Contractor → Remission
                      ↓           ↓
                    Notes    Progress Tracking
```

### Dependencias Clave
1. **Order** es el centro - conecta clientes, prendas, asignaciones
2. **Assignment** vincula órdenes con contratistas
3. **Remission** cierra el ciclo de trabajo
4. **Notes** provee comunicación transversal
5. **Garments** define qué se produce

## 🎯 Casos de Uso Principales

### 1. Crear Nueva Orden
- Cliente solicita producción
- Se definen modelos, colores y cantidades
- Se establecen fechas de entrega
- Se crean las prendas con sus tallas

### 2. Asignar Trabajo
- Seleccionar orden con trabajo pendiente
- Distribuir entre contratistas disponibles
- Generar folios de asignación
- Trackear progreso

### 3. Recibir Entregas
- Contratista completa trabajo
- Generar remisión con items entregados
- Actualizar cantidades completadas
- Cerrar asignaciones

### 4. Monitorear Producción
- Dashboard con métricas en tiempo real
- Alertas de riesgo automáticas
- Reportes de productividad
- Seguimiento por notas

## 🚀 Características Avanzadas

### Sistema de Riesgo
- Algoritmo que evalúa:
  - Tiempo restante vs trabajo pendiente
  - Historial del contratista
  - Complejidad de la orden
  - Factores externos

### Folios Inteligentes
- Generación secuencial por fecha
- Formato: YYYY/MM/NNN
- Únicos y trazables
- Reset mensual automático

### Validación Optimista
- UI responsiva inmediata
- Rollback en caso de error
- Mejor experiencia de usuario
- Reducción de latencia percibida

## 📈 Métricas de Negocio

### KPIs Principales
1. **Tasa de cumplimiento**: % de órdenes a tiempo
2. **Productividad**: Prendas/día por contratista
3. **Calidad**: % de defectos reportados
4. **Utilización**: % de capacidad usada
5. **Tiempo de ciclo**: Días promedio por orden

### Reportes Disponibles
- Productividad por período
- Análisis por cliente
- Rendimiento de contratistas
- Estado de inventario
- Proyecciones de entrega