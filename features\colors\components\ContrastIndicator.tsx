"use client";

import React from "react";
import { Chip, Tooltip } from "@heroui/react";

import { calculateContrastRatio, getWCAGLevel } from "../utils/colorUtils";

interface ContrastIndicatorProps {
    color: string;
    background?: string;
    compact?: boolean;
    className?: string;
}

export function ContrastIndicator({
    color,
    background = "#FFFFFF",
    compact = false,
    className = "",
}: ContrastIndicatorProps) {
    const contrastRatio = calculateContrastRatio(color, background);
    const wcagLevel = getWCAGLevel(contrastRatio);

    // Determinar el color del indicador
    const getChipColor = () => {
        if (wcagLevel === "AAA") return "success";
        if (wcagLevel === "AA") return "primary";
        if (wcagLevel === "A") return "warning";

        return "danger";
    };

    // Texto explicativo para el tooltip
    const getWCAGExplanation = () => {
        switch (wcagLevel) {
            case "AAA":
                return "Excelente contraste - Cumple el nivel AAA de WCAG (ratio ≥ 7:1)";
            case "AA":
                return "Buen contraste - Cumple el nivel AA de WCAG (ratio ≥ 4.5:1)";
            case "A":
                return "Contraste aceptable para texto grande - Cumple el nivel A de WCAG (ratio ≥ 3:1)";
            default:
                return "Contraste insuficiente - No cumple con los estándares de accesibilidad WCAG";
        }
    };

    if (compact) {
        return (
            <Tooltip
                content={
                    <div className="p-1">
                        <p className="font-medium">{getWCAGExplanation()}</p>
                        <p className="text-sm">
                            Ratio: {contrastRatio.toFixed(2)}:1
                        </p>
                    </div>
                }
                placement="bottom"
            >
                <Chip
                    classNames={{
                        base: `${className} cursor-help`,
                    }}
                    color={getChipColor()}
                    size="sm"
                    variant={wcagLevel === "Fail" ? "solid" : "bordered"}
                >
                    WCAG {wcagLevel}
                </Chip>
            </Tooltip>
        );
    }

    return (
        <div className={`flex flex-col gap-2 ${className}`}>
            <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Contraste WCAG
                </span>
                <Chip
                    color={getChipColor()}
                    size="sm"
                    variant={wcagLevel === "Fail" ? "solid" : "bordered"}
                >
                    {wcagLevel}
                </Chip>
            </div>

            <div className="relative w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div
                    className="absolute h-full top-0 left-0 rounded-full"
                    style={{
                        width: `${Math.min(100, (contrastRatio / 7) * 100)}%`,
                        backgroundColor:
                            wcagLevel === "AAA"
                                ? "rgb(34, 197, 94)"
                                : wcagLevel === "AA"
                                  ? "rgb(59, 130, 246)"
                                  : wcagLevel === "A"
                                    ? "rgb(245, 158, 11)"
                                    : "rgb(239, 68, 68)",
                    }}
                />
            </div>

            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>Ratio: {contrastRatio.toFixed(2)}:1</span>
                <span>{getWCAGExplanation().split(" - ")[0]}</span>
            </div>
        </div>
    );
}
