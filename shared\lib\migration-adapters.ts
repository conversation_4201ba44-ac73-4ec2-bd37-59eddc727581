import { CONFIG_NAMES } from "@/constants/config-names";

import {
    getNoteImportanceByName,
    getOrderStatusByName,
    getNoteStatusByName,
    getPackingStatusByName,
    getRejectionReasonByName,
    getRoleByName,
} from "./config-cache";

/**
 * Adaptadores de migración para mantener compatibilidad
 * mientras migramos de IDs hardcodeados a búsqueda por nombre
 */

// Mapeo de valores antiguos en inglés a nuevos en español
const NAME_TRANSLATIONS = {
    // OrderStatus
    RECEIVED: CONFIG_NAMES.orderStatus.RECEIVED,
    IN_PRODUCTION: CONFIG_NAMES.orderStatus.IN_PRODUCTION,
    QUALITY_CHECK: CONFIG_NAMES.orderStatus.QUALITY_CHECK,
    PACKING: CONFIG_NAMES.orderStatus.PACKING,
    READY_TO_DELIVER: CONFIG_NAMES.orderStatus.READY_TO_DELIVER,
    DELIVERED: CONFIG_NAMES.orderStatus.DELIVERED,
    REJECTED: CONFIG_NAMES.orderStatus.REJECTED,
    CANCELLED: CONFIG_NAMES.orderStatus.CANCELLED,

    // NoteStatus
    PENDING: CONFIG_NAMES.noteStatus.PENDING,
    IN_PROGRESS: CONFIG_NAMES.noteStatus.IN_PROGRESS,
    COMPLETED: CONFIG_NAMES.noteStatus.COMPLETED,

    // NoteImportance
    LOW: CONFIG_NAMES.noteImportance.LOW,
    MEDIUM: CONFIG_NAMES.noteImportance.MEDIUM,
    HIGH: CONFIG_NAMES.noteImportance.HIGH,

    // También mapear los nombres ya en español por si acaso
    ...Object.values(CONFIG_NAMES.orderStatus).reduce(
        (acc, val) => ({ ...acc, [val]: val }),
        {},
    ),
    ...Object.values(CONFIG_NAMES.noteStatus).reduce(
        (acc, val) => ({ ...acc, [val]: val }),
        {},
    ),
    ...Object.values(CONFIG_NAMES.noteImportance).reduce(
        (acc, val) => ({ ...acc, [val]: val }),
        {},
    ),
} as const;

/**
 * Obtiene el ID de una importancia por nombre o nivel
 * Compatible con el sistema antiguo y el nuevo
 */
export async function getImportanceIdSafe(
    nameOrLevel: string,
): Promise<string | null> {
    // Traducir si es necesario
    const translatedName =
        NAME_TRANSLATIONS[nameOrLevel as keyof typeof NAME_TRANSLATIONS] ||
        nameOrLevel;

    // Buscar por nombre
    const importance = await getNoteImportanceByName(translatedName);

    if (importance) return importance.id;

    // Si no se encuentra y parece ser un ID (formato UUID), devolverlo tal cual
    if (
        nameOrLevel.match(
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
        )
    ) {
        return nameOrLevel;
    }

    return null;
}

/**
 * Obtiene el ID de un estado de orden
 */
export async function getOrderStatusIdSafe(
    nameOrId: string,
): Promise<string | null> {
    const translatedName =
        NAME_TRANSLATIONS[nameOrId as keyof typeof NAME_TRANSLATIONS] ||
        nameOrId;
    const status = await getOrderStatusByName(translatedName);

    if (status) return status.id;

    // Si parece ser un ID, devolverlo
    if (nameOrId.match(/^[0-9a-f-]{36}$/i)) {
        return nameOrId;
    }

    return null;
}

/**
 * Obtiene configuración completa por nombre, con fallback a ID
 */
export async function getConfigSafe(
    tableName: string,
    nameOrId: string,
): Promise<any | null> {
    const translatedName =
        NAME_TRANSLATIONS[nameOrId as keyof typeof NAME_TRANSLATIONS] ||
        nameOrId;

    // Mapear tabla a función específica
    switch (tableName) {
        case "noteImportance":
            return getNoteImportanceByName(translatedName);
        case "orderStatus":
            return getOrderStatusByName(translatedName);
        case "noteStatus":
            return getNoteStatusByName(translatedName);
        case "packingStatus":
            return getPackingStatusByName(translatedName);
        case "rejectionReason":
            return getRejectionReasonByName(translatedName);
        case "role":
            return getRoleByName(translatedName);
        default:
            return null;
    }
}
