# Solución: Error "Debe seleccionar al menos una orden"

## 🔍 Problema Identificado

El error ocurría porque el wizard permitía llegar al paso final y hacer clic en "Crear Packing" sin haber seleccionado órdenes.

## ✅ Soluciones Implementadas

### 1. **Validación en Navegación**
- Agregada validación en `handleNext()` para prevenir avanzar sin órdenes seleccionadas
- Muestra mensaje de error: "Debe seleccionar al menos una orden"

### 2. **Deshabilitación de Botones**
- Botón "Siguiente" deshabilitado si no hay órdenes seleccionadas
- Botón "Crear Packing" deshabilitado si no hay órdenes o distribución de calidad

### 3. **Indicador Visual**
- Agregado mensaje de advertencia en el resumen cuando no hay órdenes seleccionadas
- Icono y texto explicativo para guiar al usuario

## 🎯 Posibles Causas del Problema Original

### 1. **No hay órdenes disponibles**
- Todas las cantidades ya fueron asignadas o empacadas
- El cliente seleccionado no tiene órdenes

### 2. **Filtrado por cliente**
- Las órdenes pueden estar asociadas a otro cliente
- Prueba hacer clic en "Ver todas las órdenes" en el paso de selección

### 3. **Estado de las órdenes**
- Solo se muestran órdenes con cantidad disponible para empacar
- Verifica que las órdenes tengan `totalQuantity > usedQuantity`

## 📋 Pasos para Crear un Packing

1. **Seleccionar Cliente**: Elige el cliente destinatario
2. **Seleccionar Órdenes**: 
   - Si no aparecen órdenes, haz clic en "Ver todas las órdenes"
   - Marca las órdenes que deseas incluir
3. **Clasificar Calidad**: Distribuye las cantidades por calidad
4. **Revisar y Crear**: Verifica el resumen y crea el packing

## 🚨 Recomendaciones

1. **Verificar disponibilidad**: Asegúrate de que existan órdenes con cantidades disponibles
2. **Crear órdenes primero**: Si no hay órdenes, créalas desde `/dashboard/orders/new`
3. **Revisar asignaciones**: Las cantidades ya asignadas no aparecerán disponibles

## 🛠️ Depuración

Si continúas teniendo problemas:

1. Abre la consola del navegador (F12)
2. Busca mensajes que empiecen con 📦 o 🔍
3. Verifica el resultado de `getOrdersForPacking`
4. Revisa si `dataLength` es 0