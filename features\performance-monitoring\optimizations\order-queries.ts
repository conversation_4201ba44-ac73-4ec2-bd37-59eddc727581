/**
 * Optimized Order Queries
 * Replaces inefficient queries with selective field loading
 */

import { prisma } from "@/shared/lib/prisma";

import { performanceTracker } from "../utils/api-metrics";

import { optimizedOrderSelect, minimalOrderSelect } from "./prisma-selects";

interface OrderQueryOptions {
    search?: string;
    statusId?: string;
    customerId?: string;
    orderBy?: string;
    order?: "asc" | "desc";
    page?: number;
    perPage?: number;
    cursor?: string;
}

/**
 * Optimized getOrders with selective loading
 */
export async function getOrdersOptimized(options: OrderQueryOptions = {}) {
    const startTime = performance.now();

    try {
        // Configuration
        const page = options.page || 1;
        const perPage = options.perPage || 25;
        const orderBy = options.orderBy || "createdAt";
        const order = options.order || "desc";

        // Build filters
        const where: any = {};

        if (options.search) {
            where.OR = [
                {
                    transferNumber: {
                        contains: options.search,
                        mode: "insensitive",
                    },
                },
                { cutOrder: { contains: options.search, mode: "insensitive" } },
                { batch: { contains: options.search, mode: "insensitive" } },
                {
                    customer: {
                        name: { contains: options.search, mode: "insensitive" },
                    },
                },
            ];
        }

        if (options.statusId) where.statusId = options.statusId;
        if (options.customerId) where.customerId = options.customerId;

        // Execute optimized query
        const [orders, total] = await Promise.all([
            prisma.order.findMany({
                where,
                select: optimizedOrderSelect,
                orderBy: { [orderBy]: order },
                skip: (page - 1) * perPage,
                take: perPage,
            }),
            prisma.order.count({ where }),
        ]);

        // Track performance
        const duration = performance.now() - startTime;

        performanceTracker.trackQuery({
            query: "getOrdersOptimized",
            duration: Math.round(duration),
            timestamp: Date.now(),
            rowCount: orders.length,
        });

        return {
            success: true,
            data: {
                orders,
                pagination: {
                    total,
                    currentPage: page,
                    lastPage: Math.ceil(total / perPage),
                },
            },
        };
    } catch (error) {
        const duration = performance.now() - startTime;

        performanceTracker.trackQuery({
            query: "getOrdersOptimized-error",
            duration: Math.round(duration),
            timestamp: Date.now(),
        });

        throw error;
    }
}

/**
 * Cursor-based pagination for infinite scroll
 */
export async function getOrdersCursorPaginated(
    options: OrderQueryOptions = {},
) {
    const startTime = performance.now();

    try {
        const take = options.perPage || 25;
        const cursor = options.cursor;

        // Build filters
        const where: any = {};

        if (options.statusId) where.statusId = options.statusId;
        if (options.customerId) where.customerId = options.customerId;

        const orders = await prisma.order.findMany({
            where,
            select: minimalOrderSelect,
            orderBy: { createdAt: "desc" },
            take: take + 1, // Fetch one extra to check if there's more
            cursor: cursor ? { id: cursor } : undefined,
            skip: cursor ? 1 : 0,
        });

        const hasNextPage = orders.length > take;
        const items = hasNextPage ? orders.slice(0, -1) : orders;
        const nextCursor = hasNextPage ? items[items.length - 1].id : null;

        // Track performance
        const duration = performance.now() - startTime;

        performanceTracker.trackQuery({
            query: "getOrdersCursorPaginated",
            duration: Math.round(duration),
            timestamp: Date.now(),
            rowCount: items.length,
        });

        return {
            items,
            nextCursor,
            hasNextPage,
        };
    } catch (error) {
        throw error;
    }
}
