"use client";

import {
    FixedSizeList,
    VariableSizeList,
    ListChildComponentProps,
} from "react-window";
import { useCallback, useRef, memo } from "react";

import { featureFlags } from "../../feature-flags";

interface VirtualizedListProps<T> {
    items: T[];
    height: number;
    width?: number | string;
    itemHeight: number | ((index: number) => number);
    renderItem: (
        item: T,
        index: number,
        style: React.CSSProperties,
    ) => React.ReactNode;
    overscan?: number;
    threshold?: number; // Number of items before enabling virtualization
    className?: string;
    onEndReached?: () => void;
    endReachedThreshold?: number;
}

/**
 * Optimized virtualized list component
 * Automatically enables virtual scrolling for large lists
 */
export function VirtualizedList<T>({
    items,
    height,
    width = "100%",
    itemHeight,
    renderItem,
    overscan = 3,
    threshold = 100,
    className = "",
    onEndReached,
    endReachedThreshold = 0.8,
}: VirtualizedListProps<T>) {
    const listRef = useRef<any>(null);
    const lastScrollTop = useRef(0);

    // Check if virtualization should be enabled
    const shouldVirtualize =
        featureFlags.ENABLE_VIRTUAL_SCROLL && items.length > threshold;

    // Handle scroll for infinite loading
    const handleScroll = useCallback(
        ({ scrollOffset, scrollUpdateWasRequested }: any) => {
            if (!onEndReached || scrollUpdateWasRequested) return;

            const scrollHeight =
                items.length *
                (typeof itemHeight === "number" ? itemHeight : 80);
            const clientHeight = height;
            const scrollPercentage =
                (scrollOffset + clientHeight) / scrollHeight;

            if (
                scrollPercentage > endReachedThreshold &&
                scrollOffset > lastScrollTop.current
            ) {
                onEndReached();
            }

            lastScrollTop.current = scrollOffset;
        },
        [items.length, itemHeight, height, endReachedThreshold, onEndReached],
    );

    // Row renderer wrapper
    const Row = memo(({ index, style }: ListChildComponentProps) => {
        return (
            <div style={style}>{renderItem(items[index], index, style)}</div>
        );
    });

    Row.displayName = "VirtualizedRow";

    // Render non-virtualized list for small datasets
    if (!shouldVirtualize) {
        return (
            <div className={className} style={{ height, overflow: "auto" }}>
                {items.map((item, index) => (
                    <div key={index}>{renderItem(item, index, {})}</div>
                ))}
            </div>
        );
    }

    // Render virtualized list for large datasets
    if (typeof itemHeight === "number") {
        // Fixed size list (better performance)
        return (
            <FixedSizeList
                ref={listRef}
                className={className}
                height={height}
                itemCount={items.length}
                itemSize={itemHeight}
                overscanCount={overscan}
                width={width}
                onScroll={handleScroll}
            >
                {Row}
            </FixedSizeList>
        );
    } else {
        // Variable size list
        return (
            <VariableSizeList
                ref={listRef}
                className={className}
                height={height}
                itemCount={items.length}
                itemSize={itemHeight}
                overscanCount={overscan}
                width={width}
                onScroll={handleScroll}
            >
                {Row}
            </VariableSizeList>
        );
    }
}

/**
 * Memoized virtualized list for better performance
 */
export const MemoizedVirtualizedList = memo(
    VirtualizedList,
) as typeof VirtualizedList;
