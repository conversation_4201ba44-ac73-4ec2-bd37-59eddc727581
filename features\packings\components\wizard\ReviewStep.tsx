"use client";

import { <PERSON>, CardBody, CardHeader, Chip, Divider } from "@heroui/react";
import {
    Calendar,
    Package,
    FileText,
    Box,
    ShoppingBag,
    CheckCircle,
} from "lucide-react";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { OrderForPacking } from "../../actions/select-orders-for-packing";

interface BasicInfo {
    customerId: string;
    subCustomerId?: string;
    deliveryDate: string;
    notes?: string;
}

interface QualityDistribution {
    modelCode: string;
    modelName: string;
    colorCode: string;
    colorName: string;
    partNumber: string;
    sizeName: string;
    quantity: number;
    qualityType: "primera" | "segunda" | "manchada" | "incompleta";
    garmentSizeId: string;
}

interface PackagingSummary {
    orderId: string;
    orderNumber: string;
    piecesPerBox: number;
    piecesPerBag: number;
    sizeBreakdown: Array<{
        size: string;
        boxes: number;
        loosePieces: number;
        bagsFirst: number;
        bagsSecond: number;
    }>;
    totalBoxes: number;
    totalBagsFirst: number;
    totalBagsSecond: number;
    totalLoosePieces: number;
}

interface ReviewStepProps {
    basicInfo: BasicInfo;
    selectedOrders: OrderForPacking[];
    qualityDistribution: QualityDistribution[];
    packagingSummary: PackagingSummary[];
}

export function ReviewStep({
    basicInfo,
    selectedOrders,
    qualityDistribution,
    packagingSummary,
}: ReviewStepProps) {
    const getQualityTypeLabel = (type: string) => {
        switch (type) {
            case "primera":
                return { label: "Primera", color: "success" };
            case "segunda":
                return { label: "Segunda", color: "warning" };
            case "manchada":
                return { label: "Manchada", color: "danger" };
            case "incompleta":
                return { label: "Incompleta", color: "default" };
            default:
                return { label: type, color: "default" };
        }
    };

    const totalQuantity = qualityDistribution.reduce(
        (acc, d) => acc + d.quantity,
        0,
    );
    const totalBoxes = packagingSummary.reduce(
        (acc, s) => acc + s.totalBoxes,
        0,
    );
    const totalBags = packagingSummary.reduce(
        (acc, s) => acc + s.totalBagsFirst + s.totalBagsSecond,
        0,
    );

    return (
        <div className="space-y-6">
            {/* Basic Information */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Información Básica
                    </h3>
                </CardHeader>
                <CardBody className="space-y-3">
                    <div className="flex justify-between">
                        <span className="text-gray-600">Cliente:</span>
                        <span className="font-medium">
                            {/* TODO: Display actual customer names */}
                            {basicInfo.customerId}
                            {basicInfo.subCustomerId &&
                                ` > ${basicInfo.subCustomerId}`}
                        </span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-gray-600">Fecha de entrega:</span>
                        <span className="font-medium flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {format(new Date(basicInfo.deliveryDate), "PPP", {
                                locale: es,
                            })}
                        </span>
                    </div>
                    {basicInfo.notes && (
                        <div>
                            <span className="text-gray-600">Notas:</span>
                            <p className="mt-1 text-sm">{basicInfo.notes}</p>
                        </div>
                    )}
                </CardBody>
            </Card>

            {/* Selected Orders */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                        <Package className="w-5 h-5" />
                        Órdenes Seleccionadas ({selectedOrders.length})
                    </h3>
                </CardHeader>
                <CardBody>
                    <div className="space-y-2">
                        {selectedOrders.map((order) => (
                            <div
                                key={order.id}
                                className="flex justify-between items-center"
                            >
                                <span>
                                    {order.transferNumber || order.displayName}
                                </span>
                                <Chip size="sm" variant="flat">
                                    {order.totalAvailableQuantity} pzs
                                </Chip>
                            </div>
                        ))}
                    </div>
                </CardBody>
            </Card>

            {/* Quality Distribution Summary */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                        <CheckCircle className="w-5 h-5" />
                        Distribución por Calidad
                    </h3>
                </CardHeader>
                <CardBody>
                    <div className="space-y-2">
                        {Object.entries(
                            qualityDistribution.reduce(
                                (acc, item) => {
                                    acc[item.qualityType] =
                                        (acc[item.qualityType] || 0) +
                                        item.quantity;

                                    return acc;
                                },
                                {} as Record<string, number>,
                            ),
                        ).map(([type, quantity]) => {
                            const { label, color } = getQualityTypeLabel(type);

                            return (
                                <div
                                    key={type}
                                    className="flex justify-between items-center"
                                >
                                    <Chip
                                        color={color as any}
                                        size="sm"
                                        variant="flat"
                                    >
                                        {label}
                                    </Chip>
                                    <span className="font-medium">
                                        {quantity} piezas
                                    </span>
                                </div>
                            );
                        })}
                        <Divider />
                        <div className="flex justify-between items-center font-semibold">
                            <span>Total</span>
                            <span>{totalQuantity} piezas</span>
                        </div>
                    </div>
                </CardBody>
            </Card>

            {/* Packaging Summary */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                        <Box className="w-5 h-5" />
                        Resumen de Empaque
                    </h3>
                </CardHeader>
                <CardBody>
                    <div className="grid grid-cols-3 gap-4 text-center">
                        <div className="space-y-1">
                            <Box className="w-8 h-8 mx-auto text-primary" />
                            <p className="text-2xl font-bold">{totalBoxes}</p>
                            <p className="text-sm text-gray-600">Cajas</p>
                        </div>
                        <div className="space-y-1">
                            <ShoppingBag className="w-8 h-8 mx-auto text-warning" />
                            <p className="text-2xl font-bold">{totalBags}</p>
                            <p className="text-sm text-gray-600">Bolsas</p>
                        </div>
                        <div className="space-y-1">
                            <Package className="w-8 h-8 mx-auto text-success" />
                            <p className="text-2xl font-bold">
                                {totalQuantity}
                            </p>
                            <p className="text-sm text-gray-600">
                                Total Piezas
                            </p>
                        </div>
                    </div>
                </CardBody>
            </Card>

            {/* Summary Message */}
            <Card className="bg-primary-50 border-primary-200">
                <CardBody>
                    <p className="text-center text-sm">
                        <strong>Resumen:</strong> Se crearán{" "}
                        {packagingSummary.length} packing(s) con un total de{" "}
                        {totalQuantity} piezas distribuidas en {totalBoxes}{" "}
                        cajas y {totalBags} bolsas.
                    </p>
                </CardBody>
            </Card>
        </div>
    );
}
