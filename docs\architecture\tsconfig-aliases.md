# Configuración de Alias para TypeScript

Añade estas configuraciones a tu `tsconfig.json`:

```json
{
  "compilerOptions": {
    // ... otras opciones existentes ...
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/app/*": ["./app/*"],
      "@/features/*": ["./features/*"],
      "@/shared/*": ["./shared/*"],
      "@/core/*": ["./core/*"],
      "@/prisma/*": ["./prisma/*"],
      "@/public/*": ["./public/*"],
      "@/styles/*": ["./styles/*"],
      "@/tests/*": ["./tests/*"]
    }
  }
}
```

## Ejemplo de uso:

```typescript
// Antes
import { OrderList } from '../../../components/orders/OrderList';
import { useOrders } from '../../../lib/hooks/useOrders';

// Después
import { OrderList } from '@/features/orders/components/OrderList';
import { useOrders } from '@/features/orders/hooks/useOrders';
```

## Configuración adicional para Next.js

Si usas imports absolutos, también actualiza `next.config.js`:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // ... otras configuraciones ...
  experimental: {
    // Habilita imports de TypeScript paths
    typedRoutes: true,
  },
}

module.exports = nextConfig
```
