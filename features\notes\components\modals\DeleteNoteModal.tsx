"use client";

import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "@heroui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface DeleteNoteModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    isDeleting?: boolean;
    noteContent?: string;
}

export function DeleteNoteModal({
    isOpen,
    onClose,
    onConfirm,
    isDeleting = false,
    noteContent = "esta nota",
}: DeleteNoteModalProps) {
    return (
        <Modal
            backdrop="blur"
            classNames={{
                base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                header: "border-b border-gray-200 dark:border-gray-700",
                body: "py-6",
                footer: "border-t border-gray-200 dark:border-gray-700",
            }}
            isOpen={isOpen}
            placement="center"
            onClose={onClose}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">
                            <div className="flex items-center gap-2">
                                <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
                                <span>Confirmar eliminación</span>
                            </div>
                        </ModalHeader>
                        <ModalBody>
                            <motion.div
                                animate={{ opacity: 1, scale: 1 }}
                                className="text-center"
                                initial={{ opacity: 0, scale: 0.95 }}
                            >
                                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/20 mb-4">
                                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                                </div>
                                <p className="text-gray-700 dark:text-gray-300">
                                    ¿Estás seguro de que deseas eliminar{" "}
                                    {noteContent}?
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                    Esta acción no se puede deshacer.
                                </p>
                            </motion.div>
                        </ModalBody>
                        <ModalFooter>
                            <Button
                                isDisabled={isDeleting}
                                variant="flat"
                                onPress={onClose}
                            >
                                Cancelar
                            </Button>
                            <Button
                                color="danger"
                                isLoading={isDeleting}
                                onPress={onConfirm}
                            >
                                {isDeleting ? "Eliminando..." : "Eliminar"}
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
