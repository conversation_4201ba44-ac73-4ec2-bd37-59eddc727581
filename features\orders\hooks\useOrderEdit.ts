"use client";

import type { SWRConfiguration } from "swr";

import useSWR from "swr";

import { getOrderEditData } from "@/features/orders/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

// Obtener helpers de revalidación para el cliente
const { revalidateData, useRevalidationListener } =
    createClientRevalidation("order");

/**
 * Hook para obtener todos los datos necesarios para editar una orden
 * @param orderId ID de la orden a editar
 * @param config Configuración opcional para SWR
 * @returns Objeto con datos del formulario, datos de la orden, estado de carga y errores
 */
export function useOrderEdit(
    orderId: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Escuchar eventos de revalidación
    const isRevalidating = useRevalidationListener(orderId || undefined);

    // Configuración por defecto
    const swrConfig: SWRConfiguration = {
        revalidateOnFocus: false,
        revalidateOnMount: true,
        dedupingInterval: 5000,
        ...config,
    };

    // Utilizar Server Action como fetcher con key array pattern
    const { data, error, isLoading, mutate } = useSWR(
        orderId ? ["orderEdit", orderId] : null,
        async () => (orderId ? getOrderEditData(orderId) : null),
        swrConfig,
    );

    // Extraer datos de forma segura
    const formData = data?.data?.formData || null;
    const orderData = data?.data?.orderData || null;

    // Procesar datos del formulario
    const customers = formData?.customers || [];
    const orderStatuses = formData?.orderStatuses || [];
    const models = formData?.models || [];
    const colors =
        formData?.colors?.map((c: any) => ({
            ...c,
            name: c.name.charAt(0).toUpperCase() + c.name.slice(1),
        })) || [];
    const sizes = formData?.sizes || [];
    const noteStatuses = formData?.noteStatuses || [];
    const noteImportances = formData?.noteImportances || [];

    return {
        // Datos del formulario
        formData: {
            customers,
            orderStatuses,
            models,
            colors,
            sizes,
            noteStatuses,
            noteImportances,
        },
        // Datos de la orden
        orderData,
        // Estados
        isLoading,
        isRevalidating,
        isError: !!error || data?.success === false,
        error: data?.error || error,
        // Función para revalidar manualmente
        mutate,
        revalidate: async () => {
            await mutate();
            if (orderId) {
                await revalidateData(orderId);
            }
        },
    };
}
