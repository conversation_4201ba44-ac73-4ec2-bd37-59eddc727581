// Configuración global de animaciones para feedback visual

export const animations = {
    // Transiciones de página/paso
    pageTransition: {
        initial: { opacity: 0, x: 20 },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: -20 },
        transition: {
            duration: 0.3,
            ease: "easeInOut",
        },
    },

    // Aparición de elementos
    fadeIn: {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: {
            duration: 0.2,
            ease: "easeOut",
        },
    },

    // Hover de cards
    cardHover: {
        whileHover: {
            scale: 1.02,
            boxShadow: "0 10px 30px rgba(0, 0, 0, 0.1)",
        },
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 20,
        },
    },

    // Pulse para elementos importantes
    pulse: {
        animate: {
            scale: [1, 1.05, 1],
            opacity: [1, 0.8, 1],
        },
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
        },
    },

    // Loading shimmer
    shimmer: {
        animate: {
            backgroundPosition: ["200% 0", "-200% 0"],
        },
        transition: {
            duration: 1.5,
            repeat: Infinity,
            ease: "linear",
        },
    },

    // Success checkmark
    successCheck: {
        initial: { scale: 0, rotate: -180 },
        animate: { scale: 1, rotate: 0 },
        transition: {
            type: "spring",
            stiffness: 200,
            damping: 15,
        },
    },
};

// Variantes para listas stagger
export const listVariants = {
    container: {
        hidden: { opacity: 0 },
        show: {
            opacity: 1,
            transition: {
                staggerChildren: 0.05,
            },
        },
    },
    item: {
        hidden: { opacity: 0, x: -10 },
        show: { opacity: 1, x: 0 },
    },
};
