import { Customer } from '@prisma/client';

// Mock types for testing
interface CustomerWithChildren extends Customer {
  children: Customer[];
  parent?: Customer;
}

// Helper functions to test
function buildCustomerHierarchy(customers: Customer[]): CustomerWithChildren[] {
  const customerMap = new Map<string, CustomerWithChildren>();
  const rootCustomers: CustomerWithChildren[] = [];

  // Initialize all customers with empty children array
  customers.forEach(customer => {
    customerMap.set(customer.id, {
      ...customer,
      children: []
    });
  });

  // Build hierarchy
  customers.forEach(customer => {
    const customerWithChildren = customerMap.get(customer.id)!;
    
    if (customer.parentId) {
      const parent = customerMap.get(customer.parentId);
      if (parent) {
        parent.children.push(customerWithChildren);
        customerWithChildren.parent = parent;
      }
    } else {
      rootCustomers.push(customerWithChildren);
    }
  });

  return rootCustomers;
}

function flattenCustomerHierarchy(customers: CustomerWithChildren[]): Array<{
  key: string;
  label: string;
  customerId: string;
  subCustomerId?: string;
  isParent: boolean;
}> {
  const result: Array<{
    key: string;
    label: string;
    customerId: string;
    subCustomerId?: string;
    isParent: boolean;
  }> = [];

  customers.forEach((customer) => {
    // Add parent customer
    result.push({
      key: customer.id,
      label: customer.name,
      customerId: customer.id,
      isParent: true
    });
    
    // Add sub-clients
    customer.children?.forEach((child) => {
      result.push({
        key: `${customer.id}:${child.id}`,
        label: `${customer.name} > ${child.name}`,
        customerId: customer.id,
        subCustomerId: child.id,
        isParent: false
      });
    });
  });

  return result;
}

function validateCustomerRelationship(
  customerId: string,
  subCustomerId: string | null,
  customers: Customer[]
): { valid: boolean; error?: string } {
  if (!customerId) {
    return { valid: false, error: 'Customer ID is required' };
  }

  const customer = customers.find(c => c.id === customerId);
  if (!customer) {
    return { valid: false, error: 'Customer not found' };
  }

  if (!subCustomerId) {
    return { valid: true };
  }

  const subCustomer = customers.find(c => c.id === subCustomerId);
  if (!subCustomer) {
    return { valid: false, error: 'Sub-customer not found' };
  }

  if (subCustomer.parentId !== customerId) {
    return { valid: false, error: 'Sub-customer does not belong to the selected customer' };
  }

  return { valid: true };
}

describe('Customer Hierarchy Helpers', () => {
  const mockCustomers: Customer[] = [
    {
      id: 'becktel-id',
      name: 'Becktel',
      email: '<EMAIL>',
      phone: '1234567890',
      address: '123 Main St',
      city: 'New York',
      country: 'USA',
      zipCode: '10001',
      createdAt: new Date(),
      updatedAt: new Date(),
      parentId: null,
      displayName: null,
      packingSettings: null
    },
    {
      id: 'reebok-id',
      name: 'Reebok',
      email: '<EMAIL>',
      phone: '0987654321',
      address: '456 Sports Ave',
      city: 'Boston',
      country: 'USA',
      zipCode: '02101',
      createdAt: new Date(),
      updatedAt: new Date(),
      parentId: 'becktel-id',
      displayName: null,
      packingSettings: null
    },
    {
      id: 'nike-id',
      name: 'Nike',
      email: '<EMAIL>',
      phone: '5555555555',
      address: '789 Athletic Way',
      city: 'Portland',
      country: 'USA',
      zipCode: '97201',
      createdAt: new Date(),
      updatedAt: new Date(),
      parentId: 'becktel-id',
      displayName: null,
      packingSettings: null
    },
    {
      id: 'standalone-id',
      name: 'Standalone Corp',
      email: '<EMAIL>',
      phone: '1111111111',
      address: '999 Independent St',
      city: 'Chicago',
      country: 'USA',
      zipCode: '60601',
      createdAt: new Date(),
      updatedAt: new Date(),
      parentId: null,
      displayName: null,
      packingSettings: null
    }
  ];

  describe('buildCustomerHierarchy', () => {
    it('should build correct hierarchy from flat customer list', () => {
      const hierarchy = buildCustomerHierarchy(mockCustomers);

      expect(hierarchy).toHaveLength(2); // Two root customers
      
      const becktel = hierarchy.find(c => c.id === 'becktel-id');
      expect(becktel).toBeDefined();
      expect(becktel!.children).toHaveLength(2); // Reebok and Nike
      expect(becktel!.children.map(c => c.id)).toContain('reebok-id');
      expect(becktel!.children.map(c => c.id)).toContain('nike-id');
      
      const standalone = hierarchy.find(c => c.id === 'standalone-id');
      expect(standalone).toBeDefined();
      expect(standalone!.children).toHaveLength(0);
    });

    it('should handle empty customer list', () => {
      const hierarchy = buildCustomerHierarchy([]);
      expect(hierarchy).toHaveLength(0);
    });

    it('should handle single customer without parent', () => {
      const singleCustomer = [mockCustomers[3]]; // Standalone Corp
      const hierarchy = buildCustomerHierarchy(singleCustomer);
      
      expect(hierarchy).toHaveLength(1);
      expect(hierarchy[0].id).toBe('standalone-id');
      expect(hierarchy[0].children).toHaveLength(0);
    });

    it('should set parent references correctly', () => {
      const hierarchy = buildCustomerHierarchy(mockCustomers);
      const becktel = hierarchy.find(c => c.id === 'becktel-id');
      
      const reebok = becktel?.children.find(c => c.id === 'reebok-id');
      expect(reebok?.parent?.id).toBe('becktel-id');
      
      const nike = becktel?.children.find(c => c.id === 'nike-id');
      expect(nike?.parent?.id).toBe('becktel-id');
    });
  });

  describe('flattenCustomerHierarchy', () => {
    it('should flatten hierarchy with correct labels', () => {
      const hierarchy = buildCustomerHierarchy(mockCustomers);
      const flattened = flattenCustomerHierarchy(hierarchy);

      expect(flattened).toHaveLength(4); // 2 parents + 2 children
      
      // Check parent entries
      const becktelEntry = flattened.find(e => e.key === 'becktel-id');
      expect(becktelEntry).toEqual({
        key: 'becktel-id',
        label: 'Becktel',
        customerId: 'becktel-id',
        isParent: true
      });
      
      // Check child entries
      const reebokEntry = flattened.find(e => e.key === 'becktel-id:reebok-id');
      expect(reebokEntry).toEqual({
        key: 'becktel-id:reebok-id',
        label: 'Becktel > Reebok',
        customerId: 'becktel-id',
        subCustomerId: 'reebok-id',
        isParent: false
      });
    });

    it('should handle customers without children', () => {
      const hierarchy = buildCustomerHierarchy([mockCustomers[3]]); // Standalone only
      const flattened = flattenCustomerHierarchy(hierarchy);

      expect(flattened).toHaveLength(1);
      expect(flattened[0]).toEqual({
        key: 'standalone-id',
        label: 'Standalone Corp',
        customerId: 'standalone-id',
        isParent: true
      });
    });

    it('should preserve order of customers and children', () => {
      const hierarchy = buildCustomerHierarchy(mockCustomers);
      const flattened = flattenCustomerHierarchy(hierarchy);

      // Should be: Becktel, Becktel > Reebok, Becktel > Nike, Standalone Corp
      const labels = flattened.map(e => e.label);
      expect(labels[0]).toBe('Becktel');
      expect(labels[1]).toBe('Becktel > Reebok');
      expect(labels[2]).toBe('Becktel > Nike');
      expect(labels[3]).toBe('Standalone Corp');
    });
  });

  describe('validateCustomerRelationship', () => {
    it('should validate correct parent-child relationship', () => {
      const result = validateCustomerRelationship('becktel-id', 'reebok-id', mockCustomers);
      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate parent without sub-customer', () => {
      const result = validateCustomerRelationship('becktel-id', null, mockCustomers);
      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject invalid parent-child relationship', () => {
      const result = validateCustomerRelationship('standalone-id', 'reebok-id', mockCustomers);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Sub-customer does not belong to the selected customer');
    });

    it('should reject non-existent customer', () => {
      const result = validateCustomerRelationship('invalid-id', null, mockCustomers);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Customer not found');
    });

    it('should reject non-existent sub-customer', () => {
      const result = validateCustomerRelationship('becktel-id', 'invalid-id', mockCustomers);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Sub-customer not found');
    });

    it('should reject empty customer ID', () => {
      const result = validateCustomerRelationship('', null, mockCustomers);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Customer ID is required');
    });

    it('should reject when sub-customer is actually a parent', () => {
      const result = validateCustomerRelationship('reebok-id', 'becktel-id', mockCustomers);
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Sub-customer does not belong to the selected customer');
    });
  });
});