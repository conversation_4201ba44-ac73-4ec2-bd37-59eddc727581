// features/assignments/types/assignment.types.ts
// Types principales de la feature de assignments

// Re-exportar types desde actions (temporal hasta migrar)
export * from "./types";

// Aquí puedes agregar nuevos types específicos de assignments
export interface AssignmentFilters {
    contractorId?: string;
    status?: AssignmentStatus;
    dateFrom?: Date;
    dateTo?: Date;
}

export type AssignmentStatus =
    | "pending"
    | "in_progress"
    | "completed"
    | "cancelled";

export interface AssignmentWizardData {
    contractorId?: string;
    orders?: string[];
    items?: AssignmentItem[];
    notes?: string;
    estimatedDeliveryDate?: Date;
}

export interface AssignmentItem {
    orderId: string;
    garmentId: string;
    quantity: number;
    colorId: string;
    sizeId: string;
}

// Draft types para guardado temporal
export interface AssignmentDraft {
    id: string;
    wizardData: AssignmentWizardData;
    createdAt: Date;
    updatedAt: Date;
    expiresAt: Date;
    userId?: string;
}

export interface DraftState {
    contractor: string | null;
    contractorData: any | null;
    selectedOrders: string[];
    assignments: any[];
    currentStep: string;
}
