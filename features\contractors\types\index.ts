export interface Contractor {
    id: string;
    name: string;
    firstName?: string | null;
    lastName?: string | null;
    middleName?: string | null;
    secondLastName?: string | null;
    email?: string | null;
    phone?: string | null;
    notes?: string | null;
    address?: string | null;
    status?: string;
    createdAt: string;
    updatedAt: string;
    deletedAt?: string | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    _count?: {
        assignments: number;
        remissions: number;
    };
}

export interface ContractorMetrics {
    activeAssignments: number;
    completedAssignments: number;
    totalPieces: number;
    completionRate: number;
    lastAssignmentDate?: Date | null;
}

export interface ContractorWithMetrics extends Contractor {
    metrics?: ContractorMetrics;
}

export interface ContractorFormData {
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    status?: string;
}

export interface ContractorResponse {
    success: boolean;
    data?: Contractor;
    error?: string;
}
