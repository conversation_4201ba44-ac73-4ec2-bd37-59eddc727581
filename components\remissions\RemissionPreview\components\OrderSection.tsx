"use client";

import type { OrderDetails } from "@/types/remission/core";

import { CubeIcon } from "@heroicons/react/24/outline";

interface OrderSectionProps {
    order: OrderDetails;
    showParts?: boolean;
}

export function OrderSection({ order, showParts = true }: OrderSectionProps) {
    return (
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 mb-3">
                <CubeIcon className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold text-primary">
                    INFORMACIÓN DE LA ORDEN
                </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label className="text-sm text-gray-600">Orden:</label>
                    <p className="font-medium">#{order.id}</p>
                </div>

                {order.cutOrder && (
                    <div>
                        <label className="text-sm text-gray-600">Corte:</label>
                        <p className="font-medium">{order.cutOrder}</p>
                    </div>
                )}

                <div>
                    <label className="text-sm text-gray-600">
                        Fecha de creación:
                    </label>
                    <p className="font-medium">
                        {new Date(order.creationDate).toLocaleDateString(
                            "es-MX",
                        )}
                    </p>
                </div>

                {showParts && order.parts && order.parts.length > 0 && (
                    <div>
                        <label className="text-sm text-gray-600">Partes:</label>
                        <p className="font-medium">
                            {order.parts.map((p) => p.code).join(", ")}
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
}
