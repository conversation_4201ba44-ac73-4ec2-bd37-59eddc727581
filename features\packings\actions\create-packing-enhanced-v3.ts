"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";

import { generatePackingFolio } from "./generate-folio";

const CreatePackingSchema = z.object({
    deliveryDate: z.string(),
    customerId: z.string(),
    subCustomerId: z.string().optional(),
    orderIds: z.array(z.string()).min(1),
    notes: z.string().optional(),
    packagingSummaries: z.array(
        z.object({
            orderId: z.string(),
            piecesPerBox: z.number().min(1),
            piecesPerBag: z.number().min(1),
            sizeBreakdown: z.array(
                z.object({
                    size: z.string(),
                    boxes: z.number(),
                    loosePieces: z.number(),
                    bagsFirst: z.number(),
                    bagsSecond: z.number(),
                }),
            ),
        }),
    ),
    details: z.array(
        z.object({
            modelCode: z.string(),
            modelName: z.string(),
            colorCode: z.string(),
            colorName: z.string(),
            partNumber: z.string(),
            sizeName: z.string(),
            quantity: z.number().min(1),
            qualityType: z.enum([
                "primera",
                "segunda",
                "manchada",
                "incompleta",
            ]),
            garmentSizeId: z.string(),
        }),
    ),
});

export async function createPackingEnhancedV3(
    data: z.infer<typeof CreatePackingSchema>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            throw new Error("Usuario no autenticado");
        }

        const validatedData = CreatePackingSchema.parse(data);

        // Validate customer relationships
        if (validatedData.subCustomerId) {
            const subCustomer = await db.customer.findFirst({
                where: {
                    id: validatedData.subCustomerId,
                    parentId: validatedData.customerId,
                },
            });

            if (!subCustomer) {
                throw new Error(
                    "El subcliente no pertenece al cliente principal",
                );
            }
        }

        // Get company settings
        const companySettings = await db.companySettings.findFirst();

        if (!companySettings) {
            throw new Error("Configuración de empresa no encontrada");
        }

        const result = await db.$transaction(async (tx) => {
            // Generate folio
            const folio = await generatePackingFolio();
            const code = `PCK-${Date.now()}`;

            // Calculate totals
            let totalBoxes = 0;
            let totalBags = 0;
            const packingSummaryBySize: Record<string, any> = {};

            validatedData.packagingSummaries.forEach((summary) => {
                packingSummaryBySize[summary.orderId] = {};

                summary.sizeBreakdown.forEach((size) => {
                    totalBoxes += size.boxes;
                    totalBags += size.bagsFirst + size.bagsSecond;

                    packingSummaryBySize[summary.orderId][size.size] = {
                        boxes: size.boxes,
                        loosePieces: size.loosePieces,
                        bagsFirst: size.bagsFirst,
                        bagsSecond: size.bagsSecond,
                    };
                });
            });

            // Create packing
            const packing = await tx.packing.create({
                data: {
                    folio,
                    code,
                    deliveryDate: new Date(validatedData.deliveryDate),
                    customerId: validatedData.customerId,
                    subCustomerId: validatedData.subCustomerId,
                    orderId: validatedData.orderIds[0], // Primary order
                    notes: validatedData.notes,
                    totalBoxes,
                    totalBags,
                    packingType: "mixed",
                    packingSummaryBySize,
                    companyInfo: {
                        name: companySettings.companyName,
                        logo: (companySettings as any).logo,
                        rfc: companySettings.rfc,
                        address: companySettings.address,
                    },
                    statusId: "draft",
                },
            });

            // Create packing details
            const packingDetails = await Promise.all(
                validatedData.details.map((detail) =>
                    tx.packingDetail.create({
                        data: {
                            packingId: packing.id,
                            ...detail,
                            packagingType: "box",
                            packagingUnits: 0, // Will be calculated
                            piecesPerUnit: 50,
                            loosePieces: 0,
                        },
                    }),
                ),
            );

            // Create packing summaries
            const packingSummaries = await Promise.all(
                validatedData.packagingSummaries.map((summary) => {
                    const orderDetails = validatedData.details.filter(
                        (d) =>
                            // Match details to order based on your logic
                            true,
                    );

                    const totals = orderDetails.reduce(
                        (acc, detail) => {
                            if (detail.qualityType === "primera")
                                acc.totalFirstQuality += detail.quantity;
                            else if (detail.qualityType === "segunda")
                                acc.totalSecondQuality += detail.quantity;
                            else if (detail.qualityType === "manchada")
                                acc.totalDefective += detail.quantity;
                            else if (detail.qualityType === "incompleta")
                                acc.totalIncomplete += detail.quantity;

                            return acc;
                        },
                        {
                            totalFirstQuality: 0,
                            totalSecondQuality: 0,
                            totalDefective: 0,
                            totalIncomplete: 0,
                        },
                    );

                    const summaryTotals = summary.sizeBreakdown.reduce(
                        (acc, size) => ({
                            totalBoxes: acc.totalBoxes + size.boxes,
                            totalBagsFirst: acc.totalBagsFirst + size.bagsFirst,
                            totalBagsSecond:
                                acc.totalBagsSecond + size.bagsSecond,
                            totalLoosePieces:
                                acc.totalLoosePieces + size.loosePieces,
                        }),
                        {
                            totalBoxes: 0,
                            totalBagsFirst: 0,
                            totalBagsSecond: 0,
                            totalLoosePieces: 0,
                        },
                    );

                    return tx.packingSummary.create({
                        data: {
                            packingId: packing.id,
                            orderId: summary.orderId,
                            ...totals,
                            ...summaryTotals,
                        },
                    });
                }),
            );

            // No necesitamos actualizar inventory aquí
            // Las cantidades empacadas se rastrean a través de PackingDetail
            // La disponibilidad se calcula dinámicamente sumando los PackingDetail

            // Create history entry
            await tx.packingHistory.create({
                data: {
                    packingId: packing.id,
                    action: "created",
                    metadata: {
                        description: "Packing creado",
                        userId: session.user.id,
                        totalItems: validatedData.details.length,
                        totalQuantity: validatedData.details.reduce(
                            (acc, d) => acc + d.quantity,
                            0,
                        ),
                    },
                },
            });

            return packing;
        });

        revalidatePath("/dashboard/packings");

        return { success: true, data: result };
    } catch (error) {
        console.error("Error creating packing:", error);

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al crear packing",
        };
    }
}
