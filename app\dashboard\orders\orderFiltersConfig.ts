export const filters = [
    {
        key: "status",
        label: "Estado",
        type: "select" as const,
        placeholder: "Todos los estados",
        options: [
            { value: "all", label: "Todos" },
            { value: "pending", label: "Pendiente" },
            { value: "in_progress", label: "En Proceso" },
            { value: "in_production", label: "En Producción" },
            { value: "delivered", label: "Entregada" },
            { value: "cancelled", label: "Cancelada" },
        ],
    },
    {
        key: "period",
        label: "Período",
        type: "select" as const,
        placeholder: "Seleccionar período",
        options: [
            { value: "all", label: "Todo el tiempo" },
            { value: "7d", label: "Últimos 7 días" },
            { value: "30d", label: "Últimos 30 días" },
            { value: "90d", label: "Últimos 90 días" },
        ],
    },
    {
        key: "urgency",
        label: "Urgencia",
        type: "select" as const,
        placeholder: "Filtrar por urgencia",
        options: [
            { value: "all", label: "Todas" },
            { value: "overdue", label: "Retrasadas" },
            { value: "today", label: "Vencen hoy" },
            { value: "week", label: "Vencen esta semana" },
        ],
    },
];

export const sortOptions = [
    {
        key: "cutOrder-asc",
        label: "Número de Corte A-Z",
        field: "cutOrder",
        direction: "asc" as const,
    },
    {
        key: "cutOrder-desc",
        label: "Número de Corte Z-A",
        field: "cutOrder",
        direction: "desc" as const,
    },
    {
        key: "deliveryDate-asc",
        label: "Entrega más próxima",
        field: "deliveryDate",
        direction: "asc" as const,
    },
    {
        key: "deliveryDate-desc",
        label: "Entrega más lejana",
        field: "deliveryDate",
        direction: "desc" as const,
    },
    {
        key: "createdAt-desc",
        label: "Más reciente",
        field: "createdAt",
        direction: "desc" as const,
    },
    {
        key: "createdAt-asc",
        label: "Más antigua",
        field: "createdAt",
        direction: "asc" as const,
    },
    {
        key: "quantity-desc",
        label: "Mayor cantidad",
        field: "quantity",
        direction: "desc" as const,
    },
    {
        key: "quantity-asc",
        label: "Menor cantidad",
        field: "quantity",
        direction: "asc" as const,
    },
];
