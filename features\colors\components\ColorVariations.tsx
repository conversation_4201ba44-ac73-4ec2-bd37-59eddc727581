"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, Tooltip } from "@heroui/react";
import { ClipboardIcon } from "@heroicons/react/24/outline";

import { generateColorVariations, isColorDark } from "../utils/colorUtils";

interface ColorVariationsProps {
    hexCode: string;
    onSelect?: (color: string) => void;
    className?: string;
}

export function ColorVariations({
    hexCode,
    onSelect,
    className = "",
}: ColorVariationsProps) {
    const variations = generateColorVariations(hexCode, 5);

    const handleCopy = async (color: string, e: React.MouseEvent) => {
        e.stopPropagation();
        try {
            await navigator.clipboard.writeText(color);
            // Se podría añadir un toast aquí
        } catch (err) {
            // REMOVED: console.error("Error al copiar", err);
        }
    };

    return (
        <div className={`flex flex-wrap gap-2 ${className}`}>
            {variations.map((color, index) => {
                const isDark = isColorDark(color);
                const isOriginal =
                    color.toLowerCase() === hexCode.toLowerCase();

                return (
                    <Tooltip
                        key={`${color}-${index}`}
                        content={
                            <div className="text-center">
                                <p className="font-mono text-xs">{color}</p>
                                <p className="text-xs opacity-80">
                                    {isOriginal
                                        ? "Original"
                                        : index < variations.indexOf(hexCode)
                                          ? `${100 - (index + 1) * 20}% más claro`
                                          : `${(index - variations.indexOf(hexCode)) * 20}% más oscuro`}
                                </p>
                            </div>
                        }
                    >
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className={`relative rounded-md overflow-hidden cursor-pointer border ${isOriginal ? "border-blue-500 dark:border-blue-400" : "border-gray-200 dark:border-gray-700"}`}
                            initial={{ opacity: 0, y: 5 }}
                            style={{
                                width: isOriginal ? "50px" : "38px",
                                height: isOriginal ? "50px" : "38px",
                            }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                            whileHover={{ scale: 1.1, y: -2 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => onSelect && onSelect(color)}
                        >
                            <div
                                className="w-full h-full flex items-center justify-center"
                                style={{ backgroundColor: color }}
                            >
                                {isOriginal && (
                                    <Badge
                                        className={`text-[9px] ${isDark ? "text-white" : "text-gray-900"}`}
                                        color="default"
                                        style={{
                                            backgroundColor: "transparent",
                                            opacity: 0.9,
                                        }}
                                        variant="flat"
                                    >
                                        100%
                                    </Badge>
                                )}

                                <motion.div
                                    className="absolute top-1 right-1 opacity-0 hover:opacity-100 transition-opacity"
                                    whileHover={{ scale: 1.2 }}
                                >
                                    <div
                                        className={`rounded-full p-1 ${isDark ? "bg-white/20 text-white" : "bg-black/20 text-black"}`}
                                        onClick={(e) => handleCopy(color, e)}
                                    >
                                        <ClipboardIcon className="w-3 h-3" />
                                    </div>
                                </motion.div>
                            </div>
                        </motion.div>
                    </Tooltip>
                );
            })}
        </div>
    );
}
