"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardBody, Button } from "@heroui/react";
import { DocumentPlusIcon, InboxIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

import { PackingCard } from "./PackingCard";

interface PackingGridProps {
    packings: any[];
    isLoading?: boolean;
    onViewDetails?: (packing: any) => void;
    onPrint?: (packing: any) => void;
    viewMode?: "grid" | "list";
}

export function PackingGrid({
    packings,
    isLoading = false,
    onViewDetails,
    onPrint,
    viewMode = "grid",
}: PackingGridProps) {
    const router = useRouter();

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(6)].map((_, index) => (
                    <Card key={index} className="animate-pulse">
                        <CardBody className="h-64">
                            <div className="space-y-3">
                                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
                                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
                                <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded" />
                                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full" />
                            </div>
                        </CardBody>
                    </Card>
                ))}
            </div>
        );
    }

    if (packings.length === 0) {
        return (
            <Card className="bg-gray-50 dark:bg-gray-900/50">
                <CardBody className="text-center py-16">
                    <InboxIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold mb-2">
                        No hay packings registrados
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-6">
                        Comienza creando tu primer packing list
                    </p>
                    <Button
                        color="primary"
                        startContent={<DocumentPlusIcon className="w-5 h-5" />}
                        onPress={() => router.push("/dashboard/packings/new")}
                    >
                        Crear Packing
                    </Button>
                </CardBody>
            </Card>
        );
    }

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.05,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 100,
                damping: 15,
            },
        },
    };

    return (
        <AnimatePresence mode="wait">
            <motion.div
                key={viewMode}
                animate="visible"
                className={
                    viewMode === "grid"
                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                        : "space-y-4"
                }
                exit="hidden"
                initial="hidden"
                variants={containerVariants}
            >
                {packings.map((packing) => (
                    <motion.div key={packing.id} layout variants={itemVariants}>
                        <PackingCard
                            packing={packing}
                            onPrint={() => onPrint?.(packing)}
                            onViewDetails={() => onViewDetails?.(packing)}
                        />
                    </motion.div>
                ))}
            </motion.div>
        </AnimatePresence>
    );
}
