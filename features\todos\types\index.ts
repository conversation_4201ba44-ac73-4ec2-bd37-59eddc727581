export interface Todo {
    id: string;
    title: string;
    description?: string | null;
    completed: boolean;
    priority: "low" | "medium" | "high";
    dueDate?: Date | string | null;
    createdAt: Date | string;
    updatedAt: Date | string;
    deletedAt?: Date | string | null;
    userId?: string | null;
}

export interface TodosResponse {
    todos: Todo[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

export interface CreateTodoInput {
    title: string;
    description?: string;
    priority?: "low" | "medium" | "high";
    dueDate?: Date | string;
}

export interface UpdateTodoInput {
    title?: string;
    description?: string;
    completed?: boolean;
    priority?: "low" | "medium" | "high";
    dueDate?: Date | string;
}

export interface TodoFilters {
    search?: string;
    completed?: boolean;
    priority?: "low" | "medium" | "high";
    orderBy?: "createdAt" | "updatedAt" | "dueDate" | "priority";
    order?: "asc" | "desc";
    page?: number;
    perPage?: number;
}
