/* Estilos para el documento de remisión compacto y elegante */
.remissionDocumentCompact {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    color: #000;
    background-color: #fff;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

/* Diseño compacto para ContractorInfo cuando está en modo compacto */
.compact-contractor-info :global(.relative.overflow-hidden) {
    padding: 0 !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

.compact-contractor-info :global(.flex.items-center.gap-3) {
    gap: 0.5rem !important;
    margin-bottom: 0 !important;
}

.compact-contractor-info :global(.w-16.h-16) {
    width: 2rem !important;
    height: 2rem !important;
}

.compact-contractor-info :global(.text-xl) {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
}

.compact-contractor-info :global(.text-sm) {
    font-size: 0.625rem !important;
    line-height: 0.875rem !important;
}

.compact-contractor-info :global(.text-tiny) {
    font-size: 0.5rem !important;
}

/* Estilos para impresión de alta calidad */
@media print, (min-resolution: 300dpi) {
    .remissionDocumentCompact {
        font-size: 9pt !important;
        line-height: 1.2 !important;
        color: #000 !important;
    }
    
    .remissionDocumentCompact table {
        font-size: 8pt !important;
        border-color: #666 !important;
    }
    
    .remissionDocumentCompact h1 {
        font-size: 14pt !important;
        color: #000 !important;
        letter-spacing: 0.05em !important;
    }
    
    .remissionDocumentCompact h2 {
        font-size: 10pt !important;
        color: #374151 !important;
        letter-spacing: 0.05em !important;
    }
    
    /* Asegurar que los bordes se impriman */
    .remissionDocumentCompact td,
    .remissionDocumentCompact th {
        border: 0.5pt solid #999 !important;
    }
    
    /* Fondo oscuro para encabezados */
    .remissionDocumentCompact thead {
        background-color: #1f2937 !important;
        color: white !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* Estilos para modo landscape compacto */
.landscapeModeCompact {
    width: 297mm !important;
    max-width: 297mm !important;
    margin: 0 auto;
}

/* Ajustes de fuente para landscape compacto */
.landscape-document-compact {
    font-size: 9pt !important;
    line-height: 1.2 !important;
}

.landscape-document-compact table {
    font-size: 8pt !important;
}

.landscape-document-compact th,
.landscape-document-compact td {
    padding: 2px 4px !important;
}

.landscape-document-compact h1 {
    font-size: 14pt !important;
    margin-bottom: 4px !important;
}

.landscape-document-compact h2 {
    font-size: 10pt !important;
    margin-bottom: 4px !important;
}

/* Tabla ultra compacta para impresión */
.compactRemissionTable {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.compactRemissionTable th {
    background-color: #1f2937 !important;
    color: white !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.625rem;
    padding: 0.25rem;
    border: 0.5pt solid #374151;
    letter-spacing: 0.05em;
}

.compactRemissionTable td {
    padding: 0.125rem 0.25rem;
    border: 0.5pt solid #d1d5db;
    font-size: 0.625rem;
}

/* Filas alternadas más sutiles */
.compactRemissionTable tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

/* Optimización para PDF con diseño compacto */
.pdf-generating-compact {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    background-color: white !important;
}

.pdf-generating-compact * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Clase para el documento durante generación de PDF */
.pdf-generating-compact .remission-document-compact {
    box-shadow: none !important;
    border: none !important;
    page-break-inside: avoid !important;
}

/* Mejorar visibilidad de tablas en PDF */
.pdf-generating-compact table {
    border-collapse: collapse !important;
}

.pdf-generating-compact th,
.pdf-generating-compact td {
    border: 0.5pt solid #999 !important;
    padding: 4px !important;
}

.pdf-generating-compact th {
    background-color: #1f2937 !important;
    color: white !important;
    font-weight: 600 !important;
}

.pdf-generating-compact tr:nth-child(even) {
    background-color: #f9fafb !important;
}

/* Estilos específicos para el modo portrait compacto */
.portrait-mode-compact {
    width: 210mm !important;
    max-width: 210mm !important;
    margin: 0 auto;
}

/* Estilos para elementos de resumen */
.summaryBox {
    background-color: #1f2937 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
}

/* Estilos para líneas de firma */
.signatureLine {
    border-bottom: 1.5pt solid #1f2937 !important;
    min-height: 40px;
}

/* Ajustes para asegurar buena impresión */
@page {
    size: A4 landscape;
    margin: 10mm;
}

@media print {
    .landscape-mode-compact {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    .portrait-mode-compact {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* Asegurar que los elementos con fondo oscuro se impriman */
    .bg-gray-800,
    .bg-gray-900 {
        background-color: #1f2937 !important;
        color: white !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* Estilos para mejorar la legibilidad */
.document-text {
    color: #111827;
    font-weight: 400;
}

.document-label {
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.document-value {
    color: #111827;
    font-weight: 600;
}

/* Bordes más elegantes */
.elegant-border {
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
}

.elegant-border-dark {
    border: 1px solid #374151;
}