"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Avatar,
    Divider,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
} from "@heroui/react";
import {
    DocumentTextIcon,
    CalendarIcon,
    PrinterIcon,
    EyeIcon,
    BuildingOfficeIcon,
    UserGroupIcon,
    CubeIcon,
    TruckIcon,
    ChevronDownIcon,
    ChevronUpIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow, isBefore } from "date-fns";
import { es } from "date-fns/locale";
import { useRouter } from "next/navigation";

import { PackingStatusBadge } from "./PackingStatusBadge";

interface PackingCardProps {
    packing: {
        id: string;
        folio: string;
        deliveryDate: string;
        createdAt: string;
        printedAt?: string | null;
        notes?: string | null;
        customer: {
            id: string;
            name: string;
        };
        subCustomer?: {
            id: string;
            name: string;
        } | null;
        status: {
            id: string;
            name: string;
            color?: string;
            iconName?: string;
        };
        details: Array<{
            id: string;
            quantity: number;
            garmentSize: {
                size: { code: string };
                garment: {
                    model: { code: string };
                    color: { name: string };
                };
            };
        }>;
        _count?: {
            details: number;
            history: number;
        };
    };
    onViewDetails?: () => void;
    onPrint?: () => void;
}

export function PackingCard({
    packing,
    onViewDetails,
    onPrint,
}: PackingCardProps) {
    const router = useRouter();
    const [isExpanded, setIsExpanded] = useState(false);

    // Calcular urgencia basada en fecha de entrega
    const deliveryDate = new Date(packing.deliveryDate);
    const today = new Date();
    const daysUntilDelivery = Math.ceil(
        (deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
    );

    const urgencyLevel = isBefore(deliveryDate, today)
        ? "overdue"
        : daysUntilDelivery <= 2
          ? "urgent"
          : daysUntilDelivery <= 7
            ? "soon"
            : "normal";

    const urgencyConfig = {
        overdue: { color: "danger", label: "Vencido", icon: ClockIcon },
        urgent: { color: "warning", label: "Urgente", icon: ClockIcon },
        soon: { color: "primary", label: "Próximo", icon: CalendarIcon },
        normal: { color: "default", label: "Normal", icon: CalendarIcon },
    };

    const urgency = urgencyConfig[urgencyLevel as keyof typeof urgencyConfig];

    // Calcular total de piezas
    const totalPieces = packing.details.reduce(
        (sum, detail) => sum + detail.quantity,
        0,
    );

    // Agrupar detalles por modelo y color
    const groupedDetails = packing.details.reduce(
        (acc, detail) => {
            const key = `${detail.garmentSize.garment.model.code}-${detail.garmentSize.garment.color.name}`;

            if (!acc[key]) {
                acc[key] = {
                    model: detail.garmentSize.garment.model.code,
                    color: detail.garmentSize.garment.color.name,
                    sizes: [],
                };
            }
            acc[key].sizes.push({
                size: detail.garmentSize.size.code,
                quantity: detail.quantity,
            });

            return acc;
        },
        {} as Record<string, any>,
    );

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.2 }}
            whileHover={{ y: -2 }}
        >
            <Card
                isPressable
                className="hover:shadow-lg transition-all duration-200"
                onPress={() => router.push(`/dashboard/packings/${packing.id}`)}
            >
                <CardHeader className="pb-3">
                    <div className="flex items-start justify-between w-full gap-4">
                        <div className="flex items-start gap-3">
                            <Avatar
                                className="flex-shrink-0"
                                color="primary"
                                name={packing.customer.name}
                                size="md"
                            />

                            <div className="space-y-1">
                                <div className="flex items-center gap-2">
                                    <h3 className="text-lg font-semibold">
                                        {packing.folio}
                                    </h3>
                                    <PackingStatusBadge
                                        size="sm"
                                        status={packing.status}
                                    />
                                </div>

                                <div className="flex items-center gap-3 text-sm text-gray-500">
                                    <span className="flex items-center gap-1">
                                        <BuildingOfficeIcon className="w-4 h-4" />
                                        {packing.customer.name}
                                    </span>
                                    {packing.subCustomer && (
                                        <>
                                            <span>•</span>
                                            <span className="flex items-center gap-1">
                                                <UserGroupIcon className="w-4 h-4" />
                                                {packing.subCustomer.name}
                                            </span>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-2">
                            <Tooltip
                                content={`${urgency.label} - ${format(deliveryDate, "PPP", { locale: es })}`}
                            >
                                <Chip
                                    color={urgency.color as any}
                                    size="sm"
                                    startContent={
                                        <urgency.icon className="w-3 h-3" />
                                    }
                                    variant="flat"
                                >
                                    {urgencyLevel === "overdue"
                                        ? `${Math.abs(daysUntilDelivery)} días vencido`
                                        : `${daysUntilDelivery} días`}
                                </Chip>
                            </Tooltip>

                            <div className="flex gap-1">
                                <Tooltip content="Ver detalles">
                                    <Button
                                        isIconOnly
                                        size="sm"
                                        variant="light"
                                        onPress={() => {
                                            onViewDetails?.();
                                        }}
                                    >
                                        <EyeIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>

                                <Tooltip content="Imprimir">
                                    <Button
                                        isIconOnly
                                        size="sm"
                                        variant="light"
                                        onPress={() => {
                                            onPrint?.();
                                        }}
                                    >
                                        <PrinterIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>
                            </div>
                        </div>
                    </div>
                </CardHeader>

                <Divider />

                <CardBody className="pt-3">
                    <div className="space-y-3">
                        {/* Estadísticas rápidas */}
                        <div className="grid grid-cols-3 gap-3">
                            <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                <CubeIcon className="w-5 h-5 mx-auto mb-1 text-gray-400" />
                                <p className="text-2xl font-bold">
                                    {totalPieces}
                                </p>
                                <p className="text-xs text-gray-500">
                                    Piezas totales
                                </p>
                            </div>

                            <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                <DocumentTextIcon className="w-5 h-5 mx-auto mb-1 text-gray-400" />
                                <p className="text-2xl font-bold">
                                    {packing._count?.details || 0}
                                </p>
                                <p className="text-xs text-gray-500">Líneas</p>
                            </div>

                            <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                <TruckIcon className="w-5 h-5 mx-auto mb-1 text-gray-400" />
                                <p className="text-sm font-semibold">
                                    {format(deliveryDate, "dd/MM", {
                                        locale: es,
                                    })}
                                </p>
                                <p className="text-xs text-gray-500">Entrega</p>
                            </div>
                        </div>

                        {/* Información adicional */}
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">
                                Creado{" "}
                                {formatDistanceToNow(
                                    new Date(packing.createdAt),
                                    {
                                        addSuffix: true,
                                        locale: es,
                                    },
                                )}
                            </span>

                            {packing.printedAt && (
                                <Chip color="success" size="sm" variant="flat">
                                    <PrinterIcon className="w-3 h-3 mr-1" />
                                    Impreso
                                </Chip>
                            )}
                        </div>

                        {/* Notas */}
                        {packing.notes && (
                            <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                <p className="text-xs text-yellow-800 dark:text-yellow-200">
                                    {packing.notes}
                                </p>
                            </div>
                        )}

                        {/* Detalles expandibles */}
                        <div>
                            <Button
                                fullWidth
                                endContent={
                                    isExpanded ? (
                                        <ChevronUpIcon className="w-4 h-4" />
                                    ) : (
                                        <ChevronDownIcon className="w-4 h-4" />
                                    )
                                }
                                size="sm"
                                variant="light"
                                onPress={() => {
                                    setIsExpanded(!isExpanded);
                                }}
                            >
                                {isExpanded ? "Ocultar" : "Mostrar"} detalles (
                                {Object.keys(groupedDetails).length} modelos)
                            </Button>

                            {isExpanded && (
                                <motion.div
                                    animate={{ opacity: 1, height: "auto" }}
                                    className="mt-2 space-y-2"
                                    exit={{ opacity: 0, height: 0 }}
                                    initial={{ opacity: 0, height: 0 }}
                                >
                                    {Object.values(groupedDetails).map(
                                        (group: any, index) => (
                                            <div
                                                key={index}
                                                className="p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg"
                                            >
                                                <div className="flex items-center justify-between mb-1">
                                                    <span className="font-medium text-sm">
                                                        {group.model} -{" "}
                                                        {group.color}
                                                    </span>
                                                    <span className="text-sm text-gray-500">
                                                        {group.sizes.reduce(
                                                            (
                                                                sum: number,
                                                                s: any,
                                                            ) =>
                                                                sum +
                                                                s.quantity,
                                                            0,
                                                        )}{" "}
                                                        pzs
                                                    </span>
                                                </div>
                                                <div className="flex flex-wrap gap-1">
                                                    {group.sizes.map(
                                                        (
                                                            size: any,
                                                            sizeIndex: number,
                                                        ) => (
                                                            <Chip
                                                                key={sizeIndex}
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {size.size}:{" "}
                                                                {size.quantity}
                                                            </Chip>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        ),
                                    )}
                                </motion.div>
                            )}
                        </div>
                    </div>
                </CardBody>
            </Card>
        </motion.div>
    );
}
