/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuración webpack para react-pdf (solo en producción para evitar conflictos con Turbopack)
  webpack: (config, { dev }) => {
    // Solo aplicar configuración webpack en builds de producción
    if (!dev) {
      config.resolve.alias.canvas = false;
      config.resolve.alias.encoding = false;
    }
    return config;
  },
  
  images: {
    domains: [
      "localhost",
      "lh3.googleusercontent.com",
      "avatars.githubusercontent.com",
      "res.cloudinary.com",
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'ui-avatars.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Configuración estricta para TypeScript y ESLint
  typescript: {
    // Forzar que el build falle si hay errores de tipos
    ignoreBuildErrors: false,
  },
  
  // Configuración estricta para ESLint
  eslint: {
    // Temporalmente ignorar errores de linting para verificar el build
    ignoreDuringBuilds: true,
  },
  
  // Configuración para Turbopack
  // Nota: En Turbopack, los alias problemáticos se manejan automáticamente
  
  // Configuraciones experimentales
  experimental: {
    // Configuramos serverActions como un objeto para especificar un timeout más largo
    serverActions: {
      bodySizeLimit: '4mb',
      // Aumentamos el timeout a 60 segundos para operaciones más complejas
      allowedOrigins: ['localhost:3000'],
      timeout: 60 * 1000, // 60 segundos
    },
    // Otras configuraciones experimentales
    optimizeCss: true,
    scrollRestoration: true,
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://www.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none';",
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/api/auth/:path*",
        destination: "/api/auth/:path*",
      },
      {
        source: "/api/trpc/:path*",
        destination: "/api/trpc/:path*",
      },
    ];
  }
};

export default nextConfig;
