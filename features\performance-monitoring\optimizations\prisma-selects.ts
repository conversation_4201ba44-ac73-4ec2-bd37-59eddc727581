/**
 * Optimized Prisma Queries
 * Implements selective field loading to reduce over-fetching
 */

/**
 * Optimized select for Order queries
 * Only fetches necessary fields
 */
export const optimizedOrderSelect = {
    id: true,
    transferNumber: true,
    cutOrder: true,
    batch: true,
    receivedDate: true,
    estimatedDeliveryDate: true,
    deliveryDate: true,
    customerId: true,
    statusId: true,
    createdAt: true,
    updatedAt: true,
    // Selective customer fields
    customer: {
        select: {
            id: true,
            name: true,
        },
    },
    // Selective status fields
    status: {
        select: {
            id: true,
            name: true,
            color: true,
            iconName: true,
        },
    },
    // Only count for parts, don't fetch all data
    _count: {
        select: {
            parts: true,
            garments: true,
            notes: true,
            packings: true,
        },
    },
} as any;

/**
 * Minimal select for list views
 */
export const minimalOrderSelect = {
    id: true,
    transferNumber: true,
    receivedDate: true,
    customer: {
        select: {
            id: true,
            name: true,
        },
    },
    status: {
        select: {
            id: true,
            name: true,
            color: true,
        },
    },
    _count: {
        select: {
            parts: true,
        },
    },
} as any;
/**
 * Additional Optimized Selects
 * For other frequently queried models
 */

/**
 * Customer select for list views
 */
export const optimizedCustomerSelect = {
    id: true,
    name: true,
    createdAt: true,
    _count: {
        select: {
            orders: true,
        },
    },
} as any;
/**
 * Garment select with minimal relations
 */
export const optimizedGarmentSelect = {
    id: true,
    modelId: true,
    colorId: true,
    orderId: true,
    model: {
        select: {
            id: true,
            code: true,
            description: true,
        },
    },
    color: {
        select: {
            id: true,
            name: true,
            hexCode: true,
        },
    },
    sizes: {
        select: {
            id: true,
            totalQuantity: true,
            usedQuantity: true,
            size: {
                select: {
                    id: true,
                    code: true,
                },
            },
        },
    },
} as any;
/**
 * Assignment select for list views
 */
export const optimizedAssignmentSelect = {
    id: true,
    quantity: true,
    garmentSizeId: true,
    userId: true,
    orderId: true,
    user: {
        select: {
            id: true,
            name: true,
            email: true,
        },
    },
} as any;
