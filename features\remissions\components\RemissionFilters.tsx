"use client";

import React from "react";
import {
    Input,
    Select,
    SelectItem,
    <PERSON>ton,
    Card,
    CardBody,
    DatePicker,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface RemissionFiltersProps {
    filters: {
        search?: string;
        status?: string;
        contractorId?: string;
        startDate?: Date;
        endDate?: Date;
    };
    onFiltersChange: (filters: any) => void;
    contractors?: Array<{
        id: string;
        name: string;
    }>;
    showCard?: boolean;
}

const statusOptions = [
    { value: "all", label: "Todos los estados" },
    { value: "active", label: "Activo" },
    { value: "pending", label: "Pendiente" },
    { value: "completed", label: "Entregado" },
    { value: "cancelled", label: "Cancelado" },
];

export default function RemissionFilters({
    filters,
    onFiltersChange,
    contractors = [],
    showCard = true,
}: RemissionFiltersProps) {
    const [localFilters, setLocalFilters] = React.useState(filters);
    const [isExpanded, setIsExpanded] = React.useState(false);

    const handleFilterChange = (key: string, value: any) => {
        const newFilters = { ...localFilters, [key]: value };

        setLocalFilters(newFilters);
        onFiltersChange(newFilters);
    };

    const handleClearFilters = () => {
        const cleared = {
            search: "",
            status: "all",
            contractorId: "all",
            startDate: undefined,
            endDate: undefined,
        };

        setLocalFilters(cleared);
        onFiltersChange(cleared);
    };

    const hasActiveFilters =
        localFilters.search ||
        (localFilters.status && localFilters.status !== "all") ||
        (localFilters.contractorId && localFilters.contractorId !== "all") ||
        localFilters.startDate ||
        localFilters.endDate;

    const content = (
        <div className="space-y-4">
            <div className="flex flex-col lg:flex-row gap-4">
                <Input
                    isClearable
                    className="flex-1"
                    placeholder="Buscar por folio..."
                    startContent={
                        <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                    }
                    value={localFilters.search || ""}
                    onChange={(e) =>
                        handleFilterChange("search", e.target.value)
                    }
                    onClear={() => handleFilterChange("search", "")}
                />

                <Select
                    className="w-full lg:w-48"
                    placeholder="Estado"
                    selectedKeys={
                        localFilters.status ? [localFilters.status] : []
                    }
                    onChange={(e) =>
                        handleFilterChange("status", e.target.value)
                    }
                >
                    {statusOptions.map((option) => (
                        <SelectItem key={option.value}>
                            {option.label}
                        </SelectItem>
                    ))}
                </Select>

                {contractors.length > 0 && (
                    <Select
                        className="w-full lg:w-64"
                        placeholder="Contratista"
                        selectedKeys={
                            localFilters.contractorId
                                ? [localFilters.contractorId]
                                : []
                        }
                        onChange={(e) =>
                            handleFilterChange("contractorId", e.target.value)
                        }
                    >
                        <SelectItem key="all">
                            Todos los contratistas
                        </SelectItem>
                        {
                            contractors.map((contractor) => (
                                <SelectItem key={contractor.id}>
                                    {contractor.name}
                                </SelectItem>
                            )) as any
                        }
                    </Select>
                )}

                <Button
                    color={isExpanded ? "primary" : "default"}
                    startContent={<FunnelIcon className="w-4 h-4" />}
                    variant="flat"
                    onPress={() => setIsExpanded(!isExpanded)}
                >
                    Más filtros
                </Button>

                {hasActiveFilters && (
                    <Button
                        color="danger"
                        startContent={<XMarkIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={handleClearFilters}
                    >
                        Limpiar
                    </Button>
                )}
            </div>

            {isExpanded && (
                <motion.div
                    animate={{ opacity: 1, height: "auto" }}
                    className="flex flex-col lg:flex-row gap-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                    exit={{ opacity: 0, height: 0 }}
                    initial={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <DatePicker
                        className="w-full lg:w-48"
                        label="Fecha inicio"
                        value={localFilters.startDate as any}
                        onChange={(date) =>
                            handleFilterChange("startDate", date)
                        }
                    />

                    <DatePicker
                        className="w-full lg:w-48"
                        label="Fecha fin"
                        value={localFilters.endDate as any}
                        onChange={(date) => handleFilterChange("endDate", date)}
                    />
                </motion.div>
            )}
        </div>
    );

    if (!showCard) {
        return content;
    }

    return (
        <Card>
            <CardBody>{content}</CardBody>
        </Card>
    );
}
