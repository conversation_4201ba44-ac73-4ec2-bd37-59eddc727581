"use client";

import type { SWRConfiguration } from "swr";

import useS<PERSON> from "swr";
import { useState } from "react";

import {
    getModel,
    getModels,
    validateModelCode as validateCode,
    createModel as create,
    updateModel as update,
    deleteModel as remove,
} from "@/features/models/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

// Obtener helpers de revalidación para el cliente
const { revalidateData } = createClientRevalidation("model");

export interface Model {
    id: string;
    code: string;
    description?: string;
    basePrice?: number;
    createdAt: string;
    updatedAt: string;
    _count?: {
        garments: number;
    };
}

export interface ModelsResponse {
    models: Model[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

/**
 * Hook para obtener todos los modelos
 * @param options Opciones de filtrado y paginación
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de modelos, paginación, estado de carga y errores
 */
export function useModels(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
    config?: SWRConfiguration,
) {
    // Convertir opciones a cadena para clave de caché
    const optionsKey = JSON.stringify(options);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        ["models", optionsKey],
        async () => getModels(options),
        config,
    );

    // Escuchar eventos de revalidación
    const { isRevalidating } = useRevalidationListener("models");

    return {
        models: (data?.data as any)?.models || [],
        pagination: (data?.data as any)?.pagination,
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para obtener la información de un modelo por su ID
 * @param id ID del modelo a cargar
 * @param config Configuración opcional para SWR
 * @returns Objeto con los datos del modelo, estado de carga y errores
 */
export function useModel(
    id: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        id ? ["model", id] : null,
        async () => (id ? getModel(id) : null),
        config,
    );

    return {
        // Usamos una comprobación más específica para asegurar que TypeScript entienda la existencia de data.data
        model:
            data && "success" in data && data.success && "data" in data
                ? data.data
                : null,
        isLoading,
        isError: !!error || (data && "success" in data && !data.success),
        error: data && "error" in data ? data.error : error,
        mutate,
    };
}

/**
 * Hook para comprobar si un código de modelo es válido
 * @param code Código del modelo a validar
 * @param excludeId ID del modelo a excluir de la validación (para edición)
 * @returns Objeto con resultado de validación y estado
 */
export function useValidateModelCode(code: string | null, excludeId?: string) {
    // Utilizar Server Action como fetcher
    const { data, error, isLoading } = useSWR(
        code && code.length >= 1
            ? ["validateModelCode", code, excludeId]
            : null,
        async () =>
            code && code.length >= 1 ? validateCode(code, excludeId) : null,
        {
            revalidateOnFocus: false,
            dedupingInterval: 10000,
        },
    );

    // Versión simplificada que favorece un resultado positivo en caso de duda
    // Asumimos que el código es válido a menos que el servidor explícitamente diga lo contrario
    let isValid = true;

    // Si hay error en la petición, asumimos válido para evitar bloquear el envío
    if (error) {
        isValid = true;
    }
    // Si hay respuesta y contiene isValid = false, entonces no es válido
    else if (data) {
        // Caso 1: { isValid: false }
        if (typeof data === "object" && data !== null && "isValid" in data) {
            isValid = data.isValid !== false; // Si es explícitamente false, no es válido
        }
        // Caso 2: { success: true, data: { isValid: false } }
        else if (
            typeof data === "object" &&
            data !== null &&
            "data" in data &&
            typeof data.data === "object" &&
            data.data !== null &&
            "isValid" in data.data
        ) {
            isValid = data.data.isValid !== false; // Si es explícitamente false, no es válido
        }
    }

    return {
        isValid,
        isValidating: isLoading,
        error,
    };
}

/**
 * Hook para crear un nuevo modelo
 * @returns Función para crear un modelo y estado de la operación
 */
export function useCreateModel() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createModel = async (data: {
        code: string;
        description: string;
        basePrice?: number;
    }) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await create(data);

            // Si se creó correctamente, revalidar todos los datos relacionados
            if (result?.success) {
                if (process.env.NODE_ENV !== "production") {
                    // // REMOVED: console.log(
                    //     "Modelo creado correctamente, revalidando datos...",
                    // );
                }

                // Revalidar datos mediante la función genérica
                if ((result?.data as any)?.id) {
                    await revalidateData((result.data as any).id);
                }
            } else {
                setError(
                    result?.error ?? "Error desconocido al crear el modelo",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al crear el modelo";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createModel,
        isCreating: isLoading,
        createError: error,
    };
}

/**
 * Hook para actualizar un modelo existente
 * @returns Función para actualizar un modelo y estado de la operación
 */
export function useUpdateModel() {
    const updateModel = async (
        id: string,
        data: { code: string; description: string; basePrice?: number },
    ) => {
        const result = await update(id, data);

        // Si se actualizó correctamente, revalidar todos los datos relacionados
        if (result?.success) {
            if (process.env.NODE_ENV !== "production") {
                // // REMOVED: console.log(
                //     `Modelo ${id} actualizado correctamente, revalidando datos...`,
                // );
            }

            // Revalidar datos mediante la función genérica
            await revalidateData(id);
        }

        return result;
    };

    return { updateModel };
}

/**
 * Hook para eliminar un modelo
 * @returns Función para eliminar un modelo y estado de la operación
 */
export function useDeleteModel() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteModel = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await remove(id);

            // Si se eliminó correctamente, revalidar todos los datos relacionados
            if (result?.success) {
                if (process.env.NODE_ENV !== "production") {
                    // // REMOVED: console.log(
                    //     `Modelo ${id} eliminado correctamente, revalidando datos...`,
                    // );
                }

                // Revalidar datos mediante la función genérica
                await revalidateData();
            } else {
                setError(
                    result?.error ?? "Error desconocido al eliminar el modelo",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al eliminar el modelo";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        deleteModel,
        isDeleting: isLoading,
        deleteError: error,
    };
}
