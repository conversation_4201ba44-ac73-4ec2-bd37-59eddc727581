"use client";

import { forwardRef, InputHTMLAttributes, ReactNode } from "react";

import { cn } from "@/lib/utils";

interface AuthInputProps extends InputHTMLAttributes<HTMLInputElement> {
    label?: string;
    error?: string;
    icon?: ReactNode;
}

export const AuthInput = forwardRef<HTMLInputElement, AuthInputProps>(
    ({ label, error, icon, className, ...props }, ref) => {
        return (
            <div className="mb-6">
                {label && (
                    <label
                        className="block text-sm font-medium text-neutral-700 dark:text-slate-300 mb-2"
                        htmlFor={props.id}
                    >
                        {label}
                    </label>
                )}
                <div className="relative">
                    {icon && (
                        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-neutral-500 dark:text-slate-500 pointer-events-none">
                            {icon}
                        </div>
                    )}
                    <input
                        ref={ref}
                        className={cn(
                            "w-full px-4 py-3 bg-neutral-200 dark:bg-slate-900/50 rounded-xl",
                            "dark:border dark:border-slate-700",
                            "shadow-[inset_2px_2px_5px_rgba(0,0,0,0.1),inset_-2px_-2px_5px_rgba(255,255,255,0.7)]",
                            "dark:shadow-none",
                            "transition-all duration-300",
                            "focus:shadow-[inset_3px_3px_7px_rgba(0,0,0,0.15),inset_-3px_-3px_7px_rgba(255,255,255,0.8)]",
                            "dark:focus:border-blue-500",
                            "focus:outline-none focus:ring-2 focus:ring-primary-500/20 dark:focus:ring-blue-500/30",
                            "placeholder:text-neutral-500 dark:placeholder:text-slate-500",
                            "dark:text-slate-100",
                            icon && "pl-10",
                            error && "ring-2 ring-error dark:ring-red-400",
                            className,
                        )}
                        {...props}
                    />
                </div>
                {error && (
                    <p className="mt-1 text-sm text-error dark:text-red-400 animate-fadeIn">
                        {error}
                    </p>
                )}
            </div>
        );
    },
);

AuthInput.displayName = "AuthInput";
