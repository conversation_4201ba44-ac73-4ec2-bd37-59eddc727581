import React, { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "@heroui/react";
import { AlertTriangle, Trash2 } from "lucide-react";
import { motion } from "framer-motion";

interface DeleteSizeModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => Promise<void>;
    sizeCode: string;
}

export function DeleteSizeModal({
    isOpen,
    onClose,
    onConfirm,
    sizeCode,
}: DeleteSizeModalProps) {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleConfirm = async () => {
        setIsDeleting(true);
        try {
            await onConfirm();
            onClose();
        } catch (error) {
            // El error se maneja en el componente padre
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <Modal
            backdrop="blur"
            classNames={{
                base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                header: "border-b border-gray-200 dark:border-gray-700",
                body: "py-6",
                footer: "border-t border-gray-200 dark:border-gray-700",
            }}
            isOpen={isOpen}
            placement="center"
            onClose={onClose}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">
                            <motion.div
                                animate={{ scale: 1, opacity: 1 }}
                                className="flex items-center gap-3"
                                initial={{ scale: 0.8, opacity: 0 }}
                            >
                                <div className="p-2 rounded-lg bg-danger-50 dark:bg-danger-900/20">
                                    <Trash2 className="w-5 h-5 text-danger-600 dark:text-danger-400" />
                                </div>
                                <span className="text-lg font-semibold">
                                    Eliminar Talla
                                </span>
                            </motion.div>
                        </ModalHeader>

                        <ModalBody>
                            <motion.div
                                animate={{ y: 0, opacity: 1 }}
                                className="space-y-4"
                                initial={{ y: 10, opacity: 0 }}
                                transition={{ delay: 0.1 }}
                            >
                                <p className="text-gray-600 dark:text-gray-400">
                                    ¿Estás seguro de que deseas eliminar la
                                    talla{" "}
                                    <span className="font-semibold text-gray-800 dark:text-gray-200">
                                        {sizeCode}
                                    </span>
                                    ?
                                </p>

                                <div className="flex items-start gap-3 p-3 rounded-lg bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800">
                                    <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
                                    <div className="text-sm text-amber-800 dark:text-amber-300">
                                        <p className="font-medium">
                                            Esta acción no se puede deshacer.
                                        </p>
                                        <p className="mt-1">
                                            La talla solo puede eliminarse si no
                                            está asociada a ninguna prenda.
                                        </p>
                                    </div>
                                </div>
                            </motion.div>
                        </ModalBody>

                        <ModalFooter>
                            <Button
                                isDisabled={isDeleting}
                                variant="flat"
                                onPress={onClose}
                            >
                                Cancelar
                            </Button>
                            <Button
                                color="danger"
                                isLoading={isDeleting}
                                startContent={
                                    !isDeleting && (
                                        <Trash2 className="w-4 h-4" />
                                    )
                                }
                                onPress={handleConfirm}
                            >
                                {isDeleting ? "Eliminando..." : "Eliminar"}
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
