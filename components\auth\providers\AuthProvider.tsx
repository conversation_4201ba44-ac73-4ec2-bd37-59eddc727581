"use client";

import {
    createContext,
    useContext,
    useState,
    useEffect,
    ReactNode,
} from "react";

import {
    User,
    AuthState,
    LoginCredentials,
    RegisterData,
} from "@/lib/auth/types";
import { authService } from "@/lib/auth/service";

interface AuthContextType extends AuthState {
    login: (credentials: LoginCredentials) => Promise<void>;
    register: (data: RegisterData) => Promise<void>;
    logout: () => Promise<void>;
    updateUser: (user: Partial<User>) => void;
    clearError: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
    children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
    const [state, setState] = useState<AuthState>({
        user: null,
        isLoading: true,
        isAuthenticated: false,
        error: null,
    });

    // Verificar sesión al montar
    useEffect(() => {
        checkAuth();
    }, []);

    const checkAuth = async () => {
        try {
            const user = await authService.getCurrentUser();

            setState({
                user,
                isLoading: false,
                isAuthenticated: !!user,
                error: null,
            });
        } catch (error) {
            setState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
                error: null,
            });
        }
    };

    const login = async (credentials: LoginCredentials) => {
        setState((prev) => ({ ...prev, isLoading: true, error: null }));

        try {
            const response = await authService.login(credentials);

            setState({
                user: response.user,
                isLoading: false,
                isAuthenticated: true,
                error: null,
            });
        } catch (error: any) {
            setState((prev) => ({
                ...prev,
                isLoading: false,
                error: error.message || "Error al iniciar sesión",
            }));
            throw error;
        }
    };

    const register = async (data: RegisterData) => {
        setState((prev) => ({ ...prev, isLoading: true, error: null }));

        try {
            const response = await authService.register(data);

            setState({
                user: response.user,
                isLoading: false,
                isAuthenticated: true,
                error: null,
            });
        } catch (error: any) {
            setState((prev) => ({
                ...prev,
                isLoading: false,
                error: error.message || "Error al registrarse",
            }));
            throw error;
        }
    };

    const logout = async () => {
        setState((prev) => ({ ...prev, isLoading: true }));

        try {
            await authService.logout();
            setState({
                user: null,
                isLoading: false,
                isAuthenticated: false,
                error: null,
            });
        } catch (error: any) {
            setState((prev) => ({
                ...prev,
                isLoading: false,
                error: error.message || "Error al cerrar sesión",
            }));
        }
    };

    const updateUser = (updates: Partial<User>) => {
        setState((prev) => ({
            ...prev,
            user: prev.user ? { ...prev.user, ...updates } : null,
        }));
    };

    const clearError = () => {
        setState((prev) => ({ ...prev, error: null }));
    };

    const value: AuthContextType = {
        ...state,
        login,
        register,
        logout,
        updateUser,
        clearError,
    };

    return (
        <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
    );
}

export const useAuth = () => {
    const context = useContext(AuthContext);

    if (!context) {
        throw new Error("useAuth must be used within AuthProvider");
    }

    return context;
};
