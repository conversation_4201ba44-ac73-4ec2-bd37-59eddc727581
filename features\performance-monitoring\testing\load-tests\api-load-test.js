import http from 'k6/http';
import { check, group } from 'k6';
import { Counter, Trend } from 'k6/metrics';

// Metrics
const orderApiDuration = new Trend('order_api_duration');
const productApiDuration = new Trend('product_api_duration');
const cacheHitRate = new Counter('cache_hit_rate');

export const options = {
  scenarios: {
    orders_scenario: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '30s', target: 20 },
        { duration: '1m', target: 20 },
        { duration: '30s', target: 0 },
      ],
      gracefulRampDown: '30s',
    },
    products_scenario: {
      executor: 'constant-vus',
      vus: 10,
      duration: '2m',
    },
  },
  thresholds: {
    'order_api_duration': ['p(95)<200'],
    'product_api_duration': ['p(95)<150'],
    'http_req_failed': ['rate<0.1'],
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';
const API_TOKEN = __ENV.API_TOKEN || '';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${API_TOKEN}`,
};

export default function () {
  // Test order APIs
  group('Order APIs', () => {
    const start = Date.now();
    const ordersRes = http.get(`${BASE_URL}/api/orders`, { headers });
    const duration = Date.now() - start;
    
    orderApiDuration.add(duration);
    
    check(ordersRes, {
      'orders status 200': (r) => r.status === 200,
      'orders has data': (r) => JSON.parse(r.body).length > 0,
    });
  });
}