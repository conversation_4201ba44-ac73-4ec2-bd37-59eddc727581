"use client";

import React from "react";
import { Progress } from "@heroui/react";

interface ContractorFormProgressProps {
    value: number;
    label?: string;
    showPercentage?: boolean;
    className?: string;
}

// Función para calcular el progreso del formulario
export function calculateContractorProgress(data: {
    firstName?: string;
    lastName?: string;
    middleName?: string;
    secondLastName?: string;
    email?: string;
    phone?: string;
    notes?: string;
}): number {
    let progress = 0;

    // Campos obligatorios (50% total)
    if (data.firstName && data.firstName.trim().length >= 2) {
        progress += 25;
    }
    if (data.lastName && data.lastName.trim().length >= 2) {
        progress += 25;
    }

    // Campos opcionales (50% total, 10% cada uno excepto notes que es 10%)
    if (data.middleName && data.middleName.trim().length > 0) {
        progress += 10;
    }
    if (data.secondLastName && data.secondLastName.trim().length > 0) {
        progress += 10;
    }
    if (data.email && data.email.trim().length > 0) {
        progress += 10;
    }
    if (data.phone && data.phone.trim().length > 0) {
        progress += 10;
    }
    if (data.notes && data.notes.trim().length > 0) {
        progress += 10;
    }

    return Math.min(progress, 100);
}

export function ContractorFormProgress({
    value,
    label = "Progreso del formulario",
    showPercentage = true,
    className = "",
}: ContractorFormProgressProps) {
    return (
        <div className={className}>
            <div className="space-y-2">
                <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {label}
                    </span>
                    {showPercentage && (
                        <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">
                            {value}%
                        </span>
                    )}
                </div>
                <Progress
                    aria-label={label}
                    classNames={{
                        base: "max-w-full",
                        track: "bg-gray-200 dark:bg-gray-700",
                        indicator: "bg-purple-600",
                    }}
                    color="secondary"
                    size="sm"
                    value={value}
                />
                {value >= 50 && value < 100 && (
                    <p className="text-xs text-blue-600 dark:text-blue-400 font-medium text-center mt-1">
                        Campos obligatorios completos - Listo para crear
                    </p>
                )}
                {value === 100 && (
                    <p className="text-xs text-green-600 dark:text-green-400 font-medium text-center mt-1">
                        Formulario completo ✓
                    </p>
                )}
            </div>
        </div>
    );
}
