"use client";

import type { PackingWithRelations } from "@/lib/types/packing";

import React from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { Card, CardBody, CardHeader } from "@heroui/react";

interface PackingPreviewDocumentProps {
    packing: PackingWithRelations;
}

interface GroupedProduct {
    modelo: string;
    ordenCorte: string | null;
    partida: string | null;
    color: string;
    descripcion: string;
    tipo: string;
    cliente: string;
    qualityData: {
        [key: string]: {
            primera: number;
            segunda: number;
            manchada: number;
            incompleta: number;
        };
    };
    packaging: {
        cajas: Record<string, number>;
        piezas: Record<string, number>;
        bolsas1ra: Record<string, number>;
        bolsas2da: Record<string, number>;
    };
}

export function PackingPreviewDocument({
    packing,
}: PackingPreviewDocumentProps) {
    const groupProducts = (): GroupedProduct[] => {
        const groups = new Map<string, GroupedProduct>();

        packing.details.forEach((detail) => {
            const garmentSize = detail.garmentSize;
            const model = garmentSize.garment.model;
            const color = garmentSize.garment.color;
            const size = garmentSize.size;

            const orderId = packing.orderId || "";
            const orderNumber = packing.order?.cutOrder || "";
            const partNumber = detail.partNumber || "";

            const groupKey = `${model.code}-${orderNumber}-${partNumber}-${color.name}`;

            if (!groups.has(groupKey)) {
                groups.set(groupKey, {
                    modelo: model.code,
                    ordenCorte: orderNumber || null,
                    partida: partNumber || null,
                    color: color.name,
                    descripcion: model.description || "",
                    tipo: "SMART FIT",
                    cliente: packing.customer.name,
                    qualityData: {},
                    packaging: {
                        cajas: {},
                        piezas: {},
                        bolsas1ra: {},
                        bolsas2da: {},
                    },
                });
            }

            const group = groups.get(groupKey)!;

            if (!group.qualityData[size.code]) {
                group.qualityData[size.code] = {
                    primera: 0,
                    segunda: 0,
                    manchada: 0,
                    incompleta: 0,
                };
            }

            const qualityType = detail.qualityType || "primera";

            if (qualityType === "primera") {
                group.qualityData[size.code].primera += detail.quantity;
            } else if (qualityType === "segunda") {
                group.qualityData[size.code].segunda += detail.quantity;
            } else if (qualityType === "manchada") {
                group.qualityData[size.code].manchada += detail.quantity;
            } else if (qualityType === "incompleta") {
                group.qualityData[size.code].incompleta += detail.quantity;
            }

            if (detail.packagingType === "caja" && detail.packagingUnits) {
                group.packaging.cajas[size.code] =
                    (group.packaging.cajas[size.code] || 0) +
                    detail.packagingUnits;
            }

            if (detail.loosePieces && detail.loosePieces > 0) {
                group.packaging.piezas[size.code] =
                    (group.packaging.piezas[size.code] || 0) +
                    detail.loosePieces;
            }

            if (detail.packagingType === "bolsa" && detail.packagingUnits) {
                if (qualityType === "primera") {
                    group.packaging.bolsas1ra[size.code] =
                        (group.packaging.bolsas1ra[size.code] || 0) +
                        detail.packagingUnits;
                } else {
                    group.packaging.bolsas2da[size.code] =
                        (group.packaging.bolsas2da[size.code] || 0) +
                        detail.packagingUnits;
                }
            }
        });

        return Array.from(groups.values());
    };

    const productGroups = groupProducts();

    const allSizes = new Set<string>();

    productGroups.forEach((group) => {
        Object.keys(group.qualityData).forEach((size) => allSizes.add(size));
    });
    const sortedSizes = ["XS", "S", "M", "L", "XL", "2XL", "3XL"].filter(
        (size) => allSizes.has(size),
    );

    let grandTotal = 0;

    productGroups.forEach((group) => {
        Object.values(group.qualityData).forEach((sizeData) => {
            grandTotal +=
                sizeData.primera +
                sizeData.segunda +
                sizeData.manchada +
                sizeData.incompleta;
        });
    });

    const companyInfo = (packing.companyInfo as any) || {
        name: "PEDRO LOBATO",
        address: "Dirección de la empresa",
    };

    const formattedDate = format(
        new Date(packing.deliveryDate),
        "EEEE, d 'de' MMMM 'de' yyyy",
        { locale: es },
    );

    return (
        <Card className="w-full shadow-lg border-0 bg-gradient-to-br from-gray-50 to-white">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
                <div className="flex items-center gap-3">
                    <div className="p-2 bg-white/20 rounded-lg">
                        <svg
                            className="w-6 h-6"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                clipRule="evenodd"
                                d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                fillRule="evenodd"
                            />
                        </svg>
                    </div>
                    <h3 className="text-xl font-bold">
                        Vista Previa del Documento
                    </h3>
                </div>
            </CardHeader>
            <CardBody className="p-0">
                <div
                    className="bg-white text-black m-6 rounded-xl shadow-xl border border-gray-200 overflow-hidden"
                    style={{
                        background:
                            "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                    }}
                >
                    <div className="p-8 space-y-8">
                        {/* Header */}
                        <div className="relative bg-gradient-to-r from-slate-800 to-slate-900 text-white p-6 rounded-lg mb-6">
                            <div className="absolute inset-0 bg-black/10 rounded-lg" />
                            <div className="relative flex justify-between items-center">
                                <div className="flex items-center gap-3">
                                    <div className="p-3 bg-white/10 rounded-full">
                                        <svg
                                            className="w-8 h-8"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                            <path
                                                clipRule="evenodd"
                                                d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm2.707 7.293a1 1 0 000 1.414L8.414 15.5a1 1 0 101.414-1.414L8.121 12.379a1 1 0 00-1.414 0z"
                                                fillRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <h1 className="text-3xl font-bold tracking-wide">
                                            LISTA DE EMPAQUE
                                        </h1>
                                        <p className="text-slate-300 text-sm mt-1">
                                            Documento oficial de empaque
                                        </p>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <div className="bg-white/10 rounded-lg p-4 border border-white/20">
                                        <p className="text-slate-300 text-sm font-medium">
                                            FOLIO
                                        </p>
                                        <p className="text-2xl font-bold text-white">
                                            {packing.folio}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* From/To Section */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200 shadow-sm">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-green-600 text-white rounded-lg">
                                        <svg
                                            className="w-5 h-5"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                clipRule="evenodd"
                                                d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 6a1 1 0 011-1h4a1 1 0 110 2H8a1 1 0 01-1-1z"
                                                fillRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <h3 className="font-bold text-green-800 text-lg">
                                        REMITENTE
                                    </h3>
                                </div>
                                <p className="text-green-700 font-semibold text-lg">
                                    {companyInfo.name}
                                </p>
                                <p className="text-green-600 text-sm mt-1">
                                    Empresa emisora
                                </p>
                            </div>

                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200 shadow-sm">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-blue-600 text-white rounded-lg">
                                        <svg
                                            className="w-5 h-5"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                        </svg>
                                    </div>
                                    <h3 className="font-bold text-blue-800 text-lg">
                                        DESTINATARIO
                                    </h3>
                                </div>
                                <p className="text-blue-700 font-semibold text-lg">
                                    {packing.customer.name}
                                </p>
                                {packing.subCustomer && (
                                    <p className="text-blue-600 text-base mt-1">
                                        {packing.subCustomer.name}
                                    </p>
                                )}
                                <p className="text-blue-600 text-sm mt-1">
                                    Cliente
                                </p>
                            </div>

                            <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-xl border border-purple-200 shadow-sm">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-purple-600 text-white rounded-lg">
                                        <svg
                                            className="w-5 h-5"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                clipRule="evenodd"
                                                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                                fillRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    <h3 className="font-bold text-purple-800 text-lg">
                                        FECHA
                                    </h3>
                                </div>
                                <p className="text-purple-700 font-semibold text-lg">
                                    {formattedDate}
                                </p>
                                <p className="text-purple-600 text-sm mt-1">
                                    Fecha de entrega
                                </p>
                            </div>
                        </div>

                        {/* Main Products Table */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                            <div className="bg-gradient-to-r from-slate-700 to-slate-800 p-4">
                                <h2 className="text-white text-xl font-bold flex items-center gap-3">
                                    <div className="p-2 bg-white/10 rounded-lg">
                                        <svg
                                            className="w-6 h-6"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                clipRule="evenodd"
                                                d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                                fillRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    DETALLE DE PRODUCTOS
                                </h2>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b-2 border-gray-200">
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm border-r border-gray-300">
                                                MODELO
                                            </th>
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm border-r border-gray-300">
                                                ORDEN DE CORTE
                                            </th>
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm border-r border-gray-300">
                                                PARTIDA
                                            </th>
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm border-r border-gray-300">
                                                COLOR
                                            </th>
                                            <th className="px-4 py-3 text-center font-bold text-gray-800 text-sm border-r border-gray-300">
                                                TALLAS
                                            </th>
                                            {sortedSizes.map((size) => (
                                                <th
                                                    key={size}
                                                    className="px-3 py-3 text-center font-bold text-gray-800 text-sm min-w-16 border-r border-gray-300 bg-blue-50"
                                                >
                                                    <div className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                                                        {size}
                                                    </div>
                                                </th>
                                            ))}
                                            <th className="px-4 py-3 text-center font-bold text-gray-800 text-sm border-r border-gray-300 bg-green-50">
                                                TOTAL
                                            </th>
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm border-r border-gray-300">
                                                DESCRIPCIÓN
                                            </th>
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm border-r border-gray-300">
                                                TIPO
                                            </th>
                                            <th className="px-4 py-3 text-left font-bold text-gray-800 text-sm">
                                                CLIENTE
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {productGroups.map(
                                            (group, groupIndex) => {
                                                const qualityRows = [];
                                                let groupTotal = 0;

                                                const primeraTotal =
                                                    Object.values(
                                                        group.qualityData,
                                                    ).reduce(
                                                        (sum, data) =>
                                                            sum + data.primera,
                                                        0,
                                                    );

                                                if (primeraTotal > 0) {
                                                    qualityRows.push({
                                                        quality: "Primeras",
                                                        data: group.qualityData,
                                                        field: "primera" as const,
                                                        total: primeraTotal,
                                                    });
                                                    groupTotal += primeraTotal;
                                                }

                                                const segundaTotal =
                                                    Object.values(
                                                        group.qualityData,
                                                    ).reduce(
                                                        (sum, data) =>
                                                            sum + data.segunda,
                                                        0,
                                                    );

                                                if (segundaTotal > 0) {
                                                    qualityRows.push({
                                                        quality:
                                                            "Segundas de Tela",
                                                        data: group.qualityData,
                                                        field: "segunda" as const,
                                                        total: segundaTotal,
                                                    });
                                                    groupTotal += segundaTotal;
                                                }

                                                const manchadaTotal =
                                                    Object.values(
                                                        group.qualityData,
                                                    ).reduce(
                                                        (sum, data) =>
                                                            sum + data.manchada,
                                                        0,
                                                    );

                                                if (manchadaTotal > 0) {
                                                    qualityRows.push({
                                                        quality: "manchas",
                                                        data: group.qualityData,
                                                        field: "manchada" as const,
                                                        total: manchadaTotal,
                                                    });
                                                    groupTotal += manchadaTotal;
                                                }

                                                const incompletaTotal =
                                                    Object.values(
                                                        group.qualityData,
                                                    ).reduce(
                                                        (sum, data) =>
                                                            sum +
                                                            data.incompleta,
                                                        0,
                                                    );

                                                if (incompletaTotal > 0) {
                                                    qualityRows.push({
                                                        quality: "Incompletas",
                                                        data: group.qualityData,
                                                        field: "incompleta" as const,
                                                        total: incompletaTotal,
                                                    });
                                                    groupTotal +=
                                                        incompletaTotal;
                                                }

                                                return (
                                                    <React.Fragment
                                                        key={groupIndex}
                                                    >
                                                        {qualityRows.map(
                                                            (row, rowIndex) => (
                                                                <tr
                                                                    key={`${groupIndex}-${rowIndex}`}
                                                                    className={`transition-all duration-200 hover:bg-blue-50 ${
                                                                        groupIndex %
                                                                            2 ===
                                                                        0
                                                                            ? "bg-white"
                                                                            : "bg-gray-50/50"
                                                                    }`}
                                                                >
                                                                    {rowIndex ===
                                                                        0 && (
                                                                        <>
                                                                            <td
                                                                                className="px-4 py-3 font-bold text-gray-900 border-r border-gray-200"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                <div className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-lg font-bold text-sm">
                                                                                    {
                                                                                        group.modelo
                                                                                    }
                                                                                </div>
                                                                            </td>
                                                                            <td
                                                                                className="px-4 py-3 text-gray-700 border-r border-gray-200"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                {group.ordenCorte ||
                                                                                    "-"}
                                                                            </td>
                                                                            <td
                                                                                className="px-4 py-3 text-gray-700 border-r border-gray-200"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                {group.partida ||
                                                                                    "-"}
                                                                            </td>
                                                                            <td
                                                                                className="px-4 py-3 border-r border-gray-200"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                <div className="bg-amber-100 text-amber-800 px-3 py-1 rounded-lg font-semibold text-sm">
                                                                                    {
                                                                                        group.color
                                                                                    }
                                                                                </div>
                                                                            </td>
                                                                        </>
                                                                    )}
                                                                    <td className="px-4 py-3 border-r border-gray-200">
                                                                        <div
                                                                            className={`px-3 py-1 rounded-lg font-semibold text-sm ${
                                                                                row.quality ===
                                                                                "Primeras"
                                                                                    ? "bg-green-100 text-green-800"
                                                                                    : row.quality ===
                                                                                        "Segundas de Tela"
                                                                                      ? "bg-yellow-100 text-yellow-800"
                                                                                      : row.quality ===
                                                                                          "manchas"
                                                                                        ? "bg-orange-100 text-orange-800"
                                                                                        : "bg-red-100 text-red-800"
                                                                            }`}
                                                                        >
                                                                            {
                                                                                row.quality
                                                                            }
                                                                        </div>
                                                                    </td>
                                                                    {sortedSizes.map(
                                                                        (
                                                                            size,
                                                                        ) => (
                                                                            <td
                                                                                key={
                                                                                    size
                                                                                }
                                                                                className="px-3 py-3 text-center border-r border-gray-200 font-semibold"
                                                                            >
                                                                                {row
                                                                                    .data[
                                                                                    size
                                                                                ]?.[
                                                                                    row
                                                                                        .field
                                                                                ] ? (
                                                                                    <div className="bg-blue-600 text-white px-2 py-1 rounded-full text-sm font-bold">
                                                                                        {
                                                                                            row
                                                                                                .data[
                                                                                                size
                                                                                            ][
                                                                                                row
                                                                                                    .field
                                                                                            ]
                                                                                        }
                                                                                    </div>
                                                                                ) : (
                                                                                    <span className="text-gray-400">
                                                                                        -
                                                                                    </span>
                                                                                )}
                                                                            </td>
                                                                        ),
                                                                    )}
                                                                    <td className="px-4 py-3 text-center border-r border-gray-200">
                                                                        <div className="bg-green-600 text-white px-4 py-2 rounded-lg font-bold text-lg">
                                                                            {
                                                                                row.total
                                                                            }
                                                                        </div>
                                                                    </td>
                                                                    {rowIndex ===
                                                                        0 && (
                                                                        <>
                                                                            <td
                                                                                className="px-4 py-3 text-gray-700 text-sm border-r border-gray-200"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                {
                                                                                    group.descripcion
                                                                                }
                                                                            </td>
                                                                            <td
                                                                                className="px-4 py-3 text-gray-700 font-medium border-r border-gray-200"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                {
                                                                                    group.tipo
                                                                                }
                                                                            </td>
                                                                            <td
                                                                                className="px-4 py-3 text-gray-700 font-medium"
                                                                                rowSpan={
                                                                                    qualityRows.length
                                                                                }
                                                                            >
                                                                                {
                                                                                    group.cliente
                                                                                }
                                                                            </td>
                                                                        </>
                                                                    )}
                                                                </tr>
                                                            ),
                                                        )}
                                                    </React.Fragment>
                                                );
                                            },
                                        )}
                                    </tbody>
                                    <tfoot>
                                        <tr className="bg-gradient-to-r from-slate-700 to-slate-800 text-white">
                                            <td
                                                className="px-4 py-4 text-right font-bold text-lg"
                                                colSpan={5 + sortedSizes.length}
                                            >
                                                <div className="flex items-center justify-end gap-3">
                                                    <svg
                                                        className="w-6 h-6"
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path
                                                            clipRule="evenodd"
                                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                                                            fillRule="evenodd"
                                                        />
                                                    </svg>
                                                    TOTAL GENERAL:
                                                </div>
                                            </td>
                                            <td className="px-4 py-4 text-center">
                                                <div className="bg-white text-slate-800 px-6 py-3 rounded-lg font-bold text-2xl shadow-lg">
                                                    {grandTotal.toLocaleString()}
                                                </div>
                                            </td>
                                            <td
                                                className="px-4 py-4"
                                                colSpan={3}
                                            >
                                                <div className="text-slate-300 text-sm">
                                                    Total de piezas
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        {/* Packaging Summary */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                            <div className="bg-gradient-to-r from-orange-600 to-red-600 p-4">
                                <h2 className="text-white text-xl font-bold flex items-center gap-3">
                                    <div className="p-2 bg-white/10 rounded-lg">
                                        <svg
                                            className="w-6 h-6"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                                        </svg>
                                    </div>
                                    RESUMEN DE EMPAQUE
                                </h2>
                            </div>

                            <div className="p-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                    {productGroups.map((group, index) => (
                                        <div
                                            key={index}
                                            className="bg-gradient-to-br from-slate-50 to-gray-100 rounded-xl shadow-md border border-gray-200 overflow-hidden"
                                        >
                                            <div className="bg-gradient-to-r from-slate-700 to-slate-800 text-white p-4">
                                                <h3 className="font-bold text-lg text-center">
                                                    {group.partida ||
                                                        group.ordenCorte ||
                                                        group.modelo}
                                                </h3>
                                            </div>
                                            <div className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 p-3 text-center border-b border-gray-200">
                                                <div className="flex items-center justify-center gap-2">
                                                    <svg
                                                        className="w-5 h-5"
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z" />
                                                        <path
                                                            clipRule="evenodd"
                                                            d="M5 8a1 1 0 011 1v6a1 1 0 102 0V9a1 1 0 112 0v6a1 1 0 102 0V9a1 1 0 112 0v6a1 1 0 102 0V9a1 1 0 011-1h2a1 1 0 110 2h-1v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9H3a1 1 0 010-2h2z"
                                                            fillRule="evenodd"
                                                        />
                                                    </svg>
                                                    <span className="font-bold text-sm">
                                                        Piezas por Caja: 50
                                                    </span>
                                                </div>
                                            </div>

                                            <div className="p-4">
                                                <table className="w-full text-sm">
                                                    <thead>
                                                        <tr className="bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300">
                                                            <th className="border-r border-gray-300 p-2 text-gray-800 font-bold text-xs">
                                                                TALLAS
                                                            </th>
                                                            <th className="border-r border-gray-300 p-2 text-gray-800 font-bold text-xs">
                                                                CAJAS
                                                            </th>
                                                            <th className="border-r border-gray-300 p-2 text-gray-800 font-bold text-xs">
                                                                PIEZAS
                                                            </th>
                                                            <th className="border-r border-gray-300 p-2 text-gray-800 font-bold text-xs">
                                                                BOLSAS 1°ra
                                                            </th>
                                                            <th className="p-2 text-gray-800 font-bold text-xs">
                                                                BOLSAS 2°da
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {sortedSizes.map(
                                                            (size) => {
                                                                const hasData =
                                                                    group
                                                                        .packaging
                                                                        .cajas[
                                                                        size
                                                                    ] ||
                                                                    group
                                                                        .packaging
                                                                        .piezas[
                                                                        size
                                                                    ] ||
                                                                    group
                                                                        .packaging
                                                                        .bolsas1ra[
                                                                        size
                                                                    ] ||
                                                                    group
                                                                        .packaging
                                                                        .bolsas2da[
                                                                        size
                                                                    ];

                                                                if (!hasData)
                                                                    return null;

                                                                return (
                                                                    <tr
                                                                        key={
                                                                            size
                                                                        }
                                                                        className="hover:bg-blue-50 transition-colors"
                                                                    >
                                                                        <td className="border-r border-gray-300 p-2 text-center font-semibold">
                                                                            <div className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                                                                                {
                                                                                    size
                                                                                }
                                                                            </div>
                                                                        </td>
                                                                        <td className="border-r border-gray-300 p-2 text-center">
                                                                            {group
                                                                                .packaging
                                                                                .cajas[
                                                                                size
                                                                            ] ? (
                                                                                <div className="bg-green-100 text-green-800 px-2 py-1 rounded font-bold text-xs">
                                                                                    {
                                                                                        group
                                                                                            .packaging
                                                                                            .cajas[
                                                                                            size
                                                                                        ]
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <span className="text-gray-400">
                                                                                    -
                                                                                </span>
                                                                            )}
                                                                        </td>
                                                                        <td className="border-r border-gray-300 p-2 text-center">
                                                                            {group
                                                                                .packaging
                                                                                .piezas[
                                                                                size
                                                                            ] ? (
                                                                                <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded font-bold text-xs">
                                                                                    {
                                                                                        group
                                                                                            .packaging
                                                                                            .piezas[
                                                                                            size
                                                                                        ]
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <span className="text-gray-400">
                                                                                    -
                                                                                </span>
                                                                            )}
                                                                        </td>
                                                                        <td className="border-r border-gray-300 p-2 text-center">
                                                                            {group
                                                                                .packaging
                                                                                .bolsas1ra[
                                                                                size
                                                                            ] ? (
                                                                                <div className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded font-bold text-xs">
                                                                                    {
                                                                                        group
                                                                                            .packaging
                                                                                            .bolsas1ra[
                                                                                            size
                                                                                        ]
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <span className="text-gray-400">
                                                                                    -
                                                                                </span>
                                                                            )}
                                                                        </td>
                                                                        <td className="p-2 text-center">
                                                                            {group
                                                                                .packaging
                                                                                .bolsas2da[
                                                                                size
                                                                            ] ? (
                                                                                <div className="bg-orange-100 text-orange-800 px-2 py-1 rounded font-bold text-xs">
                                                                                    {
                                                                                        group
                                                                                            .packaging
                                                                                            .bolsas2da[
                                                                                            size
                                                                                        ]
                                                                                    }
                                                                                </div>
                                                                            ) : (
                                                                                <span className="text-gray-400">
                                                                                    -
                                                                                </span>
                                                                            )}
                                                                        </td>
                                                                    </tr>
                                                                );
                                                            },
                                                        )}
                                                    </tbody>
                                                    <tfoot>
                                                        <tr className="bg-gradient-to-r from-green-100 to-emerald-100 border-t-2 border-green-300">
                                                            <td
                                                                className="border-r border-gray-300 p-2 font-bold text-green-800 text-xs"
                                                                colSpan={2}
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <svg
                                                                        className="w-4 h-4"
                                                                        fill="currentColor"
                                                                        viewBox="0 0 20 20"
                                                                    >
                                                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z" />
                                                                        <path
                                                                            clipRule="evenodd"
                                                                            d="M5 8a1 1 0 011 1v6a1 1 0 102 0V9a1 1 0 112 0v6a1 1 0 102 0V9a1 1 0 112 0v6a1 1 0 102 0V9a1 1 0 011-1h2a1 1 0 110 2h-1v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9H3a1 1 0 010-2h2z"
                                                                            fillRule="evenodd"
                                                                        />
                                                                    </svg>
                                                                    TOTAL CAJAS
                                                                </div>
                                                            </td>
                                                            <td
                                                                className="p-2 text-center font-bold text-green-800"
                                                                colSpan={3}
                                                            >
                                                                <div className="bg-green-600 text-white px-3 py-1 rounded-lg font-bold">
                                                                    {Object.values(
                                                                        group
                                                                            .packaging
                                                                            .cajas,
                                                                    ).reduce(
                                                                        (
                                                                            sum,
                                                                            val,
                                                                        ) =>
                                                                            sum +
                                                                            val,
                                                                        0,
                                                                    )}
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr className="bg-gradient-to-r from-yellow-100 to-orange-100">
                                                            <td
                                                                className="border-r border-gray-300 p-2 font-bold text-orange-800 text-xs"
                                                                colSpan={2}
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <svg
                                                                        className="w-4 h-4"
                                                                        fill="currentColor"
                                                                        viewBox="0 0 20 20"
                                                                    >
                                                                        <path
                                                                            clipRule="evenodd"
                                                                            d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z"
                                                                            fillRule="evenodd"
                                                                        />
                                                                    </svg>
                                                                    TOTAL BOLSAS
                                                                </div>
                                                            </td>
                                                            <td
                                                                className="p-2 text-center font-bold text-orange-800"
                                                                colSpan={3}
                                                            >
                                                                <div className="bg-orange-600 text-white px-3 py-1 rounded-lg font-bold">
                                                                    {Object.values(
                                                                        group
                                                                            .packaging
                                                                            .bolsas1ra,
                                                                    ).reduce(
                                                                        (
                                                                            sum,
                                                                            val,
                                                                        ) =>
                                                                            sum +
                                                                            val,
                                                                        0,
                                                                    ) +
                                                                        Object.values(
                                                                            group
                                                                                .packaging
                                                                                .bolsas2da,
                                                                        ).reduce(
                                                                            (
                                                                                sum,
                                                                                val,
                                                                            ) =>
                                                                                sum +
                                                                                val,
                                                                            0,
                                                                        )}
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Signatures */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden mt-8">
                            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4">
                                <h2 className="text-white text-xl font-bold flex items-center gap-3">
                                    <div className="p-2 bg-white/10 rounded-lg">
                                        <svg
                                            className="w-6 h-6"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                clipRule="evenodd"
                                                d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                fillRule="evenodd"
                                            />
                                        </svg>
                                    </div>
                                    FIRMAS Y AUTORIZACIÓN
                                </h2>
                            </div>

                            <div className="p-8">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                    <div className="text-center">
                                        <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200 shadow-sm h-32 flex flex-col justify-end">
                                            <div className="border-t-2 border-green-600 pt-4">
                                                <div className="flex items-center justify-center gap-2 mb-2">
                                                    <svg
                                                        className="w-5 h-5 text-green-600"
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                                    </svg>
                                                    <strong className="text-green-800 font-bold text-sm">
                                                        FIRMA DE RECIBIDO
                                                    </strong>
                                                </div>
                                                <p className="text-green-600 text-xs">
                                                    Cliente receptor
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200 shadow-sm h-32 flex flex-col justify-end">
                                            <div className="border-t-2 border-blue-600 pt-4">
                                                <div className="flex items-center justify-center gap-2 mb-2">
                                                    <svg
                                                        className="w-5 h-5 text-blue-600"
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path
                                                            clipRule="evenodd"
                                                            d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                            fillRule="evenodd"
                                                        />
                                                    </svg>
                                                    <strong className="text-blue-800 font-bold text-sm">
                                                        FIRMA DE ENTREGADO
                                                    </strong>
                                                </div>
                                                <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg font-bold text-xs mt-2">
                                                    LUIS A. MARIANO LUJAN
                                                </div>
                                                <p className="text-blue-600 text-xs mt-1">
                                                    Responsable de entrega
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="text-center">
                                        <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-xl border border-purple-200 shadow-sm h-32 flex flex-col justify-end">
                                            <div className="border-t-2 border-purple-600 pt-4">
                                                <div className="flex items-center justify-center gap-2 mb-2">
                                                    <svg
                                                        className="w-5 h-5 text-purple-600"
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                                                        <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1V5a1 1 0 00-1-1H3zM14 7a1 1 0 00-1 1v6.05A2.5 2.5 0 0115.95 16H17a1 1 0 001-1V8a1 1 0 00-1-1h-3z" />
                                                    </svg>
                                                    <strong className="text-purple-800 font-bold text-sm">
                                                        FIRMA DE TRANSPORTE
                                                    </strong>
                                                </div>
                                                <p className="text-purple-600 text-xs">
                                                    Transportista autorizado
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardBody>
        </Card>
    );
}
