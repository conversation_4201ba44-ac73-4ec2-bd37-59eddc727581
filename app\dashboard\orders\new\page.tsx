"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
    TagIcon,
    DocumentIcon,
    ShoppingBagIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import useS<PERSON> from "swr";

import { CrudFormTemplate } from "@/shared/templates";
import {
    OrderBasicInfoStep,
    OrderPartsStep,
    OrderGarmentsStep,
    OrderReviewStep,
} from "@/features/orders/components/wizard";
import { addToast } from "@/shared/components/ui/hero-ui-client";
import { useOrder } from "@/features/orders/hooks/useOrder";
import { getFormData } from "@/features/orders/actions";
import { SizeOption, OrderFormData } from "@/core/types/types";

// Size order for sorting
const sizeOrder = ["XS", "S", "M", "L", "XL", "XXL"];

function sortSizes(sizes: SizeOption[]): SizeOption[] {
    return [...sizes].sort((a, b) => {
        const ia = sizeOrder.indexOf(a.code.toUpperCase());
        const ib = sizeOrder.indexOf(b.code.toUpperCase());
        const va = ia >= 0 ? ia : sizeOrder.length;
        const vb = ib >= 0 ? ib : sizeOrder.length;

        return va - vb;
    });
}

export default function NewOrderPage() {
    const router = useRouter();
    const { addOrder } = useOrder();

    // Form state
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    const [receivedDate, setReceivedDate] = useState<string>(formattedDate);
    const [estimatedDeliveryDate, setEstimatedDeliveryDate] =
        useState<string>(formattedDate);
    const [formData, setFormData] = useState<OrderFormData>({
        transferNumber: "",
        cutOrder: "",
        batch: "1",
        customerId: "",
        subCustomerId: undefined,
        statusId: "",
        garments: [
            { modelId: "", colorId: "", sizes: [{ sizeId: "", quantity: "" }] },
        ],
        parts: [{ code: "" }],
        notes: [],
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [validationErrors, setValidationErrors] = useState<
        Record<string, string>
    >({});

    // Wizard state
    const [currentStep, setCurrentStep] = useState(0);
    const [completedSteps, setCompletedSteps] = useState<Set<string>>(
        new Set(),
    );

    // Load form data
    const {
        data: formDataResponse,
        error: formDataError,
        mutate,
    } = useSWR("formData", getFormData, {
        revalidateOnFocus: true,
        revalidateOnMount: true,
        revalidateIfStale: true,
    });

    const formDataOptions = formDataResponse?.data;
    const customers = formDataOptions?.customers || [];
    const orderStatuses = formDataOptions?.orderStatuses || [];
    const models = formDataOptions?.models || [];
    const colors =
        formDataOptions?.colors?.map((c: any) => ({
            ...c,
            name: c.name.charAt(0).toUpperCase() + c.name.slice(1),
        })) || [];
    const sizesOptions = formDataOptions
        ? sortSizes(formDataOptions.sizes || [])
        : [];

    // Load saved draft from localStorage
    useEffect(() => {
        try {
            const savedData = localStorage.getItem("orderDraft");

            if (savedData) {
                const parsedData = JSON.parse(savedData);

                if (parsedData && parsedData.formData) {
                    setFormData(parsedData.formData);
                    setReceivedDate(parsedData.receivedDate || formattedDate);
                    setEstimatedDeliveryDate(
                        parsedData.estimatedDeliveryDate || formattedDate,
                    );

                    addToast({
                        title: "Borrador recuperado",
                        description:
                            "Se ha cargado un borrador guardado previamente",
                        color: "primary",
                    });
                }
            }
        } catch (error) {
            console.error("Error loading draft:", error);
        }
    }, [formattedDate]);

    // Auto-save draft
    useEffect(() => {
        const timer = setTimeout(() => {
            try {
                localStorage.setItem(
                    "orderDraft",
                    JSON.stringify({
                        formData,
                        receivedDate,
                        estimatedDeliveryDate,
                    }),
                );
            } catch (error) {
                console.error("Error saving draft:", error);
            }
        }, 2000);

        return () => clearTimeout(timer);
    }, [formData, receivedDate, estimatedDeliveryDate]);

    // Field change handlers
    const handleFieldChange = useCallback((key: string, value: any) => {
        setFormData((prev) => ({ ...prev, [key]: value }));
    }, []);

    const handleDateChange = useCallback(
        (field: "receivedDate" | "estimatedDeliveryDate", value: string) => {
            if (field === "receivedDate") {
                setReceivedDate(value);
            } else {
                setEstimatedDeliveryDate(value);
            }
        },
        [],
    );

    // Parts handlers
    const addOrderPart = useCallback(() => {
        setFormData((prev) => ({
            ...prev,
            parts: [...prev.parts, { code: "" }],
        }));
    }, []);

    const removeOrderPart = useCallback((index: number) => {
        setFormData((prev) => ({
            ...prev,
            parts: prev.parts.filter((_, i) => i !== index),
        }));
    }, []);

    const handleOrderPartChange = useCallback(
        (index: number, value: string) => {
            setFormData((prev) => {
                const updated = [...prev.parts];

                updated[index] = { ...updated[index], code: value };

                return { ...prev, parts: updated };
            });
        },
        [],
    );

    // Garments handlers
    const addGarment = useCallback(() => {
        setFormData((prev) => ({
            ...prev,
            garments: [
                ...prev.garments,
                {
                    modelId: "",
                    colorId: "",
                    sizes: [{ sizeId: "", quantity: "" }],
                },
            ],
        }));
    }, []);

    const removeGarment = useCallback((index: number) => {
        setFormData((prev) => ({
            ...prev,
            garments: prev.garments.filter((_, i) => i !== index),
        }));
    }, []);

    const handleGarmentChange = useCallback(
        (garmentIndex: number, field: string, value: string) => {
            setFormData((prev) => {
                const updated = [...prev.garments];

                if (field === "modelId" || field === "colorId") {
                    updated[garmentIndex] = {
                        ...updated[garmentIndex],
                        [field]: value,
                    };
                }

                return { ...prev, garments: updated };
            });
        },
        [],
    );

    // Sizes handlers
    const addSizeToGarment = useCallback(
        (garmentIndex: number) => {
            setFormData((prev) => {
                const updated = [...prev.garments];
                const updatedGarment = { ...updated[garmentIndex] };

                updatedGarment.sizes = [
                    ...updatedGarment.sizes,
                    {
                        sizeId:
                            sizesOptions.length > 0 ? sizesOptions[0].id : "",
                        quantity: "",
                    },
                ];
                updated[garmentIndex] = updatedGarment;

                return { ...prev, garments: updated };
            });
        },
        [sizesOptions],
    );

    const removeSizeFromGarment = useCallback(
        (garmentIndex: number, sizeIndex: number) => {
            setFormData((prev) => {
                const updated = [...prev.garments];
                const updatedGarment = { ...updated[garmentIndex] };

                updatedGarment.sizes = updatedGarment.sizes.filter(
                    (_, i) => i !== sizeIndex,
                );
                updated[garmentIndex] = updatedGarment;

                return { ...prev, garments: updated };
            });
        },
        [],
    );

    const handleSizeChange = useCallback(
        (
            garmentIndex: number,
            sizeIndex: number,
            field: string,
            value: string,
        ) => {
            setFormData((prev) => {
                const updated = [...prev.garments];
                const updatedGarment = { ...updated[garmentIndex] };
                const updatedSizes = [...updatedGarment.sizes];

                updatedSizes[sizeIndex] = {
                    ...updatedSizes[sizeIndex],
                    [field]: value,
                };
                updatedGarment.sizes = updatedSizes;
                updated[garmentIndex] = updatedGarment;

                return { ...prev, garments: updated };
            });
        },
        [],
    );

    // Pure validation functions (no side effects)
    const getBasicInfoErrors = useCallback(() => {
        const errors: Record<string, string> = {};

        if (!formData.customerId) {
            errors.customerId = "Debes seleccionar un cliente";
        }
        if (!formData.statusId) {
            errors.statusId = "Debes seleccionar un estado";
        }
        if (!estimatedDeliveryDate) {
            errors.estimatedDeliveryDate = "La fecha de entrega es obligatoria";
        }

        return errors;
    }, [formData.customerId, formData.statusId, estimatedDeliveryDate]);

    // Validation functions with side effects (for events)
    const validateBasicInfo = useCallback(() => {
        const errors = getBasicInfoErrors();

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }, [getBasicInfoErrors]);

    const getPartsErrors = useCallback(() => {
        const errors: Record<string, string> = {};

        const partCodes = formData.parts
            .filter((p) => p.code.trim())
            .map((p) => p.code.trim().toUpperCase());

        if (partCodes.length === 0) {
            errors.parts = "Agrega al menos una partida con código";
        } else if (new Set(partCodes).size !== partCodes.length) {
            errors.parts_duplicate =
                "No se permiten códigos de partidas duplicados";
        }

        return errors;
    }, [formData.parts]);

    const validateParts = useCallback(() => {
        const errors = getPartsErrors();

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }, [getPartsErrors]);

    const getGarmentsErrors = useCallback(() => {
        const errors: Record<string, string> = {};

        if (formData.garments.length === 0) {
            errors.garments = "Agrega al menos una prenda";
        } else {
            formData.garments.forEach((garment, idx) => {
                if (!garment.modelId) {
                    errors[`garment_${idx}_model`] = "Selecciona un modelo";
                }
                if (!garment.colorId) {
                    errors[`garment_${idx}_color`] = "Selecciona un color";
                }

                if (garment.sizes.length === 0) {
                    errors[`garment_${idx}_sizes`] =
                        "Agrega al menos una talla";
                } else {
                    const sizeIds = garment.sizes.map((sz) => sz.sizeId);

                    if (new Set(sizeIds).size !== sizeIds.length) {
                        errors[`garment_${idx}_duplicate`] =
                            "No se permiten tallas duplicadas";
                    }

                    garment.sizes.forEach((size, sizeIdx) => {
                        const qty = parseInt(size.quantity, 10);

                        if (!size.sizeId) {
                            errors[`garment_${idx}_size_${sizeIdx}_id`] =
                                "Selecciona una talla";
                        }
                        if (isNaN(qty) || qty <= 0) {
                            errors[`garment_${idx}_size_${sizeIdx}_qty`] =
                                "Cantidad debe ser mayor a 0";
                        }
                    });
                }
            });
        }

        return errors;
    }, [formData.garments]);

    const validateGarments = useCallback(() => {
        const errors = getGarmentsErrors();

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }, [getGarmentsErrors]);

    // Wizard steps configuration
    const wizardSteps = [
        {
            id: "basic-info",
            title: "Información Básica",
            subtitle: "Datos principales de la orden",
            icon: <TagIcon className="w-5 h-5" />,
            component: (
                <OrderBasicInfoStep
                    customers={customers}
                    errors={validationErrors}
                    estimatedDeliveryDate={estimatedDeliveryDate}
                    formData={formData}
                    orderStatuses={orderStatuses}
                    receivedDate={receivedDate}
                    onChange={handleFieldChange}
                    onDateChange={handleDateChange}
                />
            ),
            validation: () => Object.keys(getBasicInfoErrors()).length === 0,
        },
        {
            id: "parts",
            title: "Partidas",
            subtitle: "Códigos de partida",
            icon: <DocumentIcon className="w-5 h-5" />,
            component: (
                <OrderPartsStep
                    errors={validationErrors}
                    parts={formData.parts}
                    onAddPart={addOrderPart}
                    onPartChange={handleOrderPartChange}
                    onRemovePart={removeOrderPart}
                />
            ),
            validation: () => Object.keys(getPartsErrors()).length === 0,
        },
        {
            id: "garments",
            title: "Prendas",
            subtitle: "Modelos, colores y tallas",
            icon: <ShoppingBagIcon className="w-5 h-5" />,
            component: (
                <OrderGarmentsStep
                    colors={colors}
                    errors={validationErrors}
                    garments={formData.garments}
                    models={models}
                    sizesOptions={sizesOptions}
                    onAddGarment={addGarment}
                    onAddSize={addSizeToGarment}
                    onGarmentChange={handleGarmentChange}
                    onRemoveGarment={removeGarment}
                    onRemoveSize={removeSizeFromGarment}
                    onSizeChange={handleSizeChange}
                />
            ),
            validation: () => Object.keys(getGarmentsErrors()).length === 0,
        },
        {
            id: "review",
            title: "Revisión",
            subtitle: "Confirma los detalles",
            icon: <CheckCircleIcon className="w-5 h-5" />,
            component: (
                <OrderReviewStep
                    colors={colors}
                    customers={customers}
                    estimatedDeliveryDate={estimatedDeliveryDate}
                    formData={formData}
                    models={models}
                    orderStatuses={orderStatuses}
                    receivedDate={receivedDate}
                    sizesOptions={sizesOptions}
                    onEditStep={setCurrentStep}
                />
            ),
            validation: () => true,
        },
    ];

    // Handle step change
    const handleStepChange = (step: number) => {
        // Validate current step if moving forward
        if (step > currentStep) {
            let isValid = false;

            switch (currentStep) {
                case 0:
                    isValid = validateBasicInfo();
                    break;
                case 1:
                    isValid = validateParts();
                    break;
                case 2:
                    isValid = validateGarments();
                    break;
                default:
                    isValid = true;
            }

            if (isValid) {
                setCompletedSteps(
                    (prev) => new Set([...prev, wizardSteps[currentStep].id]),
                );
                setCurrentStep(step);
            }
        } else {
            // Allow going back without validation
            setCurrentStep(step);
        }
    };

    // Submit handler
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate all steps
        const allValid = wizardSteps.every((step, index) => {
            setCurrentStep(index);

            return step.validation();
        });

        if (!allValid) {
            addToast({
                title: "Error",
                description: "Por favor, corrige los errores en el formulario",
                color: "danger",
            });

            return;
        }

        setIsSubmitting(true);

        try {
            // Prepare order data
            const orderData = {
                customerId: formData.customerId,
                subCustomerId: formData.subCustomerId || undefined,
                statusId: formData.statusId,
                receivedDate: receivedDate,
                estimatedDeliveryDate: estimatedDeliveryDate || undefined,
                transferNumber: formData.transferNumber || undefined,
                cutOrder: formData.cutOrder || undefined,
                batch: formData.batch || undefined,
                parts: formData.parts
                    .filter((part) => part.code.trim() !== "")
                    .map((part) => ({ code: part.code.trim() })),
                garments: formData.garments
                    .filter(
                        (garment) =>
                            garment.modelId &&
                            garment.colorId &&
                            garment.sizes.some(
                                (size) =>
                                    size.sizeId && parseInt(size.quantity) > 0,
                            ),
                    )
                    .map((garment) => ({
                        modelId: garment.modelId,
                        colorId: garment.colorId,
                        sizes: garment.sizes
                            .filter(
                                (size) =>
                                    size.sizeId && parseInt(size.quantity) > 0,
                            )
                            .map((size) => ({
                                sizeId: size.sizeId,
                                quantity: parseInt(size.quantity),
                            })),
                    })),
                notes: [],
            };

            // Create order
            const result = await addOrder(orderData);

            // Success
            addToast({
                title: "Orden creada",
                description: "La orden ha sido creada exitosamente",
                color: "success",
            });

            // Clear draft
            localStorage.removeItem("orderDraft");

            // Redirect
            router.push("/dashboard/orders");
        } catch (error: any) {
            console.error("Error creating order:", error);
            addToast({
                title: "Error",
                description:
                    error.message || "Ocurrió un error al crear la orden",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    if (formDataError) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-600 mb-4">
                        Error al cargar los datos del formulario
                    </p>
                    <button
                        className="text-primary hover:underline"
                        onClick={() => window.location.reload()}
                    >
                        Intentar nuevamente
                    </button>
                </div>
            </div>
        );
    }

    return (
        <CrudFormTemplate
            allowStepNavigation
            showProgress
            backLabel="Volver a órdenes"
            backRoute="/dashboard/orders"
            breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Órdenes", href: "/dashboard/orders" },
            ]}
            completedSteps={completedSteps}
            currentStep={currentStep}
            isSubmitting={isSubmitting}
            steps={wizardSteps}
            subtitle="Crea una nueva orden de producción"
            title="Nueva Orden"
            onStepChange={handleStepChange}
            onSubmit={handleSubmit}
        />
    );
}
