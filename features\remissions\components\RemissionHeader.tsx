"use client";

import { generateRemissionNumber } from "@/shared/services/pdf-generation";

interface RemissionHeaderProps {
    remissionId?: string;
    date?: Date;
}

export function RemissionHeader({
    remissionId = generateRemissionNumber(),
    date = new Date(),
}: RemissionHeaderProps) {
    const formattedDate = new Intl.DateTimeFormat("es-ES", {
        year: "numeric",
        month: "long",
        day: "numeric",
    }).format(date);

    return (
        <div className="mb-6 print:mb-8">
            <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                <div>
                    <h1 className="text-2xl font-bold text-blue-800 print:text-black">
                        REMISIÓN DE ASIGNACIÓN
                    </h1>
                    <div className="mt-2 space-y-1">
                        <p className="text-gray-700">
                            <span className="font-medium">Fecha:</span>{" "}
                            {formattedDate}
                        </p>
                        <p className="text-gray-700">
                            <span className="font-medium">No. Remisión:</span>{" "}
                            {remissionId}
                        </p>
                    </div>
                </div>

                <div className="text-right">
                    {/* Reemplazar con el logo real de la empresa */}
                    <div className="h-16 w-32 bg-gray-200 rounded-md flex items-center justify-center print:mb-2">
                        <span className="text-gray-500 text-sm">
                            Logo Empresa
                        </span>
                    </div>
                    <p className="text-sm mt-2 text-gray-700">
                        Lohari Textiles
                    </p>
                    <p className="text-xs text-gray-500">
                        Gestión de Producción Textil
                    </p>
                </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200">
                <h2 className="text-lg font-medium text-gray-800">
                    Información de Asignación
                </h2>
            </div>
        </div>
    );
}
