"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";

import { prisma } from "@/shared/lib/prisma";
import { handleDbError } from "@/shared/lib/db";

import { updateRemissionSchema } from "../schemas/validation";

type UpdateRemissionData = z.infer<typeof updateRemissionSchema>;

/**
 * Updates an existing remission
 */
export async function updateRemission(data: UpdateRemissionData) {
    try {
        // Validate input data
        const validated = updateRemissionSchema.parse(data);

        // Start transaction
        const result = await prisma.$transaction(async (tx) => {
            // Get current remission data for history
            const currentRemission = await tx.remission.findUnique({
                where: { id: validated.remissionId },
                include: {
                    remissionItems: true,
                },
            });

            if (!currentRemission) {
                throw new Error("Remisión no encontrada");
            }

            // Prepare metadata for history
            const changes: any = {
                timestamp: new Date().toISOString(),
            };

            // Update notes if provided
            if (
                validated.notes !== undefined &&
                validated.notes !== currentRemission.notes
            ) {
                changes.previousNotes = currentRemission.notes;
                changes.newNotes = validated.notes;

                await tx.remission.update({
                    where: { id: validated.remissionId },
                    data: { notes: validated.notes },
                });
            }

            // Update items if provided
            if (validated.items && validated.items.length > 0) {
                const itemChanges: any[] = [];

                for (const item of validated.items) {
                    const currentItem = currentRemission.remissionItems.find(
                        (ri) => ri.id === item.id,
                    );

                    if (currentItem && currentItem.quantity !== item.quantity) {
                        itemChanges.push({
                            itemId: item.id,
                            previousQuantity: currentItem.quantity,
                            newQuantity: item.quantity,
                            modelCode: currentItem.modelCode,
                            colorName: currentItem.colorName,
                            sizeCode: currentItem.sizeCode,
                        });

                        // Update the item
                        await tx.remissionItem.update({
                            where: { id: item.id },
                            data: { quantity: item.quantity },
                        });
                    }
                }

                if (itemChanges.length > 0) {
                    changes.itemChanges = itemChanges;
                }
            }

            // Create history entry if there were changes
            if (Object.keys(changes).length > 1) {
                // More than just timestamp
                await tx.remissionHistory.create({
                    data: {
                        remissionId: validated.remissionId,
                        action: "UPDATED",
                        metadata: changes,
                    },
                });
            }

            // Return updated remission
            return await tx.remission.findUnique({
                where: { id: validated.remissionId },
                include: {
                    contractor: true,
                    remissionItems: true,
                    assignments: {
                        include: {
                            assignment: {
                                include: {
                                    order: {
                                        include: {
                                            parts: true,
                                        },
                                    },
                                    garmentSize: {
                                        include: {
                                            size: true,
                                            garment: {
                                                include: {
                                                    model: true,
                                                    color: true,
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                    history: {
                        orderBy: {
                            timestamp: "desc",
                        },
                    },
                },
            });
        });

        revalidatePath("/dashboard/remissions");
        revalidatePath(`/dashboard/remissions/${validated.remissionId}`);

        return { success: true, data: result };
    } catch (error) {
        return handleDbError(error);
    }
}

/**
 * Validates if item quantities don't exceed original assignments
 */
export async function validateRemissionItemQuantities(
    remissionId: string,
    items: Array<{ id: string; quantity: number }>,
) {
    try {
        // Get remission with assignments
        const remission = await prisma.remission.findUnique({
            where: { id: remissionId },
            include: {
                remissionItems: true,
                assignments: {
                    include: {
                        assignment: {
                            include: {
                                garmentSize: {
                                    include: {
                                        size: true,
                                        garment: {
                                            include: {
                                                model: true,
                                                color: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });

        if (!remission) {
            return { success: false, error: "Remisión no encontrada" };
        }

        // Build a map of maximum quantities per model-color-size combination
        const maxQuantities = new Map<string, number>();

        remission.assignments.forEach((ra) => {
            const assignment = ra.assignment;

            if (assignment.garmentSize) {
                const key = `${assignment.garmentSize.garment.model.code}-${assignment.garmentSize.garment.color.name}-${assignment.garmentSize.size.code}`;
                const currentMax = maxQuantities.get(key) || 0;

                maxQuantities.set(key, currentMax + assignment.quantity);
            }
        });

        // Validate each item
        for (const item of items) {
            const remissionItem = remission.remissionItems.find(
                (ri) => ri.id === item.id,
            );

            if (!remissionItem) {
                return {
                    success: false,
                    error: `Item con ID ${item.id} no encontrado`,
                };
            }

            const key = `${remissionItem.modelCode}-${remissionItem.colorName}-${remissionItem.sizeCode}`;
            const maxQuantity = maxQuantities.get(key) || 0;

            if (item.quantity > maxQuantity) {
                return {
                    success: false,
                    error: `La cantidad para ${remissionItem.modelCode} - ${remissionItem.colorName} - ${remissionItem.sizeCode} excede el máximo permitido (${maxQuantity})`,
                };
            }
        }

        return { success: true };
    } catch (error) {
        return handleDbError(error);
    }
}
