"use client";

import React from "react";
import { Button, ButtonGroup } from "@heroui/button";
import {
    ArrowLeft,
    Edit,
    Check,
    FileText,
    Download,
    Maximize2,
} from "lucide-react";

interface ToolbarProps {
    folio: string;
    editMode: boolean;
    orientation: "portrait" | "landscape";
    onBack: () => void;
    onToggleEdit: () => void;
    onChangeOrientation: (orientation: "portrait" | "landscape") => void;
    onGeneratePDF: () => void;
}

export const Toolbar: React.FC<ToolbarProps> = ({
    folio,
    editMode,
    orientation,
    onBack,
    onToggleEdit,
    onChangeOrientation,
    onGeneratePDF,
}) => {
    return (
        <div className="flex items-center justify-between px-4 py-3 border-b bg-white">
            {/* Left Section */}
            <div className="flex items-center gap-4">
                <Button
                    size="sm"
                    startContent={<ArrowLeft className="w-4 h-4" />}
                    variant="ghost"
                    onPress={onBack}
                >
                    Volver
                </Button>

                <div className="flex items-center gap-2">
                    <FileText className="w-5 h-5 text-gray-600" />
                    <h2 className="text-lg font-semibold text-gray-800">
                        Remisión #{folio}
                    </h2>
                </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center gap-3">
                <Button
                    color={editMode ? "success" : "default"}
                    size="sm"
                    startContent={
                        editMode ? (
                            <Check className="w-4 h-4" />
                        ) : (
                            <Edit className="w-4 h-4" />
                        )
                    }
                    variant="flat"
                    onPress={onToggleEdit}
                >
                    {editMode ? "Guardar cambios" : "Editar"}
                </Button>

                <ButtonGroup size="sm" variant="flat">
                    <Button
                        isIconOnly
                        color={
                            orientation === "portrait" ? "primary" : "default"
                        }
                        title="Orientación vertical"
                        onPress={() => onChangeOrientation("portrait")}
                    >
                        <Maximize2 className="w-4 h-4" />
                    </Button>
                    <Button
                        isIconOnly
                        color={
                            orientation === "landscape" ? "primary" : "default"
                        }
                        title="Orientación horizontal"
                        onPress={() => onChangeOrientation("landscape")}
                    >
                        <Maximize2 className="w-4 h-4 rotate-90" />
                    </Button>
                </ButtonGroup>

                <Button
                    color="primary"
                    size="sm"
                    startContent={<Download className="w-4 h-4" />}
                    onPress={onGeneratePDF}
                >
                    Generar PDF
                </Button>
            </div>
        </div>
    );
};
