// types/remission/pdf.ts
/**
 * PDF-specific interfaces for remission document generation
 * These interfaces extend core with PDF layout and formatting data
 */

import type { RemissionCore } from "./core";
import type { ContractorInfo } from "./preview";

export interface RemissionPDF extends RemissionCore {
    contractor: ContractorFullDetails;
    order: OrderFullDetails;
    company: CompanyInfo;
    formattedItems: PDFTableRow[];
    pageLayout: PDFLayoutConfig;
}

export interface ContractorFullDetails extends ContractorInfo {
    address?: string;
    taxId?: string;
}

export interface OrderFullDetails {
    id: string;
    cutOrder?: string;
    creationDate: Date;
    parts?: Array<{
        id: string;
        code: string;
    }>;
    customer: {
        name: string;
        code: string;
    };
}

export interface CompanyInfo {
    name: string;
    logo: string;
    address: string;
    phone: string;
    email: string;
    taxId: string;
}

export interface PDFTableRow {
    modelCode: string;
    description: string;
    color: string;
    sizes: Record<string, number>;
    total: number;
}

export interface PDFLayoutConfig {
    orientation: "portrait" | "landscape";
    margins: PDFMargins;
    fontSize: number;
    showLogo: boolean;
    showParts: boolean;
}

export interface PDFMargins {
    top: number;
    right: number;
    bottom: number;
    left: number;
}
