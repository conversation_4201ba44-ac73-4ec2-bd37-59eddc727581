# 📝 Notes Feature

## 📋 Descripción
Sistema de notas y comentarios para órdenes, clientes y seguimiento general.

## 🏗️ Componentes

### Notes
Componente principal de notas.
```tsx
import { Notes } from '@/features/notes'
```

### NotesList
Lista de notas con filtros.

### NoteEditor
Editor de notas con formato rico.

## 🪝 Hooks

### useNotes
Gestión de estado de notas.
```tsx
const { notes, addNote, deleteNote } = useNotes()
```

### useNoteBulkActions
Acciones masivas en notas.
```tsx
const { bulkDelete, bulkArchive } = useNoteBulkActions()
```

## 🎯 Uso

```tsx
import { Notes, useNotes } from '@/features/notes'

export default function NotesSection({ orderId }) {
  const { notes } = useNotes(orderId)
  return <Notes items={notes} />
}
```