"use client";

import type {
    CreatePackingFromOrdersInput,
    SelectedOrderProducts,
    CompanyInfo,
} from "@/lib/types/packing";
import type { OrderForPacking } from "@/features/packings/actions/select-orders-for-packing";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Progress,
    Input,
    Textarea,
    Divider,
    Chip,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
    Select,
    SelectItem,
} from "@heroui/react";
import {
    FileText,
    AlertCircle,
    CheckCircle,
    ChevronLeft,
    ChevronRight,
    Save,
    Eye,
    ClipboardList,
    BadgeCheck,
} from "lucide-react";
import { format } from "date-fns";

import { OrderSelectionStep } from "@/features/packings/components/OrderSelectionStep";
import { QualityDistributionStep } from "@/features/packings/components/QualityDistributionStep";
import { getCompanySettings } from "@/features/settings/actions/company-settings";
import { createPackingFromOrders } from "@/features/packings/actions/create-packing-from-orders";

interface PackingNewWizardV2Props {
    initialCustomerId?: string;
}

const steps = [
    {
        id: "general",
        title: "Información General",
        icon: FileText,
        description: "Cliente y fecha de entrega",
    },
    {
        id: "orders",
        title: "Selección de Órdenes",
        icon: ClipboardList,
        description: "Órdenes a incluir en el packing",
    },
    {
        id: "quality",
        title: "Clasificación de Calidad",
        icon: BadgeCheck,
        description: "Distribución por calidad",
    },
    {
        id: "summary",
        title: "Resumen y Notas",
        icon: CheckCircle,
        description: "Revisión final",
    },
];

export function PackingNewWizardV2({
    initialCustomerId,
}: PackingNewWizardV2Props) {
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { isOpen, onOpen, onClose } = useDisclosure();

    // Form data
    const [customerId, setCustomerId] = useState(initialCustomerId || "");
    const [deliveryDate, setDeliveryDate] = useState(new Date());
    const [notes, setNotes] = useState("");
    const [transportNotes, setTransportNotes] = useState("");

    // Order selection
    const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
    const [orderDetails, setOrderDetails] = useState<
        Map<string, OrderForPacking>
    >(new Map());

    // Quality distribution
    const [qualityDistribution, setQualityDistribution] = useState<
        SelectedOrderProducts[]
    >([]);

    // Company info
    const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);

    // Load company settings
    useEffect(() => {
        getCompanySettings().then((result) => {
            if (result.success && result.data) {
                setCompanyInfo({
                    name: result.data.companyName,
                    logo: result.data.companyLogo,
                    rfc: result.data.rfc,
                    address: result.data.address,
                    city: result.data.city,
                    state: result.data.state,
                    postalCode: result.data.postalCode,
                    country: result.data.country,
                    phone: result.data.phone,
                    email: result.data.email,
                    website: result.data.website,
                });
            }
        });
    }, []);

    const handleNext = () => {
        // Validar antes de avanzar
        if (currentStep === 0 && !customerId) {
            addToast({
                title: "Error",
                description: "Debe seleccionar un cliente",
                color: "danger",
            });

            return;
        }

        if (currentStep === 1 && selectedOrders.length === 0) {
            addToast({
                title: "Error",
                description: "Debe seleccionar al menos una orden",
                color: "danger",
            });

            return;
        }

        if (currentStep < steps.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handlePrevious = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleOrdersChange = (
        orders: string[],
        details: Map<string, OrderForPacking>,
    ) => {
        setSelectedOrders(orders);
        setOrderDetails(details);
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);
        try {
            const packingData: CreatePackingFromOrdersInput = {
                customerId,
                deliveryDate,
                notes: notes || undefined,
                transportNotes: transportNotes || undefined,
                selectedProducts: qualityDistribution,
                companyInfo,
            };

            const result = await createPackingFromOrders(packingData);

            if (result.success && result.data) {
                addToast({
                    title: "Éxito",
                    description: "Packing creado exitosamente",
                    color: "success",
                });
                router.push(`/dashboard/packings/${result.data.id}`);
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al crear el packing",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al crear el packing",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    // Calcular estadísticas del resumen
    const getSummaryStats = () => {
        let totalPrimera = 0;
        let totalSegunda = 0;
        let totalManchada = 0;
        let totalIncompleta = 0;
        const totalOrders = selectedOrders.length;
        let totalProducts = 0;

        qualityDistribution.forEach((orderDist) => {
            totalProducts += orderDist.productGroups.length;

            orderDist.productGroups.forEach((group) => {
                group.qualityDistribution.forEach((dist) => {
                    totalPrimera += dist.primera;
                    totalSegunda += dist.segunda;
                    totalManchada += dist.manchada;
                    totalIncompleta += dist.incompleta;
                });
            });
        });

        return {
            totalOrders,
            totalProducts,
            totalPiezas:
                totalPrimera + totalSegunda + totalManchada + totalIncompleta,
            totalPrimera,
            totalSegunda,
            totalManchada,
            totalIncompleta,
        };
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 0: // General Info
                return (
                    <div className="space-y-6">
                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                Información del Packing
                            </h3>

                            <div className="space-y-4">
                                <Select
                                    isRequired
                                    label="Cliente"
                                    placeholder="Seleccione el cliente"
                                    selectedKeys={
                                        customerId ? [customerId] : []
                                    }
                                    onSelectionChange={(keys) => {
                                        const selectedKey = Array.from(
                                            keys,
                                        )[0] as string;

                                        setCustomerId(selectedKey || "");
                                    }}
                                >
                                    {/* Note: Customer list will need to be loaded */}
                                    <SelectItem key="placeholder">
                                        Seleccione un cliente
                                    </SelectItem>
                                </Select>

                                <Input
                                    isRequired
                                    label="Fecha de Entrega"
                                    type="date"
                                    value={format(deliveryDate, "yyyy-MM-dd")}
                                    onChange={(e) => {
                                        setDeliveryDate(
                                            new Date(e.target.value),
                                        );
                                    }}
                                />
                            </div>
                        </div>

                        <Divider />

                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                Información del Remitente
                            </h3>
                            {companyInfo ? (
                                <Card className="bg-default-50">
                                    <CardBody className="p-4">
                                        <div className="flex items-start gap-4">
                                            {companyInfo.logo && (
                                                <img
                                                    alt={companyInfo.name}
                                                    className="w-16 h-16 object-contain"
                                                    src={companyInfo.logo}
                                                />
                                            )}
                                            <div className="flex-1">
                                                <p className="font-semibold">
                                                    {companyInfo.name}
                                                </p>
                                                {companyInfo.rfc && (
                                                    <p className="text-sm text-default-600">
                                                        RFC: {companyInfo.rfc}
                                                    </p>
                                                )}
                                                {companyInfo.address && (
                                                    <p className="text-sm text-default-600">
                                                        {companyInfo.address},{" "}
                                                        {companyInfo.city},{" "}
                                                        {companyInfo.state}
                                                    </p>
                                                )}
                                                {companyInfo.phone && (
                                                    <p className="text-sm text-default-600">
                                                        Tel: {companyInfo.phone}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </CardBody>
                                </Card>
                            ) : (
                                <Card className="bg-warning-50">
                                    <CardBody className="p-4">
                                        <div className="flex items-center gap-2">
                                            <AlertCircle className="w-5 h-5 text-warning" />
                                            <p className="text-sm">
                                                Configure la información de su
                                                empresa en Ajustes
                                            </p>
                                        </div>
                                    </CardBody>
                                </Card>
                            )}
                        </div>
                    </div>
                );

            case 1: // Order Selection
                console.log(
                    "🎯 Renderizando OrderSelectionStep con customerId:",
                    customerId,
                );

                return (
                    <OrderSelectionStep
                        customerId={customerId}
                        selectedOrders={selectedOrders}
                        onNext={handleNext}
                        onOrdersChange={handleOrdersChange}
                    />
                );

            case 2: // Quality Distribution
                return (
                    <QualityDistributionStep
                        selectedOrders={orderDetails}
                        onDistributionChange={setQualityDistribution}
                        onNext={handleNext}
                        onPrevious={handlePrevious}
                    />
                );

            case 3: // Summary
                const stats = getSummaryStats();

                return (
                    <div className="space-y-6">
                        <h3 className="text-lg font-semibold">
                            Resumen del Packing
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <Card>
                                <CardHeader className="pb-3">
                                    <h4 className="font-medium">
                                        Información General
                                    </h4>
                                </CardHeader>
                                <CardBody className="pt-0 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Cliente:
                                        </span>
                                        <span className="font-medium">
                                            {customerId}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Fecha Entrega:
                                        </span>
                                        <span className="font-medium">
                                            {format(deliveryDate, "dd/MM/yyyy")}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Total Órdenes:
                                        </span>
                                        <span className="font-medium">
                                            {stats.totalOrders}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Total Productos:
                                        </span>
                                        <span className="font-medium">
                                            {stats.totalProducts}
                                        </span>
                                    </div>
                                </CardBody>
                            </Card>

                            <Card>
                                <CardHeader className="pb-3">
                                    <h4 className="font-medium">
                                        Resumen por Calidad
                                    </h4>
                                </CardHeader>
                                <CardBody className="pt-0 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Primera:
                                        </span>
                                        <span className="font-medium text-green-600">
                                            {stats.totalPrimera}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Segunda:
                                        </span>
                                        <span className="font-medium text-yellow-600">
                                            {stats.totalSegunda}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Manchada:
                                        </span>
                                        <span className="font-medium text-red-600">
                                            {stats.totalManchada}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Incompleta:
                                        </span>
                                        <span className="font-medium text-gray-600">
                                            {stats.totalIncompleta}
                                        </span>
                                    </div>
                                    <Divider className="my-2" />
                                    <div className="flex justify-between">
                                        <span className="text-default-600 font-semibold">
                                            Total Piezas:
                                        </span>
                                        <span className="font-bold text-lg">
                                            {stats.totalPiezas}
                                        </span>
                                    </div>
                                </CardBody>
                            </Card>
                        </div>

                        {/* Órdenes incluidas */}
                        <Card>
                            <CardHeader className="pb-3">
                                <h4 className="font-medium">
                                    Órdenes Incluidas
                                </h4>
                            </CardHeader>
                            <CardBody className="pt-0">
                                {selectedOrders.length === 0 ? (
                                    <div className="text-center py-8">
                                        <AlertCircle className="w-12 h-12 text-warning mx-auto mb-3" />
                                        <p className="text-warning font-medium">
                                            No se han seleccionado órdenes
                                        </p>
                                        <p className="text-sm text-default-500 mt-1">
                                            Regrese al paso anterior para
                                            seleccionar órdenes
                                        </p>
                                    </div>
                                ) : (
                                    <div className="space-y-2">
                                        {Array.from(orderDetails.values()).map(
                                            (order) => (
                                                <div
                                                    key={order.id}
                                                    className="flex items-center justify-between p-3 bg-default-50 rounded-lg"
                                                >
                                                    <div>
                                                        <p className="font-medium">
                                                            {order.displayName}
                                                        </p>
                                                        <div className="flex gap-2 mt-1">
                                                            {order.cutOrder && (
                                                                <Chip
                                                                    size="sm"
                                                                    variant="flat"
                                                                >
                                                                    OC:{" "}
                                                                    {
                                                                        order.cutOrder
                                                                    }
                                                                </Chip>
                                                            )}
                                                            {order.transferNumber && (
                                                                <Chip
                                                                    size="sm"
                                                                    variant="flat"
                                                                >
                                                                    Trans:{" "}
                                                                    {
                                                                        order.transferNumber
                                                                    }
                                                                </Chip>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <Chip
                                                        color="primary"
                                                        variant="flat"
                                                    >
                                                        {order.products.length}{" "}
                                                        productos
                                                    </Chip>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                )}
                            </CardBody>
                        </Card>

                        <div className="space-y-4">
                            <Textarea
                                label="Notas Generales"
                                minRows={3}
                                placeholder="Agregar notas o instrucciones especiales..."
                                value={notes}
                                onChange={(e) => setNotes(e.target.value)}
                            />

                            <Textarea
                                label="Notas de Transporte"
                                minRows={3}
                                placeholder="Instrucciones especiales para el transportista..."
                                value={transportNotes}
                                onChange={(e) =>
                                    setTransportNotes(e.target.value)
                                }
                            />
                        </div>

                        <Card className="bg-warning-50 dark:bg-warning-900/20">
                            <CardBody className="p-4">
                                <div className="flex items-start gap-3">
                                    <AlertCircle className="w-5 h-5 text-warning mt-0.5" />
                                    <div className="space-y-1">
                                        <p className="font-medium text-sm">
                                            Antes de confirmar:
                                        </p>
                                        <ul className="text-sm text-default-600 space-y-1 list-disc list-inside">
                                            <li>
                                                Verifique que las órdenes
                                                seleccionadas sean correctas
                                            </li>
                                            <li>
                                                Confirme la distribución de
                                                calidad de cada producto
                                            </li>
                                            <li>Revise la fecha de entrega</li>
                                        </ul>
                                        {/* Debug info */}
                                        <div className="mt-3 text-xs text-gray-500 space-y-1">
                                            <p>
                                                Debug: Órdenes:{" "}
                                                {selectedOrders.length},
                                                Distribución:{" "}
                                                {qualityDistribution.length},
                                                Piezas:{" "}
                                                {getSummaryStats().totalPiezas}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </div>
                );
        }
    };

    return (
        <div className="max-w-6xl mx-auto">
            {/* Progress Bar */}
            <Card className="mb-6">
                <CardBody className="p-6">
                    <div className="flex items-center justify-between mb-4">
                        {steps.map((step, index) => {
                            const Icon = step.icon;
                            const isActive = index === currentStep;
                            const isCompleted = index < currentStep;

                            return (
                                <div
                                    key={step.id}
                                    className="flex items-center flex-1"
                                >
                                    <div className="flex items-center">
                                        <div
                                            className={`
                      flex items-center justify-center w-10 h-10 rounded-full
                      ${
                          isActive
                              ? "bg-primary text-white"
                              : isCompleted
                                ? "bg-success text-white"
                                : "bg-default-200 text-default-600"
                      }
                    `}
                                        >
                                            {isCompleted ? (
                                                <CheckCircle className="w-5 h-5" />
                                            ) : (
                                                <Icon className="w-5 h-5" />
                                            )}
                                        </div>
                                        <div className="ml-3 hidden md:block">
                                            <p
                                                className={`text-sm font-medium ${isActive ? "text-primary" : "text-default-600"}`}
                                            >
                                                {step.title}
                                            </p>
                                            <p className="text-xs text-default-500">
                                                {step.description}
                                            </p>
                                        </div>
                                    </div>
                                    {index < steps.length - 1 && (
                                        <div
                                            className={`
                      flex-1 h-1 mx-4 rounded
                      ${index < currentStep ? "bg-success" : "bg-default-200"}
                    `}
                                        />
                                    )}
                                </div>
                            );
                        })}
                    </div>
                    <Progress
                        aria-label="Progreso del formulario"
                        className="mt-2"
                        color="primary"
                        size="sm"
                        value={((currentStep + 1) / steps.length) * 100}
                    />
                </CardBody>
            </Card>

            {/* Step Content */}
            <Card>
                <CardBody className="p-6">
                    <AnimatePresence mode="wait">
                        <motion.div
                            key={currentStep}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -20 }}
                            initial={{ opacity: 0, x: 20 }}
                            transition={{ duration: 0.3 }}
                        >
                            {renderStepContent()}
                        </motion.div>
                    </AnimatePresence>
                </CardBody>
            </Card>

            {/* Navigation Buttons */}
            {currentStep !== 1 &&
                currentStep !== 2 && ( // Steps 1 and 2 have their own navigation
                    <div className="flex justify-between mt-6">
                        <Button
                            isDisabled={currentStep === 0 || isSubmitting}
                            startContent={<ChevronLeft className="w-4 h-4" />}
                            variant="light"
                            onPress={handlePrevious}
                        >
                            Anterior
                        </Button>

                        <div className="flex gap-2">
                            {currentStep === steps.length - 1 ? (
                                <>
                                    <Button
                                        color="primary"
                                        startContent={
                                            <Eye className="w-4 h-4" />
                                        }
                                        variant="flat"
                                        onPress={onOpen}
                                    >
                                        Vista Previa
                                    </Button>
                                    <Button
                                        color="primary"
                                        endContent={
                                            <Save className="w-4 h-4" />
                                        }
                                        isDisabled={
                                            selectedOrders.length === 0 ||
                                            qualityDistribution.length === 0 ||
                                            getSummaryStats().totalPiezas === 0
                                        }
                                        isLoading={isSubmitting}
                                        onPress={handleSubmit}
                                    >
                                        Crear Packing
                                    </Button>
                                </>
                            ) : (
                                <Button
                                    color="primary"
                                    endContent={
                                        <ChevronRight className="w-4 h-4" />
                                    }
                                    isDisabled={
                                        (currentStep === 0 && !customerId) ||
                                        (currentStep === 1 &&
                                            selectedOrders.length === 0)
                                    }
                                    onPress={handleNext}
                                >
                                    Siguiente
                                </Button>
                            )}
                        </div>
                    </div>
                )}

            {/* Preview Modal */}
            <Modal
                isOpen={isOpen}
                scrollBehavior="inside"
                size="2xl"
                onClose={onClose}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader>Vista Previa del Packing</ModalHeader>
                            <ModalBody>
                                <pre className="text-xs bg-default-100 p-4 rounded-lg overflow-auto">
                                    {JSON.stringify(
                                        {
                                            customerId,
                                            deliveryDate,
                                            notes,
                                            transportNotes,
                                            selectedOrders,
                                            qualityDistribution,
                                        },
                                        null,
                                        2,
                                    )}
                                </pre>
                            </ModalBody>
                            <ModalFooter>
                                <Button variant="light" onPress={onClose}>
                                    Cerrar
                                </Button>
                                <Button
                                    color="primary"
                                    isDisabled={
                                        selectedOrders.length === 0 ||
                                        qualityDistribution.length === 0 ||
                                        getSummaryStats().totalPiezas === 0
                                    }
                                    isLoading={isSubmitting}
                                    onPress={() => {
                                        onClose();
                                        handleSubmit();
                                    }}
                                >
                                    Confirmar y Crear
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </div>
    );
}
