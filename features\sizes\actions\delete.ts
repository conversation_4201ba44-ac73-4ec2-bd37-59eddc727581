"use server";

import { db, handleDbError } from "@/shared/lib/db";

import { revalidateSizeCache } from "./revalidate";

/**
 * Elimina una talla
 */
export async function deleteSize(id: string) {
    if (!id) return { success: false, error: "ID no válido" };

    try {
        // Verificar si tiene garments asociadas primero
        const sizeWithGarments = await db.size.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { garments: true },
                },
            },
        });

        if (!sizeWithGarments) {
            return { success: false, error: "Talla no encontrada" };
        }

        // Verificar si tiene garments asociadas
        if (sizeWithGarments._count.garments > 0) {
            return {
                success: false,
                error: `No se puede eliminar la talla porque tiene ${sizeWithGarments._count.garments} prendas asociadas`,
            };
        }

        // Eliminar la talla
        const deletedSize = await db.size.delete({
            where: { id },
        });

        // Revalidar caché
        await revalidateSizeCache();

        return { success: true, data: deletedSize };
    } catch (error) {
        // Usar el helper para manejar errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al eliminar talla");
    }
}
