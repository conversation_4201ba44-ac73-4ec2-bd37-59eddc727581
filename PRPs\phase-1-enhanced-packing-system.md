# Phase 1: Enhanced Packing System - Skeleton Implementation

## Executive Summary

This PRP provides a comprehensive plan to enhance the existing packing system with sub-client management, improved order summaries, and professional PDF generation matching the Excel format requirements. The implementation follows established patterns from the remission system and leverages existing infrastructure.

## Context & Research Findings

### Existing Implementation Analysis
Based on thorough codebase analysis, the system already has:
- **Database Models**: Packing, PackingDetail, PackingSummary, PackingHistory
- **UI Components**: PackingNewWizard, EnhancedPackingDocument, QualitySelectors
- **Server Actions**: create-packing-enhanced, calculate-packaging, generate-folio
- **PDF Generation**: EnhancedPackingDocumentV2 with React PDF

### Key Gaps to Address
1. **Sub-client Management**: Current Customer model has parent/child relationships but packing UI doesn't fully utilize it
2. **Enhanced Summary Format**: Need specific format matching Excel with boxes/bags per size
3. **Signature Fields**: Need transport, verification, and receiving signatures
4. **PDF Layout**: Current PDF doesn't match the required Excel format

### Technology Stack Research
- **NextJS Server Actions**: https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations
- **Prisma Relations**: https://www.prisma.io/docs/orm/prisma-schema/data-model/relations
- **HeroUI Components**: https://www.heroui.com/docs/components/table
- **PDF Generation**: Using @react-pdf/renderer (already in project)

## Implementation Blueprint

### Phase 1: Database Schema Enhancements

```typescript
// prisma/schema.prisma enhancements needed
model Customer {
  // Existing fields...
  
  // Enhanced sub-client support
  displayName     String?    // For UI display (e.g., "Becktel - Reebok")
  packingSettings Json?      // Custom settings per customer
}

model Packing {
  // Existing fields...
  
  // Enhanced signature tracking
  transportSignature    String?
  transportSignedAt     DateTime?
  receiverSignature     String?
  receiverSignedAt      DateTime?
  receiverName          String?
  
  // Enhanced summary data
  packingSummaryBySize  Json?     // Detailed breakdown by size
}

model PackingDetail {
  // Existing fields...
  
  // Enhanced packaging info
  boxesCount      Int?
  bagsFirstCount  Int?
  bagsSecondCount Int?
  loosePiecesInfo Json?  // Detailed info about pieces not in boxes
}
```

### Phase 2: Sub-Client Management Enhancement

```typescript
// features/packings/components/CustomerSubClientSelector.tsx
export function CustomerSubClientSelector() {
  // IMPLEMENTATION TODO:
  // 1. Fetch customers with their sub-clients using include: { children: true }
  // 2. Display as hierarchical selector (e.g., "Becktel > Reebok")
  // 3. Allow selecting both main client and sub-client
  // 4. Store both IDs in packing record
  
  return (
    <div>
      {/* TODO: Implement HeroUI Autocomplete with grouped options */}
      {/* Main clients as groups, sub-clients as options */}
    </div>
  );
}
```

### Phase 3: Enhanced Packing Wizard

```typescript
// features/packings/components/wizard/EnhancedPackingWizardV3.tsx
export function EnhancedPackingWizardV3() {
  // IMPLEMENTATION TODO:
  // Step 1: Basic Info + Customer/SubCustomer selection
  // Step 2: Order Selection (existing)
  // Step 3: Quality Distribution with enhanced summary
  // Step 4: Packaging Summary (new step)
  // Step 5: Review & Generate
  
  const steps = [
    { 
      title: "Información Básica",
      component: BasicInfoStep // TODO: Include sub-client selector
    },
    {
      title: "Selección de Órdenes",
      component: OrderSelectionStep // Existing
    },
    {
      title: "Distribución por Calidad",
      component: QualityDistributionStep // Enhance with packaging info
    },
    {
      title: "Resumen de Empaque",
      component: PackagingSummaryStep // TODO: New component
    },
    {
      title: "Revisión y Generación",
      component: ReviewStep // TODO: Preview matching Excel format
    }
  ];
  
  return <WizardContainer steps={steps} />;
}
```

### Phase 4: Packaging Summary Component

```typescript
// features/packings/components/wizard/PackagingSummaryStep.tsx
interface PackagingSummaryData {
  orderId: string;
  piecesPerBox: number;
  piecesPerBag: number;
  sizeBreakdown: {
    size: string;
    boxes: number;
    loosePieces: number;
    bagsFirst: number;
    bagsSecond: number;
  }[];
  totalBoxes: number;
  totalBags: number;
}

export function PackagingSummaryStep() {
  // IMPLEMENTATION TODO:
  // 1. For each order, calculate optimal packaging
  // 2. Allow manual adjustment of pieces per box/bag
  // 3. Auto-calculate boxes needed and loose pieces
  // 4. Display in table format matching Excel
  // 5. Save to packingSummaryBySize JSON field
  
  return (
    <div>
      {/* TODO: Implement summary table per order */}
      {/* Use HeroUI Table with editable cells */}
    </div>
  );
}
```

### Phase 5: Enhanced PDF Document

```typescript
// features/packings/components/documents/ProfessionalPackingDocument.tsx
export function ProfessionalPackingDocument({ packing }: Props) {
  // IMPLEMENTATION TODO:
  // 1. Header with logo, folio, date (existing pattern)
  // 2. Sender/Receiver with sub-client display
  // 3. Main table with all order details
  // 4. Summary section per order (new format)
  // 5. Signature section at bottom
  
  return (
    <Document>
      <Page size="LETTER">
        {/* TODO: Implement sections matching Excel */}
        <Header />
        <SenderReceiverSection />
        <MainOrderTable />
        <OrderSummarySection />
        <SignatureSection />
      </Page>
    </Document>
  );
}
```

### Phase 6: Server Actions Enhancement

```typescript
// features/packings/actions/create-packing-enhanced-v3.ts
export async function createPackingEnhancedV3(data: PackingFormData) {
  // IMPLEMENTATION TODO:
  // 1. Validate customer and sub-customer relationship
  // 2. Create packing with enhanced fields
  // 3. Generate packing details with packaging info
  // 4. Create packing summaries with size breakdown
  // 5. Update inventory (existing pattern)
  // 6. Generate folio (existing)
  
  return await prisma.$transaction(async (tx) => {
    // TODO: Implement transaction logic
    // Follow pattern from create-packing-enhanced.ts
  });
}

// features/packings/actions/calculate-packaging-enhanced.ts
export async function calculatePackagingEnhanced(
  orderItems: OrderItem[],
  settings: PackagingSettings
) {
  // IMPLEMENTATION TODO:
  // 1. Group items by size
  // 2. Calculate optimal box/bag distribution
  // 3. Account for quality types (primera, segunda)
  // 4. Return detailed breakdown for UI
  
  return {
    bySize: [], // Size breakdown
    totals: {}  // Overall totals
  };
}
```

### Phase 7: Enhanced Search and Filters

```typescript
// features/packings/components/EnhancedPackingFilters.tsx
export function EnhancedPackingFilters() {
  // IMPLEMENTATION TODO:
  // 1. Add sub-client filter option
  // 2. Add date range filter
  // 3. Add status filters
  // 4. Add transport/receiver filters
  
  return (
    <div>
      {/* TODO: Implement using HeroUI components */}
      {/* Follow pattern from PackingFilters.tsx */}
    </div>
  );
}
```

## Key Integration Points

### 1. Sub-Client Data Flow
```typescript
// When creating packing:
const customerWithSubClients = await prisma.customer.findUnique({
  where: { id: customerId },
  include: { children: true }
});

// Display format: "Becktel > Reebok"
const displayName = subClientId 
  ? `${customer.name} > ${subClient.name}`
  : customer.name;
```

### 2. Packaging Calculation Logic
```typescript
// Reference implementation pattern
const calculateBoxes = (quantity: number, piecesPerBox: number) => {
  const fullBoxes = Math.floor(quantity / piecesPerBox);
  const loosePieces = quantity % piecesPerBox;
  return { fullBoxes, loosePieces };
};
```

### 3. PDF Generation Pattern
```typescript
// Follow existing pattern from RemissionTemplate.tsx
const styles = StyleSheet.create({
  page: { padding: 30 },
  header: { flexDirection: 'row', justifyContent: 'space-between' },
  // ... more styles
});
```

## Validation Requirements

### Client-Side Validation
- Use Zod schemas (existing pattern)
- Validate sub-client belongs to main client
- Ensure packaging quantities are positive
- Validate signature fields when present

### Server-Side Validation
- Verify inventory availability (existing)
- Validate customer relationships
- Ensure data consistency in transaction
- Validate packaging calculations

## Testing Strategy

### Unit Tests Required
1. **Sub-client validation logic**
2. **Packaging calculation algorithms**
3. **PDF generation with all sections**
4. **Server action transactions**

### Integration Tests
1. **Full wizard flow with sub-clients**
2. **PDF generation and download**
3. **Search with new filters**
4. **Data consistency checks**

## Migration Strategy

### Database Migration
```sql
-- Add new fields to existing tables
ALTER TABLE "Customer" ADD COLUMN "displayName" TEXT;
ALTER TABLE "Customer" ADD COLUMN "packingSettings" JSONB;

ALTER TABLE "Packing" ADD COLUMN "transportSignature" TEXT;
ALTER TABLE "Packing" ADD COLUMN "transportSignedAt" TIMESTAMP;
ALTER TABLE "Packing" ADD COLUMN "receiverSignature" TEXT;
ALTER TABLE "Packing" ADD COLUMN "receiverSignedAt" TIMESTAMP;
ALTER TABLE "Packing" ADD COLUMN "receiverName" TEXT;
ALTER TABLE "Packing" ADD COLUMN "packingSummaryBySize" JSONB;

ALTER TABLE "PackingDetail" ADD COLUMN "boxesCount" INTEGER;
ALTER TABLE "PackingDetail" ADD COLUMN "bagsFirstCount" INTEGER;
ALTER TABLE "PackingDetail" ADD COLUMN "bagsSecondCount" INTEGER;
ALTER TABLE "PackingDetail" ADD COLUMN "loosePiecesInfo" JSONB;
```

## Task Execution Order

1. **Database Schema Updates** (Day 1)
   - Update Prisma schema
   - Create and run migrations
   - Update types

2. **Sub-Client Management** (Day 2)
   - Create CustomerSubClientSelector component
   - Update server actions for sub-client support
   - Update existing forms

3. **Enhanced Wizard Implementation** (Day 3-4)
   - Create PackagingSummaryStep
   - Enhance existing steps
   - Integrate all steps

4. **PDF Generation Enhancement** (Day 5)
   - Create ProfessionalPackingDocument
   - Implement all sections
   - Add signature support

5. **Testing and Polish** (Day 6)
   - Write unit tests
   - Integration testing
   - UI polish

## Success Metrics

1. **Sub-client selection works seamlessly**
2. **Packaging calculations are accurate**
3. **PDF matches Excel format exactly**
4. **Performance remains optimal**
5. **All existing features continue working**

## Common Pitfalls to Avoid

1. **Don't break existing packings** - Ensure backward compatibility
2. **Validate parent-child relationships** - Sub-client must belong to parent
3. **Handle edge cases** - What if no sub-client selected?
4. **Performance** - Don't N+1 query when loading sub-clients
5. **PDF rendering** - Test with various data volumes

## Resources and References

- **Existing Patterns**:
  - `/features/remissions/` - Reference implementation
  - `/features/packings/components/EnhancedPackingDocumentV2.tsx` - Current PDF
  - `/lib/pdf/templates/RemissionTemplate.tsx` - PDF patterns

- **Documentation**:
  - NextJS Server Actions: https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations
  - Prisma Relations: https://www.prisma.io/docs/orm/prisma-schema/data-model/relations
  - HeroUI Table: https://www.heroui.com/docs/components/table
  - React PDF: https://react-pdf.org/

## Phase 1 Deliverables

1. **Enhanced database schema** with sub-client support
2. **Skeleton components** with detailed implementation TODOs
3. **Server action shells** with transaction structure
4. **Type definitions** for all new data structures
5. **Migration scripts** ready to run

## Quality Score: 8/10

High confidence in one-pass implementation due to:
- Extensive existing codebase patterns to follow
- Clear requirements from Excel example
- Comprehensive research completed
- Detailed implementation blueprint
- All edge cases identified

The skeleton implementation provides clear guidance while allowing flexibility for production enhancements in Phase 2.