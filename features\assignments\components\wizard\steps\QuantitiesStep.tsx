"use client";

import { useState, useCallback, useEffect } from "react";
import {
    Accordion,
    AccordionItem,
    Button,
    Card,
    CardBody,
    Chip,
    Spinner,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Badge,
} from "@heroui/react";
import {
    TrashIcon,
    ExclamationCircleIcon,
    CheckCircleIcon,
    ArrowUturnLeftIcon,
    ArrowUturnRightIcon,
    CalculatorIcon,
    SparklesIcon,
    CubeTransparentIcon,
    ClipboardDocumentListIcon,
    ChartPieIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import useSWR from "swr";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";
import { getOrderDetailsForAssignment } from "@/features/assignments/actions";
import { QuantityAssignment } from "@/features/assignments/schemas/schema";
import {
    ContextualTooltip,
    tooltipContexts,
} from "@/features/assignments/components/wizard/ui/ContextualTooltip";

import EnhancedSizesAssignment from "../ui/EnhancedSizesAssignment";

export default function QuantitiesStep() {
    const {
        selectedOrderIds,
        assignments,
        addAssignment,
        updateAssignment,
        removeAssignment,
        validationErrors,
        setValidationError,
        activeContractor,
        undo,
        redo,
        canUndo,
        canRedo,
    } = useWizard();

    const [expandedKeys, setExpandedKeys] = useState<Set<string>>(
        new Set(selectedOrderIds),
    );
    const [validItemsCount, setValidItemsCount] = useState<number>(0);

    // Keyboard shortcuts for undo/redo
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === "z" && !e.shiftKey && canUndo) {
                    e.preventDefault();
                    undo();
                } else if (
                    (e.key === "y" || (e.key === "z" && e.shiftKey)) &&
                    canRedo
                ) {
                    e.preventDefault();
                    redo();
                }
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [undo, redo, canUndo, canRedo]);

    // Load order details
    const orderDetailsResults = selectedOrderIds.map((orderId) => {
        return useSWR(["order-details", orderId], () =>
            getOrderDetailsForAssignment(orderId),
        );
    });

    const isLoading = orderDetailsResults.some((result) => result.isLoading);

    const clearError = useCallback(
        (garmentSizeId: string) => {
            setValidationError(garmentSizeId, null);
        },
        [setValidationError],
    );

    const handleQuantityChange = useCallback(
        (
            garmentSizeId: string,
            quantity: number,
            orderId: string,
            garmentId: string,
            modelCode: string,
            colorName: string,
            sizeCode: string,
            availableQuantity: number,
        ) => {
            if (quantity === 0) {
                removeAssignment(garmentSizeId);
                clearError(garmentSizeId);

                return;
            }

            const assignment: QuantityAssignment = {
                garmentSizeId,
                orderId,
                quantity,
                garmentId,
                modelCode,
                colorName,
                sizeCode,
                availableQuantity,
            };

            updateAssignment(garmentSizeId, assignment);
        },
        [updateAssignment, removeAssignment, clearError],
    );

    const handleValidationChange = useCallback(
        (garmentSizeId: string, isValid: boolean) => {
            if (!isValid) {
                if (!validationErrors[garmentSizeId]) {
                    setValidationError(garmentSizeId, "Cantidad inválida");
                }
            } else if (validationErrors[garmentSizeId]) {
                setValidationError(garmentSizeId, null);
            }
        },
        [setValidationError, validationErrors],
    );

    useEffect(() => {
        const validCount = assignments.filter(
            (item) => !validationErrors[item.garmentSizeId],
        ).length;

        setValidItemsCount(validCount);
    }, [assignments, validationErrors]);

    // Calculate statistics
    const stats = {
        totalAssigned: assignments.reduce((sum, a) => sum + a.quantity, 0),
        totalOrders: selectedOrderIds.length,
        uniqueModels: new Set(assignments.map((a) => a.modelCode)).size,
        validAssignments: validItemsCount,
    };

    const renderSizesTable = (orderResult: any) => {
        if (!orderResult.data?.success) {
            return (
                <Card className="mt-2 border-0 bg-red-50 dark:bg-red-950/30">
                    <CardBody className="text-center p-6">
                        <ExclamationCircleIcon className="w-8 h-8 text-red-500 mx-auto mb-2" />
                        <p className="text-red-600 dark:text-red-400 font-medium">
                            Error al cargar los detalles de la orden
                        </p>
                    </CardBody>
                </Card>
            );
        }

        const { order, garments } = orderResult.data.data;

        if (garments.length === 0) {
            return (
                <Card className="mt-2 border-0 bg-gray-50 dark:bg-gray-800/50">
                    <CardBody className="text-center p-6">
                        <CubeTransparentIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-600 dark:text-gray-400">
                            No hay prendas disponibles para esta orden
                        </p>
                    </CardBody>
                </Card>
            );
        }

        return (
            <div className="mt-2">
                <EnhancedSizesAssignment
                    assignments={assignments}
                    contractorId={activeContractor?.id}
                    garments={garments}
                    orderCode={order.cutOrder}
                    orderId={order.id}
                    validationErrors={validationErrors}
                    onQuantityChange={handleQuantityChange}
                    onValidationChange={handleValidationChange}
                />
            </div>
        );
    };

    return (
        <motion.div
            animate={{ opacity: 1 }}
            className="space-y-6 max-w-6xl mx-auto"
            exit={{ opacity: 0 }}
            initial={{ opacity: 0 }}
        >
            {/* Header Card */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
            >
                <Card className="bg-gradient-to-br from-white via-amber-50/50 to-orange-50/30 dark:from-gray-900 dark:via-amber-950/50 dark:to-orange-950/30 shadow-xl border-0 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-orange-600/5" />
                    <CardBody className="relative p-8">
                        <div className="flex items-center gap-4 mb-6">
                            <motion.div
                                className="p-3 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl shadow-lg"
                                transition={{ type: "spring", stiffness: 300 }}
                                whileHover={{ scale: 1.05, rotate: 5 }}
                            >
                                <CalculatorIcon className="w-7 h-7 text-white" />
                            </motion.div>
                            <div className="flex-1">
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                                    Asignar Cantidades
                                </h2>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-1.5">
                                    <SparklesIcon className="w-4 h-4 text-amber-500" />
                                    Especifica las cantidades para cada talla de
                                    las órdenes seleccionadas
                                </p>
                            </div>
                            <ContextualTooltip
                                content={tooltipContexts.quantities.text}
                                variant={tooltipContexts.quantities.variant}
                            />
                        </div>

                        {/* Statistics Grid */}
                        <div className="grid grid-cols-4 gap-4 mb-6">
                            <motion.div
                                className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                transition={{ type: "spring", stiffness: 400 }}
                                whileHover={{ scale: 1.02 }}
                            >
                                <ClipboardDocumentListIcon className="w-6 h-6 text-amber-600 mx-auto mb-2" />
                                <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                    {stats.totalOrders}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                    Órdenes
                                </p>
                            </motion.div>
                            <motion.div
                                className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                transition={{ type: "spring", stiffness: 400 }}
                                whileHover={{ scale: 1.02 }}
                            >
                                <ChartPieIcon className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                                <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                    {stats.totalAssigned.toLocaleString()}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                    Prendas asignadas
                                </p>
                            </motion.div>
                            <motion.div
                                className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                transition={{ type: "spring", stiffness: 400 }}
                                whileHover={{ scale: 1.02 }}
                            >
                                <CubeTransparentIcon className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                                <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                    {stats.uniqueModels}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                    Modelos únicos
                                </p>
                            </motion.div>
                            <motion.div
                                className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                transition={{ type: "spring", stiffness: 400 }}
                                whileHover={{ scale: 1.02 }}
                            >
                                <CheckCircleIcon className="w-6 h-6 text-green-600 mx-auto mb-2" />
                                <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                    {stats.validAssignments}
                                </p>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                    Válidas
                                </p>
                            </motion.div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-3">
                            <AnimatePresence mode="wait">
                                {canUndo && (
                                    <motion.div
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.8 }}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <Button
                                            className="font-medium"
                                            color="default"
                                            size="sm"
                                            startContent={
                                                <ArrowUturnLeftIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                            onPress={undo}
                                        >
                                            Deshacer
                                        </Button>
                                    </motion.div>
                                )}
                                {canRedo && (
                                    <motion.div
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.8 }}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        <Button
                                            className="font-medium"
                                            color="default"
                                            size="sm"
                                            startContent={
                                                <ArrowUturnRightIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                            onPress={redo}
                                        >
                                            Rehacer
                                        </Button>
                                    </motion.div>
                                )}
                            </AnimatePresence>

                            <div className="flex-1 text-right">
                                {validItemsCount === 0 ? (
                                    <Chip
                                        color="warning"
                                        size="sm"
                                        startContent={
                                            <InformationCircleIcon className="w-4 h-4" />
                                        }
                                        variant="flat"
                                    >
                                        Asigna al menos una cantidad para
                                        continuar
                                    </Chip>
                                ) : (
                                    <Chip
                                        color="success"
                                        size="sm"
                                        startContent={
                                            <CheckCircleIcon className="w-4 h-4" />
                                        }
                                        variant="flat"
                                    >
                                        {validItemsCount} asignaciones válidas
                                    </Chip>
                                )}
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Orders Accordion */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.1, ease: "easeOut" }}
            >
                {isLoading ? (
                    <Card className="shadow-xl border-0">
                        <CardBody className="flex justify-center items-center py-20">
                            <div className="text-center">
                                <div className="relative mb-4">
                                    <div className="w-20 h-20 border-4 border-amber-200 rounded-full animate-pulse" />
                                    <div className="absolute inset-0 w-20 h-20 border-4 border-amber-600 rounded-full animate-spin border-t-transparent" />
                                </div>
                                <Spinner color="warning" size="lg" />
                                <p className="mt-4 text-gray-600 dark:text-gray-400">
                                    Cargando detalles de las órdenes...
                                </p>
                            </div>
                        </CardBody>
                    </Card>
                ) : (
                    <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm overflow-hidden">
                        <CardBody className="p-0">
                            <Accordion
                                className="w-full"
                                defaultExpandedKeys={Array.from(expandedKeys)}
                                itemClasses={{
                                    base: "border-b border-gray-200 dark:border-gray-700",
                                    title: "font-medium text-gray-800 dark:text-gray-100",
                                    subtitle:
                                        "text-gray-500 dark:text-gray-400",
                                    content: "px-4 pb-4",
                                }}
                                onExpandedChange={(keys) => {
                                    const stringKeys = Array.from(keys).map(
                                        (k) => String(k),
                                    );

                                    setExpandedKeys(new Set(stringKeys));
                                }}
                            >
                                {selectedOrderIds.map((orderId, index) => {
                                    const orderResult =
                                        orderDetailsResults[index];
                                    const orderData = (orderResult as any).data
                                        ?.data?.order;
                                    const orderAssignments = assignments.filter(
                                        (a) => a.orderId === orderId,
                                    );
                                    const orderTotal = orderAssignments.reduce(
                                        (sum, a) => sum + a.quantity,
                                        0,
                                    );

                                    return (
                                        <AccordionItem
                                            key={orderId}
                                            startContent={
                                                <div className="flex items-center gap-3">
                                                    <div className="p-2 bg-gradient-to-br from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30 rounded-lg">
                                                        <ClipboardDocumentListIcon className="w-5 h-5 text-amber-700 dark:text-amber-300" />
                                                    </div>
                                                </div>
                                            }
                                            subtitle={
                                                <div className="flex items-center gap-4 mt-1">
                                                    <span className="text-xs">
                                                        {orderData?.customer
                                                            ?.name ||
                                                            "Cliente sin nombre"}
                                                    </span>
                                                    {orderTotal > 0 && (
                                                        <Badge
                                                            color="success"
                                                            content={orderTotal.toString()}
                                                            size="sm"
                                                        >
                                                            <span className="text-xs text-green-600 dark:text-green-400">
                                                                Asignadas
                                                            </span>
                                                        </Badge>
                                                    )}
                                                </div>
                                            }
                                            title={
                                                <div className="flex items-center">
                                                    <span className="text-base">
                                                        Orden:{" "}
                                                        {orderData?.cutOrder ||
                                                            `(${orderId.substring(0, 8)})`}
                                                    </span>
                                                    <Chip
                                                        className="ml-3"
                                                        color="default"
                                                        size="sm"
                                                        variant="flat"
                                                    >
                                                        {(
                                                            orderResult as any
                                                        ).data?.data?.garments?.reduce(
                                                            (
                                                                count: number,
                                                                garment: any,
                                                            ) =>
                                                                count +
                                                                garment.sizes
                                                                    .length,
                                                            0,
                                                        ) || 0}{" "}
                                                        tallas
                                                    </Chip>
                                                </div>
                                            }
                                        >
                                            {orderResult.isLoading ? (
                                                <div className="flex justify-center p-8">
                                                    <Spinner color="warning" />
                                                </div>
                                            ) : (
                                                renderSizesTable(orderResult)
                                            )}
                                        </AccordionItem>
                                    );
                                })}
                            </Accordion>
                        </CardBody>
                    </Card>
                )}
            </motion.div>

            {/* Assignments Summary Table */}
            {assignments.length > 0 && (
                <motion.div
                    animate={{ y: 0, opacity: 1 }}
                    className="mt-8"
                    initial={{ y: 20, opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.2, ease: "easeOut" }}
                >
                    <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-3">
                            <h3 className="text-lg font-bold text-gray-800 dark:text-gray-100">
                                Resumen de asignaciones
                            </h3>
                            <Badge
                                color="primary"
                                content={assignments.length.toString()}
                            >
                                <ChartPieIcon className="w-5 h-5 text-gray-600" />
                            </Badge>
                        </div>
                        <ContextualTooltip
                            content="Esta tabla muestra todas las cantidades asignadas. Puedes eliminar items o modificar cantidades arriba."
                            variant="info"
                        />
                    </div>

                    <Card className="shadow-xl border-0">
                        <Table
                            isStriped
                            removeWrapper
                            aria-label="Tabla de asignaciones"
                            classNames={{
                                th: "bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-900 text-gray-800 dark:text-gray-100 font-semibold",
                                td: "py-3",
                            }}
                        >
                            <TableHeader>
                                <TableColumn>MODELO</TableColumn>
                                <TableColumn>COLOR</TableColumn>
                                <TableColumn>TALLA</TableColumn>
                                <TableColumn>CANTIDAD</TableColumn>
                                <TableColumn>ORDEN</TableColumn>
                                <TableColumn>ESTADO</TableColumn>
                                <TableColumn>ACCIONES</TableColumn>
                            </TableHeader>
                            <TableBody>
                                {assignments.map((assignment) => {
                                    const error =
                                        validationErrors[
                                            assignment.garmentSizeId
                                        ];

                                    return (
                                        <TableRow
                                            key={assignment.garmentSizeId}
                                        >
                                            <TableCell>
                                                <span className="font-medium">
                                                    {assignment.modelCode}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    color="secondary"
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    {assignment.colorName}
                                                </Chip>
                                            </TableCell>
                                            <TableCell>
                                                <Badge
                                                    color="primary"
                                                    content={
                                                        assignment.sizeCode
                                                    }
                                                    variant="flat"
                                                >
                                                    <div className="w-1" />
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-bold text-lg">
                                                    {assignment.quantity}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                                    {(
                                                        orderDetailsResults.find(
                                                            (r) =>
                                                                (r as any).data
                                                                    ?.data
                                                                    ?.order
                                                                    ?.id ===
                                                                assignment.orderId,
                                                        ) as any
                                                    )?.data?.data?.order
                                                        ?.cutOrder ||
                                                        `${assignment.orderId.substring(0, 6)}...`}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <AnimatePresence mode="wait">
                                                    {error ? (
                                                        <motion.div
                                                            animate={{
                                                                opacity: 1,
                                                                x: 0,
                                                            }}
                                                            className="flex items-center gap-2"
                                                            exit={{
                                                                opacity: 0,
                                                                x: -10,
                                                            }}
                                                            initial={{
                                                                opacity: 0,
                                                                x: -10,
                                                            }}
                                                        >
                                                            <ExclamationCircleIcon className="w-5 h-5 text-red-500" />
                                                            <span className="text-sm text-red-600 dark:text-red-400">
                                                                {error}
                                                            </span>
                                                        </motion.div>
                                                    ) : (
                                                        <motion.div
                                                            animate={{
                                                                opacity: 1,
                                                                scale: 1,
                                                            }}
                                                            className="flex items-center gap-2"
                                                            exit={{
                                                                opacity: 0,
                                                                scale: 0.8,
                                                            }}
                                                            initial={{
                                                                opacity: 0,
                                                                scale: 0.8,
                                                            }}
                                                        >
                                                            <CheckCircleIcon className="w-5 h-5 text-green-500" />
                                                            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                                                                Válido
                                                            </span>
                                                        </motion.div>
                                                    )}
                                                </AnimatePresence>
                                            </TableCell>
                                            <TableCell>
                                                <motion.div
                                                    whileHover={{ scale: 1.1 }}
                                                    whileTap={{ scale: 0.9 }}
                                                >
                                                    <Button
                                                        isIconOnly
                                                        className="hover:bg-red-100 dark:hover:bg-red-900/30"
                                                        color="danger"
                                                        size="sm"
                                                        variant="light"
                                                        onClick={() =>
                                                            removeAssignment(
                                                                assignment.garmentSizeId,
                                                            )
                                                        }
                                                    >
                                                        <TrashIcon className="w-4 h-4" />
                                                    </Button>
                                                </motion.div>
                                            </TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </Card>
                </motion.div>
            )}
        </motion.div>
    );
}
