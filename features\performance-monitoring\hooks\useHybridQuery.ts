/**
 * Hybrid Hook Wrapper
 * Allows gradual migration from SWR to React Query using feature flags
 */

import useSWR, { SWRConfiguration } from "swr";

import { useOptimizedQuery } from "../hooks/useOptimizedQuery";
import { featureFlags } from "../feature-flags";

interface HybridOptions extends SWRConfiguration {
    // Feature flag override for specific hook
    enableReactQuery?: boolean;
    // React Query specific options
    staleTime?: number;
    gcTime?: number;
}

/**
 * Hybrid hook that can use either SWR or React Query
 * based on feature flags
 */
export function useHybridQuery<T = any>(
    key: string | string[],
    fetcher: () => Promise<T>,
    options: HybridOptions = {},
) {
    const shouldUseReactQuery =
        options.enableReactQuery ?? featureFlags.ENABLE_REACT_QUERY;

    if (shouldUseReactQuery) {
        // Use React Query
        const queryKey = Array.isArray(key) ? key : [key];
        const query = useOptimizedQuery(queryKey, fetcher, {
            ...(options.staleTime !== undefined && {
                staleTime: options.staleTime,
            }),
            ...(options.gcTime !== undefined && { gcTime: options.gcTime }),
            ...(options.errorRetryCount !== undefined && {
                retry: options.errorRetryCount,
            }),
            ...(options.revalidateOnFocus !== undefined && {
                refetchOnWindowFocus: options.revalidateOnFocus,
            }),
        } as any);

        // Transform to SWR-like API for compatibility
        return {
            data: query.data,
            error: query.error,
            isLoading: query.isLoading,
            isValidating: query.isFetching,
            mutate: query.refetch,
        };
    } else {
        // Use SWR
        return useSWR(key, fetcher, options);
    }
}
