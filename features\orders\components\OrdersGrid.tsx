"use client";

import React from "react";
import {
    CalendarIcon,
    CubeIcon,
    EyeIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";

import {
    <PERSON>,
    CardBody,
    CardHeader,
    CardFooter,
    Chip,
    Button,
    Progress,
    Tooltip,
} from "@/shared/components/ui/hero-ui-client";
import {
    adjustTimezoneOffset,
    formatDate,
    getDaysDifference,
} from "@/shared/utils/dateUtils";
import { Order } from "@/features/orders/types/orders";

import { calculateRiskScore } from "../utils/riskCalculator";
import { getStatusConfig } from "../utils/statusIcons";

import RiskIndicator from "./RiskIndicator";

// Helper functions para calcular asignaciones
const calculateTotalAssigned = (order: Order): number => {
    if (!order.assignments || order.assignments.length === 0) return 0;

    return order.assignments.reduce(
        (sum, assignment) => sum + (assignment.quantity || 0),
        0,
    );
};

const calculateTotalPieces = (order: Order): number => {
    // Opción 1: Si la orden tiene el total precalculado
    if ((order as any).totalQuantity) {
        return (order as any).totalQuantity;
    }

    // Opción 2: Sumar todas las cantidades de los garments
    if (order.garments && order.garments.length > 0) {
        const total = order.garments.reduce((sum, garment) => {
            // Sumar totalQuantity de cada size
            const garmentTotal =
                garment.sizes?.reduce((sizeSum, size) => {
                    return sizeSum + (size.totalQuantity || 0);
                }, 0) || 0;

            return sum + garmentTotal;
        }, 0);

        if (total > 0) return total;
    }

    // Opción 3: Si no hay garments o sizes, intentar con assignments como total esperado
    if (order.assignments && order.assignments.length > 0) {
        return order.assignments.reduce(
            (sum, assignment) => sum + (assignment.quantity || 0),
            0,
        );
    }

    return 0;
};

const calculateAssignmentPercentage = (order: Order): number => {
    const total = calculateTotalPieces(order);

    if (total === 0) return 0;
    const assigned = calculateTotalAssigned(order);

    return Math.round((assigned / total) * 100);
};

const getProgressColor = (
    percentage: number,
): "success" | "warning" | "danger" => {
    if (percentage === 100) return "success";
    if (percentage >= 50) return "warning";

    return "danger";
};

interface OrdersGridProps {
    orders: Order[];
    onViewDetails: (order: Order) => void;
    isLoading?: boolean;
}

export default function OrdersGrid({
    orders,
    onViewDetails,
    isLoading,
}: OrdersGridProps) {
    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {[...Array(8)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                        <CardBody className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                            <div className="h-3 bg-gray-200 rounded w-1/2" />
                        </CardBody>
                    </Card>
                ))}
            </div>
        );
    }

    if (orders.length === 0) {
        return (
            <div className="text-center py-12">
                <CubeIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No hay órdenes para mostrar</p>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {orders.map((order) => {
                const deliveryDateRaw =
                    order.estimatedDeliveryDate || order.deliveryDate;
                const deliveryDate = deliveryDateRaw
                    ? adjustTimezoneOffset(deliveryDateRaw)
                    : null;
                const daysLeft = deliveryDate
                    ? getDaysDifference(deliveryDate)
                    : null;
                const isOverdue =
                    daysLeft !== null &&
                    daysLeft < 0 &&
                    order.status !== "delivered";

                const statusConfig = getStatusConfig(order.status);

                return (
                    <Card
                        key={order.id}
                        className="hover:shadow-lg transition-shadow"
                    >
                        <CardHeader className="pb-2">
                            <div className="flex justify-between items-start w-full">
                                <div>
                                    <h3 className="text-lg font-semibold">
                                        {order.cutOrder ||
                                            `ORD-${order.id.substring(0, 8)}`}
                                    </h3>
                                    <p className="text-sm text-gray-500">
                                        {order.customer?.name || "Sin cliente"}
                                    </p>
                                </div>
                                <Chip
                                    color={statusConfig.color}
                                    size="sm"
                                    variant="flat"
                                >
                                    {statusConfig.label}
                                </Chip>
                            </div>
                        </CardHeader>

                        <CardBody className="py-2">
                            <div className="space-y-3">
                                {/* Modelo y Color */}
                                <div className="space-y-1">
                                    <div className="flex items-center text-sm">
                                        <span className="font-medium text-gray-700 w-16">
                                            Modelo:
                                        </span>
                                        <span className="text-gray-900">
                                            {(() => {
                                                if (
                                                    !order.garments ||
                                                    order.garments.length === 0
                                                ) {
                                                    console.log(
                                                        "OrdersGrid - Order sin garments:",
                                                        order.id,
                                                    );

                                                    return "Sin código";
                                                }
                                                const modelCode =
                                                    order.garments[0]?.model
                                                        ?.code;

                                                if (!modelCode) {
                                                    console.log(
                                                        "OrdersGrid - Garment sin código de modelo:",
                                                        order.id,
                                                        order.garments[0],
                                                    );
                                                }

                                                return (
                                                    modelCode || "Sin código"
                                                );
                                            })()}
                                        </span>
                                    </div>
                                    <div className="flex items-center text-sm">
                                        <span className="font-medium text-gray-700 w-16">
                                            Color:
                                        </span>
                                        <span className="text-gray-900">
                                            {order.garments?.[0]?.color?.name ||
                                                "-"}
                                        </span>
                                    </div>
                                </div>

                                {/* Partidas */}
                                <div className="space-y-1">
                                    <span className="text-sm font-medium text-gray-700">
                                        Partidas:
                                    </span>
                                    <div className="text-sm text-gray-900">
                                        {order.parts &&
                                        order.parts.length > 0 ? (
                                            <div className="flex flex-wrap gap-1">
                                                {order.parts.map(
                                                    (part, index) => (
                                                        <span
                                                            key={part.id}
                                                            className="bg-gray-100 px-2 py-0.5 rounded text-xs font-mono"
                                                        >
                                                            {part.code}
                                                        </span>
                                                    ),
                                                )}
                                            </div>
                                        ) : (
                                            <span className="text-gray-400 italic">
                                                Sin partidas
                                            </span>
                                        )}
                                    </div>
                                </div>

                                {/* Contratistas */}
                                <div className="flex items-center gap-2 text-sm">
                                    <UserGroupIcon className="w-4 h-4 text-gray-400" />
                                    <div className="flex-1">
                                        {!order.assignments ||
                                        order.assignments.length === 0 ? (
                                            <span className="text-gray-400">
                                                Sin asignar
                                            </span>
                                        ) : (
                                            <div className="flex items-center gap-1">
                                                {order.assignments
                                                    .slice(0, 3)
                                                    .map((assignment, idx) => (
                                                        <Tooltip
                                                            key={idx}
                                                            content={
                                                                <div className="space-y-1">
                                                                    <div className="font-medium">
                                                                        {assignment
                                                                            .contractor
                                                                            ?.name ||
                                                                            "Sin nombre"}
                                                                    </div>
                                                                    <div className="text-sm">
                                                                        {assignment.quantity ||
                                                                            0}{" "}
                                                                        piezas
                                                                    </div>
                                                                </div>
                                                            }
                                                        >
                                                            <div
                                                                className="w-6 h-6 rounded-full bg-primary-100 text-primary-600 
                                        flex items-center justify-center text-xs font-medium"
                                                            >
                                                                {(assignment
                                                                    .contractor
                                                                    ?.name ||
                                                                    "?")[0].toUpperCase()}
                                                            </div>
                                                        </Tooltip>
                                                    ))}
                                                {order.assignments.length >
                                                    3 && (
                                                    <Tooltip
                                                        content={
                                                            <div className="space-y-1">
                                                                <p className="font-medium mb-1">
                                                                    {order
                                                                        .assignments
                                                                        .length -
                                                                        3}{" "}
                                                                    contratistas
                                                                    más:
                                                                </p>
                                                                {order.assignments
                                                                    .slice(3)
                                                                    .map(
                                                                        (
                                                                            assignment,
                                                                            idx,
                                                                        ) => (
                                                                            <div
                                                                                key={
                                                                                    idx
                                                                                }
                                                                                className="flex justify-between gap-3 text-sm"
                                                                            >
                                                                                <span>
                                                                                    {assignment
                                                                                        .contractor
                                                                                        ?.name ||
                                                                                        "Sin nombre"}
                                                                                </span>
                                                                                <span className="font-mono">
                                                                                    {assignment.quantity ||
                                                                                        0}{" "}
                                                                                    pzs
                                                                                </span>
                                                                            </div>
                                                                        ),
                                                                    )}
                                                            </div>
                                                        }
                                                    >
                                                        <div
                                                            className="w-6 h-6 rounded-full bg-gray-100 text-gray-600 
                                        flex items-center justify-center text-xs font-medium"
                                                        >
                                                            +
                                                            {order.assignments
                                                                .length - 3}
                                                        </div>
                                                    </Tooltip>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Indicador de asignación */}
                                <div className="mt-2 pt-2 border-t border-gray-200">
                                    {(() => {
                                        const totalPieces =
                                            calculateTotalPieces(order);
                                        const totalAssigned =
                                            calculateTotalAssigned(order);
                                        const percentage =
                                            calculateAssignmentPercentage(
                                                order,
                                            );
                                        const color =
                                            getProgressColor(percentage);

                                        return (
                                            <div className="space-y-1">
                                                <div className="flex justify-between text-xs">
                                                    <span className="text-gray-600">
                                                        Asignación:
                                                    </span>
                                                    <span
                                                        className={`font-medium ${
                                                            color === "success"
                                                                ? "text-success"
                                                                : color ===
                                                                    "warning"
                                                                  ? "text-warning"
                                                                  : "text-danger"
                                                        }`}
                                                    >
                                                        {percentage}%
                                                    </span>
                                                </div>
                                                <Progress
                                                    className="w-full"
                                                    color={color}
                                                    size="sm"
                                                    value={percentage}
                                                />
                                                <div className="text-xs text-gray-500 text-center">
                                                    {totalAssigned}/
                                                    {totalPieces} piezas
                                                    asignadas
                                                </div>
                                            </div>
                                        );
                                    })()}
                                </div>

                                {/* Total de prendas */}
                                <div className="flex items-center text-sm mt-3">
                                    <CubeIcon className="w-4 h-4 mr-2 text-gray-500" />
                                    <span className="font-medium">
                                        {order._count?.garments ||
                                            order.garments?.length ||
                                            0}{" "}
                                        prendas
                                    </span>
                                </div>

                                {/* Fecha de entrega */}
                                {deliveryDate && (
                                    <div className="space-y-1">
                                        <div
                                            className={`flex items-center text-sm ${isOverdue ? "text-danger" : "text-gray-600"}`}
                                        >
                                            <CalendarIcon className="w-4 h-4 mr-2" />
                                            <span>
                                                {formatDate(
                                                    deliveryDate,
                                                    "dd MMM yyyy",
                                                )}
                                            </span>
                                        </div>
                                        {daysLeft !== null &&
                                            order.status !== "delivered" &&
                                            order.status !== "cancelled" && (
                                                <div
                                                    className={`text-xs font-medium pl-6 ${isOverdue ? "text-danger" : daysLeft <= 3 ? "text-warning" : "text-gray-500"}`}
                                                >
                                                    {isOverdue
                                                        ? `⚠️ ${Math.abs(daysLeft)} días de retraso`
                                                        : daysLeft === 0
                                                          ? "⏰ Vence hoy"
                                                          : daysLeft === 1
                                                            ? "📅 Vence mañana"
                                                            : `📅 En ${daysLeft} días`}
                                                </div>
                                            )}
                                    </div>
                                )}

                                {/* Progreso */}
                                <div className="space-y-1">
                                    <div className="flex justify-between text-xs text-gray-600">
                                        <span>Progreso</span>
                                        <span className="font-medium">
                                            {order.status === "completed" ||
                                            order.status === "delivered"
                                                ? "100"
                                                : order.status ===
                                                    "in_production"
                                                  ? "75"
                                                  : order.status ===
                                                      "in_progress"
                                                    ? "50"
                                                    : order.status === "pending"
                                                      ? "25"
                                                      : "0"}
                                            %
                                        </span>
                                    </div>
                                    <Progress
                                        color={
                                            order.status === "completed" ||
                                            order.status === "delivered"
                                                ? "success"
                                                : order.status ===
                                                    "in_production"
                                                  ? "warning"
                                                  : order.status ===
                                                      "in_progress"
                                                    ? "primary"
                                                    : "default"
                                        }
                                        size="sm"
                                        value={
                                            order.status === "completed" ||
                                            order.status === "delivered"
                                                ? 100
                                                : order.status ===
                                                    "in_production"
                                                  ? 75
                                                  : order.status ===
                                                      "in_progress"
                                                    ? 50
                                                    : order.status === "pending"
                                                      ? 25
                                                      : 0
                                        }
                                    />
                                </div>

                                {/* Score de riesgo */}
                                <div className="pt-2 border-t">
                                    <RiskIndicator
                                        assessment={calculateRiskScore(order)}
                                        showDetails={false}
                                        size="sm"
                                    />
                                </div>
                            </div>
                        </CardBody>

                        <CardFooter className="pt-2">
                            <Button
                                className="w-full"
                                color="primary"
                                size="sm"
                                startContent={<EyeIcon className="w-4 h-4" />}
                                variant="light"
                                onPress={() => onViewDetails(order)}
                            >
                                Ver Detalles
                            </Button>
                        </CardFooter>
                    </Card>
                );
            })}
        </div>
    );
}
