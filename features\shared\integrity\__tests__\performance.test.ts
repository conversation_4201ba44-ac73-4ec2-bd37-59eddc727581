import { IntegrityManager } from "../IntegrityManager";

import {
    setupIntegrityManager,
    PerformanceTimer,
    generateBulkAssignments,
    createMockOperationLog,
    mockTransactionBehavior,
} from "./test-utils";

describe("IntegrityManager - Performance Tests", () => {
    let mockPrisma: any;
    let integrityManager: IntegrityManager;

    beforeEach(() => {
        const setup = setupIntegrityManager();

        mockPrisma = setup.prisma;
        integrityManager = setup.integrityManager;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Transaction Performance", () => {
        it("should complete single transaction within 5 seconds for 10 items", async () => {
            // Arrange
            const assignments = generateBulkAssignments(10);
            const timer = new PerformanceTimer();

            mockPrisma.operationLog.create.mockResolvedValue(
                createMockOperationLog(),
            );
            mockPrisma.operationLog.update.mockResolvedValue({});
            mockPrisma.assignment.createMany.mockResolvedValue({ count: 10 });
            mockPrisma.garmentSize.update.mockResolvedValue({});
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            await integrityManager.executeWithIntegrity(
                {
                    type: "ASSIGNMENT_BATCH",
                    userId: "user-123",
                    metadata: { assignmentCount: 10 },
                },
                async () => {
                    // Simulate assignment creation
                    await mockPrisma.assignment.createMany({
                        data: assignments,
                    });
                    // Simulate quantity updates
                    for (let i = 0; i < 10; i++) {
                        await mockPrisma.garmentSize.update({
                            where: { id: `size-${i}` },
                            data: { availableQuantity: { decrement: 5 } },
                        });
                    }
                },
                jest.fn(),
            );

            const duration = timer.stop();

            // Assert
            expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
            expect(mockPrisma.assignment.createMany).toHaveBeenCalledTimes(1);
            expect(mockPrisma.garmentSize.update).toHaveBeenCalledTimes(10);
        });

        it("should use split transaction for more than 10 items", async () => {
            // Arrange
            const assignments = generateBulkAssignments(20);
            const timer = new PerformanceTimer();

            mockPrisma.operationLog.create.mockResolvedValue(
                createMockOperationLog(),
            );
            mockPrisma.operationLog.update.mockResolvedValue({});
            mockTransactionBehavior(mockPrisma, "success");

            let transactionCount = 0;

            mockPrisma.$transaction.mockImplementation(async (fn: any) => {
                transactionCount++;
                if (typeof fn === "function") {
                    return fn(mockPrisma);
                }

                return Promise.resolve();
            });

            // Act - Simulate split transaction approach
            await integrityManager.executeWithIntegrity(
                {
                    type: "ASSIGNMENT_BATCH",
                    userId: "user-123",
                    metadata: {
                        assignmentCount: 20,
                        strategy: "split",
                    },
                },
                async () => {
                    // Phase 1: Create assignments
                    await mockPrisma.$transaction(async (tx: any) => {
                        await tx.assignment.createMany({ data: assignments });
                    });

                    // Phase 2: Create remission (simulated)
                    await mockPrisma.$transaction(async (tx: any) => {
                        await tx.remission.create({ data: {} });
                    });
                },
                jest.fn(),
            );

            const duration = timer.stop();

            // Assert
            expect(transactionCount).toBeGreaterThanOrEqual(2); // Split into multiple transactions
            expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
        });
    });

    describe("Batch Operations Performance", () => {
        it("should use createMany for batch operations", async () => {
            // Arrange
            const assignments = generateBulkAssignments(50, "order-123"); // Same order

            mockPrisma.assignment.createMany.mockResolvedValue({ count: 50 });
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            await mockPrisma.$transaction(async (tx: any) => {
                await tx.assignment.createMany({ data: assignments });
            });

            // Assert
            expect(mockPrisma.assignment.createMany).toHaveBeenCalledTimes(1);
            expect(mockPrisma.assignment.createMany).toHaveBeenCalledWith({
                data: assignments,
            });
        });

        it("should handle mixed order assignments efficiently", async () => {
            // Arrange
            const assignments = generateBulkAssignments(30); // Different orders
            const groupedByOrder = assignments.reduce(
                (acc, assignment) => {
                    if (!acc[assignment.orderId]) {
                        acc[assignment.orderId] = [];
                    }
                    acc[assignment.orderId].push(assignment);

                    return acc;
                },
                {} as Record<string, any[]>,
            );

            mockPrisma.assignment.createMany.mockResolvedValue({ count: 10 });
            mockTransactionBehavior(mockPrisma, "success");

            // Act - Simulate grouped creation
            const timer = new PerformanceTimer();

            await mockPrisma.$transaction(async (tx: any) => {
                for (const [orderId, orderAssignments] of Object.entries(
                    groupedByOrder,
                )) {
                    await tx.assignment.createMany({ data: orderAssignments });
                }
            });

            const duration = timer.stop();

            // Assert
            expect(mockPrisma.assignment.createMany).toHaveBeenCalledTimes(
                Object.keys(groupedByOrder).length,
            );
            expect(duration).toBeLessThan(3000); // Grouped operations should be fast
        });
    });

    describe("Compensation Performance", () => {
        it("should compensate large batches efficiently", async () => {
            // Arrange
            const assignmentIds = Array.from(
                { length: 100 },
                (_, i) => `assign-${i}`,
            );
            const operationLog = createMockOperationLog({
                metadata: {
                    assignmentIds,
                    garmentSizeUpdates: Array.from({ length: 20 }, (_, i) => ({
                        id: `size-${i}`,
                        quantityDelta: 10,
                    })),
                },
            });

            mockPrisma.assignment.updateMany.mockResolvedValue({ count: 100 });
            mockPrisma.garmentSize.update.mockResolvedValue({});
            mockPrisma.operationLog.update.mockResolvedValue({});
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            const timer = new PerformanceTimer();

            await integrityManager.compensationStrategies.compensateAssignmentBatch(
                operationLog,
                mockPrisma,
            );

            const duration = timer.stop();

            // Assert
            expect(mockPrisma.assignment.updateMany).toHaveBeenCalledTimes(1); // Batch update
            expect(mockPrisma.garmentSize.update).toHaveBeenCalledTimes(20); // Individual updates
            expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
        });
    });

    describe("Query Optimization", () => {
        it("should minimize database queries with proper includes", async () => {
            // Arrange
            let queryCount = 0;
            const trackQueries = () => {
                queryCount++;
            };

            // Track all query methods
            Object.keys(mockPrisma).forEach((model) => {
                if (typeof mockPrisma[model] === "object") {
                    Object.keys(mockPrisma[model]).forEach((method) => {
                        const original = mockPrisma[model][method];

                        if (typeof original === "function") {
                            mockPrisma[model][method] = jest.fn((...args) => {
                                trackQueries();

                                return original(...args);
                            });
                        }
                    });
                }
            });

            mockTransactionBehavior(mockPrisma, "success");

            // Act - Simulate optimized query pattern
            await mockPrisma.$transaction(async (tx: any) => {
                // Batch read
                const sizes = await tx.garmentSize.findMany({
                    where: { id: { in: ["size-1", "size-2", "size-3"] } },
                });

                // Batch create
                await tx.assignment.createMany({
                    data: generateBulkAssignments(3),
                });

                // Batch update
                await Promise.all(
                    sizes.map((size) =>
                        tx.garmentSize.update({
                            where: { id: size.id },
                            data: { availableQuantity: { decrement: 5 } },
                        }),
                    ),
                );
            });

            // Assert
            expect(queryCount).toBeLessThanOrEqual(10); // Optimized query count
        });
    });

    describe("Memory Usage", () => {
        it("should handle large datasets without memory issues", async () => {
            // Arrange
            const largeDataset = generateBulkAssignments(1000);

            // Mock memory usage tracking (simplified)
            const initialMemory = process.memoryUsage().heapUsed;

            mockPrisma.assignment.createMany.mockResolvedValue({ count: 1000 });
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            await mockPrisma.$transaction(async (tx: any) => {
                // Process in chunks to avoid memory issues
                const chunkSize = 100;

                for (let i = 0; i < largeDataset.length; i += chunkSize) {
                    const chunk = largeDataset.slice(i, i + chunkSize);

                    await tx.assignment.createMany({ data: chunk });
                }
            });

            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB

            // Assert
            expect(memoryIncrease).toBeLessThan(100); // Should not exceed 100MB increase
            expect(mockPrisma.assignment.createMany).toHaveBeenCalledTimes(10); // 1000/100 chunks
        });
    });
});
