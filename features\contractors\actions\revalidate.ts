"use server";

import { revalidatePath, revalidateTag } from "next/cache";

import { CONTRACTOR_TAGS, CONTRACTOR_PATHS } from "./constants";

/**
 * Función para revalidar todas las rutas y tags relacionados con contratistas
 * @param id ID opcional de un contratista específico
 */
export async function revalidateContractorCache(id?: string) {
    console.log(
        `[Server] 🔄 Revalidando caché de contratistas${id ? ` (ID: ${id})` : ""}`,
    );

    // 1. Revalidar rutas específicas
    revalidatePath(CONTRACTOR_PATHS.list);
    revalidatePath(CONTRACTOR_PATHS.new);
    revalidatePath(CONTRACTOR_PATHS.api);

    if (id) {
        revalidatePath(CONTRACTOR_PATHS.detail(id));
    }

    // 2. Revalidar tags para la caché del cliente (SWR)
    revalidateTag(CONTRACTOR_TAGS.all);
    revalidateTag(CONTRACTOR_TAGS.client); // Tag especial para SWR

    if (id) {
        revalidateTag(CONTRACTOR_TAGS.detail(id));
    }

    // 3. Revalidar tags relacionados (si hay entidades que dependen de contratistas)
    revalidateTag("assignments");

    // 4. Revalidar tags adicionales para asegurar actualización en SWR
    revalidateTag("getContractors");
    revalidateTag("contractors-list");

    if (id) {
        revalidateTag(`getContractor-${id}`);
    }

    console.log(`[Server] ✅ Caché de contratistas revalidada correctamente`);
}
