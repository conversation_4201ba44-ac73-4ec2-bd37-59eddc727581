/**
 * Lazy Loading Utilities
 * Implements code splitting and lazy loading for performance
 */

import dynamic from "next/dynamic";
import { ComponentType } from "react";

import { featureFlags } from "../feature-flags";

// Loading component
const LoadingComponent = () => (
    <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
    </div>
);

/**
 * Create a lazy loaded component with automatic code splitting
 */
export function createLazyComponent<P = {}>(
    importFn: () => Promise<{ default: ComponentType<P> }>,
    options?: {
        loading?: ComponentType;
        ssr?: boolean;
    },
) {
    if (!featureFlags.ENABLE_LAZY_LOADING) {
        // If lazy loading is disabled, import normally
        return dynamic(importFn, { ssr: options?.ssr ?? true });
    }

    return dynamic(importFn, {
        loading: (options?.loading || LoadingComponent) as any,
        ssr: options?.ssr ?? false, // Disable SSR for lazy components by default
    });
}

/**
 * Lazy loaded components for heavy features
 */
export const LazyComponents = {
    // Orders
    // OrdersTable: createLazyComponent(() =>
    //     import("@/features/orders/components/OrdersTable").then((mod: any) => ({
    //         default: mod.OrdersTable,
    //     })) as any,
    // ),

    // Assignments
    AssignmentWizard: createLazyComponent(
        () =>
            import("@/features/assignments/components/wizard/AssignmentWizard"),
    ),

    // Charts and visualizations
    PerformanceDashboard: createLazyComponent(
        () =>
            import("../components/PerformanceDashboard").then((mod: any) => ({
                default: mod.PerformanceDashboard,
            })) as any,
    ),
};
