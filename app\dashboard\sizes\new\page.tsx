"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Input,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>dal<PERSON>ontent,
    <PERSON>dalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
    Card,
    CardBody,
    CardHeader,
    CardFooter,
} from "@heroui/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { Ruler, Save } from "lucide-react";

import { useCreateSize } from "@/features/sizes/hooks";
import { sizeSchema, type SizeFormData } from "@/features/sizes/schemas";
import { SizeFormHeader, SizeInfoMessage } from "@/features/sizes/components";

export default function NewSizePage() {
    const router = useRouter();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { createSize } = useCreateSize();

    const {
        register,
        handleSubmit,
        formState: { errors, isDirty },
        setValue,
        watch,
    } = useForm<SizeFormData>({
        resolver: zodResolver(sizeSchema),
        mode: "onChange",
        defaultValues: {
            code: "",
        },
    });

    const codeValue = watch("code");

    // Convertir a mayúsculas automáticamente
    const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const upperValue = e.target.value.toUpperCase();

        setValue("code", upperValue, { shouldValidate: true });
    };

    const onSubmit = async (data: SizeFormData) => {
        setIsSubmitting(true);

        try {
            const result = await createSize({
                code: data.code.trim(),
            });

            if (!result.success) {
                throw new Error(result.error || "Error al crear la talla");
            }

            addToast({
                title: "Talla creada",
                description: `La talla ${data.code} se ha creado exitosamente`,
                color: "success",
            });

            // Redirigir después de un breve delay para mostrar el toast
            setTimeout(() => {
                router.push("/dashboard/sizes");
                router.refresh();
            }, 1000);
        } catch (error) {
            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error al crear la talla",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (isDirty) {
            onOpen();
        } else {
            router.push("/dashboard/sizes");
        }
    };

    return (
        <div className="container mx-auto max-w-2xl p-6">
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
            >
                <Card className="shadow-lg">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <SizeFormHeader
                            description="Crea una nueva talla para tus prendas"
                            title="Nueva Talla"
                        />
                    </CardHeader>

                    <CardBody className="p-6">
                        <form
                            className="space-y-6"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            {/* Campo de código */}
                            <div className="space-y-2">
                                <Input
                                    {...register("code")}
                                    isRequired
                                    classNames={{
                                        inputWrapper: "shadow-sm",
                                        input: "uppercase font-medium",
                                    }}
                                    description="Solo letras, números y guiones. Máximo 10 caracteres."
                                    errorMessage={errors.code?.message}
                                    isInvalid={!!errors.code}
                                    label="Código de Talla"
                                    placeholder="Ej: S, M, L, XL, 36, 38"
                                    size="lg"
                                    startContent={
                                        <Ruler className="text-gray-400 w-4 h-4" />
                                    }
                                    value={codeValue}
                                    variant="bordered"
                                    onChange={handleCodeChange}
                                />
                            </div>

                            {/* Mensaje informativo */}
                            <SizeInfoMessage
                                message="Una vez creada, la talla no podrá ser eliminada si está asociada a prendas. El código debe ser único en el sistema."
                                title="Información importante"
                                type="info"
                            />
                        </form>
                    </CardBody>

                    <CardFooter className="border-t border-gray-200 dark:border-gray-700 px-6 py-4">
                        <div className="flex justify-end gap-3 w-full">
                            <Button variant="flat" onPress={handleCancel}>
                                Cancelar
                            </Button>
                            <Button
                                color="primary"
                                isDisabled={isSubmitting}
                                isLoading={isSubmitting}
                                startContent={
                                    !isSubmitting && (
                                        <Save className="w-4 h-4" />
                                    )
                                }
                                type="submit"
                                onPress={() => handleSubmit(onSubmit)()}
                            >
                                {isSubmitting ? "Creando..." : "Crear Talla"}
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </motion.div>

            {/* Modal de confirmación */}
            <Modal
                backdrop="blur"
                classNames={{
                    base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                    header: "border-b border-gray-200 dark:border-gray-700",
                    body: "py-6",
                    footer: "border-t border-gray-200 dark:border-gray-700",
                }}
                isOpen={isOpen}
                placement="center"
                onClose={onClose}
            >
                <ModalContent>
                    <ModalHeader>Cambios sin guardar</ModalHeader>
                    <ModalBody>
                        ¿Estás seguro de que deseas salir? Los cambios se
                        perderán.
                    </ModalBody>
                    <ModalFooter>
                        <Button variant="flat" onPress={onClose}>
                            Continuar editando
                        </Button>
                        <Button
                            color="danger"
                            onPress={() => {
                                onClose();
                                router.push("/dashboard/sizes");
                            }}
                        >
                            Salir sin guardar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </div>
    );
}
