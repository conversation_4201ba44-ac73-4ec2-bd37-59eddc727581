"use client";

import type { SWRConfiguration } from "swr";

import useSWR from "swr";

import { getSubCustomers } from "@/features/customers/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

// Obtener helpers de revalidación para el cliente
const { useRevalidationListener } = createClientRevalidation("customer");

/**
 * Hook para obtener los subclientes de un cliente específico
 * @param parentId ID del cliente padre
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de subclientes, estado de carga y errores
 */
export function useSubCustomers(
    parentId: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Escuchar eventos de revalidación
    const isRevalidating = useRevalidationListener(parentId || undefined);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        parentId ? ["subcustomers", parentId] : null,
        async () => (parentId ? getSubCustomers(parentId) : null),
        {
            revalidateOnFocus: false,
            ...config,
        },
    );

    // Verificar explícitamente si data tiene la propiedad 'data'
    const subCustomersData = data && "data" in data ? data.data : null;

    return {
        subCustomers: subCustomersData,
        isLoading,
        isRevalidating,
        isError: !!error || data?.success === false,
        error: data?.error || error,
        mutate,
    };
}
