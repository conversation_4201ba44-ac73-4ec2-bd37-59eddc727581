"use client";

import React from "react";
import { UserGroupIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface CustomerFormHeaderProps {
    title: string;
    subtitle: string;
    icon?: React.ReactNode;
    showAnimation?: boolean;
}

export function CustomerFormHeader({
    title,
    subtitle,
    icon = <UserGroupIcon className="w-7 h-7 text-white" />,
    showAnimation = true,
}: CustomerFormHeaderProps) {
    return (
        <motion.div
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-4"
            initial={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.4 }}
        >
            <motion.div
                animate={
                    showAnimation
                        ? {
                              scale: [1, 1.05, 1],
                              rotate: [0, 5, -5, 0],
                          }
                        : {}
                }
                className="relative"
                transition={{
                    repeat: showAnimation ? Infinity : 0,
                    duration: 4,
                    ease: "easeInOut",
                }}
            >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl blur-md opacity-50" />
                <div className="relative bg-gradient-to-br from-purple-500 to-blue-600 p-3.5 rounded-xl shadow-lg">
                    {icon}
                </div>
            </motion.div>

            <div className="flex-1">
                <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-gray-900 via-purple-600 to-gray-800 dark:from-white dark:via-purple-400 dark:to-gray-300 bg-clip-text text-transparent">
                    {title}
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {subtitle}
                </p>
            </div>
        </motion.div>
    );
}
