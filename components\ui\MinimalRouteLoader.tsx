"use client";

import { useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

export function MinimalRouteLoader() {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        setLoading(true);
        const timer = setTimeout(() => setLoading(false), 500);

        return () => clearTimeout(timer);
    }, [pathname, searchParams]);

    return (
        <AnimatePresence>
            {loading && (
                <motion.div
                    animate={{
                        scaleX: 1,
                        opacity: 1,
                        transition: {
                            scaleX: { duration: 0.5, ease: "easeOut" },
                            opacity: { duration: 0.2 },
                        },
                    }}
                    className="fixed top-0 left-0 right-0 h-[2px] z-[9999]"
                    exit={{
                        opacity: 0,
                        transition: { duration: 0.2 },
                    }}
                    initial={{ scaleX: 0, opacity: 0 }}
                    style={{
                        transformOrigin: "left",
                        background:
                            "linear-gradient(90deg, #006FEE 0%, #0099FF 100%)",
                    }}
                />
            )}
        </AnimatePresence>
    );
}
