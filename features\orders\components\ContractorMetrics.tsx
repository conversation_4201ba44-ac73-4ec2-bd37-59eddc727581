"use client";

import React, { useState, useEffect } from "react";
import {
    Card,
    CardHeader,
    CardBody,
    Button,
    Spinner,
    Progress,
    Select,
    SelectItem,
    Table,
    TableHeader,
    TableBody,
    TableColumn,
    TableRow,
    TableCell,
    Tabs,
    Tab,
    Chip,
} from "@heroui/react";
import {
    ExclamationCircleIcon,
    ChartBarIcon,
    ClipboardIcon,
    UsersIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    AdjustmentsHorizontalIcon,
    BoltIcon,
    InformationCircleIcon,
    ArrowPathIcon,
    BuildingOfficeIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip as RechartsTooltip,
    ResponsiveContainer,
    <PERSON>Chart,
    Line,
    Legend,
} from "recharts";

import { getContractorStats } from "@/features/contractors/actions/stats";
import { getOrderTrends } from "@/features/orders/actions";

// Definir el tipo TimeFilter
type TimeFilter = "7d" | "30d" | "90d" | "1y";

// Conjunto de colores para gráficos
const CHART_COLORS = [
    "#3B82F6", // primary blue
    "#10B981", // emerald
    "#F59E0B", // amber
    "#EF4444", // red
    "#8B5CF6", // violet
    "#EC4899", // pink
    "#14B8A6", // teal
    "#F97316", // orange
    "#6366F1", // indigo
    "#84CC16", // lime
];

// Componente para mostrar una métrica con tendencia
const MetricCard = ({
    title,
    value,
    icon: Icon,
    change,
    colorClass = "text-primary",
    description,
    isLoading = false,
}: {
    title: string;
    value: string | number;
    icon: React.ElementType;
    change?: number;
    colorClass?: string;
    description?: string;
    isLoading?: boolean;
}) => (
    <Card className="bg-white dark:bg-gray-800 overflow-hidden" shadow="sm">
        <CardBody className="p-4">
            {isLoading ? (
                <div className="flex justify-center items-center h-24">
                    <Spinner color="primary" />
                </div>
            ) : (
                <>
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                {title}
                            </p>
                            <p className="text-2xl font-bold mt-1">{value}</p>
                        </div>
                        <div
                            className={`p-2 rounded-full ${colorClass} bg-opacity-20`}
                        >
                            <Icon className={`w-5 h-5 ${colorClass}`} />
                        </div>
                    </div>

                    {change !== undefined && (
                        <div className="flex items-center mt-3">
                            {change > 0 ? (
                                <ArrowTrendingUpIcon className="w-4 h-4 text-success" />
                            ) : (
                                <ArrowTrendingDownIcon className="w-4 h-4 text-danger" />
                            )}
                            <span
                                className={`text-sm ml-1 ${change > 0 ? "text-success" : "text-danger"}`}
                            >
                                {Math.abs(change)}%{" "}
                                {change > 0 ? "incremento" : "decremento"}
                            </span>
                        </div>
                    )}

                    {description && (
                        <p className="text-xs text-gray-500 mt-2">
                            {description}
                        </p>
                    )}
                </>
            )}
        </CardBody>
    </Card>
);

// Componente principal de métricas
const MetricsDashboard: React.FC = () => {
    // Estados
    const [selectedTimeFilter, setSelectedTimeFilter] =
        useState<TimeFilter>("30d");
    const [activeTab, setActiveTab] = useState("general");
    const [isLoading, setIsLoading] = useState(true);
    const [statsData, setStatsData] = useState<any>(null);
    const [trendsData, setTrendsData] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);

    // Función para obtener rango de fechas según filtro
    const getDateRange = () => {
        const endDate = new Date();
        const startDate = new Date();

        switch (selectedTimeFilter) {
            case "7d":
                startDate.setDate(endDate.getDate() - 7);
                break;
            case "30d":
                startDate.setDate(endDate.getDate() - 30);
                break;
            case "90d":
                startDate.setDate(endDate.getDate() - 90);
                break;
            case "1y":
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
        }

        return { startDate, endDate };
    };

    // Cargar datos
    useEffect(() => {
        const loadData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const { startDate, endDate } = getDateRange();

                // Cargar datos en paralelo
                const [contractorStatsResponse, orderTrendsResponse] =
                    await Promise.all([
                        getContractorStats({ startDate, endDate }),
                        getOrderTrends({ startDate, endDate }),
                    ]);

                // Procesar respuesta de estadísticas de contratistas
                if (contractorStatsResponse) {
                    if (contractorStatsResponse.success === false) {
                        setError(
                            contractorStatsResponse.error ||
                                "Error al cargar estadísticas de contratistas",
                        );
                    } else {
                        // La función puede devolver directamente los datos con el wrapper handleDbError
                        setStatsData(
                            contractorStatsResponse.success
                                ? contractorStatsResponse.data
                                : contractorStatsResponse,
                        );
                    }
                }

                // Procesar respuesta de tendencias de órdenes
                if (
                    orderTrendsResponse &&
                    orderTrendsResponse.success === true
                ) {
                    setTrendsData(orderTrendsResponse.data);
                }

                if (!contractorStatsResponse && !orderTrendsResponse) {
                    setError("No se recibieron datos");
                }
            } catch (error) {
                // REMOVED: console.error("Error cargando datos de métricas:", error);
                setError("Ocurrió un error inesperado al cargar los datos");
            } finally {
                setIsLoading(false);
            }
        };

        loadData();
    }, [selectedTimeFilter]);

    // Spinner de carga para toda la página
    if (isLoading && !statsData && !trendsData) {
        return (
            <div className="flex flex-col justify-center items-center h-96 gap-4">
                <Spinner color="primary" size="lg" />
                <p className="text-gray-500">Cargando métricas...</p>
            </div>
        );
    }

    // Manejo de errores
    if (error && !statsData && !trendsData) {
        return (
            <div className="flex flex-col items-center justify-center h-96 gap-4">
                <ExclamationCircleIcon className="w-12 h-12 text-danger" />
                <p className="text-lg text-danger">{error}</p>
                <Button
                    color="primary"
                    startContent={<ArrowPathIcon className="w-4 h-4" />}
                    variant="solid"
                    onClick={() => setSelectedTimeFilter(selectedTimeFilter)} // Esto fuerza una recarga
                >
                    Reintentar
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Cabecera con filtros */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                <div>
                    <h2 className="text-xl font-semibold flex items-center gap-2">
                        <ChartBarIcon className="w-5 h-5 text-primary" />
                        Panel de Métricas
                    </h2>
                    <p className="text-sm text-gray-500 mt-1">
                        Visualización de indicadores clave de desempeño
                    </p>
                </div>

                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">Periodo:</span>
                        <Select
                            className="w-36"
                            defaultSelectedKeys={[selectedTimeFilter]}
                            size="sm"
                            onChange={(e) =>
                                setSelectedTimeFilter(
                                    e.target.value as TimeFilter,
                                )
                            }
                        >
                            <SelectItem key="7d">Últimos 7 días</SelectItem>
                            <SelectItem key="30d">Últimos 30 días</SelectItem>
                            <SelectItem key="90d">Últimos 90 días</SelectItem>
                            <SelectItem key="1y">Último año</SelectItem>
                        </Select>
                    </div>

                    <Button
                        isIconOnly
                        color="primary"
                        isLoading={isLoading}
                        size="sm"
                        variant="flat"
                        onClick={() =>
                            setSelectedTimeFilter(selectedTimeFilter)
                        }
                    >
                        <ArrowPathIcon className="w-4 h-4" />
                    </Button>
                </div>
            </div>

            {/* Tabs de navegación */}
            <Tabs
                aria-label="Secciones de métricas"
                classNames={{
                    base: "w-full",
                    tabList:
                        "gap-6 relative rounded-none p-0 border-b border-divider",
                    tab: "max-w-fit px-2 h-12",
                    tabContent: "group-data-[selected=true]:text-primary",
                }}
                color="primary"
                selectedKey={activeTab}
                variant="underlined"
                onSelectionChange={(key) => setActiveTab(key as string)}
            >
                <Tab
                    key="general"
                    title={
                        <div className="flex items-center gap-2">
                            <ChartBarIcon className="w-4 h-4" />
                            <span>Resumen General</span>
                        </div>
                    }
                >
                    <div className="pt-4">
                        {/* Métricas principales */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <MetricCard
                                colorClass="text-primary"
                                icon={ClipboardIcon}
                                isLoading={isLoading}
                                title="Total Órdenes"
                                value={
                                    statsData?.summary?.totalAssignments || 0
                                }
                            />
                            <MetricCard
                                change={5.2}
                                colorClass="text-success"
                                icon={UsersIcon}
                                isLoading={isLoading}
                                title="Contratistas Activos"
                                value={
                                    statsData?.summary?.activeContractors || 0
                                }
                            />
                            <MetricCard
                                colorClass="text-warning"
                                icon={BoltIcon}
                                isLoading={isLoading}
                                title="Tasa de Actividad"
                                value={`${(statsData?.summary?.activityRate || 0).toFixed(1)}%`}
                            />
                            <MetricCard
                                colorClass="text-info"
                                icon={AdjustmentsHorizontalIcon}
                                isLoading={isLoading}
                                title="Promedio Asignaciones"
                                value={(
                                    statsData?.summary
                                        ?.avgAssignmentsPerContractor || 0
                                ).toFixed(1)}
                            />
                        </div>

                        {/* Gráficos principales */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            {/* Gráfico de tendencias */}
                            <Card
                                className="bg-white dark:bg-gray-800"
                                shadow="sm"
                            >
                                <CardHeader className="flex gap-3 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                    <CalendarIcon className="w-5 h-5 text-primary" />
                                    <div className="flex flex-col">
                                        <p className="text-md font-medium">
                                            Tendencia de Órdenes
                                        </p>
                                        <p className="text-small text-default-500">
                                            Evolución mensual por estado
                                        </p>
                                    </div>
                                </CardHeader>
                                <CardBody className="px-4 py-5 overflow-hidden">
                                    {isLoading && !trendsData ? (
                                        <div className="flex justify-center items-center h-80">
                                            <Spinner color="primary" />
                                        </div>
                                    ) : !trendsData ? (
                                        <div className="flex flex-col items-center justify-center h-80 gap-2">
                                            <InformationCircleIcon className="w-8 h-8 text-warning" />
                                            <p className="text-sm text-gray-500">
                                                No hay datos disponibles para
                                                mostrar
                                            </p>
                                        </div>
                                    ) : (
                                        <div className="h-80 w-full">
                                            <ResponsiveContainer
                                                height="100%"
                                                width="100%"
                                            >
                                                <LineChart
                                                    data={trendsData}
                                                    margin={{
                                                        top: 5,
                                                        right: 30,
                                                        left: 20,
                                                        bottom: 25,
                                                    }}
                                                >
                                                    <CartesianGrid
                                                        opacity={0.3}
                                                        strokeDasharray="3 3"
                                                        vertical={false}
                                                    />
                                                    <XAxis
                                                        axisLine={{
                                                            stroke: "#E5E7EB",
                                                        }}
                                                        dataKey="month"
                                                        tick={{ fontSize: 12 }}
                                                        tickFormatter={(
                                                            value,
                                                        ) => {
                                                            const date =
                                                                new Date(value);

                                                            return date.toLocaleDateString(
                                                                "es-ES",
                                                                {
                                                                    month: "short",
                                                                },
                                                            );
                                                        }}
                                                        tickLine={false}
                                                    />
                                                    <YAxis
                                                        axisLine={false}
                                                        tick={{ fontSize: 12 }}
                                                        tickLine={false}
                                                    />
                                                    <RechartsTooltip
                                                        formatter={(
                                                            value,
                                                            name,
                                                        ) => {
                                                            return [
                                                                `${value} órdenes`,
                                                                name,
                                                            ];
                                                        }}
                                                        labelFormatter={(
                                                            label,
                                                        ) => {
                                                            const date =
                                                                new Date(label);

                                                            return date.toLocaleDateString(
                                                                "es-ES",
                                                                {
                                                                    month: "long",
                                                                    year: "numeric",
                                                                },
                                                            );
                                                        }}
                                                    />
                                                    <Legend />
                                                    {Object.keys(
                                                        trendsData[0] || {},
                                                    )
                                                        .filter(
                                                            (key) =>
                                                                key !== "month",
                                                        )
                                                        .map((key, index) => (
                                                            <Line
                                                                key={key}
                                                                activeDot={{
                                                                    r: 6,
                                                                }}
                                                                dataKey={key}
                                                                dot={{ r: 4 }}
                                                                stroke={
                                                                    CHART_COLORS[
                                                                        index %
                                                                            CHART_COLORS.length
                                                                    ]
                                                                }
                                                                strokeWidth={2}
                                                                type="monotone"
                                                            />
                                                        ))}
                                                </LineChart>
                                            </ResponsiveContainer>
                                        </div>
                                    )}
                                </CardBody>
                            </Card>

                            {/* Gráfico de top contratistas */}
                            <Card
                                className="bg-white dark:bg-gray-800"
                                shadow="sm"
                            >
                                <CardHeader className="flex gap-3 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                    <BuildingOfficeIcon className="w-5 h-5 text-primary" />
                                    <div className="flex flex-col">
                                        <p className="text-md font-medium">
                                            Top Contratistas
                                        </p>
                                        <p className="text-small text-default-500">
                                            Por número de asignaciones
                                        </p>
                                    </div>
                                </CardHeader>
                                <CardBody className="px-4 py-5 overflow-hidden">
                                    {isLoading && !statsData?.topContractors ? (
                                        <div className="flex justify-center items-center h-80">
                                            <Spinner color="primary" />
                                        </div>
                                    ) : !statsData?.topContractors ||
                                      statsData.topContractors.length === 0 ? (
                                        <div className="flex flex-col items-center justify-center h-80 gap-2">
                                            <InformationCircleIcon className="w-8 h-8 text-warning" />
                                            <p className="text-sm text-gray-500">
                                                No hay datos disponibles para
                                                mostrar
                                            </p>
                                        </div>
                                    ) : (
                                        <div className="h-80 w-full">
                                            <ResponsiveContainer
                                                height="100%"
                                                width="100%"
                                            >
                                                <BarChart
                                                    data={statsData.topContractors.slice(
                                                        0,
                                                        5,
                                                    )}
                                                    layout="vertical"
                                                    margin={{
                                                        top: 5,
                                                        right: 30,
                                                        left: 100,
                                                        bottom: 5,
                                                    }}
                                                >
                                                    <CartesianGrid
                                                        horizontal={true}
                                                        opacity={0.3}
                                                        strokeDasharray="3 3"
                                                        vertical={false}
                                                    />
                                                    <XAxis
                                                        axisLine={{
                                                            stroke: "#E5E7EB",
                                                        }}
                                                        tick={{ fontSize: 12 }}
                                                        tickLine={false}
                                                        type="number"
                                                    />
                                                    <YAxis
                                                        axisLine={false}
                                                        dataKey="name"
                                                        tick={{ fontSize: 12 }}
                                                        tickLine={false}
                                                        type="category"
                                                        width={100}
                                                    />
                                                    <RechartsTooltip
                                                        formatter={(
                                                            value,
                                                            name,
                                                        ) => {
                                                            if (
                                                                name ===
                                                                "assignmentCount"
                                                            )
                                                                return [
                                                                    `${value} asignaciones`,
                                                                    "Asignaciones",
                                                                ];

                                                            return [
                                                                value,
                                                                name,
                                                            ];
                                                        }}
                                                    />
                                                    <Bar
                                                        barSize={30}
                                                        dataKey="assignmentCount"
                                                        fill="#3B82F6"
                                                        radius={[0, 4, 4, 0]}
                                                    />
                                                </BarChart>
                                            </ResponsiveContainer>
                                        </div>
                                    )}
                                </CardBody>
                            </Card>
                        </div>

                        {/* Tabla de contratistas */}
                        <Card className="bg-white dark:bg-gray-800" shadow="sm">
                            <CardHeader className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 className="text-md font-medium">
                                    Estadísticas de Contratistas
                                </h3>
                            </CardHeader>
                            <CardBody className="px-0 py-0">
                                <Table
                                    isStriped
                                    removeWrapper
                                    aria-label="Tabla de contratistas"
                                    classNames={{
                                        base: "max-h-[420px] overflow-auto",
                                        table: "min-w-full",
                                    }}
                                >
                                    <TableHeader>
                                        <TableColumn key="name">
                                            Nombre
                                        </TableColumn>
                                        <TableColumn key="assignmentCount">
                                            Asignaciones
                                        </TableColumn>
                                        <TableColumn key="totalAssigned">
                                            Total Asignado
                                        </TableColumn>
                                        <TableColumn key="completionRate">
                                            Tasa Completitud
                                        </TableColumn>
                                        <TableColumn key="defectRate">
                                            Tasa Defectos
                                        </TableColumn>
                                    </TableHeader>
                                    <TableBody
                                        emptyContent={
                                            !statsData?.topContractors
                                                ? "No hay datos disponibles"
                                                : "No hay contratistas para mostrar"
                                        }
                                        isLoading={isLoading}
                                        items={statsData?.topContractors || []}
                                        loadingContent={
                                            <Spinner color="primary" />
                                        }
                                    >
                                        {(item: any) => (
                                            <TableRow key={item.id}>
                                                <TableCell>
                                                    {item.name}
                                                </TableCell>
                                                <TableCell>
                                                    {item.assignmentCount}
                                                </TableCell>
                                                <TableCell>
                                                    {item.totalAssigned}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <Progress
                                                            className="max-w-24"
                                                            color={
                                                                item.completionRate >
                                                                75
                                                                    ? "success"
                                                                    : item.completionRate >
                                                                        50
                                                                      ? "warning"
                                                                      : "danger"
                                                            }
                                                            size="sm"
                                                            value={
                                                                item.completionRate
                                                            }
                                                        />
                                                        <span>
                                                            {item.completionRate.toFixed(
                                                                1,
                                                            )}
                                                            %
                                                        </span>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip
                                                        color={
                                                            item.defectRate < 2
                                                                ? "success"
                                                                : item.defectRate <
                                                                    5
                                                                  ? "warning"
                                                                  : "danger"
                                                        }
                                                        size="sm"
                                                        variant="flat"
                                                    >
                                                        {item.defectRate.toFixed(
                                                            1,
                                                        )}
                                                        %
                                                    </Chip>
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </CardBody>
                        </Card>
                    </div>
                </Tab>

                <Tab
                    key="contractors"
                    title={
                        <div className="flex items-center gap-2">
                            <UsersIcon className="w-4 h-4" />
                            <span>Contratistas</span>
                        </div>
                    }
                >
                    <div className="pt-4">
                        <p className="text-center text-gray-500 py-20">
                            Análisis detallado de contratistas disponible
                            próximamente
                        </p>
                    </div>
                </Tab>

                <Tab
                    key="orders"
                    title={
                        <div className="flex items-center gap-2">
                            <ClipboardIcon className="w-4 h-4" />
                            <span>Órdenes</span>
                        </div>
                    }
                >
                    <div className="pt-4">
                        <p className="text-center text-gray-500 py-20">
                            Análisis detallado de órdenes disponible
                            próximamente
                        </p>
                    </div>
                </Tab>
            </Tabs>
        </div>
    );
};

export default MetricsDashboard;
