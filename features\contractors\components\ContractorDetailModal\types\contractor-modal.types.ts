export interface ContractorModalProps {
    isOpen: boolean;
    onClose: () => void;
    contractorId: string;
    onEdit?: (contractorId: string) => void;
    onDelete?: (contractorId: string) => Promise<void>;
    currentUserId?: string;
    userRole?: string;
}

export interface ContractorWithDetails {
    id: string;
    name: string;
    firstName?: string | null;
    middleName?: string | null;
    lastName?: string | null;
    secondLastName?: string | null;
    email?: string | null;
    phone?: string | null;
    createdAt: string;
    updatedAt: string;
    _count?: {
        assignments: number;
        remissions: number;
    };
    assignments?: Assignment[];
}

export interface Assignment {
    id: string;
    quantity: number;
    status?: string;
    createdAt: string;
    updatedAt: string;
    order: {
        id: string;
        orderCode: string;
        cutOrder?: string | null;
        status: string;
        estimatedDeliveryDate?: string | null;
        customer: {
            id: string;
            name: string;
        };
        parts?: OrderPart[];
    };
    garmentSize?: {
        id: string;
        size: {
            id: string;
            name: string;
        };
        garment: {
            id: string;
            model: {
                id: string;
                code: string;
                name: string;
            };
            color: {
                id: string;
                name: string;
                hexCode: string;
            };
        };
    };
    remissions?: {
        id: string;
        quantity: number;
        remission: {
            id: string;
            folio: string;
            status: string;
            printedAt?: string | null;
        };
    }[];
}

export interface OrderPart {
    id: string;
    code: string;
    description?: string | null;
}

export type TabType = "general" | "assignments" | "history";
