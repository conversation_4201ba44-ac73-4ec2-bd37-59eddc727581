const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function main() {
    console.log("Iniciando script de inserción de datos iniciales...");

    // =====================================
    // INSERTAR VALORES PARA ROLE
    // =====================================
    const roles = [
        {
            name: "Administrador",
            iconName: "ShieldCheckIcon",
            color: "#7E22CE", // morado-600
        },
        {
            name: "Empleado",
            iconName: "UserIcon",
            color: "#0E7490", // cyan-700
        },
        {
            name: "Contratista",
            iconName: "UserGroupIcon",
            color: "#65A30D", // lime-600
        },
        {
            name: "Invitado",
            iconName: "UserCircleIcon",
            color: "#9CA3AF", // gray-400
        },
    ];

    console.log("Insertando valores para Role...");

    for (const role of roles) {
        const existingRole = await prisma["role"].findFirst({
            where: { name: role.name },
        });

        if (!existingRole) {
            const created = await prisma["role"].create({
                data: role,
            });

            console.log(`Role creado: ${created.name}`);
        } else {
            console.log(`Role ya existe: ${role.name}`);
        }
    }

    // =====================================
    // INSERTAR VALORES PARA PACKINGSTATUS
    // =====================================
    const packingStatuses = [
        {
            name: "Pendiente",
            iconName: "ClockIcon",
            color: "#6B7280", // gray-500
        },
        {
            name: "En progreso",
            iconName: "ArrowPathIcon",
            color: "#F59E0B", // amber-500
        },
        {
            name: "Entregado",
            iconName: "CheckCircleIcon",
            color: "#16A34A", // green-600
        },
        {
            name: "Rechazado",
            iconName: "ExclamationCircleIcon",
            color: "#EF4444", // red-500
        },
        {
            name: "Cancelado",
            iconName: "XCircleIcon",
            color: "#6B7280", // gray-500
        },
    ];

    console.log("Insertando valores para PackingStatus...");

    for (const status of packingStatuses) {
        const existingStatus = await prisma["packingStatus"].findFirst({
            where: { name: status.name },
        });

        if (!existingStatus) {
            const created = await prisma["packingStatus"].create({
                data: status,
            });

            console.log(`PackingStatus creado: ${created.name}`);
        } else {
            console.log(`PackingStatus ya existe: ${status.name}`);
        }
    }

    // =====================================
    // INSERTAR VALORES PARA REJECTIONREASON
    // =====================================
    const rejectionReasons = [
        {
            name: "Calidad",
            iconName: "ShieldExclamationIcon",
            color: "#EF4444", // red-500
        },
        {
            name: "Defecto",
            iconName: "ExclamationTriangleIcon",
            color: "#F97316", // orange-500
        },        {
            name: "Retraso",
            iconName: "ClockIcon",
            color: "#F59E0B", // amber-500
        },
        {
            name: "Otro",
            iconName: "QuestionMarkCircleIcon",
            color: "#6B7280", // gray-500
        },
    ];

    console.log("Insertando valores para RejectionReason...");

    for (const reason of rejectionReasons) {
        const existingReason = await prisma["rejectionReason"].findFirst({
            where: { name: reason.name },
        });

        if (!existingReason) {
            const created = await prisma["rejectionReason"].create({
                data: reason,
            });

            console.log(`RejectionReason creado: ${created.name}`);
        } else {
            console.log(`RejectionReason ya existe: ${reason.name}`);
        }
    }

    // =====================================
    // INSERTAR VALORES PARA NOTEIMPORTANCE
    // =====================================
    const noteImportances = [
        {
            name: "Bajo",
            iconName: "InformationCircleIcon",
            color: "#3B82F6", // blue-500
        },
        {
            name: "Medio",
            iconName: "ExclamationTriangleIcon",
            color: "#F59E0B", // amber-500
        },
        {
            name: "Alto",
            iconName: "ExclamationTriangleIcon",
            color: "#EF4444", // red-500
        },
    ];

    console.log("Insertando valores para NoteImportance...");
    for (const importance of noteImportances) {
        const existingImportance = await prisma["noteImportance"].findFirst({
            where: { name: importance.name },
        });

        if (!existingImportance) {
            const created = await prisma["noteImportance"].create({
                data: importance,
            });

            console.log(`Importancia creada: ${created.name}`);
        } else {
            console.log(`Importancia ya existe: ${importance.name}`);
        }
    }

    // =====================================
    // INSERTAR VALORES PARA NOTESTATUS
    // =====================================
    const noteStatuses = [
        {
            name: "Pendiente",
            iconName: "ClockIcon",
            color: "#F59E0B", // amber-500
        },
        {
            name: "En progreso",
            iconName: "ArrowPathIcon",
            color: "#3B82F6", // blue-500
        },
        {
            name: "Completado",
            iconName: "CheckCircleIcon",
            color: "#16A34A", // green-600
        },
        {
            name: "Cancelado",
            iconName: "XCircleIcon",
            color: "#6B7280", // gray-500
        },
    ];

    console.log("Insertando valores para NoteStatus...");

    for (const status of noteStatuses) {
        const existingStatus = await prisma["noteStatus"].findFirst({
            where: { name: status.name },
        });

        if (!existingStatus) {            const created = await prisma["noteStatus"].create({
                data: status,
            });

            console.log(`NoteStatus creado: ${created.name}`);
        } else {
            console.log(`NoteStatus ya existe: ${status.name}`);
        }
    }

    // =====================================
    // INSERTAR VALORES PARA ORDERSTATUS
    // =====================================
    const orderStatuses = [
        {
            name: "Nuevo",
            iconName: "SparklesIcon",
            color: "#10B981", // emerald-500
        },
        {
            name: "Recibido",
            iconName: "InboxIcon",
            color: "#3B82F6", // blue-500
        },
        {
            name: "En producción",
            iconName: "CogIcon",
            color: "#F59E0B", // amber-500
        },
        {
            name: "Control de calidad",
            iconName: "CheckBadgeIcon",
            color: "#8B5CF6", // violet-500
        },
        {
            name: "Empaquetando",
            iconName: "CubeIcon",
            color: "#14B8A6", // teal-500
        },
        {
            name: "Listo para entregar",
            iconName: "TruckIcon",
            color: "#10B981", // emerald-500
        },
        {
            name: "Entregado",
            iconName: "CheckCircleIcon",
            color: "#16A34A", // green-600
        },
        {
            name: "Rechazado",
            iconName: "ExclamationCircleIcon",
            color: "#EF4444", // red-500
        },
        {
            name: "Cancelado",
            iconName: "XCircleIcon",
            color: "#6B7280", // gray-500
        },
    ];

    console.log("Insertando valores para OrderStatus...");

    for (const status of orderStatuses) {
        const existingStatus = await prisma["orderStatus"].findFirst({
            where: { name: status.name },
        });

        if (!existingStatus) {
            const created = await prisma["orderStatus"].create({
                data: status,
            });

            console.log(`OrderStatus creado: ${created.name}`);
        } else {
            console.log(`OrderStatus ya existe: ${status.name}`);
        }
    }

    console.log("Script de inserción completado.");
}

main()
    .catch((e) => {
        console.error(e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
