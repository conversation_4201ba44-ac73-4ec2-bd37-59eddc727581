import "../styles/globals.css";
import "../styles/print.css";
import { Metadata, Viewport } from "next";
import clsx from "clsx";

// Inicialización de la aplicación
import "@/shared/lib/app-init";

import HybridNav from "@/shared/components/navigation/HybridNav";
import { RouteLoader } from "@/components/ui/RouteLoader";
import { SearchParamsWrapper } from "@/shared/components/ui";

import { siteConfig } from "../config/site";
import { fontSans } from "../config/fonts";
import { NavigationProvider } from "../core/context/navigation-context";

import { Providers } from "./providers";

export const metadata: Metadata = {
    title: {
        default: siteConfig.name,
        template: `%s - ${siteConfig.name}`,
    },
    description: siteConfig.description,
    icons: {
        icon: "/favicon.ico",
    },
};

export const viewport: Viewport = {
    themeColor: [
        { media: "(prefers-color-scheme: light)", color: "white" },
        { media: "(prefers-color-scheme: dark)", color: "black" },
    ],
};

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html suppressHydrationWarning lang="en">
            <head />
            <body
                className={clsx(
                    "min-h-screen bg-background font-sans antialiased",
                    fontSans.variable,
                )}
            >
                <Providers
                    themeProps={{ attribute: "class", defaultTheme: "light" }}
                >
                    <NavigationProvider>
                        <SearchParamsWrapper>
                            <RouteLoader />
                        </SearchParamsWrapper>
                        <HybridNav>{children}</HybridNav>
                    </NavigationProvider>
                </Providers>
            </body>
        </html>
    );
}
