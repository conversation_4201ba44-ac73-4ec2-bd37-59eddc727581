---
description: Visual Process Map para QA Mode - disponible desde cualquier modo
globs: qa-mode-map.mdc
alwaysApply: false
---
# 🧪 QA MODE - VISUAL PROCESS MAP

> Modo de validación técnica accesible desde cualquier otro modo

## 📊 MAPA DE PROCESO COMPLETO

```mermaid
graph TD
    Start["🧪 QA Request"] --> Source{"¿Desde qué<br>modo?"}
    
    Source -->|VAN| QAVan["QA: Validar<br>Análisis"]
    Source -->|PLAN| QAPlan["QA: Validar<br>Plan"]
    Source -->|CREATIVE| QACreative["QA: Validar<br>Diseño"]
    Source -->|IMPLEMENT| QAImpl["QA: Validar<br>Código"]
    Source -->|REFLECT| QAReflect["QA: Validar<br>Métricas"]
    
    QAVan & QAPlan & QACreative & QAImpl & QAReflect --> Type{"🎯 Tipo de<br>Validación"}
    
    Type -->|Funcional| Functional["⚙️ Tests Funcionales<br>🧠 scientificmethod"]
    Type -->|Performance| Performance["⚡ Tests Performance<br>🧠 scientificmethod"]
    Type -->|Security| Security["🔒 Tests Seguridad<br>🧠 debuggingapproach"]
    Type -->|Quality| Quality["✨ Code Quality<br>🧠 metacognitivemonitoring"]
    
    Functional --> FTests["Ejecutar Suite<br>de Tests"]
    Performance --> PTests["Ejecutar<br>Benchmarks"]
    Security --> STests["Ejecutar<br>Auditoría"]
    Quality --> QTests["Ejecutar<br>Análisis"]
    
    FTests & PTests & STests & QTests --> Results{"📊 ¿Resultados<br>OK?"}
    
    Results -->|Sí| Report["📄 Generar<br>Reporte QA"]
    Results -->|No| Issues["🐛 Identificar<br>Issues"]
    
    Issues --> Fix["🔧 Proponer<br>Fixes"]
    Fix --> Retest["🔄 Re-test"]
    Retest --> Results
    
    Report --> Update["💾 Update<br>qa-report.md"]
    Update --> Return["↩️ Volver al<br>modo original"]
    
    %% Clear Thought Tools
    Functional -.-> CT1["🧠 scientificmethod<br>Validación sistemática"]
    Security -.-> CT2["🧠 debuggingapproach<br>Encontrar vulnerabilidades"]
    Quality -.-> CT3["🧠 metacognitivemonitoring<br>Evaluar calidad"]
    
    style Start fill:#ff9980
    style Source fill:#d94dbb
    style Results fill:#ffa64d
    style Report fill:#5fd94d
    style Issues fill:#ff5555
```

## 🧪 TIPOS DE VALIDACIÓN

### Functional Testing
```typescript
// Activar scientificmethod para tests sistemáticos
const functionalTests = await validateWithTool('scientificmethod', {
    hypothesis: 'Component behaves as specified',
    experiment: {
        design: 'Run comprehensive test suite',
        methodology: 'Unit + Integration + E2E',
        predictions: expectedBehaviors
    },
    results: await runTestSuite(),
    analysis: analyzeTestResults()
});
```

### Performance Testing
```typescript
interface PerformanceTests {
    loadTime: {
        target: '<3s',
        actual: number,
        status: 'pass' | 'fail'
    };
    memoryUsage: {
        target: '<100MB',
        actual: number,
        status: 'pass' | 'fail'
    };
    responseTime: {
        target: '<200ms',
        actual: number,
        status: 'pass' | 'fail'  
    };
}
```

### Security Audit
```typescript
// Activar debuggingapproach para encontrar vulnerabilidades
const securityAudit = await auditWithTool('debuggingapproach', {
    approachName: 'cause_elimination',
    issue: 'Potential security vulnerabilities',
    steps: [
        'Dependency scanning',
        'Code analysis',
        'Penetration testing',
        'Access control review'
    ]
});
```

## 📋 QA CHECKLISTS POR MODO

### QA desde VAN Mode
```markdown
## QA Checklist - Project Analysis
- [ ] Project structure correctly analyzed
- [ ] Complexity level appropriate (1-4)
- [ ] All dependencies identified
- [ ] Technology stack verified
- [ ] Workflow recommendation valid
```

### QA desde PLAN Mode
```markdown
## QA Checklist - Planning
- [ ] All requirements covered in plan
- [ ] Dependencies correctly mapped
- [ ] Time estimates reasonable
- [ ] Creative components identified
- [ ] Risk mitigation included
```

### QA desde CREATIVE Mode
```markdown
## QA Checklist - Design Decisions
- [ ] All options thoroughly explored (2-4)
- [ ] Trade-offs clearly analyzed
- [ ] Decision rationale documented
- [ ] Implementation guidelines clear
- [ ] Constraints respected
```

### QA desde IMPLEMENT Mode
```markdown
## QA Checklist - Implementation
- [ ] Code follows planned architecture
- [ ] All tests passing
- [ ] No linting errors
- [ ] Performance targets met
- [ ] Security best practices followed
- [ ] Documentation complete
```

## 📊 REPORTE QA TEMPLATE

```markdown
# 🧪 QA Report - [Component/Feature]
**Date**: [ISO timestamp]
**Triggered From**: [Mode name]
**QA Type**: [Functional | Performance | Security | Quality]

## 📋 Test Summary
| Category | Tests | Passed | Failed | Coverage |
|----------|-------|--------|--------|----------|
| Unit | X | Y | Z | A% |
| Integration | X | Y | Z | B% |
| E2E | X | Y | Z | C% |
| **Total** | **X** | **Y** | **Z** | **D%** |

## ⚡ Performance Results
| Metric | Target | Actual | Status | Notes |
|--------|--------|--------|--------|-------|
| Load Time | <3s | Xs | ✅/❌ | - |
| Memory | <100MB | XMB | ✅/❌ | - |
| CPU | <50% | X% | ✅/❌ | - |

## 🔒 Security Findings
| Issue | Severity | Status | Resolution |
|-------|----------|--------|------------|
| [Issue] | High/Med/Low | Open/Fixed | [Details] |

## ✨ Code Quality Metrics
- **Complexity**: [Score] (Target: <10)
- **Maintainability**: [Score] (Target: >70)
- **Test Coverage**: [X%] (Target: >80%)
- **Technical Debt**: [X hours]

## 🐛 Issues Found
### Critical Issues
1. **[Issue Name]**
   - Description: [Details]
   - Impact: [Impact description]
   - Suggested Fix: [Fix approach]

### Non-Critical Issues
1. **[Issue Name]**
   - Description: [Details]
   - Priority: [High/Medium/Low]
   - Suggested Fix: [Fix approach]

## ✅ Recommendations
1. **Immediate Actions**
   - [Action 1]
   - [Action 2]

2. **Future Improvements**
   - [Improvement 1]
   - [Improvement 2]

## 🎯 QA Status
**Overall Result**: PASS ✅ / FAIL ❌
**Ready for Next Phase**: Yes / No
**Blocking Issues**: [Count]

---
**Next Steps**: [Return to original mode / Fix issues]
```

## 🔄 PROCESO QA INTEGRADO

```typescript
class QAValidator {
    private sourceMode: ModeName;
    private testResults: TestResults;
    
    async runQA(type: QAType, context: any) {
        // 1. Determinar tests apropiados
        const testSuite = this.selectTestSuite(type, this.sourceMode);
        
        // 2. Ejecutar tests con Clear Thought
        switch(type) {
            case 'functional':
                this.testResults = await this.runFunctionalTests();
                break;
            case 'performance':
                this.testResults = await this.runPerformanceTests();
                break;
            case 'security':
                this.testResults = await this.runSecurityAudit();
                break;
            case 'quality':
                this.testResults = await this.runQualityAnalysis();
                break;
        }
        
        // 3. Analizar resultados
        const analysis = await this.analyzeResults(this.testResults);
        
        // 4. Generar reporte
        const report = this.generateQAReport(analysis);
        
        // 5. Actualizar Memory Bank
        this.updateMemoryBank(report);
        
        return {
            passed: analysis.passed,
            report: report,
            returnToMode: this.sourceMode
        };
    }
}
```

## 💾 INTEGRACIÓN CON MEMORY BANK

```typescript
// QA reports se guardan en Memory Bank
interface QAMemoryBank {
    'qa-reports/': {
        'qa-[timestamp].md': QAReport;
        'qa-summary.json': {
            totalTests: number;
            passRate: number;
            criticalIssues: Issue[];
            performanceMetrics: Metrics;
            lastRun: Date;
        };
    };
}

// Actualización después de QA
updateQAStatus({
    mode: sourceMode,
    component: testedComponent,
    results: qaResults,
    issues: identifiedIssues,
    recommendations: improvements
});
```

## ✅ CHECKLIST DE VERIFICACIÓN QA

### Pre-QA
- [ ] Contexto del modo origen guardado
- [ ] Tipo de QA determinado
- [ ] Test suite apropiado seleccionado

### Durante QA
- [ ] Tests funcionales ejecutados
- [ ] Performance medido
- [ ] Seguridad auditada
- [ ] Calidad analizada
- [ ] Issues documentados

### Post-QA
- [ ] Reporte QA generado
- [ ] Memory Bank actualizado
- [ ] Issues priorizados
- [ ] Recomendaciones documentadas
- [ ] Listo para volver al modo origen

---

**QA Mode - Validación técnica desde cualquier punto**
