/**
 * Constantes para todas las rutas de la aplicación
 * Centraliza la definición de rutas para evitar errores y facilitar cambios
 */

// Rutas principales
export const HOME = "/";
export const DASHBOARD = "/dashboard";
export const LOGIN = "/login";
export const REGISTER = "/register";

// Rutas del dashboard
export const ORDERS = `${DASHBOARD}/orders`;
export const CUSTOMERS = `${DASHBOARD}/customers`;
export const CONTRACTORS = `${DASHBOARD}/contractors`;
export const MODELS = `${DASHBOARD}/models`;
export const SIZES = `${DASHBOARD}/sizes`;
export const COLORS = `${DASHBOARD}/colors`;

// Funciones para generar rutas dinámicas
export const getOrderRoute = (id: string) => `${ORDERS}/${id}`;
export const getOrderEditRoute = (id: string) => `${ORDERS}/${id}/edit`;
export const getCustomerRoute = (id: string) => `${CUSTOMERS}/${id}`;
export const getCustomerEditRoute = (id: string) => `${CUSTOMERS}/${id}/edit`;
export const getContractorRoute = (id: string) => `${CONTRACTORS}/${id}`;
export const getContractorEditRoute = (id: string) =>
    `${CONTRACTORS}/${id}/edit`;
export const getModelRoute = (id: string) => `${MODELS}/${id}`;
export const getModelEditRoute = (id: string) => `${MODELS}/${id}/edit`;
export const getSizeRoute = (id: string) => `${SIZES}/${id}`;
export const getSizeEditRoute = (id: string) => `${SIZES}/${id}/edit`;
export const getColorRoute = (id: string) => `${COLORS}/${id}`;
export const getColorEditRoute = (id: string) => `${COLORS}/${id}/edit`;
