"use client";

import type { SortOption } from "@/shared/components/dashboard";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
    ClockIcon,
    ChatBubbleLeftRightIcon,
    TagIcon,
    FlagIcon,
    HeartIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";
import {
    MagnifyingGlassIcon,
    PlusIcon,
    Squares2X2Icon,
    ListBulletIcon,
} from "@heroicons/react/24/outline";

import { sortData } from "@/shared/utils/sortHelpers";
import { useNotesData } from "@/features/notes/hooks";
import {
    deleteNote,
    bulkDeleteNotes,
    bulkUpdateNoteStatus,
    bulkUpdateNoteImportance,
    toggleNoteLike,
} from "@/features/notes/actions";
import {
    Chip,
    Tooltip,
    Avatar,
    Badge,
    Spinner,
    Input,
    Select,
    SelectItem,
    Button,
    Card,
    CardBody,
} from "@/shared/components/ui/hero-ui-client";
import { EnhancedNoteCardV2 } from "@/features/notes/components/EnhancedNoteCardV2";

// Local type definition for Note
interface Note {
    id: string;
    content: string;
    createdAt: string | Date;
    updatedAt: string | Date;
    authorId?: string;
    author?: {
        id: string;
        name?: string;
        image?: string | null;
    };
    order?: {
        id: string;
        code: string;
        customer?: {
            name: string;
        };
    };
    importance?: {
        id: string;
        name: string;
        color?: string;
    };
    status?: {
        id: string;
        name: string;
        color?: string;
    };
    _count?: {
        comments?: number;
        likes?: number;
    };
    isLiked?: boolean;
    likes?: Array<{ userId: string }>;
}

// Mapa de colores para importancia
const importanceColors = {
    Crítica: "danger",
    Alta: "warning",
    Media: "primary",
    Baja: "default",
} as const;

// Mapa de colores para estado
const statusColors = {
    Pendiente: "warning",
    "En Proceso": "primary",
    Resuelta: "success",
    Archivada: "default",
} as const;

export default function UnifiedNotesPage() {
    const router = useRouter();
    const { data: session } = useSession();
    const currentUserId = session?.user?.id;

    const {
        notes = [],
        statuses = [],
        importances = [],
        users = [],
        isLoading,
        mutate,
        stats,
    } = useNotesData(currentUserId);

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "createdAt-desc",
        label: "Más reciente",
        field: "createdAt",
        direction: "desc",
    });
    const [page, setPage] = useState(1);
    const [selectedNotes, setSelectedNotes] = useState<Set<string>>(new Set());
    const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
    const rowsPerPage = 12;

    // Calcular estadísticas dinámicas
    const dynamicStats = useMemo(() => {
        const lastWeek = new Date();

        lastWeek.setDate(lastWeek.getDate() - 7);

        // Calcular cambios vs semana pasada
        const notesLastWeek = notes.filter(
            (n) => new Date(n.createdAt) < lastWeek,
        ).length;
        const criticalLastWeek = notes.filter(
            (n) =>
                new Date(n.createdAt) < lastWeek &&
                n.importance?.name === "Crítica",
        ).length;

        const criticalChange = stats.critical - criticalLastWeek;
        const activityGrowth =
            notesLastWeek > 0
                ? ((stats.recentActivity - notesLastWeek) / notesLastWeek) * 100
                : 100;

        return [
            {
                title: "Total de Notas",
                value: stats.total,
                icon: <DocumentTextIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Notas activas",
            },
            {
                title: "Críticas",
                value: stats.critical,
                icon: <ExclamationTriangleIcon className="w-6 h-6" />,
                color: "danger" as const,
                change: criticalChange,
                changeLabel: "vs. semana pasada",
                changeType:
                    criticalChange > 0 ? "negative" : ("positive" as const),
            },
            {
                title: "Sin Resolver",
                value: stats.unresolved,
                icon: <ClockIcon className="w-6 h-6" />,
                color: "warning" as const,
                description: "Requieren atención",
            },
            {
                title: "Actividad Reciente",
                value: stats.recentActivity,
                icon: <ChatBubbleLeftRightIcon className="w-6 h-6" />,
                color: "success" as const,
                change: activityGrowth,
                changeLabel: "últimos 7 días",
            },
        ];
    }, [notes, stats]);

    // Columnas de la tabla
    const columns = [
        {
            key: "content",
            label: "Contenido",
            sortable: true,
            render: (note: Note) => (
                <div className="max-w-md">
                    <p className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                        {note.content}
                    </p>
                    {note.order && (
                        <div className="mt-1 flex items-center gap-2">
                            <Badge color="primary" size="sm" variant="flat">
                                Orden: {note.order.code}
                            </Badge>
                            {note.order.customer && (
                                <span className="text-xs text-gray-500">
                                    {note.order.customer.name}
                                </span>
                            )}
                        </div>
                    )}
                </div>
            ),
        },
        {
            key: "importance",
            label: "Importancia",
            render: (note: Note) => {
                if (!note.importance) return null;
                const color =
                    importanceColors[
                        note.importance.name as keyof typeof importanceColors
                    ] || "default";

                return (
                    <Chip
                        color={color}
                        size="sm"
                        startContent={<FlagIcon className="w-3 h-3" />}
                        variant="flat"
                    >
                        {note.importance.name}
                    </Chip>
                );
            },
        },
        {
            key: "status",
            label: "Estado",
            render: (note: Note) => {
                if (!note.status) return null;
                const color =
                    statusColors[
                        note.status.name as keyof typeof statusColors
                    ] || "default";

                return (
                    <Chip color={color} size="sm" variant="flat">
                        {note.status.name}
                    </Chip>
                );
            },
        },
        {
            key: "author",
            label: "Autor",
            render: (note: Note) => (
                <div className="flex items-center gap-2">
                    <Avatar
                        color="secondary"
                        name={note.author?.name || "Unknown"}
                        size="sm"
                    />
                    <div className="text-sm">
                        <p className="font-medium">
                            {note.author?.name || "Unknown"}
                        </p>
                        <p className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(note.createdAt), {
                                locale: es,
                                addSuffix: true,
                            })}
                        </p>
                    </div>
                </div>
            ),
        },
        {
            key: "engagement",
            label: "Interacciones",
            render: (note: Note) => {
                const likes = note._count?.likes || 0;
                const comments = note._count?.comments || 0;
                const isLiked = note.likes?.some(
                    (like) => like.userId === currentUserId,
                );

                return (
                    <div className="flex items-center gap-3">
                        <Tooltip content={`${likes} me gusta`}>
                            <button
                                className="flex items-center gap-1 text-sm hover:text-red-500 transition-colors"
                                onClick={async (e) => {
                                    e.stopPropagation();
                                    if (currentUserId) {
                                        await toggleNoteLike({
                                            noteId: note.id,
                                        });
                                        mutate();
                                    }
                                }}
                            >
                                {isLiked ? (
                                    <HeartIconSolid className="w-4 h-4 text-red-500" />
                                ) : (
                                    <HeartIcon className="w-4 h-4" />
                                )}
                                <span>{likes}</span>
                            </button>
                        </Tooltip>
                        <Tooltip content={`${comments} comentarios`}>
                            <div className="flex items-center gap-1 text-sm text-gray-600">
                                <ChatBubbleLeftRightIcon className="w-4 h-4" />
                                <span>{comments}</span>
                            </div>
                        </Tooltip>
                    </div>
                );
            },
        },
        {
            key: "dates",
            label: "Actividad",
            render: (note: Note) => (
                <div className="text-sm space-y-1">
                    <div className="flex items-center gap-1 text-gray-600">
                        <CalendarIcon className="w-3 h-3" />
                        <span>
                            Creada:{" "}
                            {format(new Date(note.createdAt), "dd/MM/yyyy", {
                                locale: es,
                            })}
                        </span>
                    </div>
                    {note.updatedAt && note.updatedAt !== note.createdAt && (
                        <div className="text-xs text-gray-500">
                            Actualizada:{" "}
                            {formatDistanceToNow(new Date(note.updatedAt), {
                                locale: es,
                                addSuffix: true,
                            })}
                        </div>
                    )}
                </div>
            ),
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (note: Note) => {
                // Aquí podrías abrir un modal o navegar a una página de detalles
                router.push(`/dashboard/notes/${note.id}`);
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (note: Note) => {
                router.push(`/dashboard/notes/${note.id}/edit`);
            },
            color: "primary" as const,
            isHidden: (note: Note) => note.authorId !== currentUserId,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (note: Note) => {
                if (confirm("¿Estás seguro de eliminar esta nota?")) {
                    const result = await deleteNote(note.id);

                    if (result.success) {
                        mutate();
                        addToast({
                            title: "Nota eliminada",
                            description:
                                "La nota ha sido eliminada correctamente",
                            color: "success",
                        });
                    }
                }
            },
            color: "danger" as const,
            isHidden: (note: Note) => note.authorId !== currentUserId,
        },
    ];

    // Filtros
    const filters = [
        {
            key: "statusId",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                ...statuses.map((s) => ({ value: s.id, label: s.name })),
            ],
        },
        {
            key: "importanceId",
            label: "Importancia",
            type: "select" as const,
            placeholder: "Todas las importancias",
            options: [
                { value: "all", label: "Todas" },
                ...importances.map((i) => ({ value: i.id, label: i.name })),
            ],
        },
        {
            key: "authorId",
            label: "Autor",
            type: "select" as const,
            placeholder: "Todos los autores",
            options: [
                { value: "all", label: "Todos" },
                { value: currentUserId || "", label: "Mis notas" },
                ...users
                    .filter((u) => u.id !== currentUserId)
                    .map((u) => ({
                        value: u.id,
                        label: u.name || "Sin nombre",
                    })),
            ],
        },
        {
            key: "period",
            label: "Periodo",
            type: "select" as const,
            placeholder: "Todo el tiempo",
            options: [
                { value: "all", label: "Todo el tiempo" },
                { value: "today", label: "Hoy" },
                { value: "7", label: "Últimos 7 días" },
                { value: "30", label: "Últimos 30 días" },
                { value: "90", label: "Últimos 90 días" },
            ],
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
        {
            key: "importance-desc",
            label: "Mayor importancia",
            field: "importance.level",
            direction: "desc" as const,
        },
        {
            key: "likes-desc",
            label: "Más populares",
            field: "_count.likes",
            direction: "desc" as const,
        },
        {
            key: "comments-desc",
            label: "Más comentadas",
            field: "_count.comments",
            direction: "desc" as const,
        },
    ];

    // Filtrar y ordenar datos
    const filteredData = useMemo(() => {
        let filtered = [...notes];

        // Búsqueda
        if (searchValue) {
            const search = searchValue.toLowerCase();

            filtered = filtered.filter(
                (note) =>
                    note.content.toLowerCase().includes(search) ||
                    note.order?.code?.toLowerCase().includes(search) ||
                    note.order?.customer?.name
                        ?.toLowerCase()
                        .includes(search) ||
                    note.author?.name?.toLowerCase().includes(search),
            );
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [notes, searchValue, currentSort]);

    // Acciones masivas
    const bulkActions = useMemo(
        () => [
            {
                key: "updateStatus",
                label: "Cambiar Estado",
                icon: <TagIcon className="w-4 h-4" />,
                options: statuses.map((s) => ({ value: s.id, label: s.name })),
                action: async (noteIds: string[], statusId: string) => {
                    const result = await bulkUpdateNoteStatus({
                        noteIds,
                        statusId,
                    });

                    if (result.success) {
                        mutate();
                        addToast({
                            title: "Estado actualizado",
                            description: `${noteIds.length} notas actualizadas`,
                            color: "success",
                        });
                        setSelectedNotes(new Set());
                    }
                },
            },
            {
                key: "updateImportance",
                label: "Cambiar Importancia",
                icon: <FlagIcon className="w-4 h-4" />,
                options: importances.map((i) => ({
                    value: i.id,
                    label: i.name,
                })),
                action: async (noteIds: string[], importanceId: string) => {
                    const result = await bulkUpdateNoteImportance({
                        noteIds,
                        importanceId,
                    });

                    if (result.success) {
                        mutate();
                        addToast({
                            title: "Importancia actualizada",
                            description: `${noteIds.length} notas actualizadas`,
                            color: "success",
                        });
                        setSelectedNotes(new Set());
                    }
                },
            },
            {
                key: "delete",
                label: "Eliminar",
                icon: <TrashIcon className="w-4 h-4" />,
                color: "danger" as const,
                action: async (noteIds: string[]) => {
                    if (
                        confirm(
                            `¿Estás seguro de eliminar ${noteIds.length} notas?`,
                        )
                    ) {
                        const result = await bulkDeleteNotes({ noteIds });

                        if (result.success) {
                            mutate();
                            addToast({
                                title: "Notas eliminadas",
                                description: `${noteIds.length} notas eliminadas correctamente`,
                                color: "success",
                            });
                            setSelectedNotes(new Set());
                        }
                    }
                },
            },
        ],
        [statuses, importances, mutate],
    );

    // Componente de actividad reciente
    const RecentActivity = () => {
        const recentNotes = useMemo(() => {
            return [...notes]
                .sort(
                    (a, b) =>
                        new Date(b.createdAt).getTime() -
                        new Date(a.createdAt).getTime(),
                )
                .slice(0, 5);
        }, []);

        if (recentNotes.length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
                initial={{ opacity: 0, y: 20 }}
            >
                <h3 className="text-lg font-semibold mb-4">
                    Actividad Reciente
                </h3>
                <div className="space-y-3">
                    {recentNotes.map((note) => (
                        <div
                            key={note.id}
                            className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                            <Avatar
                                color="secondary"
                                name={note.author?.name || "Unknown"}
                                size="sm"
                            />
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <p className="font-medium text-sm">
                                        {note.author?.name}
                                    </p>
                                    <span className="text-xs text-gray-500">
                                        {formatDistanceToNow(
                                            new Date(note.createdAt),
                                            {
                                                locale: es,
                                                addSuffix: true,
                                            },
                                        )}
                                    </span>
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                                    {note.content}
                                </p>
                                <div className="flex items-center gap-3 mt-2">
                                    {note.importance && (
                                        <Chip
                                            color={
                                                importanceColors[
                                                    note.importance
                                                        .name as keyof typeof importanceColors
                                                ] || "default"
                                            }
                                            size="sm"
                                            variant="flat"
                                        >
                                            {note.importance.name}
                                        </Chip>
                                    )}
                                    {note.order && (
                                        <Badge size="sm" variant="flat">
                                            {note.order.code}
                                        </Badge>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </motion.div>
        );
    };

    // Paginación manual
    const totalPages = Math.ceil(filteredData.length / rowsPerPage);
    const paginatedData = filteredData.slice(
        (page - 1) * rowsPerPage,
        page * rowsPerPage,
    );

    // Handlers
    const handleToggleLike = async (noteId: string) => {
        if (currentUserId) {
            await toggleNoteLike({ noteId });
            mutate();
        }
    };

    const handleEdit = (note: Note) => {
        router.push(`/dashboard/notes/${note.id}/edit`);
    };

    const handleDelete = async (note: Note) => {
        if (confirm("¿Estás seguro de eliminar esta nota?")) {
            const result = await deleteNote(note.id);

            if (result.success) {
                mutate();
                addToast({
                    title: "Nota eliminada",
                    description: "La nota ha sido eliminada correctamente",
                    color: "success",
                });
            }
        }
    };

    const handleView = (note: Note) => {
        router.push(`/dashboard/notes/${note.id}`);
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold">Notas</h1>
                    <p className="text-gray-500">
                        Gestión de observaciones y comunicación
                    </p>
                </div>
                <Button
                    color="primary"
                    startContent={<PlusIcon className="w-5 h-5" />}
                    onPress={() => router.push("/dashboard/notes/new")}
                >
                    Nueva Nota
                </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {dynamicStats.map((stat, index) => (
                    <motion.div
                        key={index}
                        animate={{ opacity: 1, y: 0 }}
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ delay: index * 0.1 }}
                    >
                        <Card>
                            <CardBody className="p-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm text-gray-500">
                                            {stat.title}
                                        </p>
                                        <p className="text-2xl font-bold">
                                            {stat.value}
                                        </p>
                                        {stat.description && (
                                            <p className="text-xs text-gray-400 mt-1">
                                                {stat.description}
                                            </p>
                                        )}
                                        {stat.change !== undefined && (
                                            <div
                                                className={`text-xs mt-1 ${stat.changeType === "positive" ? "text-green-500" : "text-red-500"}`}
                                            >
                                                {stat.change > 0 ? "+" : ""}
                                                {stat.change.toFixed(1)}%{" "}
                                                {stat.changeLabel}
                                            </div>
                                        )}
                                    </div>
                                    <div
                                        className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}
                                    >
                                        {stat.icon}
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </motion.div>
                ))}
            </div>

            {/* Filters and Search */}
            <Card>
                <CardBody>
                    <div className="space-y-4">
                        <div className="flex flex-col lg:flex-row gap-4">
                            {/* Search */}
                            <Input
                                isClearable
                                className="flex-1"
                                placeholder="Buscar notas..."
                                startContent={
                                    <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                                }
                                value={searchValue}
                                onClear={() => setSearchValue("")}
                                onValueChange={setSearchValue}
                            />

                            {/* View Mode Toggle */}
                            <div className="flex gap-2">
                                <Button
                                    isIconOnly
                                    variant={
                                        viewMode === "grid" ? "solid" : "flat"
                                    }
                                    onPress={() => setViewMode("grid")}
                                >
                                    <Squares2X2Icon className="w-5 h-5" />
                                </Button>
                                <Button
                                    isIconOnly
                                    variant={
                                        viewMode === "list" ? "solid" : "flat"
                                    }
                                    onPress={() => setViewMode("list")}
                                >
                                    <ListBulletIcon className="w-5 h-5" />
                                </Button>
                            </div>
                        </div>

                        {/* Filters */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {filters.map((filter) => (
                                <Select
                                    key={filter.key}
                                    label={filter.label}
                                    placeholder={filter.placeholder}
                                    selectedKeys={
                                        filterValues[filter.key]
                                            ? [filterValues[filter.key]]
                                            : []
                                    }
                                    onSelectionChange={(keys) => {
                                        const value = Array.from(
                                            keys,
                                        )[0] as string;

                                        setFilterValues((prev) => ({
                                            ...prev,
                                            [filter.key]:
                                                value === "all"
                                                    ? undefined
                                                    : value,
                                        }));
                                    }}
                                >
                                    {filter.options.map((option) => (
                                        <SelectItem key={option.value}>
                                            {option.label}
                                        </SelectItem>
                                    ))}
                                </Select>
                            ))}
                        </div>

                        {/* Active filters count */}
                        {Object.keys(filterValues).filter(
                            (key) =>
                                filterValues[key] &&
                                filterValues[key] !== "all",
                        ).length > 0 && (
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-500">
                                    {
                                        Object.keys(filterValues).filter(
                                            (key) =>
                                                filterValues[key] &&
                                                filterValues[key] !== "all",
                                        ).length
                                    }{" "}
                                    filtros activos
                                </span>
                                <Button
                                    size="sm"
                                    variant="flat"
                                    onPress={() => setFilterValues({})}
                                >
                                    Limpiar filtros
                                </Button>
                            </div>
                        )}
                    </div>
                </CardBody>
            </Card>

            {/* Notes Grid/List */}
            {isLoading ? (
                <div className="flex justify-center items-center h-64">
                    <Spinner label="Cargando notas..." size="lg" />
                </div>
            ) : paginatedData.length === 0 ? (
                <Card>
                    <CardBody className="text-center py-12">
                        <p className="text-gray-500">
                            No hay notas registradas
                        </p>
                    </CardBody>
                </Card>
            ) : (
                <div
                    className={
                        viewMode === "grid"
                            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                            : "space-y-4"
                    }
                >
                    {paginatedData.map((note) => (
                        <EnhancedNoteCardV2
                            key={note.id}
                            currentUserId={currentUserId}
                            note={note}
                            onDelete={handleDelete}
                            onEdit={handleEdit}
                            onLike={handleToggleLike}
                            onView={handleView}
                        />
                    ))}
                </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="flex justify-center gap-2">
                    <Button
                        isDisabled={page === 1}
                        size="sm"
                        variant="flat"
                        onPress={() => setPage(page - 1)}
                    >
                        Anterior
                    </Button>
                    <div className="flex items-center gap-2">
                        {Array.from(
                            { length: Math.min(5, totalPages) },
                            (_, i) => {
                                const pageNumber = i + 1;

                                return (
                                    <Button
                                        key={pageNumber}
                                        size="sm"
                                        variant={
                                            page === pageNumber
                                                ? "solid"
                                                : "flat"
                                        }
                                        onPress={() => setPage(pageNumber)}
                                    >
                                        {pageNumber}
                                    </Button>
                                );
                            },
                        )}
                    </div>
                    <Button
                        isDisabled={page === totalPages}
                        size="sm"
                        variant="flat"
                        onPress={() => setPage(page + 1)}
                    >
                        Siguiente
                    </Button>
                </div>
            )}

            {/* Recent Activity */}
            {notes.length > 0 && <RecentActivity />}
        </div>
    );
}
