// features/remissions/components/index.ts

// Documentos principales
export { default as AssignmentRemission } from "./AssignmentRemission";
export { RemissionDocument } from "./RemissionDocument";
export { RemissionDocumentCompact } from "./RemissionDocumentCompact";

// Componentes
export { RemissionHeader } from "./RemissionHeader";
export { RemissionFooter } from "./RemissionFooter";
export { RemissionMigration } from "./RemissionMigration";

// Componentes reutilizables
export { default as RemissionCard } from "./RemissionCard";
export { default as RemissionContractorInfo } from "./RemissionContractorInfo";
export { default as ContractorInfoCompact } from "./ContractorInfoCompact";
export { default as RemissionEmptyState } from "./RemissionEmptyState";
export { default as RemissionFilters } from "./RemissionFilters";
export { default as RemissionGrid } from "./RemissionGrid";
export { default as RemissionHistory } from "./RemissionHistory";
export { default as RemissionItemsTable } from "./RemissionItemsTable";
export { default as RemissionOrderItems } from "./RemissionOrderItems";
export { default as RemissionStatusBadge } from "./RemissionStatusBadge";
export { RemissionSuccessCard } from "./RemissionSuccessCard";
