/**
 * Cache Types and TTL Configuration
 * Based on creative phase data classification
 */

export enum CacheType {
    STATIC = "STATIC", // CDN-worthy, rarely changes
    USER_SPECIFIC = "USER_SPECIFIC", // Per-user data
    REAL_TIME = "REAL_TIME", // No caching
    COMPUTED = "COMPUTED", // Expensive calculations
}

// TTL configuration in seconds
export const CacheTTL = {
    [CacheType.STATIC]: 86400, // 24 hours
    [CacheType.USER_SPECIFIC]: 300, // 5 minutes
    [CacheType.REAL_TIME]: 0, // No cache
    [CacheType.COMPUTED]: 3600, // 1 hour
} as const;

// Cache key pattern: {tenant}:{resource}:{identifier}:{version}
export interface CacheKeyOptions {
    tenant?: string;
    resource: string;
    identifier: string;
    version?: string;
}

/**
 * Generate cache key following the pattern
 */
export function generateCacheKey(options: CacheKeyOptions): string {
    const parts = [
        options.tenant || "default",
        options.resource,
        options.identifier,
        options.version || "v1",
    ];

    return parts.join(":");
}

/**
 * Parse cache key to extract components
 */
export function parseCacheKey(key: string): CacheKeyOptions {
    const [tenant, resource, identifier, version] = key.split(":");

    return {
        tenant: tenant === "default" ? undefined : tenant,
        resource,
        identifier,
        version: version === "v1" ? undefined : version,
    };
}
