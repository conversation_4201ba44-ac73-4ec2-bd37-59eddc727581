import {
    describe,
    expect,
    test,
    jest,
    beforeEach,
    afterEach,
} from "@jest/globals";

import {
    staticCache,
    userSpecificCache,
    computedCache,
} from "../../cache/strategies";
import { RedisClient } from "../../cache/redis-client";

// Mock Redis client
jest.mock("../../cache/redis-client");

describe("Cache Strategies", () => {
    let mockRedis: jest.Mocked<RedisClient>;

    beforeEach(() => {
        mockRedis = {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            sadd: jest.fn(),
            smembers: jest.fn(),
            exists: jest.fn(),
            isConnected: jest.fn().mockReturnValue(true),
        } as any;

        // Replace the actual Redis client with our mock
        (RedisClient as any).getInstance = jest.fn().mockReturnValue(mockRedis);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Static Cache Strategy", () => {
        test("should cache static data with correct TTL", async () => {
            const key = "tenant1:categories:all:v1";
            const data = { categories: ["Electronics", "Books", "Clothing"] };

            await staticCache.set(key, data);

            expect(mockRedis.set).toHaveBeenCalledWith(
                key,
                JSON.stringify(data),
                "EX",
                expect.any(Number),
            );
            expect(mockRedis.sadd).toHaveBeenCalledWith(
                "cache:type:STATIC",
                key,
            );
        });

        test("should retrieve cached data", async () => {
            const key = "tenant1:categories:all:v1";
            const data = { categories: ["Electronics", "Books", "Clothing"] };

            mockRedis.get.mockResolvedValue(JSON.stringify(data));

            const result = await staticCache.get(key);

            expect(result).toEqual(data);
            expect(mockRedis.get).toHaveBeenCalledWith(key);
        });

        test("should warm cache for multiple keys", async () => {
            const entries = [
                {
                    key: "tenant1:products:featured:v1",
                    data: { products: [] },
                    ttl: 3600,
                },
                {
                    key: "tenant1:categories:all:v1",
                    data: { categories: [] },
                    ttl: 7200,
                },
            ];

            await staticCache.warmCache(entries);

            expect(mockRedis.set).toHaveBeenCalledTimes(2);
            entries.forEach((entry) => {
                expect(mockRedis.set).toHaveBeenCalledWith(
                    entry.key,
                    JSON.stringify(entry.data),
                    "EX",
                    entry.ttl,
                );
            });
        });
    });

    describe("User-Specific Cache Strategy", () => {
        test("should handle user-specific data caching", async () => {
            const userId = "user123";
            const key = "tenant1:cart:user123:v1";
            const data = { items: [], total: 0 };

            await userSpecificCache.setUserData(userId, "cart", data);

            expect(mockRedis.set).toHaveBeenCalledWith(
                expect.stringContaining(userId),
                JSON.stringify(data),
                "EX",
                expect.any(Number),
            );
        });

        test("should clear user session data", async () => {
            const userId = "user123";
            const keys = [
                "tenant1:cart:user123:v1",
                "tenant1:preferences:user123:v1",
            ];

            mockRedis.smembers.mockResolvedValue(keys);

            await userSpecificCache.clearUserSession(userId);

            expect(mockRedis.del).toHaveBeenCalledWith(...keys);
        });
    });

    describe("Computed Cache Strategy", () => {
        test("should execute computation when cache miss", async () => {
            const key = "tenant1:analytics:sales:v1";
            const computeFn = jest.fn().mockResolvedValue({ total: 1000 });

            mockRedis.get.mockResolvedValue(null);

            const result = await computedCache.getOrCompute(key, computeFn);

            expect(computeFn).toHaveBeenCalled();
            expect(result).toEqual({ total: 1000 });
            expect(mockRedis.set).toHaveBeenCalledWith(
                key,
                JSON.stringify({ total: 1000 }),
                "EX",
                expect.any(Number),
            );
        });

        test("should return cached value when available", async () => {
            const key = "tenant1:analytics:sales:v1";
            const cachedData = { total: 1000 };
            const computeFn = jest.fn();

            mockRedis.get.mockResolvedValue(JSON.stringify(cachedData));

            const result = await computedCache.getOrCompute(key, computeFn);

            expect(computeFn).not.toHaveBeenCalled();
            expect(result).toEqual(cachedData);
        });

        test("should invalidate by resource pattern", async () => {
            const resource = "analytics";
            const keys = [
                "tenant1:analytics:sales:v1",
                "tenant1:analytics:revenue:v1",
            ];

            mockRedis.smembers.mockResolvedValue(keys);

            await computedCache.invalidateByResource(resource);

            expect(mockRedis.del).toHaveBeenCalledWith(...keys);
        });
    });
});
