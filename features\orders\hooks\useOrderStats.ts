"use client";

import { useMemo, useCallback } from "react";

import { Order } from "@/features/orders/types/orders";
import {
    adjustTimezoneOffset,
    getDaysDifference,
} from "@/shared/utils/dateUtils";

interface ExtendedOrder extends Order {
    deliveryDate?: string | Date;
}

/**
 * Hook optimizado para calcular estadísticas de órdenes
 * Usa memoización pesada para evitar recálculos innecesarios
 */
export function useOrderStats(orders: Order[]) {
    // Cálculo de nuevas órdenes con memoización por días
    const getNewOrders = useCallback(
        (days: number) => {
            try {
                const cutoffDate = new Date();

                cutoffDate.setDate(cutoffDate.getDate() - days);

                return orders.filter(
                    (o) => o.createdAt && new Date(o.createdAt) > cutoffDate,
                ).length;
            } catch (error) {
                return 0;
            }
        },
        [orders],
    );

    // Cálculo por estado con memoización
    const ordersByStatus = useMemo(() => {
        const statusMap = new Map<string, number>();

        orders.forEach((order) => {
            const statusKey =
                typeof order.status === "string"
                    ? order.status
                    : (order.status as any)?.id || "unknown";
            const count = statusMap.get(statusKey) || 0;

            statusMap.set(statusKey, count + 1);
        });

        return statusMap;
    }, [orders]);

    const getByStatus = useCallback(
        (status: string) => {
            return ordersByStatus.get(status) || 0;
        },
        [ordersByStatus],
    );

    // Cálculo de órdenes retrasadas con memoización
    const delayedOrders = useMemo(() => {
        return orders.filter((order) => {
            const deliveryDate =
                order.estimatedDeliveryDate ||
                (order as ExtendedOrder).deliveryDate;

            if (
                !deliveryDate ||
                order.status === "delivered" ||
                order.status === "cancelled"
            ) {
                return false;
            }
            const date = adjustTimezoneOffset(deliveryDate);

            if (!date) return false;
            const daysLeft = getDaysDifference(date);

            return daysLeft < 0;
        }).length;
    }, [orders]);

    // Cálculo de tasa de cumplimiento con memoización
    const complianceRate = useMemo(() => {
        const deliveredOrders = orders.filter((o) => o.status === "delivered");

        if (deliveredOrders.length === 0) return 100;

        const onTimeDeliveries = deliveredOrders.filter((order) => {
            if (!order.deliveryDate) return false;

            // Aquí deberías comparar con la fecha real de entrega
            return true; // Placeholder
        }).length;

        return Math.round((onTimeDeliveries / deliveredOrders.length) * 100);
    }, [orders]);

    // Pre-calcular estadísticas comunes
    const statsCache = useMemo(
        () => ({
            total: orders.length,
            new7Days: getNewOrders(7),
            new30Days: getNewOrders(30),
            inProgress: getByStatus("in_progress"),
            inProduction: getByStatus("in_production"),
            delivered: getByStatus("delivered"),
            cancelled: getByStatus("cancelled"),
            pending: getByStatus("pending"),
            delayed: delayedOrders,
            complianceRate: complianceRate,
        }),
        [orders, getNewOrders, getByStatus, delayedOrders, complianceRate],
    );

    return {
        stats: statsCache,
        getNewOrders,
        getByStatus,
        getDelayedOrders: () => delayedOrders,
        getComplianceRate: () => complianceRate,
    };
}
