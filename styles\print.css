/* Estilos específicos para impresión */
@media print {
  /* Ocultar todo por defecto */
  body * {
    visibility: hidden;
  }
  
  /* Mostrar solo el contenido de la remisión */
  #remission-document,
  #remission-document * {
    visibility: visible;
  }
  
  /* También mostrar el contenedor del documento si existe */
  .remission-document,
  .remission-document * {
    visibility: visible;
  }
  
  /* Posicionar el contenido de impresión en toda la página */
  #remission-document,
  .remission-document {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 10mm !important;
    margin: 0 !important;
    background: white !important;
  }
  
  /* Ocultar elementos no imprimibles */
  .print-hidden,
  nav,
  button,
  [role="navigation"],
  .modal,
  .toast,
  .tooltip {
    display: none !important;
  }
  
  /* Asegurar que los colores se impriman bien */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Quitar sombras y bordes suaves para mejorar impresión */
  .shadow-sm,
  .shadow,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    box-shadow: none !important;
  }
  
  /* Asegurar que las tablas se impriman bien */
  table {
    border-collapse: collapse !important;
    width: 100% !important;
  }
  
  table td,
  table th {
    border: 1px solid #ddd !important;
    padding: 6px !important;
  }
  
  /* Colores de fondo para encabezados de tabla */
  thead {
    background-color: #f3f4f6 !important;
  }
  
  /* Encabezados oscuros para tablas compactas */
  .remission-document-compact thead {
    background-color: #1f2937 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Elementos con fondo oscuro en documento compacto */
  .remission-document-compact .bg-gray-800,
  .remission-document-compact .bg-gray-900 {
    background-color: #1f2937 !important;
    color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Alternar colores de filas */
  tbody tr:nth-child(even) {
    background-color: #f9fafb !important;
  }
  
  /* Ajustes para saltos de página */
  .print-page-break {
    page-break-after: always;
  }
  
  /* Estilos de fuente para impresión */
  body {
    font-size: 11pt !important;
    line-height: 1.4 !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif !important;
    color: #000 !important;
    background: white !important;
  }
  
  /* Estilos específicos para el documento compacto */
  .remission-document-compact {
    font-size: 9pt !important;
    line-height: 1.2 !important;
  }
  
  .remission-document-compact h1 {
    font-size: 14pt !important;
    color: #000 !important;
    margin-bottom: 4px !important;
    letter-spacing: 0.05em !important;
  }
  
  .remission-document-compact h2 {
    font-size: 10pt !important;
    color: #374151 !important;
    margin-bottom: 4px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
  }
  
  /* Estilos generales para títulos */
  h1 {
    font-size: 18pt !important;
    color: #000 !important;
    margin-bottom: 10px !important;
  }
  
  h2 {
    font-size: 14pt !important;
    color: #1d4ed8 !important;
    margin-bottom: 8px !important;
  }
  
  h3 {
    font-size: 12pt !important;
    color: #000 !important;
    margin-bottom: 6px !important;
  }
  
  /* Mejorar impresión de logos */
  img {
    max-width: 100% !important;
    height: auto !important;
  }
  
  /* Asegurar que los bordes redondeados se vean bien */
  .rounded,
  .rounded-md,
  .rounded-lg {
    border-radius: 4px !important;
  }
  
  /* Mejorar visibilidad de bordes */
  .border {
    border: 1px solid #e5e7eb !important;
  }
  
  /* Configuración de página para landscape */
  @page {
    size: A4 landscape;
    margin: 10mm;
  }
  
  /* Para documentos en portrait */
  .portrait-mode {
    @page {
      size: A4 portrait;
    }
  }
} 