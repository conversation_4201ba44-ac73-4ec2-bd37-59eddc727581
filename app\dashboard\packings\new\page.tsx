import { Suspense } from "react";

import { Spinner } from "@/shared/components/ui/hero-ui-client";
import { DashboardHeader } from "@/shared/components/dashboard";

import { PackingNewWizardV2 } from "./PackingNewWizardV2";

export default function PackingNewPage() {
    return (
        <div className="space-y-6">
            <DashboardHeader
                subtitle="Crear un nuevo documento de empaque basado en órdenes existentes"
                title="Nuevo Packing"
            />

            <Suspense
                fallback={
                    <div className="flex items-center justify-center min-h-[50vh]">
                        <Spinner label="Cargando formulario..." size="lg" />
                    </div>
                }
            >
                <PackingNewWizardV2 />
            </Suspense>
        </div>
    );
}
