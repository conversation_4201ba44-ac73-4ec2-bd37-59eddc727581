"use client";

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Capitaliza la primera letra de una cadena
 */
export const capitalized = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
};

/**
 * Combina múltiples clases de Tailwind y las fusiona de manera eficiente
 * utilizando clsx y tailwind-merge para resolver conflictos
 */
export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}
