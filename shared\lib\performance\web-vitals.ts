/**
 * Web Vitals Performance Monitoring
 * Tracks Core Web Vitals and custom performance metrics
 */

import { onCLS, onFCP, onLCP, onTTFB, onINP, Metric } from "web-vitals"; // onFID removed - deprecated in favor of onINP

/**
 * Performance thresholds based on optimization goals
 */
export const performanceThresholds = {
    LCP: 2500, // Largest Contentful Paint: 2.5s (target < 2s for 40% improvement)
    FID: 100, // First Input Delay: 100ms
    CLS: 0.1, // Cumulative Layout Shift: 0.1
    FCP: 1800, // First Contentful Paint: 1.8s
    TTFB: 800, // Time to First Byte: 800ms
    INP: 200, // Interaction to Next Paint: 200ms
};

/**
 * Metric status based on thresholds
 */
export type MetricStatus = "good" | "needs-improvement" | "poor";

/**
 * Get status for a metric value
 */
function getMetricStatus(metricName: string, value: number): MetricStatus {
    const threshold =
        performanceThresholds[metricName as keyof typeof performanceThresholds];

    if (!threshold) return "good";

    if (value <= threshold * 0.75) return "good";
    if (value <= threshold) return "needs-improvement";

    return "poor";
}

/**
 * Analytics reporter interface
 */
export interface AnalyticsReporter {
    reportWebVitals(metric: Metric & { status: MetricStatus }): void;
    reportCustomMetric(
        name: string,
        value: number,
        status?: MetricStatus,
    ): void;
}

/**
 * Console reporter for development
 */
export const consoleReporter: AnalyticsReporter = {
    reportWebVitals(metric) {
        const color =
            metric.status === "good"
                ? "🟢"
                : metric.status === "needs-improvement"
                  ? "🟡"
                  : "🔴";

        console.log(`${color} ${metric.name}:`, {
            value: Math.round(metric.value),
            status: metric.status,
            rating: metric.rating,
        });
    },

    reportCustomMetric(name, value, status) {
        console.log(`📊 ${name}:`, {
            value: Math.round(value),
            status: status || "custom",
        });
    },
};

/**
 * Initialize Web Vitals monitoring
 */
export function initWebVitals(reporter: AnalyticsReporter = consoleReporter) {
    // Core Web Vitals
    onCLS((metric) => {
        reporter.reportWebVitals({
            ...metric,
            status: getMetricStatus("CLS", metric.value),
        });
    });

    // onFID((metric) => {
    //     reporter.reportWebVitals({
    //         ...metric,
    //         status: getMetricStatus("FID", metric.value),
    //     });
    // }); // Commented out - FID deprecated in favor of INP

    onLCP((metric) => {
        reporter.reportWebVitals({
            ...metric,
            status: getMetricStatus("LCP", metric.value),
        });
    });

    // Additional metrics
    onFCP((metric) => {
        reporter.reportWebVitals({
            ...metric,
            status: getMetricStatus("FCP", metric.value),
        });
    });

    onTTFB((metric) => {
        reporter.reportWebVitals({
            ...metric,
            status: getMetricStatus("TTFB", metric.value),
        });
    });

    onINP((metric) => {
        reporter.reportWebVitals({
            ...metric,
            status: getMetricStatus("INP", metric.value),
        });
    });
}

/**
 * Custom performance marks for application-specific metrics
 */
export class PerformanceTracker {
    private marks = new Map<string, number>();

    constructor(private reporter: AnalyticsReporter) {}

    mark(name: string) {
        this.marks.set(name, performance.now());
    }

    measure(name: string, startMark: string, endMark?: string) {
        const start = this.marks.get(startMark);

        if (!start) {
            console.warn(`Start mark "${startMark}" not found`);

            return;
        }

        const end = endMark ? this.marks.get(endMark) : performance.now();

        if (!end) {
            console.warn(`End mark "${endMark}" not found`);

            return;
        }

        const duration = end - start;

        this.reporter.reportCustomMetric(name, duration);

        return duration;
    }

    clear() {
        this.marks.clear();
    }
}

/**
 * Global performance tracker instance
 */
export const performanceTracker = new PerformanceTracker(consoleReporter);
