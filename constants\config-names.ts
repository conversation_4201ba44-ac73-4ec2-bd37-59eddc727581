/**
 * Nombres canónicos en español para todas las configuraciones
 * Estos nombres deben coincidir exactamente con los de la BD
 */

export const CONFIG_NAMES = {
    orderStatus: {
        NEW: "Nuevo",
        RECEIVED: "Recibido",
        IN_PRODUCTION: "En producción",
        QUALITY_CHECK: "Control de calidad",
        PACKING: "Empaquetando",
        READY_TO_DELIVER: "Listo para entregar",
        DELIVERED: "Entregado",
        REJECTED: "Re<PERSON>zado",
        CANCELLED: "Cancelado",
    },
    noteStatus: {
        PENDING: "Pendiente",
        IN_PROGRESS: "En progreso",
        COMPLETED: "Completado",
        CANCELLED: "Cancelado",
    },
    noteImportance: {
        LOW: "Bajo",
        MEDIUM: "Medio",
        HIGH: "Alto",
    },
    packingStatus: {
        IN_PROGRESS: "En progreso",
        DELIVERED: "Entregado",
        REJECTED: "Rechazado",
        CANCELLED: "Cancelado",
    },
    rejectionReason: {
        QUALITY: "Calidad",
        DEFECT: "Defecto",
        DELAY: "Retraso",
        OTHER: "Otro",
    },
    role: {
        ADMIN: "Administrador",
        EMPLOYEE: "Empleado",
        CONTRACTOR: "Contratista",
        GUEST: "Invitado",
    },
} as const;

// Tipos derivados para type safety
export type OrderStatusName =
    (typeof CONFIG_NAMES.orderStatus)[keyof typeof CONFIG_NAMES.orderStatus];
export type NoteStatusName =
    (typeof CONFIG_NAMES.noteStatus)[keyof typeof CONFIG_NAMES.noteStatus];
export type NoteImportanceName =
    (typeof CONFIG_NAMES.noteImportance)[keyof typeof CONFIG_NAMES.noteImportance];
export type PackingStatusName =
    (typeof CONFIG_NAMES.packingStatus)[keyof typeof CONFIG_NAMES.packingStatus];
export type RejectionReasonName =
    (typeof CONFIG_NAMES.rejectionReason)[keyof typeof CONFIG_NAMES.rejectionReason];
export type RoleName =
    (typeof CONFIG_NAMES.role)[keyof typeof CONFIG_NAMES.role];
