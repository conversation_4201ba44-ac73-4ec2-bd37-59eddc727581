import { NextResponse } from "next/server";

import { db } from "@/lib/db";

export async function GET() {
    try {
        // 1. Contar total de órdenes
        const totalOrders = await db.order.count();

        // 2. Obtener órdenes con sus detalles
        const orders = await db.order.findMany({
            take: 5,
            include: {
                customer: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
                status: true,
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        const ordersAnalysis = orders.map((order) => {
            let totalQuantity = 0;
            let totalUsed = 0;
            let totalAvailable = 0;

            const garments = order.garments.map((garment) => {
                const sizes = garment.sizes.map((size) => {
                    const available = size.totalQuantity - size.usedQuantity;

                    totalQuantity += size.totalQuantity;
                    totalUsed += size.usedQuantity;
                    totalAvailable += available;

                    return {
                        code: size.size.code,
                        totalQuantity: size.totalQuantity,
                        usedQuantity: size.usedQuantity,
                        available,
                        hasAvailability: available > 0,
                    };
                });

                return {
                    model: garment.model.code,
                    color: garment.color.name,
                    sizes,
                };
            });

            return {
                id: order.id,
                displayName: order.cutOrder || order.transferNumber || order.id,
                customer: order.customer.name,
                customerId: order.customerId,
                status: order.status?.name || "Sin estado",
                createdAt: order.createdAt,
                garments,
                summary: {
                    totalQuantity,
                    totalUsed,
                    totalAvailable,
                    hasAvailability: totalAvailable > 0,
                },
            };
        });

        // 3. Contar órdenes con disponibilidad
        const ordersWithAvailability = await db.order.count({
            where: {
                garments: {
                    some: {
                        sizes: {
                            some: {
                                totalQuantity: {
                                    gt: db.garmentSize.fields.usedQuantity,
                                },
                            },
                        },
                    },
                },
            },
        });

        // 4. Verificar assignments
        const totalAssignments = await db.assignment.count();
        const recentAssignments = await db.assignment.findMany({
            take: 5,
            include: {
                garmentSize: {
                    include: {
                        size: true,
                        garment: {
                            include: {
                                model: true,
                                color: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return NextResponse.json({
            summary: {
                totalOrders,
                ordersWithAvailability,
                totalAssignments,
            },
            orders: ordersAnalysis,
            recentAssignments: recentAssignments.map((a) => ({
                model: a.garmentSize.garment.model.code,
                color: a.garmentSize.garment.color.name,
                size: a.garmentSize.size.code,
                quantity: a.quantity,
                createdAt: a.createdAt,
            })),
        });
    } catch {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            { error: "Error checking orders" },
            { status: 500 },
        );
    }
}
