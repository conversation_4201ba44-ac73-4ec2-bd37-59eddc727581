"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
    Input,
    Button,
    Textarea,
    Card,
    CardBody,
    addToast,
} from "@heroui/react";
import {
    DocumentTextIcon,
    ChevronLeftIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    useCreateModel,
    useValidateModelCode,
} from "@/features/models/hooks/useModel";
import {
    ModelFormProgress,
    calculateModelProgress,
    ModelFormHeader,
    ModelInfoMessage,
    modelFormMessages,
} from "@/features/models/components/forms";
import { createModelSchema } from "@/features/models/schemas";

type ModelFormData = z.infer<typeof createModelSchema>;

// Animaciones simples
const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.3 },
};

export default function NewModelPage() {
    const router = useRouter();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const { createModel } = useCreateModel();

    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
        setError,
        clearErrors,
    } = useForm<ModelFormData>({
        resolver: zodResolver(createModelSchema),
        mode: "onChange",
        defaultValues: {
            code: "",
            description: "",
            basePrice: 0,
        },
    });

    // Observar todos los campos para el progreso
    const watchedFields = watch();
    const formProgress = calculateModelProgress(watchedFields);

    // Validar código duplicado
    const code = watch("code");
    const { isValid: isCodeValid, isValidating } = useValidateModelCode(code);

    useEffect(() => {
        if (code && code.length >= 2) {
            if (!isCodeValid && !isValidating) {
                setError("code", {
                    type: "manual",
                    message: "Ya existe un modelo con este código",
                });
            } else if (isCodeValid) {
                if (errors.code?.type === "manual") {
                    clearErrors("code");
                }
            }
        }
    }, [code, isCodeValid, isValidating, setError, clearErrors, errors.code]);

    const onSubmit = async (data: ModelFormData) => {
        setIsSubmitting(true);
        try {
            const result = await createModel(data);

            if (!result || !result.success) {
                if (
                    result?.error?.includes("ya existe") ||
                    result?.error?.includes("duplicado")
                ) {
                    setError("code", {
                        type: "manual",
                        message: "Ya existe un modelo con este código",
                    });
                } else {
                    throw new Error(
                        result?.error || "Error al crear el modelo",
                    );
                }
                setIsSubmitting(false);

                return;
            }

            setIsSuccess(true);

            // Redirigir después de 2 segundos
            setTimeout(() => {
                router.push("/dashboard/models");
            }, 2000);
        } catch (error) {
            setIsSubmitting(false);
            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "No se pudo crear el modelo",
                color: "danger",
            });
        }
    };

    // Validar formulario
    const isFormValid =
        watchedFields.code &&
        watchedFields.code.trim().length >= 2 &&
        watchedFields.description &&
        watchedFields.description.trim().length >= 5 &&
        !errors.code &&
        !errors.description &&
        !errors.basePrice &&
        isCodeValid;

    return (
        <DashboardLayout
            breadcrumbs={[
                { label: "Modelos", href: "/dashboard/models" },
                { label: "Nuevo" },
            ]}
            subtitle="Registra un nuevo modelo de prenda en el sistema"
            title="Nuevo Modelo"
        >
            <motion.div {...fadeIn} className="max-w-4xl mx-auto">
                <Card className="shadow-sm border border-gray-200 dark:border-gray-700">
                    <CardBody className="p-6">
                        <AnimatePresence mode="wait">
                            {isSuccess ? (
                                <div className="text-center py-16">
                                    <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full mb-6">
                                        <CheckCircleIcon className="w-10 h-10 text-green-600 dark:text-green-400" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                        ¡Modelo creado exitosamente!
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-400">
                                        Redirigiendo al listado de modelos...
                                    </p>
                                </div>
                            ) : (
                                <div className="space-y-8">
                                    {/* Header */}
                                    <ModelFormHeader mode="create" />

                                    {/* Progress */}
                                    <ModelFormProgress
                                        className="mb-6"
                                        value={formProgress}
                                    />

                                    {/* Form */}
                                    <form
                                        className="space-y-8"
                                        onSubmit={handleSubmit(onSubmit)}
                                    >
                                        {/* Sección: Información del Modelo */}
                                        <section>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <DocumentTextIcon className="w-5 h-5 text-purple-600" />
                                                Información del Modelo
                                            </h3>

                                            <div className="space-y-4">
                                                <Input
                                                    {...register("code")}
                                                    isRequired
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                        input: "uppercase",
                                                    }}
                                                    description="Código único para identificar el modelo"
                                                    errorMessage={
                                                        errors.code?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={!!errors.code}
                                                    label="Código del Modelo"
                                                    placeholder="Ej: MDL-001"
                                                    startContent={
                                                        <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    variant="bordered"
                                                />

                                                <Textarea
                                                    {...register("description")}
                                                    isRequired
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    description={`${watchedFields.description?.length || 0}/100 caracteres`}
                                                    errorMessage={
                                                        errors.description
                                                            ?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={
                                                        !!errors.description
                                                    }
                                                    label="Descripción"
                                                    maxRows={6}
                                                    minRows={3}
                                                    placeholder="Describe este modelo de prenda..."
                                                    startContent={
                                                        <div className="pt-1">
                                                            <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                                                        </div>
                                                    }
                                                    variant="bordered"
                                                />

                                                <Input
                                                    {...register("basePrice", {
                                                        valueAsNumber: true,
                                                    })}
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    description="Precio mínimo de confección para este modelo"
                                                    endContent={
                                                        <span className="text-default-400 text-small">
                                                            MXN
                                                        </span>
                                                    }
                                                    errorMessage={
                                                        errors.basePrice
                                                            ?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={
                                                        !!errors.basePrice
                                                    }
                                                    label="Precio Base"
                                                    min="0"
                                                    placeholder="0.00"
                                                    startContent={
                                                        <span className="text-gray-400">
                                                            $
                                                        </span>
                                                    }
                                                    step="0.01"
                                                    type="number"
                                                    variant="bordered"
                                                />
                                            </div>

                                            {/* Mensajes informativos */}
                                            <div className="mt-4 space-y-2">
                                                <ModelInfoMessage
                                                    {...modelFormMessages.codeFormat}
                                                />
                                                <ModelInfoMessage
                                                    {...modelFormMessages.basePriceInfo}
                                                />
                                                {errors.code?.type ===
                                                    "manual" && (
                                                    <ModelInfoMessage
                                                        {...modelFormMessages.duplicateCode}
                                                        className="mt-2"
                                                    />
                                                )}
                                            </div>
                                        </section>

                                        {/* Mensaje de formulario completo */}
                                        {isFormValid && (
                                            <ModelInfoMessage
                                                {...modelFormMessages.formComplete}
                                            />
                                        )}

                                        {/* Botones */}
                                        <div className="flex gap-3 pt-6">
                                            <Button
                                                isDisabled={isSubmitting}
                                                size="lg"
                                                startContent={
                                                    <ChevronLeftIcon className="w-4 h-4" />
                                                }
                                                type="button"
                                                variant="flat"
                                                onPress={() =>
                                                    router.push(
                                                        "/dashboard/models",
                                                    )
                                                }
                                            >
                                                Cancelar
                                            </Button>

                                            <Button
                                                className="flex-1"
                                                color="primary"
                                                isDisabled={
                                                    !isFormValid ||
                                                    isSubmitting ||
                                                    isValidating
                                                }
                                                isLoading={isSubmitting}
                                                size="lg"
                                                type="submit"
                                            >
                                                {isSubmitting
                                                    ? "Creando..."
                                                    : "Crear Modelo"}
                                            </Button>
                                        </div>
                                    </form>
                                </div>
                            )}
                        </AnimatePresence>
                    </CardBody>
                </Card>
            </motion.div>
        </DashboardLayout>
    );
}
