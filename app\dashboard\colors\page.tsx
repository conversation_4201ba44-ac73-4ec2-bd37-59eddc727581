import { Suspense } from "react";

import { Spinner } from "@/shared/components/ui/hero-ui-client";

import UnifiedColorsPage from "./UnifiedClientPage";

/**
 * Página de colors con el sistema de diseño unificado
 */
export default function ColorsPage() {
    return (
        <Suspense
            fallback={
                <div className="flex items-center justify-center min-h-[50vh]">
                    <Spinner label="Cargando colores..." size="lg" />
                </div>
            }
        >
            <UnifiedColorsPage />
        </Suspense>
    );
}
