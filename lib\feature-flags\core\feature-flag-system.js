/**
 * Feature Flag System Core - JavaScript version for Node scripts
 * This is a transpiled version for use in migration scripts
 */

const Redis = require('ioredis');
const { EventEmitter } = require('events');

class FeatureFlagSystem extends EventEmitter {
  static instance = null;

  constructor() {
    super();
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
    this.cache = new Map();
    this.updateInterval = null;
  }

  static getInstance() {
    if (!FeatureFlagSystem.instance) {
      FeatureFlagSystem.instance = new FeatureFlagSystem();
    }
    return FeatureFlagSystem.instance;
  }

  async initialize() {
    try {
      // Test Redis connection
      await this.redis.ping();
      
      // Load all feature flags
      await this.loadFlags();
      
      // Set up periodic cache refresh (every 30 seconds)
      this.updateInterval = setInterval(() => {
        this.loadFlags().catch(err => {
          this.emit('error', err);
        });
      }, 30000);
      
      this.emit('initialized');
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async loadFlags() {
    try {
      const keys = await this.redis.keys('feature_flag:*');
      
      for (const key of keys) {
        const flagData = await this.redis.get(key);
        if (flagData) {
          const flag = JSON.parse(flagData);
          this.cache.set(flag.key, flag);
        }
      }
      
      this.emit('flags-loaded', this.cache.size);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  async getFlag(key) {
    // Check cache first
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }
    
    // Fallback to Redis
    const flagData = await this.redis.get(`feature_flag:${key}`);
    if (flagData) {
      const flag = JSON.parse(flagData);
      this.cache.set(key, flag);
      return flag;
    }
    
    return null;
  }

  async setFlag(flag) {
    flag.metadata = {
      ...flag.metadata,
      modified: new Date()
    };
    
    await this.redis.set(`feature_flag:${flag.key}`, JSON.stringify(flag));
    this.cache.set(flag.key, flag);
    
    this.emit('flag-updated', flag);
  }

  async isEnabled(key, context) {
    const flag = await this.getFlag(key);
    
    if (!flag) {
      return false;
    }
    
    // Check if globally disabled
    if (!flag.enabled) {
      return false;
    }
    
    // Check user-specific enablement
    if (context?.userId && flag.enabledForUsers?.includes(context.userId)) {
      return true;
    }
    
    // Check role-specific enablement
    if (context?.userRole && flag.enabledForRoles?.includes(context.userRole)) {
      return true;
    }
    
    // Check rollout percentage
    if (flag.rolloutPercentage > 0 && flag.rolloutPercentage < 100) {
      const hash = this.hashString(context?.sessionId || context?.userId || '');
      const percentage = (hash % 100) + 1;
      return percentage <= flag.rolloutPercentage;
    }
    
    // If rollout is 100%, flag is enabled for all
    return flag.rolloutPercentage === 100;
  }

  async toggleFlag(key, enabled) {
    const flag = await this.getFlag(key);
    
    if (!flag) {
      throw new Error(`Feature flag ${key} not found`);
    }
    
    flag.enabled = enabled;
    await this.setFlag(flag);
  }

  async updateRolloutPercentage(key, percentage) {
    if (percentage < 0 || percentage > 100) {
      throw new Error('Rollout percentage must be between 0 and 100');
    }
    
    const flag = await this.getFlag(key);
    
    if (!flag) {
      throw new Error(`Feature flag ${key} not found`);
    }
    
    flag.rolloutPercentage = percentage;
    await this.setFlag(flag);
  }

  async getAllFlags() {
    return Array.from(this.cache.values());
  }

  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }

  async checkDependencies(key) {
    const flag = await this.getFlag(key);
    
    if (!flag || !flag.metadata?.dependencies) {
      return true;
    }
    
    for (const dep of flag.metadata.dependencies) {
      const depFlag = await this.getFlag(dep);
      if (!depFlag || !depFlag.enabled) {
        return false;
      }
    }
    
    return true;
  }

  destroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    this.redis.disconnect();
    this.removeAllListeners();
  }
}

// Helper function for use in other modules
function getFeatureFlagSystem() {
  return FeatureFlagSystem.getInstance();
}

module.exports = {
  FeatureFlagSystem,
  getFeatureFlagSystem
};