"use server";

import type {
    CreateRemissionData,
    RemissionFilters,
    OrderDetails,
} from "../types";

import { revalidatePath } from "next/cache";
import { Prisma } from "@prisma/client";

import { prisma } from "@/shared/lib/prisma";

import { generateSequentialNumber } from "./utils";

const COMPANY_PREFIX = "LOH";

/**
 * Generates a new folio with database persistence
 */
export async function generateFolio(): Promise<string> {
    // Use a transaction with a lock strategy to prevent race conditions
    return await prisma.$transaction(
        async (tx) => {
            // Get current date
            const now = new Date();

            // Get current date string for comparison
            const currentDateString = generateSequentialNumber(now).dateString;

            // Get date in MMDD format for the simplified folio
            const month = (now.getMonth() + 1).toString().padStart(2, "0");
            const day = now.getDate().toString().padStart(2, "0");
            const simpleDateString = `${month}${day}`;

            // Get or create sequence record with FOR UPDATE lock to prevent concurrent modifications
            const sequenceRecord = await tx.$queryRaw`
      SELECT * FROM "FolioSequence" 
      WHERE id = 'single' 
      FOR UPDATE
    `;

            // Check if we have a record or need to create one
            let lastSequence = 0;
            let lastDate = "";

            if (Array.isArray(sequenceRecord) && sequenceRecord.length > 0) {
                lastSequence = sequenceRecord[0].lastSequence;
                lastDate = sequenceRecord[0].lastDate;
            } else {
                // Create a new record if it doesn't exist
                await tx.folioSequence.create({
                    data: {
                        id: "single",
                        lastDate: currentDateString,
                        lastSequence: 0,
                    },
                });
                lastDate = currentDateString;
            }

            // Generate sequential number - reset sequence if it's a new date
            const { dateString, sequence } = generateSequentialNumber(
                now,
                lastDate === currentDateString ? lastSequence : 0,
            );

            // Update sequence record
            await tx.folioSequence.update({
                where: { id: "single" },
                data: {
                    lastDate: dateString,
                    lastSequence: sequence,
                },
            });

            // Format sequence as XXX (3 dígitos para formato simplificado)
            const sequenceStr = sequence.toString().padStart(3, "0");

            // Generate folio simplificado
            const fullFolio = `${COMPANY_PREFIX}-${simpleDateString}-${sequenceStr}`;

            // Add a delay of 100ms to reduce load on database during high concurrency
            await new Promise((resolve) => setTimeout(resolve, 100));

            return fullFolio;
        },
        {
            maxWait: 5000, // Maximum time to wait for a lock
            timeout: 10000, // Maximum transaction time
        },
    );
}

/**
 * Creates a new remission from assignment data and loads OrderParts
 */
export async function createRemission(data: CreateRemissionData) {
    // First, ensure we have the order parts if they are not provided
    let orderDetails: OrderDetails = data.orderDetails;

    // Check if we need to fetch order details (cutOrder and parts)
    if (orderDetails.id && (!orderDetails.cutOrder || !orderDetails.parts)) {
        try {
            // Get complete order information from the database
            const order = await prisma.order.findUnique({
                where: { id: orderDetails.id },
                select: {
                    id: true,
                    cutOrder: true,
                    receivedDate: true,
                    parts: {
                        select: { id: true, code: true },
                    },
                },
            });

            if (order) {
                // Update orderDetails with fetched data
                orderDetails = {
                    ...orderDetails,
                    cutOrder:
                        orderDetails.cutOrder || order.cutOrder || undefined,
                    creationDate: order.receivedDate,
                    parts: orderDetails.parts || order.parts,
                };
            }
        } catch (error) {
            // REMOVED: console.error("Error fetching order details:", error);
            // Continue without complete order details if there's an error
        }
    }

    // Generate the folio and create the remission
    const folio = await generateFolio();

    // Create remission with items and history entry in a transaction
    const remission = await prisma.$transaction(async (tx) => {
        // Create the remission
        const remission = await tx.remission.create({
            data: {
                folio,
                contractorId: data.contractorId,
                notes: data.notes,
                orderDetails: orderDetails as unknown as Prisma.InputJsonValue,
                status: "ACTIVE",
            },
        });

        // Create RemissionAssignment records for each assignment
        if (data.assignmentIds && data.assignmentIds.length > 0) {
            await tx.remissionAssignment.createMany({
                data: data.assignmentIds.map((assignmentId) => ({
                    remissionId: remission.id,
                    assignmentId,
                })),
            });
        }

        // Create remission items
        if (data.items && data.items.length > 0) {
            await tx.remissionItem.createMany({
                data: data.items.map((item) => ({
                    remissionId: remission.id,
                    modelCode: item.modelCode,
                    colorName: item.colorName,
                    sizeCode: item.sizeCode,
                    quantity: item.quantity,
                })),
            });
        }

        // Create history entry
        await tx.remissionHistory.create({
            data: {
                remissionId: remission.id,
                action: "CREATED",
                metadata: {
                    createdAt: new Date().toISOString(),
                },
            },
        });

        return remission;
    });

    revalidatePath("/dashboard/assignments");

    return remission;
}

/**
 * Gets a remission by ID with related data
 */
export async function getRemission(id: string) {
    return await prisma.remission.findUnique({
        where: { id },
        include: {
            contractor: {
                select: {
                    id: true,
                    name: true,
                    firstName: true,
                    lastName: true,
                    middleName: true,
                    secondLastName: true,
                    email: true,
                    phone: true,
                    notes: true,
                },
            },
            assignments: true,
            remissionItems: true,
            history: {
                orderBy: {
                    timestamp: "desc",
                },
            },
        },
    });
}

/**
 * Gets a remission by folio
 */
export async function getRemissionByFolio(folio: string) {
    return await prisma.remission.findUnique({
        where: { folio },
        include: {
            contractor: {
                select: {
                    id: true,
                    name: true,
                    firstName: true,
                    lastName: true,
                    middleName: true,
                    secondLastName: true,
                    email: true,
                    phone: true,
                    notes: true,
                },
            },
            assignments: true,
            remissionItems: true,
            history: {
                orderBy: {
                    timestamp: "desc",
                },
            },
        },
    });
}

/**
 * Lists remissions with optional filtering
 */
export async function listRemissions(filters: RemissionFilters = {}) {
    const where: any = {};

    if (filters.contractorId) {
        where.contractorId = filters.contractorId;
    }

    if (filters.status) {
        where.status = filters.status;
    }

    if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
            where.createdAt.gte = filters.startDate;
        }
        if (filters.endDate) {
            where.createdAt.lte = filters.endDate;
        }
    }

    return await prisma.remission.findMany({
        where,
        include: {
            contractor: {
                select: {
                    id: true,
                    name: true,
                    firstName: true,
                    lastName: true,
                    middleName: true,
                    secondLastName: true,
                    email: true,
                    phone: true,
                    notes: true,
                },
            },
            assignments: true,
            remissionItems: true,
        },
        orderBy: {
            createdAt: "desc",
        },
    });
}

/**
 * Updates remission status
 */
export async function updateRemissionStatus(id: string, status: string) {
    const remission = await prisma.$transaction(async (tx) => {
        // Update the remission
        const updated = await tx.remission.update({
            where: { id },
            data: { status },
        });

        // Add history entry
        await tx.remissionHistory.create({
            data: {
                remissionId: id,
                action: `STATUS_CHANGED_TO_${status}`,
                metadata: {
                    updatedAt: new Date().toISOString(),
                    previousStatus: updated.status,
                },
            },
        });

        return updated;
    });

    revalidatePath("/dashboard/assignments");

    return remission;
}

/**
 * Marks a remission as printed
 */
export async function markRemissionPrinted(id: string) {
    const remission = await prisma.$transaction(async (tx) => {
        // Update the remission
        const updated = await tx.remission.update({
            where: { id },
            data: {
                printedAt: new Date(),
            },
        });

        // Add history entry
        await tx.remissionHistory.create({
            data: {
                remissionId: id,
                action: "PRINTED",
                metadata: {
                    printedAt: updated.printedAt?.toISOString(),
                },
            },
        });

        return updated;
    });

    revalidatePath("/dashboard/assignments");

    return remission;
}

/**
 * Gets remissions with enhanced filtering, sorting and pagination
 */

export async function getRemissions(
    params: {
        search?: string;
        status?: string;
        contractorId?: string;
        dateFrom?: string;
        dateTo?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) {
    try {
        const {
            search,
            status,
            contractorId,
            dateFrom,
            dateTo,
            orderBy = "createdAt",
            order = "desc",
            page = 1,
            perPage = 12,
        } = params;

        // Build where clause
        const where: any = {};

        if (search) {
            where.OR = [
                { folio: { contains: search, mode: "insensitive" } },
                {
                    contractor: {
                        name: { contains: search, mode: "insensitive" },
                    },
                },
                { notes: { contains: search, mode: "insensitive" } },
            ];
        }

        if (status) {
            where.status = status;
        }

        if (contractorId) {
            where.contractorId = contractorId;
        }

        if (dateFrom || dateTo) {
            where.createdAt = {};
            if (dateFrom) {
                where.createdAt.gte = new Date(dateFrom);
            }
            if (dateTo) {
                where.createdAt.lte = new Date(dateTo);
            }
        }

        // Get total count
        const total = await prisma.remission.count({ where });

        // Get remissions with pagination
        const remissions = await prisma.remission.findMany({
            where,
            include: {
                contractor: {
                    select: {
                        id: true,
                        name: true,
                        firstName: true,
                        lastName: true,
                        middleName: true,
                        secondLastName: true,
                        email: true,
                        phone: true,
                    },
                },
                assignments: {
                    include: {
                        assignment: {
                            include: {
                                order: {
                                    include: {
                                        customer: true,
                                    },
                                },
                            },
                        },
                    },
                },
                remissionItems: true,
                _count: {
                    select: {
                        remissionItems: true,
                    },
                },
            },
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
        });

        return {
            success: true,
            data: remissions,
            pagination: {
                total,
                currentPage: page,
                lastPage: Math.ceil(total / perPage),
                perPage,
            },
        };
    } catch (error) {
        console.error("Error fetching remissions:", error);

        return {
            success: false,
            error: "Error al obtener las remisiones",
        };
    }
}
