"use client";

import { useState, useEffect, useCallback, useRef } from "react";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";

import { useVisualFeedback } from "../components/wizard/hooks/useVisualFeedback";

const DRAFT_STORAGE_KEY = "lohari_assignment_draft";
const AUTO_SAVE_INTERVAL = 30000; // 30 segundos
const DRAFT_EXPIRY_HOURS = 24; // 24 horas

interface DraftData {
    state: any;
    timestamp: number;
    expiresAt: number;
    version: string;
}

interface UseDraftPersistenceReturn {
    hasDraft: boolean;
    draftAge: string;
    saveDraft: () => void;
    loadDraft: () => void;
    clearDraft: () => void;
    isAutoSaving: boolean;
    lastSavedAt: Date | null;
}

export function useDraftPersistence(): UseDraftPersistenceReturn {
    const { state, dispatch } = useWizard();
    const { showFeedback } = useVisualFeedback();
    const [hasDraft, setHasDraft] = useState(false);
    const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);
    const [isAutoSaving, setIsAutoSaving] = useState(false);
    const autoSaveTimerRef = useRef<NodeJS.Timeout>();

    // Verificar si hay un borrador guardado
    const checkForDraft = useCallback(() => {
        try {
            const stored = localStorage.getItem(DRAFT_STORAGE_KEY);

            if (!stored) {
                setHasDraft(false);

                return;
            }

            const draft: DraftData = JSON.parse(stored);
            const now = Date.now();

            // Verificar si el borrador ha expirado
            if (draft.expiresAt < now) {
                localStorage.removeItem(DRAFT_STORAGE_KEY);
                setHasDraft(false);

                return;
            }

            setHasDraft(true);
        } catch (error) {
            console.error("Error checking draft:", error);
            setHasDraft(false);
        }
    }, []);
    // Guardar borrador
    const saveDraft = useCallback(() => {
        try {
            setIsAutoSaving(true);

            const draft: DraftData = {
                state: {
                    currentStep: state.currentStep,
                    contractor: state.contractor,
                    contractorData: state.contractorData,
                    selectedOrders: state.selectedOrders,
                    assignments: state.assignments,
                    validationErrors: state.validationErrors,
                },
                timestamp: Date.now(),
                expiresAt: Date.now() + DRAFT_EXPIRY_HOURS * 60 * 60 * 1000,
                version: "1.0",
            };

            localStorage.setItem(DRAFT_STORAGE_KEY, JSON.stringify(draft));
            setLastSavedAt(new Date());
            setHasDraft(true);

            showFeedback("success", "Borrador guardado exitosamente", {
                duration: 3000,
            });
        } catch (error) {
            console.error("Error saving draft:", error);
            showFeedback("error", "Error al guardar el borrador", {
                duration: 5000,
            });
        } finally {
            setIsAutoSaving(false);
        }
    }, [state, showFeedback]);

    // Cargar borrador
    const loadDraft = useCallback(() => {
        try {
            const stored = localStorage.getItem(DRAFT_STORAGE_KEY);

            if (!stored) {
                showFeedback(
                    "warning",
                    "No se encontró ningún borrador guardado",
                );

                return;
            }

            const draft: DraftData = JSON.parse(stored);

            // Cargar el estado del borrador
            dispatch({ type: "LOAD_DRAFT", payload: draft.state });

            showFeedback("success", "Borrador cargado exitosamente", {
                duration: 3000,
                action: {
                    label: "Deshacer",
                    onClick: () => dispatch({ type: "RESET_WIZARD" }),
                },
            });

            // Limpiar el borrador después de cargarlo
            localStorage.removeItem(DRAFT_STORAGE_KEY);
            setHasDraft(false);
        } catch (error) {
            console.error("Error loading draft:", error);
            showFeedback("error", "Error al cargar el borrador");
        }
    }, [dispatch, showFeedback]);

    // Limpiar borrador
    const clearDraft = useCallback(() => {
        try {
            localStorage.removeItem(DRAFT_STORAGE_KEY);
            setHasDraft(false);
            setLastSavedAt(null);
            showFeedback("info", "Borrador eliminado");
        } catch (error) {
            console.error("Error clearing draft:", error);
        }
    }, [showFeedback]);

    // Calcular edad del borrador
    const getDraftAge = useCallback((): string => {
        try {
            const stored = localStorage.getItem(DRAFT_STORAGE_KEY);

            if (!stored) return "";

            const draft: DraftData = JSON.parse(stored);
            const ageMs = Date.now() - draft.timestamp;
            const ageMinutes = Math.floor(ageMs / 60000);

            if (ageMinutes < 1) return "hace menos de 1 minuto";
            if (ageMinutes < 60)
                return `hace ${ageMinutes} minuto${ageMinutes > 1 ? "s" : ""}`;

            const ageHours = Math.floor(ageMinutes / 60);

            return `hace ${ageHours} hora${ageHours > 1 ? "s" : ""}`;
        } catch {
            return "";
        }
    }, []);

    // Auto-guardado
    useEffect(() => {
        // Solo auto-guardar si hay cambios significativos
        const hasContent =
            state.contractor ||
            state.selectedOrders.length > 0 ||
            state.assignments.length > 0;

        if (hasContent && !state.isSubmitting) {
            autoSaveTimerRef.current = setInterval(
                saveDraft,
                AUTO_SAVE_INTERVAL,
            );

            return () => {
                if (autoSaveTimerRef.current) {
                    clearInterval(autoSaveTimerRef.current);
                }
            };
        }
    }, [state, saveDraft]);

    // Verificar borrador al montar
    useEffect(() => {
        checkForDraft();

        // Escuchar cambios en localStorage (sincronización entre pestañas)
        const handleStorageChange = (e: StorageEvent) => {
            if (e.key === DRAFT_STORAGE_KEY) {
                checkForDraft();
            }
        };

        window.addEventListener("storage", handleStorageChange);

        return () => window.removeEventListener("storage", handleStorageChange);
    }, [checkForDraft]);

    return {
        hasDraft,
        draftAge: getDraftAge(),
        saveDraft,
        loadDraft,
        clearDraft,
        isAutoSaving,
        lastSavedAt,
    };
}
