"use client";

import type { Assignment } from "@/features/assignments/components/wizard/components/AssignmentTable";

import { forwardRef, ForwardedRef, useEffect } from "react";
import Image from "next/image";

import { formatQuantity } from "@/shared/utils/formatters";
import { useRemission } from "@/features/remissions/hooks/useRemission";

import ContractorInfoCompact from "./ContractorInfoCompact";

interface OrderDetails {
    id: string;
    cutOrder?: string;
    creationDate?: Date;
    parts?: {
        id: string;
        code: string;
    }[];
}

interface RemissionDocumentCompactProps {
    remissionId?: string;
    assignmentId?: string;
    contractorId?: string;
    assignments?: Assignment[];
    order?: OrderDetails;
    notes?: string;
    date?: Date;
    orientation?: "portrait" | "landscape";
    simplified?: boolean;
}

// Componente optimizado y compacto para impresión y exportación a PDF
export const RemissionDocumentCompact = forwardRef(
    function RemissionDocumentCompact(
        {
            remissionId,
            assignmentId,
            contractorId,
            assignments = [],
            order,
            notes,
            date = new Date(),
            orientation = "landscape",
            simplified = false,
        }: RemissionDocumentCompactProps,
        ref: ForwardedRef<HTMLDivElement>,
    ) {
        const {
            data: remissionData,
            error,
            isLoading,
        } = useRemission(remissionId || null);

        const usingDatabase = !!remissionId && !!remissionData;

        const remissionFolio = usingDatabase
            ? remissionData?.folio
            : assignmentId;

        let remissionDate = date;

        if (usingDatabase && remissionData?.createdAt) {
            remissionDate =
                remissionData.createdAt instanceof Date
                    ? remissionData.createdAt
                    : new Date(remissionData.createdAt);
        }

        if (!remissionDate || isNaN(remissionDate.getTime())) {
            remissionDate = new Date();
        }

        const remissionNotes = usingDatabase ? remissionData?.notes : notes;
        const remissionContractorId = usingDatabase
            ? remissionData?.contractorId
            : contractorId;

        const remissionAssignments =
            usingDatabase && remissionData?.remissionItems
                ? remissionData.remissionItems.map((item: any) => ({
                      modelCode: item.modelCode,
                      colorName: item.colorName,
                      sizeCode: item.sizeCode,
                      quantity: item.quantity,
                  }))
                : assignments.map((assignment: any) => {
                      if (assignment.garmentSize) {
                          return {
                              modelCode:
                                  assignment.garmentSize.garment.model.code,
                              colorName:
                                  assignment.garmentSize.garment.color.name,
                              sizeCode: assignment.garmentSize.size.code,
                              quantity: assignment.quantity,
                          };
                      } else {
                          return {
                              modelCode: assignment.modelCode || "Sin modelo",
                              colorName: assignment.colorName || "Sin color",
                              sizeCode: assignment.sizeCode || "Sin talla",
                              quantity: assignment.quantity,
                          };
                      }
                  });

        const remissionOrder =
            usingDatabase && remissionData?.orderDetails
                ? (remissionData.orderDetails as OrderDetails)
                : order;

        const totalQuantity = remissionAssignments.reduce(
            (sum: number, a: any) => sum + a.quantity,
            0,
        );

        const groupedAssignments = remissionAssignments.reduce(
            (acc: any, assignment: any) => {
                const modelCode = assignment.modelCode || "Sin modelo";
                const colorName = assignment.colorName || "Sin color";
                const sizeCode = assignment.sizeCode || "Sin talla";

                const key = `${modelCode}-${colorName}`;

                if (!acc[key]) {
                    acc[key] = {
                        modelCode: modelCode,
                        colorName: colorName,
                        sizes: {},
                    };
                }

                if (acc[key].sizes[sizeCode]) {
                    acc[key].sizes[sizeCode] += assignment.quantity;
                } else {
                    acc[key].sizes[sizeCode] = assignment.quantity;
                }

                return acc;
            },
            {} as Record<
                string,
                {
                    modelCode: string;
                    colorName: string;
                    sizes: Record<string, number>;
                }
            >,
        );

        const allSizes = [
            ...new Set(remissionAssignments.map((a: any) => a.sizeCode)),
        ].sort((a: any, b: any) => {
            const sizeOrder = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];
            const indexA = sizeOrder.indexOf(a);
            const indexB = sizeOrder.indexOf(b);

            if (indexA !== -1 && indexB !== -1) {
                return indexA - indexB;
            }
            if (indexA !== -1) return -1;
            if (indexB !== -1) return 1;

            return a.localeCompare(b);
        });

        const formattedDate = new Intl.DateTimeFormat("es-ES", {
            year: "numeric",
            month: "long",
            day: "numeric",
        }).format(remissionDate);

        let orderCreationDate = null;

        if (remissionOrder?.creationDate) {
            try {
                const orderDate =
                    remissionOrder.creationDate instanceof Date
                        ? remissionOrder.creationDate
                        : new Date(remissionOrder.creationDate);

                if (!isNaN(orderDate.getTime())) {
                    orderCreationDate = new Intl.DateTimeFormat("es-ES", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                    }).format(orderDate);
                }
            } catch (error) {
                console.error("Error formatting order creation date:", error);
            }
        }

        useEffect(() => {
            const setupPrintStyles = () => {
                if (ref && typeof ref === "object" && ref.current) {
                    ref.current.setAttribute("data-print-ready", "true");
                    ref.current.style.display = "block";
                    ref.current.style.height = "auto";
                    ref.current.style.overflow = "visible";
                    ref.current.style.backgroundColor = "white";

                    if (orientation === "landscape") {
                        ref.current.style.width = "100%";
                        ref.current.style.maxWidth = "100%";
                        ref.current.classList.add("landscape-document");
                    }
                }
            };

            setupPrintStyles();
        }, [ref, orientation]);

        if (remissionId && isLoading) {
            return <div className="p-8 bg-gray-50">Cargando remisión...</div>;
        }

        if (remissionId && error) {
            return (
                <div className="p-8 bg-red-50 text-red-600">
                    Error al cargar la remisión: {error.message}
                </div>
            );
        }

        if (remissionId && !remissionData) {
            return (
                <div className="p-8 bg-yellow-50 text-yellow-600">
                    Remisión no encontrada
                </div>
            );
        }

        return (
            <div
                ref={ref}
                className={`bg-white remission-document ${orientation === "landscape" ? "landscape-mode" : "portrait-mode"}`}
                id="remission-document"
                style={{
                    display: "block",
                    height: "auto",
                    overflow: "visible",
                    backgroundColor: "white",
                    pageBreakInside: "avoid",
                    width: orientation === "landscape" ? "297mm" : "210mm",
                    maxWidth: "100%",
                    padding: "8mm 10mm",
                    margin: "0 auto",
                    fontSize: "9pt",
                    lineHeight: "1.5",
                    fontFamily:
                        "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
                }}
            >
                {/* Encabezado compacto y elegante */}
                <div className="mb-2 border-b border-gray-800 pb-1">
                    <div className="flex justify-between items-center gap-4">
                        <div className="flex items-center gap-4">
                            <div className="border-r border-gray-300 pr-4">
                                <h1 className="text-base font-bold text-gray-900 uppercase tracking-wider">
                                    REMISIÓN DE ASIGNACIÓN
                                </h1>
                            </div>
                            <div className="flex items-center gap-4 text-xs">
                                <div>
                                    <span className="text-gray-600 uppercase tracking-wide">
                                        Folio:
                                    </span>
                                    <span className="ml-1 font-bold text-gray-900">
                                        {remissionFolio}
                                    </span>
                                </div>
                                <div className="border-l border-gray-300 pl-4">
                                    <span className="text-gray-600 uppercase tracking-wide">
                                        Fecha:
                                    </span>
                                    <span className="ml-1 text-gray-900">
                                        {formattedDate}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div className="text-right">
                            {simplified ? (
                                <div className="text-right">
                                    <h2 className="text-sm font-bold text-gray-900">
                                        LOHARI TEXTILES
                                    </h2>
                                    <p className="text-[10px] text-gray-600 font-medium">
                                        Gestión de Producción Textil
                                    </p>
                                </div>
                            ) : (
                                <div className="flex items-center gap-2">
                                    <div>
                                        <p className="text-[10px] text-gray-600 font-medium text-right">
                                            Gestión de Producción Textil
                                        </p>
                                    </div>
                                    <div className="h-8 w-16 relative flex items-center justify-center">
                                        <Image
                                            priority
                                            alt="Lohari Textiles"
                                            height={32}
                                            src="/LOGO-LOHARI.svg"
                                            style={{
                                                width: "auto",
                                                height: "100%",
                                                maxWidth: "64px",
                                                maxHeight: "32px",
                                                objectFit: "contain",
                                            }}
                                            width={64}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Información del contratista */}
                <div className="mb-2">
                    {/* Información del contratista */}
                    <div className="bg-gray-50 border border-gray-200 rounded px-3 py-1.5">
                        <h2 className="text-[10px] font-semibold text-gray-700 uppercase tracking-wide mb-0.5">
                            Contratista
                        </h2>
                        <div className="overflow-hidden">
                            {remissionContractorId &&
                                (simplified ? (
                                    <div className="space-y-1">
                                        <p className="text-xs font-semibold text-gray-800">
                                            {remissionData?.contractor?.name ||
                                                "Contratista"}
                                        </p>
                                        {(remissionData?.contractor
                                            ?.firstName ||
                                            remissionData?.contractor
                                                ?.lastName) && (
                                            <p className="text-xs text-gray-600">
                                                {
                                                    remissionData?.contractor
                                                        ?.firstName
                                                }{" "}
                                                {
                                                    remissionData?.contractor
                                                        ?.middleName
                                                }
                                                {
                                                    remissionData?.contractor
                                                        ?.lastName
                                                }{" "}
                                                {
                                                    remissionData?.contractor
                                                        ?.secondLastName
                                                }
                                            </p>
                                        )}
                                        {remissionData?.contractor?.notes && (
                                            <p className="text-[10px] text-gray-500 italic">
                                                {
                                                    remissionData?.contractor
                                                        ?.notes
                                                }
                                            </p>
                                        )}
                                    </div>
                                ) : usingDatabase &&
                                  remissionData?.contractor ? (
                                    // Si ya tenemos la información del contratista desde la remisión, usarla directamente
                                    <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0">
                                            <div className="w-8 h-8 bg-gray-800 text-white rounded flex items-center justify-center text-xs font-semibold leading-none">
                                                {remissionData.contractor.name
                                                    .split(" ")
                                                    .map(
                                                        (word: string) =>
                                                            word[0],
                                                    )
                                                    .join("")
                                                    .toUpperCase()
                                                    .slice(0, 2)}
                                            </div>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-xs font-semibold text-gray-900 break-words">
                                                {remissionData.contractor.name}
                                            </p>
                                            {(remissionData.contractor
                                                .firstName ||
                                                remissionData.contractor
                                                    .lastName) && (
                                                <p className="text-[10px] text-gray-600 break-words">
                                                    {[
                                                        remissionData.contractor
                                                            .firstName,
                                                        remissionData.contractor
                                                            .middleName,
                                                        remissionData.contractor
                                                            .lastName,
                                                        remissionData.contractor
                                                            .secondLastName,
                                                    ]
                                                        .filter(Boolean)
                                                        .join(" ")}
                                                </p>
                                            )}
                                            {(remissionData.contractor.email ||
                                                remissionData.contractor
                                                    .phone) && (
                                                <p className="text-[10px] text-gray-500 break-words">
                                                    {remissionData.contractor
                                                        .phone && (
                                                        <span className="inline-block">
                                                            {
                                                                remissionData
                                                                    .contractor
                                                                    .phone
                                                            }
                                                        </span>
                                                    )}
                                                    {remissionData.contractor
                                                        .email &&
                                                        remissionData.contractor
                                                            .phone && (
                                                            <span className="inline-block mx-1">
                                                                •
                                                            </span>
                                                        )}
                                                    {remissionData.contractor
                                                        .email && (
                                                        <span className="inline-block">
                                                            {
                                                                remissionData
                                                                    .contractor
                                                                    .email
                                                            }
                                                        </span>
                                                    )}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                ) : (
                                    <ContractorInfoCompact
                                        contractorId={remissionContractorId}
                                    />
                                ))}
                        </div>
                    </div>
                </div>

                {/* Detalle de asignaciones - tabla ultra compacta */}
                <div className="mb-2">
                    <div className="border border-gray-400 rounded-sm overflow-hidden">
                        <table
                            className="w-full border-collapse text-[10px]"
                            style={{ tableLayout: "auto" }}
                        >
                            <thead className="bg-gray-800 text-white">
                                <tr>
                                    <th
                                        className="px-1 py-1 text-left font-semibold uppercase tracking-wide"
                                        style={{
                                            width: "70px",
                                            verticalAlign: "middle",
                                        }}
                                    >
                                        Orden
                                    </th>
                                    <th
                                        className="px-1 py-1 text-left font-semibold uppercase tracking-wide"
                                        style={{
                                            width: "80px",
                                            verticalAlign: "middle",
                                        }}
                                    >
                                        Partidas
                                    </th>
                                    <th
                                        className="px-1 py-1 text-left font-semibold uppercase tracking-wide"
                                        style={{
                                            width: "60px",
                                            verticalAlign: "middle",
                                        }}
                                    >
                                        Modelo
                                    </th>
                                    <th
                                        className="px-1 py-1 text-left font-semibold uppercase tracking-wide"
                                        style={{
                                            width: "100px",
                                            verticalAlign: "middle",
                                        }}
                                    >
                                        Color
                                    </th>
                                    {/* Columnas dinámicas para cada talla */}
                                    {allSizes.map((size) => (
                                        <th
                                            key={String(size)}
                                            className="px-1 py-1 text-center font-semibold uppercase tracking-wide"
                                            style={{
                                                width: "45px",
                                                verticalAlign: "middle",
                                            }}
                                        >
                                            {String(size)}
                                        </th>
                                    ))}
                                    <th
                                        className="px-1 py-1 text-center font-semibold uppercase tracking-wide bg-gray-900"
                                        style={{
                                            width: "50px",
                                            verticalAlign: "middle",
                                        }}
                                    >
                                        Total
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white">
                                {Object.entries(groupedAssignments).map(
                                    (
                                        [groupKey, group]: [string, any],
                                        index: number,
                                    ) => {
                                        const groupTotal = (
                                            Object.values(
                                                group.sizes,
                                            ) as number[]
                                        ).reduce(
                                            (sum: number, quantity: number) =>
                                                sum + quantity,
                                            0,
                                        );

                                        return (
                                            <tr
                                                key={groupKey}
                                                className={
                                                    index % 2 === 0
                                                        ? "bg-white"
                                                        : "bg-gray-50"
                                                }
                                            >
                                                {/* Orden de Trabajo */}
                                                <td
                                                    className="px-1 py-1 text-gray-900 border-r border-gray-200"
                                                    style={{
                                                        verticalAlign: "middle",
                                                    }}
                                                >
                                                    {remissionOrder?.cutOrder ||
                                                        remissionOrder?.id ||
                                                        "N/A"}
                                                </td>

                                                {/* Partidas */}
                                                <td
                                                    className="px-1 py-1 text-gray-900 border-r border-gray-200"
                                                    style={{
                                                        verticalAlign: "middle",
                                                    }}
                                                >
                                                    {remissionOrder?.parts
                                                        ?.length
                                                        ? remissionOrder.parts
                                                              .map(
                                                                  (p) => p.code,
                                                              )
                                                              .join(", ")
                                                        : "-"}
                                                </td>

                                                {/* Modelo */}
                                                <td
                                                    className="px-1 py-1 text-gray-900 font-medium border-r border-gray-200"
                                                    style={{
                                                        verticalAlign: "middle",
                                                    }}
                                                >
                                                    {group.modelCode}
                                                </td>

                                                {/* Color */}
                                                <td
                                                    className="px-1 py-1 text-gray-900 border-r border-gray-200"
                                                    style={{
                                                        verticalAlign: "middle",
                                                    }}
                                                >
                                                    {group.colorName}
                                                </td>

                                                {/* Columnas de tallas */}
                                                {allSizes.map((size: any) => (
                                                    <td
                                                        key={String(size)}
                                                        className="px-1 py-1 text-gray-900 text-center border-r border-gray-200"
                                                        style={{
                                                            verticalAlign:
                                                                "middle",
                                                        }}
                                                    >
                                                        {group.sizes[size] ? (
                                                            <span className="font-medium">
                                                                {
                                                                    group.sizes[
                                                                        size
                                                                    ]
                                                                }
                                                            </span>
                                                        ) : (
                                                            <span className="text-gray-400">
                                                                -
                                                            </span>
                                                        )}
                                                    </td>
                                                ))}

                                                <td
                                                    className="px-1 py-1 text-center font-bold bg-gray-100"
                                                    style={{
                                                        verticalAlign: "middle",
                                                    }}
                                                >
                                                    {formatQuantity(groupTotal)}
                                                </td>
                                            </tr>
                                        );
                                    },
                                )}

                                {/* Fila de total */}
                                <tr className="bg-gray-800 text-white">
                                    <td
                                        className="px-2 py-1 text-xs font-bold text-right"
                                        colSpan={4 + allSizes.length}
                                    >
                                        TOTAL GENERAL:
                                    </td>
                                    <td className="px-1 py-1 text-xs font-bold text-center bg-gray-900">
                                        {formatQuantity(totalQuantity)}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Observaciones y resumen en una línea */}
                <div className="grid grid-cols-2 gap-3 mb-2">
                    {/* Observaciones */}
                    <div className="bg-gray-50 border border-gray-200 rounded px-3 py-2">
                        <h2 className="text-[10px] font-semibold text-gray-700 uppercase tracking-wide mb-1">
                            Observaciones
                        </h2>
                        <div className="min-h-[40px]">
                            {remissionNotes ? (
                                <p className="text-xs text-gray-700">
                                    {remissionNotes}
                                </p>
                            ) : (
                                <div className="h-full" />
                            )}
                        </div>
                    </div>

                    {/* Resumen de cantidades */}
                    <div className="bg-gray-800 text-white rounded px-3 py-2">
                        <h2 className="text-[10px] font-semibold uppercase tracking-wide mb-1">
                            Resumen de Remisión
                        </h2>
                        <div className="flex justify-between items-center">
                            <div className="text-xs">
                                <p>
                                    Total de items:{" "}
                                    <span className="font-bold">
                                        {Object.keys(groupedAssignments).length}
                                    </span>
                                </p>
                                <p>
                                    Total de unidades:{" "}
                                    <span className="font-bold text-lg">
                                        {formatQuantity(totalQuantity)}
                                    </span>
                                </p>
                            </div>
                            <div className="text-right">
                                <p className="text-[10px] text-gray-300">
                                    Generado el
                                </p>
                                <p className="text-xs font-medium">
                                    {new Date().toLocaleDateString("es-ES")}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Firmas - diseño más compacto */}
                <div className="mt-4 pt-3 border-t border-gray-400">
                    <div className="grid grid-cols-2 gap-8">
                        <div>
                            <div className="border-b border-gray-800 pb-8 mb-1">
                                <div className="h-8" />
                            </div>
                            <p className="text-[10px] font-medium text-gray-700 text-center">
                                Contratista - Recibe
                            </p>
                            <p className="text-[9px] text-gray-500 text-center">
                                Nombre y firma
                            </p>
                        </div>

                        <div>
                            <div className="border-b border-gray-800 pb-8 mb-1">
                                <div className="h-8" />
                            </div>
                            <p className="text-[10px] font-medium text-gray-700 text-center">
                                Lohari Textiles - Autoriza
                            </p>
                            <p className="text-[9px] text-gray-500 text-center">
                                Gerencia de Producción
                            </p>
                        </div>
                    </div>
                </div>

                {/* Pie de página minimalista */}
                <div className="mt-3 pt-2 border-t border-gray-200 flex justify-between items-center">
                    <p className="text-[9px] text-gray-500">
                        Documento válido como comprobante de entrega de material
                        para producción
                    </p>
                    <p className="text-[9px] text-gray-500">
                        Lohari Textiles © {new Date().getFullYear()} - Sistema
                        de Gestión de Producción
                    </p>
                </div>
            </div>
        );
    },
);
