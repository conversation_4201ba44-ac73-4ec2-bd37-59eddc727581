// Componente helper para mostrar link de remisión
// Este archivo se puede usar en OrderAssignmentsList.tsx

import React from "react";
import { Button } from "@heroui/react";
import { DocumentTextIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

interface RemissionLinkProps {
    contractorId: string;
    contractorName: string;
    orderId: string;
    hasRemissions?: boolean;
}

export function RemissionLink({
    contractorId,
    contractorName,
    orderId,
    hasRemissions = true, // Por defecto asumimos que hay remisiones si se crearon asignaciones
}: RemissionLinkProps) {
    // Por ahora navegamos a la página general de remisiones
    // En el futuro se puede mejorar con filtros específicos
    const remissionUrl = `/dashboard/remissions`;

    return (
        <Link href={remissionUrl} title={`Ver remisiones de ${contractorName}`}>
            <Button
                className="text-xs"
                color="primary"
                size="sm"
                startContent={<DocumentTextIcon className="w-4 h-4" />}
                variant="light"
            >
                <span className="hidden sm:inline">Ver Remisiones</span>
                <span className="sm:hidden">Remisiones</span>
            </Button>
        </Link>
    );
}
