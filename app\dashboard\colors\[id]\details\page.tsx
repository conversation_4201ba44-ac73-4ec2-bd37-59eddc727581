"use client";

import { useState } from "react";
import { useRout<PERSON> } from "next/navigation";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardBody,
    Card<PERSON>ooter,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
} from "@heroui/react";
import {
    SwatchIcon,
    ArrowLeftIcon,
    HomeIcon,
    ChevronRightIcon,
    InformationCircleIcon,
    DocumentTextIcon,
    PencilIcon,
    TrashIcon,
    CalendarIcon,
    ClipboardDocumentCheckIcon,
    ClipboardDocumentListIcon,
    CheckBadgeIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";

import { useColor } from "@/features/colors/hooks/useColor";
import { useDeleteColor } from "@/features/colors/hooks/useColor";
import EmptyState from "@/shared/components/ui/EmptyState";

// Animaciones
const pageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            staggerChildren: 0.05,
            ease: [0.22, 1, 0.36, 1],
        },
    },
    exit: {
        opacity: 0,
        y: -10,
        transition: {
            duration: 0.4,
            ease: [0.22, 1, 0.36, 1],
        },
    },
};

// Añadir la animación de spin lento a Tailwind
const spinSlowAnimation = `@keyframes spin-slow {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}`;

// Añadir el estilo al documento
if (typeof document !== "undefined") {
    const existingStyle = document.getElementById("spin-slow-style");

    if (!existingStyle) {
        const style = document.createElement("style");

        style.id = "spin-slow-style";
        style.textContent = spinSlowAnimation;
        document.head.appendChild(style);

        // Añadir la clase a Tailwind
        const animationStyle = document.createElement("style");

        animationStyle.textContent = `.animate-spin-slow { animation: spin-slow 20s linear infinite; }`;
        document.head.appendChild(animationStyle);
    }
}

const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: [0.22, 1, 0.36, 1],
        },
    },
};

const fadeInVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
};

export default function ColorDetailsPage({
    params,
}: {
    params: { id: string };
}) {
    const router = useRouter();
    // Accedemos directamente al ID desde los parámetros
    const id = params.id;

    // Estados
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    // Hooks
    const { color, isLoading, error } = useColor(id);
    const { deleteColor, isDeleting } = useDeleteColor();

    // Manejar eliminación
    const handleDelete = async () => {
        try {
            await deleteColor(id);
            router.push("/dashboard/colors");
        } catch {
            // Capturamos el error sin variable para evitar advertencias
            setIsConfirmingDelete(false);
        }
    };

    // Renderizar color
    const renderColorSwatch = () => {
        if (!color) return null;

        return (
            <div className="flex flex-col items-center">
                <motion.div
                    animate={{ scale: 1, opacity: 1 }}
                    className="relative"
                    initial={{ scale: 0.8, opacity: 0 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                    <div className="absolute -inset-1.5 bg-gradient-to-r from-primary/20 via-blue-500/20 to-primary/20 rounded-full blur-sm animate-pulse" />
                    <div className="absolute -inset-3 bg-gradient-to-r from-primary/10 via-transparent to-blue-500/10 rounded-full animate-spin-slow" />
                    <div
                        className="w-24 h-24 rounded-full shadow-lg relative z-10"
                        style={{ backgroundColor: color.hexCode || "#3B82F6" }}
                    />
                </motion.div>
                <div className="relative mt-4">
                    <div className="absolute -inset-x-4 -inset-y-2 bg-gradient-to-r from-primary/5 via-blue-500/5 to-primary/5 rounded-lg blur-sm" />
                    <motion.h2
                        animate={{ y: 0, opacity: 1 }}
                        className="text-2xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent relative z-10"
                        initial={{ y: 10, opacity: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        {color.name}
                    </motion.h2>
                    <motion.h3
                        animate={{ y: 0, opacity: 1 }}
                        className="text-xl text-gray-600 dark:text-gray-300 relative z-10 font-mono"
                        initial={{ y: 10, opacity: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        {color.hexCode || "#3B82F6"}
                    </motion.h3>
                </div>
            </div>
        );
    };

    return (
        <motion.div
            animate="visible"
            className="container mx-auto px-4 py-6"
            initial="hidden"
            variants={pageVariants}
        >
            {/* Navegación */}
            <motion.div className="mb-6" variants={itemVariants}>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <Link
                        className="hover:text-primary transition-colors flex items-center"
                        href="/dashboard"
                    >
                        <HomeIcon className="h-4 w-4 mr-1" />
                        Inicio
                    </Link>
                    <ChevronRightIcon className="h-3 w-3" />
                    <Link
                        className="hover:text-primary transition-colors flex items-center"
                        href="/dashboard/colors"
                    >
                        <SwatchIcon className="h-4 w-4 mr-1" />
                        Colores
                    </Link>
                    <ChevronRightIcon className="h-3 w-3" />
                    <span className="text-gray-700 dark:text-gray-300 font-medium flex items-center">
                        <SwatchIcon className="h-4 w-4 mr-1" />
                        Detalles
                    </span>
                </div>

                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent">
                            Detalles del Color
                        </h1>
                        <p className="text-gray-500 dark:text-gray-400 mt-1 max-w-xl">
                            Información detallada del color y sus usos
                        </p>
                    </div>

                    <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <Button
                            className="relative overflow-hidden bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30"
                            startContent={
                                <ArrowLeftIcon className="h-4 w-4 text-primary/90" />
                            }
                            variant="flat"
                            onPress={() => router.push("/dashboard/colors")}
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 opacity-0 hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10 font-medium">
                                Volver al listado
                            </span>
                        </Button>
                    </motion.div>
                </div>
            </motion.div>

            {/* Contenido principal */}
            <AnimatePresence mode="wait">
                {isLoading ? (
                    <motion.div
                        key="loading"
                        animate="visible"
                        className="flex flex-col items-center justify-center py-12"
                        exit="hidden"
                        initial="hidden"
                        variants={fadeInVariants}
                    >
                        <Spinner color="primary" size="lg" />
                        <p className="mt-4 text-gray-500 dark:text-gray-400">
                            Cargando información del color...
                        </p>
                    </motion.div>
                ) : error || !color ? (
                    <motion.div
                        key="error"
                        animate="visible"
                        exit="hidden"
                        initial="hidden"
                        variants={fadeInVariants}
                    >
                        <EmptyState
                            buttonIcon={<ArrowLeftIcon className="h-5 w-5" />}
                            buttonText="Volver al listado"
                            description="No se pudo encontrar la información del color solicitado. Es posible que haya sido eliminado o que no exista."
                            title="Color no encontrado"
                            onAction={() => router.push("/dashboard/colors")}
                        />
                    </motion.div>
                ) : (
                    <motion.div
                        key="content"
                        animate="visible"
                        className="grid grid-cols-1 md:grid-cols-3 gap-6"
                        exit="hidden"
                        initial="hidden"
                        variants={fadeInVariants}
                    >
                        {/* Columna izquierda - Información básica */}
                        <div className="md:col-span-1">
                            <motion.div variants={itemVariants}>
                                <Card className="shadow-lg border-none overflow-hidden bg-gradient-to-b from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10 hover:shadow-xl transition-all duration-300">
                                    <CardHeader className="flex flex-col items-center p-6 bg-gradient-to-r from-blue-50/80 via-indigo-50/60 to-blue-50/80 dark:from-blue-900/30 dark:via-indigo-900/20 dark:to-blue-900/30 relative overflow-hidden">
                                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-blue-500/5 animate-pulse" />
                                        {renderColorSwatch()}
                                    </CardHeader>
                                    <CardBody className="p-6">
                                        <div className="space-y-4">
                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <SwatchIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Nombre del color
                                                </h3>
                                                <p className="text-lg font-semibold">
                                                    {color.name}
                                                </p>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <CalendarIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Fecha de registro
                                                </h3>
                                                <p className="text-base">
                                                    {format(
                                                        new Date(
                                                            color.createdAt,
                                                        ),
                                                        "dd MMMM yyyy",
                                                        { locale: es },
                                                    )}
                                                </p>
                                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                                    {formatDistanceToNow(
                                                        new Date(
                                                            color.createdAt,
                                                        ),
                                                        {
                                                            addSuffix: true,
                                                            locale: es,
                                                        },
                                                    )}
                                                </p>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <ClipboardDocumentCheckIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Prendas
                                                </h3>
                                                <Chip
                                                    className="mt-1"
                                                    color={
                                                        (color._count
                                                            ?.garments ?? 0) > 0
                                                            ? "success"
                                                            : "warning"
                                                    }
                                                    size="sm"
                                                    startContent={
                                                        (color._count
                                                            ?.garments ?? 0) >
                                                        0 ? (
                                                            <CheckBadgeIcon className="h-3.5 w-3.5" />
                                                        ) : (
                                                            <ClipboardDocumentListIcon className="h-3.5 w-3.5" />
                                                        )
                                                    }
                                                    variant="flat"
                                                >
                                                    {(color._count?.garments ??
                                                        0) > 0
                                                        ? `${color._count?.garments ?? 0} prendas`
                                                        : "Sin prendas"}
                                                </Chip>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <DocumentTextIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Código hexadecimal
                                                </h3>
                                                <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg text-sm font-mono">
                                                    {color.hexCode || "#3B82F6"}
                                                </div>
                                            </div>
                                        </div>
                                    </CardBody>
                                    <CardFooter className="flex justify-between p-6 bg-gradient-to-r from-gray-50 to-blue-50/30 dark:from-gray-800/50 dark:to-gray-900/50">
                                        {isConfirmingDelete ? (
                                            <div className="flex w-full gap-2">
                                                <Button
                                                    color="danger"
                                                    isLoading={isDeleting}
                                                    size="sm"
                                                    startContent={
                                                        !isDeleting && (
                                                            <TrashIcon className="h-4 w-4" />
                                                        )
                                                    }
                                                    variant="solid"
                                                    onPress={handleDelete}
                                                >
                                                    {isDeleting
                                                        ? "Eliminando..."
                                                        : "Confirmar"}
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    variant="flat"
                                                    onPress={() =>
                                                        setIsConfirmingDelete(
                                                            false,
                                                        )
                                                    }
                                                >
                                                    Cancelar
                                                </Button>
                                            </div>
                                        ) : (
                                            <>
                                                <Tooltip content="Editar color">
                                                    <Button
                                                        className="shadow-sm hover:shadow-md transition-all"
                                                        color="primary"
                                                        startContent={
                                                            <PencilIcon className="h-4 w-4" />
                                                        }
                                                        variant="flat"
                                                        onPress={() =>
                                                            router.push(
                                                                `/dashboard/colors/${id}/edit`,
                                                            )
                                                        }
                                                    >
                                                        Editar
                                                    </Button>
                                                </Tooltip>

                                                <Tooltip
                                                    color="danger"
                                                    content="Eliminar color"
                                                >
                                                    <Button
                                                        className="shadow-sm hover:shadow-md transition-all"
                                                        color="danger"
                                                        isDisabled={
                                                            (color._count
                                                                ?.garments ??
                                                                0) > 0
                                                        }
                                                        startContent={
                                                            <TrashIcon className="h-4 w-4" />
                                                        }
                                                        variant="flat"
                                                        onPress={() =>
                                                            setIsConfirmingDelete(
                                                                true,
                                                            )
                                                        }
                                                    >
                                                        Eliminar
                                                    </Button>
                                                </Tooltip>
                                            </>
                                        )}
                                    </CardFooter>
                                </Card>
                            </motion.div>
                        </div>

                        {/* Columna derecha - Prendas y más información */}
                        <div className="md:col-span-2">
                            <motion.div variants={itemVariants}>
                                <Card className="shadow-lg border-none overflow-hidden bg-gradient-to-b from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10 hover:shadow-xl transition-all duration-300 mb-6">
                                    <CardHeader className="p-6 bg-gradient-to-r from-blue-50/80 via-indigo-50/60 to-blue-50/80 dark:from-blue-900/30 dark:via-indigo-900/20 dark:to-blue-900/30 relative overflow-hidden">
                                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-blue-500/5 animate-pulse" />
                                        <div className="flex items-center">
                                            <ClipboardDocumentCheckIcon className="h-6 w-6 text-primary mr-2" />
                                            <h2 className="text-xl font-bold">
                                                Prendas
                                            </h2>
                                        </div>
                                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                            Listado de prendas que utilizan este
                                            color
                                        </p>
                                    </CardHeader>
                                    <CardBody className="p-6">
                                        {(color._count?.garments ?? 0) > 0 ? (
                                            <div className="text-center p-8">
                                                <p className="text-gray-500 dark:text-gray-400">
                                                    Esta sección mostrará las
                                                    prendas que utilizan este
                                                    color cuando esté
                                                    implementada.
                                                </p>
                                            </div>
                                        ) : (
                                            <div className="flex flex-col items-center justify-center py-8">
                                                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                                                    <ClipboardDocumentListIcon className="h-8 w-8 text-gray-400" />
                                                </div>
                                                <h3 className="text-lg font-semibold mb-2">
                                                    Sin prendas
                                                </h3>
                                                <p className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-4">
                                                    Este color aún no tiene
                                                    prendas registradas en el
                                                    sistema.
                                                </p>
                                                <Button
                                                    color="primary"
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    Crear prenda
                                                </Button>
                                            </div>
                                        )}
                                    </CardBody>
                                </Card>

                                <Card className="shadow-lg border-none overflow-hidden bg-gradient-to-b from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10 hover:shadow-xl transition-all duration-300">
                                    <CardHeader className="p-6 bg-gradient-to-r from-blue-50/80 via-indigo-50/60 to-blue-50/80 dark:from-blue-900/30 dark:via-indigo-900/20 dark:to-blue-900/30 relative overflow-hidden">
                                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-blue-500/5 animate-pulse" />
                                        <div className="flex items-center">
                                            <InformationCircleIcon className="h-6 w-6 text-primary mr-2" />
                                            <h2 className="text-xl font-bold">
                                                Información adicional
                                            </h2>
                                        </div>
                                    </CardHeader>
                                    <CardBody className="p-6">
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl">
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                                                    <SwatchIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Vista previa
                                                </h3>
                                                <div className="flex flex-col items-center gap-3">
                                                    <div
                                                        className="w-24 h-24 rounded-lg shadow-md"
                                                        style={{
                                                            backgroundColor:
                                                                color.hexCode ||
                                                                "#3B82F6",
                                                        }}
                                                    />
                                                    <p className="text-sm font-mono">
                                                        {color.hexCode ||
                                                            "#3B82F6"}
                                                    </p>
                                                </div>
                                            </div>

                                            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl">
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                                                    <CalendarIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Fechas
                                                </h3>
                                                <div className="space-y-2">
                                                    <div>
                                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                                            Creado:
                                                        </span>
                                                        <p className="font-medium">
                                                            {format(
                                                                new Date(
                                                                    color.createdAt,
                                                                ),
                                                                "dd MMMM yyyy, HH:mm",
                                                                { locale: es },
                                                            )}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                                            Última
                                                            actualización:
                                                        </span>
                                                        <p className="font-medium">
                                                            {format(
                                                                new Date(
                                                                    color.updatedAt,
                                                                ),
                                                                "dd MMMM yyyy, HH:mm",
                                                                { locale: es },
                                                            )}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </CardBody>
                                </Card>
                            </motion.div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
}
