"use client";

import { Tooltip as HeroUITooltip, TooltipProps } from "@heroui/react";
import {
    InformationCircleIcon,
    QuestionMarkCircleIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { FC } from "react";

// Tipos de variantes de tooltips
export type TooltipVariant = "info" | "help" | "warning";

// Props extendidas para nuestro tooltip
interface EnhancedTooltipProps extends Omit<TooltipProps, "children"> {
    variant?: TooltipVariant;
    iconClassName?: string;
    buttonAriaLabel?: string;
    children?: React.ReactNode;
}

// Configuración de variantes
const variantConfig = {
    info: {
        icon: InformationCircleIcon,
        iconColor: "text-blue-500 dark:text-blue-400",
        defaultAriaLabel: "Más información",
    },
    help: {
        icon: QuestionMarkCircleIcon,
        iconColor: "text-gray-500 dark:text-gray-400",
        defaultAriaLabel: "Ayuda",
    },
    warning: {
        icon: ExclamationTriangleIcon,
        iconColor: "text-amber-500 dark:text-amber-400",
        defaultAriaLabel: "Advertencia",
    },
};

// Motion props consistentes
const defaultMotionProps = {
    variants: {
        exit: {
            opacity: 0,
            transition: {
                duration: 0.1,
                ease: "easeIn",
            },
        },
        enter: {
            opacity: 1,
            transition: {
                duration: 0.15,
                ease: "easeOut",
            },
        },
    },
};

/**
 * Tooltip mejorado con diseño consistente
 * Incluye icono por defecto según la variante
 */
export const EnhancedTooltip: FC<EnhancedTooltipProps> = ({
    variant = "info",
    iconClassName,
    buttonAriaLabel,
    children,
    className = "",
    placement = "top",
    delay = 0,
    closeDelay = 0,
    motionProps = defaultMotionProps,
    ...props
}) => {
    const config = variantConfig[variant];
    const Icon = config.icon;

    // Si no se proporcionan children, usar el botón con icono por defecto
    const tooltipChildren = children || (
        <button
            aria-label={buttonAriaLabel || config.defaultAriaLabel}
            className="hover:opacity-70 transition-opacity cursor-help"
        >
            <Icon className={iconClassName || `w-4 h-4 ${config.iconColor}`} />
        </button>
    );

    return (
        <HeroUITooltip
            className={`bg-gray-900 dark:bg-gray-800 text-white text-sm px-3 py-2 rounded-lg shadow-lg max-w-xs ${className}`}
            closeDelay={closeDelay}
            delay={delay}
            motionProps={motionProps}
            placement={placement}
            {...props}
        >
            {tooltipChildren}
        </HeroUITooltip>
    );
};

// Exportar también el componente base por si se necesita
export { HeroUITooltip as BaseTooltip };
