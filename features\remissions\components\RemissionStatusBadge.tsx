"use client";

import React from "react";
import { Chip } from "@heroui/react";
import {
    CheckBadgeIcon,
    ClockIcon,
    TrashIcon,
    DocumentTextIcon,
} from "@heroicons/react/24/outline";

interface RemissionStatusBadgeProps {
    status: string;
    size?: "sm" | "md" | "lg";
    showIcon?: boolean;
}

const statusConfigs = {
    completed: {
        color: "success" as const,
        icon: CheckBadgeIcon,
        label: "Entregado",
    },
    entregado: {
        color: "success" as const,
        icon: CheckBadgeIcon,
        label: "Entregado",
    },
    pending: {
        color: "warning" as const,
        icon: ClockIcon,
        label: "Pendiente",
    },
    pendiente: {
        color: "warning" as const,
        icon: ClockIcon,
        label: "Pendiente",
    },
    active: {
        color: "primary" as const,
        icon: DocumentTextIcon,
        label: "Activo",
    },
    cancelled: {
        color: "danger" as const,
        icon: TrashIcon,
        label: "Cancelado",
    },
    cancelado: {
        color: "danger" as const,
        icon: TrashIcon,
        label: "Cancelado",
    },
};

export default function RemissionStatusBadge({
    status,
    size = "md",
    showIcon = true,
}: RemissionStatusBadgeProps) {
    const normalizedStatus = status?.toLowerCase() || "pending";
    const config = statusConfigs[
        normalizedStatus as keyof typeof statusConfigs
    ] || {
        color: "default" as const,
        icon: DocumentTextIcon,
        label: status || "Sin estado",
    };

    const Icon = config.icon;

    return (
        <Chip
            color={config.color}
            size={size}
            startContent={showIcon ? <Icon className="w-4 h-4" /> : undefined}
            variant="flat"
        >
            {config.label}
        </Chip>
    );
}
