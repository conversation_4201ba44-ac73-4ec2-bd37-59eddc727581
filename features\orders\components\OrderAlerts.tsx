"use client";

import React, { useMemo } from "react";
import {
    ExclamationTriangleIcon,
    ClockIcon,
    UserGroupIcon,
    TruckIcon,
    CheckCircleIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";
import { differenceInDays } from "date-fns";

import { Card, CardBody, Button } from "@/shared/components/ui/hero-ui-client";
import { Order } from "@/features/orders/types/orders";

interface OrderAlert {
    id: string;
    type: "danger" | "warning" | "info" | "success";
    title: string;
    description: string;
    icon: React.ReactNode;
    action?: {
        label: string;
        onClick: () => void;
    };
    order?: Order;
}

interface ExtendedOrder extends Order {
    deliveryDate?: string | Date;
    quantity?: number;
    status: string;
}

interface OrderAlertsProps {
    orders: Order[];
    onViewOrder?: (order: Order) => void;
    onDismiss?: (alertId: string) => void;
}

export function useOrderAlerts(
    orders: Order[],
    onViewOrder?: (order: Order) => void,
): OrderAlert[] {
    return useMemo(() => {
        const alerts: OrderAlert[] = [];

        // Órdenes retrasadas
        const overdueOrders = orders.filter((order) => {
            const extOrder = order as ExtendedOrder;

            if (
                !extOrder.deliveryDate ||
                extOrder.status === "delivered" ||
                extOrder.status === "cancelled"
            ) {
                return false;
            }
            const daysLeft = differenceInDays(
                new Date(extOrder.deliveryDate),
                new Date(),
            );

            return daysLeft < 0;
        });

        if (overdueOrders.length > 0) {
            alerts.push({
                id: "overdue-orders",
                type: "danger",
                title: `${overdueOrders.length} órdenes retrasadas`,
                description:
                    "Estas órdenes han pasado su fecha de entrega y requieren atención inmediata",
                icon: <ExclamationTriangleIcon className="w-5 h-5" />,
                action:
                    overdueOrders.length === 1 && onViewOrder
                        ? {
                              label: "Ver orden",
                              onClick: () => onViewOrder(overdueOrders[0]),
                          }
                        : undefined,
            });
        }

        // Órdenes que vencen hoy
        const todayOrders = orders.filter((order) => {
            const extOrder = order as ExtendedOrder;

            if (
                !extOrder.deliveryDate ||
                extOrder.status === "delivered" ||
                extOrder.status === "cancelled"
            ) {
                return false;
            }
            const daysLeft = differenceInDays(
                new Date(extOrder.deliveryDate),
                new Date(),
            );

            return daysLeft === 0;
        });

        if (todayOrders.length > 0) {
            alerts.push({
                id: "today-orders",
                type: "warning",
                title: `${todayOrders.length} órdenes vencen hoy`,
                description:
                    "Asegúrate de completar estas entregas antes del final del día",
                icon: <ClockIcon className="w-5 h-5" />,
                action:
                    todayOrders.length === 1 && onViewOrder
                        ? {
                              label: "Ver orden",
                              onClick: () => onViewOrder(todayOrders[0]),
                          }
                        : undefined,
            });
        }

        // Órdenes próximas a vencer (3 días)
        const urgentOrders = orders.filter((order) => {
            const extOrder = order as ExtendedOrder;

            if (
                !extOrder.deliveryDate ||
                extOrder.status === "delivered" ||
                extOrder.status === "cancelled"
            ) {
                return false;
            }
            const daysLeft = differenceInDays(
                new Date(extOrder.deliveryDate),
                new Date(),
            );

            return daysLeft > 0 && daysLeft <= 3;
        });

        if (urgentOrders.length > 0) {
            alerts.push({
                id: "urgent-orders",
                type: "warning",
                title: `${urgentOrders.length} órdenes próximas a vencer`,
                description: "Estas órdenes vencen en los próximos 3 días",
                icon: <TruckIcon className="w-5 h-5" />,
            });
        }

        // Análisis de capacidad (simulado por ahora)
        const activeOrders = orders.filter(
            (o) =>
                o.status === "in_progress" ||
                o.status === "in_production" ||
                o.status === "pending",
        );

        if (activeOrders.length > 50) {
            // Umbral arbitrario
            alerts.push({
                id: "capacity-warning",
                type: "warning",
                title: "Alta carga de trabajo",
                description: `${activeOrders.length} órdenes activas. Considera redistribuir la carga entre contratistas`,
                icon: <UserGroupIcon className="w-5 h-5" />,
            });
        }

        // Mensaje positivo si todo está bien
        if (alerts.length === 0) {
            alerts.push({
                id: "all-good",
                type: "success",
                title: "Todo en orden",
                description: "No hay alertas pendientes. ¡Buen trabajo!",
                icon: <CheckCircleIcon className="w-5 h-5" />,
            });
        }

        return alerts;
    }, [orders, onViewOrder]);
}

export default function OrderAlerts({
    orders,
    onViewOrder,
    onDismiss,
}: OrderAlertsProps) {
    const alerts = useOrderAlerts(orders, onViewOrder);
    const [dismissedAlerts, setDismissedAlerts] = React.useState<Set<string>>(
        new Set(),
    );

    const handleDismiss = (alertId: string) => {
        setDismissedAlerts((prev) => new Set(prev).add(alertId));
        onDismiss?.(alertId);
    };

    const visibleAlerts = alerts.filter(
        (alert) => !dismissedAlerts.has(alert.id),
    );

    if (visibleAlerts.length === 0) {
        return null;
    }

    const getAlertStyles = (type: OrderAlert["type"]) => {
        switch (type) {
            case "danger":
                return "border-l-4 border-danger bg-danger-50 dark:bg-danger-900/20";
            case "warning":
                return "border-l-4 border-warning bg-warning-50 dark:bg-warning-900/20";
            case "success":
                return "border-l-4 border-success bg-success-50 dark:bg-success-900/20";
            case "info":
            default:
                return "border-l-4 border-primary bg-primary-50 dark:bg-primary-900/20";
        }
    };

    const getIconColor = (type: OrderAlert["type"]) => {
        switch (type) {
            case "danger":
                return "text-danger";
            case "warning":
                return "text-warning";
            case "success":
                return "text-success";
            case "info":
            default:
                return "text-primary";
        }
    };

    return (
        <div className="space-y-3 mb-6">
            {visibleAlerts.map((alert) => (
                <Card
                    key={alert.id}
                    className={`relative ${getAlertStyles(alert.type)}`}
                >
                    <CardBody className="py-3">
                        <div className="flex items-start gap-3">
                            <div
                                className={`flex-shrink-0 ${getIconColor(alert.type)}`}
                            >
                                {alert.icon}
                            </div>

                            <div className="flex-1">
                                <div className="font-medium text-base">
                                    {alert.title}
                                </div>
                                <div className="text-sm opacity-80 mt-1">
                                    {alert.description}
                                </div>
                            </div>

                            <div className="flex items-center gap-2">
                                {alert.action && (
                                    <Button
                                        color={alert.type as any}
                                        size="sm"
                                        variant="flat"
                                        onPress={alert.action.onClick}
                                    >
                                        {alert.action.label}
                                    </Button>
                                )}

                                <button
                                    aria-label="Cerrar alerta"
                                    className="text-gray-400 hover:text-gray-600 transition-colors"
                                    onClick={() => handleDismiss(alert.id)}
                                >
                                    <XMarkIcon className="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            ))}
        </div>
    );
}
