/**
 * Sistema de logging centralizado para producción
 *
 * Este módulo proporciona funciones de logging que:
 * - Solo muestran logs en desarrollo
 * - Pueden enviar logs a servicios externos en producción
 * - Mantienen un formato consistente
 */

const isDevelopment = process.env.NODE_ENV === "development";
const isServer = typeof window === "undefined";

export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
}

interface LogOptions {
    level?: LogLevel;
    context?: string;
    metadata?: Record<string, unknown>;
}

/**
 * Logger centralizado que solo muestra output en desarrollo
 */
class Logger {
    private static instance: Logger;
    private logLevel: LogLevel = isDevelopment
        ? LogLevel.DEBUG
        : LogLevel.ERROR;

    private constructor() {}

    static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }

        return Logger.instance;
    }

    private formatMessage(
        level: string,
        message: string,
        context?: string,
    ): string {
        const timestamp = new Date().toISOString();
        const prefix = context ? `[${context}]` : "";

        return `[${timestamp}] ${level} ${prefix} ${message}`;
    }

    private shouldLog(level: LogLevel): boolean {
        return level >= this.logLevel;
    }

    debug(message: string, options?: LogOptions): void {
        if (!this.shouldLog(LogLevel.DEBUG)) return;

        if (isDevelopment) {
            // eslint-disable-next-line no-console
            // REMOVED: console.log(this.formatMessage('DEBUG', message, options?.context), options?.metadata || '');
        }
    }

    info(message: string, options?: LogOptions): void {
        if (!this.shouldLog(LogLevel.INFO)) return;

        if (isDevelopment) {
            // eslint-disable-next-line no-console
            // REMOVED: console.info(this.formatMessage('INFO', message, options?.context), options?.metadata || '');
        }
    }

    warn(message: string, options?: LogOptions): void {
        if (!this.shouldLog(LogLevel.WARN)) return;

        if (isDevelopment) {
            // eslint-disable-next-line no-console
            // REMOVED: console.warn(this.formatMessage('WARN', message, options?.context), options?.metadata || '');
        }
    }

    error(message: string, error?: unknown, options?: LogOptions): void {
        if (!this.shouldLog(LogLevel.ERROR)) return;

        const errorDetails =
            error instanceof Error
                ? {
                      message: error.message,
                      stack: error.stack,
                      ...options?.metadata,
                  }
                : options?.metadata;

        if (isDevelopment) {
            // eslint-disable-next-line no-console
            // REMOVED: // REMOVED: console.error(this.formatMessage('ERROR', message, options?.context), errorDetails || '');
        } else if (isServer) {
            // En producción, podríamos enviar a un servicio de logging
            // Por ahora, solo lo registramos en el servidor
            // eslint-disable-next-line no-console
            console.error(
                this.formatMessage("ERROR", message, options?.context),
                errorDetails || "",
            );
        }
    }

    /**
     * Para errores críticos que siempre deben registrarse
     */
    critical(message: string, error?: unknown, options?: LogOptions): void {
        const errorDetails =
            error instanceof Error
                ? {
                      message: error.message,
                      stack: error.stack,
                      ...options?.metadata,
                  }
                : options?.metadata;

        // Los errores críticos siempre se registran
        // eslint-disable-next-line no-console
        // REMOVED: console.error(this.formatMessage('CRITICAL', message, options?.context), errorDetails || '');

        // Aquí podrías enviar a un servicio de monitoreo como Sentry
    }
}

// Exportar instancia singleton
export const logger = Logger.getInstance();

// Exportar funciones de conveniencia
export const logDebug = (message: string, options?: LogOptions) =>
    logger.debug(message, options);
export const logInfo = (message: string, options?: LogOptions) =>
    logger.info(message, options);
export const logWarn = (message: string, options?: LogOptions) =>
    logger.warn(message, options);
export const logError = (
    message: string,
    error?: unknown,
    options?: LogOptions,
) => logger.error(message, error, options);
export const logCritical = (
    message: string,
    error?: unknown,
    options?: LogOptions,
) => logger.critical(message, error, options);
