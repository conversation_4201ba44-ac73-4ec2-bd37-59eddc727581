import { Suspense } from "react";

import { Spinner } from "@/shared/components/ui/hero-ui-client";
import { auth } from "@/lib/auth-helpers";

import UnifiedRemissionsPage from "./UnifiedClientPage";

export const metadata = {
    title: "Remisiones | Lohari Textiles",
    description: "Gestión de remisiones para asignaciones de producción",
};

/**
 * Página de remisiones con el nuevo sistema de diseño unificado
 */
export default async function RemissionsPage() {
    const session = await auth();
    const userRole =
        session?.user?.role?.name || session?.user?.role?.id || "user";

    return (
        <Suspense
            fallback={
                <div className="flex items-center justify-center min-h-[50vh]">
                    <Spinner label="Cargando remisiones..." size="lg" />
                </div>
            }
        >
            <UnifiedRemissionsPage userRole={userRole} />
        </Suspense>
    );
}
