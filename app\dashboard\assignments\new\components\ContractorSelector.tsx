"use client";

import React from "react";
import { Select, SelectItem, Avatar } from "@heroui/react";
import {
    PhoneIcon,
    EnvelopeIcon,
    CheckCircleIcon,
    ClockIcon,
    CubeTransparentIcon,
} from "@heroicons/react/24/outline";

// Type definitions
interface Contractor {
    id: string;
    name: string;
    email?: string | null;
    phone?: string | null;
    address?: string | null;
}

interface ContractorMetrics {
    contractorId: string;
    contractorName: string;
    activeOrders: number;
    lastDeliveryDate: Date | null;
    daysSinceLastDelivery: number | null;
    totalCompletedPieces: number;
    activeAssignments: number;
}

interface ContractorSelectorProps {
    contractors: Contractor[];
    selectedId: string | null;
    onChange: (id: string) => void;
    metrics?: ContractorMetrics;
    error?: string;
}

export function ContractorSelector({
    contractors,
    selectedId,
    onChange,
    metrics,
    error,
}: ContractorSelectorProps) {
    return (
        <div className="space-y-4">
            <Select
                classNames={{
                    trigger: "h-12",
                }}
                errorMessage={error}
                isInvalid={!!error}
                label="Seleccionar contratista"
                placeholder="Buscar por nombre..."
                selectedKeys={selectedId ? [selectedId] : []}
                onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;

                    onChange(selected);
                }}
            >
                {contractors.map((contractor) => (
                    <SelectItem key={contractor.id} textValue={contractor.name}>
                        <div className="flex items-center gap-3">
                            <Avatar
                                color="secondary"
                                name={contractor.name}
                                size="sm"
                            />
                            <div className="flex-1">
                                <p className="font-medium">{contractor.name}</p>
                                <div className="flex items-center gap-3 text-xs text-gray-500">
                                    {contractor.phone && (
                                        <span className="flex items-center gap-1">
                                            <PhoneIcon className="w-3 h-3" />
                                            {contractor.phone}
                                        </span>
                                    )}
                                    {contractor.email && (
                                        <span className="flex items-center gap-1">
                                            <EnvelopeIcon className="w-3 h-3" />
                                            {contractor.email}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </SelectItem>
                ))}
            </Select>

            {/* Métricas del contratista seleccionado */}
            {selectedId && metrics && (
                <div className="grid grid-cols-3 gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                            <CheckCircleIcon className="w-5 h-5 text-success" />
                        </div>
                        <p className="text-2xl font-bold">
                            {metrics.totalCompletedPieces || 0}
                        </p>
                        <p className="text-xs text-gray-500">
                            Prendas completadas
                        </p>
                    </div>

                    <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                            <ClockIcon className="w-5 h-5 text-warning" />
                        </div>
                        <p className="text-2xl font-bold">
                            {metrics.activeAssignments || 0}
                        </p>
                        <p className="text-xs text-gray-500">Activas</p>
                    </div>

                    <div className="text-center">
                        <div className="flex items-center justify-center mb-2">
                            <CubeTransparentIcon className="w-5 h-5 text-primary" />
                        </div>
                        <p className="text-2xl font-bold">
                            {metrics.activeOrders || 0}
                        </p>
                        <p className="text-xs text-gray-500">Órdenes activas</p>
                    </div>
                </div>
            )}

            {/* Última entrega */}
            {selectedId &&
                metrics &&
                metrics.daysSinceLastDelivery !== null && (
                    <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                            <span>Última entrega</span>
                            <span className="font-medium">
                                hace {metrics.daysSinceLastDelivery} días
                            </span>
                        </div>
                    </div>
                )}
        </div>
    );
}
