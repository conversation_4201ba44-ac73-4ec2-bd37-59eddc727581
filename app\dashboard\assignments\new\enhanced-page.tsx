"use client";

import React from "react";
import { But<PERSON> } from "@heroui/react";
import {
    ArrowLeftIcon,
    QuestionMarkCircleIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

import EnhancedAssignmentWizard from "@/features/assignments/components/wizard/EnhancedAssignmentWizard";
import { DashboardShell } from "@/shared/components/layout/Shell";
import { DashboardHeader } from "@/shared/components/layout/Header";

import { WizardProvider } from "./wizard-context";

export default function EnhancedAssignmentPage() {
    return (
        <DashboardShell>
            <DashboardHeader
                heading="Nueva Asignación de Trabajo"
                text="Asigna trabajo a contratistas seleccionando órdenes, prendas, tallas y cantidades."
            >
                <div className="flex gap-2">
                    <Link href="/dashboard/assignments">
                        <Button
                            color="default"
                            startContent={<ArrowLeftIcon className="w-4 h-4" />}
                            variant="flat"
                        >
                            Volver a Asignaciones
                        </Button>
                    </Link>

                    <Button
                        isIconOnly
                        aria-label="Ayuda sobre asignaciones"
                        as="a"
                        className="ml-2"
                        color="primary"
                        href="/docs/asignaciones"
                        size="sm"
                        target="_blank"
                        variant="flat"
                    >
                        <QuestionMarkCircleIcon className="w-5 h-5" />
                    </Button>
                </div>
            </DashboardHeader>

            <div className="grid gap-8">
                <WizardProvider>
                    <EnhancedAssignmentWizard />
                </WizardProvider>
            </div>
        </DashboardShell>
    );
}
