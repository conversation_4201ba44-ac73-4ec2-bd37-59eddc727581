"use server";

import { prisma } from "@/shared/lib/prisma";

export async function getAssignments(
    params: {
        search?: string;
        status?: string;
        contractorId?: string;
        orderId?: string;
        dateFrom?: string;
        dateTo?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) {
    try {
        const {
            search,
            status,
            contractorId,
            orderId,
            dateFrom,
            dateTo,
            orderBy = "createdAt",
            order = "desc",
            page = 1,
            perPage = 12,
        } = params;

        // Build where clause
        const where: any = {};

        if (search) {
            where.OR = [
                { order: { code: { contains: search, mode: "insensitive" } } },
                {
                    order: {
                        customer: {
                            name: { contains: search, mode: "insensitive" },
                        },
                    },
                },
                {
                    contractor: {
                        name: { contains: search, mode: "insensitive" },
                    },
                },
                {
                    garmentSize: {
                        garment: {
                            model: {
                                code: { contains: search, mode: "insensitive" },
                            },
                        },
                    },
                },
                {
                    garmentSize: {
                        garment: {
                            color: {
                                name: { contains: search, mode: "insensitive" },
                            },
                        },
                    },
                },
            ];
        }

        if (status) {
            where.status = status;
        }

        if (contractorId) {
            where.contractorId = contractorId;
        }

        if (orderId) {
            where.orderId = orderId;
        }

        if (dateFrom || dateTo) {
            where.createdAt = {};
            if (dateFrom) {
                where.createdAt.gte = new Date(dateFrom);
            }
            if (dateTo) {
                where.createdAt.lte = new Date(dateTo);
            }
        }

        // Get total count
        const total = await prisma.assignment.count({ where });

        // Get assignments with pagination
        const assignments = await prisma.assignment.findMany({
            where,
            include: {
                order: {
                    include: {
                        customer: true,
                    },
                },
                contractor: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                    },
                },
                garmentSize: {
                    include: {
                        size: true,
                        garment: {
                            include: {
                                model: true,
                                color: true,
                            },
                        },
                    },
                },
                remissions: {
                    include: {
                        remission: {
                            select: {
                                id: true,
                                folio: true,
                                status: true,
                                printedAt: true,
                            },
                        },
                    },
                },
            },
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
        });

        return {
            success: true,
            data: assignments,
            pagination: {
                total,
                currentPage: page,
                lastPage: Math.ceil(total / perPage),
                perPage,
            },
        };
    } catch (error) {
        console.error("Error fetching assignments:", error);

        return {
            success: false,
            error: "Error al obtener las asignaciones",
        };
    }
}
