import { NextResponse } from "next/server";

import { ConfigCache } from "@/shared/lib/config-cache";

export async function GET() {
    try {
        // Obtener instancia del cache
        const cache = await ConfigCache.getInstance();

        // Forzar recarga
        await cache.reload();

        // Obtener todos los OrderStatus
        const orderStatuses = await cache.getAll("orderStatus");

        // Verificar específicamente Nuevo y En producción
        const nuevo = await cache.getByName("orderStatus", "Nuevo");
        const enProduccion = await cache.getByName(
            "orderStatus",
            "En producción",
        );

        return NextResponse.json({
            success: true,
            message: "Cache recargado",
            totalStatuses: orderStatuses.length,
            statuses: orderStatuses.map((s) => ({
                name: s.name,
                iconName: s.iconName,
                color: s.color,
            })),
            specific: {
                nuevo: nuevo
                    ? {
                          name: nuevo.name,
                          iconName: nuevo.iconName,
                          color: nuevo.color,
                      }
                    : null,
                enProduccion: enProduccion
                    ? {
                          name: enProduccion.name,
                          iconName: enProduccion.iconName,
                          color: enProduccion.color,
                      }
                    : null,
            },
        });
    } catch (error) {
        // REMOVED: console.error("Error en debug endpoint:", error);
        return NextResponse.json(
            {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "Error desconocido",
            },
            { status: 500 },
        );
    }
}
