"use client";

import { ReactNode, useState } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import {
    HomeIcon,
    DocumentTextIcon,
    UserGroupIcon,
    SwatchIcon,
    ShoppingBagIcon,
    ClipboardIcon,
    TagIcon,
    Bars3Icon,
    ClipboardDocumentListIcon,
    DocumentDuplicateIcon,
} from "@heroicons/react/24/outline";

import { useNavigation } from "../../../core/context/navigation-context";
import { ThemeSwitch } from "../theme-switch";

import Sidebar from "./Sidebar";
import MobileDrawer from "./MobileDrawer";

interface HybridNavProps {
    children: ReactNode;
}

export default function HybridNav({ children }: HybridNavProps) {
    const { toggleMobileMenu, isDesktop, isTablet, isMobile } = useNavigation();
    const pathname = usePathname();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    // Navigation items for both sidebar and mobile drawer
    const navItems = [
        { href: "/dashboard", label: "Inicio", icon: <HomeIcon /> },
        {
            href: "/dashboard/orders",
            label: "Pedidos",
            icon: <ShoppingBagIcon />,
        },
        {
            href: "/dashboard/customers",
            label: "Clientes",
            icon: <UserGroupIcon />,
        },
        {
            href: "/dashboard/contractors",
            label: "Contratistas",
            icon: <UserGroupIcon />,
        },
        {
            href: "/dashboard/assignments",
            label: "Asignaciones",
            icon: <ClipboardDocumentListIcon />,
        },
        {
            href: "/dashboard/remissions",
            label: "Remisiones",
            icon: <DocumentDuplicateIcon />,
        },
        { href: "/dashboard/models", label: "Modelos", icon: <TagIcon /> },
        { href: "/dashboard/colors", label: "Colores", icon: <SwatchIcon /> },
        { href: "/dashboard/sizes", label: "Tallas", icon: <TagIcon /> },
        {
            href: "/dashboard/notes",
            label: "Notas",
            icon: <DocumentTextIcon />,
        },
        { href: "/dashboard/todos", label: "Tareas", icon: <ClipboardIcon /> },
    ];

    // Check if we're in dashboard area
    const isDashboard = pathname.startsWith("/dashboard");

    const handleToggleMobile = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
        toggleMobileMenu();
    };

    return (
        <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
            {/* Sidebar - Desktop & Tablet */}
            {isDashboard && (isDesktop || isTablet) && <Sidebar />}

            {/* Mobile Backdrop */}
            {isDashboard && (isMobile || isTablet) && isMobileMenuOpen && (
                <div
                    aria-hidden="true"
                    className="fixed inset-0 z-backdrop bg-black/50 lg:hidden"
                    onClick={handleToggleMobile}
                />
            )}

            {/* Mobile Drawer */}
            {isDashboard && (isMobile || isTablet) && (
                <MobileDrawer isOpen={isMobileMenuOpen} navItems={navItems} />
            )}

            {/* Main Content Area */}
            <div className="flex-1 flex flex-col min-w-0">
                {/* Header */}
                <header
                    className="h-16 bg-white dark:bg-gray-900 border-b dark:border-gray-800 
                                 flex items-center justify-between px-4 lg:px-6 flex-shrink-0 z-header"
                >
                    <div className="flex items-center gap-2">
                        {/* Mobile menu button */}
                        {isDashboard && (isMobile || isTablet) && (
                            <button
                                aria-label="Toggle menu"
                                className="p-2 text-gray-500 hover:text-gray-700 
                                         dark:text-gray-400 dark:hover:text-gray-200 
                                         rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800
                                         transition-colors z-mobile-button"
                                onClick={handleToggleMobile}
                            >
                                <Bars3Icon className="w-6 h-6" />
                            </button>
                        )}

                        <div className="flex items-center gap-2">
                            <Image
                                alt="LOHARI Logo"
                                className="w-8 h-8"
                                height={32}
                                src="/LOGO-ICONO.svg"
                                style={{ width: "auto", height: "auto" }}
                                width={32}
                            />
                            <Image
                                alt="LOHARI"
                                className="h-6 w-auto"
                                height={36}
                                src="/LOGO-TEXTO.svg"
                                style={{ width: "auto", height: "auto" }}
                                width={120}
                            />
                        </div>
                    </div>

                    <div className="flex items-center gap-2">
                        <ThemeSwitch />
                    </div>
                </header>

                {/* Main Content */}
                <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
                    <div className="h-full">
                        <div className="p-6 lg:p-8">
                            <div className="w-full max-w-screen-2xl mx-auto">
                                {children}
                            </div>
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <footer
                    className="py-3 bg-white dark:bg-gray-900 border-t 
                                 dark:border-gray-800 flex-shrink-0"
                >
                    <div className="flex items-center justify-center px-4">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                            © {new Date().getFullYear()} LOHARI - Todos los
                            derechos reservados
                        </span>
                    </div>
                </footer>
            </div>
        </div>
    );
}
