import { db } from "@/shared/lib/db";
import { checkVersion } from "@/features/shared/integrity";

/**
 * Example: Update assignment with optimistic locking
 * This demonstrates how to use version checking in update operations
 */
export async function updateAssignmentWithLocking(
    assignmentId: string,
    expectedVersion: number,
    updateData: {
        quantity?: number;
        defects?: number;
        isCompleted?: boolean;
    },
) {
    try {
        // First, check the version matches
        await checkVersion(db.assignment, assignmentId, expectedVersion);

        // If version matches, proceed with update and increment version
        const updated = await db.assignment.update({
            where: { id: assignmentId },
            data: {
                ...updateData,
                version: {
                    increment: 1,
                },
            },
        });

        return {
            success: true,
            data: updated,
        };
    } catch (error) {
        if (error instanceof Error && error.name === "OptimisticLockError") {
            return {
                success: false,
                error: "The assignment was modified by another user. Please refresh and try again.",
                type: "OPTIMISTIC_LOCK_ERROR",
            };
        }

        return {
            success: false,
            error: "Failed to update assignment",
        };
    }
}

/**
 * Example: Bulk update with version checking
 */
export async function bulkUpdateAssignmentsWithLocking(
    updates: Array<{
        id: string;
        expectedVersion: number;
        data: {
            quantity?: number;
            defects?: number;
            isCompleted?: boolean;
        };
    }>,
) {
    const results = await Promise.allSettled(
        updates.map((update) =>
            updateAssignmentWithLocking(
                update.id,
                update.expectedVersion,
                update.data,
            ),
        ),
    );

    const successful = results.filter(
        (r) => r.status === "fulfilled" && r.value.success,
    );
    const failed = results.filter(
        (r) =>
            r.status === "rejected" ||
            (r.status === "fulfilled" && !r.value.success),
    );

    return {
        totalUpdates: updates.length,
        successCount: successful.length,
        failureCount: failed.length,
        failures: failed.map((r, index) => ({
            assignmentId: updates[index].id,
            error: r.status === "rejected" ? r.reason : (r as any).value.error,
        })),
    };
}

/**
 * Example: Complete assignment with integrity
 */
export async function completeAssignmentWithIntegrity(
    assignmentId: string,
    expectedVersion: number,
    completionData: {
        defects: number;
        notes?: string;
    },
) {
    return await db.$transaction(async (tx) => {
        // Check version
        const assignment = await tx.assignment.findUnique({
            where: { id: assignmentId },
            select: {
                version: true,
                quantity: true,
                garmentSizeId: true,
            },
        });

        if (!assignment) {
            throw new Error("Assignment not found");
        }

        if (assignment.version !== expectedVersion) {
            throw new Error(
                `Version mismatch: expected ${expectedVersion}, got ${assignment.version}`,
            );
        }

        // Update assignment
        const updated = await tx.assignment.update({
            where: { id: assignmentId },
            data: {
                isCompleted: true,
                defects: completionData.defects,
                version: {
                    increment: 1,
                },
            },
        });

        // Update garment size if there are defects
        if (completionData.defects > 0) {
            await tx.garmentSize.update({
                where: { id: assignment.garmentSizeId },
                data: {
                    usedQuantity: {
                        decrement: completionData.defects,
                    },
                },
            });
        }

        // Create progress record
        await tx.assignmentProgress.create({
            data: {
                assignmentId,
                type: "COMPLETED",
                completed: assignment.quantity - completionData.defects,
            },
        });

        return updated;
    });
}
