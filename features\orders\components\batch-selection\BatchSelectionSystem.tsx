"use client";

import React from "react";

import { SelectionProvider, useSelection } from "./SelectionContext";
import SmartSelectionToolbar from "./SmartSelectionToolbar";
import BatchActionBar from "./BatchActionBar";

import { BatchSelectionSystemProps } from "./index";

/**
 * Componente interno que maneja la lógica y renderiza las herramientas de selección
 */
const BatchSelectionTools = ({
    statuses,
    customers,
    onUpdateStatus,
    onAssignContractor,
    onDelete,
    onExport,
    onDuplicate,
}: BatchSelectionSystemProps) => {
    const { selectedIds } = useSelection();
    const hasSelection = selectedIds.size > 0;

    return (
        <>
            <SmartSelectionToolbar customers={customers} statuses={statuses} />

            {hasSelection && (
                <BatchActionBar
                    statuses={statuses}
                    onAssignContractor={onAssignContractor}
                    onDelete={onDelete}
                    onDuplicate={onDuplicate}
                    onExport={onExport}
                    onUpdateStatus={onUpdateStatus}
                />
            )}
        </>
    );
};

/**
 * Sistema de selección por lotes que proporciona contexto de selección y herramientas
 * para la gestión de selecciones múltiples de órdenes
 */
const BatchSelectionSystem = (props: BatchSelectionSystemProps) => {
    return (
        <SelectionProvider items={[]}>
            <BatchSelectionTools {...props} />
        </SelectionProvider>
    );
};

export default BatchSelectionSystem;
