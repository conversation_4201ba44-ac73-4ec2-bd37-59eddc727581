"use client";

import { useEffect, useCallback, useRef } from "react";
import { debounce } from "lodash";

const DRAFT_KEY_PREFIX = "note_draft_";
const DRAFT_SAVE_DELAY = 1000; // 1 second

export interface NoteDraft {
    content: string;
    importanceId?: string;
    statusId?: string;
    lastSaved: number;
}

export function useNoteDraft(orderId: string) {
    const draftKey = `${DRAFT_KEY_PREFIX}${orderId}`;
    const saveTimeoutRef = useRef<NodeJS.Timeout>();

    // Get draft from localStorage
    const getDraft = useCallback((): NoteDraft | null => {
        if (typeof window === "undefined") return null;

        try {
            const draftData = localStorage.getItem(draftKey);

            if (draftData) {
                const draft = JSON.parse(draftData) as NoteDraft;
                // Check if draft is older than 24 hours
                const isExpired =
                    Date.now() - draft.lastSaved > 24 * 60 * 60 * 1000;

                if (isExpired) {
                    localStorage.removeItem(draftKey);

                    return null;
                }

                return draft;
            }
        } catch (error) {
            console.error("Error reading draft:", error);
        }

        return null;
    }, [draftKey]);

    // Save draft to localStorage with debouncing
    const saveDraft = useCallback(
        debounce((data: Partial<NoteDraft>) => {
            if (typeof window === "undefined") return;

            try {
                const currentDraft = getDraft();
                const newDraft: NoteDraft = {
                    content: data.content || currentDraft?.content || "",
                    importanceId:
                        data.importanceId || currentDraft?.importanceId,
                    statusId: data.statusId || currentDraft?.statusId,
                    lastSaved: Date.now(),
                };

                // Only save if there's actual content
                if (newDraft.content.trim()) {
                    localStorage.setItem(draftKey, JSON.stringify(newDraft));
                } else {
                    // Remove empty drafts
                    localStorage.removeItem(draftKey);
                }
            } catch (error) {
                console.error("Error saving draft:", error);
            }
        }, DRAFT_SAVE_DELAY),
        [draftKey, getDraft],
    );

    // Clear draft
    const clearDraft = useCallback(() => {
        if (typeof window === "undefined") return;

        try {
            localStorage.removeItem(draftKey);
        } catch (error) {
            console.error("Error clearing draft:", error);
        }
    }, [draftKey]);

    // Clean up old drafts on mount
    useEffect(() => {
        if (typeof window === "undefined") return;

        try {
            // Clean up any drafts older than 7 days
            const keys = Object.keys(localStorage);

            keys.forEach((key) => {
                if (key.startsWith(DRAFT_KEY_PREFIX)) {
                    try {
                        const draftData = localStorage.getItem(key);

                        if (draftData) {
                            const draft = JSON.parse(draftData) as NoteDraft;
                            const isOld =
                                Date.now() - draft.lastSaved >
                                7 * 24 * 60 * 60 * 1000;

                            if (isOld) {
                                localStorage.removeItem(key);
                            }
                        }
                    } catch {
                        // If can't parse, remove it
                        localStorage.removeItem(key);
                    }
                }
            });
        } catch (error) {
            console.error("Error cleaning up old drafts:", error);
        }
    }, []);

    // Clean up on unmount
    useEffect(() => {
        return () => {
            if (saveTimeoutRef.current) {
                clearTimeout(saveTimeoutRef.current);
            }
        };
    }, []);

    return {
        getDraft,
        saveDraft,
        clearDraft,
    };
}

// Hook for managing multiple drafts across different contexts
export function useNoteDrafts() {
    const getAllDrafts = useCallback(() => {
        if (typeof window === "undefined") return [];

        const drafts: Array<{ orderId: string; draft: NoteDraft }> = [];

        try {
            const keys = Object.keys(localStorage);

            keys.forEach((key) => {
                if (key.startsWith(DRAFT_KEY_PREFIX)) {
                    try {
                        const orderId = key.replace(DRAFT_KEY_PREFIX, "");
                        const draftData = localStorage.getItem(key);

                        if (draftData) {
                            const draft = JSON.parse(draftData) as NoteDraft;

                            drafts.push({ orderId, draft });
                        }
                    } catch {
                        // Skip invalid drafts
                    }
                }
            });
        } catch (error) {
            console.error("Error getting all drafts:", error);
        }

        return drafts.sort((a, b) => b.draft.lastSaved - a.draft.lastSaved);
    }, []);

    const clearAllDrafts = useCallback(() => {
        if (typeof window === "undefined") return;

        try {
            const keys = Object.keys(localStorage);

            keys.forEach((key) => {
                if (key.startsWith(DRAFT_KEY_PREFIX)) {
                    localStorage.removeItem(key);
                }
            });
        } catch (error) {
            console.error("Error clearing all drafts:", error);
        }
    }, []);

    return {
        getAllDrafts,
        clearAllDrafts,
    };
}
