import {
  CreatePackingInputSchemaV3,
  PackingDetailInputSchema,
  PackagingSummaryInputSchema,
  PackingFiltersSchema
} from '@/features/packings/schemas/packing-enhanced-schema-v3';

describe('Packing Enhanced Schema V3', () => {
  describe('CreatePackingInputSchemaV3', () => {
    it('should validate a complete valid packing input', () => {
      const validInput = {
        customerId: 'cust-123',
        subCustomerId: 'sub-456',
        date: new Date(),
        transportNotes: 'Handle with care',
        senderName: '<PERSON>',
        senderEmail: '<EMAIL>',
        senderPhone: '1234567890',
        details: [
          {
            orderDetailId: 'od-1',
            orderId: 'order-1',
            garmentSizeId: 'gs-1',
            sizeName: 'M',
            modelId: 'model-1',
            modelName: 'Basic Shirt',
            colorId: 'color-1',
            colorName: 'Blue',
            quantity: 100,
            qualityType: 'primera',
            partida: 'P-001'
          }
        ],
        packagingSummaries: [
          {
            orderId: 'order-1',
            sizeBreakdown: [
              {
                size: 'M',
                totalQuantity: 100,
                boxes: 2,
                bagsFirst: 0,
                bagsSecond: 0,
                loosePieces: 0
              }
            ]
          }
        ]
      };

      const result = CreatePackingInputSchemaV3.safeParse(validInput);
      expect(result.success).toBe(true);
    });

    it('should require customerId', () => {
      const invalidInput = {
        date: new Date(),
        details: [],
        packagingSummaries: []
      };

      const result = CreatePackingInputSchemaV3.safeParse(invalidInput);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('customerId');
      }
    });

    it('should allow optional subCustomerId', () => {
      const inputWithoutSubCustomer = {
        customerId: 'cust-123',
        date: new Date(),
        details: [],
        packagingSummaries: []
      };

      const result = CreatePackingInputSchemaV3.safeParse(inputWithoutSubCustomer);
      expect(result.success).toBe(true);
    });

    it('should validate email format', () => {
      const invalidEmailInput = {
        customerId: 'cust-123',
        date: new Date(),
        senderEmail: 'not-an-email',
        details: [],
        packagingSummaries: []
      };

      const result = CreatePackingInputSchemaV3.safeParse(invalidEmailInput);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => issue.message.includes('email'))).toBe(true);
      }
    });

    it('should require at least one detail', () => {
      const inputWithoutDetails = {
        customerId: 'cust-123',
        date: new Date(),
        details: [],
        packagingSummaries: []
      };

      const result = CreatePackingInputSchemaV3.safeParse(inputWithoutDetails);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => issue.message.includes('al menos un detalle'))).toBe(true);
      }
    });
  });

  describe('PackingDetailInputSchema', () => {
    it('should validate valid quality types', () => {
      const qualityTypes = ['primera', 'segunda', 'manchada', 'incompleta'];
      
      qualityTypes.forEach(qualityType => {
        const detail = {
          orderDetailId: 'od-1',
          orderId: 'order-1',
          garmentSizeId: 'gs-1',
          sizeName: 'M',
          modelId: 'model-1',
          modelName: 'Shirt',
          colorId: 'color-1',
          colorName: 'Blue',
          quantity: 50,
          qualityType,
          partida: 'P-001'
        };

        const result = PackingDetailInputSchema.safeParse(detail);
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid quality types', () => {
      const detail = {
        orderDetailId: 'od-1',
        orderId: 'order-1',
        garmentSizeId: 'gs-1',
        sizeName: 'M',
        modelId: 'model-1',
        modelName: 'Shirt',
        colorId: 'color-1',
        colorName: 'Blue',
        quantity: 50,
        qualityType: 'invalid',
        partida: 'P-001'
      };

      const result = PackingDetailInputSchema.safeParse(detail);
      expect(result.success).toBe(false);
    });

    it('should require positive quantities', () => {
      const detail = {
        orderDetailId: 'od-1',
        orderId: 'order-1',
        garmentSizeId: 'gs-1',
        sizeName: 'M',
        modelId: 'model-1',
        modelName: 'Shirt',
        colorId: 'color-1',
        colorName: 'Blue',
        quantity: 0,
        qualityType: 'primera',
        partida: 'P-001'
      };

      const result = PackingDetailInputSchema.safeParse(detail);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => issue.message.includes('mayor a 0'))).toBe(true);
      }
    });

    it('should allow optional partida', () => {
      const detailWithoutPartida = {
        orderDetailId: 'od-1',
        orderId: 'order-1',
        garmentSizeId: 'gs-1',
        sizeName: 'M',
        modelId: 'model-1',
        modelName: 'Shirt',
        colorId: 'color-1',
        colorName: 'Blue',
        quantity: 50,
        qualityType: 'primera'
      };

      const result = PackingDetailInputSchema.safeParse(detailWithoutPartida);
      expect(result.success).toBe(true);
    });
  });

  describe('PackagingSummaryInputSchema', () => {
    it('should validate size breakdown with non-negative values', () => {
      const summary = {
        orderId: 'order-1',
        sizeBreakdown: [
          {
            size: 'M',
            totalQuantity: 100,
            boxes: 2,
            bagsFirst: 0,
            bagsSecond: 0,
            loosePieces: 0
          },
          {
            size: 'L',
            totalQuantity: 50,
            boxes: 0,
            bagsFirst: 2,
            bagsSecond: 0,
            loosePieces: 0
          }
        ]
      };

      const result = PackagingSummaryInputSchema.safeParse(summary);
      expect(result.success).toBe(true);
    });

    it('should reject negative packaging quantities', () => {
      const summary = {
        orderId: 'order-1',
        sizeBreakdown: [
          {
            size: 'M',
            totalQuantity: 100,
            boxes: -1,
            bagsFirst: 0,
            bagsSecond: 0,
            loosePieces: 0
          }
        ]
      };

      const result = PackagingSummaryInputSchema.safeParse(summary);
      expect(result.success).toBe(false);
    });

    it('should require at least one size breakdown', () => {
      const summary = {
        orderId: 'order-1',
        sizeBreakdown: []
      };

      const result = PackagingSummaryInputSchema.safeParse(summary);
      expect(result.success).toBe(false);
    });
  });

  describe('PackingFiltersSchema', () => {
    it('should validate date range filters', () => {
      const filters = {
        dateFrom: '2024-01-01',
        dateTo: '2024-12-31',
        customerId: 'cust-123'
      };

      const result = PackingFiltersSchema.safeParse(filters);
      expect(result.success).toBe(true);
    });

    it('should accept all optional filters', () => {
      const emptyFilters = {};

      const result = PackingFiltersSchema.safeParse(emptyFilters);
      expect(result.success).toBe(true);
    });

    it('should validate status enum values', () => {
      const validStatuses = ['pending', 'completed', 'cancelled'];
      
      validStatuses.forEach(status => {
        const filters = { status };
        const result = PackingFiltersSchema.safeParse(filters);
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid status values', () => {
      const filters = {
        status: 'invalid-status'
      };

      const result = PackingFiltersSchema.safeParse(filters);
      expect(result.success).toBe(false);
    });

    it('should validate folio pattern', () => {
      const filters = {
        folio: 'PCK-2024-001'
      };

      const result = PackingFiltersSchema.safeParse(filters);
      expect(result.success).toBe(true);
    });

    it('should handle complex filter combinations', () => {
      const filters = {
        customerId: 'cust-123',
        subCustomerId: 'sub-456',
        dateFrom: '2024-01-01',
        dateTo: '2024-12-31',
        status: 'completed',
        folio: 'PCK-2024-001',
        search: 'blue shirt'
      };

      const result = PackingFiltersSchema.safeParse(filters);
      expect(result.success).toBe(true);
    });
  });
});