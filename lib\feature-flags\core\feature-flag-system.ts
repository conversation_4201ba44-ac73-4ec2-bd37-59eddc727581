/**
 * Feature Flag System Core
 * Manages feature flags for gradual rollout and A/B testing
 */

import { EventEmitter } from "events";

import Redis from "ioredis";

export interface FeatureFlag {
    key: string;
    name: string;
    description: string;
    enabled: boolean;
    rolloutPercentage: number;
    enabledForUsers?: string[];
    enabledForRoles?: string[];
    metadata?: {
        created: Date;
        modified: Date;
        owner?: string;
        jiraTicket?: string;
        dependencies?: string[];
        metrics?: string[];
        risk?: "low" | "medium" | "high";
    };
}

export interface FeatureFlagContext {
    userId?: string;
    userRole?: string;
    sessionId?: string;
    requestId?: string;
    [key: string]: string | undefined;
}

export class FeatureFlagSystem extends EventEmitter {
    private static instance: FeatureFlagSystem;
    public redis: Redis;
    public cache: Map<string, FeatureFlag>;
    private updateInterval: NodeJS.Timeout | null = null;

    private constructor() {
        super();
        this.redis = new Redis(
            process.env.REDIS_URL || "redis://localhost:6379",
        );
        this.cache = new Map();
    }

    static getInstance(): FeatureFlagSystem {
        if (!FeatureFlagSystem.instance) {
            FeatureFlagSystem.instance = new FeatureFlagSystem();
        }

        return FeatureFlagSystem.instance;
    }

    async initialize(): Promise<void> {
        try {
            // Test Redis connection
            await this.redis.ping();

            // Load all feature flags
            await this.loadFlags();

            // Set up periodic cache refresh (every 30 seconds)
            this.updateInterval = setInterval(() => {
                this.loadFlags().catch((err) => {
                    this.emit("error", err);
                });
            }, 30000);

            this.emit("initialized");
        } catch (error) {
            this.emit("error", error);
            throw error;
        }
    }

    async loadFlags(): Promise<void> {
        try {
            const keys = await this.redis.keys("feature_flag:*");

            for (const key of keys) {
                const flagData = await this.redis.get(key);

                if (flagData) {
                    const flag = JSON.parse(flagData);

                    this.cache.set(flag.key, flag);
                }
            }

            this.emit("flags-loaded", this.cache.size);
        } catch (error) {
            this.emit("error", error);
            throw error;
        }
    }

    async getFlag(key: string): Promise<FeatureFlag | null> {
        // Check cache first
        if (this.cache.has(key)) {
            return this.cache.get(key)!;
        }

        // Fallback to Redis
        const flagData = await this.redis.get(`feature_flag:${key}`);

        if (flagData) {
            const flag = JSON.parse(flagData);

            this.cache.set(key, flag);

            return flag;
        }

        return null;
    }

    async setFlag(flag: FeatureFlag): Promise<void> {
        flag.metadata = {
            ...flag.metadata,
            modified: new Date(),
        } as any;

        await this.redis.set(`feature_flag:${flag.key}`, JSON.stringify(flag));
        this.cache.set(flag.key, flag);

        this.emit("flag-updated", flag);
    }

    async isEnabled(
        key: string,
        context?: FeatureFlagContext,
    ): Promise<boolean> {
        const flag = await this.getFlag(key);

        if (!flag) {
            return false;
        }

        // Check if globally disabled
        if (!flag.enabled) {
            return false;
        }

        // Check user-specific enablement
        if (context?.userId && flag.enabledForUsers?.includes(context.userId)) {
            return true;
        }

        // Check role-specific enablement
        if (
            context?.userRole &&
            flag.enabledForRoles?.includes(context.userRole)
        ) {
            return true;
        }

        // Check rollout percentage
        if (flag.rolloutPercentage > 0 && flag.rolloutPercentage < 100) {
            const hash = this.hashString(
                context?.sessionId || context?.userId || "",
            );
            const percentage = (hash % 100) + 1;

            return percentage <= flag.rolloutPercentage;
        }

        // If rollout is 100%, flag is enabled for all
        return flag.rolloutPercentage === 100;
    }

    async toggleFlag(key: string, enabled: boolean): Promise<void> {
        const flag = await this.getFlag(key);

        if (!flag) {
            throw new Error(`Feature flag ${key} not found`);
        }

        flag.enabled = enabled;
        await this.setFlag(flag);
    }

    async updateRolloutPercentage(
        key: string,
        percentage: number,
    ): Promise<void> {
        if (percentage < 0 || percentage > 100) {
            throw new Error("Rollout percentage must be between 0 and 100");
        }

        const flag = await this.getFlag(key);

        if (!flag) {
            throw new Error(`Feature flag ${key} not found`);
        }

        flag.rolloutPercentage = percentage;
        await this.setFlag(flag);
    }

    async addUserToFlag(key: string, userId: string): Promise<void> {
        const flag = await this.getFlag(key);

        if (!flag) {
            throw new Error(`Feature flag ${key} not found`);
        }

        if (!flag.enabledForUsers) {
            flag.enabledForUsers = [];
        }

        if (!flag.enabledForUsers.includes(userId)) {
            flag.enabledForUsers.push(userId);
            await this.setFlag(flag);
        }
    }

    async removeUserFromFlag(key: string, userId: string): Promise<void> {
        const flag = await this.getFlag(key);

        if (!flag) {
            throw new Error(`Feature flag ${key} not found`);
        }

        if (flag.enabledForUsers) {
            flag.enabledForUsers = flag.enabledForUsers.filter(
                (id) => id !== userId,
            );
            await this.setFlag(flag);
        }
    }

    async getAllFlags(): Promise<FeatureFlag[]> {
        return Array.from(this.cache.values());
    }

    async deleteFlag(key: string): Promise<void> {
        await this.redis.del(`feature_flag:${key}`);
        this.cache.delete(key);

        this.emit("flag-deleted", key);
    }

    private hashString(str: string): number {
        let hash = 0;

        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);

            hash = (hash << 5) - hash + char;
            hash = hash & hash;
        }

        return Math.abs(hash);
    }

    async checkDependencies(key: string): Promise<boolean> {
        const flag = await this.getFlag(key);

        if (!flag || !flag.metadata?.dependencies) {
            return true;
        }

        for (const dep of flag.metadata.dependencies) {
            const depFlag = await this.getFlag(dep);

            if (!depFlag || !depFlag.enabled) {
                return false;
            }
        }

        return true;
    }

    async getMetrics(key: string): Promise<{
        key: string;
        enabled: boolean;
        usage: number;
        errors: number;
        performance: number;
    }> {
        // Placeholder for metrics integration
        // In a real implementation, this would connect to your metrics system
        return {
            key,
            enabled: await this.isEnabled(key),
            usage: 0,
            errors: 0,
            performance: 0,
        };
    }

    destroy(): void {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        this.redis.disconnect();
        this.removeAllListeners();
    }
}

// Helper function for use in other modules
export function getFeatureFlagSystem(): FeatureFlagSystem {
    return FeatureFlagSystem.getInstance();
}

// React hook helper (for client-side usage)
export async function checkFeatureFlag(
    key: string,
    context?: FeatureFlagContext,
): Promise<boolean> {
    const system = getFeatureFlagSystem();

    return system.isEnabled(key, context);
}
