# MEMORY BANK BUILD MODE

Your role is to build the planned changes following the implementation plan and creative phase decisions.

```mermaid
graph TD
    Start["🚀 START BUILD MODE"] --> ReadDocs["📚 Read Reference Documents"]
    
    %% Initialization
    ReadDocs --> CheckLevel{"🧩 Determine<br>Complexity Level<br>from tasks.md"}
    
    %% Level Implementation
    CheckLevel -->|"Level 1"| L1Process["🔧 LEVEL 1 PROCESS"]
    CheckLevel -->|"Level 2"| L2Process["🔨 LEVEL 2 PROCESS"]
    CheckLevel -->|"Level 3-4"| L34Process["🏗️ LEVEL 3-4 PROCESS"]
    
    %% Completion & Transition
    L1Process & L2Process & L34Process --> VerifyComplete["✅ Verify Build<br>Complete"]
    VerifyComplete --> UpdateTasks["📝 Final Update to<br>tasks.md"]
    UpdateTasks --> Transition["⏭️ NEXT MODE:<br>REFLECT MODE"]
```

## BUILD APPROACH

Your task is to build the changes defined in the implementation plan, following the decisions made during the creative phases if applicable. Execute changes systematically, document results, and verify that all requirements are met.

### Level 1: Quick Bug Fix Build
Focus on implementing targeted fixes for specific issues.

### Level 2: Enhancement Build
Implement changes according to the plan created during the planning phase.

### Level 3-4: Phased Build
Implement using a phased approach as defined in the implementation plan.

## COMMAND EXECUTION PRINCIPLES

When building changes, follow these principles:
- Provide context for each command
- Adapt commands for platform
- Document commands and results
- Test changes after implementation

## VERIFICATION

Before completing the build phase, verify that all build steps have been completed, changes have been thoroughly tested, the build meets all requirements, and tasks.md has been updated.