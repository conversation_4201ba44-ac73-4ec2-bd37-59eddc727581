"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, cn } from "@heroui/react";
import { CheckIcon } from "@heroicons/react/24/solid";
import {
    UserIcon,
    DocumentDuplicateIcon,
    CalculatorIcon,
    ClipboardDocumentCheckIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { useState, useEffect } from "react";

import {
    useProgressTracker,
    ProgressStep,
} from "../../../hooks/useProgressTracker";

export type WizardStep = "contractor" | "orders" | "quantities" | "summary";

interface EnhancedStepIndicatorProps {
    steps?: ProgressStep[];
    currentStep?: number;
    onStepClick?: (stepId: number) => void;
    allowNavigation?: boolean;
    stepsWithErrors?: number[]; // Pasos que tienen errores
    stepsWithWarnings?: number[]; // Pasos que tienen advertencias
}

// Custom hook for responsive design
function useMediaQuery(query: string): boolean {
    const [matches, setMatches] = useState(false);

    useEffect(() => {
        if (typeof window !== "undefined") {
            const media = window.matchMedia(query);

            setMatches(media.matches);

            const listener = (e: MediaQueryListEvent) => {
                setMatches(e.matches);
            };

            media.addEventListener("change", listener);

            return () => media.removeEventListener("change", listener);
        }

        return undefined;
    }, [query]);

    return matches;
}

export const EnhancedStepIndicator = ({
    steps,
    currentStep,
    onStepClick,
    allowNavigation = false,
    stepsWithErrors = [],
    stepsWithWarnings = [],
}: EnhancedStepIndicatorProps) => {
    // Usar el hook para manejar de manera segura el progreso
    const {
        steps: safeSteps,
        currentStep: safeCurrentStep,
        progressWidth,
        isStepActive,
        isStepCompleted,
        canStepBeClicked,
    } = useProgressTracker(currentStep, steps, defaultSteps);

    const isMobile = useMediaQuery("(max-width: 640px)");

    // Definir iconos para cada paso
    const getStepIcon = (stepId: number) => {
        switch (stepId) {
            case 1:
                return <UserIcon className="w-5 h-5" />;
            case 2:
                return <DocumentDuplicateIcon className="w-5 h-5" />;
            case 3:
                return <CalculatorIcon className="w-5 h-5" />;
            case 4:
                return <ClipboardDocumentCheckIcon className="w-5 h-5" />;
            default:
                return <span className="font-medium">{stepId}</span>;
        }
    };

    // Define step labels based on screen size
    const getStepLabel = (step: ProgressStep) => {
        if (isMobile) {
            return null; // Don't show labels on mobile
        }

        return step.label;
    };

    return (
        <div className="w-full mb-8 px-4 py-2">
            <div className="relative">
                {/* Pasos con estado visual mejorado */}
                <div className="relative flex justify-between">
                    {safeSteps.map((step) => {
                        const stepIsActive = isStepActive(step.id);
                        const stepIsCompleted = isStepCompleted(step.id);
                        const isClickable = canStepBeClicked(
                            step.id,
                            allowNavigation,
                        );
                        const stepLabel = getStepLabel(step);
                        const hasError = stepsWithErrors.includes(step.id);
                        const hasWarning = stepsWithWarnings.includes(step.id);

                        return (
                            <div
                                key={step.id}
                                className="flex flex-col items-center relative"
                            >
                                <Tooltip
                                    closeDelay={100}
                                    color={
                                        hasError
                                            ? "danger"
                                            : hasWarning
                                              ? "warning"
                                              : "default"
                                    }
                                    content={
                                        hasError
                                            ? `${step.label} - Tiene errores que corregir`
                                            : hasWarning
                                              ? `${step.label} - Tiene advertencias`
                                              : step.description || step.label
                                    }
                                    delay={200}
                                    placement="top"
                                >
                                    <Button
                                        isIconOnly
                                        aria-current={
                                            stepIsActive ? "step" : undefined
                                        }
                                        aria-label={`Paso ${step.id}: ${step.label}`}
                                        className={cn(
                                            "rounded-full mb-2 z-10 border-2 transition-all duration-200",
                                            isMobile ? "w-9 h-9" : "w-11 h-11",
                                            "focus:ring-2 focus:ring-offset-2 focus:outline-none",
                                            {
                                                // Error state
                                                "bg-red-500 border-red-500 text-white hover:bg-red-600 focus:ring-red-300 animate-pulse":
                                                    hasError && !stepIsActive,
                                                "bg-red-500 border-red-600 text-white hover:bg-red-600 focus:ring-red-300":
                                                    hasError && stepIsActive,
                                                // Warning state
                                                "bg-yellow-500 border-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-300":
                                                    hasWarning &&
                                                    !hasError &&
                                                    !stepIsActive,
                                                // Normal states
                                                "bg-blue-500 border-blue-500 text-white hover:bg-blue-600 focus:ring-blue-300":
                                                    stepIsActive &&
                                                    !hasError &&
                                                    !hasWarning,
                                                "bg-green-500 border-green-500 text-white hover:bg-green-600 focus:ring-green-300":
                                                    stepIsCompleted &&
                                                    !hasError &&
                                                    !hasWarning,
                                                "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400":
                                                    !stepIsActive &&
                                                    !stepIsCompleted &&
                                                    !hasError &&
                                                    !hasWarning,
                                                "shadow-md":
                                                    stepIsActive || hasError,
                                                "cursor-pointer": isClickable,
                                                "cursor-not-allowed opacity-70":
                                                    !isClickable &&
                                                    !stepIsActive,
                                            },
                                        )}
                                        data-step-id={step.id}
                                        data-step-state={
                                            hasError
                                                ? "error"
                                                : hasWarning
                                                  ? "warning"
                                                  : stepIsActive
                                                    ? "active"
                                                    : stepIsCompleted
                                                      ? "completed"
                                                      : "pending"
                                        }
                                        isDisabled={
                                            !isClickable && !stepIsActive
                                        }
                                        size={isMobile ? "sm" : "md"}
                                        onClick={() =>
                                            isClickable &&
                                            onStepClick &&
                                            onStepClick(step.id)
                                        }
                                    >
                                        <motion.div
                                            animate={{
                                                scale: hasError
                                                    ? [1, 1.1, 1]
                                                    : 1,
                                                opacity: 1,
                                                rotate:
                                                    stepIsCompleted && !hasError
                                                        ? [0, 10, -10, 0]
                                                        : 0,
                                            }}
                                            initial={{ scale: 0.8, opacity: 0 }}
                                            transition={{
                                                duration: hasError ? 0.5 : 0.3,
                                                type:
                                                    stepIsCompleted || hasError
                                                        ? "spring"
                                                        : "tween",
                                                stiffness: 200,
                                                repeat: hasError ? Infinity : 0,
                                                repeatDelay: 2,
                                            }}
                                        >
                                            {hasError ? (
                                                <ExclamationTriangleIcon className="w-5 h-5 text-white" />
                                            ) : stepIsCompleted ? (
                                                <CheckIcon className="w-5 h-5 text-white" />
                                            ) : (
                                                getStepIcon(step.id)
                                            )}
                                        </motion.div>
                                    </Button>
                                </Tooltip>

                                {/* Nombre del paso con animación - hidden on mobile */}
                                {stepLabel && (
                                    <motion.span
                                        animate={{
                                            opacity: stepIsActive ? 1 : 0.8,
                                            y: stepIsActive ? 0 : 3,
                                            scale: stepIsActive ? 1.05 : 1,
                                        }}
                                        className={cn(
                                            "text-xs font-medium sm:text-sm max-w-[90px] text-center leading-tight",
                                            {
                                                "text-red-600 dark:text-red-400":
                                                    hasError,
                                                "text-yellow-600 dark:text-yellow-400":
                                                    hasWarning && !hasError,
                                                "text-blue-600 dark:text-blue-400":
                                                    stepIsActive &&
                                                    !hasError &&
                                                    !hasWarning,
                                                "text-green-600 dark:text-green-400":
                                                    stepIsCompleted &&
                                                    !hasError &&
                                                    !hasWarning,
                                                "text-gray-500 dark:text-gray-400":
                                                    !stepIsActive &&
                                                    !stepIsCompleted &&
                                                    !hasError &&
                                                    !hasWarning,
                                            },
                                        )}
                                        initial={{ opacity: 0.6, y: 5 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        {stepLabel}
                                    </motion.span>
                                )}
                            </div>
                        );
                    })}
                </div>

                {/* Barra de progreso única */}
                <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mt-6">
                    <motion.div
                        animate={{
                            width: progressWidth,
                        }}
                        className="h-full bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg"
                        initial={{ width: 0 }}
                        transition={{
                            duration: 0.5,
                            ease: "easeInOut",
                        }}
                    />
                </div>
            </div>
        </div>
    );
};

// Pasos predeterminados del wizard con IDs numéricos
const defaultSteps = [
    { id: 1, label: "Contratista", description: "Seleccionar contratista" },
    { id: 2, label: "Órdenes", description: "Seleccionar órdenes" },
    { id: 3, label: "Cantidades", description: "Asignar cantidades" },
    { id: 4, label: "Resumen", description: "Resumen y confirmación" },
];
