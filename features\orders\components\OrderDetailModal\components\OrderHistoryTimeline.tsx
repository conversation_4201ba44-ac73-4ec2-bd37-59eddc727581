"use client";

import type { ExtendedOrder } from "../types/order-modal.types";

import React from "react";
import { Card, CardBody, Chip } from "@heroui/react";
import {
    ClockIcon,
    CheckCircleIcon,
    PlusCircleIcon,
    PencilIcon,
    ArrowPathIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { useThemeSafe, getThemeColors, formatDate } from "../utils";

interface OrderHistoryTimelineProps {
    order: ExtendedOrder;
}

const generateHistoryEvents = (order: ExtendedOrder) => {
    const events: any[] = [];

    events.push({
        id: "created",
        type: "created",
        icon: PlusCircleIcon,
        color: "text-success",
        title: "Orden creada",
        description: `Orden ${(order as any).code} creada`,
        date: order.createdAt,
        user: (order as any).user?.name || "Sistema",
    });

    order.OrderAssignment?.forEach((assignment) => {
        events.push({
            id: `assignment-${assignment.id}`,
            type: "assigned",
            icon: ArrowPathIcon,
            color: "text-primary",
            title: "Asignación creada",
            description: `${assignment.orderGarmentPart?.part?.name || "Parte"} asignada a ${assignment.contractor?.name || "contratista"}`,
            date: assignment.createdAt,
            user: "Sistema",
        });

        if (assignment.completed && assignment.completedAt) {
            events.push({
                id: `completed-${assignment.id}`,
                type: "completed",
                icon: CheckCircleIcon,
                color: "text-success",
                title: "Asignación completada",
                description: `${assignment.orderGarmentPart?.part?.name || "Parte"} completada por ${assignment.contractor?.name}`,
                date: assignment.completedAt,
                user: assignment.contractor?.name || "Contratista",
            });
        }
    });

    (order as any).OrderNote?.forEach((note: any) => {
        events.push({
            id: `note-${note.id}`,
            type: "note",
            icon: PencilIcon,
            color: "text-warning",
            title: "Nota agregada",
            description:
                note.content.substring(0, 50) +
                (note.content.length > 50 ? "..." : ""),
            date: note.createdAt,
            user: note.user?.name || "Usuario",
        });
    });

    if (order.updatedAt !== order.createdAt) {
        events.push({
            id: "updated",
            type: "updated",
            icon: PencilIcon,
            color: "text-primary",
            title: "Orden actualizada",
            description: "Se actualizó la información de la orden",
            date: order.updatedAt,
            user: "Sistema",
        });
    }

    return events.sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    );
};

export default function OrderHistoryTimeline({
    order,
}: OrderHistoryTimelineProps) {
    const theme = useThemeSafe();
    const colors = getThemeColors(theme);

    const events = generateHistoryEvents(order);

    if (events.length === 0) {
        return (
            <Card className="border border-default-200">
                <CardBody className="p-8 text-center">
                    <ClockIcon className="w-12 h-12 mx-auto mb-4 text-default-300" />
                    <p className="text-default-500">
                        No hay historial disponible
                    </p>
                </CardBody>
            </Card>
        );
    }

    return (
        <div className="space-y-4">
            {events.map((event, index) => {
                const Icon = event.icon;

                return (
                    <motion.div
                        key={event.id}
                        animate={{ opacity: 1, x: 0 }}
                        initial={{ opacity: 0, x: -20 }}
                        transition={{ delay: index * 0.05 }}
                    >
                        <Card className="border border-default-200">
                            <CardBody className="p-4">
                                <div className="flex gap-4">
                                    <div className={`mt-1 ${event.color}`}>
                                        <Icon className="w-5 h-5" />
                                    </div>
                                    <div className="flex-1">
                                        <div className="flex items-center justify-between mb-1">
                                            <h5 className="font-medium text-foreground">
                                                {event.title}
                                            </h5>
                                            <Chip
                                                className="text-xs"
                                                size="sm"
                                                variant="flat"
                                            >
                                                {event.type}
                                            </Chip>
                                        </div>
                                        <p className="text-sm text-default-600 mb-2">
                                            {event.description}
                                        </p>
                                        <div className="flex items-center gap-4 text-xs text-default-500">
                                            <span className="flex items-center gap-1">
                                                <ClockIcon className="w-3 h-3" />
                                                {formatDate(event.date)}
                                            </span>
                                            <span>Por: {event.user}</span>
                                        </div>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </motion.div>
                );
            })}
        </div>
    );
}
