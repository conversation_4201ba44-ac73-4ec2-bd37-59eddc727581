import React from "react";
import {
    CheckCircleIcon,
    ClockIcon,
    CalendarIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

import {
    isToday,
    isTomorrow,
    getDaysDifference,
    formatDate,
    formatRelative,
    adjustTimezoneOffset,
} from "@/shared/utils";

interface Order {
    id: string;
    estimatedDeliveryDate: string | Date | null;
    parts?: Array<{ code: string; [key: string]: any }>;
}

interface UpcomingDeliveryCardProps {
    order: Order;
    className?: string;
    onClick?: (order: Order) => void;
}

/**
 * Componente para mostrar una tarjeta de entrega próxima
 */
export default function UpcomingDeliveryCard({
    order,
    className = "",
    onClick,
}: UpcomingDeliveryCardProps) {
    // Ajustar la fecha para compensar el desfase de zona horaria
    const adjustedDate = adjustTimezoneOffset(order.estimatedDeliveryDate);

    // Manejar el clic en la tarjeta
    const handleClick = () => {
        if (onClick) onClick(order);
    };

    // Si la fecha no es válida, mostrar un mensaje de error
    if (!adjustedDate) {
        return (
            <div
                className={`flex items-center gap-2 p-1.5 rounded-md bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 my-1 shadow-sm cursor-pointer hover:shadow transition-all ${className}`}
                onClick={handleClick}
            >
                <ExclamationCircleIcon className="w-4 h-4 text-red-500 flex-shrink-0" />
                <div className="flex flex-col">
                    <span className="text-red-600 dark:text-red-400 text-xs font-medium">
                        Formato inválido
                    </span>
                    <span className="text-[10px] text-red-500 truncate">
                        {order.parts && order.parts.length > 0
                            ? `Partida: ${order.parts[0].code}`
                            : `Orden ${order.id.substring(0, 6)}...`}
                    </span>
                </div>
            </div>
        );
    }

    // Formatear la fecha como DD MMM (ej: 07 abr)
    const formattedDate = formatDate(adjustedDate, "dd MMM");

    // Obtener el texto relativo
    const relativeText = formatRelative(adjustedDate);

    // Determinar las clases CSS según el estado
    let bgClass = "";
    let textClass = "";
    let iconComponent: React.ReactNode = null;

    const diffDays = getDaysDifference(adjustedDate);

    if (diffDays < 0) {
        // Atrasada
        bgClass =
            "bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/30 border-red-200 dark:border-red-800";
        textClass = "text-red-700 dark:text-red-300";
        iconComponent = (
            <ExclamationCircleIcon className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" />
        );
    } else if (isToday(adjustedDate)) {
        // Hoy
        bgClass =
            "bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/30 dark:to-amber-800/30 border-amber-200 dark:border-amber-800";
        textClass = "text-amber-700 dark:text-amber-300";
        iconComponent = (
            <CheckCircleIcon className="w-4 h-4 text-amber-600 dark:text-amber-400 flex-shrink-0" />
        );
    } else if (isTomorrow(adjustedDate)) {
        // Mañana
        bgClass =
            "bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30 border-orange-200 dark:border-orange-800";
        textClass = "text-orange-700 dark:text-orange-300";
        iconComponent = (
            <ClockIcon className="w-4 h-4 text-orange-600 dark:text-orange-400 flex-shrink-0" />
        );
    } else if (diffDays > 0 && diffDays <= 7) {
        // Próxima semana
        bgClass =
            "bg-gradient-to-r from-blue-50/80 to-cyan-50/80 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200/80 dark:border-blue-800/50";
        textClass = "text-blue-600 dark:text-blue-300";
        iconComponent = (
            <ClockIcon className="w-4 h-4 text-blue-500 dark:text-blue-400 flex-shrink-0" />
        );
    } else {
        // Más lejanas
        bgClass =
            "bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/30 dark:to-cyan-900/30 border-blue-200 dark:border-blue-800";
        textClass = "text-blue-700 dark:text-blue-300";
        iconComponent = (
            <CalendarIcon className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
        );
    }

    // Obtener el código de la partida si existe
    const partCode =
        order.parts && order.parts.length > 0
            ? order.parts[0].code
            : order.id.substring(0, 6) + "...";

    return (
        <div
            className={`flex items-center gap-2 p-1.5 rounded-md ${bgClass} my-1 shadow-sm hover:shadow-md transition-all cursor-pointer ${className}`}
            onClick={handleClick}
        >
            {iconComponent}
            <div className="flex flex-col">
                <span className={`${textClass} text-xs font-medium`}>
                    {formattedDate} - {relativeText}
                </span>
                <span className={`text-[10px] ${textClass} font-mono truncate`}>
                    {partCode}
                </span>
            </div>
        </div>
    );
}
