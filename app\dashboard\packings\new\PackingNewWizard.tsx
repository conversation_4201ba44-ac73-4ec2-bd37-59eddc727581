"use client";

import type {
    CreatePackingInput,
    PackingDetailFormData,
    QualityType,
    PackagingType,
} from "@/lib/types/packing";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Progress,
    Input,
    Textarea,
    Select,
    SelectItem,
    Divider,
    Chip,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
} from "@heroui/react";
import {
    Package,
    FileText,
    AlertCircle,
    CheckCircle,
    ChevronLeft,
    ChevronRight,
    Plus,
    Trash2,
    Upload,
    Save,
    Eye,
    Calculator,
} from "lucide-react";
import { format } from "date-fns";

import { CustomerSelector } from "@/features/customers/components/hierarchy/CustomerSelector";
import {
    PackingQualitySelector,
    QualityBadge,
} from "@/features/packings/components/PackingQualitySelector";
import { createPackingEnhanced } from "@/features/packings/actions/create-packing-enhanced";
import {
    importFromOrder,
    getOrdersForPacking,
} from "@/features/packings/actions/import-from-order";
import { calculatePackaging } from "@/features/packings/actions/calculate-packaging";
import { getCompanySettings } from "@/features/settings/actions/company-settings";

interface PackingNewWizardProps {
    initialCustomerId?: string;
    initialOrderId?: string;
}

const steps = [
    {
        id: "general",
        title: "Información General",
        icon: FileText,
        description: "Datos básicos del packing",
    },
    {
        id: "products",
        title: "Selección de Productos",
        icon: Package,
        description: "Productos a empacar",
    },
    {
        id: "packaging",
        title: "Configuración de Empaque",
        icon: Calculator,
        description: "Tipo y cantidad de empaque",
    },
    {
        id: "summary",
        title: "Resumen y Notas",
        icon: CheckCircle,
        description: "Revisión final",
    },
];

export function PackingNewWizard({
    initialCustomerId,
    initialOrderId,
}: PackingNewWizardProps) {
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isImporting, setIsImporting] = useState(false);
    const [isCalculating, setIsCalculating] = useState(false);
    const { isOpen, onOpen, onClose } = useDisclosure();

    // Form data
    const [formData, setFormData] = useState<Partial<CreatePackingInput>>({
        customerId: initialCustomerId || "",
        orderId: initialOrderId || "",
        deliveryDate: new Date(),
        notes: "",
        transportNotes: "",
        packingType: null,
        details: [],
    });

    const [orderOptions, setOrderOptions] = useState<any[]>([]);
    const [companyInfo, setCompanyInfo] = useState<any>(null);
    const [calculationResult, setCalculationResult] = useState<any>(null);

    // Load company settings
    useEffect(() => {
        getCompanySettings().then((result) => {
            if (result.success && result.data) {
                setCompanyInfo({
                    name: result.data.companyName,
                    logo: result.data.companyLogo,
                    rfc: result.data.rfc,
                    address: result.data.address,
                    city: result.data.city,
                    state: result.data.state,
                    postalCode: result.data.postalCode,
                    country: result.data.country,
                    phone: result.data.phone,
                    email: result.data.email,
                    website: result.data.website,
                });
            }
        });
    }, []);

    // Load orders when customer changes
    useEffect(() => {
        if (formData.customerId) {
            getOrdersForPacking(formData.customerId).then((orders) => {
                setOrderOptions(orders);
            });
        }
    }, [formData.customerId]);

    const handleNext = () => {
        if (currentStep < steps.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handlePrevious = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleImportFromOrder = async () => {
        if (!formData.orderId) {
            addToast({
                title: "Error",
                description: "Seleccione una orden primero",
                color: "danger",
            });

            return;
        }

        setIsImporting(true);
        try {
            const result = await importFromOrder({
                orderId: formData.orderId,
                includeAllItems: true,
            });

            if (result.success && result.data) {
                const suggestedDetails = result.data.suggestedDetails;

                setFormData((prev) => ({
                    ...prev,
                    details: suggestedDetails,
                }));
                addToast({
                    title: "Éxito",
                    description: `${suggestedDetails.length} productos importados`,
                    color: "success",
                });
                handleNext();
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al importar",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al importar desde la orden",
                color: "danger",
            });
        } finally {
            setIsImporting(false);
        }
    };

    const handleCalculatePackaging = async () => {
        if (!formData.details || formData.details.length === 0) {
            addToast({
                title: "Error",
                description: "Agregue productos primero",
                color: "danger",
            });

            return;
        }

        setIsCalculating(true);
        try {
            const result = await calculatePackaging({
                items: formData.details.map((detail) => ({
                    garmentSizeId: detail.garmentSizeId,
                    quantity: detail.quantity,
                    qualityType: detail.qualityType || "primera",
                    preferredPackaging:
                        detail.packagingType === null
                            ? undefined
                            : detail.packagingType,
                })),
            });

            if (result.success && result.data) {
                const data = result.data;

                setCalculationResult(data);

                // Update details with calculated values
                const updatedDetails = formData.details.map((detail, index) => {
                    const calculation = data.calculations[index];

                    if (calculation) {
                        return {
                            ...detail,
                            packagingType: calculation.packagingType,
                            packagingUnits: calculation.packagingUnits,
                            piecesPerUnit: calculation.piecesPerUnit,
                            loosePieces: calculation.loosePieces,
                        };
                    }

                    return detail;
                });

                setFormData((prev) => ({
                    ...prev,
                    details: updatedDetails,
                    packingType:
                        data.summary.totalBoxes > 0 &&
                        data.summary.totalBags > 0
                            ? "mixto"
                            : data.summary.totalBoxes > 0
                              ? "cajas"
                              : "bolsas",
                }));

                addToast({
                    title: "Éxito",
                    description: "Cálculo de empaque completado",
                    color: "success",
                });
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al calcular",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al calcular empaque",
                color: "danger",
            });
        } finally {
            setIsCalculating(false);
        }
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);
        try {
            const result = await createPackingEnhanced({
                ...formData,
                companyInfo,
                deliveryDate: formData.deliveryDate || new Date(),
                details: formData.details || [],
            } as CreatePackingInput);

            if (result.success && result.data) {
                addToast({
                    title: "Éxito",
                    description: "Packing creado exitosamente",
                    color: "success",
                });
                router.push(`/dashboard/packings/${result.data.id}`);
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al crear el packing",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al crear el packing",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const addProduct = () => {
        setFormData((prev) => ({
            ...prev,
            details: [
                ...(prev.details || []),
                {
                    garmentSizeId: "",
                    modelCode: "",
                    colorName: "",
                    partNumber: "",
                    quantity: 0,
                    qualityType: "primera" as QualityType,
                    packagingType: "caja" as PackagingType,
                    packagingUnits: 0,
                    piecesPerUnit: 0,
                    loosePieces: 0,
                },
            ],
        }));
    };

    const removeProduct = (index: number) => {
        setFormData((prev) => ({
            ...prev,
            details: prev.details?.filter((_, i) => i !== index) || [],
        }));
    };

    const updateProduct = (
        index: number,
        updates: Partial<PackingDetailFormData>,
    ) => {
        setFormData((prev) => ({
            ...prev,
            details:
                prev.details?.map((detail, i) =>
                    i === index ? { ...detail, ...updates } : detail,
                ) || [],
        }));
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 0: // General Info
                return (
                    <div className="space-y-6">
                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                Información del Packing
                            </h3>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="md:col-span-2">
                                    <CustomerSelector
                                        isRequired
                                        label="Cliente / Subcliente"
                                        placeholder="Seleccione el destinatario"
                                        value={{
                                            customerId:
                                                formData.customerId || "",
                                            subCustomerId:
                                                formData.subCustomerId === null
                                                    ? undefined
                                                    : formData.subCustomerId,
                                        }}
                                        onChange={({
                                            customerId,
                                            subCustomerId,
                                        }) => {
                                            setFormData((prev) => ({
                                                ...prev,
                                                customerId,
                                                subCustomerId,
                                            }));
                                        }}
                                    />
                                </div>

                                <Select
                                    endContent={
                                        formData.orderId && (
                                            <Button
                                                color="primary"
                                                isLoading={isImporting}
                                                size="sm"
                                                startContent={
                                                    <Upload className="w-4 h-4" />
                                                }
                                                variant="flat"
                                                onPress={handleImportFromOrder}
                                            >
                                                Importar
                                            </Button>
                                        )
                                    }
                                    label="Orden de Trabajo (Opcional)"
                                    placeholder="Seleccione una orden"
                                    selectedKeys={
                                        formData.orderId
                                            ? [formData.orderId]
                                            : []
                                    }
                                    onSelectionChange={(keys) => {
                                        const orderId = Array.from(
                                            keys,
                                        )[0] as string;

                                        setFormData((prev) => ({
                                            ...prev,
                                            orderId,
                                        }));
                                    }}
                                >
                                    {orderOptions.map((order) => (
                                        <SelectItem key={order.id}>
                                            {order.label}
                                        </SelectItem>
                                    ))}
                                </Select>

                                <Input
                                    isRequired
                                    label="Fecha de Entrega"
                                    type="date"
                                    value={format(
                                        formData.deliveryDate || new Date(),
                                        "yyyy-MM-dd",
                                    )}
                                    onChange={(e) => {
                                        setFormData((prev) => ({
                                            ...prev,
                                            deliveryDate: new Date(
                                                e.target.value,
                                            ),
                                        }));
                                    }}
                                />
                            </div>
                        </div>

                        <Divider />

                        <div>
                            <h3 className="text-lg font-semibold mb-4">
                                Información del Remitente
                            </h3>
                            {companyInfo ? (
                                <Card className="bg-default-50">
                                    <CardBody className="p-4">
                                        <div className="flex items-start gap-4">
                                            {companyInfo.logo && (
                                                <img
                                                    alt={companyInfo.name}
                                                    className="w-16 h-16 object-contain"
                                                    src={companyInfo.logo}
                                                />
                                            )}
                                            <div className="flex-1">
                                                <p className="font-semibold">
                                                    {companyInfo.name}
                                                </p>
                                                {companyInfo.rfc && (
                                                    <p className="text-sm text-default-600">
                                                        RFC: {companyInfo.rfc}
                                                    </p>
                                                )}
                                                {companyInfo.address && (
                                                    <p className="text-sm text-default-600">
                                                        {companyInfo.address},{" "}
                                                        {companyInfo.city},{" "}
                                                        {companyInfo.state}
                                                    </p>
                                                )}
                                                {companyInfo.phone && (
                                                    <p className="text-sm text-default-600">
                                                        Tel: {companyInfo.phone}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </CardBody>
                                </Card>
                            ) : (
                                <Card className="bg-warning-50">
                                    <CardBody className="p-4">
                                        <div className="flex items-center gap-2">
                                            <AlertCircle className="w-5 h-5 text-warning" />
                                            <p className="text-sm">
                                                Configure la información de su
                                                empresa en Ajustes
                                            </p>
                                        </div>
                                    </CardBody>
                                </Card>
                            )}
                        </div>
                    </div>
                );

            case 1: // Products Selection
                return (
                    <div className="space-y-6">
                        <div className="flex justify-between items-center">
                            <h3 className="text-lg font-semibold">
                                Productos a Empacar
                            </h3>
                            <Button
                                color="primary"
                                startContent={<Plus className="w-4 h-4" />}
                                variant="flat"
                                onPress={addProduct}
                            >
                                Agregar Producto
                            </Button>
                        </div>

                        {formData.details && formData.details.length > 0 ? (
                            <div className="space-y-4">
                                {formData.details.map((detail, index) => (
                                    <Card key={index} className="border-1">
                                        <CardBody className="p-4">
                                            <div className="flex justify-between items-start mb-4">
                                                <h4 className="font-medium">
                                                    Producto {index + 1}
                                                </h4>
                                                <Button
                                                    isIconOnly
                                                    color="danger"
                                                    size="sm"
                                                    variant="light"
                                                    onPress={() =>
                                                        removeProduct(index)
                                                    }
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </Button>
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <Input
                                                    isRequired
                                                    label="Código de Modelo"
                                                    value={
                                                        detail.modelCode || ""
                                                    }
                                                    onChange={(e) =>
                                                        updateProduct(index, {
                                                            modelCode:
                                                                e.target.value,
                                                        })
                                                    }
                                                />

                                                <Input
                                                    isRequired
                                                    label="Color"
                                                    value={
                                                        detail.colorName || ""
                                                    }
                                                    onChange={(e) =>
                                                        updateProduct(index, {
                                                            colorName:
                                                                e.target.value,
                                                        })
                                                    }
                                                />

                                                <Input
                                                    label="Número de Partida"
                                                    value={
                                                        detail.partNumber || ""
                                                    }
                                                    onChange={(e) =>
                                                        updateProduct(index, {
                                                            partNumber:
                                                                e.target.value,
                                                        })
                                                    }
                                                />

                                                <Input
                                                    isRequired
                                                    label="Cantidad"
                                                    type="number"
                                                    value={detail.quantity.toString()}
                                                    onChange={(e) =>
                                                        updateProduct(index, {
                                                            quantity:
                                                                parseInt(
                                                                    e.target
                                                                        .value,
                                                                ) || 0,
                                                        })
                                                    }
                                                />
                                            </div>

                                            <div className="mt-4">
                                                <p className="text-sm font-medium mb-2">
                                                    Clasificación de Calidad
                                                </p>
                                                <PackingQualitySelector
                                                    value={
                                                        detail.qualityType ||
                                                        "primera"
                                                    }
                                                    onChange={(qualityType) =>
                                                        updateProduct(index, {
                                                            qualityType,
                                                        })
                                                    }
                                                />
                                            </div>
                                        </CardBody>
                                    </Card>
                                ))}
                            </div>
                        ) : (
                            <Card className="bg-default-50">
                                <CardBody className="p-8 text-center">
                                    <Package className="w-12 h-12 text-default-400 mx-auto mb-4" />
                                    <p className="text-default-600 mb-4">
                                        No hay productos agregados
                                    </p>
                                    <Button
                                        color="primary"
                                        startContent={
                                            <Plus className="w-4 h-4" />
                                        }
                                        variant="flat"
                                        onPress={addProduct}
                                    >
                                        Agregar Primer Producto
                                    </Button>
                                </CardBody>
                            </Card>
                        )}
                    </div>
                );

            case 2: // Packaging Configuration
                return (
                    <div className="space-y-6">
                        <div className="flex justify-between items-center">
                            <h3 className="text-lg font-semibold">
                                Configuración de Empaque
                            </h3>
                            <Button
                                color="primary"
                                isLoading={isCalculating}
                                startContent={
                                    <Calculator className="w-4 h-4" />
                                }
                                variant="flat"
                                onPress={handleCalculatePackaging}
                            >
                                Calcular Automático
                            </Button>
                        </div>

                        {calculationResult && (
                            <Card className="bg-primary-50 dark:bg-primary-900/20">
                                <CardBody className="p-4">
                                    <h4 className="font-medium mb-2">
                                        Resultado del Cálculo
                                    </h4>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <p className="text-default-600">
                                                Total Cajas
                                            </p>
                                            <p className="text-2xl font-semibold">
                                                {
                                                    calculationResult.summary
                                                        .totalBoxes
                                                }
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-default-600">
                                                Total Bolsas
                                            </p>
                                            <p className="text-2xl font-semibold">
                                                {
                                                    calculationResult.summary
                                                        .totalBags
                                                }
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-default-600">
                                                Piezas Sueltas
                                            </p>
                                            <p className="text-2xl font-semibold">
                                                {
                                                    calculationResult.summary
                                                        .totalLoosePieces
                                                }
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-default-600">
                                                Eficiencia
                                            </p>
                                            <p className="text-2xl font-semibold">
                                                {
                                                    calculationResult.summary
                                                        .packingEfficiency
                                                }
                                                %
                                            </p>
                                        </div>
                                    </div>

                                    {calculationResult.recommendations.length >
                                        0 && (
                                        <div className="mt-4 space-y-2">
                                            <p className="text-sm font-medium">
                                                Recomendaciones:
                                            </p>
                                            {calculationResult.recommendations.map(
                                                (rec: string, idx: number) => (
                                                    <div
                                                        key={idx}
                                                        className="flex items-start gap-2"
                                                    >
                                                        <AlertCircle className="w-4 h-4 text-warning mt-0.5" />
                                                        <p className="text-sm text-default-600">
                                                            {rec}
                                                        </p>
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    )}
                                </CardBody>
                            </Card>
                        )}

                        <div className="space-y-4">
                            {formData.details?.map((detail, index) => (
                                <Card key={index} className="border-1">
                                    <CardBody className="p-4">
                                        <div className="flex items-center justify-between mb-4">
                                            <div className="flex items-center gap-3">
                                                <h4 className="font-medium">
                                                    {detail.modelCode} -{" "}
                                                    {detail.colorName}
                                                </h4>
                                                <QualityBadge
                                                    quality={
                                                        detail.qualityType ||
                                                        "primera"
                                                    }
                                                />
                                                <Chip size="sm" variant="flat">
                                                    {detail.quantity} pzs
                                                </Chip>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                            <Select
                                                label="Tipo de Empaque"
                                                selectedKeys={
                                                    detail.packagingType
                                                        ? [detail.packagingType]
                                                        : []
                                                }
                                                onSelectionChange={(keys) => {
                                                    const type = Array.from(
                                                        keys,
                                                    )[0] as PackagingType;

                                                    updateProduct(index, {
                                                        packagingType: type,
                                                    });
                                                }}
                                            >
                                                <SelectItem key="caja">
                                                    Caja
                                                </SelectItem>
                                                <SelectItem key="bolsa">
                                                    Bolsa
                                                </SelectItem>
                                            </Select>

                                            <Input
                                                label="Unidades"
                                                type="number"
                                                value={
                                                    detail.packagingUnits?.toString() ||
                                                    "0"
                                                }
                                                onChange={(e) =>
                                                    updateProduct(index, {
                                                        packagingUnits:
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                    })
                                                }
                                            />

                                            <Input
                                                label="Piezas por Unidad"
                                                type="number"
                                                value={
                                                    detail.piecesPerUnit?.toString() ||
                                                    "0"
                                                }
                                                onChange={(e) =>
                                                    updateProduct(index, {
                                                        piecesPerUnit:
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                    })
                                                }
                                            />

                                            <Input
                                                label="Piezas Sueltas"
                                                type="number"
                                                value={
                                                    detail.loosePieces?.toString() ||
                                                    "0"
                                                }
                                                onChange={(e) =>
                                                    updateProduct(index, {
                                                        loosePieces:
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                    })
                                                }
                                            />
                                        </div>
                                    </CardBody>
                                </Card>
                            ))}
                        </div>
                    </div>
                );

            case 3: // Summary
                return (
                    <div className="space-y-6">
                        <h3 className="text-lg font-semibold">
                            Resumen del Packing
                        </h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <Card>
                                <CardHeader className="pb-3">
                                    <h4 className="font-medium">
                                        Información General
                                    </h4>
                                </CardHeader>
                                <CardBody className="pt-0 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Cliente:
                                        </span>
                                        <span className="font-medium">
                                            {formData.customerId}
                                        </span>
                                    </div>
                                    {formData.subCustomerId && (
                                        <div className="flex justify-between">
                                            <span className="text-default-600">
                                                Subcliente:
                                            </span>
                                            <span className="font-medium">
                                                {formData.subCustomerId}
                                            </span>
                                        </div>
                                    )}
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Fecha Entrega:
                                        </span>
                                        <span className="font-medium">
                                            {format(
                                                formData.deliveryDate ||
                                                    new Date(),
                                                "dd/MM/yyyy",
                                            )}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Total Productos:
                                        </span>
                                        <span className="font-medium">
                                            {formData.details?.length || 0}
                                        </span>
                                    </div>
                                </CardBody>
                            </Card>

                            <Card>
                                <CardHeader className="pb-3">
                                    <h4 className="font-medium">
                                        Resumen de Empaque
                                    </h4>
                                </CardHeader>
                                <CardBody className="pt-0 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Total Cajas:
                                        </span>
                                        <span className="font-medium">
                                            {formData.details?.reduce(
                                                (sum, d) =>
                                                    sum +
                                                    (d.packagingType === "caja"
                                                        ? d.packagingUnits || 0
                                                        : 0),
                                                0,
                                            ) || 0}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Total Bolsas:
                                        </span>
                                        <span className="font-medium">
                                            {formData.details?.reduce(
                                                (sum, d) =>
                                                    sum +
                                                    (d.packagingType === "bolsa"
                                                        ? d.packagingUnits || 0
                                                        : 0),
                                                0,
                                            ) || 0}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-default-600">
                                            Total Piezas:
                                        </span>
                                        <span className="font-medium">
                                            {formData.details?.reduce(
                                                (sum, d) => sum + d.quantity,
                                                0,
                                            ) || 0}
                                        </span>
                                    </div>
                                </CardBody>
                            </Card>
                        </div>

                        <div className="space-y-4">
                            <Textarea
                                label="Notas Generales"
                                minRows={3}
                                placeholder="Agregar notas o instrucciones especiales..."
                                value={formData.notes || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        notes: e.target.value,
                                    }))
                                }
                            />

                            <Textarea
                                label="Notas de Transporte"
                                minRows={3}
                                placeholder="Instrucciones especiales para el transportista..."
                                value={formData.transportNotes || ""}
                                onChange={(e) =>
                                    setFormData((prev) => ({
                                        ...prev,
                                        transportNotes: e.target.value,
                                    }))
                                }
                            />
                        </div>

                        <Card className="bg-warning-50 dark:bg-warning-900/20">
                            <CardBody className="p-4">
                                <div className="flex items-start gap-3">
                                    <AlertCircle className="w-5 h-5 text-warning mt-0.5" />
                                    <div className="space-y-1">
                                        <p className="font-medium text-sm">
                                            Antes de confirmar:
                                        </p>
                                        <ul className="text-sm text-default-600 space-y-1 list-disc list-inside">
                                            <li>
                                                Verifique que las cantidades
                                                sean correctas
                                            </li>
                                            <li>
                                                Confirme la clasificación de
                                                calidad de cada producto
                                            </li>
                                            <li>
                                                Revise la configuración de
                                                empaque
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </div>
                );
        }
    };

    return (
        <div className="max-w-6xl mx-auto">
            {/* Progress Bar */}
            <Card className="mb-6">
                <CardBody className="p-6">
                    <div className="flex items-center justify-between mb-4">
                        {steps.map((step, index) => {
                            const Icon = step.icon;
                            const isActive = index === currentStep;
                            const isCompleted = index < currentStep;

                            return (
                                <div
                                    key={step.id}
                                    className="flex items-center flex-1"
                                >
                                    <div className="flex items-center">
                                        <div
                                            className={`
                      flex items-center justify-center w-10 h-10 rounded-full
                      ${
                          isActive
                              ? "bg-primary text-white"
                              : isCompleted
                                ? "bg-success text-white"
                                : "bg-default-200 text-default-600"
                      }
                    `}
                                        >
                                            {isCompleted ? (
                                                <CheckCircle className="w-5 h-5" />
                                            ) : (
                                                <Icon className="w-5 h-5" />
                                            )}
                                        </div>
                                        <div className="ml-3">
                                            <p
                                                className={`text-sm font-medium ${isActive ? "text-primary" : "text-default-600"}`}
                                            >
                                                {step.title}
                                            </p>
                                            <p className="text-xs text-default-500">
                                                {step.description}
                                            </p>
                                        </div>
                                    </div>
                                    {index < steps.length - 1 && (
                                        <div
                                            className={`
                      flex-1 h-1 mx-4 rounded
                      ${index < currentStep ? "bg-success" : "bg-default-200"}
                    `}
                                        />
                                    )}
                                </div>
                            );
                        })}
                    </div>
                    <Progress
                        className="mt-2"
                        color="primary"
                        size="sm"
                        value={((currentStep + 1) / steps.length) * 100}
                    />
                </CardBody>
            </Card>

            {/* Step Content */}
            <Card>
                <CardBody className="p-6">
                    <AnimatePresence mode="wait">
                        <motion.div
                            key={currentStep}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -20 }}
                            initial={{ opacity: 0, x: 20 }}
                            transition={{ duration: 0.3 }}
                        >
                            {renderStepContent()}
                        </motion.div>
                    </AnimatePresence>
                </CardBody>
            </Card>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
                <Button
                    isDisabled={currentStep === 0 || isSubmitting}
                    startContent={<ChevronLeft className="w-4 h-4" />}
                    variant="light"
                    onPress={handlePrevious}
                >
                    Anterior
                </Button>

                <div className="flex gap-2">
                    {currentStep === steps.length - 1 ? (
                        <>
                            <Button
                                color="primary"
                                startContent={<Eye className="w-4 h-4" />}
                                variant="flat"
                                onPress={onOpen}
                            >
                                Vista Previa
                            </Button>
                            <Button
                                color="primary"
                                endContent={<Save className="w-4 h-4" />}
                                isLoading={isSubmitting}
                                onPress={handleSubmit}
                            >
                                Crear Packing
                            </Button>
                        </>
                    ) : (
                        <Button
                            color="primary"
                            endContent={<ChevronRight className="w-4 h-4" />}
                            isDisabled={
                                (currentStep === 0 && !formData.customerId) ||
                                (currentStep === 1 &&
                                    (!formData.details ||
                                        formData.details.length === 0))
                            }
                            onPress={handleNext}
                        >
                            Siguiente
                        </Button>
                    )}
                </div>
            </div>

            {/* Preview Modal */}
            <Modal
                isOpen={isOpen}
                scrollBehavior="inside"
                size="2xl"
                onClose={onClose}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader>Vista Previa del Packing</ModalHeader>
                            <ModalBody>
                                <pre className="text-xs bg-default-100 p-4 rounded-lg overflow-auto">
                                    {JSON.stringify(formData, null, 2)}
                                </pre>
                            </ModalBody>
                            <ModalFooter>
                                <Button variant="light" onPress={onClose}>
                                    Cerrar
                                </Button>
                                <Button
                                    color="primary"
                                    isLoading={isSubmitting}
                                    onPress={() => {
                                        onClose();
                                        handleSubmit();
                                    }}
                                >
                                    Confirmar y Crear
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </div>
    );
}
