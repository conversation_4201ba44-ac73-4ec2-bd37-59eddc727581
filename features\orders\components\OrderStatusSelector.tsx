"use client";

import React, { useMemo } from "react";
import {
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    <PERSON><PERSON>,
    <PERSON>,
    Spinner,
    cn,
    Selection,
    DropdownSection,
} from "@heroui/react";
import {
    CheckCircleIcon,
    InboxIcon,
    XCircleIcon,
    CubeIcon,
    ClockIcon,
    ExclamationCircleIcon,
    CalendarIcon,
    DocumentIcon,
    CogIcon,
    CheckBadgeIcon,
    TruckIcon,
    SparklesIcon,
} from "@heroicons/react/24/outline";
import { ChevronDownIcon } from "@heroicons/react/20/solid";

import { OrderStatus } from "@/features/orders/types/orders";

// Mapeo de nombres de íconos a componentes JSX - actualizado con todos los iconos de BD
const iconMap: Record<string, JSX.Element> = {
    CheckCircleIcon: <CheckCircleIcon className="w-4 h-4" />,
    InboxIcon: <InboxIcon className="w-4 h-4" />,
    XCircleIcon: <XCircleIcon className="w-4 h-4" />,
    CubeIcon: <CubeIcon className="w-4 h-4" />,
    ClockIcon: <ClockIcon className="w-4 h-4" />,
    ExclamationCircleIcon: <ExclamationCircleIcon className="w-4 h-4" />,
    CalendarIcon: <CalendarIcon className="w-4 h-4" />,
    DocumentIcon: <DocumentIcon className="w-4 h-4" />,
    CogIcon: <CogIcon className="w-4 h-4" />,
    CheckBadgeIcon: <CheckBadgeIcon className="w-4 h-4" />,
    TruckIcon: <TruckIcon className="w-4 h-4" />,
    SparklesIcon: <SparklesIcon className="w-4 h-4" />,
};

interface OrderStatusSelectorProps {
    statuses: OrderStatus[];
    selectedStatus?: OrderStatus | Selection | null;
    onChange: (status: OrderStatus | Selection) => void;
    isLoading?: boolean;
    label?: string;
    isDisabled?: boolean;
    size?: "sm" | "md" | "lg";
    variant?: "bordered" | "flat" | "solid";
    placeholder?: string;
    className?: string;
    errorMessage?: string;
    _id?: string;
}

// Función auxiliar para verificar si un valor es de tipo OrderStatus
function isOrderStatus(value: any): value is OrderStatus {
    return (
        value && typeof value === "object" && "id" in value && "name" in value
    );
}

// Interfaz para una descripción de estado - no forma parte del tipo OrderStatus original
interface StatusDescriptions {
    [key: string]: string;
}

// Descripciones para los estados más comunes
const statusDescriptions: StatusDescriptions = {
    nuevo: "Pedido recién recibido",
    produccion: "En proceso de fabricación",
    completado: "Pedido terminado y listo para entregar",
    cancelado: "Pedido cancelado",
    // Añadir más descripciones según sea necesario
};

export default function OrderStatusSelector({
    statuses,
    selectedStatus,
    onChange,
    isLoading = false,
    label = "Estado",
    isDisabled = false,
    size = "md",
    variant = "bordered",
    placeholder = "Seleccionar estado",
    className,
    errorMessage,
    _id,
}: OrderStatusSelectorProps) {
    // Memorizar los estados para evitar re-renders innecesarios
    const sortedStatuses = useMemo(() => {
        return [...statuses].sort((a, b) => a.name.localeCompare(b.name));
    }, [statuses]);

    // Renderizar el ícono para un estado
    const renderStatusIcon = (status: OrderStatus) => {
        if (!status.iconName) return null;

        // Mapeo de respaldo por si el iconName no coincide exactamente
        const iconBackup: Record<string, JSX.Element> = {
            Nuevo: <SparklesIcon className="w-4 h-4" />,
            "En producción": <CogIcon className="w-4 h-4" />,
            Empaquetando: <CubeIcon className="w-4 h-4" />,
            "Listo para entregar": <TruckIcon className="w-4 h-4" />,
            Entregado: <CheckCircleIcon className="w-4 h-4" />,
            Recibido: <InboxIcon className="w-4 h-4" />,
            "Control de calidad": <CheckBadgeIcon className="w-4 h-4" />,
            Rechazado: <ExclamationCircleIcon className="w-4 h-4" />,
            Cancelado: <XCircleIcon className="w-4 h-4" />,
        };

        return iconMap[status.iconName] || iconBackup[status.name] || null;
    };

    // Generar el estilo de color para un chip basado en el color del estado
    const getChipColorStyle = (status: OrderStatus) => {
        if (!status.color) return {};

        return {
            backgroundColor: `${status.color}15`, // Color con 15% de opacidad
            color: status.color,
            borderColor: `${status.color}30`, // Color con 30% de opacidad para el borde
        };
    };

    // Renderizar el estado seleccionado como un chip
    const renderSelectedStatus = () => {
        if (!selectedStatus) {
            return "Todos los estados";
        }

        if (typeof selectedStatus === "string" && selectedStatus === "all") {
            return "Todos los estados";
        }

        if (isOrderStatus(selectedStatus)) {
            return (
                <div className="flex items-center gap-2">
                    {renderStatusIcon(selectedStatus)}
                    <span>{selectedStatus.name}</span>
                </div>
            );
        }

        return "Estado desconocido";
    };

    // En la función handleSelectionChange
    const handleSelectionChange = (keys: Selection) => {
        const selectedKey = Array.from(keys).pop();

        if (selectedKey === "all" || selectedKey === undefined) {
            onChange && onChange("all");

            return;
        }

        const status = statuses.find((s) => s.id === selectedKey);

        if (status) {
            onChange && onChange(status);
        }
    };

    // Determinar el texto para mostrar en el botón del selector
    const buttonText = useMemo(() => {
        if (!selectedStatus) {
            return placeholder || "Seleccionar estado";
        }

        if (selectedStatus === "all") {
            return "Todos los estados";
        }

        if (isOrderStatus(selectedStatus)) {
            const { name } = selectedStatus;

            return name || "Estado seleccionado";
        }

        return "Estado seleccionado";
    }, [selectedStatus, placeholder]);

    return (
        <div className={cn("w-full", className)}>
            {label && (
                <label className="block text-small font-medium pb-1.5 text-default-700">
                    {label}
                </label>
            )}
            <Dropdown className="w-full">
                <DropdownTrigger className="w-full">
                    <Button
                        className={cn(
                            "w-full justify-between",
                            !selectedStatus && "text-default-500",
                            errorMessage && "border-danger text-danger",
                        )}
                        endContent={
                            isLoading ? (
                                <Spinner size="sm" />
                            ) : (
                                <ChevronDownIcon className="w-5 h-5 text-default-400" />
                            )
                        }
                        isDisabled={isDisabled || isLoading}
                        size={size}
                        variant={variant}
                    >
                        {buttonText}
                    </Button>
                </DropdownTrigger>
                <DropdownMenu
                    aria-label="Estados de orden"
                    classNames={{
                        base: "min-w-[240px] max-h-[320px] overflow-y-auto",
                    }}
                    selectedKeys={
                        !selectedStatus || selectedStatus === "all"
                            ? new Set(["all"])
                            : isOrderStatus(selectedStatus)
                              ? new Set([selectedStatus.id])
                              : new Set()
                    }
                    onSelectionChange={handleSelectionChange}
                >
                    <DropdownItem key="all" className="text-default-500">
                        Todos
                    </DropdownItem>
                    <DropdownSection
                        showDivider
                        items={sortedStatuses}
                        title="Estados"
                    >
                        {(status) => (
                            <DropdownItem
                                key={status.id}
                                description={
                                    statusDescriptions[
                                        status.id.toLowerCase()
                                    ] || ""
                                }
                                textValue={status.name}
                            >
                                <div className="flex items-center gap-2">
                                    <Chip
                                        className="truncate"
                                        size="sm"
                                        startContent={renderStatusIcon(status)}
                                        style={getChipColorStyle(status)}
                                        variant="flat"
                                    >
                                        {status.name}
                                    </Chip>
                                </div>
                            </DropdownItem>
                        )}
                    </DropdownSection>
                </DropdownMenu>
            </Dropdown>
            {errorMessage && (
                <span className="text-danger text-xs mt-1 block">
                    {errorMessage}
                </span>
            )}
        </div>
    );
}
