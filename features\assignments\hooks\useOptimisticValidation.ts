"use client";

import { useState, useCallback } from "react";

import {
    validateOptimistic,
    validateAndR<PERSON>oncile,
    getQuantitySuggestions,
} from "@/features/assignments/actions";

interface ValidationState {
    isLoading: boolean;
    isValid: boolean;
    error: string | null;
    versionToken?: string;
    availableQuantity?: number;
    suggestions?: {
        maxAvailable?: number;
        recommendedQuantity?: number;
    };
}

interface UseSuggestionsState {
    isLoading: boolean;
    suggestedQuantity: number;
    maxQuantity: number;
    recentAssignments: number[];
}

/**
 * Hook personalizado para validación optimista con control de estados
 */
export function useOptimisticValidation() {
    const [validationState, setValidationState] = useState<ValidationState>({
        isLoading: false,
        isValid: true,
        error: null,
    });

    const [suggestionsState, setSuggestionsState] =
        useState<UseSuggestionsState>({
            isLoading: false,
            suggestedQuantity: 0,
            maxQuantity: 0,
            recentAssignments: [],
        });

    /**
     * Realiza validación optimista de disponibilidad
     */
    const validateQuantity = useCallback(
        async (garmentSizeId: string, quantity: number) => {
            setValidationState((prev) => ({ ...prev, isLoading: true }));

            try {
                const response = await validateOptimistic(
                    garmentSizeId,
                    quantity,
                );

                setValidationState({
                    isLoading: false,
                    isValid: response.success,
                    error: response.error || null,
                    versionToken: response.version,
                    availableQuantity: response.availableQuantity,
                    suggestions: response.suggestions,
                });

                return response;
            } catch (error) {
                // REMOVED: console.error("Error en validación optimista:", error);
                setValidationState({
                    isLoading: false,
                    isValid: false,
                    error: "Error al validar disponibilidad",
                });

                return {
                    success: false,
                    error: "Error al validar disponibilidad",
                };
            }
        },
        [],
    );

    /**
     * Realiza validación final antes de confirmar
     */
    const validateFinal = useCallback(
        async (
            garmentSizeId: string,
            quantity: number,
            versionToken?: string,
        ) => {
            setValidationState((prev) => ({ ...prev, isLoading: true }));

            try {
                const response = await validateAndReconcile(
                    garmentSizeId,
                    quantity,
                    versionToken,
                );

                setValidationState({
                    isLoading: false,
                    isValid: response.success,
                    error: response.error || null,
                    availableQuantity: response.availableQuantity,
                    suggestions: response.suggestions,
                });

                return response;
            } catch (error) {
                // REMOVED: console.error("Error en validación final:", error);
                setValidationState({
                    isLoading: false,
                    isValid: false,
                    error: "Error al validar disponibilidad final",
                });

                return {
                    success: false,
                    error: "Error al validar disponibilidad final",
                };
            }
        },
        [],
    );

    /**
     * Obtiene sugerencias para la cantidad basadas en histórico
     */
    const getSuggestions = useCallback(
        async (garmentSizeId: string, contractorId: string) => {
            setSuggestionsState((prev) => ({ ...prev, isLoading: true }));

            try {
                const suggestions = await getQuantitySuggestions(
                    garmentSizeId,
                    contractorId,
                );

                setSuggestionsState({
                    isLoading: false,
                    ...suggestions,
                });

                return suggestions;
            } catch (error) {
                // REMOVED: console.error("Error al obtener sugerencias:", error);
                setSuggestionsState((prev) => ({
                    ...prev,
                    isLoading: false,
                }));

                return {
                    suggestedQuantity: 0,
                    maxQuantity: 0,
                    recentAssignments: [],
                };
            }
        },
        [],
    );

    /**
     * Reinicia el estado de validación
     */
    const resetValidation = useCallback(() => {
        setValidationState({
            isLoading: false,
            isValid: true,
            error: null,
        });
    }, []);

    return {
        validation: validationState,
        suggestions: suggestionsState,
        validateQuantity,
        validateFinal,
        getSuggestions,
        resetValidation,
    };
}

export default useOptimisticValidation;
