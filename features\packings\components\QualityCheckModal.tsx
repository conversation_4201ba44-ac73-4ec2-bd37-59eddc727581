"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON><PERSON>,
    <PERSON>dalBody,
    Modal<PERSON>ooter,
    Button,
    Checkbox,
    Textarea,
    Card,
    CardBody,
    Chip,
    Progress,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    addToast,
} from "@heroui/react";
import {
    CheckCircleIcon,
    XCircleIcon,
    ClipboardDocumentCheckIcon,
    CubeIcon,
    ShieldCheckIcon,
    TagIcon,
    DocumentTextIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

import { performQualityCheck } from "@/features/packings/actions";

interface QualityCheckModalProps {
    isOpen: boolean;
    onClose: () => void;
    packing: {
        id: string;
        folio: string;
        details: Array<{
            id: string;
            quantity: number;
            qualityPassed: boolean;
            defects: number;
            verifiedQuantity?: number | null;
            garmentSize: {
                size: { code: string };
                garment: {
                    model: { code: string };
                    color: { name: string };
                };
            };
        }>;
    };
    onComplete?: () => void;
}

interface QualityCheck {
    type: "quantity" | "quality" | "packaging" | "labeling";
    label: string;
    icon: React.ElementType;
    description: string;
    passed: boolean;
    notes: string;
}

export function QualityCheckModal({
    isOpen,
    onClose,
    packing,
    onComplete,
}: QualityCheckModalProps) {
    const [qualityChecks, setQualityChecks] = useState<QualityCheck[]>([
        {
            type: "quantity",
            label: "Verificación de Cantidades",
            icon: CubeIcon,
            description:
                "Confirmar que las cantidades empacadas coinciden con lo solicitado",
            passed: false,
            notes: "",
        },
        {
            type: "quality",
            label: "Control de Calidad",
            icon: ShieldCheckIcon,
            description:
                "Verificar que las prendas no tengan defectos y cumplan estándares",
            passed: false,
            notes: "",
        },
        {
            type: "packaging",
            label: "Empaque Correcto",
            icon: DocumentTextIcon,
            description:
                "Confirmar que el empaque es adecuado y protege los productos",
            passed: false,
            notes: "",
        },
        {
            type: "labeling",
            label: "Etiquetado y Documentación",
            icon: TagIcon,
            description: "Verificar etiquetas, folios y documentación completa",
            passed: false,
            notes: "",
        },
    ]);

    const [detailsVerification, setDetailsVerification] = useState(
        packing.details.map((detail) => ({
            detailId: detail.id,
            qualityPassed: detail.qualityPassed,
            defects: detail.defects,
            verifiedQuantity: detail.verifiedQuantity || detail.quantity,
        })),
    );

    const [generalNotes, setGeneralNotes] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    const allChecksPassed = qualityChecks.every((check) => check.passed);
    const passedChecksCount = qualityChecks.filter(
        (check) => check.passed,
    ).length;
    const completionPercentage =
        (passedChecksCount / qualityChecks.length) * 100;

    const handleCheckChange = (index: number, passed: boolean) => {
        const updated = [...qualityChecks];

        updated[index].passed = passed;
        setQualityChecks(updated);
    };

    const handleCheckNotes = (index: number, notes: string) => {
        const updated = [...qualityChecks];

        updated[index].notes = notes;
        setQualityChecks(updated);
    };

    const handleDetailUpdate = (index: number, field: string, value: any) => {
        const updated = [...detailsVerification];

        updated[index] = {
            ...updated[index],
            [field]: value,
        };
        setDetailsVerification(updated);
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);

        try {
            const result = await performQualityCheck({
                packingId: packing.id,
                checks: qualityChecks.map((check) => ({
                    checkType: check.type,
                    passed: check.passed,
                    notes: check.notes || undefined,
                })),
                qualityNotes: generalNotes || undefined,
                updateDetails: detailsVerification,
            });

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description:
                        result.message || "Control de calidad completado",
                    color: "success",
                });
                onComplete?.();
                onClose();
            } else {
                addToast({
                    title: "Error",
                    description:
                        result.error || "Error al realizar control de calidad",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error inesperado",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal
            isOpen={isOpen}
            scrollBehavior="inside"
            size="5xl"
            onClose={onClose}
        >
            <ModalContent>
                <ModalHeader className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                        <ClipboardDocumentCheckIcon className="w-6 h-6 text-primary" />
                        <h2 className="text-xl font-semibold">
                            Control de Calidad - {packing.folio}
                        </h2>
                    </div>
                    <Progress
                        className="mt-2"
                        color={allChecksPassed ? "success" : "primary"}
                        size="sm"
                        value={completionPercentage}
                    />
                </ModalHeader>

                <ModalBody>
                    <div className="space-y-6">
                        {/* Checklist de calidad */}
                        <div>
                            <h3 className="text-lg font-semibold mb-3">
                                Lista de Verificación
                            </h3>
                            <div className="grid gap-3">
                                {qualityChecks.map((check, index) => (
                                    <motion.div
                                        key={check.type}
                                        animate={{ opacity: 1, x: 0 }}
                                        initial={{ opacity: 0, x: -20 }}
                                        transition={{ delay: index * 0.1 }}
                                    >
                                        <Card>
                                            <CardBody>
                                                <div className="space-y-3">
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex items-start gap-3">
                                                            <check.icon
                                                                className={`w-5 h-5 mt-1 ${
                                                                    check.passed
                                                                        ? "text-success"
                                                                        : "text-gray-400"
                                                                }`}
                                                            />
                                                            <div className="space-y-1">
                                                                <p className="font-medium">
                                                                    {
                                                                        check.label
                                                                    }
                                                                </p>
                                                                <p className="text-sm text-gray-500">
                                                                    {
                                                                        check.description
                                                                    }
                                                                </p>
                                                            </div>
                                                        </div>

                                                        <div className="flex items-center gap-4">
                                                            <Checkbox
                                                                color="success"
                                                                isSelected={
                                                                    check.passed
                                                                }
                                                                onValueChange={(
                                                                    value,
                                                                ) =>
                                                                    handleCheckChange(
                                                                        index,
                                                                        value,
                                                                    )
                                                                }
                                                            >
                                                                Aprobado
                                                            </Checkbox>
                                                        </div>
                                                    </div>

                                                    {!check.passed && (
                                                        <Textarea
                                                            label="Observaciones"
                                                            minRows={2}
                                                            placeholder="Describe las observaciones o problemas encontrados..."
                                                            startContent={
                                                                <ExclamationTriangleIcon className="w-4 h-4 text-warning" />
                                                            }
                                                            value={check.notes}
                                                            variant="bordered"
                                                            onValueChange={(
                                                                value,
                                                            ) =>
                                                                handleCheckNotes(
                                                                    index,
                                                                    value,
                                                                )
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            </CardBody>
                                        </Card>
                                    </motion.div>
                                ))}
                            </div>
                        </div>

                        {/* Verificación de detalles */}
                        <div>
                            <h3 className="text-lg font-semibold mb-3">
                                Verificación de Artículos
                            </h3>
                            <Card>
                                <CardBody>
                                    <Table aria-label="Verificación de artículos">
                                        <TableHeader>
                                            <TableColumn>
                                                MODELO/COLOR/TALLA
                                            </TableColumn>
                                            <TableColumn align="center">
                                                CANTIDAD
                                            </TableColumn>
                                            <TableColumn align="center">
                                                VERIFICADO
                                            </TableColumn>
                                            <TableColumn align="center">
                                                DEFECTOS
                                            </TableColumn>
                                            <TableColumn align="center">
                                                ESTADO
                                            </TableColumn>
                                        </TableHeader>
                                        <TableBody>
                                            {packing.details.map(
                                                (detail, index) => (
                                                    <TableRow key={detail.id}>
                                                        <TableCell>
                                                            <div>
                                                                <p className="font-medium">
                                                                    {
                                                                        detail
                                                                            .garmentSize
                                                                            .garment
                                                                            .model
                                                                            .code
                                                                    }
                                                                </p>
                                                                <p className="text-sm text-gray-500">
                                                                    {
                                                                        detail
                                                                            .garmentSize
                                                                            .garment
                                                                            .color
                                                                            .name
                                                                    }{" "}
                                                                    -{" "}
                                                                    {
                                                                        detail
                                                                            .garmentSize
                                                                            .size
                                                                            .code
                                                                    }
                                                                </p>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell align="center">
                                                            {detail.quantity}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Input
                                                                className="w-20 mx-auto"
                                                                size="sm"
                                                                type="number"
                                                                value={
                                                                    detailsVerification[
                                                                        index
                                                                    ].verifiedQuantity?.toString() ||
                                                                    ""
                                                                }
                                                                onValueChange={(
                                                                    value,
                                                                ) =>
                                                                    handleDetailUpdate(
                                                                        index,
                                                                        "verifiedQuantity",
                                                                        parseInt(
                                                                            value,
                                                                        ) || 0,
                                                                    )
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Input
                                                                className="w-20 mx-auto"
                                                                size="sm"
                                                                type="number"
                                                                value={detailsVerification[
                                                                    index
                                                                ].defects.toString()}
                                                                onValueChange={(
                                                                    value,
                                                                ) =>
                                                                    handleDetailUpdate(
                                                                        index,
                                                                        "defects",
                                                                        parseInt(
                                                                            value,
                                                                        ) || 0,
                                                                    )
                                                                }
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Checkbox
                                                                color={
                                                                    detailsVerification[
                                                                        index
                                                                    ]
                                                                        .qualityPassed
                                                                        ? "success"
                                                                        : "danger"
                                                                }
                                                                isSelected={
                                                                    detailsVerification[
                                                                        index
                                                                    ]
                                                                        .qualityPassed
                                                                }
                                                                onValueChange={(
                                                                    value,
                                                                ) =>
                                                                    handleDetailUpdate(
                                                                        index,
                                                                        "qualityPassed",
                                                                        value,
                                                                    )
                                                                }
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ),
                                            )}
                                        </TableBody>
                                    </Table>
                                </CardBody>
                            </Card>
                        </div>

                        {/* Notas generales */}
                        <div>
                            <Textarea
                                label="Notas Generales del Control de Calidad"
                                minRows={3}
                                placeholder="Ingrese cualquier observación adicional sobre el control de calidad..."
                                value={generalNotes}
                                variant="bordered"
                                onValueChange={setGeneralNotes}
                            />
                        </div>

                        {/* Resumen */}
                        <Card
                            className={
                                allChecksPassed
                                    ? "border-success"
                                    : "border-warning"
                            }
                        >
                            <CardBody>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        {allChecksPassed ? (
                                            <>
                                                <CheckCircleIcon className="w-8 h-8 text-success" />
                                                <div>
                                                    <p className="font-semibold text-success">
                                                        Control de Calidad
                                                        Aprobado
                                                    </p>
                                                    <p className="text-sm text-gray-500">
                                                        Todos los puntos de
                                                        verificación han sido
                                                        aprobados
                                                    </p>
                                                </div>
                                            </>
                                        ) : (
                                            <>
                                                <XCircleIcon className="w-8 h-8 text-warning" />
                                                <div>
                                                    <p className="font-semibold text-warning">
                                                        Control de Calidad con
                                                        Observaciones
                                                    </p>
                                                    <p className="text-sm text-gray-500">
                                                        {passedChecksCount} de{" "}
                                                        {qualityChecks.length}{" "}
                                                        puntos aprobados
                                                    </p>
                                                </div>
                                            </>
                                        )}
                                    </div>

                                    <Chip
                                        color={
                                            allChecksPassed
                                                ? "success"
                                                : "warning"
                                        }
                                        size="lg"
                                        variant="flat"
                                    >
                                        {Math.round(completionPercentage)}%
                                        Completado
                                    </Chip>
                                </div>
                            </CardBody>
                        </Card>
                    </div>
                </ModalBody>

                <ModalFooter>
                    <Button
                        isDisabled={isSubmitting}
                        variant="light"
                        onPress={onClose}
                    >
                        Cancelar
                    </Button>
                    <Button
                        color={allChecksPassed ? "success" : "primary"}
                        isLoading={isSubmitting}
                        startContent={<ShieldCheckIcon className="w-5 h-5" />}
                        onPress={handleSubmit}
                    >
                        {allChecksPassed
                            ? "Aprobar Control de Calidad"
                            : "Guardar con Observaciones"}
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
}
