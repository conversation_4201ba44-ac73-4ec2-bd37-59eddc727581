"use client";

import React from "react";
import { CalendarIcon } from "@heroicons/react/24/outline";

import {
    Card,
    CardBody,
    CardHeader,
} from "@/shared/components/ui/hero-ui-client";

export default function CalendarPlaceholder({
    orders,
    onSelectOrder,
}: {
    orders: any[];
    onSelectOrder: (order: any) => void;
}) {
    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <div className="flex items-center gap-2">
                        <CalendarIcon className="w-5 h-5" />
                        <h3 className="text-lg font-semibold">
                            Vista de Calendario
                        </h3>
                    </div>
                </CardHeader>
                <CardBody>
                    <div className="text-center py-12 text-gray-500">
                        <CalendarIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                        <p>La vista de calendario se mostrará aquí</p>
                        <p className="text-sm mt-2">
                            Podrás ver las órdenes organizadas por fecha de
                            entrega
                        </p>
                    </div>
                </CardBody>
            </Card>
        </div>
    );
}
