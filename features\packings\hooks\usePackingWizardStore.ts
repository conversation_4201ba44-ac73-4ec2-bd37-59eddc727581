"use client";

// Note: Zustand dependency not available, this is a mock implementation
// The actual store functionality has been moved to useState hooks in the component

export const usePackingWizardStore = () => {
    // Mock implementation - actual state is now handled by useState in components
    return {
        basicInfo: {},
        selectedOrders: [],
        qualityDistribution: [],
        packagingSummary: [],
        setBasicInfo: () => {},
        setSelectedOrders: () => {},
        setQualityDistribution: () => {},
        setPackagingSummary: () => {},
        reset: () => {},
    };
};
