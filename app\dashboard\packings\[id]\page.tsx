import { Suspense } from "react";
import { notFound } from "next/navigation";

import { Spinner } from "@/shared/components/ui/hero-ui-client";
import { getPackingById } from "@/features/packings/actions";

import PackingDetailClient from "./PackingDetailClient";

interface PackingDetailPageProps {
    params: {
        id: string;
    };
}

export default async function PackingDetailPage({
    params,
}: PackingDetailPageProps) {
    const result = await getPackingById(params.id);

    if (!result.success || !result.data) {
        notFound();
    }

    return (
        <Suspense
            fallback={
                <div className="flex items-center justify-center min-h-[50vh]">
                    <Spinner
                        label="Cargando detalles del packing..."
                        size="lg"
                    />
                </div>
            }
        >
            <PackingDetailClient packing={result.data} />
        </Suspense>
    );
}
