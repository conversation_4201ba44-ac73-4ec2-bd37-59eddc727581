import React from "react";
import { Ruler } from "lucide-react";

interface SizeFormHeaderProps {
    title: string;
    description?: string;
    icon?: React.ReactNode;
}

export function SizeFormHeader({
    title,
    description,
    icon = <Ruler className="w-5 h-5" />,
}: SizeFormHeaderProps) {
    return (
        <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary-50 dark:bg-primary-900/20">
                <div className="text-primary-600 dark:text-primary-400">
                    {icon}
                </div>
            </div>
            <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                    {title}
                </h1>
                {description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-0.5">
                        {description}
                    </p>
                )}
            </div>
        </div>
    );
}
