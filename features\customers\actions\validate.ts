"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Valida si un nombre de cliente es único
 * @returns Un objeto con { isValid: boolean } o un objeto con { success, data, error }
 */
export async function validateCustomerName(name: string, excludeId?: string) {
    if (!name || name.length < 2) {
        return {
            success: false,
            data: { isValid: false },
            error: "Nombre inválido",
        };
    }

    return await handleDbError(async () => {
        const existingCustomer = await db.customer.findFirst({
            where: {
                name: {
                    equals: name,
                    mode: Prisma.QueryMode.insensitive,
                },
                id: excludeId ? { not: excludeId } : undefined,
            },
        });

        return { isValid: !existingCustomer };
    }, "Error al validar nombre de cliente");
}
