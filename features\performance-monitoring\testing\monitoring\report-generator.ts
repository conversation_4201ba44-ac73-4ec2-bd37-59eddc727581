import fs from "fs/promises";
import path from "path";

interface ReportData {
    baseline: any;
    postTest: any;
    validation: any;
    timestamp: Date;
}

export async function generateFinalReport(data: ReportData): Promise<void> {
    const report = {
        title: "Performance Optimization - Final Report",
        generatedAt: data.timestamp,
        summary: generateSummary(data),
        metrics: {
            baseline: data.baseline,
            postTest: data.postTest,
            improvements: data.validation.improvements,
        },
        validation: data.validation,
        recommendations: generateRecommendations(data),
    };

    // Save JSON report
    const jsonPath = path.join(
        process.cwd(),
        "features/performance-monitoring/testing/reports",
        `final-report-${Date.now()}.json`,
    );

    await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));

    // Save Markdown report
    const mdPath = jsonPath.replace(".json", ".md");

    await fs.writeFile(mdPath, generateMarkdownReport(report));

    console.log(`Reports saved:\n- JSON: ${jsonPath}\n- Markdown: ${mdPath}`);
}

function generateSummary(data: ReportData): string {
    const { validation } = data;

    return validation.passed
        ? "✅ All performance targets achieved successfully!"
        : "⚠️ Some performance targets were not met. See details below.";
}

function generateRecommendations(data: ReportData): string[] {
    const recommendations = [];
    const { validation } = data;

    if (validation.improvements.cacheHitRate < 80) {
        recommendations.push(
            "Consider implementing more aggressive cache warming strategies",
        );
    }

    return recommendations;
}

function generateMarkdownReport(report: any): string {
    return `# ${report.title}

Generated: ${report.generatedAt}

## Summary
${report.summary}

## Performance Metrics

### Cache Performance
- **Hit Rate**: ${report.metrics.postTest.hitRate.toFixed(1)}%
- **Improvement**: ${report.metrics.improvements.cacheHitRate.toFixed(1)}%

### Response Times
- **Average**: ${report.metrics.postTest.avgResponseTime.toFixed(0)}ms
- **Improvement**: ${report.metrics.improvements.responseTime.toFixed(1)}%

### Cache Distribution
- **Static Content**: ${report.metrics.postTest.keysByType.STATIC} keys
- **User-Specific**: ${report.metrics.postTest.keysByType.USER_SPECIFIC} keys
- **Computed Data**: ${report.metrics.postTest.keysByType.COMPUTED} keys

## Validation Results

### ✅ Successes
${report.validation.successes.map((s: any) => `- ${s}`).join("\n")}

### ❌ Failures
${report.validation.failures.length ? report.validation.failures.map((f: any) => `- ${f}`).join("\n") : "None"}

### ⚠️ Warnings
${report.validation.warnings.length ? report.validation.warnings.map((w: any) => `- ${w}`).join("\n") : "None"}

## Recommendations
${report.recommendations.length ? report.recommendations.map((r: any) => `- ${r}`).join("\n") : "No additional recommendations"}

## Next Steps
1. Deploy to staging environment for extended testing
2. Monitor production metrics after deployment
3. Adjust cache TTLs based on real-world usage patterns
`;
}
