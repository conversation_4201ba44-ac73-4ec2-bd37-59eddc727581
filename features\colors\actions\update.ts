"use server";

import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";

import { updateColorSchema } from "../schemas/schema";

import { validateColorUnique } from "./validate";
import { revalidateCache } from "./query";

/**
 * Actualiza un color existente
 */
export async function updateColor(
    id: string | null | undefined,
    data: { name?: string; hexCode?: string },
) {
    if (!id) return { success: false, error: "ID no válido" };

    try {
        // Validar datos
        const validatedData = updateColorSchema.parse(data);

        // Verificar si el nombre ya existe (excluyendo el color actual)
        if (validatedData.name) {
            const uniqueValidation = await validateColorUnique(
                validatedData,
                id,
            );

            if (!uniqueValidation || !uniqueValidation.success) {
                return {
                    success: false,
                    error:
                        uniqueValidation?.error ||
                        "Error al validar unicidad del color",
                };
            }

            if (!(uniqueValidation.data as any)?.isValid) {
                return {
                    success: false,
                    error: "El nombre del color ya existe en el sistema",
                };
            }
        }

        // Actualizar color en la base de datos
        const color = await db.color.update({
            where: { id },
            data: {
                name: validatedData.name,
                hexCode: validatedData.hexCode,
            },
        });

        // Revalidar rutas
        revalidateCache(id);

        return { success: true, data: color };
    } catch (error) {
        // Manejar errores de validación
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos de color inválidos",
            };
        }

        // Usar helper para manejar errores
        return handleDbError(() => {
            throw error;
        }, "Error al actualizar color");
    }
}
