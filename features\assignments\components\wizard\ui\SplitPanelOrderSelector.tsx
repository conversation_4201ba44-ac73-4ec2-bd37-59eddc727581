"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import {
    Card,
    CardBody,
    CardHeader,
    Input,
    Button,
    Checkbox,
    Badge,
    Tabs,
    Tab,
    Chip,
    Divider,
    addToast,
} from "@heroui/react";
import {
    DocumentIcon,
    XMarkIcon,
    CheckIcon,
    MagnifyingGlassIcon,
    ShoppingBagIcon,
    CalendarIcon,
    SwatchIcon,
    SparklesIcon,
    ClockIcon,
    UserGroupIcon,
    CubeIcon,
    ChartBarIcon,
    TruckIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { Order } from "@/features/assignments/types";
import { formatDate } from "@/shared/utils/formatters";

import {
    OrderSelectorSkeleton,
    SearchSuggestions,
    useKeyboardNavigation,
    AnimatedCounter,
    FocusTrap,
    InlineLoadingIndicator,
    listItemTransition,
} from "./OrderSelectorEnhancements";
import OrderSizesPreview from "./OrderSizesPreview";

// Función para determinar el contraste del texto
function getContrastColor(hexColor: string): string {
    const hex = hexColor.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    return luminance > 0.5 ? "text-gray-900" : "text-white";
}

interface SplitPanelOrderSelectorProps {
    orders: Order[];
    selectedOrders: string[];
    isLoading: boolean;
    onToggleOrder: (orderId: string) => void;
    searchQuery: string;
    onSearchChange: (query: string) => void;
}

export default function SplitPanelOrderSelector({
    orders,
    selectedOrders,
    isLoading,
    onToggleOrder,
    searchQuery,
    onSearchChange,
}: SplitPanelOrderSelectorProps) {
    const [activeOrderId, setActiveOrderId] = useState<string | null>(null);
    const [previewOrder, setPreviewOrder] = useState<Order | null>(null);
    const [isMobile, setIsMobile] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);
    const [focusedIndex, setFocusedIndex] = useState<number>(-1);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Generate search suggestions
    const searchSuggestions = useMemo(() => {
        if (!searchQuery || searchQuery.length < 2) return [];
        const suggestions = new Set<string>();

        orders.forEach((order) => {
            if (
                order.cutOrder
                    ?.toLowerCase()
                    .includes(searchQuery.toLowerCase())
            ) {
                suggestions.add(`Orden ${order.cutOrder}`);
            }
            if (
                order.customer?.name
                    ?.toLowerCase()
                    .includes(searchQuery.toLowerCase())
            ) {
                suggestions.add(order.customer.name);
            }
        });

        return Array.from(suggestions).slice(0, 5);
    }, [orders, searchQuery]);

    // Detect mobile viewport
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 1024);
        };

        checkMobile();
        window.addEventListener("resize", checkMobile);

        return () => window.removeEventListener("resize", checkMobile);
    }, []);

    // Set first order as active
    useEffect(() => {
        if (orders.length > 0 && !activeOrderId) {
            setActiveOrderId(orders[0].id);
            setPreviewOrder(orders[0]);
        }
    }, [orders, activeOrderId]);

    // Update preview order
    useEffect(() => {
        if (activeOrderId) {
            const order = orders.find((o) => o.id === activeOrderId) || null;

            setPreviewOrder(order);
        }
    }, [activeOrderId, orders]);

    // Keyboard navigation
    useKeyboardNavigation({
        items: orders,
        focusedIndex,
        setFocusedIndex,
        onSelect: (order) => {
            setActiveOrderId(order.id);
            setPreviewOrder(order);
        },
        onToggle: (order) => handleToggleOrder(order.id),
        isActive: !isMobile && !showSuggestions,
    });

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onSearchChange(e.target.value);
        setShowSuggestions(e.target.value.length > 0);
    };

    const handleSuggestionSelect = (suggestion: string) => {
        const searchTerm = suggestion.replace(/^Orden /, "");

        onSearchChange(searchTerm);
        setShowSuggestions(false);
        searchInputRef.current?.focus();
    };

    const handleToggleOrder = async (orderId: string) => {
        setIsUpdating(true);
        await new Promise((resolve) => setTimeout(resolve, 300));
        onToggleOrder(orderId);

        if (!selectedOrders.includes(orderId)) {
            addToast({
                title: "Orden seleccionada",
                description: "La orden ha sido seleccionada correctamente",
                color: "success",
                icon: <CheckCircleIcon className="w-5 h-5" />,
            });
        }

        setIsUpdating(false);
    };

    // Enhanced search header
    const SearchHeader = useMemo(() => {
        const Component = () => (
            <CardHeader className=" flex-col p-6 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border-b border-gray-200 dark:border-gray-700 space-y-4">
                <div className="flex flex-col gap-4 w-full items-center justify-between">
                    <div className="flex items-center gap-4">
                        <motion.div
                            className="p-2.5 bg-gradient-to-    br from-purple-500 to-pink-600 rounded-xl shadow-md"
                            transition={{ type: "spring", stiffness: 300 }}
                            whileHover={{ scale: 1.05 }}
                        >
                            <DocumentIcon className="w-5 h-5 text-white" />
                        </motion.div>
                        <div className="space-y-1">
                            <div className="flex items-center gap-3">
                                <h3 className="text-base font-bold text-gray-800 dark:text-gray-100">
                                    Órdenes disponibles
                                </h3>
                                <Chip
                                    className="font-semibold"
                                    color="primary"
                                    size="sm"
                                    variant="flat"
                                >
                                    {orders.length}
                                </Chip>
                            </div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Selecciona las órdenes para asignar
                            </p>
                        </div>
                    </div>
                    {isUpdating ? (
                        <InlineLoadingIndicator text="Actualizando" />
                    ) : (
                        selectedOrders.length > 0 && (
                            <motion.div
                                animate={{ scale: 1, opacity: 1 }}
                                initial={{ scale: 0.8, opacity: 0 }}
                                transition={{
                                    type: "spring",
                                    stiffness: 500,
                                    damping: 30,
                                }}
                            >
                                <Chip
                                    className="font-medium px-3"
                                    color="success"
                                    size="md"
                                    startContent={
                                        <CheckCircleIcon className="w-4 h-4" />
                                    }
                                    variant="flat"
                                >
                                    {selectedOrders.length} seleccionadas
                                </Chip>
                            </motion.div>
                        )
                    )}
                </div>
                <div className="relative w-full">
                    <Input
                        ref={searchInputRef}
                        isClearable
                        classNames={{
                            input: "text-sm",
                            inputWrapper:
                                "h-12 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow",
                        }}
                        placeholder="Buscar por orden, cliente, lote..."
                        size="md"
                        startContent={
                            <motion.div
                                animate={{ rotate: searchQuery ? 360 : 0 }}
                                transition={{ duration: 0.5 }}
                            >
                                <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
                            </motion.div>
                        }
                        value={searchQuery}
                        variant="bordered"
                        onBlur={() =>
                            setTimeout(() => setShowSuggestions(false), 200)
                        }
                        onChange={handleSearchChange}
                        onClear={() => {
                            onSearchChange("");
                            setShowSuggestions(false);
                            setTimeout(
                                () => searchInputRef.current?.focus(),
                                0,
                            );
                        }}
                    />
                    <AnimatePresence>
                        {showSuggestions && searchSuggestions.length > 0 && (
                            <SearchSuggestions
                                suggestions={searchSuggestions}
                                onSelect={handleSuggestionSelect}
                            />
                        )}
                    </AnimatePresence>
                </div>
            </CardHeader>
        );

        Component.displayName = "SearchHeader";

        return Component;
    }, [
        orders.length,
        isUpdating,
        selectedOrders.length,
        searchQuery,
        showSuggestions,
        searchSuggestions,
    ]);

    // Enhanced order list
    const OrderList = () => (
        <div className="divide-y divide-gray-100 dark:divide-gray-800">
            <AnimatePresence mode="wait">
                {orders.map((order, index) => {
                    const isSelected = selectedOrders.includes(order.id);
                    const isActive = activeOrderId === order.id;
                    const isFocused = focusedIndex === index;

                    // Calculate urgency
                    const daysUntilDelivery = order.estimatedDeliveryDate
                        ? Math.ceil(
                              (new Date(order.estimatedDeliveryDate).getTime() -
                                  new Date().getTime()) /
                                  (1000 * 60 * 60 * 24),
                          )
                        : null;
                    const isUrgent =
                        daysUntilDelivery !== null && daysUntilDelivery <= 7;

                    return (
                        <motion.div
                            key={order.id}
                            data-order-index={index}
                            {...listItemTransition}
                            className={`
                                p-4 cursor-pointer transition-all duration-200 relative overflow-hidden
                                ${
                                    isActive
                                        ? "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-l-4 border-l-blue-500 shadow-md"
                                        : "hover:bg-gray-50 dark:hover:bg-gray-800/50 hover:shadow-sm border-l-4 border-l-transparent"
                                }
                                ${isSelected ? "ring-2 ring-inset ring-green-500/30" : ""}
                                ${isFocused ? "ring-2 ring-offset-2 ring-blue-500" : ""}
                            `}
                            onClick={() => {
                                setActiveOrderId(order.id);
                                setPreviewOrder(order);
                            }}
                        >
                            <div className="flex items-start gap-4">
                                <motion.div
                                    className="pt-0.5"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Checkbox
                                        classNames={{
                                            wrapper: "shadow-sm",
                                        }}
                                        color="success"
                                        isSelected={isSelected}
                                        size="md"
                                        onChange={() =>
                                            handleToggleOrder(order.id)
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                </motion.div>

                                <div className="flex-1 min-w-0">
                                    <div className="flex justify-between items-start gap-3 mb-2">
                                        <div className="min-w-0">
                                            <div className="flex items-center gap-2 mb-1">
                                                <h4 className="font-bold text-sm text-gray-800 dark:text-gray-100 truncate">
                                                    Orden{" "}
                                                    {order.cutOrder ||
                                                        order.transferNumber}
                                                </h4>
                                                {isUrgent && (
                                                    <Chip
                                                        className="px-2 font-medium"
                                                        color="warning"
                                                        size="sm"
                                                        startContent={
                                                            <ClockIcon className="w-3 h-3" />
                                                        }
                                                        variant="flat"
                                                    >
                                                        Urgente
                                                    </Chip>
                                                )}
                                            </div>
                                            <p className="text-xs text-gray-600 dark:text-gray-400 truncate flex items-center gap-1.5">
                                                <UserGroupIcon className="w-3 h-3" />
                                                {order.customer?.name}
                                            </p>
                                        </div>

                                        <div className="flex items-center gap-2 flex-shrink-0">
                                            <Chip
                                                className="font-medium"
                                                color={getStatusColor(
                                                    order.status?.name ||
                                                        "pending",
                                                )}
                                                size="sm"
                                                variant="flat"
                                            >
                                                {order.status?.name ||
                                                    "Sin estado"}
                                            </Chip>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                        <div className="flex items-center gap-1.5">
                                            <CubeIcon className="w-3.5 h-3.5" />
                                            <span className="font-medium">
                                                {order.garments?.length || 0}{" "}
                                                prendas
                                            </span>
                                        </div>
                                        {order.estimatedDeliveryDate && (
                                            <div className="flex items-center gap-1.5">
                                                <CalendarIcon className="w-3.5 h-3.5" />
                                                <span>
                                                    {formatDate(
                                                        order.estimatedDeliveryDate,
                                                    )}
                                                </span>
                                            </div>
                                        )}
                                        {(order as any).totalQuantity && (
                                            <div className="flex items-center gap-1.5">
                                                <ChartBarIcon className="w-3.5 h-3.5" />
                                                <span>
                                                    {
                                                        (order as any)
                                                            .totalQuantity
                                                    }{" "}
                                                    unidades
                                                </span>
                                            </div>
                                        )}
                                    </div>

                                    {/* Progress indicator if available */}
                                    {(order as any).assignedQuantity !==
                                        undefined &&
                                        (order as any).totalQuantity && (
                                            <div className="mt-3">
                                                <div className="flex justify-between items-center mb-1">
                                                    <span className="text-xs text-gray-500">
                                                        Progreso
                                                    </span>
                                                    <span className="text-xs font-medium">
                                                        {Math.round(
                                                            ((order as any)
                                                                .assignedQuantity /
                                                                (order as any)
                                                                    .totalQuantity) *
                                                                100,
                                                        )}
                                                        %
                                                    </span>
                                                </div>
                                                <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                                    <motion.div
                                                        animate={{
                                                            width: `${((order as any).assignedQuantity / (order as any).totalQuantity) * 100}%`,
                                                        }}
                                                        className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
                                                        initial={{ width: 0 }}
                                                        transition={{
                                                            duration: 0.5,
                                                            delay: index * 0.05,
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        )}
                                </div>
                            </div>
                        </motion.div>
                    );
                })}
            </AnimatePresence>
        </div>
    );

    // Enhanced order details
    const OrderDetails = () => (
        <>
            <CardHeader className="p-6 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-row gap-4 items-center">
                    {/* Primera sección: Información de la orden */}
                    <div className="flex items-center justify-center">
                        <div className="flex items-center gap-3">
                            <motion.div
                                className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-md"
                                transition={{ type: "spring", stiffness: 300 }}
                                whileHover={{ scale: 1.05 }}
                            >
                                <ShoppingBagIcon className="w-5 h-5 text-white" />
                            </motion.div>
                            <div>
                                <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100">
                                    Orden{" "}
                                    {previewOrder?.cutOrder ||
                                        previewOrder?.transferNumber}
                                </h3>
                                <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1.5">
                                    <UserGroupIcon className="w-4 h-4" />
                                    {previewOrder?.customer?.name}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Segunda sección: Botón centrado */}
                    <div className="flex justify-center">
                        <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                        >
                            <Button
                                className={`
                                    font-semibold shadow-lg transition-all
                                    ${
                                        selectedOrders.includes(
                                            previewOrder?.id || "",
                                        )
                                            ? "bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white"
                                            : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white"
                                    }
                                `}
                                size="md"
                                startContent={
                                    selectedOrders.includes(
                                        previewOrder?.id || "",
                                    ) ? (
                                        <XMarkIcon className="w-5 h-5" />
                                    ) : (
                                        <CheckIcon className="w-5 h-5" />
                                    )
                                }
                                onClick={() =>
                                    previewOrder &&
                                    handleToggleOrder(previewOrder.id)
                                }
                            >
                                {selectedOrders.includes(previewOrder?.id || "")
                                    ? "Quitar selección"
                                    : "Seleccionar orden"}
                            </Button>
                        </motion.div>
                    </div>

                    {/* Tercera sección: Tags de orden centrados */}
                    <div className="flex flex-wrap gap-2 justify-center">
                        {(previewOrder as any)?.orderPart && (
                            <Chip color="secondary" size="sm" variant="flat">
                                Partida: {(previewOrder as any).orderPart}
                            </Chip>
                        )}
                        {previewOrder?.batch && (
                            <Chip color="primary" size="sm" variant="flat">
                                Lote: {previewOrder.batch}
                            </Chip>
                        )}
                        {previewOrder?.transferNumber && (
                            <Chip color="default" size="sm" variant="flat">
                                Transfer: {previewOrder.transferNumber}
                            </Chip>
                        )}
                    </div>
                </div>
            </CardHeader>

            <CardBody className="p-6 overflow-y-auto">
                {/* Enhanced metric cards */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <motion.div
                        className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-4 border border-blue-200 dark:border-blue-800"
                        transition={{ type: "spring", stiffness: 400 }}
                        whileHover={{ scale: 1.02 }}
                    >
                        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                            <CubeIcon className="w-4 h-4" />
                            <span className="text-xs font-semibold uppercase tracking-wide">
                                Total Prendas
                            </span>
                        </div>
                        <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                            <AnimatedCounter
                                value={previewOrder?.garments?.length || 0}
                            />
                        </p>
                    </motion.div>

                    <motion.div
                        className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 rounded-xl p-4 border border-purple-200 dark:border-purple-800"
                        transition={{ type: "spring", stiffness: 400 }}
                        whileHover={{ scale: 1.02 }}
                    >
                        <div className="flex items-center gap-2 text-purple-700 dark:text-purple-300 mb-2">
                            <SwatchIcon className="w-4 h-4" />
                            <span className="text-xs font-semibold uppercase tracking-wide">
                                Modelos
                            </span>
                        </div>
                        <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                            <AnimatedCounter
                                value={
                                    new Set(
                                        previewOrder?.garments?.map(
                                            (g) => g.model?.code,
                                        ),
                                    ).size || 0
                                }
                            />
                        </p>
                    </motion.div>

                    <motion.div
                        className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-xl p-4 border border-green-200 dark:border-green-800"
                        transition={{ type: "spring", stiffness: 400 }}
                        whileHover={{ scale: 1.02 }}
                    >
                        <div className="flex items-center gap-2 text-green-700 dark:text-green-300 mb-2">
                            <CalendarIcon className="w-4 h-4" />
                            <span className="text-xs font-semibold uppercase tracking-wide">
                                Entrega Estimada
                            </span>
                        </div>
                        <p className="text-sm font-bold text-gray-800 dark:text-gray-100">
                            {previewOrder?.estimatedDeliveryDate
                                ? formatDate(previewOrder.estimatedDeliveryDate)
                                : "No definida"}
                        </p>
                    </motion.div>

                    <motion.div
                        className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 rounded-xl p-4 border border-amber-200 dark:border-amber-800"
                        transition={{ type: "spring", stiffness: 400 }}
                        whileHover={{ scale: 1.02 }}
                    >
                        <div className="flex items-center gap-2 text-amber-700 dark:text-amber-300 mb-2">
                            <TruckIcon className="w-4 h-4" />
                            <span className="text-xs font-semibold uppercase tracking-wide">
                                Estado
                            </span>
                        </div>
                        <Badge
                            className="font-semibold"
                            color={getStatusColor(
                                previewOrder?.status?.name || "pending",
                            )}
                            size="sm"
                            variant="flat"
                        >
                            {previewOrder?.status?.name || "Sin estado"}
                        </Badge>
                    </motion.div>
                </div>

                <Divider className="my-6" />

                {/* Garments list */}
                {previewOrder?.garments && previewOrder.garments.length > 0 ? (
                    <div>
                        <h4 className="text-lg font-bold text-gray-800 dark:text-gray-100 mb-4 flex items-center gap-2">
                            <SparklesIcon className="w-5 h-5 text-indigo-500" />
                            Detalle de Prendas
                        </h4>
                        <OrderSizesPreview
                            garments={previewOrder.garments as any}
                        />
                    </div>
                ) : (
                    <div>
                        <h4 className="text-lg font-bold text-gray-800 dark:text-gray-100 mb-4">
                            Detalle de Prendas
                        </h4>
                        <div className="text-center p-12 bg-gray-50 dark:bg-gray-800/50 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-xl">
                            <ShoppingBagIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                            <p className="text-gray-600 dark:text-gray-400 font-medium">
                                No hay prendas asociadas a esta orden
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                                Las prendas aparecerán aquí cuando estén
                                disponibles
                            </p>
                        </div>
                    </div>
                )}
            </CardBody>
        </>
    );

    if (isLoading) {
        return <OrderSelectorSkeleton />;
    }

    // Empty state
    if (orders.length === 0) {
        return (
            <Card className="min-h-[700px] h-[calc(100vh-200px)] max-h-[900px] shadow-xl">
                <SearchHeader />
                <CardBody className="flex items-center justify-center">
                    <motion.div
                        animate={{ opacity: 1, scale: 1 }}
                        className="text-center p-12"
                        initial={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.5 }}
                    >
                        <motion.div
                            animate={{
                                boxShadow: [
                                    "0 0 0 0 rgba(99, 102, 241, 0.4)",
                                    "0 0 0 20px rgba(99, 102, 241, 0)",
                                    "0 0 0 0 rgba(99, 102, 241, 0)",
                                ],
                            }}
                            className="mb-6 mx-auto w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-full flex items-center justify-center"
                            transition={{ duration: 2, repeat: Infinity }}
                        >
                            <DocumentIcon className="h-16 w-16 text-gray-400" />
                        </motion.div>
                        <h3 className="text-2xl font-bold mb-3 text-gray-800 dark:text-gray-100">
                            No se encontraron órdenes
                        </h3>
                        <p className="text-base text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                            {searchQuery
                                ? "No hay resultados para tu búsqueda. Intenta con otros términos."
                                : "No hay órdenes disponibles para asignar en este momento."}
                        </p>
                    </motion.div>
                </CardBody>
            </Card>
        );
    }

    // Mobile layout
    if (isMobile) {
        return (
            <div className="min-h-[600px] h-[calc(100vh-120px)]">
                <Tabs
                    aria-label="Órdenes"
                    className="h-full"
                    classNames={{
                        tabList:
                            "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                        cursor: "w-full bg-gradient-to-r from-blue-500 to-indigo-600",
                        tab: "max-w-fit px-0 h-12",
                        tabContent:
                            "group-data-[selected=true]:text-white font-semibold",
                    }}
                    color="primary"
                    variant="solid"
                >
                    <Tab
                        key="list"
                        title={
                            <div className="flex items-center gap-2">
                                <DocumentIcon className="w-4 h-4" />
                                <span>Lista de Órdenes</span>
                                <Badge color="primary" size="sm" variant="flat">
                                    {orders.length}
                                </Badge>
                            </div>
                        }
                    >
                        <Card className="h-[calc(100vh-200px)] min-h-[550px] shadow-xl">
                            <SearchHeader />
                            <CardBody className="p-0 overflow-y-auto">
                                <OrderList />
                            </CardBody>
                        </Card>
                    </Tab>
                    <Tab
                        key="details"
                        isDisabled={!previewOrder}
                        title={
                            <div className="flex items-center gap-2">
                                <ShoppingBagIcon className="w-4 h-4" />
                                <span>Detalles</span>
                            </div>
                        }
                    >
                        <Card className="h-[calc(100vh-200px)] min-h-[550px] shadow-xl">
                            {previewOrder ? (
                                <OrderDetails />
                            ) : (
                                <CardBody className="flex items-center justify-center">
                                    <div className="text-center">
                                        <DocumentIcon className="w-20 h-20 mx-auto mb-6 text-gray-300" />
                                        <p className="text-gray-500 text-xl">
                                            Selecciona una orden para ver sus
                                            detalles
                                        </p>
                                    </div>
                                </CardBody>
                            )}
                        </Card>
                    </Tab>
                </Tabs>
            </div>
        );
    }

    // Desktop layout
    return (
        <FocusTrap isActive={!isLoading && orders.length > 0}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[700px] h-[calc(100vh-200px)] max-h-[900px]">
                {/* Left panel */}
                <Card className="lg:col-span-1 h-full shadow-xl border-0 overflow-hidden">
                    <SearchHeader />
                    <CardBody className="p-0 overflow-y-auto">
                        <OrderList />
                    </CardBody>
                </Card>

                {/* Right panel */}
                <Card className="lg:col-span-2 h-full shadow-xl border-0 overflow-hidden">
                    {previewOrder ? (
                        <OrderDetails />
                    ) : (
                        <CardBody className="flex items-center justify-center p-12">
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                className="text-center"
                                initial={{ opacity: 0, y: 20 }}
                                transition={{ duration: 0.5 }}
                            >
                                <motion.div
                                    animate={{ rotate: 360 }}
                                    className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-full flex items-center justify-center"
                                    transition={{
                                        duration: 20,
                                        repeat: Infinity,
                                        ease: "linear",
                                    }}
                                >
                                    <DocumentIcon className="w-12 h-12 text-gray-400" />
                                </motion.div>
                                <p className="text-gray-600 dark:text-gray-400 text-xl font-medium">
                                    Selecciona una orden para ver sus detalles
                                </p>
                                <p className="text-gray-500 dark:text-gray-500 text-sm mt-2">
                                    Haz clic en cualquier orden de la lista
                                </p>
                            </motion.div>
                        </CardBody>
                    )}
                </Card>
            </div>
        </FocusTrap>
    );
}

// Enhanced status color function
function getStatusColor(
    status: string,
): "primary" | "success" | "warning" | "danger" | "default" {
    const statusLower = status.toLowerCase();

    if (statusLower.includes("pendiente") || statusLower.includes("espera")) {
        return "warning";
    } else if (
        statusLower.includes("proceso") ||
        statusLower.includes("progreso")
    ) {
        return "primary";
    } else if (
        statusLower.includes("completado") ||
        statusLower.includes("terminado") ||
        statusLower.includes("entregado")
    ) {
        return "success";
    } else if (
        statusLower.includes("cancelado") ||
        statusLower.includes("error")
    ) {
        return "danger";
    }

    return "default";
}
