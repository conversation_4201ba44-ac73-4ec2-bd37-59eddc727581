/**
 * Update Size Use Case
 *
 * Business logic for updating an existing size.
 * Handles validation, business rules, and persistence.
 */

// import { injectable, inject } from "tsyringe"; // Commented out - tsyringe not installed

import { SizeRepositoryInterface } from "../../domain/repositories/size.repository.interface";
import {
    UpdateSizeDTO,
    SizeResponseDTO,
    toSizeResponse,
} from "../dto/size.dto";

export interface UpdateSizeCommand {
    id: string;
    data: UpdateSizeDTO;
    userId: string;
}

export interface UpdateSizeResult {
    success: boolean;
    data?: SizeResponseDTO;
    error?: string;
    validationErrors?: Array<{ field: string; message: string }>;
}

// @injectable() // Commented out - tsyringe not installed
export class UpdateSizeUseCase {
    constructor(
        // @inject("SizeRepository") // Commented out - tsyringe not installed
        private readonly sizeRepository: SizeRepositoryInterface,
    ) {}

    async execute(command: UpdateSizeCommand): Promise<UpdateSizeResult> {
        try {
            // 1. Validate command
            if (!command.id || !command.data || !command.userId) {
                return {
                    success: false,
                    error: "Invalid command: missing required data",
                };
            }

            // 2. Find existing size
            const existingSize = await this.sizeRepository.findById(command.id);

            if (!existingSize) {
                return {
                    success: false,
                    error: "Size not found",
                };
            }

            // 3. Check if size is deleted
            if (existingSize.deletedAt) {
                return {
                    success: false,
                    error: "Cannot update a deleted size",
                };
            }

            // 4. Check name uniqueness if name is being changed
            if (command.data.name && command.data.name !== existingSize.name) {
                const nameExists = await this.sizeRepository.exists(
                    command.data.name,
                    command.id,
                );

                if (nameExists) {
                    return {
                        success: false,
                        error: "Size name already exists",
                        validationErrors: [
                            {
                                field: "name",
                                message: "A size with this name already exists",
                            },
                        ],
                    };
                }
            }

            // 5. Update the entity
            existingSize.update(command.data);

            // 6. Validate updated entity
            const validationErrors = existingSize.validate();

            if (validationErrors.length > 0) {
                return {
                    success: false,
                    error: "Validation failed",
                    validationErrors: validationErrors.map((error) => ({
                        field: "general",
                        message: error,
                    })),
                };
            }

            // 7. Apply business rules
            // If changing order, ensure it doesn't conflict
            if (command.data.order !== undefined) {
                // Could add logic here to reorder other sizes if needed
            }

            // 8. Save to repository
            const savedSize = await this.sizeRepository.save(existingSize);

            // 9. Return success result
            return {
                success: true,
                data: toSizeResponse(savedSize.toObject() as any),
            };
        } catch (error) {
            // Log error for monitoring
            console.error("UpdateSizeUseCase error:", error);

            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Activate a size
     */
    async activate(id: string, userId: string): Promise<UpdateSizeResult> {
        return this.execute({
            id,
            data: { isActive: true },
            userId,
        });
    }

    /**
     * Deactivate a size
     */
    async deactivate(id: string, userId: string): Promise<UpdateSizeResult> {
        return this.execute({
            id,
            data: { isActive: false },
            userId,
        });
    }
}
