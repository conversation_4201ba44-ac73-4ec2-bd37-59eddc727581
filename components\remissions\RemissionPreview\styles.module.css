/* RemissionPreview Styles */
.remission-preview {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 2rem auto;
  padding: 2rem;
  max-width: 1200px;
  position: relative;
  transition: transform 0.2s ease;
}

/* Orientations */
.remission-preview--portrait {
  width: 210mm;
  min-height: 297mm;
  padding: 20mm 15mm;
}

.remission-preview--landscape {
  width: 297mm;
  min-height: 210mm;
  padding: 15mm 20mm;
}

/* Editable Mode */
.remission-preview--editable {
  .editable-field {
    position: relative;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
  }

  .editable-field:hover {
    background-color: rgba(59, 130, 246, 0.1);
    cursor: text;
  }

  .editable-field:focus-within {
    background-color: rgba(59, 130, 246, 0.2);
    outline: 2px solid rgb(59, 130, 246);
  }
}

/* Print Styles */
@media print {
  .remission-preview {
    margin: 0;
    box-shadow: none;
    page-break-after: always;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .remission-preview {
    width: 100%;
    min-height: auto;
    padding: 1rem;
  }
}

/* Component-specific styles will be added by child components */
