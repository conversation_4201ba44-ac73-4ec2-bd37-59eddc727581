"use server";

import { prisma } from "@/shared/lib/prisma";

import { Assignment } from "../components/wizard/components/AssignmentTable";

export async function getAssignmentsForRemission(
    contractorId: string,
    orderId: string,
) {
    try {
        // Obtener asignaciones con información completa
        const assignments = await prisma.assignment.findMany({
            where: {
                contractorId,
                orderId,
                status: "ACTIVE",
                cancelledAt: null,
            },
            include: {
                garmentSize: {
                    include: {
                        garment: {
                            include: {
                                model: true,
                                color: true,
                            },
                        },
                        size: true,
                    },
                },
                order: {
                    include: {
                        parts: true,
                    },
                },
            },
        });

        // Obtener información de la orden
        const order = await prisma.order.findUnique({
            where: { id: orderId },
            select: {
                id: true,
                cutOrder: true,
                createdAt: true,
                receivedDate: true,
                parts: {
                    select: {
                        id: true,
                        code: true,
                    },
                },
            },
        });

        // Formatear asignaciones para incluir todos los datos necesarios
        const formattedAssignments: Assignment[] = assignments.map((a) => ({
            id: a.id,
            orderId: a.orderId,
            garmentSizeId: a.garmentSizeId,
            quantity: a.quantity,
            defects: a.defects || 0,
            modelCode: a.garmentSize.garment.model.code,
            colorName: a.garmentSize.garment.color.name,
            sizeCode: a.garmentSize.size.code,
            cutOrder: order?.cutOrder || undefined,
            parts: order?.parts || [],
        }));

        return {
            success: true,
            data: {
                assignments: formattedAssignments,
                orderData: order
                    ? {
                          id: order.id,
                          cutOrder: order.cutOrder,
                          creationDate: order.receivedDate,
                          parts: order.parts,
                      }
                    : null,
            },
        };
    } catch (error) {
        console.error("Error fetching assignments for remission:", error);

        return {
            success: false,
            error: "Error al obtener las asignaciones",
        };
    }
}
