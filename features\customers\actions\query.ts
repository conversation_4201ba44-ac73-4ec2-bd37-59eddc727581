"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Obtiene todos los clientes con opciones de filtrado y paginación
 */
export async function getCustomers(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) {
    const {
        search,
        orderBy = "name",
        order = "asc",
        page = 1,
        perPage = 50,
    } = options;

    return await handleDbError(async () => {
        // Construir filtros
        const where = search
            ? {
                  name: {
                      contains: search,
                      mode: Prisma.QueryMode.insensitive,
                  },
              }
            : {};

        // Obtener datos con paginación
        const customers = await db.customer.findMany({
            where,
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
            include: {
                _count: {
                    select: { orders: true },
                },
            },
        });

        // Obtener total para paginación
        const total = await db.customer.count({ where });

        return {
            customers,
            pagination: {
                total,
                currentPage: page,
                lastPage: Math.ceil(total / perPage),
            },
        };
    }, "Error al obtener clientes");
}

/**
 * Obtiene un cliente por ID
 */
export async function getCustomer(id: string | null | undefined) {
    if (!id) return { success: false, error: "ID no válido" };

    return await handleDbError(async () => {
        const customer = await db.customer.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { orders: true },
                },
            },
        });

        if (!customer) {
            throw new Error("Cliente no encontrado");
        }

        return customer;
    }, "Error al obtener cliente");
}
