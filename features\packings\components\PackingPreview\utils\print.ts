// Print-specific utility functions

/**
 * Generate print-friendly CSS for the document
 */
export function getPrintStyles(): string {
    return `
    @media print {
      @page {
        size: A4;
        margin: 0;
      }
      
      body {
        margin: 0;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
      
      /* Hide non-printable elements */
      .no-print {
        display: none !important;
      }
      
      /* Ensure proper page breaks */
      .page-break-before {
        page-break-before: always;
      }
      
      .page-break-after {
        page-break-after: always;
      }
      
      .avoid-break {
        page-break-inside: avoid;
      }
    }
  `;
}

/**
 * Prepare document for printing
 */
export function preparePrintDocument(element: HTMLElement): void {
    // Add print styles
    const styleSheet = document.createElement("style");

    styleSheet.textContent = getPrintStyles();
    document.head.appendChild(styleSheet);

    // Force layout recalculation
    element.offsetHeight;

    // Clean up after print
    window.addEventListener(
        "afterprint",
        () => {
            document.head.removeChild(styleSheet);
        },
        { once: true },
    );
}

/**
 * Print the document
 */
export function printDocument(elementId: string): void {
    const element = document.getElementById(elementId);

    if (!element) {
        console.error(`Element with id "${elementId}" not found`);

        return;
    }

    preparePrintDocument(element);
    window.print();
}

/**
 * Export document as PDF using browser's print dialog
 */
export function exportAsPDF(elementId: string): void {
    // Same as print, but user should select "Save as PDF" in print dialog
    printDocument(elementId);
}

/**
 * Get optimal page orientation based on content
 */
export function getOptimalOrientation(items: any[]): "portrait" | "landscape" {
    // If we have many size columns, landscape might be better
    const maxSizes = Math.max(...items.map((item) => item.sizes?.length || 0));

    return maxSizes > 6 ? "landscape" : "portrait";
}

/**
 * Calculate if content fits in one page
 */
export function fitsInOnePage(
    contentHeight: number,
    orientation: "portrait" | "landscape",
): boolean {
    const mmToPx = 3.7795275591;
    const pageHeight = orientation === "portrait" ? 297 * mmToPx : 210 * mmToPx;
    const margin = 15 * mmToPx * 2; // Top and bottom margins

    return contentHeight <= pageHeight - margin;
}
