/**
 * Filtros para buscar y filtrar notas
 */
export interface NoteFilters {
    /**
     * ID del nivel de importancia para filtrar
     */
    importanceId?: string;

    /**
     * ID del estado para filtrar
     */
    statusId?: string;

    /**
     * ID del autor para filtrar
     */
    authorId?: string;

    /**
     * Texto de búsqueda que se aplicará al contenido
     */
    searchText?: string;

    /**
     * Rango de fechas para filtrar
     */
    dateRange?: {
        from?: Date;
        to?: Date;
    };

    /**
     * Página actual para paginación
     */
    page?: number;

    /**
     * Cantidad de items por página
     */
    pageSize?: number;

    /**
     * Filtros específicos para UI que se aplican en cliente
     */
    uiFilters?: {
        /**
         * Destacar notas nuevas
         */
        highlightNew?: boolean;

        /**
         * Ordenamiento personalizado
         */
        sortOrder?: "importance" | "date" | "status";
    };

    /**
     * Si se deben incluir comentarios en los resultados
     */
    includeComments?: boolean;
}

/**
 * Estructura de datos para un comentario de nota
 */
export interface NoteCommentData {
    id: string;
    content: string;
    createdAt: Date;
    updatedAt: Date;
    authorId: string;
    author: {
        id: string;
        name: string | null;
        image: string | null;
    };
    parentId: string | null;
    replies?: NoteCommentData[];
    _count?: {
        replies: number;
    };
}

/**
 * Resultado de una operación de comentario
 */
export interface CommentResult {
    success: boolean;
    data?: NoteCommentData | { id: string; deleted: boolean };
    error?: string;
}
