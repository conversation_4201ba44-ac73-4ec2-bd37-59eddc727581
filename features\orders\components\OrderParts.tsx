// components/OrderParts.tsx

import React from "react";
import { Card, Button, Input } from "@heroui/react";
import { CubeIcon, PlusIcon } from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { OrderPart } from "../types/orders";

interface OrderPartsProps {
    parts: OrderPart[];
    onAddPart: () => void;
    onRemovePart: (index: number) => void;
    onPartChange: (index: number, value: string) => void;
}

const OrderParts: React.FC<OrderPartsProps> = ({
    parts,
    onAddPart,
    onRemovePart,
    onPartChange,
}) => {
    return (
        <Card className="shadow-md rounded-lg p-6 bg-white dark:bg-gray-800">
            <div className="flex items-center gap-2 mb-4">
                <CubeIcon className="w-6 h-6" />
                <h2 className="text-2xl font-bold">Partidas</h2>
            </div>
            <AnimatePresence>
                {parts.map((part, index) => (
                    <motion.div
                        key={index}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center gap-4 mb-4"
                        exit={{ opacity: 0, y: 10 }}
                        initial={{ opacity: 0, y: -10 }}
                    >
                        <Input
                            label={`Código de Partida ${index + 1}`}
                            placeholder="Ej: PART-001"
                            value={part.code}
                            variant="bordered"
                            onChange={(e) =>
                                onPartChange(index, e.target.value)
                            }
                        />
                        <Button
                            color="danger"
                            variant="flat"
                            onPress={() => onRemovePart(index)}
                        >
                            Eliminar
                        </Button>
                    </motion.div>
                ))}
            </AnimatePresence>
            <Button
                className="flex items-center gap-2 text-blue-500 hover:text-blue-700 transition-all duration-150"
                variant="flat"
                onPress={onAddPart}
            >
                <PlusIcon className="w-4 h-4" />
                Agregar Partida
            </Button>
        </Card>
    );
};

export default OrderParts;
