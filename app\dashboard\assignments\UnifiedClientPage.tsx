"use client";

import type { SortOption } from "@/shared/components/dashboard";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    UserGroupIcon,
    ClipboardDocumentListIcon,
    CheckCircleIcon,
    ClockIcon,
    CubeTransparentIcon,
    ChartBarIcon,
    DocumentTextIcon,
    CalendarIcon,
    ShoppingBagIcon,
    TableCellsIcon,
    Squares2X2Icon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import { useAssignmentsData } from "@/features/assignments/hooks/useAssignmentsData";
import {
    Avatar,
    Badge,
    Chip,
    Progress,
    Tooltip,
    Button,
    ButtonGroup,
} from "@/shared/components/ui/hero-ui-client";
import MultiSelectDropdown from "@/shared/components/ui/MultiSelectDropdown";

import AssignmentsGrid from "./AssignmentsGrid";

interface Assignment {
    id: string;
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
    quantity: number;
    createdAt: string;
    order: {
        id: string;
        code: string;
        cutOrder?: string;
        customer?: {
            id: string;
            name: string;
        };
    };
    contractor: {
        id: string;
        name: string;
        email?: string;
    };
    garmentSize: {
        size: {
            code: string;
        };
        garment: {
            model: {
                id: string;
                code: string;
                name: string;
            };
            color: {
                name: string;
                hexCode: string;
            };
        };
    };
    remissionAssignments?: Array<{
        remission: {
            id: string;
            folio: string;
            printedAt?: string;
        };
    }>;
    remissions?: Array<{
        id: string;
        folio: string;
        printedAt?: string;
    }>;
}

// Mapa de colores para estados
const statusColors = {
    PENDING: "warning",
    IN_PROGRESS: "primary",
    COMPLETED: "success",
    CANCELLED: "danger",
} as const;

// Mapa de textos para estados
const statusLabels = {
    PENDING: "Pendiente",
    IN_PROGRESS: "En Proceso",
    COMPLETED: "Completada",
    CANCELLED: "Cancelada",
} as const;

export default function UnifiedAssignmentsPage() {
    const router = useRouter();
    const { assignments = [], isLoading, stats } = useAssignmentsData();

    const [searchValue, setSearchValue] = useState("");

    interface FilterValues {
        status?: string;
        customer?: string;
        period?: string;
        hasRemission?: string;
    }

    const [filterValues, setFilterValues] = useState<FilterValues>({});
    const [selectedContractors, setSelectedContractors] = useState<string[]>(
        [],
    );
    const [selectedModels, setSelectedModels] = useState<string[]>([]);
    const [viewMode, setViewMode] = useState<"grid" | "list">("list");
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "createdAt-desc",
        label: "Más reciente",
        field: "createdAt",
        direction: "desc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 12;

    // Calcular estadísticas dinámicas
    const dynamicStats = useMemo(() => {
        // Calcular cambios vs semana pasada
        const pendingChange =
            stats.pending > 0
                ? Math.round((stats.pending / stats.total) * 100)
                : 0;

        const completionRate =
            stats.total > 0
                ? Math.round((stats.completed / stats.total) * 100)
                : 0;

        return [
            {
                title: "Total Asignaciones",
                value: stats.total,
                icon: <ClipboardDocumentListIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Asignaciones totales",
            },
            {
                title: "Pendientes",
                value: stats.pending,
                icon: <ClockIcon className="w-6 h-6" />,
                color: "warning" as const,
                change: pendingChange,
                changeLabel: "% del total",
                changeType:
                    pendingChange > 50 ? "negative" : ("neutral" as const),
            },
            {
                title: "Completadas",
                value: stats.completed,
                icon: <CheckCircleIcon className="w-6 h-6" />,
                color: "success" as const,
                change: completionRate,
                changeLabel: "tasa de completado",
            },
            {
                title: "Total Prendas",
                value: stats.totalQuantity.toLocaleString("es-MX"),
                icon: <CubeTransparentIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: `Promedio: ${stats.averageQuantity}`,
            },
        ];
    }, [stats]);

    // Columnas de la tabla
    const columns = [
        {
            key: "order",
            label: "Orden",
            sortable: true,
            render: (assignment: Assignment) => (
                <div className="flex items-center gap-2">
                    <ShoppingBagIcon className="w-5 h-5 text-gray-400" />
                    <div>
                        <p className="font-bold text-primary">
                            {assignment.order.code}
                        </p>
                        <p className="text-xs text-gray-500">
                            {assignment.order.customer?.name}
                        </p>
                    </div>
                </div>
            ),
        },
        {
            key: "contractor",
            label: "Contratista",
            sortable: true,
            render: (assignment: Assignment) => (
                <div className="flex items-center gap-3">
                    <Avatar
                        color="secondary"
                        name={assignment.contractor.name}
                        size="sm"
                    />
                    <div>
                        <p className="font-medium">
                            {assignment.contractor.name}
                        </p>
                        {assignment.contractor.email && (
                            <p className="text-xs text-gray-500">
                                {assignment.contractor.email}
                            </p>
                        )}
                    </div>
                </div>
            ),
        },
        {
            key: "garment",
            label: "Prenda",
            render: (assignment: Assignment) => {
                const garment = assignment.garmentSize.garment;

                return (
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <span className="font-medium">
                                {garment.model.code}
                            </span>
                            <span className="text-sm text-gray-500">
                                - {garment.model.name}
                            </span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div
                                className="w-4 h-4 rounded-full border border-gray-300"
                                style={{
                                    backgroundColor: garment.color.hexCode,
                                }}
                            />
                            <span className="text-sm">
                                {garment.color.name}
                            </span>
                            <Badge size="sm" variant="flat">
                                Talla {assignment.garmentSize.size.code}
                            </Badge>
                        </div>
                    </div>
                );
            },
        },
        {
            key: "quantity",
            label: "Cantidad",
            render: (assignment: Assignment) => (
                <div className="flex items-center gap-2">
                    <CubeTransparentIcon className="w-4 h-4 text-gray-400" />
                    <span className="font-medium">{assignment.quantity}</span>
                    <span className="text-sm text-gray-500">prendas</span>
                </div>
            ),
        },
        {
            key: "status",
            label: "Estado",
            render: (assignment: Assignment) => {
                const color =
                    statusColors[
                        assignment.status as keyof typeof statusColors
                    ] || "default";
                const label =
                    statusLabels[
                        assignment.status as keyof typeof statusLabels
                    ] || assignment.status;

                return (
                    <Chip color={color} size="sm" variant="flat">
                        {label}
                    </Chip>
                );
            },
        },
        {
            key: "remission",
            label: "Remisión",
            render: (assignment: Assignment) => {
                const remission =
                    assignment.remissionAssignments?.[0]?.remission;

                if (!remission) {
                    return <span className="text-gray-400">Sin remisión</span>;
                }

                return (
                    <div className="flex items-center gap-2">
                        <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                        <button
                            className="text-blue-600 hover:text-blue-800 font-medium"
                            onClick={() =>
                                router.push(
                                    `/dashboard/remissions/${remission.id}`,
                                )
                            }
                        >
                            {remission.folio}
                        </button>
                        {remission.printedAt && (
                            <Tooltip content="Impresa">
                                <CheckCircleIcon className="w-4 h-4 text-green-500" />
                            </Tooltip>
                        )}
                    </div>
                );
            },
        },
        {
            key: "createdAt",
            label: "Fecha",
            sortable: true,
            render: (assignment: Assignment) => (
                <div className="text-sm">
                    <div className="flex items-center gap-1 text-gray-600">
                        <CalendarIcon className="w-3 h-3" />
                        <span>
                            {format(
                                new Date(assignment.createdAt),
                                "dd/MM/yyyy",
                                { locale: es },
                            )}
                        </span>
                    </div>
                </div>
            ),
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <ClipboardDocumentListIcon className="w-4 h-4" />,
            onClick: (assignment: Assignment) => {
                // Por ahora mostrar un toast, ya que no hay página de detalles
                addToast({
                    title: "Detalles de asignación",
                    description: `Asignación para ${assignment.contractor.name}`,
                    color: "primary",
                });
            },
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "PENDING", label: "Pendiente" },
                { value: "IN_PROGRESS", label: "En Proceso" },
                { value: "COMPLETED", label: "Completada" },
                { value: "CANCELLED", label: "Cancelada" },
            ],
        },
        {
            key: "customer",
            label: "Cliente",
            type: "select" as const,
            placeholder: "Todos los clientes",
            options: [
                { value: "all", label: "Todos" },
                ...Array.from(
                    new Set(
                        assignments
                            .map((a) => a.order.customer?.id)
                            .filter(Boolean),
                    ),
                )
                    .map((id) => {
                        const customer = assignments.find(
                            (a) => a.order.customer?.id === id,
                        )?.order.customer;

                        return customer
                            ? { value: id, label: customer.name }
                            : null;
                    })
                    .filter(
                        (option): option is { value: string; label: string } =>
                            option !== null,
                    ),
            ],
        },
        {
            key: "period",
            label: "Periodo",
            type: "select" as const,
            placeholder: "Todo el tiempo",
            options: [
                { value: "all", label: "Todo el tiempo" },
                { value: "today", label: "Hoy" },
                { value: "7", label: "Últimos 7 días" },
                { value: "30", label: "Últimos 30 días" },
                { value: "90", label: "Últimos 90 días" },
            ],
        },
        {
            key: "hasRemission",
            label: "Remisión",
            type: "select" as const,
            placeholder: "Todas",
            options: [
                { value: "all", label: "Todas" },
                { value: "with", label: "Con remisión" },
                { value: "without", label: "Sin remisión" },
            ],
        },
    ];

    // Obtener listas únicas para multiselects
    const uniqueContractors = useMemo(() => {
        const contractors = new Map();

        assignments.forEach((a) => {
            contractors.set(a.contractor.id, a.contractor.name);
        });

        return Array.from(contractors, ([value, label]) => ({ value, label }));
    }, [assignments]);

    const uniqueModels = useMemo(() => {
        const models = new Map();

        assignments.forEach((a) => {
            const model = a.garmentSize.garment.model;

            models.set(model.id, `${model.code} - ${model.name}`);
        });

        return Array.from(models, ([value, label]) => ({ value, label }));
    }, [assignments]);

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
        {
            key: "quantity-desc",
            label: "Mayor cantidad",
            field: "quantity",
            direction: "desc" as const,
        },
        {
            key: "quantity-asc",
            label: "Menor cantidad",
            field: "quantity",
            direction: "asc" as const,
        },
        {
            key: "contractor-asc",
            label: "Contratista A-Z",
            field: "contractor.name",
            direction: "asc" as const,
        },
    ];

    // Filtrar y ordenar datos localmente
    const filteredData = useMemo(() => {
        let filtered = [...assignments];

        // Búsqueda local
        if (searchValue) {
            const search = searchValue.toLowerCase();

            filtered = filtered.filter(
                (assignment) =>
                    assignment.order.code.toLowerCase().includes(search) ||
                    assignment.order.customer?.name
                        ?.toLowerCase()
                        .includes(search) ||
                    assignment.contractor.name.toLowerCase().includes(search) ||
                    assignment.garmentSize.garment.model.code
                        .toLowerCase()
                        .includes(search) ||
                    assignment.garmentSize.garment.color.name
                        .toLowerCase()
                        .includes(search),
            );
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            filtered = filtered.filter((a) => a.status === filterValues.status);
        }

        // Filtro por contratistas (multiselect)
        if (selectedContractors.length > 0) {
            filtered = filtered.filter((a) =>
                selectedContractors.includes(a.contractor.id),
            );
        }

        // Filtro por modelos (multiselect)
        if (selectedModels.length > 0) {
            filtered = filtered.filter((a) =>
                selectedModels.includes(a.garmentSize.garment.model.id),
            );
        }

        // Filtro por cliente
        if (filterValues.customer && filterValues.customer !== "all") {
            filtered = filtered.filter(
                (a) => a.order.customer?.id === filterValues.customer,
            );
        }

        // Filtro por remisión
        if (filterValues.hasRemission && filterValues.hasRemission !== "all") {
            const hasRemission = filterValues.hasRemission === "with";

            filtered = filtered.filter((a) => {
                const hasRem = a.remissions && a.remissions.length > 0;

                return hasRemission ? hasRem : !hasRem;
            });
        }

        // Filtro por periodo
        if (filterValues.period && filterValues.period !== "all") {
            const days = parseInt(filterValues.period);
            const startDate = new Date();

            startDate.setDate(startDate.getDate() - days);
            filtered = filtered.filter(
                (a) => new Date(a.createdAt) >= startDate,
            );
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [
        assignments,
        searchValue,
        filterValues,
        selectedContractors,
        selectedModels,
        currentSort,
    ]);

    // Componente de top contratistas
    const TopContractors = () => {
        if (stats.topContractors.length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
                initial={{ opacity: 0, y: 20 }}
            >
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <UserGroupIcon className="w-5 h-5 text-primary" />
                    Top 5 Contratistas
                </h3>
                <div className="space-y-3">
                    {stats.topContractors.map((contractor, index) => (
                        <div
                            key={index}
                            className="flex items-center justify-between"
                        >
                            <div className="flex items-center gap-3">
                                <span className="text-2xl font-bold text-gray-400">
                                    #{index + 1}
                                </span>
                                <div>
                                    <p className="font-medium">
                                        {contractor.contractor}
                                    </p>
                                    <p className="text-sm text-gray-500">
                                        {contractor.count} asignaciones
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="font-bold text-primary">
                                    {contractor.quantity.toLocaleString(
                                        "es-MX",
                                    )}
                                </p>
                                <p className="text-xs text-gray-500">prendas</p>
                            </div>
                        </div>
                    ))}
                </div>
            </motion.div>
        );
    };

    // Componente de modelos populares
    const PopularModels = () => {
        if (stats.topModels.length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.1 }}
            >
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <ChartBarIcon className="w-5 h-5 text-secondary" />
                    Modelos Más Asignados
                </h3>
                <div className="space-y-4">
                    {stats.topModels.map((model, index) => {
                        const percentage =
                            stats.totalQuantity > 0
                                ? (model.quantity / stats.totalQuantity) * 100
                                : 0;

                        return (
                            <div key={index}>
                                <div className="flex justify-between mb-1">
                                    <span className="text-sm font-medium">
                                        {model.model}
                                    </span>
                                    <span className="text-sm text-gray-500">
                                        {model.quantity.toLocaleString("es-MX")}{" "}
                                        prendas
                                    </span>
                                </div>
                                <Progress
                                    color="secondary"
                                    size="sm"
                                    value={percentage}
                                />
                            </div>
                        );
                    })}
                </div>
            </motion.div>
        );
    };

    // Paginación
    const paginatedData = useMemo(() => {
        const startIndex = (page - 1) * rowsPerPage;

        return filteredData.slice(startIndex, startIndex + rowsPerPage);
    }, [filteredData, page, rowsPerPage]);

    const totalPages = Math.ceil(filteredData.length / rowsPerPage);

    // Handler para ver detalles
    const handleViewDetails = (assignment: Assignment) => {
        addToast({
            title: "Detalles de asignación",
            description: `Asignación para ${assignment.contractor.name}`,
            color: "primary",
        });
    };

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={viewMode === "list" ? columns : []}
                currentSort={currentSort}
                filterValues={filterValues}
                isLoading={isLoading}
                page={page}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Gestión de asignaciones de producción"
                title="Asignaciones"
                totalPages={totalPages}
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                    setSelectedContractors([]);
                    setSelectedModels([]);
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={viewMode === "list" ? actions : []}
                // Create
                createRoute="/dashboard/assignments/new"
                breadcrumbs={[{ label: "Asignaciones" }]}
                // Stats
                stats={dynamicStats}
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key as keyof FilterValues] && filterValues[key as keyof FilterValues] !== "all",
                    ).length +
                    (selectedContractors.length > 0 ? 1 : 0) +
                    (selectedModels.length > 0 ? 1 : 0)
                }
                // Pagination
                createLabel="Nueva Asignación"
                // Filters
                filters={filters}
                data={viewMode === "list" ? paginatedData as any : []}
                // Table - Solo se muestra en vista lista
                emptyContent="No hay asignaciones registradas"
                onPageChange={setPage}
                // Additional actions
                additionalActions={
                    <ButtonGroup size="sm">
                        <Button
                            isIconOnly
                            variant={viewMode === "list" ? "solid" : "bordered"}
                            onPress={() => setViewMode("list")}
                        >
                            <TableCellsIcon className="w-4 h-4" />
                        </Button>
                        <Button
                            isIconOnly
                            variant={viewMode === "grid" ? "solid" : "bordered"}
                            onPress={() => setViewMode("grid")}
                        >
                            <Squares2X2Icon className="w-4 h-4" />
                        </Button>
                    </ButtonGroup>
                }
            />

            {/* Filtros multiselect fuera del template */}
            <div className="flex items-center gap-4 mt-4">
                <MultiSelectDropdown
                    label="Contratistas"
                    options={uniqueContractors}
                    placeholder="Todos los contratistas"
                    value={selectedContractors}
                    onChange={(value) => {
                        setSelectedContractors(value);
                        setPage(1);
                    }}
                />

                <MultiSelectDropdown
                    label="Modelos"
                    options={uniqueModels}
                    placeholder="Todos los modelos"
                    value={selectedModels}
                    onChange={(value) => {
                        setSelectedModels(value);
                        setPage(1);
                    }}
                />
            </div>

            {/* Vista Grid si está seleccionada */}
            {viewMode === "grid" && (
                <div className="mt-6">
                    <AssignmentsGrid
                        assignments={paginatedData as any}
                        isLoading={isLoading}
                        onViewDetails={handleViewDetails as any}
                    />
                </div>
            )}

            {/* Secciones adicionales */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <TopContractors />
                <PopularModels />
            </div>
        </>
    );
}
