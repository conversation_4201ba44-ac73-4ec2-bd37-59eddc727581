import React, { useState, useMemo } from "react";
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    ChevronDownIcon,
    ChevronUpIcon,
    ClipboardDocumentListIcon,
    CubeIcon,
    CalendarIcon,
    TruckIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";

import {
    Card,
    CardBody,
    CardHeader,
    Badge,
    Button,
    Input,
    Select,
    SelectItem,
    Chip,
    Progress,
    Avatar,
    Divider,
} from "@/shared/components/ui/hero-ui-client";

import { Assignment } from "../types/contractor-modal.types";

interface ContractorAssignmentsListProps {
    assignments: Assignment[];
    contractorName: string;
}

export default function ContractorAssignmentsList({
    assignments,
    contractorName,
}: ContractorAssignmentsListProps) {
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");
    const [expandedOrders, setExpandedOrders] = useState<Set<string>>(
        new Set(),
    );

    // Agrupar asignaciones por orden
    const groupedAssignments = useMemo(() => {
        const grouped = assignments.reduce(
            (acc, assignment) => {
                const orderId = assignment.order.id;

                if (!acc[orderId]) {
                    acc[orderId] = {
                        order: assignment.order,
                        assignments: [],
                        totalQuantity: 0,
                        completedQuantity: 0,
                    };
                }
                acc[orderId].assignments.push(assignment);
                acc[orderId].totalQuantity += assignment.quantity;

                // Sumar cantidades remisionadas
                const remittedQuantity =
                    assignment.remissions?.reduce(
                        (sum, r) => sum + r.quantity,
                        0,
                    ) || 0;

                acc[orderId].completedQuantity += remittedQuantity;

                return acc;
            },
            {} as Record<string, any>,
        );

        return Object.values(grouped);
    }, [assignments]);

    // Filtrar por búsqueda y estado
    const filteredGroups = useMemo(() => {
        return groupedAssignments.filter((group) => {
            // Filtro de búsqueda
            if (searchTerm) {
                const search = searchTerm.toLowerCase();
                const matchOrder =
                    group.order.orderCode.toLowerCase().includes(search) ||
                    group.order.cutOrder?.toLowerCase().includes(search) ||
                    group.order.customer.name.toLowerCase().includes(search);
                const matchModel = group.assignments.some(
                    (a: Assignment) =>
                        a.garmentSize?.garment.model.code
                            .toLowerCase()
                            .includes(search) ||
                        a.garmentSize?.garment.model.name
                            .toLowerCase()
                            .includes(search),
                );

                if (!matchOrder && !matchModel) return false;
            }

            // Filtro de estado
            if (statusFilter !== "all") {
                const hasStatus = group.assignments.some(
                    (a: Assignment) => a.status === statusFilter,
                );

                if (!hasStatus) return false;
            }

            return true;
        });
    }, [groupedAssignments, searchTerm, statusFilter]);

    const toggleOrderExpansion = (orderId: string) => {
        setExpandedOrders((prev) => {
            const newSet = new Set(prev);

            if (newSet.has(orderId)) {
                newSet.delete(orderId);
            } else {
                newSet.add(orderId);
            }

            return newSet;
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case "COMPLETED":
                return "success";
            case "IN_PROGRESS":
                return "warning";
            case "PENDING":
                return "default";
            default:
                return "default";
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case "COMPLETED":
                return "Completado";
            case "IN_PROGRESS":
                return "En Proceso";
            case "PENDING":
                return "Pendiente";
            default:
                return status;
        }
    };

    if (assignments.length === 0) {
        return (
            <div className="text-center py-20">
                <ClipboardDocumentListIcon className="w-16 h-16 mx-auto text-gray-300 dark:text-gray-600 mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                    No hay asignaciones registradas para {contractorName}
                </p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Filtros */}
            <div className="flex flex-col md:flex-row gap-4">
                <Input
                    className="flex-1"
                    placeholder="Buscar por orden, modelo o cliente..."
                    startContent={
                        <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
                    }
                    value={searchTerm}
                    onValueChange={setSearchTerm}
                />
                <Select
                    className="w-full md:w-48"
                    placeholder="Estado"
                    selectedKeys={[statusFilter]}
                    startContent={<FunnelIcon className="w-4 h-4" />}
                    onSelectionChange={(keys) =>
                        setStatusFilter(Array.from(keys)[0] as string)
                    }
                >
                    <SelectItem key="all">Todos</SelectItem>
                    <SelectItem key="PENDING">Pendientes</SelectItem>
                    <SelectItem key="IN_PROGRESS">En Proceso</SelectItem>
                    <SelectItem key="COMPLETED">Completados</SelectItem>
                </Select>
            </div>

            {/* Lista de órdenes */}
            <div className="space-y-4">
                {filteredGroups.map(
                    ({
                        order,
                        assignments: orderAssignments,
                        totalQuantity,
                        completedQuantity,
                    }) => {
                        const isExpanded = expandedOrders.has(order.id);
                        const completionPercentage =
                            totalQuantity > 0
                                ? (completedQuantity / totalQuantity) * 100
                                : 0;

                        // Agrupar tallas para mostrar resumen
                        const tallasResumen = orderAssignments.reduce(
                            (acc: any, assignment: any) => {
                                const sizeName =
                                    assignment.garmentSize?.size.name || "N/A";

                                acc[sizeName] =
                                    (acc[sizeName] || 0) + assignment.quantity;

                                return acc;
                            },
                            {} as Record<string, number>,
                        );

                        return (
                            <motion.div
                                key={order.id}
                                animate={{ opacity: 1, y: 0 }}
                                initial={{ opacity: 0, y: 20 }}
                                transition={{ duration: 0.3 }}
                            >
                                <Card className="overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                                    <CardHeader
                                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                                        onClick={() =>
                                            toggleOrderExpansion(order.id)
                                        }
                                    >
                                        <div className="flex items-center justify-between w-full gap-4">
                                            <div className="flex-1 w-full">
                                                <div className="flex items-center gap-3 mb-3">
                                                    <Avatar
                                                        color="primary"
                                                        name={
                                                            order.customer.name
                                                        }
                                                        size="sm"
                                                    />
                                                    <div className="flex-1">
                                                        <p className="font-semibold text-lg">
                                                            Orden{" "}
                                                            {order.orderCode}
                                                            {order.cutOrder &&
                                                                ` - Corte ${order.cutOrder}`}
                                                        </p>
                                                        <p className="text-sm text-gray-500">
                                                            {
                                                                order.customer
                                                                    .name
                                                            }
                                                        </p>
                                                    </div>
                                                </div>

                                                {/* Información del producto (mostrado una sola vez) */}
                                                {orderAssignments.length > 0 &&
                                                    orderAssignments[0]
                                                        .garmentSize && (
                                                        <div className="bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-lg p-3 mb-3 border border-gray-200 dark:border-gray-700">
                                                            <div className="flex items-center justify-between flex-wrap gap-3">
                                                                <div className="flex items-center gap-2">
                                                                    <CubeIcon className="w-5 h-5 text-primary" />
                                                                    <span className="font-semibold text-base">
                                                                        {
                                                                            orderAssignments[0]
                                                                                .garmentSize
                                                                                .garment
                                                                                .model
                                                                                .code
                                                                        }
                                                                    </span>
                                                                    <span className="text-sm text-gray-600 dark:text-gray-400">
                                                                        {
                                                                            orderAssignments[0]
                                                                                .garmentSize
                                                                                .garment
                                                                                .model
                                                                                .name
                                                                        }
                                                                    </span>
                                                                </div>
                                                                <div className="flex items-center gap-2">
                                                                    <div
                                                                        className="w-6 h-6 rounded-full border-2 border-gray-300 shadow-sm"
                                                                        style={{
                                                                            backgroundColor:
                                                                                orderAssignments[0]
                                                                                    .garmentSize
                                                                                    .garment
                                                                                    .color
                                                                                    .hexCode,
                                                                        }}
                                                                    />
                                                                    <span className="font-medium">
                                                                        {
                                                                            orderAssignments[0]
                                                                                .garmentSize
                                                                                .garment
                                                                                .color
                                                                                .name
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )}

                                                <div className="flex flex-wrap items-center gap-2 mb-3">
                                                    {/* Partidas */}
                                                    {order.parts &&
                                                        order.parts.length >
                                                            0 && (
                                                            <div className="flex items-center gap-1">
                                                                <CubeIcon className="w-4 h-4 text-gray-400" />
                                                                {order.parts.map(
                                                                    (
                                                                        part: any,
                                                                    ) => (
                                                                        <Badge
                                                                            key={
                                                                                part.id
                                                                            }
                                                                            color="primary"
                                                                            size="sm"
                                                                            variant="flat"
                                                                        >
                                                                            {
                                                                                part.code
                                                                            }
                                                                        </Badge>
                                                                    ),
                                                                )}
                                                            </div>
                                                        )}

                                                    {/* Estado de la orden */}
                                                    <Chip
                                                        color={getStatusColor(
                                                            order.status,
                                                        )}
                                                        size="sm"
                                                        variant="flat"
                                                    >
                                                        {getStatusLabel(
                                                            order.status,
                                                        )}
                                                    </Chip>

                                                    {/* Fecha de entrega */}
                                                    {order.estimatedDeliveryDate && (
                                                        <div className="flex items-center gap-1 text-sm text-gray-500">
                                                            <CalendarIcon className="w-4 h-4" />
                                                            {format(
                                                                new Date(
                                                                    order.estimatedDeliveryDate,
                                                                ),
                                                                "dd MMM yyyy",
                                                                { locale: es },
                                                            )}
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Progreso */}
                                                <div className="mt-4">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                                            Progreso:{" "}
                                                            {completedQuantity}{" "}
                                                            / {totalQuantity}{" "}
                                                            piezas
                                                        </span>
                                                        <span className="text-sm font-semibold">
                                                            {completionPercentage.toFixed(
                                                                0,
                                                            )}
                                                            %
                                                        </span>
                                                    </div>
                                                    <Progress
                                                        aria-label="Progreso de la orden"
                                                        className="h-2"
                                                        color={
                                                            completionPercentage ===
                                                            100
                                                                ? "success"
                                                                : completionPercentage >
                                                                    0
                                                                  ? "warning"
                                                                  : "default"
                                                        }
                                                        value={
                                                            completionPercentage
                                                        }
                                                    />
                                                </div>

                                                {/* Resumen de tallas */}
                                                <div className="mt-3 flex flex-wrap items-center gap-2">
                                                    <span className="text-sm text-gray-600 dark:text-gray-400">
                                                        Tallas:
                                                    </span>
                                                    {Object.entries(
                                                        tallasResumen,
                                                    ).map(
                                                        ([size, qty]: [
                                                            string,
                                                            any,
                                                        ]) => (
                                                            <Badge
                                                                key={size}
                                                                color="secondary"
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {size}: {qty}
                                                            </Badge>
                                                        ),
                                                    )}
                                                </div>
                                            </div>

                                            <Button
                                                isIconOnly
                                                size="sm"
                                                variant="light"
                                            >
                                                {isExpanded ? (
                                                    <ChevronUpIcon className="w-5 h-5" />
                                                ) : (
                                                    <ChevronDownIcon className="w-5 h-5" />
                                                )}
                                            </Button>
                                        </div>
                                    </CardHeader>

                                    <AnimatePresence>
                                        {isExpanded && (
                                            <motion.div
                                                animate={{
                                                    height: "auto",
                                                    opacity: 1,
                                                }}
                                                exit={{ height: 0, opacity: 0 }}
                                                initial={{
                                                    height: 0,
                                                    opacity: 0,
                                                }}
                                                transition={{ duration: 0.3 }}
                                            >
                                                <Divider />
                                                <CardBody>
                                                    <div className="grid grid-cols-1 gap-3">
                                                        {orderAssignments.map(
                                                            (
                                                                assignment: Assignment,
                                                            ) => (
                                                                <div
                                                                    key={
                                                                        assignment.id
                                                                    }
                                                                    className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                                                                >
                                                                    <div className="flex items-center justify-between gap-3">
                                                                        <div className="flex items-center gap-4 flex-1">
                                                                            {/* Talla */}
                                                                            <div className="flex flex-col items-center bg-primary/10 dark:bg-primary/20 rounded-lg px-4 py-2">
                                                                                <span className="text-xs text-gray-600 dark:text-gray-400 uppercase font-medium">
                                                                                    Talla
                                                                                </span>
                                                                                <span className="text-xl font-bold text-primary">
                                                                                    {assignment
                                                                                        .garmentSize
                                                                                        ?.size
                                                                                        .name ||
                                                                                        "N/A"}
                                                                                </span>
                                                                            </div>

                                                                            {/* Cantidad */}
                                                                            <div className="flex flex-col flex-1">
                                                                                <span className="text-base font-semibold">
                                                                                    {
                                                                                        assignment.quantity
                                                                                    }{" "}
                                                                                    piezas
                                                                                </span>
                                                                                {assignment.remissions &&
                                                                                    assignment
                                                                                        .remissions
                                                                                        .length >
                                                                                        0 && (
                                                                                        <div className="flex items-center gap-1 mt-1">
                                                                                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                                                                <div
                                                                                                    className="bg-green-500 h-2 rounded-full transition-all"
                                                                                                    style={{
                                                                                                        width: `${(assignment.remissions.reduce((sum, r) => sum + r.quantity, 0) / assignment.quantity) * 100}%`,
                                                                                                    }}
                                                                                                />
                                                                                            </div>
                                                                                            <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                                                                                                {assignment.remissions.reduce(
                                                                                                    (
                                                                                                        sum,
                                                                                                        r,
                                                                                                    ) =>
                                                                                                        sum +
                                                                                                        r.quantity,
                                                                                                    0,
                                                                                                )}

                                                                                                /
                                                                                                {
                                                                                                    assignment.quantity
                                                                                                }
                                                                                            </span>
                                                                                        </div>
                                                                                    )}
                                                                            </div>
                                                                        </div>

                                                                        {/* Estado */}
                                                                        <Chip
                                                                            color={getStatusColor(
                                                                                assignment.status ||
                                                                                    "PENDING",
                                                                            )}
                                                                            size="sm"
                                                                            variant="dot"
                                                                        >
                                                                            {getStatusLabel(
                                                                                assignment.status ||
                                                                                    "PENDING",
                                                                            )}
                                                                        </Chip>
                                                                    </div>

                                                                    {/* Remisiones */}
                                                                    {assignment.remissions &&
                                                                        assignment
                                                                            .remissions
                                                                            .length >
                                                                            0 && (
                                                                            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                                                                                <div className="flex items-start gap-2">
                                                                                    <TruckIcon className="w-4 h-4 text-green-600 mt-0.5" />
                                                                                    <div className="text-sm">
                                                                                        <span className="font-medium text-gray-700 dark:text-gray-300">
                                                                                            Remisiones:
                                                                                        </span>
                                                                                        <div className="mt-1 space-y-1">
                                                                                            {assignment.remissions.map(
                                                                                                (
                                                                                                    r,
                                                                                                ) => (
                                                                                                    <div
                                                                                                        key={
                                                                                                            r.id
                                                                                                        }
                                                                                                        className="text-gray-600 dark:text-gray-400"
                                                                                                    >
                                                                                                        Folio{" "}
                                                                                                        {
                                                                                                            r
                                                                                                                .remission
                                                                                                                .folio
                                                                                                        }{" "}
                                                                                                        -{" "}
                                                                                                        {
                                                                                                            r.quantity
                                                                                                        }{" "}
                                                                                                        piezas
                                                                                                    </div>
                                                                                                ),
                                                                                            )}
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                </div>
                                                            ),
                                                        )}
                                                    </div>
                                                </CardBody>
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </Card>
                            </motion.div>
                        );
                    },
                )}
            </div>

            {filteredGroups.length === 0 && (
                <div className="text-center py-10">
                    <p className="text-gray-500 dark:text-gray-400">
                        No se encontraron asignaciones que coincidan con los
                        filtros
                    </p>
                </div>
            )}
        </div>
    );
}
