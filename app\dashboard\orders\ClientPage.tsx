"use client";

import React, { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
    ArrowPathIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";
import { addToast } from "@heroui/react";

import { Order } from "@/features/orders/types/orders";
import { Order as OrderModalType } from "@/features/orders/components/OrderDetailModal/types/order-modal.types";
import OrderDetailModal from "@/features/orders/components/OrderDetailModal";
import OrderDashboard from "@/features/orders/components/OrderDashboard";

// Importar los hooks personalizados
import { useOrdersState } from "@/features/orders/hooks/useOrdersState";
import { useOrdersData } from "@/features/orders/hooks/useOrdersData";

interface OrdersClientPageProps {
    currentUserId?: string;
    userRole?: string;
}

/**
 * Componente principal para la gestión de órdenes
 * Implementa una arquitectura basada en hooks para mejor separación de responsabilidades
 */
export default function OrdersClientPage({
    currentUserId,
    userRole,
}: OrdersClientPageProps) {
    const router = useRouter();

    // Usar hooks personalizados para manejar estado y datos
    const { state, actions } = useOrdersState();
    const {
        data: { orders, orderStatuses },
        status: { isLoading, isRevalidating },
        operations: {
            mutate,
            deleteOrder,
            verifyAndCreateParts,
            fetchOrder,
            handleDelete,
        },
    } = useOrdersData(state);

    // Estado para el modal de detalles (independiente de los parámetros URL)
    const [selectedOrder, setSelectedOrder] = useState<OrderModalType | null>(
        null,
    );
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

    /**
     * Manejador para abrir el detalle de una orden
     */
    const handleOpenDetail = useCallback(
        async (order: Order) => {
            // Mostrar toast de carga
            addToast({
                title: "Cargando detalles",
                description: `Obteniendo información de la orden ${order.cutOrder || order.id.substring(0, 6)}...`,
                icon: <ArrowPathIcon className="w-5 h-5 animate-spin" />,
                color: "primary",
                timeout: 5000, // Se cierra después de 5 segundos si no se completa antes
            });

            try {
                // Limpiar cualquier orden anterior
                setSelectedOrder(null);

                // Primero abrir el modal con un estado de carga
                setIsDetailModalOpen(true);

                // Verificar si estamos en la vista de calendario para evitar revalidaciones
                const isCalendarView = state.view === "calendar";

                // Obtener los datos completos de la orden sin revalidar cuando estamos en calendario
                const orderDetails = await fetchOrder(
                    order.id,
                    !isCalendarView,
                );

                if (orderDetails) {
                    // Mostrar toast de éxito
                    addToast({
                        title: "Detalles cargados",
                        description: `Orden ${orderDetails.cutOrder || orderDetails.id.substring(0, 6)} lista para visualizar`,
                        icon: <CheckCircleIcon className="w-5 h-5" />,
                        color: "success",
                        timeout: 2000, // Cerrar después de 2 segundos
                    });

                    // Pasar la orden directamente sin adaptador
                    setSelectedOrder(orderDetails as OrderModalType);
                } else {
                    // Si no se pudo obtener la orden, mostrar toast de error y cerrar el modal
                    addToast({
                        title: "Error al cargar",
                        description:
                            "No se pudieron obtener los detalles de la orden",
                        icon: <ExclamationCircleIcon className="w-5 h-5" />,
                        color: "danger",
                        timeout: 3000,
                    });

                    // Error al obtener detalles de la orden - comentario removido
                    closeDetailModal();
                }
            } catch (error) {
                // Mostrar toast de error
                addToast({
                    title: "Error al cargar",
                    description:
                        "Ocurrió un error al obtener los detalles de la orden",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                    color: "danger",
                    timeout: 3000,
                });

                // Error al obtener los detalles de la orden - comentario removido
                closeDetailModal();
            }
        },
        [fetchOrder],
    );

    /**
     * Manejador para cerrar el modal de detalles
     */
    const closeDetailModal = useCallback(() => {
        // Cerrar el modal primero
        setIsDetailModalOpen(false);

        // Esperar a que la animación del modal termine antes de limpiar el estado
        setTimeout(() => {
            setSelectedOrder(null);
        }, 500);
    }, []);

    /**
     * Manejador para verificar partes de una orden
     */
    const handleVerifyParts = useCallback(
        async (orderId: string) => {
            try {
                await verifyAndCreateParts(orderId);
                if (typeof mutate === "function") {
                    mutate();
                }
            } catch (error) {
                // REMOVED: console.error("Error verificando partes:", error);
            }
        },
        [verifyAndCreateParts, mutate],
    );

    /**
     * Manejador para eliminar una orden desde el modal
     */
    const handleModalDeleteOrder = useCallback(
        async (orderId: string) => {
            const deleted = await handleDelete(orderId);

            if (deleted && isDetailModalOpen) {
                closeDetailModal();
            }
        },
        [handleDelete, isDetailModalOpen, closeDetailModal],
    );

    /**
     * Manejador para cambiar entre vistas grid/list dentro del dashboard
     */
    const handleViewModeChange = useCallback((mode: "grid" | "list") => {
        // Este manejador es solo para cambios entre vistas de grid y list
        // No se necesita implementación adicional ya que todo se maneja en useOrdersState
    }, []);

    // Animaciones para la página
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.08,
                delayChildren: 0.1,
            },
        },
    };

    return (
        <div className="flex-1 flex flex-col min-h-0">
            {/* Cabecera */}
            <div className="flex justify-between mb-4">
                <h1 className="text-2xl font-bold">Gestión de Órdenes</h1>
            </div>

            {/* Vista principal de órdenes */}
            <OrderDashboard
                filterValue={state.status}
                initialSortColumn={state.sortBy}
                initialSortDirection={state.sortDirection}
                isLoading={isLoading}
                isRefreshing={isRevalidating}
                orders={orders}
                pagination={{
                    total: orders.length,
                    currentPage: state.page,
                    lastPage: Math.ceil(orders.length / state.limit),
                }}
                perPage={state.limit}
                refreshData={mutate}
                searchValue={state.search}
                viewMode="grid"
                onCreateNewOrder={() => router.push("/dashboard/orders/new")}
                onDeleteOrder={(order) => handleDelete(order.id)}
                onEditOrder={(order) =>
                    router.push(`/dashboard/orders/${order.id}/edit`)
                }
                onFilterChange={actions.setStatus}
                onOpenDetail={handleOpenDetail}
                onPageChange={(p) => {
                    actions.setPage(p);
                    window.scrollTo(0, 0);
                }}
                onPerPageChange={(p) => actions.setLimit(p)}
                onSearchChange={actions.setSearch}
                onSelectOrder={handleOpenDetail}
                onSortChange={(column, direction) =>
                    actions.setSort(column || "", direction)
                }
                onViewModeChange={handleViewModeChange}
            />

            {/* Modal de detalles */}
            {selectedOrder && (
                <OrderDetailModal
                    currentUserId={currentUserId}
                    isOpen={isDetailModalOpen}
                    order={selectedOrder}
                    userRole={userRole}
                    onClose={closeDetailModal}
                    onDelete={handleModalDeleteOrder}
                />
            )}
        </div>
    );
}
