"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@heroui/react";
import {
    ExclamationTriangleIcon,
    ArrowPathIcon,
} from "@heroicons/react/24/outline";

export default function Error({
    error,
    reset,
}: {
    error: Error & { digest?: string };
    reset: () => void;
}) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error("Global error boundary caught:", error);

        // Send error to monitoring service (e.g., Sentry)
        if (typeof window !== "undefined" && (window as any).gtag) {
            (window as any).gtag("event", "exception", {
                description: error.message,
                fatal: true,
            });
        }
    }, [error]);

    return (
        <div className="min-h-screen flex items-center justify-center px-4">
            <div className="max-w-md w-full text-center">
                <div className="mb-8">
                    <ExclamationTriangleIcon className="w-24 h-24 text-red-500 mx-auto mb-4" />
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        Algo salió mal
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400">
                        Ha ocurrido un error inesperado. Nuestro equipo ha sido
                        notificado.
                    </p>
                </div>

                <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-6">
                    <p className="text-sm text-gray-700 dark:text-gray-300 font-mono">
                        {error.message || "Error desconocido"}
                    </p>
                    {error.digest && (
                        <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                            ID de error: {error.digest}
                        </p>
                    )}
                </div>

                <div className="space-y-3">
                    <Button
                        className="w-full"
                        color="primary"
                        size="lg"
                        startContent={<ArrowPathIcon className="w-5 h-5" />}
                        onPress={() => reset()}
                    >
                        Intentar de nuevo
                    </Button>

                    <Button
                        className="w-full"
                        size="lg"
                        variant="flat"
                        onPress={() => (window.location.href = "/dashboard")}
                    >
                        Volver al inicio
                    </Button>
                </div>

                <p className="text-sm text-gray-500 dark:text-gray-400 mt-6">
                    Si el problema persiste, por favor contacta a soporte.
                </p>
            </div>
        </div>
    );
}
