"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";

import { useVisualFeedback, FeedbackType } from "../hooks/useVisualFeedback";

const feedbackConfig: Record<
    FeedbackType,
    {
        icon: React.ElementType;
        bgColor: string;
        textColor: string;
        borderColor: string;
    }
> = {
    success: {
        icon: CheckCircleIcon,
        bgColor: "bg-green-50 dark:bg-green-900/20",
        textColor: "text-green-800 dark:text-green-200",
        borderColor: "border-green-200 dark:border-green-800",
    },
    error: {
        icon: XCircleIcon,
        bgColor: "bg-red-50 dark:bg-red-900/20",
        textColor: "text-red-800 dark:text-red-200",
        borderColor: "border-red-200 dark:border-red-800",
    },
    warning: {
        icon: ExclamationTriangleIcon,
        bgColor: "bg-amber-50 dark:bg-amber-900/20",
        textColor: "text-amber-800 dark:text-amber-200",
        borderColor: "border-amber-200 dark:border-amber-800",
    },
    info: {
        icon: InformationCircleIcon,
        bgColor: "bg-blue-50 dark:bg-blue-900/20",
        textColor: "text-blue-800 dark:text-blue-200",
        borderColor: "border-blue-200 dark:border-blue-800",
    },
    loading: {
        icon: () => <Spinner size="sm" />,
        bgColor: "bg-gray-50 dark:bg-gray-900/20",
        textColor: "text-gray-800 dark:text-gray-200",
        borderColor: "border-gray-200 dark:border-gray-800",
    },
};

export function FeedbackToast() {
    const { feedbackItems, clearFeedback } = useVisualFeedback();

    return (
        <div className="fixed top-20 right-4 z-50 space-y-2 max-w-sm">
            <AnimatePresence mode="popLayout">
                {feedbackItems.map((item) => {
                    const config = feedbackConfig[item.type];
                    const Icon = config.icon;

                    return (
                        <motion.div
                            key={item.id}
                            layout
                            animate={{ opacity: 1, x: 0, scale: 1 }}
                            className={`
                                flex items-start gap-3 p-4 rounded-lg border shadow-lg
                                ${config.bgColor} ${config.borderColor}
                            `}
                            exit={{ opacity: 0, x: 50, scale: 0.9 }}
                            initial={{ opacity: 0, x: 50, scale: 0.9 }}
                            transition={{
                                type: "spring",
                                stiffness: 500,
                                damping: 40,
                            }}
                        >
                            <div
                                className={`flex-shrink-0 ${config.textColor}`}
                            >
                                <Icon className="w-5 h-5" />
                            </div>

                            <div className="flex-1 min-w-0">
                                <p
                                    className={`text-sm font-medium ${config.textColor}`}
                                >
                                    {item.message}
                                </p>

                                {item.action && (
                                    <Button
                                        className={`mt-2 ${config.textColor}`}
                                        size="sm"
                                        variant="light"
                                        onPress={item.action.onClick}
                                    >
                                        {item.action.label}
                                    </Button>
                                )}
                            </div>

                            {item.type !== "loading" && (
                                <button
                                    className={`
                                        flex-shrink-0 p-1 rounded hover:bg-black/5 
                                        dark:hover:bg-white/5 transition-colors
                                        ${config.textColor}
                                    `}
                                    onClick={() => clearFeedback(item.id)}
                                >
                                    <XMarkIcon className="w-4 h-4" />
                                </button>
                            )}
                        </motion.div>
                    );
                })}
            </AnimatePresence>
        </div>
    );
}
