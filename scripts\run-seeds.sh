#!/bin/bash
# Script para ejecutar los seeds de datos iniciales en LOHARI
# Uso: ./run-seeds.sh

echo "🚀 Ejecutando seeds de datos iniciales para LOHARI..."

# Verificar si existe el archivo .env
if [ ! -f .env ]; then
    echo "❌ Error: No se encontró el archivo .env"
    echo "Por favor, copia .env.example a .env y configura tu DATABASE_URL"
    exit 1
fi

# Opción 1: Ejecutar el seed TypeScript con ts-node
if command -v ts-node &> /dev/null; then
    echo "📦 Ejecutando seed con ts-node..."
    npx ts-node prisma/seed.ts
elif command -v tsx &> /dev/null; then
    echo "📦 Ejecutando seed con tsx..."
    npx tsx prisma/seed.ts
else
    echo "⚠️  ts-node/tsx no encontrado, usando node directamente..."
    # Compilar TypeScript a JavaScript primero
    npx tsc prisma/seed.ts --outDir prisma/
    node prisma/seed.js
fi

echo "✅ Seeds ejecutados exitosamente!"
