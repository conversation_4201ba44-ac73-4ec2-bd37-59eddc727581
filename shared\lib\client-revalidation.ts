"use client";

import { mutate } from "swr";
import { useEffect, useState } from "react";

import {
    subscribeToRevalidation,
    unsubscribeFromRevalidation,
    triggerRevalidation,
} from "@/shared/providers/swr-provider";

/**
 * Crea helpers de revalidación para el cliente
 * @param entityName Nombre de la entidad en singular (ej: "size", "color", "model")
 * @returns Función para revalidar datos en el cliente y hook para escuchar eventos
 */
export function createClientRevalidation(entityName: string) {
    // Determinar la forma plural del nombre (simplificado)
    const pluralName = entityName + "s";

    /**
     * Revalida todos los datos relacionados con la entidad en el cliente
     * @param id ID opcional de la entidad específica
     */
    async function revalidateEntityData(id?: string) {
        // Lista de promesas de revalidación
        const revalidationPromises: Promise<any>[] = [];

        if (id) {
            // Revalidación específica cuando hay un ID
            // 1. Revalidar la entidad específica
            revalidationPromises.push(
                mutate([entityName, id], undefined, {
                    revalidate: true,
                    populateCache: true,
                }),
            );

            // 2. Revalidar listas que puedan contener esta entidad
            revalidationPromises.push(
                mutate(
                    (key) => Array.isArray(key) && key[0] === pluralName,
                    undefined,
                    {
                        revalidate: true,
                        populateCache: true,
                    },
                ),
            );
        } else {
            // Revalidación general cuando no hay ID específico
            revalidationPromises.push(
                mutate(
                    (key) => Array.isArray(key) && key[0] === pluralName,
                    undefined,
                    {
                        revalidate: true,
                        populateCache: true,
                    },
                ),
            );
        }

        // 3. Revalidar recursos que puedan depender de la entidad
        // (solo si es necesario, no siempre)
        const entityWithDependencies = [
            "model",
            "color",
            "size",
            "contractor",
        ].includes(entityName);

        if (entityWithDependencies) {
            if (entityName === "contractor") {
                // Para contratistas, revalidar también getContractors
                revalidationPromises.push(
                    mutate(
                        (key) =>
                            Array.isArray(key) && key[0] === "getContractors",
                        undefined,
                        { revalidate: true, populateCache: true },
                    ),
                );
            } else {
                // Para otras entidades con dependencias
                revalidationPromises.push(
                    mutate(
                        (key) => Array.isArray(key) && key[0] === "garments",
                        undefined,
                        { revalidate: true },
                    ),
                );
            }
        }

        // Ejecutar todas las revalidaciones en paralelo
        if (process.env.NODE_ENV !== "production") {
            // Revalidando datos - comentario removido
        }

        try {
            await Promise.all(revalidationPromises);

            if (process.env.NODE_ENV !== "production") {
                // Datos revalidados correctamente - comentario removido
            }

            // Disparar evento personalizado para notificar a todos los componentes
            triggerRevalidation(pluralName, id);
        } catch (error) {
            if (process.env.NODE_ENV !== "production") {
                // Error al revalidar datos - comentario removido
            }
            // Solo en caso de error, intentar una revalidación más amplia
            await mutate(
                (key) =>
                    Array.isArray(key) &&
                    (key[0] === pluralName ||
                        key[0] === entityName ||
                        (entityWithDependencies && key[0] === "garments")),
                undefined,
                { revalidate: true },
            );
        }
    }

    /**
     * Hook para escuchar eventos de revalidación
     * @param id ID opcional para escuchar revalidaciones específicas
     * @returns Boolean indicando si hay revalidación en curso
     */
    function useRevalidationListener(): boolean;
    function useRevalidationListener(id?: string): boolean;
    function useRevalidationListener(
        id?: string,
        callback?: (id?: string) => void,
    ): boolean {
        const [isRevalidating, setIsRevalidating] = useState(false);

        useEffect(() => {
            // Manejador para actualizar el estado cuando ocurre revalidación
            const handleRevalidation = (eventId?: string) => {
                // Si no hay ID específico o coincide con el ID proporcionado
                if (!id || !eventId || id === eventId) {
                    setIsRevalidating(true);

                    // Ejecutar callback si existe
                    if (callback) {
                        callback(eventId);
                    }

                    // Resetear después de un breve tiempo para mostrar el indicador
                    setTimeout(() => setIsRevalidating(false), 1000);
                }
            };

            // Suscribirse a eventos de revalidación
            subscribeToRevalidation(pluralName, handleRevalidation);

            // Limpieza al desmontar
            return () => {
                unsubscribeFromRevalidation(pluralName, handleRevalidation);
            };
        }, [id, callback]);

        return isRevalidating;
    }

    return {
        revalidateData: revalidateEntityData,
        useRevalidationListener,
    };
}
