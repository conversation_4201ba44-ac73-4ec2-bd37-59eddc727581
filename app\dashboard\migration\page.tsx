"use client";

import { useEffect, useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { Card } from "@heroui/react";
import {
    CheckCircleIcon,
    XCircleIcon,
    ClockIcon,
    ArrowPathIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

// Initialize Supabase client
const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
);

interface FeatureFlag {
    key: string;
    name: string;
    enabled: boolean;
    rollout_percentage: number;
    metadata?: any;
    updated_at: string;
}

interface MigrationProgress {
    id: string;
    phase: string;
    status: string;
    started_at?: string;
    completed_at?: string;
    duration_ms?: number;
    metrics?: any;
    errors?: any[];
}

export default function MigrationDashboard() {
    const [flags, setFlags] = useState<FeatureFlag[]>([]);
    const [progress, setProgress] = useState<MigrationProgress[]>([]);
    const [loading, setLoading] = useState(true);
    const [realTimeEnabled, setRealTimeEnabled] = useState(true);

    useEffect(() => {
        // Load initial data
        loadData();

        // Set up real-time subscriptions
        const channel = supabase
            .channel("migration-dashboard")
            .on(
                "postgres_changes",
                { event: "*", schema: "public", table: "feature_flags" },
                (payload) => {
                    console.log("Feature flag change:", payload);
                    loadFlags();
                },
            )
            .on(
                "postgres_changes",
                { event: "*", schema: "public", table: "migration_progress" },
                (payload) => {
                    console.log("Migration progress change:", payload);
                    loadProgress();
                },
            )
            .subscribe();

        return () => {
            supabase.removeChannel(channel);
        };
    }, []);

    const loadData = async () => {
        setLoading(true);
        await Promise.all([loadFlags(), loadProgress()]);
        setLoading(false);
    };

    const loadFlags = async () => {
        const { data, error } = await supabase
            .from("feature_flags")
            .select("*")
            .order("created_at", { ascending: true });

        if (!error && data) {
            setFlags(data);
        }
    };

    const loadProgress = async () => {
        const { data, error } = await supabase
            .from("migration_progress")
            .select("*")
            .order("created_at", { ascending: false })
            .limit(20);

        if (!error && data) {
            setProgress(data);
        }
    };

    const toggleFlag = async (flag: FeatureFlag) => {
        const { error } = await supabase
            .from("feature_flags")
            .update({ enabled: !flag.enabled })
            .eq("key", flag.key);

        if (!error) {
            await loadFlags();
        }
    };

    const updateRollout = async (flag: FeatureFlag, percentage: number) => {
        const { error } = await supabase
            .from("feature_flags")
            .update({ rollout_percentage: percentage })
            .eq("key", flag.key);

        if (!error) {
            await loadFlags();
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "completed":
                return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
            case "failed":
                return <XCircleIcon className="w-5 h-5 text-red-500" />;
            case "in_progress":
                return (
                    <ClockIcon className="w-5 h-5 text-blue-500 animate-spin" />
                );
            case "rolled_back":
                return <ArrowPathIcon className="w-5 h-5 text-orange-500" />;
            default:
                return <ClockIcon className="w-5 h-5 text-gray-400" />;
        }
    };

    const getRiskColor = (risk?: string) => {
        switch (risk) {
            case "high":
                return "text-red-500";
            case "medium":
                return "text-yellow-500";
            case "low":
                return "text-green-500";
            default:
                return "text-gray-500";
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500" />
            </div>
        );
    }

    return (
        <div className="p-8 max-w-7xl mx-auto">
            <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Migration Dashboard</h1>
                <p className="text-gray-600">
                    Zero-budget migration system powered by Supabase
                </p>
            </div>

            {/* Real-time indicator */}
            <div className="mb-6 flex items-center gap-2">
                <div
                    className={`w-3 h-3 rounded-full ${realTimeEnabled ? "bg-green-500 animate-pulse" : "bg-red-500"}`}
                />
                <span className="text-sm text-gray-600">
                    {realTimeEnabled
                        ? "Real-time updates active"
                        : "Real-time updates inactive"}
                </span>
            </div>

            {/* Feature Flags Section */}
            <Card className="mb-8 p-6">
                <h2 className="text-xl font-semibold mb-4">Feature Flags</h2>
                <div className="space-y-4">
                    {flags.map((flag) => (
                        <div key={flag.key} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-3">
                                    <button
                                        className={`w-12 h-6 rounded-full transition-colors ${
                                            flag.enabled
                                                ? "bg-green-500"
                                                : "bg-gray-300"
                                        } relative`}
                                        onClick={() => toggleFlag(flag)}
                                    >
                                        <div
                                            className={`absolute w-5 h-5 bg-white rounded-full shadow transition-transform ${
                                                flag.enabled
                                                    ? "translate-x-6"
                                                    : "translate-x-0.5"
                                            } top-0.5`}
                                        />
                                    </button>
                                    <div>
                                        <h3 className="font-medium">
                                            {flag.name}
                                        </h3>
                                        <p className="text-sm text-gray-600">
                                            {flag.key}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-4">
                                    {flag.metadata?.risk && (
                                        <span
                                            className={`text-sm font-medium ${getRiskColor(flag.metadata.risk)}`}
                                        >
                                            {flag.metadata.risk.toUpperCase()}{" "}
                                            RISK
                                        </span>
                                    )}
                                    <div className="flex items-center gap-2">
                                        <input
                                            className="w-24"
                                            disabled={!flag.enabled}
                                            max="100"
                                            min="0"
                                            type="range"
                                            value={flag.rollout_percentage}
                                            onChange={(e) =>
                                                updateRollout(
                                                    flag,
                                                    parseInt(e.target.value),
                                                )
                                            }
                                        />
                                        <span className="text-sm w-12 text-right">
                                            {flag.rollout_percentage}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {flag.metadata?.dependencies &&
                                flag.metadata.dependencies.length > 0 && (
                                    <div className="mt-2">
                                        <span className="text-xs text-gray-500">
                                            Dependencies:{" "}
                                        </span>
                                        <span className="text-xs text-blue-600">
                                            {flag.metadata.dependencies.join(
                                                ", ",
                                            )}
                                        </span>
                                    </div>
                                )}
                        </div>
                    ))}
                </div>
            </Card>

            {/* Migration Progress Section */}
            <Card className="p-6">
                <h2 className="text-xl font-semibold mb-4">
                    Migration Progress
                </h2>
                <div className="space-y-3">
                    {progress.map((record) => (
                        <div key={record.id} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    {getStatusIcon(record.status)}
                                    <div>
                                        <h3 className="font-medium">
                                            Phase: {record.phase}
                                        </h3>
                                        <p className="text-sm text-gray-600">
                                            Status: {record.status}
                                            {record.duration_ms &&
                                                ` • Duration: ${record.duration_ms}ms`}
                                        </p>
                                    </div>
                                </div>
                                <div className="text-sm text-gray-500">
                                    {record.started_at
                                        ? new Date(
                                              record.started_at,
                                          ).toLocaleString()
                                        : "Not started"}
                                </div>
                            </div>
                            {record.errors && record.errors.length > 0 && (
                                <div className="mt-2 p-2 bg-red-50 rounded">
                                    <div className="flex items-center gap-2 text-red-700">
                                        <ExclamationTriangleIcon className="w-4 h-4" />
                                        <span className="text-sm">
                                            Errors:{" "}
                                            {record.errors
                                                .map((e) => e.message)
                                                .join(", ")}
                                        </span>
                                    </div>
                                </div>
                            )}
                            {record.metrics &&
                                Object.keys(record.metrics).length > 0 && (
                                    <div className="mt-2 text-sm text-gray-600">
                                        Metrics:{" "}
                                        {JSON.stringify(record.metrics)}
                                    </div>
                                )}
                        </div>
                    ))}
                </div>
            </Card>
        </div>
    );
}
