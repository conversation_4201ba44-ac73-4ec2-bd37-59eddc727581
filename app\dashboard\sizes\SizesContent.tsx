"use client";

import React, {
    useC<PERSON>back,
    useEffect,
    useMemo,
    useState,
    useTransition,
} from "react";
import {
    <PERSON><PERSON>,
    <PERSON>ton,
    <PERSON>dal,
    ModalContent,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableColumn,
    TableHeader,
    TableRow,
    Tooltip,
    useDisclosure,
    SortDescriptor,
    addToast,
} from "@heroui/react";
import {
    ArrowLongLeftIcon,
    PencilSquareIcon,
    PlusIcon,
    TrashIcon,
    TagIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import CubeSpinner from "@/shared/components/ui/CubeSpinner";
import { CardV2 } from "@/shared/components/ui/card-v2";
import { useSizes, useDeleteSize } from "@/features/sizes/hooks";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

import SearchControls from "./search-controls";

// Interfaces mejoradas con tipado más estricto
interface Size {
    id: string;
    code: string;
    createdAt: string;
    updatedAt: string;
    _count?: {
        garments: number;
    };
}

interface Column {
    name: string;
    uid: keyof Size | "actions" | "usage";
    sortable: boolean;
}

// Definición de columnas con mejor tipado
const columns: Column[] = [
    { name: "CÓDIGO", uid: "code", sortable: true },
    { name: "USO", uid: "usage", sortable: false },
    { name: "CREACIÓN", uid: "createdAt", sortable: true },
    { name: "ACTUALIZACIÓN", uid: "updatedAt", sortable: true },
    { name: "ACCIONES", uid: "actions", sortable: false },
];

const SizesContent = () => {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [isPending, startTransition] = useTransition();

    const {
        isOpen: isDeleteModalOpen,
        onOpen: onDeleteModalOpen,
        onClose: onDeleteModalClose,
    } = useDisclosure();

    const {
        isOpen: isDetailModalOpen,
        onOpen: onDetailModalOpen,
        onClose: onDetailModalClose,
    } = useDisclosure();

    const [searchTerm, setSearchTerm] = useState("");
    const [sizeToDelete, setSizeToDelete] = useState<Size | null>(null);
    const [selectedSize, setSelectedSize] = useState<Size | null>(null);
    const [isRefreshing, setIsRefreshing] = useState(false);

    // Usar useEffect para establecer el sortDescriptor después del montaje
    // para evitar errores de hidratación
    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
        column: "code",
        direction: "ascending",
    });

    // Establecer el valor inicial después del montaje para evitar errores de hidratación
    useEffect(() => {
        setSortDescriptor({
            column: "code",
            direction: "ascending",
        });
    }, []);

    const [currentPage, setCurrentPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    // Uso de SWR para obtener tallas con opciones optimizadas
    const { sizes, pagination, isLoading, mutate } = useSizes({
        search: searchTerm,
        page: currentPage,
        perPage: rowsPerPage,
        orderBy: sortDescriptor.column?.toString() || "code",
        order: sortDescriptor.direction === "ascending" ? "asc" : "desc",
    });

    // Escuchar eventos de revalidación de tallas
    const { isRevalidating } = useRevalidationListener("sizes");

    // Efecto para revalidar cuando cambia isRevalidating
    useEffect(() => {
        if (isRevalidating) {
            // Cuando se recibe un evento de revalidación, actualizar la tabla
            // if (process.env.NODE_ENV !== "production") {
            //     console.log("⚡ Revalidando tabla de tallas desde evento");
            // }
            mutate();
        }
    }, [isRevalidating, mutate]);

    // Hook para eliminar tallas
    const { deleteSize } = useDeleteSize();

    // Añadir cálculo de estadísticas basado en los datos de sizes
    const statistics = useMemo(() => {
        const totalSizes = sizes.length;
        // Añadir aserción de tipo para el array antes del filtrado
        const sizesWithType = sizes as Size[];
        const usedSizes = sizesWithType.filter(
            (size) => (size._count?.garments || 0) > 0,
        ).length;
        const unusedSizes = totalSizes - usedSizes;

        const usedPercentage = totalSizes
            ? Math.round((usedSizes / totalSizes) * 100)
            : 0;
        const unusedPercentage = totalSizes
            ? Math.round((unusedSizes / totalSizes) * 100)
            : 0;

        return {
            totalSizes,
            usedSizes,
            unusedSizes,
            usedPercentage,
            unusedPercentage,
        };
    }, [sizes]);

    useEffect(() => {
        const params = {
            search: searchParams.get("search"),
            page: searchParams.get("page"),
            perPage: searchParams.get("perPage"),
            sort: searchParams.get("sort"),
            order: searchParams.get("order"),
        };

        if (params.search) setSearchTerm(params.search);
        if (params.page) setCurrentPage(parseInt(params.page));
        if (params.perPage) setRowsPerPage(parseInt(params.perPage));
        if (params.sort) {
            setSortDescriptor({
                column: params.sort,
                direction:
                    params.order === "descending" ? "descending" : "ascending",
            });
        }
    }, [searchParams]);

    useEffect(() => {
        startTransition(() => {
            const params = new URLSearchParams();

            if (searchTerm) params.set("search", searchTerm);
            if (currentPage > 1) params.set("page", currentPage.toString());
            if (rowsPerPage !== 10)
                params.set("perPage", rowsPerPage.toString());
            if (sortDescriptor.column) {
                params.set("sort", sortDescriptor.column.toString());
                params.set("order", sortDescriptor.direction);
            }

            router.replace(`${pathname}?${params.toString()}`, {
                scroll: false,
            });
        });
    }, [
        searchTerm,
        currentPage,
        rowsPerPage,
        sortDescriptor,
        pathname,
        router,
        startTransition,
    ]);

    // Función para actualizar/recargar datos
    const refreshData = useCallback(async () => {
        setIsRefreshing(true);

        try {
            // console.log("Actualizando datos de tallas...");

            // Invalidar caché y obtener datos frescos
            await mutate(undefined, {
                revalidate: true, // Forzar revalidación aunque la caché no sea vieja
                populateCache: true, // Actualizar la caché con los nuevos datos
                rollbackOnError: true, // Volver a datos anteriores en caso de error
            });

            // Pequeño retraso para una mejor experiencia visual
            setTimeout(() => {
                addToast({
                    title: "Datos actualizados",
                    description: `Se han cargado ${sizes.length} tallas correctamente`,
                    color: "success",
                });
                setIsRefreshing(false);
            }, 500);
        } catch (error) {
            // console.error("Error al actualizar datos:", error);

            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error al actualizar los datos",
                color: "danger",
            });

            setIsRefreshing(false);
        }
    }, [mutate, sizes.length]);

    // Filtrado de elementos ya viene del servidor, solo aplicamos ordenamiento local si es necesario
    const filteredItems = useMemo(() => sizes, [sizes]);

    // Optimizado: cálculos de paginación usando datos del servidor
    const { paginatedItems, pages, startIndex, endIndex } = useMemo(() => {
        const totalPages =
            pagination?.lastPage ||
            Math.ceil(filteredItems.length / rowsPerPage) ||
            1;
        const start = pagination
            ? (pagination.currentPage - 1) * rowsPerPage
            : 0;
        const end = pagination
            ? Math.min(start + rowsPerPage, pagination.total)
            : filteredItems.length;

        return {
            paginatedItems: filteredItems,
            pages: totalPages,
            startIndex: start,
            endIndex: end,
        };
    }, [filteredItems, rowsPerPage, pagination]);

    // Handlers de eventos optimizados
    const handleSearch = useCallback((value: string) => {
        setSearchTerm(value);
        setCurrentPage(1); // Resetear a la primera página al buscar
    }, []);

    const handleOpenDetailModal = useCallback(
        (size: Size) => {
            setSelectedSize(size);
            onDetailModalOpen();
        },
        [onDetailModalOpen],
    );

    // Función para eliminar una talla
    const handleDeleteSize = useCallback(async () => {
        if (!sizeToDelete) return;

        try {
            const result = await deleteSize(sizeToDelete.id);

            if (!result || !result.success) {
                throw new Error(result?.error || "Error al eliminar la talla");
            }

            onDeleteModalClose();

            addToast({
                title: "Talla eliminada",
                description: `La talla ${sizeToDelete.code} ha sido eliminada correctamente`,
                color: "success",
            });

            // Forzar actualización de la UI después de eliminar
            await mutate();

            // Si estábamos en una página y ahora no hay suficientes elementos, retroceder una página
            if (sizes.length === 1 && currentPage > 1) {
                setCurrentPage((prev) => prev - 1);
            }
        } catch (error) {
            addToast({
                title: "Error eliminando talla",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error desconocido",
                color: "danger",
            });
        } finally {
            setSizeToDelete(null);
        }
    }, [
        sizeToDelete,
        onDeleteModalClose,
        deleteSize,
        mutate,
        sizes.length,
        currentPage,
    ]);

    const handleOpenDeleteModal = useCallback(
        (size: Size) => {
            setSizeToDelete(size);
            onDeleteModalOpen();
        },
        [onDeleteModalOpen],
    );

    // Atajo de teclado para búsqueda rápida
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // Ctrl/Cmd + K para enfocar la búsqueda
            if ((e.ctrlKey || e.metaKey) && e.key === "k") {
                e.preventDefault();
                document.getElementById("searchInput")?.focus();
            }

            // Tecla N para nueva talla
            if (
                e.key === "n" &&
                !(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)
            ) {
                e.preventDefault();
                router.push("/dashboard/sizes/new");
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [router]);

    // Render cell
    const renderCell = useCallback(
        (size: Size, columnKey: keyof Size | "actions" | "usage") => {
            switch (columnKey) {
                case "code":
                    return (
                        <div className="flex items-center justify-center gap-2">
                            <Button
                                className="font-mono font-medium bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-md text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50 transition-colors min-w-0"
                                variant="light"
                                onPress={() => handleOpenDetailModal(size)}
                            >
                                {size.code}
                            </Button>
                        </div>
                    );

                case "usage":
                    const count = size._count?.garments || 0;

                    return (
                        <div className="flex justify-center">
                            {count > 0 ? (
                                <Badge
                                    className="px-2 py-1"
                                    color="success"
                                    variant="flat"
                                >
                                    {count} {count === 1 ? "prenda" : "prendas"}
                                </Badge>
                            ) : (
                                <Badge
                                    className="px-2 py-1 inline-flex items-center justify-center"
                                    color="warning"
                                    variant="flat"
                                >
                                    <span>Sin uso</span>
                                </Badge>
                            )}
                        </div>
                    );

                case "createdAt":
                case "updatedAt":
                    return (
                        <div className="flex flex-col items-center justify-center">
                            <span className="text-sm text-gray-600 dark:text-gray-300">
                                {format(
                                    new Date(size[columnKey]),
                                    "dd MMM yyyy",
                                    { locale: es },
                                )}
                            </span>
                            <span className="text-xs text-[var(--text-secondary)]">
                                {formatDistanceToNow(
                                    new Date(size[columnKey]),
                                    {
                                        addSuffix: true,
                                        locale: es,
                                    },
                                )}
                            </span>
                        </div>
                    );

                case "actions":
                    const isUsed = (size._count?.garments || 0) > 0;

                    return (
                        <div className="flex gap-2 justify-center">
                            <Tooltip
                                color="primary"
                                content="Ver detalles"
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="relative overflow-hidden bg-blue-50/80 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-blue-200/50 dark:border-blue-800/30 group"
                                    href={`/dashboard/sizes/${size.id}/details`}
                                    size="sm"
                                    variant="flat"
                                >
                                    <span className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                    <InformationCircleIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color="primary"
                                content="Editar"
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="relative overflow-hidden bg-indigo-50/80 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-indigo-200/50 dark:border-indigo-800/30 group"
                                    href={`/dashboard/sizes/${size.id}/edit`}
                                    size="sm"
                                    variant="flat"
                                >
                                    <span className="absolute inset-0 bg-gradient-to-r from-indigo-400/0 via-indigo-400/20 to-indigo-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                    <PencilSquareIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color={isUsed ? "warning" : "danger"}
                                content={
                                    isUsed
                                        ? "No se puede eliminar (en uso)"
                                        : "Eliminar"
                                }
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    className={`relative overflow-hidden ${isUsed ? "bg-amber-50/80 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 border-amber-200/50 dark:border-amber-800/30" : "bg-red-50/80 dark:bg-red-900/30 text-red-600 dark:text-red-400 border-red-200/50 dark:border-red-800/30"} hover:scale-105 hover:shadow-md transition-all duration-200 border group`}
                                    isDisabled={isUsed}
                                    size="sm"
                                    variant="flat"
                                    onPress={() =>
                                        !isUsed && handleOpenDeleteModal(size)
                                    }
                                >
                                    <span
                                        className={`absolute inset-0 bg-gradient-to-r ${isUsed ? "from-amber-400/0 via-amber-400/20 to-amber-400/0" : "from-red-400/0 via-red-400/20 to-red-400/0"} opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300`}
                                    />
                                    <TrashIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>
                        </div>
                    );

                default:
                    return size[columnKey]?.toString() || "-";
            }
        },
        [handleOpenDeleteModal, handleOpenDetailModal],
    );

    return (
        <div className="max-w-7xl mx-auto p-4 sm:p-6">
            {/* Breadcrumbs mejorados */}
            <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex flex-col gap-2">
                    <nav className="flex items-center space-x-1 text-sm">
                        <Link
                            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                            href="/dashboard"
                        >
                            Dashboard
                        </Link>
                        <span className="text-gray-400 dark:text-gray-600">
                            /
                        </span>
                        <span className="text-gray-900 dark:text-white font-medium">
                            Tallas
                        </span>
                    </nav>
                    <h1 className="sr-only">Gestión de Tallas</h1>
                </div>

                <div className="flex items-center gap-2">
                    <Button
                        aria-label="Volver al dashboard"
                        as={Link}
                        className="text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                        href="/dashboard"
                        size="sm"
                        startContent={<ArrowLongLeftIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Volver
                    </Button>
                </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-sm dark:shadow-gray-800/30 border border-gray-100 dark:border-gray-800">
                {/* Header */}
                <div className="p-6 border-b border-gray-100 dark:border-gray-800">
                    <div className="flex flex-col md:flex-row justify-between gap-6">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold flex items-center gap-3 text-gray-900 dark:text-white">
                                <div className="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                                    <TagIcon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                                </div>
                                Gestión de Tallas
                            </h1>
                            <p className="text-gray-500 dark:text-gray-400">
                                Administra las tallas disponibles en el sistema
                            </p>
                        </div>

                        <Button
                            as={Link}
                            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:opacity-90 shadow-lg text-white dark:text-white"
                            color="primary"
                            href="/dashboard/sizes/new"
                            startContent={<PlusIcon className="w-4 h-4" />}
                        >
                            Nueva Talla
                        </Button>
                    </div>
                </div>

                {/* Dashboard Cards - Mejorado con CardV2 y espaciado consistente */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6">
                    <CardV2
                        className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-100 dark:border-blue-800/30"
                        spacing="normal"
                    >
                        <div className="flex justify-between items-start">
                            <div className="flex flex-col">
                                <span className="text-sm text-[var(--text-secondary)]">
                                    Total de Tallas
                                </span>
                                <span className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                                    {statistics.totalSizes}
                                </span>
                            </div>
                            <div className="bg-blue-100 dark:bg-blue-800/50 p-3 rounded-lg">
                                <TagIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                    </CardV2>

                    <CardV2
                        className="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 border border-green-100 dark:border-green-800/30"
                        spacing="normal"
                    >
                        <div className="flex justify-between items-start">
                            <div className="flex flex-col">
                                <span className="text-sm text-[var(--text-secondary)]">
                                    Tallas en Uso
                                </span>
                                <span className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                                    {statistics.usedSizes}
                                </span>
                            </div>
                            <div className="bg-green-100 dark:bg-green-800/50 p-3 rounded-lg flex items-center justify-center">
                                <span className="text-sm font-semibold text-green-700 dark:text-green-300">
                                    {statistics.usedPercentage}%
                                </span>
                            </div>
                        </div>
                    </CardV2>

                    <CardV2
                        className="bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 border border-amber-100 dark:border-amber-800/30"
                        spacing="normal"
                    >
                        <div className="flex justify-between items-start">
                            <div className="flex flex-col">
                                <span className="text-sm text-[var(--text-secondary)]">
                                    Tallas sin Uso
                                </span>
                                <span className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                                    {statistics.unusedSizes}
                                </span>
                            </div>
                            <div className="bg-amber-100 dark:bg-amber-800/50 p-3 rounded-lg flex items-center justify-center">
                                <span className="text-sm font-semibold text-amber-700 dark:text-amber-300">
                                    {statistics.unusedPercentage}%
                                </span>
                            </div>
                        </div>
                    </CardV2>
                </div>

                {/* Controls */}
                <SearchControls
                    handleRefresh={refreshData}
                    handleSearch={handleSearch}
                    isRefreshing={isRefreshing}
                    rowsPerPage={rowsPerPage}
                    searchTerm={searchTerm}
                    setCurrentPage={setCurrentPage}
                    setRowsPerPage={setRowsPerPage}
                    setSearchTerm={setSearchTerm}
                />

                {/* Table - with loading overlay */}
                <div className="relative">
                    {isLoading && !isRefreshing && (
                        <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 z-10 flex items-center justify-center">
                            <CubeSpinner text="Cargando tallas..." />
                        </div>
                    )}

                    <Table
                        aria-label="Tabla de tallas"
                        classNames={{
                            wrapper: "px-6 py-4 rounded-xl shadow-md",
                            base: "border-0 overflow-hidden",
                            th: "bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-700 py-3.5 first:rounded-tl-lg last:rounded-tr-lg",
                            td: "py-4 px-2 group-hover:bg-blue-50/40 dark:group-hover:bg-blue-900/20 text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-800",
                            tr: "transition-all duration-200 hover:bg-blue-50/30 dark:hover:bg-blue-900/10 hover:shadow-sm table-row-hover",
                        }}
                    >
                        <TableHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-t-lg">
                            {columns.map((column) => (
                                <TableColumn
                                    key={column.uid}
                                    allowsSorting={false}
                                    className="hover:bg-blue-100/50 dark:hover:bg-blue-900/40 transition-all duration-200 py-3 group"
                                >
                                    {column.name}
                                </TableColumn>
                            ))}
                        </TableHeader>

                        <TableBody
                            emptyContent={
                                isLoading ? (
                                    <div className="h-[200px] flex items-center justify-center">
                                        <CubeSpinner text="Cargando tallas..." />
                                    </div>
                                ) : (
                                    <div className="py-12 flex flex-col items-center justify-center text-center">
                                        <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-3 mb-4">
                                            <TagIcon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                                        </div>
                                        <p className="text-gray-500 dark:text-gray-400 mb-2">
                                            No se encontraron tallas
                                        </p>
                                        <Button
                                            as={Link}
                                            color="primary"
                                            href="/dashboard/sizes/new"
                                            size="sm"
                                            startContent={
                                                <PlusIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                        >
                                            Crear nueva talla
                                        </Button>
                                    </div>
                                )
                            }
                            items={paginatedItems}
                            loadingContent={
                                <CubeSpinner text="Actualizando..." />
                            }
                            loadingState={isRefreshing ? "loading" : "idle"}
                        >
                            {(item: Size) => (
                                <TableRow
                                    key={item.id}
                                    className="cursor-default group transition-colors"
                                >
                                    {(columnKey) => (
                                        <TableCell className="text-center">
                                            {renderCell(
                                                item,
                                                columnKey as
                                                    | keyof Size
                                                    | "actions"
                                                    | "usage",
                                            )}
                                        </TableCell>
                                    )}
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination with status */}
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 border-t border-gray-100 dark:border-gray-800 bg-gradient-to-r from-gray-50 to-blue-50/30 dark:from-gray-800/50 dark:to-blue-900/10 rounded-b-xl">
                    <div className="flex items-center gap-2">
                        <div className="bg-blue-100/70 dark:bg-blue-900/40 rounded-full p-1.5 shadow-sm">
                            <TagIcon className="h-3.5 w-3.5 text-primary/90" />
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                            Mostrando{" "}
                            <span className="font-semibold text-primary">
                                {filteredItems.length > 0 ? startIndex + 1 : 0}-
                                {Math.min(
                                    endIndex,
                                    pagination?.total || filteredItems.length,
                                )}
                            </span>{" "}
                            de{" "}
                            <span className="font-semibold text-primary">
                                {pagination?.total || filteredItems.length}
                            </span>{" "}
                            resultados
                        </span>
                        {isPending && (
                            <div className="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 border-t-primary rounded-full animate-spin" />
                        )}
                    </div>

                    <Pagination
                        isCompact
                        showControls
                        classNames={{
                            base: "gap-0 overflow-visible",
                            item: "bg-transparent text-gray-600 dark:text-gray-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors",
                            cursor: "bg-gradient-to-r from-blue-600 to-indigo-600 text-white dark:text-white shadow-md hover:shadow-lg transition-shadow",
                            prev: "bg-gradient-to-r from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 text-gray-600 dark:text-gray-300 hover:opacity-90 shadow-sm hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700",
                            next: "bg-gradient-to-r from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 text-gray-600 dark:text-gray-300 hover:opacity-90 shadow-sm hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700",
                        }}
                        page={currentPage}
                        total={pages}
                        onChange={setCurrentPage}
                    />
                </div>
            </div>

            {/* Delete Modal - Mejorado con efectos visuales y mejor UX */}
            <Modal
                classNames={{
                    backdrop:
                        "bg-gradient-to-tr from-red-900/20 via-red-900/10 to-black/20 backdrop-blur-sm",
                }}
                isOpen={isDeleteModalOpen}
                motionProps={{
                    variants: {
                        enter: {
                            opacity: 1,
                            scale: 1,
                            transition: {
                                duration: 0.3,
                                ease: [0.22, 1, 0.36, 1],
                            },
                        },
                        exit: {
                            opacity: 0,
                            scale: 0.95,
                            transition: {
                                duration: 0.2,
                                ease: [0.22, 1, 0.36, 1],
                            },
                        },
                    },
                }}
                onClose={onDeleteModalClose}
            >
                <ModalContent className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 overflow-hidden border border-red-200/50 dark:border-red-900/30 shadow-xl">
                    {/* Header con gradiente */}
                    <div className="bg-gradient-to-r from-red-50 to-pink-50/50 dark:from-red-900/20 dark:to-pink-900/20 p-5 border-b border-red-100/50 dark:border-red-900/30">
                        <div className="flex items-center gap-3">
                            <div className="p-2.5 bg-gradient-to-br from-red-100/80 to-pink-100/80 dark:from-red-900/30 dark:to-pink-900/30 rounded-xl shadow-md animate-pulse">
                                <TrashIcon className="w-6 h-6 text-red-600/90 dark:text-red-500/90" />
                            </div>
                            <div>
                                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                    <span>Eliminar talla</span>
                                    <span className="text-red-600 dark:text-red-400">
                                        {sizeToDelete?.code}
                                    </span>
                                </h3>
                                <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                                    Esta acción no se puede deshacer
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Contenido principal */}
                    <div className="p-6">
                        <div className="bg-gradient-to-br from-red-50/50 to-pink-50/30 dark:from-red-900/10 dark:to-pink-900/10 p-4 rounded-xl border border-red-100/50 dark:border-red-900/30 shadow-sm">
                            <div className="flex items-start gap-3">
                                <div className="bg-red-100/70 dark:bg-red-900/40 rounded-full p-1.5 shadow-sm mt-0.5">
                                    <svg
                                        className="h-4 w-4 text-red-600/90"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                        />
                                    </svg>
                                </div>
                                <div>
                                    <p className="text-gray-700 dark:text-gray-300">
                                        Estás a punto de eliminar
                                        permanentemente la talla{" "}
                                        <span className="font-semibold text-red-600 dark:text-red-400">
                                            {sizeToDelete?.code}
                                        </span>{" "}
                                        del sistema.
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                        Esta acción eliminará todos los datos
                                        asociados a esta talla y no podrá ser
                                        recuperada.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Footer con acciones */}
                    <div className="bg-gradient-to-r from-gray-50 to-red-50/30 dark:from-gray-800/50 dark:to-red-900/10 p-4 border-t border-red-100/50 dark:border-red-900/30 flex justify-end gap-3">
                        <Button
                            className="relative overflow-hidden bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-300 hover:scale-105 hover:shadow-md transition-all duration-200 border border-gray-200/70 dark:border-gray-700/50 group"
                            startContent={
                                <svg
                                    className="h-4 w-4 text-gray-500 dark:text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        d="M6 18L18 6M6 6l12 12"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                    />
                                </svg>
                            }
                            variant="flat"
                            onPress={onDeleteModalClose}
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-gray-200/0 via-gray-200/30 to-gray-200/0 dark:from-gray-700/0 dark:via-gray-700/30 dark:to-gray-700/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10">Cancelar</span>
                        </Button>
                        <Button
                            className="relative overflow-hidden bg-gradient-to-r from-red-600 to-pink-600 text-white hover:scale-105 hover:shadow-md transition-all duration-200 group"
                            color="danger"
                            startContent={<TrashIcon className="h-4 w-4" />}
                            onPress={handleDeleteSize}
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10">
                                Confirmar Eliminación
                            </span>
                        </Button>
                    </div>
                </ModalContent>
            </Modal>

            {/* Detail Modal - Enhanced with copy button and better layout */}
            {selectedSize && (
                <Modal
                    isOpen={isDetailModalOpen}
                    size="lg"
                    onClose={onDetailModalClose}
                >
                    <ModalContent className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 overflow-hidden">
                        {/* Header con gradiente */}
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 border-b border-blue-100/50 dark:border-blue-900/30">
                            <div className="flex items-center gap-3">
                                <div className="p-2.5 bg-gradient-to-br from-blue-100/80 to-indigo-100/80 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-xl shadow-md">
                                    <TagIcon className="w-6 h-6 text-primary/90" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                        <span>Talla:</span>
                                        <span className="text-primary">
                                            {selectedSize.code}
                                        </span>
                                    </h3>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                                        Información detallada de la talla
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Contenido principal */}
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* ID con efecto de copiado mejorado */}
                                <div className="bg-gradient-to-br from-gray-50/80 to-blue-50/30 dark:from-gray-800/80 dark:to-blue-900/10 p-4 rounded-xl border border-blue-100/50 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center gap-1.5">
                                        <span className="bg-blue-100/70 dark:bg-blue-900/40 rounded-full p-1 shadow-sm">
                                            <svg
                                                className="h-3 w-3 text-primary/90"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                />
                                            </svg>
                                        </span>
                                        Identificador único
                                    </p>
                                    <div className="flex items-center gap-2 mt-1">
                                        <code className="font-mono text-xs bg-white/70 dark:bg-gray-900/70 px-2 py-1 rounded text-gray-800 dark:text-gray-200 truncate flex-1 border border-gray-100 dark:border-gray-800">
                                            {selectedSize.id}
                                        </code>
                                        <Tooltip
                                            color="primary"
                                            content="Copiar ID"
                                        >
                                            <Button
                                                isIconOnly
                                                aria-label="Copiar ID"
                                                className="relative overflow-hidden bg-blue-50/80 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-blue-200/50 dark:border-blue-800/30 group"
                                                size="sm"
                                                variant="flat"
                                                onPress={() => {
                                                    navigator.clipboard.writeText(
                                                        selectedSize.id,
                                                    );
                                                    addToast({
                                                        title: "ID copiado al portapapeles",
                                                        description:
                                                            "El identificador ha sido copiado correctamente",
                                                        color: "success",
                                                    });
                                                }}
                                            >
                                                <span className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                                <svg
                                                    className="h-3.5 w-3.5 relative z-10"
                                                    fill="currentColor"
                                                    viewBox="0 0 20 20"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                                                    <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                                                </svg>
                                            </Button>
                                        </Tooltip>
                                    </div>
                                </div>

                                {/* Código de talla */}
                                <div className="bg-gradient-to-br from-gray-50/80 to-indigo-50/30 dark:from-gray-800/80 dark:to-indigo-900/10 p-4 rounded-xl border border-indigo-100/50 dark:border-indigo-900/30 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center gap-1.5">
                                        <span className="bg-indigo-100/70 dark:bg-indigo-900/40 rounded-full p-1 shadow-sm">
                                            <TagIcon className="h-3 w-3 text-indigo-600/90" />
                                        </span>
                                        Código de talla
                                    </p>
                                    <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400 mt-2">
                                        {selectedSize.code}
                                    </p>
                                </div>

                                {/* Fecha de creación */}
                                <div className="bg-gradient-to-br from-gray-50/80 to-green-50/30 dark:from-gray-800/80 dark:to-green-900/10 p-4 rounded-xl border border-green-100/50 dark:border-green-900/30 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center gap-1.5">
                                        <span className="bg-green-100/70 dark:bg-green-900/40 rounded-full p-1 shadow-sm">
                                            <svg
                                                className="h-3 w-3 text-green-600/90"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                />
                                            </svg>
                                        </span>
                                        Fecha de creación
                                    </p>
                                    <div className="mt-2">
                                        <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                                            {format(
                                                new Date(
                                                    selectedSize.createdAt,
                                                ),
                                                "PPP",
                                                { locale: es },
                                            )}
                                        </p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                            {formatDistanceToNow(
                                                new Date(
                                                    selectedSize.createdAt,
                                                ),
                                                { addSuffix: true, locale: es },
                                            )}
                                        </p>
                                    </div>
                                </div>

                                {/* Fecha de actualización */}
                                <div className="bg-gradient-to-br from-gray-50/80 to-amber-50/30 dark:from-gray-800/80 dark:to-amber-900/10 p-4 rounded-xl border border-amber-100/50 dark:border-amber-900/30 shadow-sm hover:shadow-md transition-shadow duration-300">
                                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center gap-1.5">
                                        <span className="bg-amber-100/70 dark:bg-amber-900/40 rounded-full p-1 shadow-sm">
                                            <svg
                                                className="h-3 w-3 text-amber-600/90"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                />
                                            </svg>
                                        </span>
                                        Última actualización
                                    </p>
                                    <div className="mt-2">
                                        <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                                            {format(
                                                new Date(
                                                    selectedSize.updatedAt,
                                                ),
                                                "PPP",
                                                { locale: es },
                                            )}
                                        </p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                            {formatDistanceToNow(
                                                new Date(
                                                    selectedSize.updatedAt,
                                                ),
                                                { addSuffix: true, locale: es },
                                            )}
                                        </p>
                                    </div>
                                </div>

                                {/* Prendas asociadas */}
                                <div className="bg-gradient-to-br from-gray-50/80 to-purple-50/30 dark:from-gray-800/80 dark:to-purple-900/10 p-4 rounded-xl border border-purple-100/50 dark:border-purple-900/30 shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2">
                                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center gap-1.5">
                                        <span className="bg-purple-100/70 dark:bg-purple-900/40 rounded-full p-1 shadow-sm">
                                            <svg
                                                className="h-3 w-3 text-purple-600/90"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                />
                                            </svg>
                                        </span>
                                        Prendas asociadas
                                    </p>
                                    <div className="mt-3">
                                        {(selectedSize._count?.garments || 0) >
                                        0 ? (
                                            <div className="flex items-center gap-3">
                                                <Badge
                                                    className="bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-400 border border-green-200/50 dark:border-green-800/30 shadow-sm px-3 py-1.5"
                                                    size="lg"
                                                    variant="flat"
                                                >
                                                    <div className="flex items-center gap-1.5">
                                                        <svg
                                                            className="h-4 w-4"
                                                            fill="none"
                                                            stroke="currentColor"
                                                            viewBox="0 0 24 24"
                                                        >
                                                            <path
                                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth={2}
                                                            />
                                                        </svg>
                                                        <span className="font-medium">
                                                            {selectedSize._count
                                                                ?.garments || 0}
                                                        </span>
                                                        <span>
                                                            prendas usan esta
                                                            talla
                                                        </span>
                                                    </div>
                                                </Badge>
                                                <Button
                                                    className="relative overflow-hidden bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-600 dark:text-green-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-green-200/50 dark:border-green-800/30 group"
                                                    size="sm"
                                                    startContent={
                                                        <svg
                                                            className="h-4 w-4"
                                                            fill="none"
                                                            stroke="currentColor"
                                                            viewBox="0 0 24 24"
                                                        >
                                                            <path
                                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth={2}
                                                            />
                                                            <path
                                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth={2}
                                                            />
                                                        </svg>
                                                    }
                                                    variant="flat"
                                                    onPress={() => {
                                                        addToast({
                                                            title: "Función en desarrollo",
                                                            description:
                                                                "Próximamente podrás ver las prendas asociadas a esta talla",
                                                            color: "warning",
                                                        });
                                                    }}
                                                >
                                                    <span className="absolute inset-0 bg-gradient-to-r from-green-400/0 via-green-400/20 to-green-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                                    <span className="relative z-10">
                                                        Ver prendas asociadas
                                                    </span>
                                                </Button>
                                            </div>
                                        ) : (
                                            <Badge
                                                className="bg-gradient-to-r from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30 text-amber-700 dark:text-amber-400 border border-amber-200/50 dark:border-amber-800/30 shadow-sm px-3 py-1.5"
                                                size="lg"
                                                variant="flat"
                                            >
                                                <div className="flex items-center gap-1.5">
                                                    <svg
                                                        className="h-4 w-4"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                    >
                                                        <path
                                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            strokeWidth={2}
                                                        />
                                                    </svg>
                                                    <span className="font-medium">
                                                        Sin prendas asociadas
                                                    </span>
                                                </div>
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Footer con acciones */}
                        <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 dark:from-gray-800/50 dark:to-blue-900/10 p-4 border-t border-blue-100/50 dark:border-blue-900/30 flex justify-end gap-3">
                            <Button
                                className="relative overflow-hidden bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-300 hover:scale-105 hover:shadow-md transition-all duration-200 border border-gray-200/70 dark:border-gray-700/50 group"
                                startContent={
                                    <svg
                                        className="h-4 w-4 text-gray-500 dark:text-gray-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            d="M6 18L18 6M6 6l12 12"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                        />
                                    </svg>
                                }
                                variant="flat"
                                onPress={onDetailModalClose}
                            >
                                <span className="absolute inset-0 bg-gradient-to-r from-gray-200/0 via-gray-200/30 to-gray-200/0 dark:from-gray-700/0 dark:via-gray-700/30 dark:to-gray-700/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                <span className="relative z-10">Cerrar</span>
                            </Button>
                            <Button
                                as={Link}
                                className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:scale-105 hover:shadow-md transition-all duration-200 group"
                                color="primary"
                                href={`/dashboard/sizes/${selectedSize.id}`}
                                startContent={
                                    <PencilSquareIcon className="h-4 w-4" />
                                }
                                onPress={onDetailModalClose}
                            >
                                <span className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                <span className="relative z-10">
                                    Editar Talla
                                </span>
                            </Button>
                        </div>
                    </ModalContent>
                </Modal>
            )}

            {/* Mostrar spinner de carga condicional para revalidación */}
            {(isLoading || isRevalidating) && (
                <div className="absolute top-4 right-4">
                    <CubeSpinner
                        className="text-primary"
                        text="Actualizando..."
                    />
                </div>
            )}
        </div>
    );
};

export default SizesContent;
