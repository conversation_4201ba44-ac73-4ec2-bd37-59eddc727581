// Exportación de todos los componentes de selección por lotes
export { SelectionProvider, useSelection } from "./SelectionContext";
export { default as SelectionCheckbox } from "./SelectionCheckbox";
export { default as SmartSelectionToolbar } from "./SmartSelectionToolbar";
export { default as BatchActionBar } from "./BatchActionBar";

// Tipo para las props del sistema de selección por lotes
export interface BatchSelectionSystemProps {
    statuses: {
        id: string;
        name: string;
        color: string;
        iconName?: string;
    }[];
    customers: {
        id: string;
        name: string;
    }[];
    onUpdateStatus: (orderIds: string[], statusId: string) => Promise<void>;
    onAssignContractor: (orderIds: string[]) => Promise<void>;
    onDelete: (orderIds: string[]) => Promise<void>;
    onExport: (orderIds: string[], format: "csv" | "pdf") => Promise<void>;
    onDuplicate: (orderIds: string[]) => Promise<void>;
}
