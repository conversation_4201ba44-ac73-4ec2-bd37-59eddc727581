# Sistema de Diseño y Componentes UI - Proyecto Lohari

## 🎨 Visión General

El proyecto Lohari implementa un sistema de diseño moderno y cohesivo basado en HeroUI (NextUI v2), con un enfoque en consistencia visual, accesibilidad y experiencia de usuario fluida. El sistema está completamente migrado y unificado en todos los módulos.

## 📱 Últimas Actualizaciones (2025)

### Patrón de Tarjetas Mejoradas
- **EnhancedNoteCardV2**: Implementación exitosa para el módulo de notas
- **EnhancedOrderCardV2**: Extensión del patrón para órdenes
- Diseño con información jerárquica y contenido expandible
- Indicadores visuales claros de estado y urgencia
- Animaciones fluidas con Framer Motion

## 🏗️ Arquitectura del Sistema de Diseño

### Design Tokens (`/shared/styles/design-tokens.css`)

#### Colores
```css
/* Sistema de 12 niveles basado en Radix UI */
--color-primary-1 → --color-primary-12    /* Azul principal */
--color-gray-1 → --color-gray-12          /* Escala de grises */
--color-success/warning/error/info-[1-12] /* Colores semánticos */

/* Modo oscuro con inversión automática */
.dark {
  --color-primary-1: hsl(206 100% 10%);   /* Invertido */
  --color-primary-12: hsl(206 98% 95.8%); /* Invertido */
}
```

#### Tipografía
```css
/* Tamaños */
--font-size-xs: 12px;    /* Texto muy pequeño */
--font-size-sm: 14px;    /* Texto pequeño */
--font-size-base: 16px;  /* Texto normal */
--font-size-lg: 18px;    /* Texto grande */
--font-size-xl: 20px;    /* Encabezados pequeños */
--font-size-2xl: 24px;   /* Encabezados medianos */
--font-size-3xl: 30px;   /* Encabezados grandes */
--font-size-4xl: 36px;   /* Títulos principales */

/* Pesos */
--font-weight-light: 300;
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

#### Espaciado (Sistema base 4px)
```css
--space-0: 0;      /* 0px */
--space-1: 4px;    /* XS */
--space-2: 8px;    /* SM */
--space-3: 12px;   /* Base */
--space-4: 16px;   /* MD */
--space-5: 20px;   /* LG */
--space-6: 24px;   /* XL */
--space-8: 32px;   /* 2XL */
--space-10: 40px;  /* 3XL */
--space-12: 48px;  /* 4XL */
--space-16: 64px;  /* 5XL */
--space-20: 80px;  /* 6XL */
```

#### Otros Tokens
```css
/* Border Radius */
--radius-sm: 2px;
--radius-md: 4px;
--radius-lg: 6px;
--radius-xl: 8px;
--radius-2xl: 12px;
--radius-3xl: 16px;
--radius-full: 9999px;

/* Sombras (con variantes dark) */
--shadow-sm → --shadow-2xl

/* Z-Index */
--z-dropdown: 10;
--z-modal: 100;
--z-popover: 200;
--z-tooltip: 300;

/* Animaciones */
--duration-fast: 150ms;
--duration-normal: 250ms;
--duration-slow: 400ms;
--easing-default: cubic-bezier(0.4, 0, 0.2, 1);
```

## 🧩 Componentes del Dashboard

### DashboardLayout
Layout principal que envuelve todas las páginas del dashboard.

```tsx
<DashboardLayout
  header={<DashboardHeader breadcrumbs={breadcrumbs} actions={actions} />}
  stats={showStats && <DashboardStats stats={stats} />}
>
  {children}
</DashboardLayout>
```

**Características:**
- Fondo con gradiente y pattern decorativo
- Soporte para header, stats y acciones
- Animaciones con Framer Motion
- Glassmorphism en cards principales

### DashboardHeader
Header con sistema de breadcrumbs animados.

```tsx
<DashboardHeader
  title="Órdenes"
  breadcrumbs={[
    { label: "Dashboard", href: "/dashboard", icon: "home" },
    { label: "Órdenes", href: "/dashboard/orders", icon: "document" }
  ]}
  actions={<Button>Nueva Orden</Button>}
/>
```

**Características:**
- Breadcrumbs con iconos y animación escalonada
- Título con gradiente de texto
- Área de acciones flexible
- Responsive design

### DashboardStats
Sistema de tarjetas de estadísticas con múltiples variantes.

```tsx
<DashboardStats
  stats={[
    {
      name: "Total Órdenes",
      value: "1,234",
      icon: DocumentIcon,
      change: "+12%",
      trend: "up",
      color: "primary"
    }
  ]}
  columns={4} // 2, 3, o 4
/>
```

**Variantes de color:**
- `primary`: Azul
- `success`: Verde
- `warning`: Amarillo
- `danger`: Rojo

### DashboardTable
Tabla unificada con integración completa de HeroUI.

```tsx
<DashboardTable
  columns={columns}
  data={data}
  onAction={(key, item) => handleAction(key, item)}
  renderCell={(item, columnKey) => renderCell(item, columnKey)}
  actions={[
    { key: "edit", label: "Editar", icon: "edit" },
    { key: "delete", label: "Eliminar", icon: "trash", color: "danger" }
  ]}
/>
```

**Características:**
- Renderizado condicional de celdas
- Sistema de acciones con dropdown
- Manejo de estados loading/empty
- Hidratación segura

### DashboardFilters
Sistema completo de filtros, búsqueda y ordenamiento.

```tsx
<DashboardFilters
  onSearch={setSearchTerm}
  onFilter={setFilters}
  onSort={setSorting}
  onClearFilters={clearFilters}
  filters={[
    {
      key: "status",
      label: "Estado",
      type: "select",
      options: statusOptions
    }
  ]}
  sortOptions={[
    { key: "name-asc", label: "Nombre (A-Z)" },
    { key: "name-desc", label: "Nombre (Z-A)" }
  ]}
/>
```

## 📋 Plantillas CRUD

### CrudListTemplate
Plantilla completa para páginas de listado CRUD.

```tsx
<CrudListTemplate
  // Datos
  items={items}
  columns={columns}
  
  // Configuración
  title="Gestión de Clientes"
  entityName="cliente"
  createHref="/dashboard/customers/new"
  
  // Estadísticas (opcional)
  stats={stats}
  
  // Acciones
  onEdit={(item) => router.push(`/customers/${item.id}`)}
  onDelete={handleDelete}
  
  // Búsqueda y filtros
  searchPlaceholder="Buscar clientes..."
  filters={filters}
  sortOptions={sortOptions}
  
  // Renderizado personalizado
  renderCell={renderCell}
/>
```

### CrudFormTemplate
Plantilla para formularios y wizards multi-paso.

```tsx
<CrudFormTemplate
  title="Nueva Orden"
  steps={[
    { key: "basic", label: "Información Básica" },
    { key: "items", label: "Productos" },
    { key: "review", label: "Revisión" }
  ]}
  currentStep={currentStep}
  onStepChange={setCurrentStep}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
>
  {renderStepContent(currentStep)}
</CrudFormTemplate>
```

### Componentes de Formulario Unificados (2025)

#### FormHeader
Componente reutilizable para encabezados de formulario con diseño consistente.

```tsx
interface FormHeaderProps {
  title: string;
  description?: string;
  icon?: ReactNode;
}

// Uso
<ColorFormHeader
  title={isEditing ? "Editar Color" : "Nuevo Color"}
  description={isEditing ? "Modifica los datos del color" : "Registra un nuevo color"}
  icon={<Palette className="w-5 h-5" />}
/>
```

#### FormProgress
Indicador visual de progreso para formularios multi-paso.

```tsx
interface FormProgressProps {
  currentStep: number;
  totalSteps: number;
  steps?: Array<{
    label: string;
    completed: boolean;
  }>;
}

// Uso
<ColorFormProgress 
  currentStep={currentStep}
  totalSteps={2}
  steps={[
    { label: "Información básica", completed: true },
    { label: "Configuración", completed: false }
  ]}
/>
```

#### InfoMessage
Mensajes informativos con iconos y estilos consistentes.

```tsx
interface InfoMessageProps {
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
}

// Uso
<ColorInfoMessage
  type="info"
  title="Código hexadecimal"
  message="El código debe incluir el símbolo # seguido de 6 caracteres"
/>
```

### Modales de Confirmación

#### DeleteModal Pattern
Patrón consistente para modales de eliminación con animaciones.

```tsx
interface DeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemName: string;
  isLoading?: boolean;
}

// Implementación
<DeleteColorModal
  isOpen={isDeleteModalOpen}
  onClose={() => setDeleteModalOpen(false)}
  onConfirm={handleDelete}
  itemName={color.name}
  isLoading={isDeleting}
/>

// Estilos consistentes
classNames={{
  base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
  header: "border-b border-gray-200 dark:border-gray-700",
  body: "py-6",
  footer: "border-t border-gray-200 dark:border-gray-700"
}}
```

## 🎭 Sistema de Animaciones

### Framer Motion Variants (`/shared/utils/ui/animations.ts`)

#### Card Variants
```typescript
export const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.3 }
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0 10px 30px rgba(0,0,0,0.1)"
  },
  tap: { scale: 0.98 }
}
```

#### List Variants (Stagger)
```typescript
export const listVariants = {
  container: {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  item: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 }
  }
}
```

#### Configuraciones de Spring
```typescript
export const springConfigs = {
  gentle: { type: "spring", stiffness: 300, damping: 30 },
  wobbly: { type: "spring", stiffness: 180, damping: 12 },
  stiff: { type: "spring", stiffness: 500, damping: 40 },
  slow: { type: "spring", stiffness: 200, damping: 40 }
}
```

## 🌈 Sistema de Colores Dinámico

### Utilidades de Color (`/shared/utils/ui/color-palette.ts`)

#### Generación de Paletas
```typescript
// Generar shades automáticamente
const blueShades = generateColorPalette('#3B82F6')
// Resultado: { 50: '#EFF6FF', 100: '#DBEAFE', ..., 900: '#1E3A8A' }

// Colores complementarios
const complementary = getComplementaryColor('#3B82F6')

// Colores triádicos
const triadic = getTriadicColors('#3B82F6')

// Colores análogos
const analogous = getAnalogousColors('#3B82F6')
```

#### Gradientes Predefinidos
```typescript
export const gradients = {
  primary: 'from-blue-500 to-purple-600',
  success: 'from-green-400 to-emerald-600',
  sunset: 'from-orange-400 to-pink-600',
  ocean: 'from-blue-400 to-teal-600',
  // ... más gradientes
}
```

#### Utilidades de Contraste
```typescript
// Determinar color de texto según fondo
const textColor = getTextColorForBackground('#3B82F6') // returns 'white'

// Calcular ratio de contraste
const ratio = getContrastRatio('#3B82F6', '#FFFFFF') // returns 4.5
```

## 💎 Efectos Glassmorphism

### Estilos Base (`/shared/utils/ui/glass-morphism.ts`)

```typescript
export const glassBase = {
  backdropFilter: 'blur(10px)',
  backgroundColor: 'rgba(255, 255, 255, 0.1)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
}

// Variantes por color
export const glassVariants = {
  primary: {
    ...glassBase,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    border: '1px solid rgba(59, 130, 246, 0.2)'
  },
  // ... más variantes
}
```

### Bordes Animados
```typescript
export const animatedBorder = {
  background: `linear-gradient(45deg, 
    transparent, 
    rgba(59, 130, 246, 0.5), 
    transparent)`,
  animation: 'borderRotate 3s linear infinite'
}
```

## 🧱 Componentes UI Base

### ImprovedButton
```tsx
<ImprovedButton
  variant="primary"  // primary, secondary, success, danger, ghost, outline
  size="md"         // xs, sm, md, lg, xl
  isLoading={false}
  leftIcon={<Icon />}
  rightIcon={<Icon />}
  fullWidth={false}
>
  Click me
</ImprovedButton>
```

### ImprovedCard
```tsx
<ImprovedCard
  variant="stats"     // stats, content, interactive
  isLoading={false}
  isPressable={true}
  className="custom-class"
>
  <CardContent />
</ImprovedCard>
```

## 🎯 Mejores Prácticas

### 1. Uso de Tokens
```tsx
// ✅ Correcto - usar tokens
<div className="p-4 text-gray-12 bg-primary-1">

// ❌ Evitar - valores arbitrarios
<div className="p-[17px] text-[#333] bg-blue-500">
```

### 2. Composición de Componentes
```tsx
// ✅ Correcto - composición
<DashboardLayout>
  <DashboardHeader />
  <DashboardStats />
  <CrudListTemplate />
</DashboardLayout>

// ❌ Evitar - componentes monolíticos
<GiantDashboardComponent />
```

### 3. Animaciones Accesibles
```tsx
// ✅ Correcto - respetar preferencias
<motion.div
  variants={cardVariants}
  className="motion-safe:animate-fadeIn"
>

// ❌ Evitar - forzar animaciones
<div className="animate-spin-always">
```

### 4. Modo Oscuro
```tsx
// ✅ Correcto - usar clases dark:
<div className="bg-gray-1 dark:bg-gray-12">

// ❌ Evitar - condicionales manuales
<div className={isDark ? 'bg-black' : 'bg-white'}>
```

## 📊 Métricas del Sistema

- **Componentes del Dashboard**: 5 principales
- **Plantillas CRUD**: 2 (List y Form)
- **Variantes de animación**: 8+ predefinidas
- **Tokens de diseño**: 200+ variables CSS
- **Módulos migrados**: 9/9 (100%)
- **Soporte modo oscuro**: 100%

## 🚀 Roadmap Futuro

1. **Componentes adicionales**: DatePicker mejorado, FileUpload
2. **Más plantillas**: Dashboard analytics, Reports
3. **Temas personalizados**: Permitir temas de marca
4. **Documentación interactiva**: Storybook
5. **Testing visual**: Pruebas de regresión visual