/**
 * Genera un checksum simple para validación de folios
 */
export function generateChecksum(folioBase: string): string {
    let sum = 0;

    for (let i = 0; i < folioBase.length; i++) {
        sum += folioBase.charCodeAt(i);
    }

    return (sum % 10).toString();
}

/**
 * Genera el número secuencial para el folio
 */
export function generateSequentialNumber(
    date: Date,
    lastSequence: number = 0,
): { dateString: string; sequence: number } {
    // Format date as YYYYMMDD
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const dateString = `${year}${month}${day}`;

    // Increment sequence
    const sequence = lastSequence + 1;

    return { dateString, sequence };
}
