/**
 * Script para actualizar datos de contacto de contratistas
 * Ejecutar con: npx tsx scripts/update-contractor-contact.ts
 */

import { prisma } from "../shared/lib/prisma";

async function updateContractorContact() {
    try {
        console.log("📧 Actualizando información de contacto de contratistas...");

        // Obtener todos los contratistas
        const contractors = await prisma.contractor.findMany();

        if (contractors.length === 0) {
            console.log("❌ No hay contratistas en la base de datos");
            return;
        }

        console.log(`✅ Encontrados ${contractors.length} contratistas`);

        // Actualizar cada contratista con datos de ejemplo
        for (const contractor of contractors) {
            // Generar email y teléfono basados en el nombre
            const nameParts = contractor.name.toLowerCase().split(' ');
            const email = `${nameParts[0]}.${nameParts[nameParts.length - 1]}@ejemplo.com`;
            const phone = `555-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`;

            await prisma.contractor.update({
                where: { id: contractor.id },
                data: {
                    email: email,
                    phone: phone
                }
            });

            console.log(`✅ Actualizado: ${contractor.name}`);
            console.log(`   📧 Email: ${email}`);
            console.log(`   📱 Teléfono: ${phone}`);
        }

        console.log("\n✨ Actualización completada exitosamente!");

    } catch (error) {
        console.error("❌ Error al actualizar contratistas:", error);
    } finally {
        await prisma.$disconnect();
    }
}

// Ejecutar el script
updateContractorContact();