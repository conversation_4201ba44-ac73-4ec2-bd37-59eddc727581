"use server";

import { z } from "zod";
import { Prisma } from "@prisma/client";

import { validateCustomerName } from "@/features/customers/actions/validate";
import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

import { customerSchema } from "./schema";

// Obtener helpers de revalidación para la entidad "customer"
const { revalidateCache } = createServerRevalidation("customer");

/**
 * Crea un nuevo cliente
 * @param data Datos del cliente a crear
 * @param forceCreate Si es true, ignorará la validación de nombre único (para casos especiales de inconsistencia)
 */
export async function createCustomer(
    data: { name: string; displayName?: string | null },
    forceCreate = false,
) {
    console.log(
        `[createCustomer] Iniciando creación de cliente: ${data.name}, forceCreate: ${forceCreate}`,
    );

    try {
        // Validar datos con parse en lugar de safeParse para generar errores
        const validatedData = customerSchema.parse(data);

        console.log(
            `[createCustomer] Datos validados correctamente:`,
            validatedData,
        );

        // Verificar si el nombre ya existe (solo si no se está forzando la creación)
        if (!forceCreate) {
            const nameValidation = await validateCustomerName(
                validatedData.name,
            );

            console.log(
                `[createCustomer] Validación de nombre:`,
                nameValidation,
            );

            if (
                !nameValidation ||
                !nameValidation.data ||
                !(nameValidation.data as any)?.isValid
            ) {
                console.log(
                    `[createCustomer] Nombre no válido, retornando error`,
                );

                return {
                    success: false,
                    error: "El nombre del cliente ya existe en el sistema",
                };
            }
        }

        // Asegurarse de que no hay clientes con este nombre (si se está forzando la creación)
        if (forceCreate) {
            // Intentar eliminar cualquier cliente existente con el mismo nombre
            // para evitar errores de unicidad
            try {
                await db.customer.deleteMany({
                    where: {
                        name: {
                            equals: validatedData.name,
                            mode: Prisma.QueryMode.insensitive,
                        },
                    },
                });
            } catch (_unused) {
                // Ignora errores de esta operación, es solo por seguridad
                // // REMOVED: console.log(
                //     "No se encontraron clientes duplicados para eliminar",
                // );
            }
        }

        // Crear el cliente en la base de datos
        console.log(`[createCustomer] Creando cliente en la base de datos...`);
        const customer = await db.customer.create({
            data: {
                name: validatedData.name,
                displayName: validatedData.displayName || null,
            },
        });

        console.log(`[createCustomer] Cliente creado exitosamente:`, customer);

        // Revalidar caché usando el helper genérico
        revalidateCache(customer.id);
        console.log(
            `[createCustomer] Caché revalidado para ID: ${customer.id}`,
        );

        return { success: true, data: customer };
    } catch (error) {
        console.error(`[createCustomer] Error capturado:`, error);

        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            console.log(
                `[createCustomer] Error de validación Zod:`,
                error.errors,
            );

            return {
                success: false,
                error: error.errors[0]?.message || "Datos de cliente inválidos",
            };
        }

        // Usar el helper para manejar errores de DB
        console.log(`[createCustomer] Manejando error con handleDbError...`);
        const result = handleDbError(() => {
            throw error;
        }, "Error al crear cliente");

        console.log(`[createCustomer] Resultado de handleDbError:`, result);

        return result;
    }
}
