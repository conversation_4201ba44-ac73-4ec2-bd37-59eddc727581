import React from "react";
import { Chip, Tooltip } from "@heroui/react";
import { ClockIcon, MinusCircleIcon } from "@heroicons/react/24/outline";

import DateDisplay from "@/features/orders/components/DateDisplay";
import { DateDisplayType } from "@/shared/utils/dateUtils";

/**
 * Función auxiliar para renderizar la fecha de recibido
 */
export const renderReceivedDate = (dateString: string | undefined | null) => {
    // Si no hay fecha, mostrar mensaje
    if (!dateString) {
        return (
            <Tooltip content="Esta orden aún no tiene una fecha de recepción registrada">
                <div className="flex flex-col items-center gap-1">
                    <Chip
                        className="bg-gray-50 dark:bg-gray-800/50 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700 text-xs shadow-sm hover:shadow transition-all"
                        startContent={
                            <MinusCircleIcon className="w-3 h-3 text-gray-500" />
                        }
                        variant="flat"
                    >
                        Sin registro
                    </Chip>
                    <span className="text-[10px] text-gray-500">Pendiente</span>
                </div>
            </Tooltip>
        );
    }

    // Usar nuestro componente DateDisplay para mostrar la fecha
    return (
        <div className="flex flex-col items-center">
            <DateDisplay
                date={dateString}
                showRelative={true}
                type={DateDisplayType.received}
            />
        </div>
    );
};

/**
 * Función auxiliar para renderizar la fecha de entrega estimada
 */
export const renderEstimatedDeliveryDate = (
    dateString: string | undefined | null,
) => {
    // Si no hay fecha, mostrar mensaje
    if (!dateString) {
        return (
            <Tooltip content="No se ha establecido una fecha de entrega para esta orden">
                <div className="flex flex-col items-center gap-1">
                    <Chip
                        className="bg-gray-50 dark:bg-gray-800/50 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700 text-xs shadow-sm hover:shadow transition-all"
                        startContent={
                            <ClockIcon className="w-3 h-3 text-gray-500" />
                        }
                        variant="flat"
                    >
                        Sin fecha
                    </Chip>
                    <span className="text-[10px] text-gray-500">Pendiente</span>
                </div>
            </Tooltip>
        );
    }

    // Usar nuestro componente DateDisplay para mostrar la fecha
    return (
        <div className="flex flex-col items-center">
            <DateDisplay
                date={dateString}
                showRelative={true}
                type={DateDisplayType.estimated}
            />
        </div>
    );
};
