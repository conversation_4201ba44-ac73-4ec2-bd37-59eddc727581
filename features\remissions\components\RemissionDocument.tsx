"use client";

import type { Assignment } from "@/features/assignments/components/wizard/components/AssignmentTable";

import { forwardRef, ForwardedRef, useEffect } from "react";
import Image from "next/image";

import { formatQuantity } from "@/shared/utils/formatters";
import { useRemission } from "@/features/remissions/hooks/useRemission";
import { ContractorInfo } from "@/features/assignments/components/wizard/components/ContractorInfo";

interface OrderDetails {
    id: string;
    cutOrder?: string;
    creationDate?: Date;
    // Añadir soporte para partes de orden
    parts?: {
        id: string;
        code: string;
    }[];
}

interface RemissionDocumentProps {
    remissionId?: string;
    // Keep old props for backward compatibility
    assignmentId?: string;
    contractorId?: string;
    assignments?: Assignment[];
    order?: OrderDetails;
    notes?: string;
    date?: Date;
    // Nueva opción para orientación y estilo
    orientation?: "portrait" | "landscape";
    // Nueva opción para modo simplificado (para PDF)
    simplified?: boolean;
}

// Componente optimizado para impresión y exportación a PDF
export const RemissionDocument = forwardRef(function RemissionDocument(
    {
        remissionId,
        // Backward compatibility props
        assignmentId,
        contractorId,
        assignments = [],
        order,
        notes,
        date = new Date(),
        // Por defecto usamos landscape para el nuevo diseño
        orientation = "landscape",
        simplified = false,
    }: RemissionDocumentProps,
    ref: ForwardedRef<HTMLDivElement>,
) {
    // If remissionId is provided, fetch from database
    const {
        data: remissionData,
        error,
        isLoading,
    } = useRemission(remissionId || null);

    // Determine if we're using database or legacy props
    const usingDatabase = !!remissionId && !!remissionData;

    // Extract data from remission if available
    const remissionFolio = usingDatabase ? remissionData?.folio : assignmentId;

    // Ensure remissionDate is a valid Date object
    let remissionDate = date;

    if (usingDatabase && remissionData?.createdAt) {
        // Convert string date to Date object if necessary
        remissionDate =
            remissionData.createdAt instanceof Date
                ? remissionData.createdAt
                : new Date(remissionData.createdAt);
    }

    // Validate date to prevent Invalid time value error
    if (!remissionDate || isNaN(remissionDate.getTime())) {
        remissionDate = new Date(); // Fallback to current date
    }

    const remissionNotes = usingDatabase ? remissionData?.notes : notes;
    const remissionContractorId = usingDatabase
        ? remissionData?.contractorId
        : contractorId;

    // For database remissions, extract items from remissionItems
    const remissionAssignments =
        usingDatabase && remissionData?.remissionItems
            ? remissionData.remissionItems.map((item: any) => ({
                  modelCode: item.modelCode,
                  colorName: item.colorName,
                  sizeCode: item.sizeCode,
                  quantity: item.quantity,
              }))
            : assignments.map((assignment: any) => {
                  // Manejar tanto estructura plana como anidada
                  if (assignment.garmentSize) {
                      // Estructura anidada (desde BD)
                      return {
                          modelCode: assignment.garmentSize.garment.model.code,
                          colorName: assignment.garmentSize.garment.color.name,
                          sizeCode: assignment.garmentSize.size.code,
                          quantity: assignment.quantity,
                      };
                  } else {
                      // Estructura plana (desde wizard)
                      return {
                          modelCode: assignment.modelCode || "Sin modelo",
                          colorName: assignment.colorName || "Sin color",
                          sizeCode: assignment.sizeCode || "Sin talla",
                          quantity: assignment.quantity,
                      };
                  }
              });

    // Order details from database or props
    const remissionOrder =
        usingDatabase && remissionData?.orderDetails
            ? (remissionData.orderDetails as OrderDetails)
            : order;

    // Calcular el total de unidades
    const totalQuantity = remissionAssignments.reduce(
        (sum: number, a: any) => sum + a.quantity,
        0,
    );

    // Agrupar asignaciones por modelo y color
    const groupedAssignments = remissionAssignments.reduce(
        (acc: any, assignment: any) => {
            // Asegurarse de que tenemos valores válidos
            const modelCode = assignment.modelCode || "Sin modelo";
            const colorName = assignment.colorName || "Sin color";
            const sizeCode = assignment.sizeCode || "Sin talla";

            const key = `${modelCode}-${colorName}`;

            if (!acc[key]) {
                acc[key] = {
                    modelCode: modelCode,
                    colorName: colorName,
                    sizes: {},
                };
            }

            // Agregar o sumar la cantidad para cada talla
            if (acc[key].sizes[sizeCode]) {
                acc[key].sizes[sizeCode] += assignment.quantity;
            } else {
                acc[key].sizes[sizeCode] = assignment.quantity;
            }

            return acc;
        },
        {} as Record<
            string,
            {
                modelCode: string;
                colorName: string;
                sizes: Record<string, number>; // { "S": 10, "M": 20, etc }
            }
        >,
    );

    // Obtener todas las tallas únicas ordenadas
    const allSizes = [
        ...new Set(remissionAssignments.map((a: any) => a.sizeCode)),
    ].sort((a: any, b: any) => {
        // Orden personalizado de tallas
        const sizeOrder = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];
        const indexA = sizeOrder.indexOf(a);
        const indexB = sizeOrder.indexOf(b);

        if (indexA !== -1 && indexB !== -1) {
            return indexA - indexB;
        }
        if (indexA !== -1) return -1;
        if (indexB !== -1) return 1;

        return a.localeCompare(b);
    });

    // Formato de fecha
    const formattedDate = new Intl.DateTimeFormat("es-ES", {
        year: "numeric",
        month: "long",
        day: "numeric",
    }).format(remissionDate);

    // Formatear fecha de creación de orden si está disponible
    let orderCreationDate = null;

    if (remissionOrder?.creationDate) {
        try {
            const orderDate =
                remissionOrder.creationDate instanceof Date
                    ? remissionOrder.creationDate
                    : new Date(remissionOrder.creationDate);

            if (!isNaN(orderDate.getTime())) {
                orderCreationDate = new Intl.DateTimeFormat("es-ES", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                }).format(orderDate);
            }
        } catch (error) {
            console.error("Error formatting order creation date:", error);
        }
    }

    // Efecto para asegurar que el documento esté correctamente formateado para impresión
    useEffect(() => {
        // Asegurarse de que el documento esté listo para la impresión
        const setupPrintStyles = () => {
            // Agregar clase especial para depuración
            if (ref && typeof ref === "object" && ref.current) {
                ref.current.setAttribute("data-print-ready", "true");

                // Asegurarse de que el contenido sea visible
                ref.current.style.display = "block";
                ref.current.style.height = "auto";
                ref.current.style.overflow = "visible";
                ref.current.style.backgroundColor = "white";

                // Ajustar estilo según orientación
                if (orientation === "landscape") {
                    ref.current.style.width = "100%";
                    ref.current.style.maxWidth = "100%";
                    ref.current.classList.add("landscape-document");
                }
            }
        };

        setupPrintStyles();
    }, [ref, orientation]);

    // Show loading state if fetching from database
    if (remissionId && isLoading) {
        return <div className="p-8 bg-gray-50">Cargando remisión...</div>;
    }

    // Show error if fetch failed
    if (remissionId && error) {
        return (
            <div className="p-8 bg-red-50 text-red-600">
                Error al cargar la remisión: {error.message}
            </div>
        );
    }

    // Show not found message if remission doesn't exist
    if (remissionId && !remissionData) {
        return (
            <div className="p-8 bg-yellow-50 text-yellow-600">
                Remisión no encontrada
            </div>
        );
    }

    return (
        <div
            ref={ref}
            className={`bg-white remission-document ${orientation === "landscape" ? "landscape-mode" : "portrait-mode"}`}
            id="remission-document"
            style={{
                display: "block",
                height: "auto",
                overflow: "visible",
                backgroundColor: "white",
                pageBreakInside: "avoid",
                width: orientation === "landscape" ? "260mm" : "190mm",
                maxWidth: "100%",
                padding: "15mm",
                margin: "0 auto",
                fontSize: "11pt",
                lineHeight: "1.4",
            }}
        >
            {/* Encabezado */}
            <div className="mb-4 border-b-2 border-gray-300 pb-3">
                <div className="flex justify-between items-start gap-4">
                    <div className="flex-1">
                        <h1 className="text-xl font-bold text-gray-900 uppercase tracking-wide">
                            REMISIÓN DE ASIGNACIÓN
                        </h1>
                        <div className="mt-2 flex flex-wrap gap-x-8 gap-y-1">
                            <p className="text-sm text-gray-700">
                                <span className="font-semibold">Fecha:</span>{" "}
                                <span className="text-gray-900">
                                    {formattedDate}
                                </span>
                            </p>
                            <p className="text-sm text-gray-700">
                                <span className="font-semibold">Folio:</span>{" "}
                                <span className="font-bold text-gray-900 text-base">
                                    {remissionFolio}
                                </span>
                            </p>
                        </div>
                    </div>

                    <div className="text-right">
                        {/* Logo de la empresa */}
                        {simplified ? (
                            <div className="text-right">
                                <h2 className="text-lg font-bold text-blue-700">
                                    LOHARI TEXTILES
                                </h2>
                                <p className="text-xs text-gray-600 mt-1 font-medium">
                                    Gestión de Producción Textil
                                </p>
                            </div>
                        ) : (
                            <>
                                <div className="h-12 w-24 relative">
                                    <Image
                                        fill
                                        priority
                                        alt="Lohari Textiles"
                                        src="/LOGO-LOHARI.svg"
                                        style={{ objectFit: "contain" }}
                                    />
                                </div>
                                <p className="text-xs text-gray-600 mt-1 font-medium">
                                    Gestión de Producción Textil
                                </p>
                            </>
                        )}
                    </div>
                </div>
            </div>

            {/* Contenedor flexible para información del contratista y orden */}
            <div className="flex flex-wrap gap-4 mb-5">
                {/* Información del contratista */}
                <div className="flex-1 min-w-[280px]">
                    <h2 className="text-md font-semibold text-blue-700 mb-2">
                        Información del Contratista
                    </h2>
                    <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                        {remissionContractorId &&
                            (simplified ? (
                                // Versión simplificada para PDF
                                <div className="space-y-1">
                                    <p className="text-sm font-semibold text-gray-800">
                                        {remissionData?.contractor?.name ||
                                            "Contratista"}
                                    </p>
                                    {(remissionData?.contractor?.firstName ||
                                        remissionData?.contractor
                                            ?.lastName) && (
                                        <p className="text-sm text-gray-600">
                                            {
                                                remissionData?.contractor
                                                    ?.firstName
                                            }{" "}
                                            {
                                                remissionData?.contractor
                                                    ?.middleName
                                            }
                                            {
                                                remissionData?.contractor
                                                    ?.lastName
                                            }{" "}
                                            {
                                                remissionData?.contractor
                                                    ?.secondLastName
                                            }
                                        </p>
                                    )}
                                    {remissionData?.contractor?.notes && (
                                        <p className="text-xs text-gray-500 italic">
                                            {remissionData?.contractor?.notes}
                                        </p>
                                    )}
                                </div>
                            ) : (
                                <ContractorInfo
                                    contractorId={remissionContractorId}
                                />
                            ))}
                    </div>
                </div>

                {/* Información de la orden */}
                {remissionOrder && (
                    <div className="flex-1 min-w-[280px]">
                        <h2 className="text-md font-semibold text-blue-700 mb-2">
                            Información de la Orden
                        </h2>
                        <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                            <div className="grid grid-cols-1 gap-2">
                                <p className="text-sm text-gray-600">
                                    <span className="font-medium">
                                        Orden de trabajo:
                                    </span>{" "}
                                    <span className="text-gray-800">
                                        {remissionOrder.cutOrder ||
                                            remissionOrder.id}
                                    </span>
                                </p>

                                {orderCreationDate && (
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">
                                            Fecha de creación:
                                        </span>{" "}
                                        <span className="text-gray-800">
                                            {orderCreationDate}
                                        </span>
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Detalle de asignaciones */}
            <div className="mb-5">
                <h2 className="text-md font-semibold text-blue-700 mb-2">
                    Detalle de Asignaciones
                </h2>

                <div className="border border-gray-300 rounded overflow-hidden">
                    <table
                        className="w-full border-collapse"
                        style={{ tableLayout: "auto" }}
                    >
                        <thead className="bg-gray-100">
                            <tr>
                                <th className="px-2 py-1.5 text-left text-xs font-semibold text-gray-700 uppercase border-r border-gray-300">
                                    Orden Trabajo
                                </th>
                                <th className="px-2 py-1.5 text-left text-xs font-semibold text-gray-700 uppercase border-r border-gray-300">
                                    Partidas
                                </th>
                                <th className="px-2 py-1.5 text-left text-xs font-semibold text-gray-700 uppercase border-r border-gray-300">
                                    Modelo
                                </th>
                                <th className="px-2 py-1.5 text-left text-xs font-semibold text-gray-700 uppercase border-r border-gray-300">
                                    Color
                                </th>
                                {/* Columnas dinámicas para cada talla */}
                                {allSizes.map((size, idx) => (
                                    <th
                                        key={String(size)}
                                        className={`px-1 py-1.5 text-center text-xs font-semibold text-gray-700 uppercase ${
                                            idx < allSizes.length - 1
                                                ? "border-r border-gray-300"
                                                : ""
                                        }`}
                                        style={{ minWidth: "40px" }}
                                    >
                                        {String(size)}
                                    </th>
                                ))}
                                <th className="px-2 py-1.5 text-right text-xs font-semibold text-gray-700 uppercase border-l border-gray-300">
                                    Total
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {Object.entries(groupedAssignments).map(
                                (
                                    [groupKey, group]: [string, any],
                                    index: number,
                                ) => {
                                    // Calcular el total de unidades para este grupo
                                    const groupTotal = (
                                        Object.values(group.sizes) as number[]
                                    ).reduce(
                                        (sum: number, quantity: number) =>
                                            sum + quantity,
                                        0,
                                    );

                                    return (
                                        <tr
                                            key={groupKey}
                                            className={
                                                index % 2 === 0
                                                    ? "bg-white"
                                                    : "bg-gray-50"
                                            }
                                        >
                                            {/* Orden de Trabajo */}
                                            <td className="px-2 py-1.5 text-xs text-gray-900 border-r border-gray-300">
                                                {remissionOrder?.cutOrder ||
                                                    remissionOrder?.id ||
                                                    "N/A"}
                                            </td>

                                            {/* Partidas */}
                                            <td className="px-2 py-1.5 text-xs text-gray-900 border-r border-gray-300">
                                                {remissionOrder?.parts?.length
                                                    ? remissionOrder.parts
                                                          .map((p) => p.code)
                                                          .join(", ")
                                                    : "-"}
                                            </td>

                                            {/* Modelo */}
                                            <td className="px-2 py-1.5 text-xs text-gray-900 font-medium border-r border-gray-300">
                                                {group.modelCode}
                                            </td>

                                            {/* Color */}
                                            <td className="px-2 py-1.5 text-xs text-gray-900 border-r border-gray-300">
                                                {group.colorName}
                                            </td>

                                            {/* Columnas de tallas */}
                                            {allSizes.map(
                                                (size: any, idx: number) => (
                                                    <td
                                                        key={String(size)}
                                                        className={`px-1 py-1.5 text-xs text-gray-900 text-center ${
                                                            idx <
                                                            allSizes.length - 1
                                                                ? "border-r border-gray-300"
                                                                : ""
                                                        }`}
                                                    >
                                                        {group.sizes[size] ||
                                                            "-"}
                                                    </td>
                                                ),
                                            )}

                                            {/* Total */}
                                            <td className="px-2 py-1.5 text-xs text-gray-900 text-right font-bold border-l border-gray-300">
                                                {formatQuantity(groupTotal)}
                                            </td>
                                        </tr>
                                    );
                                },
                            )}

                            {/* Fila de total */}
                            <tr className="bg-blue-50 border-t-2 border-blue-200">
                                <td
                                    className="px-3 py-2 text-sm font-bold text-blue-700 text-right"
                                    colSpan={4 + allSizes.length}
                                >
                                    Total de unidades:
                                </td>
                                <td className="px-3 py-2 text-sm font-bold text-blue-700 text-right">
                                    {formatQuantity(totalQuantity)}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Observaciones */}
            <div className="mb-5">
                <h2 className="text-md font-semibold text-blue-700 mb-2">
                    Observaciones
                </h2>
                <div className="p-3 border border-gray-200 rounded-md bg-gray-50 min-h-16">
                    {remissionNotes ? (
                        <p className="text-sm text-gray-600">
                            {remissionNotes}
                        </p>
                    ) : (
                        <p className="text-sm text-gray-400 italic">
                            Sin observaciones adicionales
                        </p>
                    )}
                </div>
            </div>

            {/* Firmas */}
            <div className="mt-8 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-8">
                    <div className="text-center">
                        <div className="border-t border-gray-400 pt-2 mx-auto w-48">
                            <p className="text-sm font-medium text-gray-700">
                                Firma de Contratista
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                                Quien recibe
                            </p>
                        </div>
                    </div>

                    <div className="text-center">
                        <div className="border-t border-gray-400 pt-2 mx-auto w-48">
                            <p className="text-sm font-medium text-gray-700">
                                Firma Autorizada
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                                Por Lohari Textiles
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Pie de página */}
            <div className="mt-6 pt-3 border-t border-gray-200 text-center">
                <p className="text-xs text-gray-500">
                    Este documento certifica la entrega de material para
                    producción según las especificaciones descritas.
                </p>
                <p className="text-xs text-gray-500 mt-1">
                    © {new Date().getFullYear()} Lohari Textiles - Documento
                    generado electrónicamente
                </p>
            </div>
        </div>
    );
});
