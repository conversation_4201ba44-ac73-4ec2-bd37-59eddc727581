// Ripple Button Component with CSS ripple effect
"use client";

import React, { useRef, MouseEvent } from "react";
import { <PERSON>ton, ButtonProps } from "@heroui/react";
import { motion } from "framer-motion";
import clsx from "clsx";

interface RippleButtonProps extends ButtonProps {
    rippleColor?: string;
    rippleDuration?: number;
}

export function RippleButton({
    children,
    className,
    rippleColor = "rgba(255, 255, 255, 0.3)",
    rippleDuration = 600,
    onClick,
    ...props
}: RippleButtonProps) {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const rippleRef = useRef<HTMLSpanElement>(null);

    const createRipple = (event: MouseEvent<HTMLButtonElement>) => {
        const button = buttonRef.current;
        const ripple = rippleRef.current;

        if (!button || !ripple) return;

        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
        ripple.classList.add("animate-ripple");

        setTimeout(() => {
            ripple.classList.remove("animate-ripple");
        }, rippleDuration);

        if (onClick) {
            onClick(event);
        }
    };

    return (
        <motion.div
            transition={{ duration: 0.2 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
        >
            <Button
                ref={buttonRef}
                className={clsx("relative overflow-hidden", className)}
                onClick={createRipple as any}
                {...props}
            >
                <span className="relative z-10">{children}</span>
                <span
                    ref={rippleRef}
                    className="absolute rounded-full pointer-events-none"
                    style={{
                        backgroundColor: rippleColor,
                        transform: "scale(0)",
                    }}
                />
            </Button>
        </motion.div>
    );
}

// CSS for ripple animation (add to global styles)
export const rippleStyles = `
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.animate-ripple {
    animation: ripple 600ms ease-out;
}
`;
