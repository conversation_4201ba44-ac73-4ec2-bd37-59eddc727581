---
description: Visual Process Map para IMPLEMENT Mode optimizado
globs: implement-mode-map.mdc
alwaysApply: false
---
# ⚒️ IMPLEMENT MODE - VISUAL PROCESS MAP

> Modo de implementación con debugging y testing integrado

## 📊 MAPA DE PROCESO COMPLETO

```mermaid
graph TD
    Start["⚒️ AUTO-FLOW IMPLEMENT"] --> Load["📂 Cargar Plan +<br>Creative Decisions"]
    
    Load --> Level{"🎯 Nivel de<br>Complejidad"}
    
    %% Level 1 - Quick Fix
    Level -->|Level 1| L1Start["🔧 Quick Fix"]
    L1Start --> L1Locate["📍 Localizar Issue<br>🧠 debuggingapproach"]
    L1Locate --> L1Fix["⚡ Implementar Fix"]
    L1Fix --> L1Test["✅ Test Rápido"]
    
    %% Level 2-4 - Structured
    Level -->|Level 2-4| LXStart["🏗️ Implementación<br>Estructurada"]
    LXStart --> LXPhases["📋 Fases según Plan"]
    
    LXPhases --> Phase{"📦 Seleccionar<br>Fase"}
    
    Phase --> Setup["⚙️ Setup<br>🧠 programmingparadigm"]
    Setup --> Core["💻 Core Implementation<br>🧠 debuggingapproach"]
    Core --> Integration["🔗 Integration<br>🧠 scientificmethod"]
    Integration --> Testing["🧪 Testing"]
    
    Testing --> PhaseComplete{"✅ Fase<br>Completa?"}
    PhaseComplete -->|No| Debug["🐛 Debug<br>🧠 debuggingapproach"]
    Debug --> Core
    
    PhaseComplete -->|Sí| UpdateProgress["📊 Update<br>progress.md"]
    UpdateProgress --> NextPhase{"¿Más<br>Fases?"}
    NextPhase -->|Sí| Phase
    
    %% Convergencia
    L1Test --> FinalUpdate["💾 Update Final<br>Memory Bank"]
    NextPhase -->|No| FinalTest["🧪 Tests Finales<br>🧠 scientificmethod"]
    FinalTest --> FinalUpdate
    
    FinalUpdate --> Ready["✅ IMPLEMENT Complete<br>→ REFLECT"]
    
    %% Clear Thought Tools
    L1Locate -.-> CT1["🧠 debuggingapproach<br>Localizar problema"]
    Setup -.-> CT2["🧠 programmingparadigm<br>Paradigma apropiado"]
    Core -.-> CT3["🧠 debuggingapproach<br>Resolver errores"]
    Integration -.-> CT4["🧠 scientificmethod<br>Validar integración"]
    
    style Start fill:#4dbb5f
    style Level fill:#d94dbb
    style Ready fill:#5fd94d
    style Debug fill:#ff5555
```

## 🔧 PROCESO POR NIVEL

### Level 1: Quick Fix Implementation
```typescript
// Proceso simplificado para fixes rápidos
async function implementQuickFix() {
    // 1. Localizar con debuggingapproach
    const issue = await debugWithTool('debuggingapproach', {
        approachName: 'cause_elimination',
        issue: bugDescription,
        steps: ['reproduce', 'isolate', 'identify']
    });
    
    // 2. Implementar fix
    const fix = implementSolution(issue.resolution);
    
    // 3. Validar
    const valid = runQuickTests(fix);
    
    // 4. Commit
    if (valid) {
        commitChanges(`fix: ${issue.summary}`);
    }
}
```

### Level 2-4: Phased Implementation
```typescript
// Proceso estructurado por fases
async function implementPhased(plan: ImplementationPlan) {
    for (const phase of plan.phases) {
        // 1. Setup con programmingparadigm
        const paradigm = await selectWithTool('programmingparadigm', {
            paradigmName: determineOptimalParadigm(phase),
            problem: phase.description,
            approach: phase.technicalApproach
        });
        
        // 2. Core implementation
        const implementation = await buildPhase(phase, paradigm);
        
        // 3. Testing con scientificmethod
        const testResults = await testWithTool('scientificmethod', {
            hypothesis: phase.expectedBehavior,
            experiment: phase.testPlan,
            analysis: runTests(implementation)
        });
        
        // 4. Update progress
        updateProgress(phase.id, testResults);
    }
}
```

## 📊 TRACKING DE PROGRESO

```markdown
## Progress Tracking - [Project Name]

### 📈 Overall Progress: [X]%

### 📦 Phase Status
| Phase | Status | Completed | Tests | Notes |
|-------|--------|-----------|-------|-------|
| Setup | ✅ | 100% | 5/5 ✅ | - |
| Core Features | 🔄 | 75% | 12/15 | 3 pending |
| Integration | ⏳ | 0% | 0/8 | Waiting |
| Polish | ⏳ | 0% | 0/5 | Waiting |

### 🐛 Issues Tracker
| Issue | Severity | Status | Resolution |
|-------|----------|--------|------------|
| [Bug 1] | High | ✅ Fixed | [Details] |
| [Bug 2] | Medium | 🔄 Working | [Progress] |

### ⏱️ Time Tracking
- Estimated: [X] hours
- Actual: [Y] hours
- Remaining: [Z] hours
```

## 🧪 TESTING STRATEGY

```mermaid
graph LR
    Unit["Unit Tests<br>Per componente"] --> Integration["Integration Tests<br>Entre componentes"]
    Integration --> E2E["E2E Tests<br>Flujos completos"]
    E2E --> Performance["Performance Tests<br>Métricas"]
    
    style Unit fill:#4dbb5f
    style Integration fill:#4da6ff
    style E2E fill:#ffa64d
    style Performance fill:#d94dbb
```

## 🐛 DEBUG WORKFLOW

```typescript
// Cuando se encuentra un error
async function handleError(error: Error) {
    // 1. Activar debuggingapproach
    const debugPlan = await analyzeWithTool('debuggingapproach', {
        approachName: selectDebugStrategy(error),
        issue: error.message,
        findings: gatherErrorContext()
    });
    
    // 2. Implementar fix
    const fix = await implementFix(debugPlan.resolution);
    
    // 3. Verificar fix
    const verified = await verifyFix(fix);
    
    // 4. Documentar
    documentFix({
        error: error,
        approach: debugPlan,
        solution: fix,
        verification: verified
    });
}
```

## 💾 MEMORY BANK UPDATES

Durante IMPLEMENT se actualiza continuamente:

```typescript
// Progress.md updates
updateProgress({
    currentPhase: activePhase,
    completedTasks: getCompletedTasks(),
    pendingTasks: getPendingTasks(),
    issues: getActiveIssues(),
    metrics: {
        linesOfCode: countLOC(),
        testCoverage: calculateCoverage(),
        performance: measurePerformance()
    }
});

// Auto-save cada 5 comandos
if (commandCount % 5 === 0) {
    createSnapshot({
        mode: 'IMPLEMENT',
        phase: currentPhase,
        filesModified: getModifiedFiles(),
        testsStatus: getTestResults(),
        nextSteps: determineNextSteps()
    });
}
```

## ✅ CHECKLIST DE VERIFICACIÓN

### Por Fase:
- [ ] Setup completado con paradigma apropiado
- [ ] Core features implementadas
- [ ] Tests unitarios pasando
- [ ] Integración verificada
- [ ] Sin errores de linting/tipos

### Final:
- [ ] Todas las fases completadas
- [ ] Tests E2E pasando
- [ ] Performance dentro de límites
- [ ] Progress.md actualizado al 100%
- [ ] Documentación inline completa
- [ ] Listo para REFLECT

---

**IMPLEMENT Mode completo - Código construido y testeado**
