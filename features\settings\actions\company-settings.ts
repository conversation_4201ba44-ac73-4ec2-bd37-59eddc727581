"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";
import { auth } from "@/lib/auth";

const companySettingsSchema = z.object({
    companyName: z.string().min(1, "El nombre de la empresa es requerido"),
    companyLogo: z.string().url().optional().nullable(),
    rfc: z.string().optional().nullable(),
    address: z.string().optional().nullable(),
    city: z.string().optional().nullable(),
    state: z.string().optional().nullable(),
    postalCode: z.string().optional().nullable(),
    country: z.string().optional().nullable(),
    phone: z.string().optional().nullable(),
    email: z.string().email().optional().nullable(),
    website: z.string().url().optional().nullable(),
    defaultBoxCapacity: z.number().int().min(1).optional(),
    defaultBagCapacity: z.number().int().min(1).optional(),
});

export async function getCompanySettings() {
    try {
        const settings = await db.companySettings.findUnique({
            where: { id: "default" },
        });

        if (!settings) {
            // Crear configuración por defecto si no existe
            const defaultSettings = await db.companySettings.create({
                data: {
                    id: "default",
                    companyName: "Mi Empresa",
                    country: "México",
                    defaultBoxCapacity: 25,
                    defaultBagCapacity: 20,
                },
            });

            return { success: true, data: defaultSettings };
        }

        return { success: true, data: settings };
    } catch (error) {
        console.error("Error getting company settings:", error);

        return {
            success: false,
            error: "Error al obtener la configuración de la empresa",
        };
    }
}

export async function updateCompanySettings(
    data: z.infer<typeof companySettingsSchema>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        // Verificar que el usuario tenga permisos de administrador
        const user = await db.user.findUnique({
            where: { id: session.user.id },
            include: { role: true },
        });

        if (!user || user.role.name !== "Admin") {
            return {
                success: false,
                error: "Solo los administradores pueden modificar la configuración",
            };
        }

        const validatedData = companySettingsSchema.parse(data);

        // Actualizar o crear configuración
        const settings = await db.companySettings.upsert({
            where: { id: "default" },
            update: validatedData,
            create: {
                id: "default",
                ...validatedData,
            },
        });

        // Revalidar rutas que usen esta configuración
        revalidatePath("/dashboard/settings");
        revalidatePath("/dashboard/packings");

        return { success: true, data: settings };
    } catch (error) {
        console.error("Error updating company settings:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return {
            success: false,
            error: "Error al actualizar la configuración",
        };
    }
}

// Función para subir el logo de la empresa
export async function uploadCompanyLogo(formData: FormData) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const file = formData.get("logo") as File;

        if (!file) {
            return { success: false, error: "No se proporcionó archivo" };
        }

        // Validar tipo de archivo
        const validTypes = ["image/jpeg", "image/png", "image/webp"];

        if (!validTypes.includes(file.type)) {
            return {
                success: false,
                error: "Tipo de archivo no válido. Use JPG, PNG o WebP",
            };
        }

        // Validar tamaño (máximo 2MB)
        if (file.size > 2 * 1024 * 1024) {
            return {
                success: false,
                error: "El archivo es muy grande. Máximo 2MB",
            };
        }

        // Aquí deberías implementar la lógica para subir el archivo a tu servicio de almacenamiento
        // Por ejemplo: Cloudinary, AWS S3, Supabase Storage, etc.

        // Por ahora, retornamos un placeholder
        const logoUrl = `/uploads/company-logo-${Date.now()}.${file.type.split("/")[1]}`;

        // Actualizar la configuración con la nueva URL del logo
        const settings = await db.companySettings.update({
            where: { id: "default" },
            data: { companyLogo: logoUrl },
        });

        revalidatePath("/dashboard/settings");
        revalidatePath("/dashboard/packings");

        return { success: true, data: { logoUrl } };
    } catch (error) {
        console.error("Error uploading company logo:", error);

        return { success: false, error: "Error al subir el logo" };
    }
}
