"use client";

import type { Order } from "@/features/assignments/types/types";
import type { OrderPart } from "@/shared/types/common";

import React, { useState, useCallback, useEffect } from "react";
import {
    Button,
    Select,
    SelectItem,
    DateRangePicker,
    Switch,
    Input,
    Popover,
    PopoverTrigger,
    PopoverContent,
    Badge,
    Divider,
    RadioGroup,
    Radio,
} from "@heroui/react";
import {
    FunnelIcon,
    XMarkIcon,
    BookmarkIcon,
    MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { parseDate } from "@internationalized/date";

import { useAutocomplete } from "../hooks/useAutocomplete";
import { OrderFilters } from "../hooks/useOrderFilters";

import { AutocompleteInput } from "./AutocompleteInput";

interface OrderWithExtras extends Order {
    code?: string;
    parts?: OrderPart[];
    createdAt: string;
}

interface Suggestion {
    id: string;
    value: string;
    label: string;
    type:
        | "cutOrder"
        | "orderCode"
        | "customer"
        | "part"
        | "model"
        | "orderPart";
    category?: string;
    icon?: string;
    metadata?: {
        customerName?: string;
        orderDate?: string;
        orderId?: string;
        cutOrder?: string;
        ordersCount?: number;
        recentOrderId?: string;
        recentCutOrder?: string;
        orderCode?: string;
        description?: string;
    };
}

interface OrderFiltersPanelProps {
    filters: OrderFilters;
    onFilterChange: (key: keyof OrderFilters, value: unknown) => void;
    onClearFilters: () => void;
    orderCount: number;
    orders: OrderWithExtras[];
    onSuggestionSelect?: (suggestion: Suggestion) => void;
}

export function OrderFiltersPanel({
    filters,
    onFilterChange,
    onClearFilters,
    orderCount,
    orders,
    onSuggestionSelect,
}: OrderFiltersPanelProps) {
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [presetName, setPresetName] = useState("");
    const [savedPresets, setSavedPresets] = useState<
        Record<string, OrderFilters>
    >({});

    // Cargar presets guardados cuando el componente se monte
    useEffect(() => {
        if (typeof window !== "undefined") {
            const presets = JSON.parse(
                localStorage.getItem("order-filter-presets") || "{}",
            );

            setSavedPresets(presets);
        }
    }, []);

    // Función para obtener sugerencias unificadas
    const getUnifiedSuggestions = useCallback(
        async (term: string) => {
            if (!term || term.length < 2) return [];

            const searchTerm = term.toLowerCase();
            const suggestions: Suggestion[] = [];
            const maxPerCategory = 3;

            // Buscar en órdenes de corte
            const cutOrderMatches = orders
                .filter((order) =>
                    order.cutOrder?.toLowerCase().includes(searchTerm),
                )
                .slice(0, maxPerCategory)
                .map((order) => ({
                    id: `cutOrder-${order.id}`,
                    value: order.cutOrder || "",
                    label: order.cutOrder || "",
                    type: "cutOrder" as const,
                    category: "Orden de corte",
                    icon: "HashtagIcon",
                    metadata: {
                        customerName: order.customer?.name,
                        orderDate: order.createdAt
                            ? new Date(order.createdAt).toLocaleDateString()
                            : undefined,
                        orderId: order.id,
                        cutOrder: order.cutOrder || undefined,
                    },
                }));

            // Buscar en códigos de orden
            const orderCodeMatches = orders
                .filter((order) =>
                    order.code?.toLowerCase().includes(searchTerm),
                )
                .slice(0, maxPerCategory)
                .map((order) => ({
                    id: `code-${order.id}`,
                    value: order.code || "",
                    label: order.code || "",
                    type: "orderCode" as const,
                    category: "Código",
                    icon: "DocumentTextIcon",
                    metadata: {
                        customerName: order.customer?.name,
                        cutOrder: order.cutOrder || undefined,
                        orderId: order.id,
                    },
                }));

            // Buscar en nombres de clientes
            const customerSet = new Set<string>();
            const customerMatches = orders
                .filter((order) =>
                    order.customer?.name?.toLowerCase().includes(searchTerm),
                )
                .filter((order) => {
                    if (customerSet.has(order.customer.name)) return false;
                    customerSet.add(order.customer.name);

                    return true;
                })
                .slice(0, maxPerCategory)
                .map((order) => {
                    const customerOrders = orders.filter(
                        (o) => o.customer?.id === order.customer.id,
                    );
                    const recentOrder = customerOrders.sort(
                        (a, b) =>
                            new Date(b.createdAt).getTime() -
                            new Date(a.createdAt).getTime(),
                    )[0];

                    return {
                        id: `customer-${order.customer.id}`,
                        value: order.customer.name || "",
                        label: order.customer.name || "",
                        type: "customer" as const,
                        category: "Cliente",
                        icon: "UserIcon",
                        metadata: {
                            ordersCount: customerOrders.length,
                            recentOrderId: recentOrder?.id,
                            recentCutOrder: recentOrder?.cutOrder || undefined,
                        },
                    };
                });

            // Buscar en partidas
            const partMatches: Suggestion[] = [];
            const seenParts = new Set();

            for (const order of orders) {
                if (partMatches.length >= maxPerCategory) break;

                order.parts?.forEach((part: OrderPart) => {
                    if (
                        partMatches.length < maxPerCategory &&
                        part.code.toLowerCase().includes(searchTerm) &&
                        !seenParts.has(part.code)
                    ) {
                        seenParts.add(part.code);
                        partMatches.push({
                            id: `part-${part.id}`,
                            value: part.code,
                            label: part.code,
                            type: "part" as const,
                            category: "Partida",
                            icon: "TagIcon",
                            metadata: {
                                orderCode: order.code || "",
                                customerName: order.customer?.name,
                                orderId: order.id,
                                cutOrder: order.cutOrder || undefined,
                            },
                        });
                    }
                });
            }

            // Buscar en modelos
            const modelMatches: Suggestion[] = [];
            const seenModels = new Set();

            for (const order of orders) {
                if (modelMatches.length >= maxPerCategory) break;

                order.garments?.forEach((g) => {
                    if (
                        modelMatches.length < maxPerCategory &&
                        g.model?.code?.toLowerCase().includes(searchTerm) &&
                        !seenModels.has(g.model.code)
                    ) {
                        seenModels.add(g.model.code);
                        modelMatches.push({
                            id: `model-${g.model.id}`,
                            value: g.model.code,
                            label: g.model.code,
                            type: "model" as const,
                            category: "Modelo",
                            icon: "CubeIcon",
                            metadata: {
                                description: g.model.description,
                                orderId: order.id,
                                cutOrder: order.cutOrder || undefined,
                            },
                        });
                    }
                });
            }

            // Combinar todas las sugerencias
            suggestions.push(
                ...cutOrderMatches,
                ...orderCodeMatches,
                ...customerMatches,
                ...partMatches,
                ...modelMatches,
            );

            return suggestions;
        },
        [orders],
    );

    // Hook de autocompletado unificado
    const unifiedAutocomplete = useAutocomplete({
        searchValue: filters.searchQuery,
        onSearchChange: (value) => onFilterChange("searchQuery", value),
        getSuggestions: getUnifiedSuggestions,
        onSelect:
            onSuggestionSelect ||
            ((suggestion) => onFilterChange("searchQuery", suggestion.value)),
    });

    // Estadísticas de órdenes
    const orderStats = React.useMemo(() => {
        const stats = {
            statuses: {} as Record<string, number>,
            customers: new Set(),
            models: new Set(),
        };

        orders.forEach((order) => {
            if (order.status) {
                const statusKey = order.status as unknown as string;

                stats.statuses[statusKey] =
                    (stats.statuses[statusKey] || 0) + 1;
            }
            if (order.customer) stats.customers.add(order.customer.id);
            order.garments?.forEach((g) => {
                if (g.model) stats.models.add(g.model.id);
            });
        });

        return stats;
    }, [orders]);

    return (
        <div className="space-y-4">
            {/* Búsqueda unificada con autocompletado */}
            <AutocompleteInput
                icon={<MagnifyingGlassIcon className="w-4 h-4" />}
                inputRef={unifiedAutocomplete.inputRef}
                isLoading={unifiedAutocomplete.isLoading}
                label="Buscar órdenes"
                placeholder="Buscar por código, orden de corte, cliente, partida o modelo..."
                renderSuggestion={(suggestion) => (
                    <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-500">
                                {suggestion.category}:
                            </span>
                            <span className="font-medium">
                                {suggestion.label}
                            </span>
                        </div>
                        {(suggestion.metadata?.cutOrder ||
                            suggestion.metadata?.recentCutOrder) && (
                            <div className="flex items-center gap-2 text-xs text-gray-400">
                                <span>
                                    Orden:{" "}
                                    {suggestion.metadata.cutOrder ||
                                        suggestion.metadata.recentCutOrder}
                                </span>
                                {suggestion.metadata.customerName && (
                                    <>
                                        <span>•</span>
                                        <span>
                                            {suggestion.metadata.customerName}
                                        </span>
                                    </>
                                )}
                                {suggestion.metadata.ordersCount && (
                                    <>
                                        <span>•</span>
                                        <span>
                                            {suggestion.metadata.ordersCount}{" "}
                                            órdenes
                                        </span>
                                    </>
                                )}
                            </div>
                        )}
                    </div>
                )}
                selectedIndex={unifiedAutocomplete.selectedIndex}
                showSuggestions={unifiedAutocomplete.showSuggestions}
                suggestions={unifiedAutocomplete.suggestions}
                value={filters.searchQuery}
                onBlur={unifiedAutocomplete.handleBlur}
                onChange={(value) => onFilterChange("searchQuery", value)}
                onFocus={unifiedAutocomplete.handleFocus}
                onKeyDown={unifiedAutocomplete.handleKeyDown}
                onSelect={unifiedAutocomplete.handleSelect}
            />

            {/* Botón para filtros avanzados */}
            <div className="flex items-center justify-between">
                <Button
                    size="sm"
                    startContent={<FunnelIcon className="w-4 h-4" />}
                    variant="flat"
                    onPress={() => setShowAdvanced(!showAdvanced)}
                >
                    Filtros avanzados
                    {filters.status.length > 0 ||
                    filters.clientId ||
                    filters.urgency !== "all" ? (
                        <Badge
                            color="primary"
                            content={
                                filters.status.length +
                                (filters.clientId ? 1 : 0) +
                                (filters.urgency !== "all" ? 1 : 0)
                            }
                            size="sm"
                        >
                            <span className="sr-only">Active filters</span>
                        </Badge>
                    ) : null}
                </Button>

                {(filters.searchQuery || filters.status.length > 0) && (
                    <Button
                        color="danger"
                        size="sm"
                        startContent={<XMarkIcon className="w-4 h-4" />}
                        variant="light"
                        onPress={onClearFilters}
                    >
                        Limpiar filtros
                    </Button>
                )}
            </div>

            {/* Panel de filtros avanzados */}
            {showAdvanced && (
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-4">
                    {/* Estado */}
                    <Select
                        label="Estado"
                        placeholder="Todos los estados"
                        selectedKeys={filters.status}
                        selectionMode="multiple"
                        onSelectionChange={(keys) =>
                            onFilterChange("status", Array.from(keys))
                        }
                    >
                        <SelectItem key="pendiente">
                            Pendiente ({orderStats.statuses.pendiente || 0})
                        </SelectItem>
                        <SelectItem key="enProgreso">
                            En Progreso ({orderStats.statuses.enProgreso || 0})
                        </SelectItem>
                        <SelectItem key="completado">
                            Completado ({orderStats.statuses.completado || 0})
                        </SelectItem>
                    </Select>

                    {/* Urgencia */}
                    <RadioGroup
                        label="Urgencia"
                        orientation="horizontal"
                        value={filters.urgency}
                        onValueChange={(value) =>
                            onFilterChange("urgency", value)
                        }
                    >
                        <Radio value="all">Todas</Radio>
                        <Radio value="urgent">Urgentes (≤3 días)</Radio>
                        <Radio value="normal">Normal (&gt;3 días)</Radio>
                    </RadioGroup>

                    {/* Rango de fechas */}
                    <DateRangePicker
                        label="Rango de fechas de entrega"
                        value={
                            filters.dateRange.from && filters.dateRange.to
                                ? {
                                      start: parseDate(
                                          filters.dateRange.from
                                              .toISOString()
                                              .split("T")[0],
                                      ),
                                      end: parseDate(
                                          filters.dateRange.to
                                              .toISOString()
                                              .split("T")[0],
                                      ),
                                  }
                                : null
                        }
                        onChange={(value) => {
                            if (value) {
                                onFilterChange("dateRange", {
                                    from: new Date(value.start.toString()),
                                    to: new Date(value.end.toString()),
                                });
                            } else {
                                onFilterChange("dateRange", {
                                    from: null,
                                    to: null,
                                });
                            }
                        }}
                    />

                    {/* Switches adicionales */}
                    <div className="space-y-2">
                        <Switch
                            isSelected={filters.hasInventory}
                            onValueChange={(value) =>
                                onFilterChange("hasInventory", value)
                            }
                        >
                            Solo órdenes con inventario disponible
                        </Switch>
                    </div>

                    {/* Cantidad mínima */}
                    <Input
                        label="Cantidad mínima de prendas"
                        placeholder="0"
                        type="number"
                        value={filters.minQuantity?.toString() || ""}
                        onValueChange={(value) =>
                            onFilterChange(
                                "minQuantity",
                                value ? parseInt(value) : null,
                            )
                        }
                    />
                </div>
            )}

            {/* Resumen de resultados */}
            <div className="flex items-center justify-between text-sm text-gray-500">
                <span>
                    Mostrando {orderCount} de {orders.length} órdenes
                </span>

                {/* Presets */}
                <Popover placement="bottom-end">
                    <PopoverTrigger>
                        <Button
                            size="sm"
                            startContent={<BookmarkIcon className="w-4 h-4" />}
                            variant="light"
                        >
                            Presets
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-64">
                        <div className="space-y-3 p-2">
                            <h4 className="font-semibold text-sm">
                                Guardar filtros actuales
                            </h4>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Nombre del preset"
                                    size="sm"
                                    value={presetName}
                                    onValueChange={setPresetName}
                                />
                                <Button
                                    color="primary"
                                    isDisabled={!presetName}
                                    size="sm"
                                    onPress={() => {
                                        // Guardar preset
                                        if (typeof window !== "undefined") {
                                            const presets = JSON.parse(
                                                localStorage.getItem(
                                                    "order-filter-presets",
                                                ) || "{}",
                                            );

                                            presets[presetName] = filters;
                                            localStorage.setItem(
                                                "order-filter-presets",
                                                JSON.stringify(presets),
                                            );
                                            setSavedPresets(presets); // Actualizar el estado
                                            setPresetName("");
                                        }
                                    }}
                                >
                                    Guardar
                                </Button>
                            </div>

                            <Divider />

                            <h4 className="font-semibold text-sm">
                                Presets guardados
                            </h4>
                            <div className="space-y-1">
                                {Object.keys(savedPresets).length === 0 ? (
                                    <p className="text-sm text-gray-500">
                                        No hay presets guardados
                                    </p>
                                ) : (
                                    Object.keys(savedPresets).map((name) => (
                                        <Button
                                            key={name}
                                            fullWidth
                                            size="sm"
                                            variant="flat"
                                            onPress={() => {
                                                if (savedPresets[name]) {
                                                    Object.entries(
                                                        savedPresets[name],
                                                    ).forEach(
                                                        ([key, value]) => {
                                                            onFilterChange(
                                                                key as keyof OrderFilters,
                                                                value as OrderFilters[keyof OrderFilters],
                                                            );
                                                        },
                                                    );
                                                }
                                            }}
                                        >
                                            {name}
                                        </Button>
                                    ))
                                )}
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            </div>
        </div>
    );
}
