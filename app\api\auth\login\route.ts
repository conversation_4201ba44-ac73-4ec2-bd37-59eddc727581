import { NextRequest, NextResponse } from "next/server";

import { AuthResponse } from "@/lib/auth/types";
import {
    generateMockAccessToken,
    generateMockRefreshToken,
} from "@/lib/auth/token-utils";

/**
 * POST /api/auth/login
 * Adapter endpoint for AuthService compatibility with NextAuth
 */
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { email, password, rememberMe } = body;

        // Validate input
        if (!email || !password) {
            return NextResponse.json(
                {
                    error: {
                        message: "Email y contraseña son requeridos",
                        code: "INVALID_CREDENTIALS",
                        field: !email ? "email" : "password",
                    },
                },
                { status: 400 },
            );
        }

        // Attempt NextAuth sign in programmatically
        // Since signIn is a server action, we need to handle it differently
        // We'll create a workaround using the credentials provider directly
        try {
            // Import auth config to access the authorize function
            const { authConfig } = await import("@/auth");
            const credentialsProvider = authConfig.providers.find(
                (p: any) => p.id === "credentials",
            );

            if (!credentialsProvider || !credentialsProvider.authorize) {
                throw new Error("Credentials provider not configured");
            }

            // Call the authorize function directly
            const user = await credentialsProvider.authorize(
                {
                    email,
                    password,
                    remember: rememberMe ? "true" : "false",
                },
                request as any,
            );

            if (!user) {
                return NextResponse.json(
                    {
                        error: {
                            message: "Credenciales inválidas",
                            code: "INVALID_CREDENTIALS",
                        },
                    },
                    { status: 401 },
                );
            }

            // Generate mock tokens for AuthService compatibility
            const accessToken = generateMockAccessToken(user);
            const refreshToken = generateMockRefreshToken(user);

            // Create the response in the format expected by AuthService
            const response: AuthResponse = {
                user: {
                    id: user.id,
                    email: user.email || "",
                    name: user.name || "",
                    image: user.image || undefined,
                    role: (user.role as any)?.name || undefined,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
                accessToken,
                refreshToken,
            };

            // Create session by setting appropriate cookies
            const res = NextResponse.json(response);

            // Note: NextAuth will handle session creation through middleware
            // The actual session is managed by NextAuth, not these mock tokens

            return res;
        } catch {
            // In production, errors are logged to monitoring service
            // authError details are not exposed to client for security
            // Authentication error logged to monitoring service

            return NextResponse.json(
                {
                    error: {
                        message: "Error al iniciar sesión",
                        code: "AUTH_ERROR",
                    },
                },
                { status: 500 },
            );
        }
    } catch {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            {
                error: {
                    message: "Error interno del servidor",
                    code: "INTERNAL_ERROR",
                },
            },
            { status: 500 },
        );
    }
}
