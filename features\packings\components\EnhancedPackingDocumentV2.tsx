"use client";

import type { PackingWithRelations } from "@/lib/types/packing";

import React from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";

interface EnhancedPackingDocumentV2Props {
    packing: PackingWithRelations;
    showSignatures?: boolean;
}

interface GroupedProduct {
    modelo: string;
    ordenCorte: string | null;
    partida: string | null;
    color: string;
    descripcion: string;
    tipo: string;
    cliente: string;
    qualityData: {
        [key: string]: {
            // size code
            primera: number;
            segunda: number;
            manchada: number;
            incompleta: number;
        };
    };
    packaging: {
        cajas: Record<string, number>; // size -> boxes
        piezas: Record<string, number>; // size -> pieces
        bolsas1ra: Record<string, number>; // size -> bags
        bolsas2da: Record<string, number>; // size -> bags
    };
}

export function EnhancedPackingDocumentV2({
    packing,
    showSignatures = true,
}: EnhancedPackingDocumentV2Props) {
    // Agrupar productos por modelo + orden + partida + color
    const groupProducts = (): GroupedProduct[] => {
        const groups = new Map<string, GroupedProduct>();

        packing.details.forEach((detail) => {
            const garmentSize = detail.garmentSize;
            const model = garmentSize.garment.model;
            const color = garmentSize.garment.color;
            const size = garmentSize.size;

            // Obtener orden y partida
            const orderId = packing.orderId || "";
            const orderNumber = packing.order?.cutOrder || "";
            const partNumber = detail.partNumber || "";

            const groupKey = `${model.code}-${orderNumber}-${partNumber}-${color.name}`;

            if (!groups.has(groupKey)) {
                groups.set(groupKey, {
                    modelo: model.code,
                    ordenCorte: orderNumber || null,
                    partida: partNumber || null,
                    color: color.name,
                    descripcion: model.description || "",
                    tipo: "SMART FIT", // This should come from model metadata
                    cliente: packing.customer.name,
                    qualityData: {},
                    packaging: {
                        cajas: {},
                        piezas: {},
                        bolsas1ra: {},
                        bolsas2da: {},
                    },
                });
            }

            const group = groups.get(groupKey)!;

            // Initialize size data if not exists
            if (!group.qualityData[size.code]) {
                group.qualityData[size.code] = {
                    primera: 0,
                    segunda: 0,
                    manchada: 0,
                    incompleta: 0,
                };
            }

            // Add quantities by quality
            const qualityType = detail.qualityType || "primera";

            if (qualityType === "primera") {
                group.qualityData[size.code].primera += detail.quantity;
            } else if (qualityType === "segunda") {
                group.qualityData[size.code].segunda += detail.quantity;
            } else if (qualityType === "manchada") {
                group.qualityData[size.code].manchada += detail.quantity;
            } else if (qualityType === "incompleta") {
                group.qualityData[size.code].incompleta += detail.quantity;
            }

            // Add packaging data
            if (detail.packagingType === "caja" && detail.packagingUnits) {
                group.packaging.cajas[size.code] =
                    (group.packaging.cajas[size.code] || 0) +
                    detail.packagingUnits;
            }

            if (detail.loosePieces && detail.loosePieces > 0) {
                group.packaging.piezas[size.code] =
                    (group.packaging.piezas[size.code] || 0) +
                    detail.loosePieces;
            }

            if (detail.packagingType === "bolsa" && detail.packagingUnits) {
                // Determine if it's first or second quality bag
                if (qualityType === "primera") {
                    group.packaging.bolsas1ra[size.code] =
                        (group.packaging.bolsas1ra[size.code] || 0) +
                        detail.packagingUnits;
                } else {
                    group.packaging.bolsas2da[size.code] =
                        (group.packaging.bolsas2da[size.code] || 0) +
                        detail.packagingUnits;
                }
            }
        });

        return Array.from(groups.values());
    };

    const productGroups = groupProducts();

    // Get all unique sizes in order
    const allSizes = new Set<string>();

    productGroups.forEach((group) => {
        Object.keys(group.qualityData).forEach((size) => allSizes.add(size));
    });
    const sortedSizes = ["XS", "S", "M", "L", "XL", "2XL", "3XL"].filter(
        (size) => allSizes.has(size),
    );

    // Calculate totals
    let grandTotal = 0;

    productGroups.forEach((group) => {
        Object.values(group.qualityData).forEach((sizeData) => {
            grandTotal +=
                sizeData.primera +
                sizeData.segunda +
                sizeData.manchada +
                sizeData.incompleta;
        });
    });

    // Get company info
    const companyInfo = (packing.companyInfo as any) || {
        name: "PEDRO LOBATO",
        address: "Dirección de la empresa",
    };

    // Format date
    const formattedDate = format(
        new Date(packing.deliveryDate),
        "EEEE, d 'de' MMMM 'de' yyyy",
        { locale: es },
    );

    return (
        <div
            className="bg-white text-black"
            id={`packing-document-${packing.id}`}
            style={{
                width: "297mm",
                minHeight: "210mm",
                padding: "10mm",
                fontFamily: "monospace",
                fontSize: "10pt",
            }}
        >
            {/* Header */}
            <div className="text-center mb-8">
                <h1 style={{ fontSize: "16pt", letterSpacing: "0.5em" }}>
                    LISTA DE EMPAQUE
                </h1>
                <div className="text-right mt-4">
                    <strong>FOLIO: {packing.folio}</strong>
                </div>
            </div>

            {/* From/To Section */}
            <div className="flex justify-between mb-6">
                <div>
                    <strong>DE:</strong> {companyInfo.name}
                </div>
                <div>
                    <strong>Para:</strong> {packing.customer.name}
                    {packing.subCustomer && ` - ${packing.subCustomer.name}`}
                </div>
                <div>
                    <strong>Fecha:</strong> {formattedDate}
                </div>
            </div>

            {/* Products Table */}
            <table
                className="w-full border-collapse mb-8"
                style={{ fontSize: "8pt" }}
            >
                <thead>
                    <tr>
                        <th className="border border-black p-1 text-left">
                            MODELO
                        </th>
                        <th className="border border-black p-1 text-left">
                            ORDEN DE CORTE
                        </th>
                        <th className="border border-black p-1 text-left">
                            PARTIDA
                        </th>
                        <th className="border border-black p-1 text-left">
                            COLOR
                        </th>
                        <th className="border border-black p-1 text-center">
                            TALLAS
                        </th>
                        {sortedSizes.map((size) => (
                            <th
                                key={size}
                                className="border border-black p-1 text-center w-12"
                            >
                                {size}
                            </th>
                        ))}
                        <th className="border border-black p-1 text-center">
                            TOTAL
                        </th>
                        <th className="border border-black p-1 text-left">
                            DESCRIPCIÓN
                        </th>
                        <th className="border border-black p-1 text-left">
                            TIPO
                        </th>
                        <th className="border border-black p-1 text-left">
                            CLIENTE
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {productGroups.map((group, groupIndex) => {
                        const qualityRows = [];
                        let groupTotal = 0;

                        // Primera row
                        const primeraTotal = Object.values(
                            group.qualityData,
                        ).reduce((sum, data) => sum + data.primera, 0);

                        if (primeraTotal > 0) {
                            qualityRows.push({
                                quality: "Primeras",
                                data: group.qualityData,
                                field: "primera" as const,
                                total: primeraTotal,
                            });
                            groupTotal += primeraTotal;
                        }

                        // Segunda row
                        const segundaTotal = Object.values(
                            group.qualityData,
                        ).reduce((sum, data) => sum + data.segunda, 0);

                        if (segundaTotal > 0) {
                            qualityRows.push({
                                quality: "Segundas de Tela",
                                data: group.qualityData,
                                field: "segunda" as const,
                                total: segundaTotal,
                            });
                            groupTotal += segundaTotal;
                        }

                        // Manchada row
                        const manchadaTotal = Object.values(
                            group.qualityData,
                        ).reduce((sum, data) => sum + data.manchada, 0);

                        if (manchadaTotal > 0) {
                            qualityRows.push({
                                quality: "manchas",
                                data: group.qualityData,
                                field: "manchada" as const,
                                total: manchadaTotal,
                            });
                            groupTotal += manchadaTotal;
                        }

                        // Incompleta row
                        const incompletaTotal = Object.values(
                            group.qualityData,
                        ).reduce((sum, data) => sum + data.incompleta, 0);

                        if (incompletaTotal > 0) {
                            qualityRows.push({
                                quality: "Incompletas",
                                data: group.qualityData,
                                field: "incompleta" as const,
                                total: incompletaTotal,
                            });
                            groupTotal += incompletaTotal;
                        }

                        return (
                            <React.Fragment key={groupIndex}>
                                {qualityRows.map((row, rowIndex) => (
                                    <tr key={`${groupIndex}-${rowIndex}`}>
                                        {rowIndex === 0 && (
                                            <>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.modelo}
                                                </td>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.ordenCorte || ""}
                                                </td>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.partida || ""}
                                                </td>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.color}
                                                </td>
                                            </>
                                        )}
                                        <td className="border border-black p-1">
                                            {row.quality}
                                        </td>
                                        {sortedSizes.map((size) => (
                                            <td
                                                key={size}
                                                className="border border-black p-1 text-center"
                                            >
                                                {row.data[size]?.[row.field] ||
                                                    ""}
                                            </td>
                                        ))}
                                        <td className="border border-black p-1 text-center font-bold">
                                            {row.total}
                                        </td>
                                        {rowIndex === 0 && (
                                            <>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.descripcion}
                                                </td>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.tipo}
                                                </td>
                                                <td
                                                    className="border border-black p-1"
                                                    rowSpan={qualityRows.length}
                                                >
                                                    {group.cliente}
                                                </td>
                                            </>
                                        )}
                                    </tr>
                                ))}

                                {/* Empty rows for spacing if needed */}
                                {qualityRows.length < 4 &&
                                    Array.from({
                                        length: 4 - qualityRows.length,
                                    }).map((_, i) => (
                                        <tr key={`${groupIndex}-empty-${i}`}>
                                            {qualityRows.length + i === 0 && (
                                                <>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.modelo}
                                                    </td>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.ordenCorte || ""}
                                                    </td>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.partida || ""}
                                                    </td>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.color}
                                                    </td>
                                                </>
                                            )}
                                            <td className="border border-black p-1">
                                                &nbsp;
                                            </td>
                                            {sortedSizes.map((size) => (
                                                <td
                                                    key={size}
                                                    className="border border-black p-1"
                                                >
                                                    &nbsp;
                                                </td>
                                            ))}
                                            <td className="border border-black p-1">
                                                &nbsp;
                                            </td>
                                            {qualityRows.length + i === 0 && (
                                                <>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.descripcion}
                                                    </td>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.tipo}
                                                    </td>
                                                    <td
                                                        className="border border-black p-1"
                                                        rowSpan={
                                                            4 -
                                                            qualityRows.length
                                                        }
                                                    >
                                                        {group.cliente}
                                                    </td>
                                                </>
                                            )}
                                        </tr>
                                    ))}
                            </React.Fragment>
                        );
                    })}
                </tbody>
                <tfoot>
                    <tr>
                        <td
                            className="border border-black p-1 text-right font-bold"
                            colSpan={5 + sortedSizes.length}
                        >
                            TOTAL GENERAL:
                        </td>
                        <td className="border border-black p-1 text-center font-bold">
                            {grandTotal}
                        </td>
                        <td className="border border-black p-1" colSpan={3}>
                            &nbsp;
                        </td>
                    </tr>
                </tfoot>
            </table>

            {/* Packaging Summary */}
            <div className="grid grid-cols-4 gap-4 mb-8">
                {productGroups.map((group, index) => (
                    <div key={index} className="border border-black p-2">
                        <div className="text-center font-bold mb-2">
                            {group.partida || group.ordenCorte || group.modelo}
                        </div>
                        <div className="text-center mb-2">
                            Piezas por Caja: 100
                        </div>

                        <table className="w-full text-xs">
                            <thead>
                                <tr>
                                    <th className="border border-black p-1">
                                        TALLAS
                                    </th>
                                    <th className="border border-black p-1">
                                        CAJAS
                                    </th>
                                    <th className="border border-black p-1">
                                        PIEZAS
                                    </th>
                                    <th className="border border-black p-1">
                                        BOLSAS 1°ra
                                    </th>
                                    <th className="border border-black p-1">
                                        BOLSAS 2°da
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {sortedSizes.map((size) => {
                                    const hasData =
                                        group.packaging.cajas[size] ||
                                        group.packaging.piezas[size] ||
                                        group.packaging.bolsas1ra[size] ||
                                        group.packaging.bolsas2da[size];

                                    if (!hasData) return null;

                                    return (
                                        <tr key={size}>
                                            <td className="border border-black p-1 text-center">
                                                {size}
                                            </td>
                                            <td className="border border-black p-1 text-center">
                                                {group.packaging.cajas[size] ||
                                                    ""}
                                            </td>
                                            <td className="border border-black p-1 text-center">
                                                {group.packaging.piezas[size] ||
                                                    ""}
                                            </td>
                                            <td className="border border-black p-1 text-center">
                                                {group.packaging.bolsas1ra[
                                                    size
                                                ] || ""}
                                            </td>
                                            <td className="border border-black p-1 text-center">
                                                {group.packaging.bolsas2da[
                                                    size
                                                ] || ""}
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td
                                        className="border border-black p-1 font-bold"
                                        colSpan={2}
                                    >
                                        TOTAL DE CAJAS
                                    </td>
                                    <td
                                        className="border border-black p-1 text-center font-bold"
                                        colSpan={3}
                                    >
                                        {Object.values(
                                            group.packaging.cajas,
                                        ).reduce((sum, val) => sum + val, 0)}
                                    </td>
                                </tr>
                                <tr>
                                    <td
                                        className="border border-black p-1 font-bold"
                                        colSpan={2}
                                    >
                                        TOTAL DE BOLSAS
                                    </td>
                                    <td
                                        className="border border-black p-1 text-center font-bold"
                                        colSpan={3}
                                    >
                                        {Object.values(
                                            group.packaging.bolsas1ra,
                                        ).reduce((sum, val) => sum + val, 0) +
                                            Object.values(
                                                group.packaging.bolsas2da,
                                            ).reduce(
                                                (sum, val) => sum + val,
                                                0,
                                            )}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                ))}
            </div>

            {/* Signatures */}
            {showSignatures && (
                <div className="grid grid-cols-4 gap-8 mt-16">
                    <div className="text-center">
                        <div className="border-t border-black mt-16 pt-2">
                            <strong>FIRMA DE RECIBIDO</strong>
                        </div>
                    </div>
                    <div className="text-center">
                        <div className="border-t border-black mt-16 pt-2">
                            <strong>FIRMA DE ENTREGADO</strong>
                            <div className="mt-2">LUIS A. MARIANO LUJAN</div>
                        </div>
                    </div>
                    <div className="text-center">
                        <div className="border-t border-black mt-16 pt-2">
                            <strong>FIRMA DE TRANSPORTE</strong>
                        </div>
                    </div>
                    <div />
                </div>
            )}
        </div>
    );
}
