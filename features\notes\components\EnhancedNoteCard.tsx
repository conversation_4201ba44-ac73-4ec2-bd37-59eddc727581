// Enhanced Note Card with premium UI/UX design
"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    <PERSON>,
    CardBody,
    CardHeader,
    <PERSON><PERSON>oot<PERSON>,
    <PERSON><PERSON>,
    Chip,
    Tooltip,
    Avatar,
    Di<PERSON>r,
    Badge,
} from "@heroui/react";
import {
    TrashIcon,
    PencilIcon,
    ClockIcon,
    ChatBubbleLeftIcon,
    SparklesIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
    FireIcon,
    HeartIcon,
    BellAlertIcon,
} from "@heroicons/react/24/outline";
import {
    ChatBubbleLeftIcon as ChatBubbleLeftIconSolid,
    HeartIcon as HeartIconSolid,
} from "@heroicons/react/24/solid";
import clsx from "clsx";

import { getUserAvatarColor } from "@/shared/utils/avatar-colors";
import { useAnimation } from "@/shared/contexts/AnimationContext";

import { useNoteComments } from "../hooks/useNoteComments";
import { useNoteLikes } from "../hooks/useNoteLikes";

import { TimeAgo } from "./TimeAgo";
import { NoteContent } from "./NoteContent";
import { NoteCommentsSection } from "./NoteCommentsSection";

// ... (interfaces remain the same)
export interface Note {
    id: string;
    content: string;
    authorId?: string;
    importanceId?: string;
    statusId?: string;
    orderId?: string;
    createdAt: Date | string;
    updatedAt?: Date | string;
    author?: {
        id?: string;
        name?: string;
        email?: string;
        image?: string;
    };
}

export interface EnhancedNoteCardProps {
    note: Note;
    orderId?: string;
    importanceOptions: { id: string; name: string; color?: string | null }[];
    statusOptions: { id: string; name: string; color?: string | null }[];
    onDelete?: () => void;
    onUpdate?: () => void;
    canEdit?: boolean;
    isEditable?: boolean;
    isDeletable?: boolean;
    commentCount?: number;
    currentUserId?: string;
    userRole?: string;
    className?: string;
    onTagClick?: (tag: string) => void;
    onMentionClick?: (mention: string) => void;
    isSelected?: boolean;
    onEdit?: () => void;
    onToggleSelect?: () => void;
    isPriority?: boolean;
}

function getInitials(name?: string | null): string {
    if (!name) return "U";
    const parts = name.trim().split(" ");

    if (parts.length === 1) return parts[0].charAt(0).toUpperCase();

    return (
        parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
}

export function EnhancedNoteCard({
    note,
    orderId,
    importanceOptions = [],
    statusOptions = [],
    onDelete,
    onUpdate,
    canEdit = false,
    isEditable,
    isDeletable,
    commentCount = 0,
    currentUserId,
    userRole,
    className,
    onTagClick,
    onMentionClick,
    isSelected = false,
    onEdit,
    onToggleSelect,
    isPriority = false,
}: EnhancedNoteCardProps) {
    const [showComments, setShowComments] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const [showSparkle, setShowSparkle] = useState(false);
    const { enableAnimations } = useAnimation();

    // Hook para manejar likes
    const initialLikesCount = (note as any)._count?.likes || 0;
    const { isLiked, likesCount, toggleLike, canLike } = useNoteLikes(
        note.id,
        initialLikesCount,
    );

    const {
        comments,
        total,
        hasMore,
        isLoading,
        isCreating,
        createComment,
        updateComment,
        deleteComment,
        loadMore,
    } = useNoteComments(note.id);

    const importance = importanceOptions.find(
        (opt) => opt.id === note.importanceId,
    );
    const status = statusOptions.find((opt) => opt.id === note.statusId);
    const isAuthor = note.authorId === currentUserId;
    const userColor = getUserAvatarColor(note.authorId);
    const actualCommentCount = total || commentCount;

    // Determine if this is a high-priority note
    const isHighPriority =
        importance?.name === "Alta" || importance?.name === "Urgente";

    // Check if note is new (created within last 24 hours)
    const isNew =
        note.createdAt &&
        new Date(note.createdAt).getTime() > Date.now() - 24 * 60 * 60 * 1000;

    // Check if note was recently updated
    const isRecentlyUpdated =
        note.updatedAt &&
        new Date(note.updatedAt).getTime() > Date.now() - 2 * 60 * 60 * 1000;

    // Animation trigger for sparkle effect
    useEffect(() => {
        if (isNew) {
            const timer = setTimeout(() => setShowSparkle(true), 500);

            return () => clearTimeout(timer);
        }
    }, [isNew]);

    // Get importance icon
    const getImportanceIcon = () => {
        switch (importance?.name) {
            case "Urgente":
                return <FireIcon className="w-4 h-4" />;
            case "Alta":
                return <ExclamationCircleIcon className="w-4 h-4" />;
            case "Media":
                return <BellAlertIcon className="w-4 h-4" />;
            default:
                return null;
        }
    };

    return (
        <motion.div
            animate="visible"
            className={clsx("relative group note-hover-container", className)}
            initial="hidden"
            style={{ transformOrigin: "center" }}
            variants={
                enableAnimations
                    ? {
                          hidden: { opacity: 0, y: 20, scale: 0.95 },
                          visible: {
                              opacity: 1,
                              y: 0,
                              scale: 1,
                              transition: {
                                  type: "spring",
                                  stiffness: 260,
                                  damping: 20,
                              },
                          },
                      }
                    : undefined
            }
            whileHover={{ y: -8, scale: 1.01, zIndex: 10 }}
            whileTap={{ scale: 0.98 }}
            onHoverEnd={() => setIsHovered(false)}
            onHoverStart={() => setIsHovered(true)}
        >
            {/* Decorative gradient background */}
            <div
                className={clsx(
                    "absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500",
                    "bg-gradient-to-br",
                    isHighPriority
                        ? "from-red-400/20 to-orange-400/20"
                        : "from-blue-400/10 to-purple-400/10",
                )}
            />

            {/* New indicator */}
            {isNew && (
                <motion.div
                    animate={{ scale: 1, rotate: 0 }}
                    className="absolute -top-2 -right-2 z-10"
                    initial={{ scale: 0, rotate: -180 }}
                    transition={{ type: "spring", stiffness: 200 }}
                >
                    <Badge className="animate-pulse" color="success" size="sm">
                        NEW
                    </Badge>
                </motion.div>
            )}

            <Card
                className={clsx(
                    "relative overflow-visible transition-all duration-300 transform-gpu",
                    "backdrop-blur-md bg-white/90 dark:bg-gray-900/90",
                    "border border-gray-200/50 dark:border-gray-700/50",
                    isHighPriority &&
                        "border-l-4 border-red-500 bg-gradient-to-r from-red-50 to-transparent dark:from-red-950/30",
                    isPriority && "border-l-4 border-primary-500",
                    isSelected &&
                        "ring-2 ring-primary-300 dark:ring-primary-700",
                    "shadow-sm hover:shadow-xl dark:shadow-gray-900/20",
                )}
            >
                <CardHeader className="relative pb-3 pt-4 px-4 bg-gradient-to-b from-gray-50/50 to-transparent dark:from-gray-800/50">
                    <div className="flex justify-between items-start w-full">
                        <div className="flex items-center gap-3">
                            {/* Enhanced Avatar with animation */}
                            <motion.div
                                transition={{ type: "spring", stiffness: 300 }}
                                whileHover={{ scale: 1.1, rotate: 5 }}
                            >
                                <Avatar
                                    className="ring-2 ring-white/80 dark:ring-gray-700/80 shadow-lg"
                                    fallback={
                                        <div className="font-semibold w-full h-full flex items-center justify-center text-sm select-none text-black dark:text-white">
                                            {getInitials(note.author?.name)}
                                        </div>
                                    }
                                    size="sm"
                                    src={note.author?.image || undefined}
                                />
                            </motion.div>

                            <div className="flex-1">
                                <div className="flex items-center gap-2">
                                    <p className="text-sm font-semibold text-foreground">
                                        {note.author?.name || "Usuario"}
                                    </p>
                                    {isAuthor && (
                                        <Chip
                                            className="h-5 text-xs bg-primary-100/50 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400"
                                            size="sm"
                                            variant="flat"
                                        >
                                            Tú
                                        </Chip>
                                    )}
                                </div>
                                <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                                    <ClockIcon className="w-3 h-3" />
                                    <TimeAgo date={note.createdAt} />
                                    {isRecentlyUpdated && (
                                        <>
                                            <span className="text-gray-400">
                                                •
                                            </span>
                                            <span className="text-amber-600 dark:text-amber-400">
                                                editado
                                            </span>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-2">
                            {/* Enhanced Importance Badge with icon */}
                            {importance && (
                                <motion.div
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Chip
                                        className="font-medium shadow-sm"
                                        size="sm"
                                        startContent={getImportanceIcon()}
                                        style={{
                                            background: `linear-gradient(135deg, ${importance.color}15 0%, ${importance.color}25 100%)`,
                                            color:
                                                importance.color || "#9CA3AF",
                                            borderColor:
                                                importance.color || "#9CA3AF",
                                            borderWidth: "1.5px",
                                            backdropFilter: "blur(8px)",
                                        }}
                                        variant="bordered"
                                    >
                                        {importance.name}
                                    </Chip>
                                </motion.div>
                            )}

                            {/* Enhanced Status Chip */}
                            {status && (
                                <motion.div
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Chip
                                        className="font-medium shadow-sm"
                                        size="sm"
                                        startContent={
                                            <CheckCircleIcon className="w-3 h-3" />
                                        }
                                        style={{
                                            background: `linear-gradient(135deg, ${status.color}10 0%, ${status.color}20 100%)`,
                                            color: status.color || "#9CA3AF",
                                            borderWidth: "1px",
                                            borderColor:
                                                status.color || "#9CA3AF",
                                            backdropFilter: "blur(8px)",
                                        }}
                                        variant="bordered"
                                    >
                                        {status.name}
                                    </Chip>
                                </motion.div>
                            )}
                        </div>
                    </div>

                    {/* Sparkle effect for new notes */}
                    {showSparkle && isNew && (
                        <motion.div
                            animate={{ opacity: 1, scale: 1 }}
                            className="absolute top-2 right-2"
                            exit={{ opacity: 0, scale: 0 }}
                            initial={{ opacity: 0, scale: 0 }}
                        >
                            <SparklesIcon className="w-5 h-5 text-yellow-500 animate-pulse" />
                        </motion.div>
                    )}
                </CardHeader>

                <Divider className="opacity-10" />

                <CardBody className="py-3 px-4">
                    <NoteContent
                        className="text-sm leading-relaxed text-gray-700 dark:text-gray-300 line-clamp-3"
                        content={note.content}
                        onMentionClick={onMentionClick}
                        onTagClick={onTagClick}
                    />

                    {/* Enhanced interaction section */}
                    <div className="mt-3 pt-3 border-t border-gray-200/20 dark:border-gray-700/20">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                {/* Comments button */}
                                <motion.button
                                    className={clsx(
                                        "flex items-center gap-1.5 px-2 py-1 rounded-full transition-all",
                                        "text-xs font-medium",
                                        actualCommentCount > 0
                                            ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50"
                                            : "text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800",
                                    )}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={() =>
                                        setShowComments(!showComments)
                                    }
                                >
                                    {showComments ? (
                                        <ChatBubbleLeftIconSolid className="w-4 h-4" />
                                    ) : (
                                        <ChatBubbleLeftIcon className="w-4 h-4" />
                                    )}
                                    <span>
                                        {actualCommentCount || "Comentar"}
                                    </span>
                                </motion.button>

                                {/* Like button */}
                                <motion.button
                                    className={clsx(
                                        "flex items-center gap-1.5 px-2 py-1 rounded-full transition-all",
                                        "text-xs font-medium",
                                        isLiked
                                            ? "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400"
                                            : "text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800",
                                        !canLike &&
                                            "opacity-50 cursor-not-allowed",
                                    )}
                                    disabled={!canLike}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={canLike ? toggleLike : undefined}
                                >
                                    {isLiked ? (
                                        <HeartIconSolid className="w-4 h-4" />
                                    ) : (
                                        <HeartIcon className="w-4 h-4" />
                                    )}
                                    <span>{likesCount}</span>
                                </motion.button>
                            </div>

                            {/* Priority indicator */}
                            {isHighPriority && (
                                <motion.div
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{
                                        repeat: Infinity,
                                        duration: 2,
                                    }}
                                >
                                    <FireIcon className="w-4 h-4 text-red-500" />
                                </motion.div>
                            )}
                        </div>
                    </div>
                </CardBody>

                {/* Enhanced action footer with animations */}
                {(isEditable ?? canEdit) && (
                    <>
                        <Divider className="opacity-10" />
                        <CardFooter className="py-2 px-4 bg-gradient-to-t from-gray-50/50 to-transparent dark:from-gray-800/30">
                            <div className="flex justify-between items-center w-full">
                                <div className="flex items-center gap-2">
                                    {onToggleSelect && (
                                        <motion.div
                                            className="relative"
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                        >
                                            <button
                                                aria-label="Seleccionar nota"
                                                className={clsx(
                                                    "relative w-6 h-6 rounded-md transition-all duration-200",
                                                    "border-2 flex items-center justify-center",
                                                    isSelected
                                                        ? "bg-primary-500 border-primary-500 shadow-lg shadow-primary-500/30"
                                                        : "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-primary-400",
                                                )}
                                                onClick={onToggleSelect}
                                            >
                                                <AnimatePresence>
                                                    {isSelected && (
                                                        <motion.svg
                                                            animate={{
                                                                scale: 1,
                                                                opacity: 1,
                                                            }}
                                                            className="w-4 h-4 text-white"
                                                            exit={{
                                                                scale: 0,
                                                                opacity: 0,
                                                            }}
                                                            fill="none"
                                                            initial={{
                                                                scale: 0,
                                                                opacity: 0,
                                                            }}
                                                            stroke="currentColor"
                                                            strokeWidth="3"
                                                            transition={{
                                                                type: "spring",
                                                                stiffness: 500,
                                                                damping: 30,
                                                            }}
                                                            viewBox="0 0 24 24"
                                                        >
                                                            <path
                                                                d="M5 13l4 4L19 7"
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                            />
                                                        </motion.svg>
                                                    )}
                                                </AnimatePresence>
                                            </button>
                                        </motion.div>
                                    )}
                                </div>

                                <div className="flex items-center gap-2">
                                    {onEdit && (
                                        <motion.div
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <Tooltip
                                                content="Editar nota"
                                                placement="top"
                                            >
                                                <Button
                                                    isIconOnly
                                                    aria-label="Editar nota"
                                                    className="bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/50 dark:hover:bg-primary-800/50 backdrop-blur-sm"
                                                    size="sm"
                                                    variant="flat"
                                                    onClick={onEdit}
                                                >
                                                    <PencilIcon className="w-4 h-4" />
                                                </Button>
                                            </Tooltip>
                                        </motion.div>
                                    )}

                                    {onDelete && isDeletable && (
                                        <motion.div
                                            whileHover={{ scale: 1.05 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            <Tooltip
                                                content="Eliminar nota"
                                                placement="top"
                                            >
                                                <Button
                                                    isIconOnly
                                                    aria-label="Eliminar nota"
                                                    className="bg-danger-100 hover:bg-danger-200 dark:bg-danger-900/50 dark:hover:bg-danger-800/50 backdrop-blur-sm"
                                                    size="sm"
                                                    variant="flat"
                                                    onClick={onDelete}
                                                >
                                                    <TrashIcon className="w-4 h-4" />
                                                </Button>
                                            </Tooltip>
                                        </motion.div>
                                    )}
                                </div>
                            </div>
                        </CardFooter>
                    </>
                )}

                {/* Comments Section */}
                <AnimatePresence>
                    {showComments && (
                        <NoteCommentsSection
                            currentUserId={currentUserId}
                            noteId={note.id}
                            userRole={userRole}
                        />
                    )}
                </AnimatePresence>
            </Card>

            {/* Shadow effect */}
            <motion.div
                animate={{
                    opacity: isHovered ? 1 : 0,
                    scale: isHovered ? 1.05 : 1,
                }}
                className={clsx(
                    "absolute -inset-4 rounded-lg pointer-events-none",
                    "shadow-2xl dark:shadow-gray-900/50",
                )}
                initial={{ opacity: 0 }}
                style={{
                    boxShadow: isHovered
                        ? "0 25px 50px -12px rgba(0, 0, 0, 0.3), 0 0 30px -5px rgba(0, 0, 0, 0.15)"
                        : "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    zIndex: -1,
                }}
                transition={{ duration: 0.3, ease: "easeOut" }}
            />

            {/* Animated background glow on hover */}
            <AnimatePresence>
                {isHovered && (
                    <motion.div
                        animate={{ opacity: 1, scale: 1.1 }}
                        className={clsx(
                            "absolute -inset-8 rounded-lg blur-3xl pointer-events-none",
                            "bg-gradient-to-br",
                            isHighPriority
                                ? "from-red-300/30 via-orange-300/30 to-yellow-300/30"
                                : "from-blue-300/25 via-purple-300/25 to-pink-300/25",
                        )}
                        exit={{ opacity: 0, scale: 0.8 }}
                        initial={{ opacity: 0, scale: 0.8 }}
                        style={{ zIndex: -2 }}
                        transition={{ duration: 0.4, ease: "easeOut" }}
                    />
                )}
            </AnimatePresence>
        </motion.div>
    );
}
