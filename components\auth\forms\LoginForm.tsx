"use client";

import { useState } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { Mail, Lock, Eye, EyeOff } from "lucide-react";

import {
    AuthCard,
    AuthInput,
    AuthButton,
    SocialButton,
    AuthDivider,
} from "@/components/auth/ui";
import { useCSRFToken } from "@/hooks/useCSRFToken";

import { useAuthForm } from "./useAuthForm";

interface LoginFormProps {
    onSuccess?: () => void;
}

export function LoginForm({ onSuccess }: LoginFormProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [authError, setAuthError] = useState<string | null>(null);
    const searchParams = useSearchParams();
    const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";

    console.log("LoginForm - callbackUrl:", callbackUrl);

    // Ensure CSRF token is available
    useCSRFToken();

    const { form, onSubmit, isLoading, errors } = useAuthForm({
        type: "login",
        callbackUrl,
        onSuccess: () => {
            setAuthError(null);
            onSuccess?.();
        },
        onError: (error) => {
            // Capturar cualquier error adicional
            console.error("LoginForm error:", error);
            if (error.message && !errors.root?.message) {
                setAuthError(error.message);
            }
        },
    });

    const { register, watch } = form;
    const email = watch("email");
    const password = watch("password");

    return (
        <AuthCard className="animate-fadeIn">
            <div className="text-center mb-8">
                <h1 className="text-3xl font-bold font-display text-neutral-800 mb-2">
                    ¡Bienvenido de nuevo!
                </h1>
                <p className="text-neutral-600">
                    Ingresa tus credenciales para acceder a tu cuenta
                </p>
            </div>

            <form className="space-y-6" onSubmit={onSubmit}>
                <AuthInput
                    {...register("email")}
                    autoComplete="email"
                    disabled={isLoading}
                    error={errors.email?.message as string}
                    icon={<Mail className="w-5 h-5" />}
                    label="Correo electrónico"
                    placeholder="<EMAIL>"
                    type="email"
                />

                <div className="relative">
                    <AuthInput
                        {...register("password")}
                        autoComplete="current-password"
                        disabled={isLoading}
                        error={errors.password?.message as string}
                        icon={<Lock className="w-5 h-5" />}
                        label="Contraseña"
                        placeholder="••••••••"
                        type={showPassword ? "text" : "password"}
                    />
                    <button
                        className="absolute right-3 top-[42px] text-neutral-500 hover:text-neutral-700 transition-colors"
                        tabIndex={-1}
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                    >
                        {showPassword ? (
                            <EyeOff className="w-5 h-5" />
                        ) : (
                            <Eye className="w-5 h-5" />
                        )}
                    </button>
                </div>

                <div className="flex items-center justify-between">
                    <label className="flex items-center gap-2 cursor-pointer">
                        <input
                            {...register("rememberMe")}
                            className="w-4 h-4 rounded border-neutral-300 text-primary-600 
                       focus:ring-primary-500 focus:ring-offset-0"
                            disabled={isLoading}
                            type="checkbox"
                        />
                        <span className="text-sm text-neutral-700">
                            Recordarme
                        </span>
                    </label>

                    <Link
                        className="text-sm text-primary-600 hover:text-primary-700 transition-colors"
                        href="/forgot-password"
                    >
                        ¿Olvidaste tu contraseña?
                    </Link>
                </div>

                {(errors.root || authError) && (
                    <div className="p-3 rounded-lg bg-error/10 border border-error/20 animate-slideIn">
                        <p className="text-sm text-error font-medium">
                            {(errors.root?.message as string) || authError}
                        </p>
                    </div>
                )}

                <AuthButton
                    disabled={!email || !password}
                    isLoading={isLoading}
                    type="submit"
                >
                    Iniciar Sesión
                </AuthButton>
            </form>

            <AuthDivider text="o continúa con" />

            <div className="space-y-3">
                <SocialButton
                    disabled={isLoading}
                    provider="google"
                    onClick={() => {
                        /* Implementar OAuth */
                    }}
                />
            </div>

            <p className="text-center text-sm text-neutral-600 mt-8">
                ¿No tienes una cuenta?{" "}
                <Link
                    className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
                    href="/register"
                >
                    Regístrate aquí
                </Link>
            </p>
        </AuthCard>
    );
}
