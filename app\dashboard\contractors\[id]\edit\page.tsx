"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    Input,
    Button,
    Textarea,
    Card,
    CardBody,
    Spinner,
    addToast,
} from "@heroui/react";
import {
    UserIcon,
    EnvelopeIcon,
    PhoneIcon,
    DocumentTextIcon,
    ChevronLeftIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    useContractor,
    useUpdateContractor,
    useValidateContractorName,
} from "@/features/contractors/hooks/useContractor";
import {
    ContractorFormProgress,
    calculateContractorProgress,
    ContractorFormHeader,
    ContractorInfoMessage,
    contractorFormMessages,
} from "@/features/contractors/components/forms";
import { updateContractorSchema } from "@/features/contractors/schemas";

type ContractorFormData = z.infer<typeof updateContractorSchema>;

// Animaciones simples
const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.3 },
};

export default function EditContractorPage() {
    const router = useRouter();
    const params = useParams();
    const contractorId = params?.id as string;

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [showPhoneInfo, setShowPhoneInfo] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const { contractor, isLoading: isLoadingContractor } =
        useContractor(contractorId);
    const { update } = useUpdateContractor();
    const { validate } = useValidateContractorName();

    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
        setError,
        clearErrors,
        reset,
    } = useForm<ContractorFormData>({
        resolver: zodResolver(updateContractorSchema),
        mode: "onChange",
        defaultValues: {
            firstName: "",
            middleName: "",
            lastName: "",
            secondLastName: "",
            email: "",
            phone: "",
            notes: "",
        },
    });

    // Cargar datos del contratista
    useEffect(() => {
        if (contractor && !isLoadingContractor && !Array.isArray(contractor)) {
            reset({
                firstName: contractor.firstName || "",
                middleName: contractor.middleName || undefined,
                lastName: contractor.lastName || "",
                secondLastName: contractor.secondLastName || undefined,
                email: contractor.email || undefined,
                phone: contractor.phone || undefined,
                notes: contractor.notes || undefined,
            });
            setIsLoading(false);
        }
    }, [contractor, isLoadingContractor, reset]);

    // Observar todos los campos para el progreso
    const watchedFields = watch();
    const formProgress = calculateContractorProgress({
        firstName: watchedFields.firstName,
        lastName: watchedFields.lastName,
        middleName: watchedFields.middleName || undefined,
        secondLastName: watchedFields.secondLastName || undefined,
        email: watchedFields.email || undefined,
        phone: watchedFields.phone || undefined,
        notes: watchedFields.notes || undefined,
    });

    // Validar nombre duplicado
    const firstName = watch("firstName");
    const lastName = watch("lastName");

    useEffect(() => {
        const validateName = async () => {
            if (
                firstName &&
                firstName.length >= 2 &&
                lastName &&
                lastName.length >= 2
            ) {
                const isValid = await validate(
                    firstName,
                    lastName,
                    contractorId,
                );

                if (!isValid) {
                    setError("firstName", {
                        type: "manual",
                        message: "Ya existe un contratista con este nombre",
                    });
                    setError("lastName", {
                        type: "manual",
                        message: "Ya existe un contratista con este nombre",
                    });
                } else {
                    if (errors.firstName?.type === "manual")
                        clearErrors("firstName");
                    if (errors.lastName?.type === "manual")
                        clearErrors("lastName");
                }
            }
        };

        const timeoutId = setTimeout(validateName, 500);

        return () => clearTimeout(timeoutId);
    }, [
        firstName,
        lastName,
        contractorId,
        validate,
        setError,
        clearErrors,
        errors,
    ]);

    const onSubmit = async (data: ContractorFormData) => {
        setIsSubmitting(true);
        try {
            // Transformar strings vacíos a null antes de enviar
            const processedData = {
                ...data,
                middleName: data.middleName || null,
                secondLastName: data.secondLastName || null,
                email: data.email || null,
                phone: data.phone || null,
                notes: data.notes || null,
            };

            await update(contractorId, processedData);
            setIsSuccess(true);

            // Redirigir después de 2 segundos
            setTimeout(() => {
                router.push("/dashboard/contractors");
            }, 2000);
        } catch (error) {
            setIsSubmitting(false);
            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "No se pudo actualizar el contratista",
                color: "danger",
            });
        }
    };

    // Mostrar info del teléfono cuando el usuario enfoca el campo
    const phone = watch("phone");

    useEffect(() => {
        if (phone && phone.length > 0) {
            setShowPhoneInfo(true);
        } else {
            setShowPhoneInfo(false);
        }
    }, [phone]);

    // Validar solo campos obligatorios
    const isFormValid =
        firstName &&
        firstName.trim().length >= 2 &&
        lastName &&
        lastName.trim().length >= 2 &&
        !errors.firstName &&
        !errors.lastName;

    return (
        <DashboardLayout
            breadcrumbs={[
                { label: "Contratistas", href: "/dashboard/contractors" },
                {
                    label: contractor?.name || "Contratista",
                    href: `/dashboard/contractors/${contractorId}/details`,
                },
                { label: "Editar" },
            ]}
            subtitle="Actualiza la información del contratista"
            title="Editar Contratista"
        >
            <motion.div {...fadeIn} className="max-w-4xl mx-auto">
                <Card className="shadow-sm border border-gray-200 dark:border-gray-700">
                    <CardBody className="p-6">
                        <AnimatePresence mode="wait">
                            {isLoading ? (
                                <div className="flex justify-center items-center py-12">
                                    <Spinner color="primary" size="lg" />
                                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                                        Cargando información del contratista...
                                    </span>
                                </div>
                            ) : isSuccess ? (
                                <div className="text-center py-16">
                                    <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full mb-6">
                                        <CheckCircleIcon className="w-10 h-10 text-green-600 dark:text-green-400" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                        ¡Contratista actualizado exitosamente!
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-400">
                                        Redirigiendo al listado de
                                        contratistas...
                                    </p>
                                </div>
                            ) : (
                                <div className="space-y-8">
                                    {/* Header */}
                                    <ContractorFormHeader
                                        contractorName={contractor?.name}
                                        mode="edit"
                                    />

                                    {/* Progress */}
                                    <ContractorFormProgress
                                        className="mb-6"
                                        value={formProgress}
                                    />

                                    {/* Form */}
                                    <form
                                        className="space-y-8"
                                        onSubmit={handleSubmit(onSubmit)}
                                    >
                                        {/* Sección: Datos Personales */}
                                        <section>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <UserIcon className="w-5 h-5 text-purple-600" />
                                                Datos Personales
                                            </h3>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <Input
                                                    {...register("firstName")}
                                                    isRequired
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    errorMessage={
                                                        errors.firstName
                                                            ?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={
                                                        !!errors.firstName
                                                    }
                                                    label="Primer Nombre"
                                                    placeholder="Ej: Juan"
                                                    startContent={
                                                        <UserIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    variant="bordered"
                                                />

                                                <Input
                                                    {...register("middleName")}
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    isDisabled={isSubmitting}
                                                    label="Segundo Nombre"
                                                    placeholder="Ej: Carlos (Opcional)"
                                                    startContent={
                                                        <UserIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    variant="bordered"
                                                />

                                                <Input
                                                    {...register("lastName")}
                                                    isRequired
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    errorMessage={
                                                        errors.lastName?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={
                                                        !!errors.lastName
                                                    }
                                                    label="Apellido Paterno"
                                                    placeholder="Ej: García"
                                                    startContent={
                                                        <UserIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    variant="bordered"
                                                />

                                                <Input
                                                    {...register(
                                                        "secondLastName",
                                                    )}
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    isDisabled={isSubmitting}
                                                    label="Apellido Materno"
                                                    placeholder="Ej: López (Opcional)"
                                                    startContent={
                                                        <UserIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    variant="bordered"
                                                />
                                            </div>

                                            {/* Mensaje de nombre duplicado */}
                                            {errors.firstName?.type ===
                                                "manual" && (
                                                <ContractorInfoMessage
                                                    {...contractorFormMessages.duplicateName}
                                                    className="mt-4"
                                                />
                                            )}
                                        </section>

                                        {/* Sección: Información de Contacto */}
                                        <section>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <EnvelopeIcon className="w-5 h-5 text-blue-600" />
                                                Información de Contacto
                                            </h3>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <Input
                                                    {...register("email")}
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    errorMessage={
                                                        errors.email?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={!!errors.email}
                                                    label="Email"
                                                    placeholder="<EMAIL>"
                                                    startContent={
                                                        <EnvelopeIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    type="email"
                                                    variant="bordered"
                                                />

                                                <Input
                                                    {...register("phone")}
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    errorMessage={
                                                        errors.phone?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={!!errors.phone}
                                                    label="Teléfono"
                                                    placeholder="+52 ************"
                                                    startContent={
                                                        <PhoneIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    type="tel"
                                                    variant="bordered"
                                                />
                                            </div>

                                            {/* Mensajes informativos */}
                                            <div className="mt-4 space-y-2">
                                                <ContractorInfoMessage
                                                    {...contractorFormMessages.emailOptional}
                                                />
                                                {showPhoneInfo && (
                                                    <ContractorInfoMessage
                                                        {...contractorFormMessages.phoneFormat}
                                                    />
                                                )}
                                            </div>
                                        </section>

                                        {/* Sección: Notas */}
                                        <section>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <DocumentTextIcon className="w-5 h-5 text-green-600" />
                                                Notas Adicionales
                                            </h3>

                                            <Textarea
                                                {...register("notes")}
                                                classNames={{
                                                    inputWrapper:
                                                        "bg-gray-50 dark:bg-gray-800",
                                                }}
                                                description={`${watchedFields.notes?.length || 0}/500 caracteres`}
                                                errorMessage={
                                                    errors.notes?.message
                                                }
                                                isDisabled={isSubmitting}
                                                isInvalid={!!errors.notes}
                                                label="Notas"
                                                maxRows={6}
                                                minRows={3}
                                                placeholder="Información adicional sobre el contratista..."
                                                startContent={
                                                    <div className="pt-1">
                                                        <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                                                    </div>
                                                }
                                                variant="bordered"
                                            />
                                        </section>

                                        {/* Mensaje de formulario completo */}
                                        {isFormValid && (
                                            <ContractorInfoMessage
                                                {...contractorFormMessages.formComplete}
                                            />
                                        )}

                                        {/* Botones */}
                                        <div className="flex gap-3 pt-6">
                                            <Button
                                                isDisabled={isSubmitting}
                                                size="lg"
                                                startContent={
                                                    <ChevronLeftIcon className="w-4 h-4" />
                                                }
                                                type="button"
                                                variant="flat"
                                                onPress={() =>
                                                    router.push(
                                                        "/dashboard/contractors",
                                                    )
                                                }
                                            >
                                                Cancelar
                                            </Button>

                                            <Button
                                                className="flex-1"
                                                color="primary"
                                                isDisabled={
                                                    !isFormValid || isSubmitting
                                                }
                                                isLoading={isSubmitting}
                                                size="lg"
                                                type="submit"
                                            >
                                                {isSubmitting
                                                    ? "Guardando..."
                                                    : "Guardar Cambios"}
                                            </Button>
                                        </div>
                                    </form>
                                </div>
                            )}
                        </AnimatePresence>
                    </CardBody>
                </Card>
            </motion.div>
        </DashboardLayout>
    );
}
