/* ===================================
   LOHARI Icon System v1.0
   Sistema unificado de espaciado para iconos
   =================================== */

/* Variables del sistema de iconos */
:root {
  /* Tamaños de iconos */
  --icon-size-xs: 1rem;      /* 16px */
  --icon-size-sm: 1.25rem;   /* 20px */
  --icon-size-md: 1.5rem;    /* 24px - default */
  --icon-size-lg: 2rem;      /* 32px */
  --icon-size-xl: 2.5rem;    /* 40px */
  
  /* Padding para wrappers */
  --icon-padding-xs: 0.125rem;  /* 2px */
  --icon-padding-sm: 0.25rem;   /* 4px */
  --icon-padding-md: 0.375rem;  /* 6px */
  --icon-padding-lg: 0.5rem;    /* 8px */
  --icon-padding-xl: 0.625rem;  /* 10px */
  
  /* Tamaños totales resultantes */
  --icon-total-xs: 1.25rem;   /* 20px */
  --icon-total-sm: 1.75rem;   /* 28px */
  --icon-total-md: 2.25rem;   /* 36px */
  --icon-total-lg: 3rem;      /* 48px */
  --icon-total-xl: 3.75rem;   /* 60px */
}

/* Base icon wrapper */
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all var(--duration-fast) var(--ease-out);
  line-height: 0;
  position: relative;
}

/* Tamaños */
.icon-xs {
  width: var(--icon-total-xs);
  height: var(--icon-total-xs);
  padding: var(--icon-padding-xs);
}

.icon-xs svg {
  width: var(--icon-size-xs);
  height: var(--icon-size-xs);
}

.icon-sm {
  width: var(--icon-total-sm);
  height: var(--icon-total-sm);
  padding: var(--icon-padding-sm);
}

.icon-sm svg {
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
}

.icon-md {
  width: var(--icon-total-md);
  height: var(--icon-total-md);
  padding: var(--icon-padding-md);
}

.icon-md svg {
  width: var(--icon-size-md);
  height: var(--icon-size-md);
}

.icon-lg {
  width: var(--icon-total-lg);
  height: var(--icon-total-lg);
  padding: var(--icon-padding-lg);
}

.icon-lg svg {
  width: var(--icon-size-lg);
  height: var(--icon-size-lg);
}

.icon-xl {
  width: var(--icon-total-xl);
  height: var(--icon-total-xl);
  padding: var(--icon-padding-xl);
}

.icon-xl svg {
  width: var(--icon-size-xl);
  height: var(--icon-size-xl);
}

/* Variantes de estilo */
.icon-ghost {
  background-color: transparent;
  color: currentColor;
}

.icon-ghost:hover {
  background-color: var(--color-gray-3);
}

.icon-soft {
  border-radius: var(--radius-lg);
}

.icon-solid {
  border-radius: var(--radius-lg);
}

.icon-bordered {
  border: 2px solid;
  border-radius: var(--radius-lg);
  background-color: transparent;
}

/* Colores - Primary */
.icon-primary.icon-soft {
  background-color: var(--color-primary-3);
  color: var(--color-primary-9);
}

.icon-primary.icon-soft:hover {
  background-color: var(--color-primary-4);
  color: var(--color-primary-10);
}

.icon-primary.icon-solid {
  background-color: var(--color-primary-9);
  color: white;
}

.icon-primary.icon-solid:hover {
  background-color: var(--color-primary-10);
}

.icon-primary.icon-bordered {
  border-color: var(--color-primary-9);
  color: var(--color-primary-9);
}

.icon-primary.icon-bordered:hover {
  background-color: var(--color-primary-3);
}

/* Colores - Success */
.icon-success.icon-soft {
  background-color: rgba(48, 164, 108, 0.15);
  color: var(--color-success-9);
}

.icon-success.icon-soft:hover {
  background-color: rgba(48, 164, 108, 0.25);
  color: var(--color-success-11);
}

.icon-success.icon-solid {
  background-color: var(--color-success-9);
  color: white;
}

.icon-success.icon-solid:hover {
  background-color: var(--color-success-11);
}

/* Colores - Warning */
.icon-warning.icon-soft {
  background-color: rgba(245, 165, 36, 0.15);
  color: var(--color-warning-9);
}

.icon-warning.icon-soft:hover {
  background-color: rgba(245, 165, 36, 0.25);
  color: var(--color-warning-11);
}

.icon-warning.icon-solid {
  background-color: var(--color-warning-9);
  color: white;
}

/* Colores - Danger */
.icon-danger.icon-soft {
  background-color: rgba(229, 77, 77, 0.15);
  color: var(--color-error-9);
}

.icon-danger.icon-soft:hover {
  background-color: rgba(229, 77, 77, 0.25);
  color: var(--color-error-11);
}

.icon-danger.icon-solid {
  background-color: var(--color-error-9);
  color: white;
}

.icon-danger.icon-solid:hover {
  background-color: var(--color-error-11);
}

/* Colores - Default/Gray */
.icon-default.icon-soft {
  background-color: var(--color-gray-3);
  color: var(--color-gray-11);
}

.icon-default.icon-soft:hover {
  background-color: var(--color-gray-4);
  color: var(--color-gray-12);
}

.icon-default.icon-bordered {
  border-color: var(--color-gray-6);
  color: var(--color-gray-11);
}

/* Estados interactivos */
.icon-wrapper.icon-interactive {
  cursor: pointer;
}

.icon-wrapper.icon-interactive:active {
  transform: scale(0.95);
}

/* Dark mode adjustments */
[data-theme="dark"] .icon-soft {
  opacity: 0.9;
}

[data-theme="dark"] .icon-ghost:hover {
  background-color: var(--color-gray-3);
}

/* Utilidades adicionales */
.icon-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Compatibilidad con botones */
button .icon-wrapper,
a .icon-wrapper {
  pointer-events: none;
}

/* Focus styles para accesibilidad */
.icon-wrapper:focus-visible {
  outline: 2px solid var(--color-primary-9);
  outline-offset: 2px;
}

/* Responsive - Opcional */
@media (max-width: 640px) {
  .icon-sm-mobile {
    width: var(--icon-total-sm);
    height: var(--icon-total-sm);
    padding: var(--icon-padding-sm);
  }
  
  .icon-sm-mobile svg {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
  }
}