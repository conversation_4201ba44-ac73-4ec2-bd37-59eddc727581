"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Valida si un código de modelo es único
 */
export async function validateModelCode(code: string, excludeId?: string) {
    if (!code || code.length < 2) {
        return { success: true, data: { isValid: false } };
    }

    return await handleDbError(async () => {
        const existingModel = await db.garmentModel.findFirst({
            where: {
                code: {
                    equals: code,
                    mode: "insensitive" as Prisma.QueryMode,
                },
                id: excludeId ? { not: excludeId } : undefined,
            },
        });

        return { isValid: !existingModel };
    }, "Error al validar código de modelo");
}
