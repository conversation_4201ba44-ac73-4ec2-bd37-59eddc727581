"use server";

import { CONFIG_NAMES } from "@/constants/config-names";
import { getNoteImportanceByName } from "@/shared/lib/config-cache";
import { prisma } from "@/shared/lib/prisma";

import {
    ActionResponse,
    createErrorResponse,
    revalidateCache,
    getCurrentUser,
} from "./utils";

/**
 * Crea una nueva nota para una orden
 */
export async function createOrderNote(
    orderId: string,
    data: {
        content: string;
        statusId: string;
        importanceId?: string;
    },
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id: orderId },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${orderId}`,
            };
        }

        // Obtener el usuario actual para asignarlo como autor
        const user = await getCurrentUser();

        if (!user || !user.id) {
            return {
                success: false,
                error: "No se pudo identificar al usuario actual",
            };
        }

        // Obtener ID de importancia por defecto si no se proporciona
        let importanceId = data.importanceId;

        if (!importanceId) {
            const mediumImportance = await getNoteImportanceByName(
                CONFIG_NAMES.noteImportance.MEDIUM,
            );

            if (!mediumImportance) {
                return {
                    success: false,
                    error: "No se encontró la importancia 'Medio' en la base de datos",
                };
            }
            importanceId = mediumImportance.id;
        }

        // Crear la nota
        const note = await prisma.note.create({
            data: {
                content: data.content,
                statusId: data.statusId,
                orderId: orderId,
                authorId: user.id,
                importanceId: importanceId as string,
            },
            include: {
                status: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
            },
        });

        // Revalidar caché para esta orden
        revalidateCache(orderId);

        return {
            success: true,
            data: note,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Actualiza una nota existente
 */
export async function updateOrderNote(
    id: string,
    data: {
        content?: string;
        statusId?: string;
        importanceId?: string;
    },
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la nota exista
        const note = await prisma.note.findUnique({
            where: { id },
        });

        if (!note) {
            return {
                success: false,
                error: `No se encontró la nota con ID ${id}`,
            };
        }

        // Obtener el usuario actual para verificar permisos
        const user = await getCurrentUser();

        if (!user || !user.id) {
            return {
                success: false,
                error: "No se pudo identificar al usuario actual",
            };
        }

        // Solo permitir que el autor o un administrador actualice la nota
        const isAuthor = note.authorId === user.id;

        // Verificar si es admin, manejando diferentes estructuras posibles para el role
        let isAdmin = false;

        if (typeof user.role === "string") {
            isAdmin = user.role === "ADMIN";
        } else if (
            user.role &&
            typeof user.role === "object" &&
            "id" in user.role
        ) {
            // Usar una aserción de tipo directa para ayudar a TypeScript
            isAdmin = (user.role as { id: string }).id === "ADMIN";
        }

        if (!isAuthor && !isAdmin) {
            return {
                success: false,
                error: "No tienes permisos para actualizar esta nota",
            };
        }

        // Actualizar la nota
        const updatedNote = await prisma.note.update({
            where: { id },
            data: {
                content: data.content !== undefined ? data.content : undefined,
                statusId:
                    data.statusId !== undefined ? data.statusId : undefined,
                importanceId:
                    data.importanceId !== undefined
                        ? data.importanceId
                        : undefined,
            },
            include: {
                status: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
            },
        });

        // Revalidar caché para la orden relacionada
        revalidateCache(note.orderId);

        return {
            success: true,
            data: updatedNote,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Elimina una nota
 */
export async function deleteOrderNote(
    id: string,
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la nota exista
        const note = await prisma.note.findUnique({
            where: { id },
        });

        if (!note) {
            return {
                success: false,
                error: `No se encontró la nota con ID ${id}`,
            };
        }

        // Obtener el usuario actual para verificar permisos
        const user = await getCurrentUser();

        if (!user || !user.id) {
            return {
                success: false,
                error: "No se pudo identificar al usuario actual",
            };
        }

        // Solo permitir que el autor o un administrador elimine la nota
        const isAuthor = note.authorId === user.id;

        // Verificar si es admin, manejando diferentes estructuras posibles para el role
        let isAdmin = false;

        if (typeof user.role === "string") {
            isAdmin = user.role === "ADMIN";
        } else if (
            user.role &&
            typeof user.role === "object" &&
            "id" in user.role
        ) {
            // Usar una aserción de tipo directa para ayudar a TypeScript
            isAdmin = (user.role as { id: string }).id === "ADMIN";
        }

        if (!isAuthor && !isAdmin) {
            return {
                success: false,
                error: "No tienes permisos para eliminar esta nota",
            };
        }

        // Guardar el ID de la orden para revalidar después
        const orderId = note.orderId;

        // Eliminar la nota
        await prisma.note.delete({
            where: { id },
        });

        // Revalidar caché para la orden relacionada
        revalidateCache(orderId);

        return {
            success: true,
            data: { id, orderId },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene todas las notas de una orden
 */
export async function getOrderNotes(
    orderId: string,
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id: orderId },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${orderId}`,
            };
        }

        // Obtener las notas
        const notes = await prisma.note.findMany({
            where: { orderId },
            include: {
                status: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return {
            success: true,
            data: notes,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
