"use server";

import type { PackingDetailFormData } from "@/lib/types/packing";

import { z } from "zod";

import { db } from "@/lib/db";

const importFromOrderSchema = z.object({
    orderId: z.string().min(1, "La orden es requerida"),
    includeAllItems: z.boolean().default(true),
});

export interface ImportFromOrderResult {
    success: boolean;
    data?: {
        order: {
            id: string;
            transferNumber: string | null;
            cutOrder: string | null;
            batch: string | null;
            customer: {
                id: string;
                name: string;
            };
        };
        suggestedDetails: PackingDetailFormData[];
        totalQuantity: number;
        estimatedBoxes: number;
        estimatedBags: number;
    };
    error?: string;
}

export async function importFromOrder(
    data: z.infer<typeof importFromOrderSchema>,
): Promise<ImportFromOrderResult> {
    try {
        const validatedData = importFromOrderSchema.parse(data);

        // Obtener la orden con todos sus detalles
        const order = await db.order.findUnique({
            where: { id: validatedData.orderId },
            include: {
                customer: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                                _count: {
                                    select: { packingDetails: true },
                                },
                            },
                        },
                    },
                },
                parts: true,
            },
        });

        if (!order) {
            return { success: false, error: "Orden no encontrada" };
        }

        // Obtener configuración de empaque por defecto
        const companySettings = await db.companySettings.findUnique({
            where: { id: "default" },
        });

        const defaultBoxCapacity = companySettings?.defaultBoxCapacity || 25;
        const defaultBagCapacity = companySettings?.defaultBagCapacity || 20;

        // Construir detalles sugeridos
        const suggestedDetails: PackingDetailFormData[] = [];
        let totalQuantity = 0;

        for (const garment of order.garments) {
            for (const garmentSize of garment.sizes) {
                // Calcular cantidad disponible
                const usedInPackings = garmentSize._count.packingDetails;
                const availableQuantity =
                    garmentSize.totalQuantity - garmentSize.usedQuantity;

                if (availableQuantity > 0) {
                    // Determinar el número de partida si existe
                    const partNumber =
                        order.parts.length > 0
                            ? order.parts[0].code
                            : undefined;

                    // Calcular empaque sugerido
                    const boxesNeeded = Math.floor(
                        availableQuantity / defaultBoxCapacity,
                    );
                    const remainingAfterBoxes =
                        availableQuantity % defaultBoxCapacity;
                    const bagsNeeded = remainingAfterBoxes > 0 ? 1 : 0;

                    suggestedDetails.push({
                        garmentSizeId: garmentSize.id,
                        modelCode: garment.model.code,
                        colorName: garment.color.name,
                        partNumber: partNumber,
                        quantity: availableQuantity,
                        qualityType: "primera", // Por defecto primera calidad
                        packagingType: boxesNeeded > 0 ? "caja" : "bolsa",
                        packagingUnits:
                            boxesNeeded > 0 ? boxesNeeded : bagsNeeded,
                        piecesPerUnit:
                            boxesNeeded > 0
                                ? defaultBoxCapacity
                                : defaultBagCapacity,
                        loosePieces: boxesNeeded > 0 ? remainingAfterBoxes : 0,
                    });

                    totalQuantity += availableQuantity;
                }
            }
        }

        if (suggestedDetails.length === 0) {
            return {
                success: false,
                error: "No hay productos disponibles en esta orden para empacar",
            };
        }

        // Calcular estimados totales
        const estimatedBoxes = suggestedDetails.reduce(
            (sum, detail) =>
                sum +
                (detail.packagingType === "caja"
                    ? detail.packagingUnits || 0
                    : 0),
            0,
        );
        const estimatedBags = suggestedDetails.reduce(
            (sum, detail) =>
                sum +
                (detail.packagingType === "bolsa"
                    ? detail.packagingUnits || 0
                    : 0),
            0,
        );

        return {
            success: true,
            data: {
                order: {
                    id: order.id,
                    transferNumber: order.transferNumber,
                    cutOrder: order.cutOrder,
                    batch: order.batch,
                    customer: {
                        id: order.customer.id,
                        name: order.customer.name,
                    },
                },
                suggestedDetails,
                totalQuantity,
                estimatedBoxes,
                estimatedBags,
            },
        };
    } catch (error) {
        console.error("Error importing from order:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return { success: false, error: "Error al importar desde la orden" };
    }
}

// Función auxiliar para obtener órdenes disponibles para packing
export async function getOrdersForPacking(customerId?: string) {
    try {
        const where: any = {
            // Solo órdenes que no estén completamente empacadas
            garments: {
                some: {
                    sizes: {
                        some: {
                            totalQuantity: {
                                gt: db.garmentSize.fields.usedQuantity,
                            },
                        },
                    },
                },
            },
        };

        if (customerId) {
            where.customerId = customerId;
        }

        const orders = await db.order.findMany({
            where,
            include: {
                customer: true,
                status: true,
                _count: {
                    select: {
                        garments: true,
                        packings: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return orders.map((order) => ({
            id: order.id,
            label: `${order.cutOrder || order.transferNumber || order.id} - ${order.customer.name}`,
            transferNumber: order.transferNumber,
            cutOrder: order.cutOrder,
            batch: order.batch,
            customerName: order.customer.name,
            status: order.status.name,
            garmentCount: order._count.garments,
            packingCount: order._count.packings,
        }));
    } catch (error) {
        console.error("Error getting orders for packing:", error);

        return [];
    }
}
