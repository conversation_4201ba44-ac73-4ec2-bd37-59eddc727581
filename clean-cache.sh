#!/bin/bash
# Script para limpiar el caché de Next.js y resolver problemas de permisos

echo "🧹 Limpiando caché de Next.js..."

# Eliminar el directorio .next si existe
if [ -d ".next" ]; then
    echo "Eliminando directorio .next..."
    rm -rf .next
fi

# Eliminar node_modules/.cache si existe
if [ -d "node_modules/.cache" ]; then
    echo "Eliminando node_modules/.cache..."
    rm -rf node_modules/.cache
fi

echo "✅ Caché limpiado exitosamente"
echo "📝 Puedes ejecutar 'npm run dev' nuevamente"
