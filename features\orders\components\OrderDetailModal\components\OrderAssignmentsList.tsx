"use client";

import type { ExtendedOrder } from "../types/order-modal.types";

import React from "react";
import { Card, CardBody, Avatar, Chip, Progress } from "@heroui/react";
import {
    UserIcon,
    ClockIcon,
    CheckCircleIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { useThemeSafe, getThemeColors, formatDate } from "../utils";

import { RemissionLink } from "./RemissionLink";

interface OrderAssignmentsListProps {
    order: ExtendedOrder;
}

export default function OrderAssignmentsList({
    order,
}: OrderAssignmentsListProps) {
    const theme = useThemeSafe();
    const colors = getThemeColors(theme);

    const assignments = order.assignments || order.OrderAssignment || [];

    const assignmentsByContractor = assignments.reduce(
        (acc: any, assignment) => {
            const contractorId = assignment.contractor?.id || "unassigned";

            if (!acc[contractorId]) {
                acc[contractorId] = {
                    contractor: assignment.contractor,
                    assignments: [],
                    totalCompleted: 0,
                    totalPending: 0,
                };
            }

            acc[contractorId].assignments.push(assignment);
            if (assignment.isCompleted || assignment.completed) {
                acc[contractorId].totalCompleted++;
            } else {
                acc[contractorId].totalPending++;
            }

            return acc;
        },
        {},
    );

    const contractorGroups = Object.values(assignmentsByContractor);

    if (assignments.length === 0) {
        return (
            <Card className="border border-default-200">
                <CardBody className="p-8 text-center">
                    <UserGroupIcon className="w-12 h-12 mx-auto mb-4 text-default-300" />
                    <p className="text-default-500">
                        No hay asignaciones registradas
                    </p>
                </CardBody>
            </Card>
        );
    }

    return (
        <div className="space-y-4">
            {contractorGroups.map((group: any, groupIndex) => {
                const progress =
                    (group.totalCompleted /
                        (group.totalCompleted + group.totalPending)) *
                        100 || 0;

                return (
                    <motion.div
                        key={group.contractor?.id || "unassigned"}
                        animate={{ opacity: 1, y: 0 }}
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ delay: groupIndex * 0.1 }}
                    >
                        <Card className="border border-default-200">
                            <CardBody className="p-6">
                                {/* Contractor Header */}
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center gap-3">
                                        <Avatar
                                            fallback={
                                                <UserIcon className="w-4 h-4" />
                                            }
                                            name={
                                                group.contractor?.name ||
                                                "Sin asignar"
                                            }
                                            size="sm"
                                        />
                                        <div>
                                            <h4 className="font-semibold text-foreground">
                                                {group.contractor?.name ||
                                                    "Sin asignar"}
                                            </h4>
                                            <p className="text-xs text-default-500">
                                                {group.assignments.length}{" "}
                                                asignaciones
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {group.contractor && (
                                            <RemissionLink
                                                contractorId={
                                                    group.contractor.id
                                                }
                                                contractorName={
                                                    group.contractor.name
                                                }
                                                orderId={order.id}
                                            />
                                        )}
                                        <Chip
                                            color="success"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {group.totalCompleted} completadas
                                        </Chip>
                                        <Chip
                                            color="warning"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {group.totalPending} pendientes
                                        </Chip>
                                    </div>
                                </div>
                                {/* Progress */}
                                <Progress
                                    className="mb-4"
                                    color={
                                        progress === 100 ? "success" : "primary"
                                    }
                                    size="sm"
                                    value={progress}
                                />

                                {/* Assignments List */}
                                <div className="space-y-2">
                                    {group.assignments.map(
                                        (assignment: any, index: number) => (
                                            <div
                                                key={assignment.id}
                                                className="flex items-center justify-between p-3 bg-default-50 rounded-lg"
                                            >
                                                <div className="flex items-center gap-3">
                                                    {assignment.isCompleted ||
                                                    assignment.completed ? (
                                                        <CheckCircleIcon className="w-5 h-5 text-success" />
                                                    ) : (
                                                        <ClockIcon className="w-5 h-5 text-warning" />
                                                    )}
                                                    <div>
                                                        <p className="text-sm font-medium text-foreground">
                                                            {assignment
                                                                .garmentSize
                                                                ?.garment?.model
                                                                ?.description ||
                                                                assignment
                                                                    .orderGarmentPart
                                                                    ?.part
                                                                    ?.name ||
                                                                "Prenda sin nombre"}
                                                        </p>
                                                        <div className="flex items-center gap-2">
                                                            <p className="text-xs text-default-500">
                                                                Cantidad:{" "}
                                                                {assignment.quantity ||
                                                                    assignment
                                                                        .orderGarmentPart
                                                                        ?.quantity ||
                                                                    0}
                                                            </p>
                                                            {assignment
                                                                .garmentSize
                                                                ?.size
                                                                ?.code && (
                                                                <p className="text-xs text-default-500">
                                                                    • Talla:{" "}
                                                                    {
                                                                        assignment
                                                                            .garmentSize
                                                                            .size
                                                                            .code
                                                                    }
                                                                </p>
                                                            )}
                                                            {assignment.folio && (
                                                                <p className="text-xs text-default-500">
                                                                    • Folio:{" "}
                                                                    {
                                                                        assignment.folio
                                                                    }
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <p className="text-xs text-default-500">
                                                        {assignment.completedAt
                                                            ? `Completado: ${formatDate(assignment.completedAt)}`
                                                            : `Creado: ${formatDate(assignment.createdAt)}`}
                                                    </p>
                                                </div>
                                            </div>
                                        ),
                                    )}
                                </div>
                            </CardBody>
                        </Card>
                    </motion.div>
                );
            })}
        </div>
    );
}
