---
description: Visual Process Map para REFLECT Mode con métricas
globs: reflect-mode-map.mdc
alwaysApply: false
---
# 🔍 REFLECT MODE - VISUAL PROCESS MAP

> Modo de reflexión y aprendizaje con análisis de métricas

## 📊 MAPA DE PROCESO COMPLETO

```mermaid
graph TD
    Start["🔍 AUTO-FLOW REFLECT"] --> Verify{"✅ Implementación<br>Completa?"}
    
    Verify -->|No| Error["❌ Regresar a<br>IMPLEMENT"]
    Verify -->|Sí| Load["📂 Cargar Progress +<br>Historial"]
    
    Load --> Review["📊 Review General<br>🧠 metacognitivemonitoring"]
    
    Review --> Metrics["📈 Analizar M<PERSON>tricas"]
    Metrics --> Time["⏱️ Tiempo:<br>Estimado vs Real"]
    Metrics --> Quality["✨ Calidad:<br>Tests, Coverage"]
    Metrics --> Perf["⚡ Performance:<br>Benchmarks"]
    
    Time & Quality & Perf --> Compare["🔄 Comparar con Plan<br>Original"]
    
    Compare --> Success["✅ Documentar<br>Éxitos"]
    Compare --> Challenges["⚠️ Documentar<br>Desafíos"]
    
    Success & Challenges --> Lessons["💡 Lecciones Aprendidas<br>🧠 collaborativereasoning"]
    
    Lessons --> Patterns["🔮 Identificar<br>Patrones"]
    Patterns --> Improvements["📈 Mejoras para<br>Próxima vez"]
    
    Improvements --> Document["📄 Crear<br>reflect-[timestamp].md"]
    Document --> Update["💾 Update<br>Memory Bank"]
    
    Update --> Ready["✅ REFLECT Complete<br>→ ARCHIVE (opcional)"]
    
    %% Clear Thought Tools
    Review -.-> CT1["🧠 metacognitivemonitoring<br>Auto-evaluación"]
    Lessons -.-> CT2["🧠 collaborativereasoning<br>Múltiples perspectivas"]
    
    style Start fill:#4dbbbb
    style Verify fill:#d94dbb
    style Ready fill:#5fd94d
    style Error fill:#ff5555
```

## 📈 MÉTRICAS DE ANÁLISIS

### Métricas de Tiempo
```typescript
interface TimeMetrics {
    estimated: number;      // Horas estimadas
    actual: number;        // Horas reales
    variance: number;      // % variación
    byPhase: {
        [phase: string]: {
            estimated: number;
            actual: number;
        }
    };
}
```

### Métricas de Calidad
```typescript
interface QualityMetrics {
    testCoverage: number;          // % coverage
    testsTotal: number;
    testsPassed: number;
    lintIssues: number;
    typeErrors: number;
    codeComplexity: number;        // Cyclomatic complexity
    technicalDebt: number;         // Horas estimadas
}
```

### Métricas de Clear Thought
```typescript
interface ClearThoughtMetrics {
    toolsUsed: {
        [toolName: string]: {
            count: number;
            effectiveness: number;  // 1-10
            bestUseCase: string;
        }
    };
    mostEffective: string[];
    recommendations: string[];
}
```

## 📄 TEMPLATE REFLECT DOCUMENT

```markdown
# 🔍 Reflection Document - [Project/Feature Name]
**Date**: [ISO timestamp]
**Duration**: [Actual time] (Estimated: [estimated time])
**Complexity Level**: [1-4]

## 📊 Metrics Summary

### ⏱️ Time Analysis
- **Total Variance**: [+/-X%]
- **Most Accurate Phase**: [Phase name]
- **Least Accurate Phase**: [Phase name]

### ✨ Quality Metrics
- **Test Coverage**: [X%]
- **Tests**: [X/Y passed]
- **Code Health**: [Score]
- **Technical Debt**: [X hours]

### 🧠 Clear Thought Usage
- **Most Used Tool**: [tool] ([X] times)
- **Most Effective Tool**: [tool] (effectiveness: [X/10])
- **Tool Recommendations**: [List]

## ✅ What Went Well
1. **[Success 1]**
   - Description: [Details]
   - Impact: [Positive outcome]
   - Replicable pattern: [Pattern to repeat]

2. **[Success 2]**
   [Similar structure]

## ⚠️ Challenges Faced
1. **[Challenge 1]**
   - Description: [What happened]
   - Root cause: [Analysis]
   - Resolution: [How it was solved]
   - Prevention: [How to avoid next time]

2. **[Challenge 2]**
   [Similar structure]

## 💡 Key Lessons Learned

### Technical Lessons
- [Lesson 1]: [Application]
- [Lesson 2]: [Application]

### Process Lessons
- [Lesson 1]: [Application]
- [Lesson 2]: [Application]

### Tool Usage Lessons
- [Tool]: Best for [use case]
- [Tool]: Not effective for [use case]

## 🔮 Patterns Identified

### Positive Patterns (Keep)
- [Pattern 1]: [Why it works]
- [Pattern 2]: [Why it works]

### Negative Patterns (Avoid)
- [Pattern 1]: [Why it doesn't work]
- [Pattern 2]: [Why it doesn't work]

## 📈 Recommendations for Improvement

### For Similar Tasks
1. [Recommendation 1]
2. [Recommendation 2]

### For Process
1. [Process improvement 1]
2. [Process improvement 2]

### For Tool Usage
1. Use [tool] earlier for [benefit]
2. Combine [tool1] + [tool2] for [synergy]

## 🎯 Action Items for Next Time
- [ ] [Specific action 1]
- [ ] [Specific action 2]
- [ ] [Specific action 3]

---
**Next Step**: ARCHIVE mode (if Level 3-4) or new task
```

## 🧠 PROCESO DE REFLEXIÓN

```typescript
// Activar metacognitivemonitoring para auto-evaluación
const selfAssessment = await evaluateWithTool('metacognitivemonitoring', {
    task: projectDescription,
    knowledgeAssessment: {
        domain: projectDomain,
        knowledgeLevel: assessKnowledgeGrowth(),
        confidenceScore: calculateConfidence(),
        knownLimitations: identifyGaps()
    },
    overallConfidence: calculateOverallSuccess(),
    uncertaintyAreas: identifyAreasForImprovement()
});

// Activar collaborativereasoning para perspectivas múltiples
const perspectives = await analyzeWithTool('collaborativereasoning', {
    topic: 'Project retrospective',
    personas: [
        { id: 'developer', perspective: 'Technical execution' },
        { id: 'architect', perspective: 'Design decisions' },
        { id: 'user', perspective: 'End result usability' }
    ],
    contributions: gatherMultiplePerspectives(),
    consensusPoints: identifyAgreements(),
    disagreements: identifyDisagreements()
});
```

## 💾 MEMORY BANK UPDATES

```typescript
// Crear documento de reflexión
saveReflection({
    filename: `reflect-${timestamp}.md`,
    metrics: {
        time: timeMetrics,
        quality: qualityMetrics,
        clearThought: clearThoughtMetrics
    },
    lessons: consolidatedLessons,
    patterns: identifiedPatterns,
    recommendations: futureRecommendations
});

// Actualizar patrones aprendidos
updateLearnedPatterns({
    positivePatterns: patternsToKeep,
    negativePatterns: patternsToAvoid,
    toolEffectiveness: toolMetrics
});

// Actualizar tasks.md
markTaskComplete({
    taskId: currentTask.id,
    completionTime: actualTime,
    reflectionDoc: reflectionFilename,
    nextRecommendedAction: determineNextStep()
});
```

## ✅ CHECKLIST DE VERIFICACIÓN

- [ ] Implementación verificada como completa
- [ ] Métricas de tiempo analizadas
- [ ] Métricas de calidad evaluadas
- [ ] Uso de Clear Thought tools analizado
- [ ] Éxitos documentados con patrones
- [ ] Desafíos analizados con root causes
- [ ] Lecciones aprendidas extraídas
- [ ] Patrones positivos/negativos identificados
- [ ] Recomendaciones para mejora creadas
- [ ] reflect-*.md documento guardado
- [ ] Memory Bank actualizado
- [ ] Listo para ARCHIVE o nueva tarea

---

**REFLECT Mode completo - Aprendizajes capturados**
