// Clear Thought Selector for AUTO-FLOW V5.1
import { 
  ClearThoughtTool, 
  ClearThoughtConfig, 
  ToolMatch, 
  ToolSelection,
  ToolMetric 
} from './clear-thought-types';
import { create_entities, search_nodes } from '../mcp-tools';

export class ClearThoughtSelector {
  private config: ClearThoughtConfig;
  private metricsCache: Map<string, ToolMetric[]> = new Map();

  constructor(config: ClearThoughtConfig) {
    this.config = config;
    this.loadMetricsCache();
  }

  /**
   * Analiza el comando y extrae keywords relevantes
   */
  analyzeCommand(command: string): string[] {
    // Normalizar comando
    const normalized = command.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Extraer palabras clave
    const words = normalized.split(' ');
    const keywords: string[] = [];

    // Buscar keywords de 1-3 palabras
    for (let i = 0; i < words.length; i++) {
      // Single word
      keywords.push(words[i]);
      
      // Two words
      if (i < words.length - 1) {
        keywords.push(`${words[i]} ${words[i + 1]}`);
      }
      
      // Three words (para "pros and cons", etc)
      if (i < words.length - 2) {
        keywords.push(`${words[i]} ${words[i + 1]} ${words[i + 2]}`);
      }
    }

    return keywords.filter(k => k.length > 2); // Filtrar palabras muy cortas
  }

  /**
   * Encuentra herramientas que coincidan con los keywords
   */
  matchTool(keywords: string[]): ToolMatch[] {
    const matches: ToolMatch[] = [];

    // Revisar cada herramienta
    for (const [toolName, toolConfig] of Object.entries(this.config.tools)) {
      const matchedTriggers: string[] = [];
      
      // Buscar coincidencias con triggers
      for (const trigger of toolConfig.triggers) {
        for (const keyword of keywords) {
          if (trigger.includes(keyword) || keyword.includes(trigger)) {
            matchedTriggers.push(trigger);
          }
        }
      }

      if (matchedTriggers.length > 0) {
        // Calcular confianza basada en coincidencias y peso
        const baseConfidence = matchedTriggers.length / toolConfig.triggers.length;
        const weightedConfidence = baseConfidence * toolConfig.weight;
        
        // Ajustar por métricas históricas
        const historicalBoost = this.getHistoricalBoost(toolName as ClearThoughtTool);
        const finalConfidence = Math.min(weightedConfidence + historicalBoost, 1.0);

        matches.push({
          tool: toolName as ClearThoughtTool,
          confidence: finalConfidence,
          matchedTriggers: [...new Set(matchedTriggers)],
          context: this.determineContext(keywords, toolConfig.contexts)
        });
      }
    }

    // Ordenar por confianza descendente
    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Selecciona la mejor herramienta basándose en confianza
   */
  selectBestTool(command: string): ToolSelection {
    const keywords = this.analyzeCommand(command);
    const matches = this.matchTool(keywords);

    // Si no hay matches o confianza muy baja, usar fallback
    if (matches.length === 0 || matches[0].confidence < this.config.confidence.threshold) {
      return {
        selectedTool: this.config.fallbackTool,
        confidence: 0.5,
        reason: `No clear match found. Using fallback tool: ${this.config.fallbackTool}`,
        alternatives: matches
      };
    }

    // Si hay múltiples herramientas con confianza similar
    if (matches.length > 1 && matches[1].confidence >= this.config.confidence.multiToolThreshold) {
      // Podríamos usar múltiples herramientas
      const multiToolReason = `Multiple tools applicable: ${matches.slice(0, 3).map(m => m.tool).join(', ')}`;
      
      return {
        selectedTool: matches[0].tool,
        confidence: matches[0].confidence,
        reason: `${multiToolReason}. Selected ${matches[0].tool} (highest confidence: ${(matches[0].confidence * 100).toFixed(0)}%)`,
        alternatives: matches.slice(1, 4)
      };
    }

    // Selección clara
    return {
      selectedTool: matches[0].tool,
      confidence: matches[0].confidence,
      reason: `Clear match: ${matches[0].tool} (${(matches[0].confidence * 100).toFixed(0)}% confidence) - matched triggers: ${matches[0].matchedTriggers.join(', ')}`,
      alternatives: matches.slice(1, 3)
    };
  }

  /**
   * Obtiene recomendación con explicación
   */
  async getToolRecommendation(command: string): Promise<ToolSelection> {
    const startTime = Date.now();
    const selection = this.selectBestTool(command);
    const elapsedTime = Date.now() - startTime;

    // Verificar performance
    if (elapsedTime > 100) {
      console.warn(`⚠️ Clear Thought selection took ${elapsedTime}ms (target: <100ms)`);
    }

    // Log para debugging
    console.log(`🧠 Clear Thought Selection:`);
    console.log(`   Command: "${command}"`);
    console.log(`   Selected: ${selection.selectedTool}`);
    console.log(`   Confidence: ${(selection.confidence * 100).toFixed(0)}%`);
    console.log(`   Reason: ${selection.reason}`);
    
    if (selection.alternatives.length > 0) {
      console.log(`   Alternatives: ${selection.alternatives.map(a => `${a.tool} (${(a.confidence * 100).toFixed(0)}%)`).join(', ')}`);
    }

    return selection;
  }

  /**
   * Determina el contexto basado en keywords
   */
  private determineContext(keywords: string[], availableContexts: string[]): string {
    // Simple: retornar el primer contexto disponible
    // Podría ser más sofisticado en el futuro
    return availableContexts[0] || 'general';
  }

  /**
   * Obtiene boost de confianza basado en métricas históricas
   */
  private getHistoricalBoost(tool: ClearThoughtTool): number {
    const metrics = this.metricsCache.get(tool) || [];
    if (metrics.length === 0) return 0;

    // Calcular tasa de éxito reciente
    const recentMetrics = metrics.slice(-10); // Últimas 10 uses
    const successRate = recentMetrics.filter(m => m.success).length / recentMetrics.length;
    
    // Boost máximo de 0.1 basado en éxito histórico
    return successRate * 0.1;
  }

  /**
   * Carga métricas desde Memento
   */
  private async loadMetricsCache(): Promise<void> {
    try {
      const metrics = await search_nodes({
        query: 'CLEAR_THOUGHT_METRIC'
      });

      metrics.entities.forEach(entity => {
        try {
          const metric = JSON.parse(entity.observations[0]) as ToolMetric;
          const toolMetrics = this.metricsCache.get(metric.tool) || [];
          toolMetrics.push(metric);
          this.metricsCache.set(metric.tool, toolMetrics);
        } catch (e) {
          // Ignorar métricas mal formadas
        }
      });
    } catch (error) {
      console.warn('Could not load Clear Thought metrics:', error);
    }
  }

  /**
   * Guarda métrica de uso en Memento
   */
  async saveMetric(metric: ToolMetric): Promise<void> {
    try {
      await create_entities([{
        name: `CLEAR_THOUGHT_METRIC_${metric.tool}_${metric.timestamp}`,
        entityType: 'CLEAR_THOUGHT_METRIC',
        observations: [
          JSON.stringify(metric),
          `Tool: ${metric.tool}`,
          `Success: ${metric.success}`,
          `Time: ${metric.timeSpent}ms`,
          `Context: ${metric.context}`
        ]
      }]);

      // Actualizar cache
      const toolMetrics = this.metricsCache.get(metric.tool) || [];
      toolMetrics.push(metric);
      this.metricsCache.set(metric.tool, toolMetrics);
    } catch (error) {
      console.error('Error saving Clear Thought metric:', error);
    }
  }
}

// Singleton instance (se inicializa con config cuando se carga)
let selector: ClearThoughtSelector | null = null;

export function initializeClearThoughtSelector(config: ClearThoughtConfig): ClearThoughtSelector {
  selector = new ClearThoughtSelector(config);
  return selector;
}

export function getClearThoughtSelector(): ClearThoughtSelector {
  if (!selector) {
    throw new Error('Clear Thought Selector not initialized. Call initializeClearThoughtSelector first.');
  }
  return selector;
}