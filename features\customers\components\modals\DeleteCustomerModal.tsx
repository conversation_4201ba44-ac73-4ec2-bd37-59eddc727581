"use client";

import { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Button,
    Avatar,
} from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";
import {
    TrashIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { addToast } from "@heroui/react";

import { Customer } from "@/features/customers/types";

interface DeleteCustomerModalProps {
    isOpen: boolean;
    onClose: () => void;
    customer: Customer | null;
    onConfirm: (customerId: string) => Promise<void>;
}

export function DeleteCustomerModal({
    isOpen,
    onClose,
    customer,
    onConfirm,
}: DeleteCustomerModalProps) {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
        if (!customer) return;

        setIsDeleting(true);
        try {
            await onConfirm(customer.id);
            addToast({
                title: "Cliente eliminado",
                description: `${customer.name} ha sido eliminado exitosamente`,
                color: "success",
            });
            onClose();
        } catch (error) {
            addToast({
                title: "Error al eliminar",
                description: "No se pudo eliminar el cliente",
                color: "danger",
            });
            console.error("Error deleting customer:", error);
        } finally {
            setIsDeleting(false);
        }
    };

    if (!customer) return null;

    return (
        <Modal
            backdrop="blur"
            classNames={{
                base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                header: "border-b border-gray-200 dark:border-gray-700",
                body: "py-6",
                footer: "border-t border-gray-200 dark:border-gray-700",
            }}
            isOpen={isOpen}
            placement="center"
            onClose={onClose}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">
                            <motion.div
                                animate={{ scale: 1, opacity: 1 }}
                                className="flex items-center gap-3"
                                initial={{ scale: 0.8, opacity: 0 }}
                            >
                                <div className="bg-red-500/20 p-2 rounded-lg">
                                    <ExclamationTriangleIcon className="w-6 h-6 text-red-500" />
                                </div>
                                <h3 className="text-xl font-semibold">
                                    Confirmar Eliminación
                                </h3>
                            </motion.div>
                        </ModalHeader>

                        <ModalBody>
                            <AnimatePresence mode="wait">
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className="space-y-4"
                                    exit={{ opacity: 0, y: -10 }}
                                    initial={{ opacity: 0, y: 10 }}
                                >
                                    <p className="text-gray-600">
                                        ¿Estás seguro de que deseas eliminar
                                        este cliente?
                                    </p>

                                    <motion.div
                                        animate={{ scale: 1 }}
                                        className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 p-4 rounded-lg border border-purple-500/20"
                                        initial={{ scale: 0.95 }}
                                    >
                                        <div className="flex items-center gap-4">
                                            <Avatar
                                                className="bg-gradient-to-br from-purple-500 to-blue-600"
                                                name={customer.name}
                                                size="lg"
                                            />
                                            <div className="flex-1">
                                                <h4 className="font-semibold text-lg">
                                                    {customer.name}
                                                </h4>
                                                <div className="flex flex-wrap gap-2 mt-1">
                                                    {customer.email && (
                                                        <span className="text-sm text-gray-500">
                                                            {customer.email}
                                                        </span>
                                                    )}
                                                    {customer.phone && (
                                                        <span className="text-sm text-gray-500">
                                                            • {customer.phone}
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>

                                    <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                                        <div className="flex items-start gap-2">
                                            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                                            <div className="text-sm text-red-700">
                                                <p className="font-medium">
                                                    Esta acción no se puede
                                                    deshacer
                                                </p>
                                                <p className="mt-1">
                                                    Se eliminarán todos los
                                                    datos asociados a este
                                                    cliente, incluyendo sus
                                                    órdenes y asignaciones.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        </ModalBody>

                        <ModalFooter>
                            <Button
                                isDisabled={isDeleting}
                                variant="light"
                                onPress={onClose}
                            >
                                Cancelar
                            </Button>
                            <Button
                                className="bg-gradient-to-r from-red-500 to-red-600"
                                color="danger"
                                isLoading={isDeleting}
                                startContent={
                                    !isDeleting && (
                                        <TrashIcon className="w-4 h-4" />
                                    )
                                }
                                variant="shadow"
                                onPress={handleDelete}
                            >
                                {isDeleting
                                    ? "Eliminando..."
                                    : "Eliminar Cliente"}
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
