// Tipos compartidos para roles en el sistema LOHARI
export const ROLE_NAMES = ["ADMIN", "EMPL<PERSON>YEE", "CONTRACTOR", "GUEST"] as const;
export type RoleName = (typeof ROLE_NAMES)[number];

// Mapa de configuración de roles
export const ROLE_CONFIG: Record<
    RoleName,
    {
        iconName: string;
        color: string;
        label: string;
    }
> = {
    ADMIN: {
        iconName: "ShieldCheckIcon",
        color: "#EF4444",
        label: "Administrador",
    },
    EMPLOYEE: {
        iconName: "UserIcon",
        color: "#3B82F6",
        label: "Empleado",
    },
    CONTRACTOR: {
        iconName: "BriefcaseIcon",
        color: "#10B981",
        label: "Contratista",
    },
    GUEST: {
        iconName: "UserCircleIcon",
        color: "#9CA3AF",
        label: "Invitado",
    },
};

// Helper para validar si un string es un RoleName válido
export function isValidRoleName(role: string): role is RoleName {
    return ROLE_NAMES.includes(role as RoleName);
}

// Helper para obtener el label de un rol
export function getRoleLabel(roleName: RoleName): string {
    return ROLE_CONFIG[roleName].label;
}

// Helper para obtener la configuración completa de un rol
export function getRoleConfig(roleName: RoleName) {
    return ROLE_CONFIG[roleName];
}
