"use client";

import React from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Button,
    Input,
    Divider,
} from "@heroui/react";
import { motion } from "framer-motion";
import {
    CubeIcon,
    MinusIcon,
    PlusIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { Tooltip } from "@heroui/react";

interface OrderAssignment {
    id: string;
    quantity: number;
    garmentSize: {
        size: {
            code: string;
        };
        garment: {
            model: {
                code: string;
            };
            color: {
                name: string;
            };
        };
    };
}

interface OrderGroup {
    orderId: string;
    cutOrder: string;
    parts: Array<{ id: string; code: string }>;
    assignments: OrderAssignment[];
}

interface SizeGroup {
    sizeCode: string;
    assignments: OrderAssignment[];
    totalQuantity: number;
    colors: string[];
}

interface RemissionOrderItemsProps {
    orderGroups: OrderGroup[];
    showRowAnimations?: boolean;
    onQuantityChange?: (
        modelCode: string,
        colorName: string,
        sizeCode: string,
        newQuantity: number,
    ) => void;
    readOnly?: boolean;
}

export default function RemissionOrderItems({
    orderGroups,
    showRowAnimations = true,
    onQuantityChange,
    readOnly = true,
}: RemissionOrderItemsProps) {
    const springAnimation = {
        type: "spring",
        stiffness: 300,
        damping: 30,
    };

    const renderOrderCard = (orderGroup: OrderGroup, index: number) => {
        // Validate order data
        if (!orderGroup.assignments || orderGroup.assignments.length === 0) {
            return null;
        }

        // Get unique models from all assignments in this order
        const models = Array.from(
            new Set(
                orderGroup.assignments
                    .filter((a) => a.garmentSize?.garment?.model?.code)
                    .map((a) => a.garmentSize.garment.model.code),
            ),
        );

        // Get unique colors from all assignments in this order
        const colors = Array.from(
            new Set(
                orderGroup.assignments
                    .filter((a) => a.garmentSize?.garment?.color?.name)
                    .map((a) => a.garmentSize.garment.color.name),
            ),
        );

        // Group assignments by size for the table
        const sizeGroups = orderGroup.assignments.reduce(
            (acc, assignment) => {
                const sizeCode = assignment.garmentSize?.size?.code;
                const colorName = assignment.garmentSize?.garment?.color?.name;

                if (!sizeCode) return acc;

                if (!acc[sizeCode]) {
                    acc[sizeCode] = {
                        sizeCode,
                        assignments: [],
                        totalQuantity: 0,
                        colors: [],
                    };
                }

                acc[sizeCode].assignments.push(assignment);
                acc[sizeCode].totalQuantity += assignment.quantity || 0;

                if (colorName && !acc[sizeCode].colors.includes(colorName)) {
                    acc[sizeCode].colors.push(colorName);
                }

                return acc;
            },
            {} as Record<string, SizeGroup>,
        );

        const sizeGroupsArray = Object.values(sizeGroups);

        const totalOrderQuantity = orderGroup.assignments.reduce(
            (sum, a) => sum + a.quantity,
            0,
        );

        // Define columns for the table
        const columns = readOnly
            ? [
                  { key: "size", label: "TALLA" },
                  { key: "total", label: "CANTIDAD" },
              ]
            : [
                  { key: "size", label: "TALLA" },
                  { key: "quantity", label: "ASIGNAR" },
                  { key: "total", label: "TOTAL" },
                  { key: "available", label: "DISPONIBLE" },
              ];

        // Render cell content based on column key
        const renderCell = (sizeGroup: SizeGroup, columnKey: React.Key) => {
            switch (columnKey) {
                case "size":
                    return (
                        <Chip
                            className="bg-default/40 text-default-700"
                            size="sm"
                            variant="flat"
                        >
                            {sizeGroup.sizeCode}
                        </Chip>
                    );
                case "quantity":
                    if (readOnly) {
                        return (
                            <div className="text-center font-medium">
                                {sizeGroup.totalQuantity}
                            </div>
                        );
                    }

                    return (
                        <div className="flex items-center gap-1 justify-center">
                            <Button
                                isIconOnly
                                className="w-6 h-6 min-w-6"
                                size="sm"
                                variant="flat"
                                onPress={() => {
                                    if (
                                        !onQuantityChange ||
                                        sizeGroup.totalQuantity <= 0
                                    )
                                        return;
                                    const assignment = sizeGroup.assignments[0];
                                    const modelCode =
                                        assignment.garmentSize?.garment?.model
                                            ?.code || "";
                                    const colorName =
                                        assignment.garmentSize?.garment?.color
                                            ?.name || "";
                                    const sizeCode =
                                        assignment.garmentSize?.size?.code ||
                                        "";

                                    onQuantityChange(
                                        modelCode,
                                        colorName,
                                        sizeCode,
                                        Math.max(
                                            0,
                                            sizeGroup.totalQuantity - 1,
                                        ),
                                    );
                                }}
                            >
                                <MinusIcon className="w-3 h-3" />
                            </Button>
                            <Input
                                classNames={{
                                    base: "w-20",
                                    input: "text-center",
                                    inputWrapper: "h-8 min-h-8",
                                }}
                                min={0}
                                type="number"
                                value={String(sizeGroup.totalQuantity)}
                                onChange={(e) => {
                                    if (!onQuantityChange) return;
                                    const newValue =
                                        parseInt(e.target.value) || 0;

                                    if (newValue < 0) return;
                                    const assignment = sizeGroup.assignments[0];
                                    const modelCode =
                                        assignment.garmentSize?.garment?.model
                                            ?.code || "";
                                    const colorName =
                                        assignment.garmentSize?.garment?.color
                                            ?.name || "";
                                    const sizeCode =
                                        assignment.garmentSize?.size?.code ||
                                        "";

                                    onQuantityChange(
                                        modelCode,
                                        colorName,
                                        sizeCode,
                                        newValue,
                                    );
                                }}
                            />
                            <Button
                                isIconOnly
                                className="w-6 h-6 min-w-6"
                                size="sm"
                                variant="flat"
                                onPress={() => {
                                    if (!onQuantityChange) return;
                                    const assignment = sizeGroup.assignments[0];
                                    const modelCode =
                                        assignment.garmentSize?.garment?.model
                                            ?.code || "";
                                    const colorName =
                                        assignment.garmentSize?.garment?.color
                                            ?.name || "";
                                    const sizeCode =
                                        assignment.garmentSize?.size?.code ||
                                        "";

                                    onQuantityChange(
                                        modelCode,
                                        colorName,
                                        sizeCode,
                                        sizeGroup.totalQuantity + 1,
                                    );
                                }}
                            >
                                <PlusIcon className="w-3 h-3" />
                            </Button>
                            <Button
                                className="h-6 text-xs px-2"
                                size="sm"
                                variant="flat"
                                onPress={() => {
                                    if (!onQuantityChange) return;
                                    const assignment = sizeGroup.assignments[0];
                                    const modelCode =
                                        assignment.garmentSize?.garment?.model
                                            ?.code || "";
                                    const colorName =
                                        assignment.garmentSize?.garment?.color
                                            ?.name || "";
                                    const sizeCode =
                                        assignment.garmentSize?.size?.code ||
                                        "";

                                    // Set to max available quantity (assignment original quantity)
                                    onQuantityChange(
                                        modelCode,
                                        colorName,
                                        sizeCode,
                                        assignment.quantity,
                                    );
                                }}
                            >
                                Max
                            </Button>
                        </div>
                    );
                case "total":
                    return (
                        <div className="text-center font-medium">
                            {sizeGroup.totalQuantity}
                        </div>
                    );
                case "available":
                    return (
                        <div className="text-center">
                            <span className="text-success">
                                {sizeGroup.totalQuantity}
                            </span>
                        </div>
                    );
                default:
                    return null;
            }
        };

        return (
            <motion.div
                key={orderGroup.orderId}
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                transition={{ ...springAnimation, delay: index * 0.1 }}
            >
                <Card className="hover:shadow-lg transition-shadow">
                    <CardHeader className="flex flex-col gap-3 p-4">
                        <div className="flex items-start justify-between gap-2 w-full">
                            <div className="flex-1">
                                <div className="flex items-center gap-2 flex-wrap">
                                    <h4 className="font-semibold text-lg">
                                        OT:{" "}
                                        {orderGroup.cutOrder ||
                                            `ID-${orderGroup.orderId.slice(-6)}`}
                                    </h4>
                                    {orderGroup.parts &&
                                        orderGroup.parts.length > 0 && (
                                            <>
                                                <span className="text-default-400">
                                                    •
                                                </span>
                                                {/* Show parts (partidas) */}
                                                {orderGroup.parts.map(
                                                    (part, partIndex) => (
                                                        <Tooltip
                                                            key={`${part.id}-${partIndex}`}
                                                            content={`Partida: ${part.code}`}
                                                            placement="top"
                                                        >
                                                            <Chip
                                                                classNames={{
                                                                    base: "bg-secondary/20 text-secondary-600 cursor-help",
                                                                }}
                                                                color="secondary"
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {part.code}
                                                            </Chip>
                                                        </Tooltip>
                                                    ),
                                                )}
                                            </>
                                        )}
                                </div>
                                <p className="text-sm text-default-500 mt-1">
                                    Modelo{models.length > 1 ? "s" : ""}:{" "}
                                    {models.join(", ") || "Sin modelo"}
                                </p>
                                <p className="text-sm text-default-500">
                                    Color{colors.length > 1 ? "es" : ""}:{" "}
                                    {colors.join(", ") || "Sin color"}
                                </p>
                            </div>
                            <div className="relative inline-flex shrink-0">
                                <CubeIcon className="w-5 h-5 text-default-400" />
                                <span className="flex z-10 flex-wrap absolute box-border rounded-full whitespace-nowrap place-content-center origin-center items-center select-none font-regular scale-100 opacity-100 subpixel-antialiased text-tiny px-0 transition-transform-opacity !ease-soft-spring !duration-300 border-2 border-background bg-primary text-primary-foreground w-4 h-4 min-w-4 min-h-4 top-[5%] right-[5%] translate-x-1/2 -translate-y-1/2">
                                    {Object.keys(sizeGroups).length}
                                </span>
                            </div>
                        </div>
                    </CardHeader>
                    <Divider />
                    <CardBody className="p-4">
                        <Table
                            aria-label="Tabla de asignación de cantidades"
                            classNames={{
                                wrapper: "shadow-none p-0",
                            }}
                        >
                            <TableHeader columns={columns}>
                                {(column) => (
                                    <TableColumn
                                        key={column.key}
                                        align={
                                            column.key === "size"
                                                ? "start"
                                                : "center"
                                        }
                                        className={
                                            column.key === "quantity" &&
                                            !readOnly
                                                ? "w-48"
                                                : ""
                                        }
                                    >
                                        {column.label}
                                    </TableColumn>
                                )}
                            </TableHeader>
                            <TableBody items={sizeGroupsArray}>
                                {(sizeGroup) => (
                                    <TableRow key={sizeGroup.sizeCode}>
                                        {(columnKey) => (
                                            <TableCell>
                                                {renderCell(
                                                    sizeGroup,
                                                    columnKey,
                                                )}
                                            </TableCell>
                                        )}
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                        <div className="flex justify-between items-center pt-3 mt-3 border-t border-default-200">
                            <span className="text-xs text-default-500">
                                {Object.keys(sizeGroups).length} tallas
                            </span>
                            <span className="text-sm">
                                Total:{" "}
                                <span className="font-medium text-primary">
                                    {totalOrderQuantity}
                                </span>
                                <span className="text-default-400">
                                    {" "}
                                    prendas
                                </span>
                            </span>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>
        );
    };

    const totalItems = React.useMemo(
        () =>
            orderGroups.reduce(
                (sum, group) =>
                    sum +
                    group.assignments.reduce(
                        (groupSum, a) => groupSum + (a.quantity || 0),
                        0,
                    ),
                0,
            ),
        [orderGroups],
    );

    if (!orderGroups || orderGroups.length === 0) {
        return (
            <Card className="p-8 text-center">
                <CardBody>
                    <ExclamationTriangleIcon className="w-12 h-12 mx-auto mb-4 text-warning" />
                    <p className="text-default-500">
                        No hay órdenes para mostrar
                    </p>
                </CardBody>
            </Card>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                    Items a remitir por orden
                </h3>
                <div className="flex gap-2">
                    <Chip color="primary" variant="flat">
                        {orderGroups.length}{" "}
                        {orderGroups.length === 1 ? "orden" : "órdenes"}
                    </Chip>
                    <Chip color="success" variant="flat">
                        {totalItems} {totalItems === 1 ? "prenda" : "prendas"}
                    </Chip>
                </div>
            </div>
            {orderGroups.map((orderGroup, index) =>
                renderOrderCard(orderGroup, index),
            )}
        </div>
    );
}
