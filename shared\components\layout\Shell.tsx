"use client";

import React from "react";

/**
 * Componente Shell para el dashboard
 * Contiene la estructura principal de las páginas del dashboard
 */
export function DashboardShell({ children }: { children: React.ReactNode }) {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="max-w-[1600px] mx-auto px-6 sm:px-8 lg:px-12 py-8 lg:py-12">
                <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                    <div className="p-8 sm:p-10 lg:p-12 xl:p-16">
                        {children}
                    </div>
                </div>
            </div>
        </div>
    );
}
