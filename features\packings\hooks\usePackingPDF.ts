"use client";

import { useState } from "react";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { addToast } from "@heroui/react";

import { markPackingAsPrinted } from "../actions";

export function usePackingPDF() {
    const [isGenerating, setIsGenerating] = useState(false);

    const generatePDF = async (packingId: string, packingFolio: string) => {
        setIsGenerating(true);
        console.log(
            "Starting PDF generation for packing:",
            packingId,
            packingFolio,
        );

        try {
            // Buscar el elemento del documento
            const element = document.getElementById(
                `packing-document-${packingId}`,
            );

            console.log(
                "Looking for element with id:",
                `packing-document-${packingId}`,
            );
            console.log("Element found:", !!element);

            if (!element) {
                console.error(
                    "Document element not found for ID:",
                    `packing-document-${packingId}`,
                );
                throw new Error(
                    "No se encontró el documento para generar el PDF",
                );
            }

            // Configuración para html2canvas
            console.log("Starting html2canvas conversion...");
            const canvas = await html2canvas(element, {
                scale: 2,
                logging: true, // Enable logging to see html2canvas errors
                useCORS: true,
                backgroundColor: "#ffffff",
            });

            console.log("Canvas generated successfully");

            // Crear PDF en orientación vertical (portrait)
            const pdf = new jsPDF({
                orientation: "portrait",
                unit: "mm",
                format: "letter",
            });

            // Calcular dimensiones
            const imgWidth = 210; // Ancho de página carta en mm
            const imgHeight = (canvas.height * imgWidth) / canvas.width;

            // Agregar imagen al PDF
            const imgData = canvas.toDataURL("image/png");

            pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);

            // Guardar el PDF
            pdf.save(`packing-${packingFolio}.pdf`);

            // Marcar como impreso
            console.log("Marking packing as printed...");
            const markResult = await markPackingAsPrinted(packingId);

            console.log("Mark as printed result:", markResult);

            if (!markResult.success) {
                console.error("Failed to mark as printed:", markResult.error);
                throw new Error(
                    markResult.error || "Error al marcar como impreso",
                );
            }

            addToast({
                title: "PDF generado",
                description: "El documento se ha descargado correctamente",
                color: "success",
            });

            return true;
        } catch (error) {
            console.error("Error generating PDF:", error);
            const errorMessage =
                error instanceof Error ? error.message : "Error desconocido";

            console.error("Error details:", errorMessage);

            addToast({
                title: "Error al generar PDF",
                description:
                    errorMessage || "No se pudo generar el documento PDF",
                color: "danger",
            });

            return false;
        } finally {
            setIsGenerating(false);
        }
    };

    const printPacking = async (packingId: string) => {
        try {
            // Buscar el elemento del documento
            const element = document.getElementById(
                `packing-document-${packingId}`,
            );

            if (!element) {
                throw new Error("No se encontró el documento para imprimir");
            }

            // Crear una ventana de impresión
            const printWindow = window.open("", "_blank");

            if (!printWindow) {
                throw new Error("No se pudo abrir la ventana de impresión");
            }

            // Clonar el contenido
            const clonedElement = element.cloneNode(true) as HTMLElement;

            // Escribir el contenido en la nueva ventana
            printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Imprimir Packing</title>
            <style>
              @media print {
                body { margin: 0; }
                @page { size: letter portrait; margin: 10mm; }
              }
              * { 
                font-family: system-ui, -apple-system, sans-serif; 
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              table { border-collapse: collapse; width: 100%; }
              th, td { border: 1px solid #ddd; padding: 8px; }
              th { background-color: #f3f4f6; font-weight: 600; }
              .bg-gray-50 { background-color: #f9fafb; }
              .bg-gray-100 { background-color: #f3f4f6; }
              .text-center { text-align: center; }
              .text-right { text-align: right; }
              .text-left { text-align: left; }
              .font-bold { font-weight: 700; }
              .font-semibold { font-weight: 600; }
              .font-medium { font-weight: 500; }
              .text-sm { font-size: 0.875rem; }
              .text-xs { font-size: 0.75rem; }
              .text-lg { font-size: 1.125rem; }
              .text-2xl { font-size: 1.5rem; }
            </style>
          </head>
          <body>
            ${clonedElement.outerHTML}
          </body>
        </html>
      `);

            printWindow.document.close();

            // Esperar a que se cargue y luego imprimir
            printWindow.onload = async () => {
                printWindow.print();
                printWindow.onafterprint = () => {
                    printWindow.close();
                };

                // Marcar como impreso
                await markPackingAsPrinted(packingId);
            };

            addToast({
                title: "Enviado a imprimir",
                description: "El documento se ha enviado a la impresora",
                color: "success",
            });

            return true;
        } catch (error) {
            console.error("Error printing:", error);
            addToast({
                title: "Error al imprimir",
                description: "No se pudo imprimir el documento",
                color: "danger",
            });

            return false;
        }
    };

    return {
        generatePDF,
        printPacking,
        isGenerating,
    };
}
