"use client";

import React, { useState, useMemo, useRef, useEffect } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";

import {
    Select,
    SelectItem,
    Input,
} from "@/shared/components/ui/hero-ui-client";

interface AutocompleteSelectProps {
    label: React.ReactNode;
    placeholder?: string;
    items: Array<{ id: string; name: string }>;
    selectedKey?: string;
    onSelectionChange?: (key: string) => void;
    startContent?: React.ReactNode;
    errorMessage?: string;
    variant?: "bordered" | "flat" | "faded" | "underlined";
    isRequired?: boolean;
    isDisabled?: boolean;
}

export function AutocompleteSelect({
    label,
    placeholder = "Buscar...",
    items,
    selectedKey,
    onSelectionChange,
    startContent,
    errorMessage,
    variant = "bordered",
    isRequired = false,
    isDisabled = false,
}: AutocompleteSelectProps) {
    const [searchValue, setSearchValue] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);

    // Filter items based on search
    const filteredItems = useMemo(() => {
        if (!searchValue) return items;

        const searchLower = searchValue.toLowerCase();

        return items.filter((item) =>
            item.name.toLowerCase().includes(searchLower),
        );
    }, [items, searchValue]);

    // Get selected item name
    const selectedItem = items.find((item) => item.id === selectedKey);

    // Reset search when closing
    useEffect(() => {
        if (!isOpen) {
            setSearchValue("");
        }
    }, [isOpen]);

    return (
        <div className="relative">
            <Select
                errorMessage={errorMessage}
                isDisabled={isDisabled}
                isRequired={isRequired}
                label={label}
                placeholder={placeholder}
                renderValue={() => selectedItem?.name || ""}
                selectedKeys={selectedKey ? [selectedKey] : []}
                startContent={startContent}
                variant={variant}
                onChange={(e) => onSelectionChange?.(e.target.value)}
                onOpenChange={setIsOpen}
            >
                <SelectItem
                    key="search"
                    className="sticky top-0 bg-white dark:bg-gray-900 border-b"
                    textValue=""
                >
                    <Input
                        ref={inputRef}
                        classNames={{
                            input: "text-sm",
                            inputWrapper: "h-9",
                        }}
                        placeholder="Buscar..."
                        size="sm"
                        startContent={
                            <MagnifyingGlassIcon className="w-4 h-4" />
                        }
                        value={searchValue}
                        variant="flat"
                        onClick={(e) => e.stopPropagation()}
                        onKeyDown={(e) => e.stopPropagation()}
                        onValueChange={setSearchValue}
                    />
                </SelectItem>
                {
                    (filteredItems.length === 0 ? (
                        <SelectItem key="no-results" isDisabled textValue="">
                            <div className="text-center text-gray-500 py-2">
                                No se encontraron resultados
                            </div>
                        </SelectItem>
                    ) : (
                        filteredItems.map((item) => (
                            <SelectItem key={item.id} textValue={item.name}>
                                {item.name}
                            </SelectItem>
                        ))
                    )) as any
                }
            </Select>
        </div>
    );
}
