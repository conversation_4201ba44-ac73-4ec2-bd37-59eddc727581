// Proof of concept for glassmorphism and animations
"use client";

import { motion } from "framer-motion";
import { Card, CardBody, Button } from "@heroui/react";
import clsx from "clsx";

import {
    createGlassClass,
    glassHoverStyles,
} from "@/shared/utils/ui/glass-morphism";
import { cardVariants, pulseVariants } from "@/shared/utils/ui/animations";

export default function GlassmorphismTest() {
    return (
        <div className="min-h-screen p-8 bg-gradient-to-br from-blue-500 to-purple-600">
            <motion.div
                animate="visible"
                className="max-w-md mx-auto"
                initial="hidden"
                variants={cardVariants}
                whileHover="hover"
            >
                <Card
                    className={clsx(
                        createGlassClass(),
                        glassHoverStyles.lift,
                        "overflow-hidden",
                    )}
                >
                    <CardBody className="p-6">
                        <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                            Glassmorphism Test
                        </h2>
                        <p className="mb-4 text-gray-700 dark:text-gray-300">
                            This is a proof of concept showing glassmorphism
                            effects with framer-motion animations.
                        </p>

                        <motion.div
                            animate="animate"
                            initial="initial"
                            variants={pulseVariants}
                        >
                            <Button
                                className="w-full"
                                color="primary"
                                variant="shadow"
                            >
                                Animated Button
                            </Button>
                        </motion.div>
                    </CardBody>
                </Card>
            </motion.div>
        </div>
    );
}
