"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Textarea,
    DatePicker,
    Divider,
    addToast,
} from "@heroui/react";
import {
    ArrowLeftIcon,
    DocumentPlusIcon,
    CalendarIcon,
    DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { parseDate } from "@internationalized/date";

import { DashboardHeader } from "@/shared/components/dashboard";
import { CustomerSelector } from "@/features/customers/components";
import { useCreatePacking } from "@/features/packings/hooks";

export default function PackingNewClient() {
    const router = useRouter();
    const { createPacking } = useCreatePacking();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [formData, setFormData] = useState({
        customer: { customerId: "", subCustomerId: "" },
        deliveryDate: null as any,
        notes: "",
        details: [] as any[],
    });

    const handleSubmit = async () => {
        // Validaciones básicas
        if (!formData.customer.customerId) {
            addToast({
                title: "Error",
                description: "Debe seleccionar un cliente",
                color: "danger",
            });

            return;
        }

        if (!formData.deliveryDate) {
            addToast({
                title: "Error",
                description: "Debe seleccionar una fecha de entrega",
                color: "danger",
            });

            return;
        }

        setIsSubmitting(true);

        // Por ahora, crear con detalles vacíos para prueba
        const packingData = {
            customerId: formData.customer.customerId,
            subCustomerId: formData.customer.subCustomerId,
            deliveryDate: new Date(formData.deliveryDate.toString()),
            notes: formData.notes,
            details: [
                {
                    garmentSizeId: "placeholder", // TODO: Implementar selector de productos
                    quantity: 1,
                },
            ],
        };

        const result = await createPacking(packingData);

        if (result.success && result.data) {
            router.push(`/dashboard/packings/${result.data.id}`);
        }

        setIsSubmitting(false);
    };

    return (
        <div className="space-y-6">
            <DashboardHeader
                actions={
                    <Button
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        variant="light"
                        onPress={() => router.push("/dashboard/packings")}
                    >
                        Cancelar
                    </Button>
                }
                breadcrumbs={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Packings", href: "/dashboard/packings" },
                    { label: "Nuevo" },
                ]}
                subtitle="Crea un nuevo packing list para entrega"
                title="Nuevo Packing"
            />

            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    handleSubmit();
                }}
            >
                <div className="max-w-3xl mx-auto space-y-6">
                    {/* Información básica */}
                    <Card>
                        <CardHeader>
                            <h3 className="text-lg font-semibold flex items-center gap-2">
                                <DocumentTextIcon className="w-5 h-5" />
                                Información General
                            </h3>
                        </CardHeader>
                        <Divider />
                        <CardBody className="space-y-4">
                            <CustomerSelector
                                isRequired
                                label="Cliente *"
                                value={formData.customer}
                                onChange={(value) =>
                                    setFormData({
                                        ...formData,
                                        customer: {
                                            customerId: value.customerId,
                                            subCustomerId:
                                                value.subCustomerId || "",
                                        },
                                    })
                                }
                            />

                            <DatePicker
                                isRequired
                                label="Fecha de Entrega *"
                                minValue={parseDate(
                                    new Date().toISOString().split("T")[0],
                                )}
                                startContent={
                                    <CalendarIcon className="w-4 h-4 text-gray-400" />
                                }
                                value={formData.deliveryDate}
                                onChange={(value) =>
                                    setFormData({
                                        ...formData,
                                        deliveryDate: value,
                                    })
                                }
                            />

                            <Textarea
                                label="Notas (Opcional)"
                                minRows={3}
                                placeholder="Agregar notas o instrucciones especiales..."
                                value={formData.notes}
                                onValueChange={(value) =>
                                    setFormData({ ...formData, notes: value })
                                }
                            />
                        </CardBody>
                    </Card>

                    {/* TODO: Implementar selector de productos */}
                    <Card>
                        <CardHeader>
                            <h3 className="text-lg font-semibold">
                                Artículos del Packing
                            </h3>
                        </CardHeader>
                        <Divider />
                        <CardBody>
                            <p className="text-gray-500 text-center py-8">
                                La funcionalidad de agregar productos se
                                implementará próximamente
                            </p>
                        </CardBody>
                    </Card>

                    {/* Acciones */}
                    <div className="flex justify-end gap-3">
                        <Button
                            variant="flat"
                            onPress={() => router.push("/dashboard/packings")}
                        >
                            Cancelar
                        </Button>
                        <Button
                            color="primary"
                            isLoading={isSubmitting}
                            startContent={
                                !isSubmitting && (
                                    <DocumentPlusIcon className="w-4 h-4" />
                                )
                            }
                            type="submit"
                        >
                            Crear Packing
                        </Button>
                    </div>
                </div>
            </form>
        </div>
    );
}
