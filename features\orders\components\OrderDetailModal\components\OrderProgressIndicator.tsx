"use client";

import React from "react";
import { motion } from "framer-motion";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

interface OrderProgressIndicatorProps {
    progress: number;
    size?: number;
    strokeWidth?: number;
    showPercentage?: boolean;
    status?: string;
}

export default function OrderProgressIndicator({
    progress = 0,
    size = 120,
    strokeWidth = 8,
    showPercentage = true,
    status,
}: OrderProgressIndicatorProps) {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDashoffset = circumference - (progress / 100) * circumference;

    // Color based on progress or status
    const getProgressColor = () => {
        if (status === "completed") return "#10B981"; // green
        if (progress >= 80) return "#10B981"; // green
        if (progress >= 50) return "#F59E0B"; // amber

        return "#3B82F6"; // blue
    };

    const isCompleted = progress >= 100 || status === "completed";

    return (
        <div className="relative inline-flex items-center justify-center">
            <svg className="transform -rotate-90" height={size} width={size}>
                {/* Background circle */}
                <circle
                    className="text-default-200"
                    cx={size / 2}
                    cy={size / 2}
                    fill="none"
                    r={radius}
                    stroke="currentColor"
                    strokeWidth={strokeWidth}
                />

                {/* Gradient definition */}
                <defs>
                    <linearGradient
                        id="progress-gradient"
                        x1="0%"
                        x2="100%"
                        y1="0%"
                        y2="100%"
                    >
                        <stop
                            offset="0%"
                            stopColor={getProgressColor()}
                            stopOpacity={0.8}
                        />
                        <stop
                            offset="100%"
                            stopColor={getProgressColor()}
                            stopOpacity={1}
                        />
                    </linearGradient>
                </defs>

                {/* Progress circle */}
                <motion.circle
                    animate={{ strokeDashoffset }}
                    cx={size / 2}
                    cy={size / 2}
                    fill="none"
                    initial={{ strokeDashoffset: circumference }}
                    r={radius}
                    stroke="url(#progress-gradient)"
                    strokeLinecap="round"
                    strokeWidth={strokeWidth}
                    style={{
                        strokeDasharray: circumference,
                    }}
                    transition={{
                        type: "spring",
                        stiffness: 100,
                        damping: 20,
                        duration: 1,
                    }}
                />
            </svg>

            {/* Center content */}
            <div className="absolute inset-0 flex items-center justify-center">
                {isCompleted ? (
                    <motion.div
                        animate={{ scale: 1 }}
                        initial={{ scale: 0 }}
                        transition={{
                            type: "spring",
                            stiffness: 200,
                            delay: 0.5,
                        }}
                    >
                        <CheckCircleIcon className="w-8 h-8 text-success" />
                    </motion.div>
                ) : (
                    showPercentage && (
                        <motion.div
                            animate={{ opacity: 1 }}
                            className="text-center"
                            initial={{ opacity: 0 }}
                            transition={{ delay: 0.2 }}
                        >
                            <span className="text-lg font-bold text-default-900">
                                {Math.round(progress)}
                            </span>
                            <span className="text-sm text-default-500">%</span>
                        </motion.div>
                    )
                )}
            </div>
        </div>
    );
}
