"use client";

import React, { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Divider,
    Textarea,
    Chip,
    Progress,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    addToast,
} from "@heroui/react";
import {
    DocumentTextIcon,
    ArrowLeftIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
    SparklesIcon,
    ArrowPathIcon,
    PencilSquareIcon,
} from "@heroicons/react/24/outline";

import {
    updateRemission,
    validateRemissionItemQuantities,
} from "@/features/remissions/actions/update";
import {
    RemissionOrderItems,
    RemissionContractorInfo,
} from "@/features/remissions/components";
import { DashboardShell } from "@/shared/components/layout/Shell";
import { DashboardHeader } from "@/shared/components/layout/Header";

interface RemissionEditClientProps {
    remission: {
        id: string;
        folio: string;
        notes: string | null;
        contractor?: {
            id: string;
            name: string;
            firstName?: string | null;
            lastName?: string | null;
            middleName?: string | null;
            secondLastName?: string | null;
            email?: string | null;
            phone?: string | null;
        };
        remissionItems: Array<{
            id: string;
            modelCode: string;
            colorName: string;
            sizeCode: string;
            quantity: number;
        }>;
        assignments: Array<{
            assignment: {
                id: string;
                quantity: number;
                order: {
                    id: string;
                    cutOrder: string | null;
                    parts: Array<{ id: string; code: string }>;
                };
                garmentSize: {
                    size: { code: string };
                    garment: {
                        model: { code: string };
                        color: { name: string };
                    };
                };
            };
        }>;
    };
}

export default function RemissionEditClient({
    remission,
}: RemissionEditClientProps) {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [notes, setNotes] = useState(remission.notes || "");
    const [itemQuantities, setItemQuantities] = useState<
        Record<string, number>
    >(() => {
        const quantities: Record<string, number> = {};

        remission.remissionItems?.forEach((item: any) => {
            quantities[item.id] = item.quantity;
        });

        return quantities;
    });
    const [hasChanges, setHasChanges] = useState(false);
    const [confirmModal, setConfirmModal] = useState({
        isOpen: false,
        action: null as string | null,
    });

    // Transform remission data to order groups for RemissionOrderItems
    const transformToOrderGroups = useCallback(() => {
        if (!remission.assignments || remission.assignments.length === 0) {
            return [];
        }

        // Group assignments by order
        const orderMap = new Map();

        remission.assignments.forEach((ra: any) => {
            const assignment = ra.assignment;

            // Validar que assignment existe
            if (!assignment) return;

            const order = assignment.order;

            // Validar que order existe
            if (!order || !order.id) return;

            const orderKey = order.id;

            if (!orderMap.has(orderKey)) {
                orderMap.set(orderKey, {
                    orderId: orderKey,
                    cutOrder: order.cutOrder || `ID-${orderKey.slice(-6)}`,
                    parts: order.parts || [],
                    assignments: [],
                });
            }

            // Find the corresponding remissionItem
            const remissionItem = remission.remissionItems.find(
                (item) =>
                    item.modelCode ===
                        assignment.garmentSize?.garment?.model?.code &&
                    item.colorName ===
                        assignment.garmentSize?.garment?.color?.name &&
                    item.sizeCode === assignment.garmentSize?.size?.code,
            );

            // Map assignment data to match expected structure
            const mappedAssignment = {
                id: assignment.id,
                quantity: remissionItem
                    ? itemQuantities[remissionItem.id] || remissionItem.quantity
                    : assignment.quantity,
                garmentSize: assignment.garmentSize,
            };

            orderMap.get(orderKey).assignments.push(mappedAssignment);
        });

        return Array.from(orderMap.values());
    }, [remission.assignments, itemQuantities]);

    const orderGroups = transformToOrderGroups();

    // Check if there are changes
    const checkForChanges = useCallback(() => {
        const notesChanged = notes !== (remission.notes || "");

        const quantitiesChanged = remission.remissionItems?.some((item) => {
            return itemQuantities[item.id] !== item.quantity;
        });

        setHasChanges(notesChanged || quantitiesChanged || false);
    }, [notes, remission.notes, remission.remissionItems, itemQuantities]);

    React.useEffect(() => {
        checkForChanges();
    }, [checkForChanges]);

    const handleQuantityChange = (
        modelCode: string,
        colorName: string,
        sizeCode: string,
        newQuantity: number,
    ) => {
        // Find the remissionItem that matches the model, color, and size
        const remissionItem = remission.remissionItems.find(
            (item) =>
                item.modelCode === modelCode &&
                item.colorName === colorName &&
                item.sizeCode === sizeCode,
        );

        if (remissionItem) {
            setItemQuantities((prev) => ({
                ...prev,
                [remissionItem.id]: newQuantity,
            }));
        }
    };

    const handleSaveChanges = async () => {
        setConfirmModal({ isOpen: false, action: null });
        setIsLoading(true);

        try {
            // Prepare items with changed quantities
            const updatedItems = remission.remissionItems
                .filter((item: any) => {
                    return (
                        itemQuantities[item.id] !== undefined &&
                        itemQuantities[item.id] !== item.quantity
                    );
                })
                .map((item: any) => {
                    return {
                        id: item.id,
                        quantity: itemQuantities[item.id],
                    };
                });

            // Validate quantities first
            if (updatedItems.length > 0) {
                const validation = await validateRemissionItemQuantities(
                    remission.id,
                    updatedItems,
                );

                if (!validation.success) {
                    addToast({
                        title: "Error de validación",
                        description:
                            validation.error ||
                            "Las cantidades exceden los límites permitidos",
                        color: "danger",
                        icon: <ExclamationCircleIcon className="w-5 h-5" />,
                    });
                    setIsLoading(false);

                    return;
                }
            }

            // Update remission
            const result = await updateRemission({
                remissionId: remission.id,
                notes: notes !== remission.notes ? notes : undefined,
                items: updatedItems.length > 0 ? updatedItems : undefined,
            });

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description: "Remisión actualizada correctamente",
                    color: "success",
                    icon: <CheckCircleIcon className="w-5 h-5" />,
                });

                // Redirect to detail page
                router.push(`/dashboard/remissions/${remission.id}`);
                router.refresh();
            } else {
                throw new Error(result.error || "Error al actualizar");
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al guardar los cambios",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        if (hasChanges) {
            // Show confirmation dialog
            setConfirmModal({ isOpen: true, action: "cancel" });
        } else {
            router.back();
        }
    };

    const springAnimation = {
        type: "spring",
        stiffness: 300,
        damping: 30,
    };

    return (
        <DashboardShell>
            <DashboardHeader
                heading={`Editar Remisión ${remission.folio}`}
                text="Modifica las cantidades y observaciones de la remisión"
            />

            <div className="space-y-6">
                {/* Progress indicator */}
                <Card className="border-2 border-blue-100 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900">
                    <CardBody className="p-4">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium flex items-center gap-2">
                                <PencilSquareIcon className="w-4 h-4" />
                                Modo de edición
                            </span>
                            <Chip
                                color={hasChanges ? "warning" : "default"}
                                size="sm"
                                variant="flat"
                            >
                                {hasChanges
                                    ? "Cambios sin guardar"
                                    : "Sin cambios"}
                            </Chip>
                        </div>
                        {hasChanges && (
                            <Progress
                                className="mb-1"
                                color="warning"
                                size="sm"
                                value={100}
                            />
                        )}
                    </CardBody>
                </Card>

                {/* Header Card */}
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: -20 }}
                    transition={springAnimation}
                >
                    <Card className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-gray-900 dark:to-purple-950/20 border-none shadow-xl">
                        <CardBody className="p-8">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                    <motion.div
                                        className="p-4 bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg backdrop-blur-sm"
                                        transition={{
                                            type: "spring",
                                            stiffness: 300,
                                        }}
                                        whileHover={{ scale: 1.05, rotate: 5 }}
                                    >
                                        <DocumentTextIcon className="w-10 h-10 text-blue-600 dark:text-blue-400" />
                                    </motion.div>
                                    <div>
                                        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                            {remission.folio}
                                        </h1>
                                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                            <span>Editar Remisión</span>
                                        </div>
                                    </div>
                                </div>
                                <SparklesIcon className="w-8 h-8 text-purple-500 animate-pulse" />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>

                {/* Contractor Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {remission.contractor && (
                        <RemissionContractorInfo
                            compact={false}
                            contractor={remission.contractor as any}
                            showCard={true}
                        />
                    )}
                </div>

                {/* Observations Card */}
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ ...springAnimation, delay: 0.1 }}
                >
                    <Card className="hover:shadow-lg transition-shadow">
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <DocumentTextIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                <h3 className="text-lg font-semibold">
                                    Observaciones
                                </h3>
                            </div>
                        </CardHeader>
                        <Divider />
                        <CardBody>
                            <Textarea
                                classNames={{
                                    input: "resize-y",
                                    inputWrapper: "hover:border-primary",
                                }}
                                maxRows={6}
                                minRows={3}
                                placeholder="Agrega observaciones adicionales para esta remisión (opcional)"
                                value={notes}
                                variant="bordered"
                                onChange={(e) => setNotes(e.target.value)}
                            />
                        </CardBody>
                    </Card>
                </motion.div>

                {/* Items Table - Currently Read Only */}
                {orderGroups && orderGroups.length > 0 && (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ ...springAnimation, delay: 0.2 }}
                    >
                        <Card className="p-4">
                            <CardBody>
                                <RemissionOrderItems
                                    orderGroups={orderGroups}
                                    readOnly={false}
                                    showRowAnimations={true}
                                    onQuantityChange={handleQuantityChange}
                                />
                            </CardBody>
                        </Card>
                    </motion.div>
                )}

                {/* Action Buttons */}
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-between items-center sticky bottom-0 bg-white dark:bg-gray-900 p-4 rounded-lg shadow-lg"
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ ...springAnimation, delay: 0.3 }}
                >
                    <Button
                        color="default"
                        isDisabled={isLoading}
                        size="lg"
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={handleCancel}
                    >
                        Cancelar
                    </Button>

                    <div className="flex gap-2">
                        <Button
                            color="default"
                            isDisabled={!hasChanges || isLoading}
                            size="lg"
                            startContent={<ArrowPathIcon className="w-4 h-4" />}
                            variant="flat"
                            onPress={() => {
                                setNotes(remission.notes || "");
                                const originalQuantities: Record<
                                    string,
                                    number
                                > = {};

                                remission.remissionItems?.forEach(
                                    (item: any) => {
                                        originalQuantities[item.id] =
                                            item.quantity;
                                    },
                                );
                                setItemQuantities(originalQuantities);
                            }}
                        >
                            Restablecer
                        </Button>

                        <Button
                            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                            color="primary"
                            endContent={<CheckCircleIcon className="w-4 h-4" />}
                            isDisabled={!hasChanges || isLoading}
                            isLoading={isLoading}
                            size="lg"
                            onPress={() =>
                                setConfirmModal({
                                    isOpen: true,
                                    action: "save",
                                })
                            }
                        >
                            Guardar Cambios
                        </Button>
                    </div>
                </motion.div>

                {/* Confirmation Modal */}
                <Modal
                    backdrop="blur"
                    isOpen={
                        confirmModal.isOpen && confirmModal.action === "save"
                    }
                    onClose={() =>
                        setConfirmModal({ isOpen: false, action: null })
                    }
                >
                    <ModalContent>
                        {(onClose) => (
                            <>
                                <ModalHeader>Confirmar Cambios</ModalHeader>
                                <ModalBody>
                                    <p>
                                        ¿Estás seguro de que deseas guardar los
                                        cambios en la remisión?
                                    </p>
                                    {notes !== remission.notes && (
                                        <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                                            <p className="text-sm font-medium mb-1">
                                                Observaciones actualizadas:
                                            </p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {notes || "(Sin observaciones)"}
                                            </p>
                                        </div>
                                    )}
                                </ModalBody>
                                <ModalFooter>
                                    <Button
                                        color="default"
                                        variant="flat"
                                        onPress={() =>
                                            setConfirmModal({
                                                isOpen: false,
                                                action: null,
                                            })
                                        }
                                    >
                                        Cancelar
                                    </Button>
                                    <Button
                                        color="primary"
                                        isLoading={isLoading}
                                        onPress={handleSaveChanges}
                                    >
                                        Guardar
                                    </Button>
                                </ModalFooter>
                            </>
                        )}
                    </ModalContent>
                </Modal>

                {/* Cancel Confirmation Modal */}
                <Modal
                    backdrop="blur"
                    isOpen={
                        confirmModal.isOpen && confirmModal.action === "cancel"
                    }
                    onClose={() =>
                        setConfirmModal({ isOpen: false, action: null })
                    }
                >
                    <ModalContent>
                        <ModalHeader>Cambios sin guardar</ModalHeader>
                        <ModalBody>
                            <p>
                                Tienes cambios sin guardar. ¿Estás seguro de que
                                deseas salir sin guardar?
                            </p>
                        </ModalBody>
                        <ModalFooter>
                            <Button
                                color="default"
                                variant="flat"
                                onPress={() =>
                                    setConfirmModal({
                                        isOpen: false,
                                        action: null,
                                    })
                                }
                            >
                                Continuar editando
                            </Button>
                            <Button
                                color="danger"
                                onPress={() => router.back()}
                            >
                                Salir sin guardar
                            </Button>
                        </ModalFooter>
                    </ModalContent>
                </Modal>
            </div>
        </DashboardShell>
    );
}
