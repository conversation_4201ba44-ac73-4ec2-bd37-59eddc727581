/**
 * Utility functions for formatting values in the application
 */

/**
 * Format a date to local string format
 */
export function formatDate(date: Date | string | null | undefined): string {
    if (!date) return "—";

    const dateObject = typeof date === "string" ? new Date(date) : date;

    try {
        return dateObject.toLocaleDateString("es-ES", {
            year: "numeric",
            month: "short",
            day: "numeric",
        });
    } catch (e) {
        return "Fecha inválida";
    }
}

/**
 * Format a date with time
 */
export function formatDateTime(date: Date | string | null | undefined): string {
    if (!date) return "—";

    const dateObject = typeof date === "string" ? new Date(date) : date;

    try {
        return dateObject.toLocaleDateString("es-ES", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    } catch (e) {
        return "Fecha inválida";
    }
}

/**
 * Format currency amount
 */
export function formatCurrency(amount: number | null | undefined): string {
    if (amount === null || amount === undefined) return "—";

    return new Intl.NumberFormat("es-MX", {
        style: "currency",
        currency: "MXN",
        minimumFractionDigits: 2,
    }).format(amount);
}

/**
 * Format percentage
 */
export function formatPercent(value: number | null | undefined): string {
    if (value === null || value === undefined) return "—";

    return new Intl.NumberFormat("es-ES", {
        style: "percent",
        minimumFractionDigits: 1,
        maximumFractionDigits: 1,
    }).format(value / 100);
}

/**
 * Format quantity with thousands separator
 */
export function formatQuantity(value: number | null | undefined): string {
    if (value === null || value === undefined) return "—";

    return new Intl.NumberFormat("es-ES").format(value);
}

/**
 * Trunca un texto largo y añade puntos suspensivos
 * @param text Texto a truncar
 * @param maxLength Longitud máxima (por defecto: 30)
 * @returns Texto truncado
 */
export function truncateText(
    text: string | null | undefined,
    maxLength = 30,
): string {
    if (!text) return "";

    return text.length <= maxLength
        ? text
        : `${text.substring(0, maxLength)}...`;
}

/**
 * Formatea un identificador para que sea legible
 * @param id Identificador a formatear
 * @param prefix Prefijo opcional
 * @returns Identificador formateado
 */
export function formatId(id: string | null | undefined, prefix = "#"): string {
    if (!id) return "---";

    // Si el ID es muy largo (como un UUID), tomar los últimos 6 caracteres
    const shortId = id.length > 8 ? id.substring(id.length - 6) : id;

    return `${prefix}${shortId}`;
}
