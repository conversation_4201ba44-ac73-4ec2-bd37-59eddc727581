import { z } from "zod";

import { RemissionPreview } from "@/types/remission";

// Esquema para validar la entrada de creación de asignaciones individuales
export const assignmentSchema = z.object({
    garmentSizeId: z.string().min(1, "La talla es obligatoria"),
    quantity: z.number().min(1, "La cantidad debe ser mayor a 0"),
    orderId: z.string().min(1, "La orden es obligatoria"),
});

// Esquema para validar la entrada de creación de asignaciones múltiples
export const createAssignmentsSchema = z.object({
    contractorId: z.string().min(1, "El contratista es obligatorio"),
    assignments: z
        .array(assignmentSchema)
        .min(1, "Debe agregar al menos una asignación"),
});

// Interfaz para la respuesta de asignaciones
export interface AssignmentResponse {
    success: boolean;
    data?: {
        assignments?: any[];
        remission?: {
            id: string;
            folio: string;
            createdAt: Date;
        };
        remissionPreview?: RemissionPreview;
        errors?: any[];
    };
    error?: string;
}

// Interfaz para datos de disponibilidad de tallas
export interface GarmentSizeAvailability {
    id: string;
    code: string;
    garmentId: string;
    totalQuantity: number;
    usedQuantity: number;
    availableQuantity: number;
    modelCode: string;
    colorName: string;
}

// Interfaz para filtros de consulta de órdenes disponibles
export interface OrdersQueryFilters {
    search?: string;
    page?: number;
    perPage?: number;
    onlyWithAvailability?: boolean;
}

// Interfaz para la entrada de asignación de cantidad
export interface QuantityAssignment {
    garmentSizeId: string;
    orderId: string;
    quantity: number;
    garmentId: string;
    modelCode: string;
    colorName: string;
    sizeCode: string;
    availableQuantity: number;
}
