"use client";

import { useState } from "react";
import Link from "next/link";
import { User, Mail, Lock, Eye, EyeOff, Check } from "lucide-react";

import {
    AuthCard,
    AuthInput,
    AuthButton,
    SocialButton,
    AuthDivider,
    PasswordStrength,
} from "@/components/auth/ui";

import { useAuthForm } from "./useAuthForm";

interface RegisterFormProps {
    onSuccess?: () => void;
}

export function RegisterForm({ onSuccess }: RegisterFormProps) {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const { form, onSubmit, isLoading, errors } = useAuthForm({
        type: "register",
        onSuccess,
    });

    const { register, watch } = form;
    const password = watch("password");
    const acceptTerms = (watch as any)("acceptTerms");

    return (
        <AuthCard className="animate-fadeIn dark:bg-slate-800/50 dark:backdrop-blur-sm">
            <div className="text-center mb-8">
                <h1 className="text-3xl font-bold font-display text-neutral-800 dark:text-slate-100 mb-2">
                    Crea tu cuenta
                </h1>
                <p className="text-neutral-600 dark:text-slate-400">
                    Únete a nosotros y comienza tu experiencia
                </p>
            </div>

            <form className="space-y-6" onSubmit={onSubmit}>
                <AuthInput
                    {...(register as any)("name")}
                    autoComplete="name"
                    disabled={isLoading}
                    error={(errors as any).name?.message}
                    icon={<User className="w-5 h-5" />}
                    label="Nombre completo"
                    placeholder="Juan Pérez"
                />

                <AuthInput
                    {...register("email")}
                    autoComplete="email"
                    disabled={isLoading}
                    error={errors.email?.message as string}
                    icon={<Mail className="w-5 h-5" />}
                    label="Correo electrónico"
                    placeholder="<EMAIL>"
                    type="email"
                />

                <div className="relative">
                    <AuthInput
                        {...register("password")}
                        autoComplete="new-password"
                        disabled={isLoading}
                        error={errors.password?.message as string}
                        icon={<Lock className="w-5 h-5" />}
                        label="Contraseña"
                        placeholder="••••••••"
                        type={showPassword ? "text" : "password"}
                    />
                    <button
                        className="absolute right-3 top-[42px] text-neutral-500 hover:text-neutral-700 transition-colors"
                        tabIndex={-1}
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                    >
                        {showPassword ? (
                            <EyeOff className="w-5 h-5" />
                        ) : (
                            <Eye className="w-5 h-5" />
                        )}
                    </button>
                    <PasswordStrength password={password || ""} />
                </div>

                <div className="relative">
                    <AuthInput
                        {...(register as any)("confirmPassword")}
                        autoComplete="new-password"
                        disabled={isLoading}
                        error={(errors as any).confirmPassword?.message}
                        icon={<Check className="w-5 h-5" />}
                        label="Confirmar contraseña"
                        placeholder="••••••••"
                        type={showConfirmPassword ? "text" : "password"}
                    />
                    <button
                        className="absolute right-3 top-[42px] text-neutral-500 hover:text-neutral-700 transition-colors"
                        tabIndex={-1}
                        type="button"
                        onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                        }
                    >
                        {showConfirmPassword ? (
                            <EyeOff className="w-5 h-5" />
                        ) : (
                            <Eye className="w-5 h-5" />
                        )}
                    </button>
                </div>

                <div className="space-y-3">
                    <label className="flex items-start gap-2 cursor-pointer">
                        <input
                            {...(register as any)("acceptTerms")}
                            className="w-4 h-4 mt-0.5 rounded border-neutral-300 text-primary-600 
                       focus:ring-primary-500 focus:ring-offset-0"
                            disabled={isLoading}
                            type="checkbox"
                        />
                        <span className="text-sm text-neutral-700">
                            Acepto los{" "}
                            <Link
                                className="text-primary-600 hover:underline"
                                href="/terms"
                            >
                                términos y condiciones
                            </Link>{" "}
                            y la{" "}
                            <Link
                                className="text-primary-600 hover:underline"
                                href="/privacy"
                            >
                                política de privacidad
                            </Link>
                        </span>
                    </label>
                    {(errors as any).acceptTerms && (
                        <p className="text-sm text-error ml-6">
                            {(errors as any).acceptTerms.message}
                        </p>
                    )}
                </div>

                {(errors as any).root && (
                    <div className="p-3 rounded-lg bg-error/10 border border-error/20">
                        <p className="text-sm text-error">
                            {(errors as any).root.message}
                        </p>
                    </div>
                )}

                <AuthButton
                    disabled={!acceptTerms}
                    isLoading={isLoading}
                    type="submit"
                >
                    Crear cuenta
                </AuthButton>
            </form>

            <AuthDivider text="o regístrate con" />

            <div className="space-y-3">
                <SocialButton
                    disabled={isLoading}
                    provider="google"
                    onClick={() => {
                        /* Implementar OAuth */
                    }}
                />
            </div>

            <p className="text-center text-sm text-neutral-600 mt-8">
                ¿Ya tienes una cuenta?{" "}
                <Link
                    className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
                    href="/login"
                >
                    Inicia sesión aquí
                </Link>
            </p>
        </AuthCard>
    );
}
