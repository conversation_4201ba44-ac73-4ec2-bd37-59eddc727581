"use client";

import React from "react";
import { Chip } from "@heroui/react";
import {
    ClockIcon,
    TruckIcon,
    CheckCircleIcon,
    XCircleIcon,
    DocumentCheckIcon,
} from "@heroicons/react/24/outline";

interface PackingStatus {
    id: string;
    name: string;
    color?: string;
    iconName?: string;
}

interface PackingStatusBadgeProps {
    status: PackingStatus;
    size?: "sm" | "md" | "lg";
}

const statusConfig: Record<
    string,
    {
        color:
            | "default"
            | "primary"
            | "secondary"
            | "success"
            | "warning"
            | "danger";
        icon: React.ElementType;
    }
> = {
    Pendiente: {
        color: "warning",
        icon: ClockIcon,
    },
    "En Proceso": {
        color: "primary",
        icon: DocumentCheckIcon,
    },
    "Listo para Entrega": {
        color: "secondary",
        icon: TruckIcon,
    },
    Entregado: {
        color: "success",
        icon: CheckCircleIcon,
    },
    Cancelado: {
        color: "danger",
        icon: XCircleIcon,
    },
};

export function PackingStatusBadge({
    status,
    size = "md",
}: PackingStatusBadgeProps) {
    const config = statusConfig[status.name] || {
        color: "default" as const,
        icon: ClockIcon,
    };

    const Icon = config.icon;

    return (
        <Chip
            color={config.color}
            size={size}
            startContent={
                <Icon className={`${size === "sm" ? "w-3 h-3" : "w-4 h-4"}`} />
            }
            variant="flat"
        >
            {status.name}
        </Chip>
    );
}
