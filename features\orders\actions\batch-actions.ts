"use server";

import { revalidatePath } from "next/cache";

import { prisma } from "@/shared/lib/prisma";
import { auth } from "@/lib/auth-helpers";

/**
 * Actualiza el estado de múltiples órdenes a la vez
 */
export async function updateOrdersStatus(orderIds: string[], statusId: string) {
    try {
        const session = await auth();

        if (!session?.user) {
            return { success: false, error: "No autorizado" };
        }

        // Verificar que el ID de usuario exista antes de proceder
        const userId = session.user.id;

        if (!userId) {
            return { success: false, error: "ID de usuario no disponible" };
        }

        // Actualizar todas las órdenes en una sola transacción
        const result = await prisma.$transaction(async (tx) => {
            // Actualizar el estado de todas las órdenes
            await tx.order.updateMany({
                where: {
                    id: {
                        in: orderIds,
                    },
                },
                data: {
                    statusId,
                    updatedAt: new Date(),
                },
            });

            // Registrar el historial para cada orden
            const historyEntries = await Promise.all(
                orderIds.map(async (orderId) => {
                    return tx.orderStatusHistory.create({
                        data: {
                            orderId,
                            statusId,
                            userId, // Ahora estamos seguros de que userId existe
                            createdAt: new Date(),
                        },
                    });
                }),
            );

            return { orders: orderIds.length, history: historyEntries.length };
        });

        // Revalidar las rutas relevantes
        revalidatePath("/dashboard/orders");
        orderIds.forEach((id) => {
            revalidatePath(`/dashboard/orders/${id}`);
        });

        return {
            success: true,
            data: {
                updatedOrders: result.orders,
                historyEntries: result.history,
            },
        };
    } catch (error: any) {
        // REMOVED: console.error("Error actualizando estados de órdenes:", error);

        return {
            success: false,
            error: error.message || "Error actualizando estados",
        };
    }
}

/**
 * Elimina múltiples órdenes a la vez
 */
export async function deleteOrders(orderIds: string[]) {
    try {
        const session = await auth();

        if (!session?.user) {
            return { success: false, error: "No autorizado" };
        }

        const result = await prisma.$transaction(async (tx) => {
            // Eliminar todas las órdenes
            const deletedOrders = await tx.order.deleteMany({
                where: {
                    id: {
                        in: orderIds,
                    },
                },
            });

            return { deletedCount: deletedOrders.count };
        });

        // Revalidar las rutas relevantes
        revalidatePath("/dashboard/orders");

        return {
            success: true,
            data: {
                deletedCount: result.deletedCount,
            },
        };
    } catch (error: any) {
        // REMOVED: console.error("Error eliminando órdenes:", error);

        return {
            success: false,
            error: error.message || "Error eliminando órdenes",
        };
    }
}

/**
 * Exporta múltiples órdenes a CSV
 */
export async function exportOrdersToCSV(orderIds: string[]) {
    try {
        const session = await auth();

        if (!session?.user) {
            return { success: false, error: "No autorizado" };
        }

        // Obtener los datos de las órdenes para exportar
        const orders = await prisma.order.findMany({
            where: {
                id: {
                    in: orderIds,
                },
            },
            include: {
                customer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
            },
        });

        // Aquí solo devolvemos los datos para que el cliente pueda generar el CSV
        // En una implementación real, podríamos generar el CSV en el servidor

        return {
            success: true,
            data: {
                orders,
                exportType: "csv",
                timestamp: new Date().toISOString(),
            },
        };
    } catch (error: any) {
        // REMOVED: console.error("Error exportando órdenes a CSV:", error);

        return {
            success: false,
            error: error.message || "Error exportando órdenes",
        };
    }
}

/**
 * Exporta múltiples órdenes a PDF
 */
export async function exportOrdersToPDF(orderIds: string[]) {
    try {
        const session = await auth();

        if (!session?.user) {
            return { success: false, error: "No autorizado" };
        }

        // Obtener los datos de las órdenes para exportar
        const orders = await prisma.order.findMany({
            where: {
                id: {
                    in: orderIds,
                },
            },
            include: {
                customer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
            },
        });

        // Aquí solo devolvemos los datos para que el cliente pueda generar el PDF
        // En una implementación real, podríamos generar el PDF en el servidor

        return {
            success: true,
            data: {
                orders,
                exportType: "pdf",
                timestamp: new Date().toISOString(),
            },
        };
    } catch (error: any) {
        // REMOVED: console.error("Error exportando órdenes a PDF:", error);

        return {
            success: false,
            error: error.message || "Error exportando órdenes",
        };
    }
}

/**
 * Duplica múltiples órdenes
 */
export async function duplicateOrders(orderIds: string[]) {
    try {
        const session = await auth();

        if (!session?.user) {
            return { success: false, error: "No autorizado" };
        }

        const result = await prisma.$transaction(async (tx) => {
            const duplicatedOrders: any[] = [];

            // Obtener el estado inicial para nuevas órdenes
            const initialStatus = await tx.orderStatus.findFirst({
                where: {
                    name: "Nuevo", // Ajustar según el nombre real del estado inicial
                },
            });

            if (!initialStatus) {
                throw new Error(
                    "No se encontró el estado inicial para nuevas órdenes",
                );
            }

            // Duplicar cada orden
            for (const orderId of orderIds) {
                // Obtener la orden original con sus relaciones
                const originalOrder = await tx.order.findUnique({
                    where: { id: orderId },
                    include: {
                        garments: {
                            include: {
                                sizes: true,
                            },
                        },
                        parts: true,
                    },
                });

                if (originalOrder) {
                    // Crear la nueva orden
                    const newOrder = await tx.order.create({
                        data: {
                            customerId: originalOrder.customerId,
                            statusId: initialStatus.id,
                            receivedDate: new Date(),
                            estimatedDeliveryDate:
                                originalOrder.estimatedDeliveryDate
                                    ? new Date(
                                          originalOrder.estimatedDeliveryDate,
                                      )
                                    : undefined,
                            transferNumber: `COPY-${originalOrder.transferNumber || ""}`,
                            cutOrder: `COPY-${originalOrder.cutOrder || ""}`,
                            batch: originalOrder.batch,
                            // Crear partes relacionadas
                            parts: {
                                create: originalOrder.parts.map((part) => ({
                                    code: part.code,
                                })),
                            },
                            // Crear prendas relacionadas
                            garments: {
                                create: originalOrder.garments.map(
                                    (garment) => ({
                                        modelId: garment.modelId,
                                        colorId: garment.colorId,
                                        // Crear tallas relacionadas
                                        sizes: {
                                            create: garment.sizes.map(
                                                (size) => ({
                                                    sizeId: size.sizeId,
                                                    totalQuantity:
                                                        size.totalQuantity || 0,
                                                    usedQuantity: 0,
                                                }),
                                            ),
                                        },
                                    }),
                                ),
                            },
                        },
                    });

                    duplicatedOrders.push(newOrder);
                }
            }

            return {
                duplicated: duplicatedOrders.length,
                orders: duplicatedOrders,
            };
        });

        // Revalidar las rutas relevantes
        revalidatePath("/dashboard/orders");

        return {
            success: true,
            data: {
                duplicatedCount: result.duplicated,
                orders: result.orders,
            },
        };
    } catch (error: any) {
        // REMOVED: console.error("Error duplicando órdenes:", error);

        return {
            success: false,
            error: error.message || "Error duplicando órdenes",
        };
    }
}
