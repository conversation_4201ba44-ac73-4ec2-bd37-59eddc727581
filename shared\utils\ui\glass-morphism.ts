// Glass morphism utility classes for modern UI effects
import clsx, { ClassValue } from "clsx";

// Glass morphism base styles
export const glassStyles = {
    light: "backdrop-blur-md bg-white/70 border border-white/20 shadow-xl",
    dark: "dark:backdrop-blur-md dark:bg-gray-900/70 dark:border-gray-700/20 dark:shadow-2xl",
    full: "backdrop-blur-md bg-white/70 border border-white/20 shadow-xl dark:bg-gray-900/70 dark:border-gray-700/20 dark:shadow-2xl",
} as const;

// Enhanced glass styles with gradients
export const enhancedGlassStyles = {
    primary: clsx(
        glassStyles.full,
        "bg-gradient-to-br from-primary-50/50 to-primary-100/50",
        "dark:from-primary-900/50 dark:to-primary-800/50",
    ),
    secondary: clsx(
        glassStyles.full,
        "bg-gradient-to-br from-secondary-50/50 to-secondary-100/50",
        "dark:from-secondary-900/50 dark:to-secondary-800/50",
    ),
    success: clsx(
        glassStyles.full,
        "bg-gradient-to-br from-success-50/50 to-success-100/50",
        "dark:from-success-900/50 dark:to-success-800/50",
    ),
    danger: clsx(
        glassStyles.full,
        "bg-gradient-to-br from-danger-50/50 to-danger-100/50",
        "dark:from-danger-900/50 dark:to-danger-800/50",
    ),
} as const;

// Animated gradient borders
export const animatedBorderStyles = {
    base: "relative overflow-hidden",
    gradient: clsx(
        "before:absolute before:inset-0 before:-z-10",
        "before:bg-gradient-to-r before:from-primary-500 before:via-secondary-500 before:to-primary-500",
        "before:bg-[length:200%_100%] before:animate-gradient-x",
        "before:p-[1px] before:rounded-[inherit]",
    ),
    content: "relative bg-background rounded-[inherit]",
} as const;

// Utility function to combine glass styles
export function createGlassClass(...classes: ClassValue[]) {
    return clsx(glassStyles.full, ...classes);
}

// Hover effects for glass components
export const glassHoverStyles = {
    scale: "transition-all duration-300 hover:scale-[1.02]",
    glow: "transition-all duration-300 hover:shadow-2xl hover:shadow-primary-500/20",
    lift: "transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl",
    tilt: "transition-all duration-300 hover:rotate-1",
} as const;

// Dark mode aware shadow styles
export const shadowStyles = {
    soft: "shadow-lg shadow-gray-200/50 dark:shadow-gray-900/50",
    colored: "shadow-lg shadow-primary-200/50 dark:shadow-primary-900/50",
    elevated: "shadow-2xl shadow-gray-300/50 dark:shadow-gray-800/50",
    glow: "shadow-2xl shadow-primary-400/30 dark:shadow-primary-600/30",
} as const;

// Utility for reduced motion
export function getMotionSafeClass(
    motionClass: string,
    fallbackClass: string = "",
) {
    return clsx("motion-safe:" + motionClass, "motion-reduce:" + fallbackClass);
}
