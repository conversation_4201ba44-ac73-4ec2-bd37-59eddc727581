"use client";

import { FEATURES } from "@/shared/config/features";
import { isUserInRollout } from "@/shared/utils/rollout";

/**
 * Hook to check if current user is in a feature rollout
 * Note: This is a simplified version. In production, you'd get the user from your auth context
 * @param feature - Feature name to check
 * @returns Whether the feature is enabled for the current user
 */
export function useRollout(feature: string): boolean {
    // TODO: Replace with actual user context
    // Example: const { user } = useUser();
    const userId =
        typeof window !== "undefined"
            ? window.localStorage.getItem("userId") || "anonymous"
            : "anonymous";

    const percentage = FEATURES.SCHEMA_ALIGNMENT_ROLLOUT;

    return isUserInRollout(userId, feature, percentage);
}

/**
 * Hook to get user's A/B test bucket
 * @param experiment - Experiment name
 * @param buckets - Number of buckets (default: 2)
 * @returns Bucket number (0 to buckets-1)
 */
export function useABTestBucket(
    experiment: string,
    buckets: number = 2,
): number {
    const userId =
        typeof window !== "undefined"
            ? window.localStorage.getItem("userId") || "anonymous"
            : "anonymous";

    const { getUserBucket } = require("@/shared/utils/rollout");

    return getUserBucket(userId, experiment, buckets);
}
