import { describe, expect, test, jest } from "@jest/globals";

import { tagInvalidator } from "../../cache/cache-invalidator";
import { RedisClient } from "../../cache/redis-client";

// Mock Redis client
jest.mock("../../cache/redis-client");

describe("Cache Invalidator", () => {
    let mockRedis: jest.Mocked<RedisClient>;

    beforeEach(() => {
        mockRedis = {
            sadd: jest.fn(),
            smembers: jest.fn(),
            srem: jest.fn(),
            del: jest.fn(),
            isConnected: jest.fn().mockReturnValue(true),
        } as any;

        (RedisClient as any).getInstance = jest.fn().mockReturnValue(mockRedis);
    });

    test("should add tag to key association", async () => {
        const key = "tenant1:orders:123:v1";
        const tag = "orders:123";

        await tagInvalidator.addTag(key, tag);

        expect(mockRedis.sadd).toHaveBeenCalledWith(`tags:${tag}`, key);
    });

    test("should add multiple tags to a key", async () => {
        const key = "tenant1:user:123:orders:v1";
        const tags = ["user:123", "orders"];

        await tagInvalidator.addTags(key, tags);

        tags.forEach((tag) => {
            expect(mockRedis.sadd).toHaveBeenCalledWith(`tags:${tag}`, key);
        });
    });

    test("should invalidate all keys by tag", async () => {
        const tag = "orders:123";
        const keys = ["tenant1:orders:123:v1", "tenant1:orders:123:items:v1"];

        mockRedis.smembers.mockResolvedValue(keys);

        await tagInvalidator.invalidateByTag(tag);

        expect(mockRedis.smembers).toHaveBeenCalledWith(`tags:${tag}`);
        expect(mockRedis.del).toHaveBeenCalledWith(...keys);
        expect(mockRedis.del).toHaveBeenCalledWith(`tags:${tag}`);
    });
});
