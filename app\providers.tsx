"use client";

import type { ThemeProviderProps } from "next-themes";

import * as React from "react";
import { HeroUIProvider } from "@heroui/system";
import { useRouter } from "next/navigation";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ToastProvider } from "@heroui/react";
import { XCircleIcon } from "@heroicons/react/24/outline";
import { SessionProvider } from "next-auth/react";

import { SWRProvider } from "@/shared/providers/swr-provider";
import { ReactQueryProvider } from "@/shared/components/providers/ReactQueryProvider";
import { AuthProvider } from "@/components/auth/providers/AuthProvider";

export interface ProvidersProps {
    children: React.ReactNode;
    themeProps?: ThemeProviderProps;
}

declare module "@react-types/shared" {
    interface RouterConfig {
        routerOptions: NonNullable<
            Parameters<ReturnType<typeof useRouter>["push"]>[1]
        >;
    }
}

export function Providers({ children, themeProps }: ProvidersProps) {
    const router = useRouter();

    return (
        <HeroUIProvider navigate={router.push}>
            <ToastProvider
                maxVisibleToasts={3}
                placement="top-center"
                toastProps={{
                    variant: "flat",
                    timeout: 2000,
                    radius: "md",
                    closeIcon: (
                        <XCircleIcon className="w-4 h-4 text-gray-500" />
                    ),
                }}
            />
            <SessionProvider
                refetchInterval={5 * 60 * 1000} // 5 minutos en lugar de 1 segundo
                refetchOnWindowFocus={false} // Desactivar refetch automático al cambiar de ventana
                refetchWhenOffline={false}
            >
                <NextThemesProvider {...themeProps}>
                    {/* React Query and SWR coexist during migration */}
                    <ReactQueryProvider>
                        <SWRProvider>
                            <AuthProvider>{children}</AuthProvider>
                        </SWRProvider>
                    </ReactQueryProvider>
                </NextThemesProvider>
            </SessionProvider>
        </HeroUIProvider>
    );
}
