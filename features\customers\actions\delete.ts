"use server";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

// Obtener helpers de revalidación para la entidad "customer"
const { revalidateCache } = createServerRevalidation("customer");

/**
 * Elimina un cliente
 */
export async function deleteCustomer(id: string) {
    if (!id) return { success: false, error: "ID no válido" };

    try {
        // Verificar si tiene órdenes asociadas primero
        const customerWithOrders = await db.customer.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { orders: true },
                },
            },
        });

        if (!customerWithOrders) {
            return { success: false, error: "Cliente no encontrado" };
        }

        // Verificar si tiene órdenes asociadas
        if (customerWithOrders._count.orders > 0) {
            return {
                success: false,
                error: `No se puede eliminar el cliente porque tiene ${customerWithOrders._count.orders} órdenes asociadas`,
            };
        }

        // Eliminar el cliente
        const deletedCustomer = await db.customer.delete({
            where: { id },
        });

        // Revalidar caché
        revalidateCache();

        return { success: true, data: deletedCustomer };
    } catch (error) {
        // Usar el helper para manejar errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al eliminar cliente");
    }
}
