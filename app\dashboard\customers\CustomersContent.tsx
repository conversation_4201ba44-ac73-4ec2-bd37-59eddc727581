"use client";

import type { Customer } from "@/features/customers/types";

import React, {
    useState,
    useEffect,
    useMemo,
    useCallback,
    useTransition,
} from "react";
import {
    Badge,
    Button,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownTrigger,
    Input,
    Modal,
    ModalContent,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableColumn,
    TableHeader,
    TableRow,
    Tooltip,
    useDisclosure,
    SortDescriptor,
    addToast,
    Kbd,
} from "@heroui/react";
import {
    ArrowPathIcon,
    ChevronDownIcon,
    MagnifyingGlassIcon,
    PencilSquareIcon,
    PlusIcon,
    TrashIcon,
    UserGroupIcon,
    InformationCircleIcon,
    HomeIcon,
    ChevronRightIcon,
    ArrowLeftIcon,
    CheckIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import CubeSpinner from "@/shared/components/ui/CubeSpinner";
import {
    useCustomers,
    useDeleteCustomer,
} from "@/features/customers/hooks/useCustomer";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

// Define los tipos de columna para la tabla
type ColumnKey = keyof Customer | "actions" | "usage";

// Definición de columnas con mejor tipado
interface Column {
    name: string;
    uid: string;
    sortable: boolean;
}

const COLUMNS: Column[] = [
    { name: "NOMBRE", uid: "name", sortable: true },
    { name: "USO", uid: "usage", sortable: false },
    { name: "CREACIÓN", uid: "createdAt", sortable: true },
    { name: "ACCIONES", uid: "actions", sortable: false },
];

const CustomersContent = () => {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [, startTransition] = useTransition();

    const {
        isOpen: isDeleteModalOpen,
        onOpen: onDeleteModalOpen,
        onClose: onDeleteModalClose,
    } = useDisclosure();

    const {
        isOpen: isDetailModalOpen,
        onOpen: onDetailModalOpen,
        onClose: onDetailModalClose,
    } = useDisclosure();

    const [searchTerm, setSearchTerm] = useState("");
    const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(
        null,
    );
    const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
        null,
    );
    const [isRefreshing, setIsRefreshing] = useState(false);

    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
        column: "name",
        direction: "ascending",
    });

    const [currentPage, setCurrentPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    // Usar optimizaciones de rendimiento: lazy load, useMemo, useCallback
    // Esto permite reducir el trabajo del browser y mejorar la experiencia
    const handleSortChange = useCallback((descriptor: SortDescriptor) => {
        setSortDescriptor(descriptor);
    }, []);

    // Uso del hook para clientes mediante Server Actions
    const { customers, isLoading, /* pagination, isError, error, */ mutate } =
        useCustomers({
            search: searchTerm,
            page: currentPage,
            perPage: rowsPerPage,
            orderBy: sortDescriptor.column?.toString(),
            order: sortDescriptor.direction === "ascending" ? "asc" : "desc",
        });

    // Hook para eliminar clientes
    const { deleteCustomer } = useDeleteCustomer();

    // Escuchar eventos de revalidación de clientes
    const { isRevalidating } = useRevalidationListener("customers");

    // Efecto para revalidar cuando cambia isRevalidating
    useEffect(() => {
        if (isRevalidating) {
            // Cuando se recibe un evento de revalidación, actualizar la tabla
            // if (process.env.NODE_ENV !== "production") {
            //     console.log("⚡ Revalidando tabla de clientes desde evento");
            // }
            mutate();
        }
    }, [isRevalidating, mutate]);

    // Añadir cálculo de estadísticas basado en los datos de clientes
    const statistics = useMemo(() => {
        const totalCustomers = customers.length;
        const activeCustomers = customers.filter(
            (customer: Customer) => (customer._count?.orders || 0) > 0,
        ).length;
        const inactiveCustomers = totalCustomers - activeCustomers;

        const activePercentage = totalCustomers
            ? Math.round((activeCustomers / totalCustomers) * 100)
            : 0;
        const inactivePercentage = totalCustomers
            ? Math.round((inactiveCustomers / totalCustomers) * 100)
            : 0;

        return {
            totalCustomers,
            activeCustomers,
            inactiveCustomers,
            activePercentage,
            inactivePercentage,
        };
    }, [customers]);

    useEffect(() => {
        const params = {
            search: searchParams.get("search"),
            page: searchParams.get("page"),
            perPage: searchParams.get("perPage"),
            sort: searchParams.get("sort"),
            order: searchParams.get("order"),
        };

        if (params.search) setSearchTerm(params.search);
        if (params.page) setCurrentPage(parseInt(params.page));
        if (params.perPage) setRowsPerPage(parseInt(params.perPage));
        if (params.sort) {
            setSortDescriptor({
                column: params.sort,
                direction:
                    params.order === "descending" ? "descending" : "ascending",
            });
        }
    }, [searchParams]);

    useEffect(() => {
        startTransition(() => {
            const params = new URLSearchParams();

            if (searchTerm) params.set("search", searchTerm);
            if (currentPage > 1) params.set("page", currentPage.toString());
            if (rowsPerPage !== 10)
                params.set("perPage", rowsPerPage.toString());
            if (sortDescriptor.column) {
                params.set("sort", sortDescriptor.column.toString());
                params.set("order", sortDescriptor.direction);
            }

            router.replace(`${pathname}?${params.toString()}`, {
                scroll: false,
            });
        });
    }, [
        searchTerm,
        currentPage,
        rowsPerPage,
        sortDescriptor,
        pathname,
        router,
        startTransition,
    ]);

    // Función para refrescar los datos manualmente
    const handleRefresh = async () => {
        setIsRefreshing(true);
        try {
            await mutate();
            addToast({
                title: "Datos actualizados",
                description: `Se han cargado ${customers.length} clientes`,
                color: "success",
            });
        } catch {
            // No se necesita la variable de error
            addToast({
                title: "Error",
                description: "No se pudieron actualizar los datos",
                color: "danger",
            });
        } finally {
            setIsRefreshing(false);
        }
    };

    // Optimizado: filtrado de clientes
    const filteredItems = useMemo(() => {
        // Si customers es undefined o null, retornamos un array vacío
        if (!customers) return [];

        // Filtrar por término de búsqueda
        return searchTerm
            ? customers.filter((item: Customer) =>
                  item.name.toLowerCase().includes(searchTerm.toLowerCase()),
              )
            : customers;
    }, [customers, searchTerm]);

    // Optimizado: ordenamiento de clientes
    const sortedItems = useMemo(() => {
        return [...filteredItems].sort((a, b) => {
            const column = sortDescriptor.column as keyof Customer;

            if (!a[column] || !b[column] || column === "_count") {
                return sortDescriptor.direction === "ascending"
                    ? a.name.localeCompare(b.name)
                    : b.name.localeCompare(a.name);
            }

            const aValue = String(a[column]);
            const bValue = String(b[column]);

            return sortDescriptor.direction === "ascending"
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        });
    }, [filteredItems, sortDescriptor]);

    // Optimizado: cálculos de paginación
    const { paginatedItems, pages, startIndex, endIndex } = useMemo(() => {
        const totalPages = Math.ceil(sortedItems.length / rowsPerPage);
        const start = (currentPage - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        return {
            paginatedItems: sortedItems.slice(start, end),
            pages: totalPages,
            startIndex: start,
            endIndex: end,
        };
    }, [sortedItems, currentPage, rowsPerPage]);

    // Handlers de eventos optimizados
    const handleSearch = useCallback((value: string) => {
        setSearchTerm(value);
        setCurrentPage(1);
    }, []);

    const handleOpenDetailModal = useCallback(
        (customer: Customer) => {
            setSelectedCustomer(customer);
            onDetailModalOpen();
        },
        [onDetailModalOpen],
    );

    const handleDeleteCustomer = useCallback(async () => {
        if (!customerToDelete) return;

        try {
            const result = await deleteCustomer(customerToDelete.id);

            if (!result || !result.success) {
                throw new Error(
                    result?.error || "Error al eliminar el cliente",
                );
            }

            onDeleteModalClose();

            addToast({
                title: "Cliente eliminado",
                description: `El cliente ${customerToDelete.name} ha sido eliminado correctamente`,
                color: "success",
            });
        } catch (error) {
            addToast({
                title: "Error eliminando cliente",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error desconocido",
                color: "danger",
            });
        } finally {
            setCustomerToDelete(null);
        }
    }, [customerToDelete, onDeleteModalClose, deleteCustomer]);

    const handleOpenDeleteModal = useCallback(
        (customer: Customer) => {
            setCustomerToDelete(customer);
            onDeleteModalOpen();
        },
        [onDeleteModalOpen],
    );

    // Atajo de teclado para búsqueda rápida
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if ((e.ctrlKey || e.metaKey) && e.key === "k") {
                e.preventDefault();
                document.getElementById("searchInput")?.focus();
            }

            if (
                e.key === "n" &&
                !(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)
            ) {
                e.preventDefault();
                router.push("/dashboard/customers/new");
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [router]);

    // Render cell
    const renderCell = useCallback(
        (customer: Customer, columnKey: ColumnKey) => {
            switch (columnKey) {
                case "name":
                    return (
                        <div className="flex items-center justify-center gap-2">
                            <Button
                                className="font-medium bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-md text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50 transition-colors min-w-0"
                                variant="light"
                                onPress={() => handleOpenDetailModal(customer)}
                            >
                                {customer.name}
                            </Button>
                        </div>
                    );

                case "usage":
                    const count = customer._count?.orders || 0;

                    return (
                        <div className="flex justify-center">
                            {count > 0 ? (
                                <Badge
                                    className="px-2 py-1"
                                    color="success"
                                    variant="flat"
                                >
                                    {count} {count === 1 ? "orden" : "órdenes"}
                                </Badge>
                            ) : (
                                <Badge
                                    className="px-2 py-1 inline-flex items-center justify-center"
                                    color="warning"
                                    variant="flat"
                                >
                                    <span>Sin uso</span>
                                </Badge>
                            )}
                        </div>
                    );

                case "createdAt":
                case "updatedAt":
                    return (
                        <div className="flex flex-col items-center justify-center">
                            <span className="text-sm text-gray-600 dark:text-gray-300">
                                {format(
                                    new Date(customer[columnKey]),
                                    "dd MMM yyyy",
                                    {
                                        locale: es,
                                    },
                                )}
                            </span>
                            <span className="text-xs text-gray-400 dark:text-gray-500">
                                {formatDistanceToNow(
                                    new Date(customer[columnKey]),
                                    {
                                        addSuffix: true,
                                        locale: es,
                                    },
                                )}
                            </span>
                        </div>
                    );

                case "actions":
                    const isUsed = (customer._count?.orders || 0) > 0;

                    return (
                        <div className="flex gap-2 justify-center">
                            <Tooltip
                                color="primary"
                                content="Ver detalles"
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="relative overflow-hidden bg-blue-50/80 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-blue-200/50 dark:border-blue-800/30 group"
                                    href={`/dashboard/customers/${customer.id}/details`}
                                    size="sm"
                                    variant="flat"
                                >
                                    <span className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                    <InformationCircleIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color="primary"
                                content="Editar"
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="relative overflow-hidden bg-indigo-50/80 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-indigo-200/50 dark:border-indigo-800/30 group"
                                    href={`/dashboard/customers/${customer.id}/edit`}
                                    size="sm"
                                    variant="flat"
                                >
                                    <span className="absolute inset-0 bg-gradient-to-r from-indigo-400/0 via-indigo-400/20 to-indigo-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                    <PencilSquareIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color={isUsed ? "warning" : "danger"}
                                content={
                                    isUsed
                                        ? "No se puede eliminar (en uso)"
                                        : "Eliminar"
                                }
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    className={`relative overflow-hidden ${isUsed ? "bg-amber-50/80 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400" : "bg-red-50/80 dark:bg-red-900/30 text-red-600 dark:text-red-400"} hover:scale-105 hover:shadow-md transition-all duration-200 border ${isUsed ? "border-amber-200/50 dark:border-amber-800/30" : "border-red-200/50 dark:border-red-800/30"} group`}
                                    isDisabled={isUsed}
                                    size="sm"
                                    variant="flat"
                                    onPress={() =>
                                        !isUsed &&
                                        handleOpenDeleteModal(customer)
                                    }
                                >
                                    <span
                                        className={`absolute inset-0 bg-gradient-to-r ${isUsed ? "from-amber-400/0 via-amber-400/20 to-amber-400/0" : "from-red-400/0 via-red-400/20 to-red-400/0"} opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300`}
                                    />
                                    <TrashIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>
                        </div>
                    );

                default:
                    return customer[columnKey]?.toString() || "-";
            }
        },
        [handleOpenDeleteModal, handleOpenDetailModal],
    );

    return (
        <div className="max-w-7xl mx-auto p-4 sm:p-6">
            {/* Breadcrumbs mejorados */}
            <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex flex-col gap-2">
                    <nav className="flex items-center space-x-2 text-sm">
                        <Link
                            className="flex items-center text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                            href="/dashboard"
                        >
                            <HomeIcon className="w-4 h-4 mr-1" />
                            Dashboard
                        </Link>
                        <ChevronRightIcon className="w-3 h-3 text-gray-400 dark:text-gray-600" />
                        <span className="text-gray-900 dark:text-white font-medium flex items-center">
                            <UserGroupIcon className="w-4 h-4 mr-1 text-primary-500" />
                            Clientes
                        </span>
                    </nav>
                    <h1 className="sr-only">Gestión de Clientes</h1>
                </div>

                <div className="flex items-center gap-2">
                    <Button
                        aria-label="Volver al dashboard"
                        as={Link}
                        className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200"
                        href="/dashboard"
                        size="sm"
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Volver
                    </Button>
                </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-sm dark:shadow-gray-800/30 border border-gray-100 dark:border-gray-800">
                {/* Header */}
                <div className="p-6 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10">
                    <div className="flex flex-col md:flex-row justify-between gap-6">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold flex items-center gap-3 bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">
                                <div className="p-2 bg-gradient-to-br from-primary-50 to-blue-50 dark:from-primary-900/30 dark:to-blue-900/30 rounded-lg shadow-sm shadow-primary-100/50 dark:shadow-primary-900/20">
                                    <UserGroupIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                                </div>
                                Gestión de Clientes
                            </h1>
                            <p className="text-gray-500 dark:text-gray-400 max-w-xl">
                                Administra los clientes registrados en el
                                sistema. Puedes crear, editar, ver detalles y
                                eliminar clientes desde esta interfaz.
                            </p>
                        </div>

                        <Button
                            as={Link}
                            className="bg-gradient-to-r from-primary-600 to-blue-600 hover:opacity-90 shadow-lg text-white dark:text-white hover:shadow-xl hover:scale-105 transition-all duration-300"
                            href="/dashboard/customers/new"
                            startContent={<PlusIcon className="w-4 h-4" />}
                        >
                            Nuevo Cliente
                        </Button>
                    </div>
                </div>

                {/* Dashboard Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-5 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-md hover:shadow-lg transition-all duration-300 hover:translate-y-[-2px] group">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-blue-600 dark:text-blue-400 flex items-center gap-1.5">
                                    <span className="inline-block w-1.5 h-1.5 bg-blue-500 rounded-full" />
                                    Total de Clientes
                                </p>
                                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mt-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                                    {statistics.totalCustomers}
                                </h3>
                            </div>
                            <div className="bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-800/50 dark:to-indigo-800/50 p-3 rounded-lg shadow-sm group-hover:scale-110 transition-transform duration-300">
                                <UserGroupIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 p-5 rounded-xl border border-green-100 dark:border-green-800/30 shadow-md hover:shadow-lg transition-all duration-300 hover:translate-y-[-2px] group">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-green-600 dark:text-green-400 flex items-center gap-1.5">
                                    <span className="inline-block w-1.5 h-1.5 bg-green-500 rounded-full" />
                                    Clientes Activos
                                </p>
                                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mt-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">
                                    {statistics.activeCustomers}
                                </h3>
                                <div className="mt-2 bg-white/50 dark:bg-black/10 rounded-full h-1.5 w-full overflow-hidden">
                                    <div
                                        className="h-full bg-gradient-to-r from-green-400 to-teal-400 rounded-full"
                                        style={{
                                            width: `${statistics.activePercentage}%`,
                                        }}
                                    />
                                </div>
                                <p className="text-xs text-green-600 dark:text-green-400 mt-1 font-medium">
                                    {statistics.activePercentage}% del total
                                </p>
                            </div>
                            <div className="bg-gradient-to-br from-green-100 to-teal-100 dark:from-green-800/50 dark:to-teal-800/50 p-3 rounded-lg shadow-sm group-hover:scale-110 transition-transform duration-300">
                                <CheckIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 p-5 rounded-xl border border-amber-100 dark:border-amber-800/30 shadow-md hover:shadow-lg transition-all duration-300 hover:translate-y-[-2px] group">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-amber-600 dark:text-amber-400 flex items-center gap-1.5">
                                    <span className="inline-block w-1.5 h-1.5 bg-amber-500 rounded-full" />
                                    Clientes Inactivos
                                </p>
                                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mt-2 group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors duration-300">
                                    {statistics.inactiveCustomers}
                                </h3>
                                <div className="mt-2 bg-white/50 dark:bg-black/10 rounded-full h-1.5 w-full overflow-hidden">
                                    <div
                                        className="h-full bg-gradient-to-r from-amber-400 to-yellow-400 rounded-full"
                                        style={{
                                            width: `${statistics.inactivePercentage}%`,
                                        }}
                                    />
                                </div>
                                <p className="text-xs text-amber-600 dark:text-amber-400 mt-1 font-medium">
                                    {statistics.inactivePercentage}% del total
                                </p>
                            </div>
                            <div className="bg-gradient-to-br from-amber-100 to-yellow-100 dark:from-amber-800/50 dark:to-yellow-800/50 p-3 rounded-lg shadow-sm group-hover:scale-110 transition-transform duration-300">
                                <InformationCircleIcon className="w-6 h-6 text-amber-600 dark:text-amber-400" />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Controls */}
                <div className="p-6 border-t border-b border-gray-100 dark:border-gray-800">
                    <div className="flex flex-col md:flex-row gap-4 justify-between">
                        <div className="relative w-full max-w-lg">
                            <Input
                                isClearable
                                aria-label="Buscar clientes"
                                classNames={{
                                    inputWrapper:
                                        "bg-transparent dark:bg-gray-800/50 shadow-sm dark:shadow-gray-900/50",
                                    input: "text-gray-900 dark:text-gray-100",
                                }}
                                id="searchInput"
                                placeholder="Buscar clientes... (Ctrl+K)"
                                startContent={
                                    <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                                }
                                value={searchTerm}
                                onClear={() => setSearchTerm("")}
                                onValueChange={handleSearch}
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 hidden md:flex items-center pointer-events-none">
                                <Kbd className="text-xs">Ctrl</Kbd>
                                <span className="mx-1 text-gray-400">+</span>
                                <Kbd className="text-xs">K</Kbd>
                            </div>
                        </div>

                        <div className="flex flex-wrap gap-3 items-center">
                            <Tooltip
                                content={
                                    isRefreshing
                                        ? "Actualizando..."
                                        : "Recargar datos"
                                }
                            >
                                <Button
                                    isIconOnly
                                    aria-label="Recargar datos"
                                    className="text-gray-700 dark:text-gray-300"
                                    isLoading={isRefreshing}
                                    variant="flat"
                                    onPress={handleRefresh}
                                >
                                    <ArrowPathIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>

                            <Dropdown>
                                <DropdownTrigger>
                                    <Button
                                        className="text-gray-700 dark:text-gray-300"
                                        endContent={
                                            <ChevronDownIcon className="w-4 h-4" />
                                        }
                                        variant="flat"
                                    >
                                        Mostrar: {rowsPerPage}
                                    </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                    disallowEmptySelection
                                    aria-label="Seleccionar registros por página"
                                    selectedKeys={
                                        new Set([rowsPerPage.toString()])
                                    }
                                    selectionMode="single"
                                    variant="flat"
                                    onAction={(key) => {
                                        const value = Number(key);

                                        if (!isNaN(value)) {
                                            setRowsPerPage(value);
                                            setCurrentPage(1);
                                        }
                                    }}
                                >
                                    {[5, 10, 20, 30, 40, 50].map((option) => (
                                        <DropdownItem
                                            key={option.toString()}
                                            textValue={option.toString()}
                                        >
                                            {option}
                                        </DropdownItem>
                                    ))}
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </div>
                </div>

                {/* Table - with loading overlay */}
                <div className="relative">
                    {isLoading && !isRefreshing && (
                        <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 z-10 flex items-center justify-center">
                            <CubeSpinner text="Cargando clientes..." />
                        </div>
                    )}

                    <Table
                        aria-label="Tabla de clientes"
                        classNames={{
                            wrapper: "px-6 py-4 rounded-b-xl",
                            base: "border-0",
                            th: "bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 text-center border-b border-gray-100 dark:border-gray-700",
                            td: "py-4 group-hover:bg-gray-50 dark:group-hover:bg-gray-800/50 text-center text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-800",
                        }}
                        sortDescriptor={sortDescriptor}
                        onSortChange={handleSortChange}
                    >
                        <TableHeader>
                            {COLUMNS.map((column) => (
                                <TableColumn
                                    key={column.uid}
                                    allowsSorting={column.sortable}
                                    className="text-sm font-semibold text-center"
                                >
                                    {column.name}
                                </TableColumn>
                            ))}
                        </TableHeader>

                        <TableBody
                            emptyContent={
                                isLoading ? (
                                    <div className="h-[200px] flex items-center justify-center">
                                        <CubeSpinner text="Cargando clientes..." />
                                    </div>
                                ) : (
                                    <div className="py-12 flex flex-col items-center justify-center text-center">
                                        <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-3 mb-4">
                                            <UserGroupIcon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                                        </div>
                                        <p className="text-gray-500 dark:text-gray-400 mb-2">
                                            No se encontraron clientes
                                        </p>
                                        <Button
                                            as={Link}
                                            color="primary"
                                            href="/dashboard/customers/new"
                                            size="sm"
                                            startContent={
                                                <PlusIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                        >
                                            Crear nuevo cliente
                                        </Button>
                                    </div>
                                )
                            }
                            items={paginatedItems}
                            loadingContent={
                                <CubeSpinner text="Actualizando..." />
                            }
                            loadingState={isRefreshing ? "loading" : "idle"}
                        >
                            {(item: Customer) => (
                                <TableRow
                                    key={item.id}
                                    className="cursor-default group transition-colors"
                                >
                                    {(columnKey) => (
                                        <TableCell className="text-center">
                                            {renderCell(
                                                item,
                                                columnKey as ColumnKey,
                                            )}
                                        </TableCell>
                                    )}
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination with status */}
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 border-t border-gray-100 dark:border-gray-800">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                            Mostrando{" "}
                            {filteredItems.length > 0 ? startIndex + 1 : 0}-
                            {Math.min(endIndex, filteredItems.length)} de{" "}
                            {filteredItems.length} resultados
                        </span>
                    </div>

                    <Pagination
                        isCompact
                        showControls
                        classNames={{
                            item: "bg-transparent text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800",
                            cursor: "bg-gradient-to-r from-purple-600 to-blue-600 text-white dark:text-white",
                            prev: "bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 text-gray-600 dark:text-gray-300 hover:opacity-90",
                            next: "bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 text-gray-600 dark:text-gray-300 hover:opacity-90",
                        }}
                        page={currentPage}
                        total={pages}
                        onChange={setCurrentPage}
                    />
                </div>
            </div>

            {/* Delete Modal */}
            <Modal isOpen={isDeleteModalOpen} onClose={onDeleteModalClose}>
                <ModalContent className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                    <div className="p-6 text-center">
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
                            <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                            ¿Eliminar cliente permanentemente?
                        </h3>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            El cliente{" "}
                            <span className="font-semibold text-gray-700 dark:text-gray-300">
                                {customerToDelete?.name}
                            </span>{" "}
                            será eliminado permanentemente del sistema.
                        </p>
                    </div>
                    <div className="flex gap-3 justify-center p-6 pt-0">
                        <Button
                            className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                            variant="flat"
                            onPress={onDeleteModalClose}
                        >
                            Cancelar
                        </Button>
                        <Button
                            className="bg-gradient-to-r from-red-600 to-pink-600 text-white dark:text-white shadow-danger-md"
                            color="danger"
                            onPress={handleDeleteCustomer}
                        >
                            Confirmar Eliminación
                        </Button>
                    </div>
                </ModalContent>
            </Modal>

            {/* Detail Modal */}
            {selectedCustomer && (
                <Modal
                    isOpen={isDetailModalOpen}
                    size="lg"
                    onClose={onDetailModalClose}
                >
                    <ModalContent className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                        <div className="p-6">
                            <div className="flex items-center mb-4">
                                <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg mr-3">
                                    <UserGroupIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                                    Detalles del Cliente:{" "}
                                    {selectedCustomer.name}
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        ID
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedCustomer.id}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Nombre
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedCustomer.name}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Fecha de Creación
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {format(
                                                new Date(
                                                    selectedCustomer.createdAt,
                                                ),
                                                "dd MMM yyyy",
                                                {
                                                    locale: es,
                                                },
                                            )}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Fecha de Actualización
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {format(
                                                new Date(
                                                    selectedCustomer.updatedAt,
                                                ),
                                                "dd MMM yyyy",
                                                {
                                                    locale: es,
                                                },
                                            )}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Uso
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedCustomer._count?.orders ||
                                                0}{" "}
                                            órdenes
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ModalContent>
                </Modal>
            )}

            {/* Mostrar spinner de carga condicional para revalidación */}
            {(isLoading || isRevalidating) && (
                <div className="absolute top-4 right-4">
                    <CubeSpinner
                        className="text-primary"
                        text="Actualizando..."
                    />
                </div>
            )}
        </div>
    );
};

export default CustomersContent;
