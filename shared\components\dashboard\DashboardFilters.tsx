"use client";

import React from "react";
import { motion } from "framer-motion";
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    XMarkIcon,
    CalendarIcon,
    ArrowsUpDownIcon,
} from "@heroicons/react/24/outline";

import {
    Input,
    Select,
    SelectItem,
    Button,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    Chip,
} from "@/shared/components/ui/hero-ui-client";

export interface FilterOption {
    key: string;
    label: string;
    type: "select" | "search" | "date" | "dateRange";
    placeholder?: string;
    options?: Array<{ value: string; label: string }>;
    icon?: React.ReactNode;
}

export interface SortOption {
    key: string;
    label: string;
    field: string;
    direction: "asc" | "desc";
}

interface DashboardFiltersProps {
    filters?: FilterOption[];
    sortOptions?: SortOption[];
    searchValue?: string;
    onSearchChange?: (value: string) => void;
    filterValues?: Record<string, any>;
    onFilterChange?: (key: string, value: any) => void;
    onSortChange?: (option: SortOption) => void;
    currentSort?: SortOption;
    onClearFilters?: () => void;
    activeFiltersCount?: number;
    children?: React.ReactNode;
}

export function DashboardFilters({
    filters = [],
    sortOptions = [],
    searchValue = "",
    onSearchChange,
    filterValues = {},
    onFilterChange,
    onSortChange,
    currentSort,
    onClearFilters,
    activeFiltersCount = 0,
    children,
}: DashboardFiltersProps) {
    const [showFilters, setShowFilters] = React.useState(false);

    const hasActiveFilters = activeFiltersCount > 0 || searchValue.length > 0;

    return (
        <div className="space-y-4">
            {/* Main Search and Action Bar */}
            <div className="flex flex-col sm:flex-row gap-4">
                {/* Search Input */}
                <div className="flex-1">
                    <Input
                        classNames={{
                            base: "max-w-full sm:max-w-[400px]",
                            inputWrapper:
                                "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700",
                        }}
                        placeholder="Buscar..."
                        startContent={
                            <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                        }
                        value={searchValue}
                        onValueChange={onSearchChange}
                    />
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                    {/* Filter Toggle */}
                    {filters.length > 0 && (
                        <Button
                            className="border-gray-300 dark:border-gray-600"
                            endContent={
                                activeFiltersCount > 0 && (
                                    <Chip
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {activeFiltersCount}
                                    </Chip>
                                )
                            }
                            startContent={<FunnelIcon className="w-4 h-4" />}
                            variant={showFilters ? "flat" : "bordered"}
                            onPress={() => setShowFilters(!showFilters)}
                        >
                            Filtros
                        </Button>
                    )}

                    {/* Sort Dropdown */}
                    {sortOptions.length > 0 && (
                        <Dropdown>
                            <DropdownTrigger>
                                <Button
                                    className="border-gray-300 dark:border-gray-600"
                                    startContent={
                                        <ArrowsUpDownIcon className="w-4 h-4" />
                                    }
                                    variant="bordered"
                                >
                                    {currentSort
                                        ? currentSort.label
                                        : "Ordenar"}
                                </Button>
                            </DropdownTrigger>
                            <DropdownMenu
                                aria-label="Opciones de ordenamiento"
                                selectedKeys={
                                    currentSort ? [currentSort.key] : []
                                }
                            >
                                {sortOptions.map((option) => (
                                    <DropdownItem
                                        key={option.key}
                                        onPress={() => onSortChange?.(option)}
                                    >
                                        {option.label} (
                                        {option.direction === "asc" ? "↑" : "↓"}
                                        )
                                    </DropdownItem>
                                ))}
                            </DropdownMenu>
                        </Dropdown>
                    )}

                    {/* Clear Filters */}
                    {hasActiveFilters && (
                        <Button
                            color="danger"
                            startContent={<XMarkIcon className="w-4 h-4" />}
                            variant="flat"
                            onPress={onClearFilters}
                        >
                            Limpiar
                        </Button>
                    )}

                    {/* Additional Actions */}
                    {children}
                </div>
            </div>

            {/* Filter Panel */}
            {showFilters && filters.length > 0 && (
                <motion.div
                    animate={{ opacity: 1, height: "auto" }}
                    className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
                    exit={{ opacity: 0, height: 0 }}
                    initial={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        {filters.map((filter) => {
                            switch (filter.type) {
                                case "select":
                                    const currentValue =
                                        filterValues[filter.key];
                                    const selectedKeysSet = currentValue
                                        ? new Set([currentValue])
                                        : new Set();

                                    return (
                                        <Select
                                            key={filter.key}
                                            classNames={{
                                                trigger:
                                                    "bg-white dark:bg-gray-800",
                                            }}
                                            label={filter.label}
                                            placeholder={filter.placeholder}
                                            selectedKeys={selectedKeysSet}
                                            startContent={filter.icon}
                                            onSelectionChange={(keys) => {
                                                const keysArray =
                                                    Array.from(keys);
                                                const value = keysArray[0];

                                                // Only trigger change if the value is actually different
                                                if (value !== currentValue) {
                                                    onFilterChange?.(
                                                        filter.key,
                                                        value,
                                                    );
                                                }
                                            }}
                                        >
                                            {filter.options?.map((option) => (
                                                <SelectItem key={option.value}>
                                                    {option.label}
                                                </SelectItem>
                                            )) ?? []}
                                        </Select>
                                    );

                                case "date":
                                    return (
                                        <Input
                                            key={filter.key}
                                            classNames={{
                                                inputWrapper:
                                                    "bg-white dark:bg-gray-800",
                                            }}
                                            label={filter.label}
                                            placeholder={filter.placeholder}
                                            startContent={
                                                <CalendarIcon className="w-4 h-4" />
                                            }
                                            type="date"
                                            value={
                                                filterValues[filter.key] || ""
                                            }
                                            onValueChange={(value) =>
                                                onFilterChange?.(
                                                    filter.key,
                                                    value,
                                                )
                                            }
                                        />
                                    );

                                default:
                                    return null;
                            }
                        })}
                    </div>
                </motion.div>
            )}
        </div>
    );
}
