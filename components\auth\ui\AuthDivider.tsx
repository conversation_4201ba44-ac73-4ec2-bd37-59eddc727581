"use client";

interface AuthDividerProps {
    text?: string;
}

export function AuthDivider({ text = "o" }: AuthDividerProps) {
    return (
        <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-neutral-300" />
            </div>
            <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white/70 text-neutral-500 backdrop-blur-sm">
                    {text}
                </span>
            </div>
        </div>
    );
}
