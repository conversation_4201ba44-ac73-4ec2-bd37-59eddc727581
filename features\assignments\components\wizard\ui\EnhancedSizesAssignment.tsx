"use client";

import React, { useState, useMemo, useCallback } from "react";
import {
    Card,
    CardBody,
    Badge,
    Tooltip,
    Progress,
    Button,
    Input,
} from "@heroui/react";
import {
    CubeIcon,
    SparklesIcon,
    InformationCircleIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
    ChartBarIcon,
    ArrowsPointingOutIcon,
    ArrowsPointingInIcon,
    ChevronDownIcon,
    ChevronUpIcon,
    FireIcon,
    BoltIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

interface Size {
    id: string;
    code: string;
    availableQuantity: number;
}

interface Garment {
    id: string;
    modelCode: string;
    modelDescription: string;
    colorName: string;
    colorHex?: string;
    sizes: Size[];
}

interface Assignment {
    garmentSizeId: string;
    quantity: number;
}

interface EnhancedSizesAssignmentProps {
    orderId: string;
    orderCode?: string;
    garments: Garment[];
    assignments: Assignment[];
    onQuantityChange: (
        garmentSizeId: string,
        quantity: number,
        orderId: string,
        garmentId: string,
        modelCode: string,
        colorName: string,
        sizeCode: string,
        availableQuantity: number,
    ) => void;
    onValidationChange?: (garmentSizeId: string, isValid: boolean) => void;
    validationErrors?: Record<string, string>;
    contractorId?: string;
}

export default function EnhancedSizesAssignment({
    orderId,
    orderCode,
    garments,
    assignments,
    onQuantityChange,
    onValidationChange,
    validationErrors = {},
    contractorId,
}: EnhancedSizesAssignmentProps) {
    const [focusedSizeId, setFocusedSizeId] = useState<string | null>(null);
    const [expandedGarments, setExpandedGarments] = useState<Set<string>>(
        new Set(garments.map((g) => g.id)),
    );

    // Calcular métricas para cada prenda
    const garmentMetrics = useMemo(() => {
        return garments.map((garment) => {
            const sizes = garment.sizes || [];
            const totalAvailable = sizes.reduce(
                (sum, size) => sum + size.availableQuantity,
                0,
            );

            // Calcular cantidades asignadas
            const assignedQuantities = sizes.map((size) => {
                const assignment = assignments.find(
                    (a) => a.garmentSizeId === size.id,
                );

                return {
                    sizeId: size.id,
                    assigned: assignment?.quantity || 0,
                };
            });

            const totalAssigned = assignedQuantities.reduce(
                (sum, a) => sum + a.assigned,
                0,
            );
            const assignmentRate =
                totalAvailable > 0 ? (totalAssigned / totalAvailable) * 100 : 0;

            // Encontrar la talla con más disponibilidad
            const mostAvailableSize = sizes.reduce(
                (prev, current) =>
                    current.availableQuantity > (prev?.availableQuantity || 0)
                        ? current
                        : prev,
                sizes[0],
            );

            return {
                ...garment,
                totalAvailable,
                totalAssigned,
                assignmentRate,
                mostAvailableSizeCode: mostAvailableSize?.code,
                sizesData: sizes.map((size) => {
                    const assignment = assignments.find(
                        (a) => a.garmentSizeId === size.id,
                    );
                    const assigned = assignment?.quantity || 0;
                    const availabilityPercent =
                        size.availableQuantity > 0
                            ? ((size.availableQuantity - assigned) /
                                  size.availableQuantity) *
                              100
                            : 0;

                    return {
                        ...size,
                        assigned,
                        availabilityPercent,
                        hasError: !!validationErrors[size.id],
                    };
                }),
            };
        });
    }, [garments, assignments, validationErrors]);

    const getAvailabilityColor = (percent: number) => {
        if (percent >= 80) return "success";
        if (percent >= 50) return "warning";
        if (percent >= 20) return "danger";

        return "default";
    };

    const getIntensityClass = (available: number, maxAvailable: number) => {
        if (maxAvailable === 0) return "";
        const ratio = available / maxAvailable;

        if (ratio >= 0.8) return "bg-primary/20 border-primary/60";
        if (ratio >= 0.6) return "bg-primary/15 border-primary/40";
        if (ratio >= 0.4) return "bg-primary/10 border-primary/30";
        if (ratio >= 0.2) return "bg-primary/5 border-primary/20";

        return "";
    };

    const handleQuantityInput = useCallback(
        (
            e: React.ChangeEvent<HTMLInputElement>,
            sizeData: any,
            garment: any,
        ) => {
            const value = parseInt(e.target.value) || 0;

            onQuantityChange(
                sizeData.id,
                value,
                orderId,
                garment.id,
                garment.modelCode,
                garment.colorName,
                sizeData.code,
                sizeData.availableQuantity,
            );

            if (onValidationChange) {
                const isValid =
                    value >= 0 && value <= sizeData.availableQuantity;

                onValidationChange(sizeData.id, isValid);
            }
        },
        [orderId, onQuantityChange, onValidationChange],
    );

    const toggleGarmentExpansion = (garmentId: string) => {
        setExpandedGarments((prev) => {
            const newSet = new Set(prev);

            if (newSet.has(garmentId)) {
                newSet.delete(garmentId);
            } else {
                newSet.add(garmentId);
            }

            return newSet;
        });
    };

    // Calcular estadísticas globales
    const globalStats = useMemo(() => {
        const totalAvailable = garmentMetrics.reduce(
            (sum, g) => sum + g.totalAvailable,
            0,
        );
        const totalAssigned = garmentMetrics.reduce(
            (sum, g) => sum + g.totalAssigned,
            0,
        );
        const avgAssignmentRate =
            garmentMetrics.length > 0
                ? garmentMetrics.reduce((sum, g) => sum + g.assignmentRate, 0) /
                  garmentMetrics.length
                : 0;

        return { totalAvailable, totalAssigned, avgAssignmentRate };
    }, [garmentMetrics]);

    if (!garments || garments.length === 0) {
        return (
            <Card className="border border-default-200">
                <CardBody className="text-center p-6">
                    <p className="text-default-500">
                        No hay prendas disponibles para esta orden
                    </p>
                </CardBody>
            </Card>
        );
    }

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.6, type: "spring" }}
        >
            <Card className="overflow-hidden bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-2xl">
                <CardBody className="p-0">
                    {/* Enhanced Header */}
                    <div className="p-6 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm border-b border-white/20 dark:border-gray-700/30">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <motion.div
                                    className="p-3 rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm shadow-lg"
                                    whileHover={{ scale: 1.1, rotate: 5 }}
                                >
                                    <CubeIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                </motion.div>
                                <div>
                                    <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                        Asignación de Cantidades por Talla
                                    </h3>
                                    <div className="flex items-center gap-2 mt-1">
                                        <Badge
                                            color="primary"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {orderCode ||
                                                orderId.substring(0, 8)}
                                        </Badge>
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            {garments.length} modelo
                                            {garments.length !== 1 ? "s" : ""}
                                        </span>
                                        <span className="text-sm text-gray-400 dark:text-gray-500">
                                            •
                                        </span>
                                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            {globalStats.totalAvailable}{" "}
                                            unidades
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3">
                                <motion.div
                                    animate={{ scale: 1 }}
                                    className="relative"
                                    initial={{ scale: 0 }}
                                    transition={{ delay: 0.3, type: "spring" }}
                                >
                                    <svg className="w-20 h-20">
                                        <circle
                                            className="text-gray-200 dark:text-gray-700"
                                            cx="40"
                                            cy="40"
                                            fill="none"
                                            r="36"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        />
                                        <motion.circle
                                            animate={{
                                                strokeDashoffset:
                                                    226 -
                                                    (226 *
                                                        globalStats.avgAssignmentRate) /
                                                        100,
                                            }}
                                            cx="40"
                                            cy="40"
                                            fill="none"
                                            initial={{ strokeDashoffset: 226 }}
                                            r="36"
                                            stroke="url(#gradient)"
                                            strokeDasharray={226}
                                            strokeDashoffset={
                                                226 -
                                                (226 *
                                                    globalStats.avgAssignmentRate) /
                                                    100
                                            }
                                            strokeLinecap="round"
                                            strokeWidth="4"
                                            transform="rotate(-90 40 40)"
                                            transition={{
                                                duration: 1,
                                                type: "spring",
                                            }}
                                        />
                                        <defs>
                                            <linearGradient
                                                id="gradient"
                                                x1="0%"
                                                x2="100%"
                                                y1="0%"
                                                y2="100%"
                                            >
                                                <stop
                                                    offset="0%"
                                                    stopColor="#3B82F6"
                                                />
                                                <stop
                                                    offset="100%"
                                                    stopColor="#A855F7"
                                                />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    <div className="absolute inset-0 flex items-center justify-center">
                                        <motion.span className="text-lg font-bold">
                                            {Math.round(
                                                globalStats.avgAssignmentRate,
                                            )}
                                            %
                                        </motion.span>
                                    </div>
                                </motion.div>
                                <motion.div
                                    animate={{ rotate: [0, 360] }}
                                    transition={{
                                        duration: 20,
                                        repeat: Infinity,
                                        ease: "linear",
                                    }}
                                >
                                    <SparklesIcon className="w-6 h-6 text-purple-500" />
                                </motion.div>
                            </div>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                        {/* Lista de prendas mejorada */}
                        <div className="space-y-4">
                            {garmentMetrics.map((garment, garmentIndex) => {
                                const maxAvailable = Math.max(
                                    ...garment.sizesData.map(
                                        (s) => s.availableQuantity,
                                    ),
                                );
                                const isExpanded = expandedGarments.has(
                                    garment.id,
                                );

                                return (
                                    <motion.div
                                        key={garment.id}
                                        animate={{ opacity: 1, y: 0, scale: 1 }}
                                        className="group"
                                        initial={{
                                            opacity: 0,
                                            y: 20,
                                            scale: 0.95,
                                        }}
                                        transition={{
                                            duration: 0.5,
                                            delay: garmentIndex * 0.1,
                                            type: "spring",
                                            stiffness: 100,
                                        }}
                                        whileHover={{ y: -4 }}
                                    >
                                        <div className="relative rounded-2xl overflow-hidden bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-xl hover:shadow-2xl transition-all duration-300">
                                            {/* Gradient overlay on hover */}
                                            <motion.div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                            {/* Header de la prenda */}
                                            <motion.div
                                                className="p-5 cursor-pointer relative z-10"
                                                whileTap={{ scale: 0.995 }}
                                                onClick={() =>
                                                    toggleGarmentExpansion(
                                                        garment.id,
                                                    )
                                                }
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-3">
                                                        <motion.div
                                                            className="relative"
                                                            whileHover={{
                                                                scale: 1.1,
                                                                rotate: 5,
                                                            }}
                                                        >
                                                            <Badge
                                                                className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border-blue-500/30"
                                                                color="primary"
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {
                                                                    garment.modelCode
                                                                }
                                                            </Badge>
                                                            {garment.totalAssigned >
                                                                0 && (
                                                                <motion.div
                                                                    animate={{
                                                                        scale: 1,
                                                                    }}
                                                                    className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full"
                                                                    initial={{
                                                                        scale: 0,
                                                                    }}
                                                                />
                                                            )}
                                                        </motion.div>
                                                        <div className="flex items-center gap-3">
                                                            <span className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                                                                {
                                                                    garment.modelDescription
                                                                }
                                                            </span>
                                                            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm">
                                                                <motion.div
                                                                    className="w-4 h-4 rounded-full border-2 border-white dark:border-gray-700 shadow-lg"
                                                                    style={{
                                                                        backgroundColor:
                                                                            garment.colorHex ||
                                                                            "#94a3b8",
                                                                    }}
                                                                    whileHover={{
                                                                        scale: 1.2,
                                                                    }}
                                                                />
                                                                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                                                    {
                                                                        garment.colorName
                                                                    }
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-4">
                                                        <div className="text-right">
                                                            <motion.p
                                                                animate={{
                                                                    scale:
                                                                        garment.totalAssigned >
                                                                        0
                                                                            ? [
                                                                                  1,
                                                                                  1.05,
                                                                                  1,
                                                                              ]
                                                                            : 1,
                                                                }}
                                                                className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                                                                transition={{
                                                                    duration: 0.3,
                                                                }}
                                                            >
                                                                {
                                                                    garment.totalAssigned
                                                                }{" "}
                                                                /{" "}
                                                                {
                                                                    garment.totalAvailable
                                                                }
                                                            </motion.p>
                                                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                                                unidades
                                                            </p>
                                                        </div>
                                                        <motion.div
                                                            animate={{
                                                                rotate: isExpanded
                                                                    ? 180
                                                                    : 0,
                                                            }}
                                                            className="p-2 rounded-lg bg-gray-100/50 dark:bg-gray-800/50 group-hover:bg-gray-200/50 dark:group-hover:bg-gray-700/50 transition-colors"
                                                            transition={{
                                                                duration: 0.3,
                                                                type: "spring",
                                                            }}
                                                        >
                                                            {isExpanded ? (
                                                                <ChevronUpIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                                            ) : (
                                                                <ChevronDownIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                                            )}
                                                        </motion.div>
                                                    </div>
                                                </div>

                                                {/* Enhanced Progress bar */}
                                                <div className="mt-4 relative">
                                                    <div className="absolute inset-0 flex items-center justify-center">
                                                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                                            {Math.round(
                                                                garment.assignmentRate,
                                                            )}
                                                            % completado
                                                        </span>
                                                    </div>
                                                    <Progress
                                                        classNames={{
                                                            base: "gap-0",
                                                            track: "bg-gray-200/50 dark:bg-gray-700/50",
                                                            indicator: `bg-gradient-to-r ${
                                                                garment.assignmentRate >=
                                                                80
                                                                    ? "from-green-400 to-emerald-500"
                                                                    : garment.assignmentRate >=
                                                                        50
                                                                      ? "from-amber-400 to-orange-500"
                                                                      : "from-blue-400 to-purple-500"
                                                            } shadow-lg`,
                                                        }}
                                                        size="md"
                                                        value={
                                                            garment.assignmentRate
                                                        }
                                                    />
                                                </div>
                                            </motion.div>

                                            {/* Contenido expandible con tallas */}
                                            <AnimatePresence>
                                                {isExpanded && (
                                                    <motion.div
                                                        animate={{
                                                            height: "auto",
                                                            opacity: 1,
                                                        }}
                                                        className="overflow-hidden"
                                                        exit={{
                                                            height: 0,
                                                            opacity: 0,
                                                        }}
                                                        initial={{
                                                            height: 0,
                                                            opacity: 0,
                                                        }}
                                                        transition={{
                                                            duration: 0.3,
                                                        }}
                                                    >
                                                        <div className="p-5 bg-gradient-to-b from-transparent to-gray-50/50 dark:to-gray-900/50 border-t border-white/20 dark:border-gray-700/30">
                                                            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                                                                {garment.sizesData.map(
                                                                    (
                                                                        sizeData,
                                                                        sizeIndex,
                                                                    ) => (
                                                                        <motion.div
                                                                            key={
                                                                                sizeData.id
                                                                            }
                                                                            animate={{
                                                                                scale: 1,
                                                                                opacity: 1,
                                                                                y: 0,
                                                                            }}
                                                                            className="relative group"
                                                                            initial={{
                                                                                scale: 0.8,
                                                                                opacity: 0,
                                                                                y: 20,
                                                                            }}
                                                                            transition={{
                                                                                delay:
                                                                                    sizeIndex *
                                                                                    0.05,
                                                                                type: "spring",
                                                                                stiffness: 200,
                                                                            }}
                                                                            whileHover={{
                                                                                scale: 1.05,
                                                                                y: -5,
                                                                            }}
                                                                        >
                                                                            <div
                                                                                className={`
                                                                    relative p-4 rounded-xl transition-all duration-300
                                                                    ${
                                                                        focusedSizeId ===
                                                                        sizeData.id
                                                                            ? "ring-2 ring-blue-500 ring-offset-2 shadow-2xl scale-105"
                                                                            : "shadow-lg hover:shadow-xl"
                                                                    }
                                                                    ${
                                                                        sizeData.hasError
                                                                            ? "bg-gradient-to-br from-red-500/20 to-pink-500/20 border-red-500/50"
                                                                            : sizeData.assigned >
                                                                                0
                                                                              ? "bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/30"
                                                                              : "bg-gradient-to-br from-white/80 to-gray-50/80 dark:from-gray-800/80 dark:to-gray-900/80 border-gray-200/50 dark:border-gray-700/50"
                                                                    }
                                                                    backdrop-blur-sm border cursor-pointer
                                                                    group-hover:transform group-hover:translate-y-[-2px]
                                                                `}
                                                                                onClick={() =>
                                                                                    setFocusedSizeId(
                                                                                        sizeData.id,
                                                                                    )
                                                                                }
                                                                            >
                                                                                {/* Intensity indicator */}
                                                                                <motion.div
                                                                                    className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10"
                                                                                    style={{
                                                                                        opacity:
                                                                                            (sizeData.availableQuantity /
                                                                                                maxAvailable) *
                                                                                            0.5,
                                                                                    }}
                                                                                />
                                                                                {/* Enhanced availability indicator */}
                                                                                {sizeData.code ===
                                                                                    garment.mostAvailableSizeCode && (
                                                                                    <motion.div
                                                                                        animate={{
                                                                                            scale: 1,
                                                                                            rotate: 0,
                                                                                        }}
                                                                                        className="absolute -top-2 -right-2 z-10"
                                                                                        initial={{
                                                                                            scale: 0,
                                                                                            rotate: -180,
                                                                                        }}
                                                                                        transition={{
                                                                                            delay: 0.5,
                                                                                            type: "spring",
                                                                                        }}
                                                                                    >
                                                                                        <Tooltip content="Mayor disponibilidad">
                                                                                            <div className="relative">
                                                                                                <motion.div
                                                                                                    animate={{
                                                                                                        rotate: [
                                                                                                            0,
                                                                                                            360,
                                                                                                        ],
                                                                                                    }}
                                                                                                    className="absolute inset-0 w-8 h-8 bg-gradient-to-r from-amber-400 to-yellow-400 rounded-full blur-md"
                                                                                                    transition={{
                                                                                                        duration: 20,
                                                                                                        repeat: Infinity,
                                                                                                        ease: "linear",
                                                                                                    }}
                                                                                                />
                                                                                                <Badge
                                                                                                    classNames={{
                                                                                                        badge: "border-0 bg-gradient-to-r from-amber-500 to-yellow-500 shadow-lg",
                                                                                                    }}
                                                                                                    color="warning"
                                                                                                    placement="top-right"
                                                                                                    size="sm"
                                                                                                >
                                                                                                    <FireIcon className="w-3 h-3" />
                                                                                                </Badge>
                                                                                            </div>
                                                                                        </Tooltip>
                                                                                    </motion.div>
                                                                                )}

                                                                                <div className="relative z-10 space-y-3">
                                                                                    <div className="text-center">
                                                                                        <motion.p
                                                                                            animate={{
                                                                                                scale:
                                                                                                    focusedSizeId ===
                                                                                                    sizeData.id
                                                                                                        ? 1.1
                                                                                                        : 1,
                                                                                            }}
                                                                                            className="text-sm font-bold text-gray-800 dark:text-gray-200 mb-1"
                                                                                        >
                                                                                            Talla{" "}
                                                                                            {
                                                                                                sizeData.code
                                                                                            }
                                                                                        </motion.p>
                                                                                        <div className="flex items-center justify-center gap-1">
                                                                                            <motion.span
                                                                                                animate={{
                                                                                                    scale:
                                                                                                        sizeData.assigned >
                                                                                                        0
                                                                                                            ? [
                                                                                                                  1,
                                                                                                                  1.1,
                                                                                                                  1,
                                                                                                              ]
                                                                                                            : 1,
                                                                                                }}
                                                                                                className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                                                                                                transition={{
                                                                                                    duration: 0.3,
                                                                                                }}
                                                                                            >
                                                                                                {sizeData.availableQuantity -
                                                                                                    sizeData.assigned}
                                                                                            </motion.span>
                                                                                            <span className="text-xs text-gray-500">
                                                                                                /
                                                                                            </span>
                                                                                            <span className="text-xs text-gray-600 dark:text-gray-400">
                                                                                                {
                                                                                                    sizeData.availableQuantity
                                                                                                }
                                                                                            </span>
                                                                                        </div>
                                                                                    </div>

                                                                                    {/* Enhanced Input */}
                                                                                    <motion.div
                                                                                        whileHover={{
                                                                                            scale: 1.02,
                                                                                        }}
                                                                                        whileTap={{
                                                                                            scale: 0.98,
                                                                                        }}
                                                                                    >
                                                                                        <Input
                                                                                            classNames={{
                                                                                                base: "w-full",
                                                                                                input: "text-center font-bold text-lg",
                                                                                                inputWrapper: `
                                                                                    backdrop-blur-sm transition-all duration-300
                                                                                    ${
                                                                                        sizeData.hasError
                                                                                            ? "bg-red-500/20 border-red-500 shadow-red-500/20 shadow-lg"
                                                                                            : sizeData.assigned >
                                                                                                0
                                                                                              ? "bg-green-500/20 border-green-500 shadow-green-500/20 shadow-lg"
                                                                                              : "bg-white/50 dark:bg-gray-800/50 border-gray-300 dark:border-gray-600"
                                                                                    }
                                                                                `,
                                                                                            }}
                                                                                            endContent={
                                                                                                sizeData.hasError ? (
                                                                                                    <Tooltip
                                                                                                        content={
                                                                                                            validationErrors[
                                                                                                                sizeData
                                                                                                                    .id
                                                                                                            ]
                                                                                                        }
                                                                                                    >
                                                                                                        <motion.div
                                                                                                            animate={{
                                                                                                                rotate: [
                                                                                                                    0,
                                                                                                                    -10,
                                                                                                                    10,
                                                                                                                    -10,
                                                                                                                    0,
                                                                                                                ],
                                                                                                            }}
                                                                                                            transition={{
                                                                                                                duration: 0.5,
                                                                                                                repeat: Infinity,
                                                                                                            }}
                                                                                                        >
                                                                                                            <ExclamationCircleIcon className="w-4 h-4 text-red-500" />
                                                                                                        </motion.div>
                                                                                                    </Tooltip>
                                                                                                ) : sizeData.assigned >
                                                                                                  0 ? (
                                                                                                    <motion.div
                                                                                                        animate={{
                                                                                                            scale: [
                                                                                                                0,
                                                                                                                1.2,
                                                                                                                1,
                                                                                                            ],
                                                                                                        }}
                                                                                                        initial={{
                                                                                                            scale: 0,
                                                                                                        }}
                                                                                                        transition={{
                                                                                                            duration: 0.3,
                                                                                                        }}
                                                                                                    >
                                                                                                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                                                                                                    </motion.div>
                                                                                                ) : null
                                                                                            }
                                                                                            max={
                                                                                                sizeData.availableQuantity
                                                                                            }
                                                                                            min={
                                                                                                0
                                                                                            }
                                                                                            size="sm"
                                                                                            startContent={
                                                                                                sizeData.assigned >
                                                                                                    0 && (
                                                                                                    <motion.div
                                                                                                        animate={{
                                                                                                            scale: 1,
                                                                                                        }}
                                                                                                        className="text-green-500"
                                                                                                        initial={{
                                                                                                            scale: 0,
                                                                                                        }}
                                                                                                    >
                                                                                                        <BoltIcon className="w-4 h-4" />
                                                                                                    </motion.div>
                                                                                                )
                                                                                            }
                                                                                            type="number"
                                                                                            value={sizeData.assigned.toString()}
                                                                                            onChange={(
                                                                                                e,
                                                                                            ) =>
                                                                                                handleQuantityInput(
                                                                                                    e,
                                                                                                    sizeData,
                                                                                                    garment,
                                                                                                )
                                                                                            }
                                                                                        />
                                                                                    </motion.div>

                                                                                    {/* Enhanced mini progress bar */}
                                                                                    <div className="relative">
                                                                                        <Progress
                                                                                            classNames={{
                                                                                                base: "max-w-full",
                                                                                                track: "h-2 bg-gray-200/50 dark:bg-gray-700/50",
                                                                                                indicator: `h-2 bg-gradient-to-r ${
                                                                                                    sizeData.availabilityPercent <=
                                                                                                    20
                                                                                                        ? "from-red-400 to-pink-500"
                                                                                                        : sizeData.availabilityPercent <=
                                                                                                            50
                                                                                                          ? "from-amber-400 to-orange-500"
                                                                                                          : "from-green-400 to-emerald-500"
                                                                                                } shadow-sm`,
                                                                                            }}
                                                                                            size="sm"
                                                                                            value={
                                                                                                100 -
                                                                                                sizeData.availabilityPercent
                                                                                            }
                                                                                        />
                                                                                        <motion.div
                                                                                            animate={{
                                                                                                x: [
                                                                                                    0,
                                                                                                    100,
                                                                                                    0,
                                                                                                ],
                                                                                                opacity:
                                                                                                    [
                                                                                                        0,
                                                                                                        0.5,
                                                                                                        0,
                                                                                                    ],
                                                                                            }}
                                                                                            className="absolute top-0 left-0 h-2 bg-white/30 rounded-full"
                                                                                            style={{
                                                                                                width: "20%",
                                                                                            }}
                                                                                            transition={{
                                                                                                duration: 2,
                                                                                                repeat: Infinity,
                                                                                                ease: "linear",
                                                                                            }}
                                                                                        />
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </motion.div>
                                                                    ),
                                                                )}
                                                            </div>

                                                            {/* Enhanced Quick Actions */}
                                                            <motion.div
                                                                animate={{
                                                                    opacity: 1,
                                                                    y: 0,
                                                                }}
                                                                className="mt-5 p-4 rounded-xl bg-gradient-to-r from-gray-50/50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-900/50 backdrop-blur-sm"
                                                                initial={{
                                                                    opacity: 0,
                                                                    y: 10,
                                                                }}
                                                                transition={{
                                                                    delay: 0.3,
                                                                }}
                                                            >
                                                                <div className="flex items-center justify-between mb-3">
                                                                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                                                        Acciones
                                                                        Rápidas
                                                                    </span>
                                                                    <motion.div
                                                                        animate={{
                                                                            scale: [
                                                                                1,
                                                                                1.1,
                                                                                1,
                                                                            ],
                                                                        }}
                                                                        transition={{
                                                                            duration: 2,
                                                                            repeat: Infinity,
                                                                        }}
                                                                    >
                                                                        <BoltIcon className="w-4 h-4 text-amber-500" />
                                                                    </motion.div>
                                                                </div>
                                                                <div className="flex flex-wrap gap-2">
                                                                    <motion.div
                                                                        whileHover={{
                                                                            scale: 1.05,
                                                                        }}
                                                                        whileTap={{
                                                                            scale: 0.95,
                                                                        }}
                                                                    >
                                                                        <Button
                                                                            className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 hover:from-blue-500/30 hover:to-indigo-500/30 border-blue-500/30"
                                                                            size="sm"
                                                                            startContent={
                                                                                <ChartBarIcon className="w-4 h-4" />
                                                                            }
                                                                            variant="flat"
                                                                            onPress={() => {
                                                                                // Asignar proporcionalmente
                                                                                const totalToAssign =
                                                                                    Math.floor(
                                                                                        garment.totalAvailable *
                                                                                            0.5,
                                                                                    );
                                                                                let remaining =
                                                                                    totalToAssign;

                                                                                garment.sizesData.forEach(
                                                                                    (
                                                                                        size,
                                                                                    ) => {
                                                                                        const proportion =
                                                                                            size.availableQuantity /
                                                                                            garment.totalAvailable;
                                                                                        const toAssign =
                                                                                            Math.min(
                                                                                                Math.floor(
                                                                                                    totalToAssign *
                                                                                                        proportion,
                                                                                                ),
                                                                                                size.availableQuantity,
                                                                                                remaining,
                                                                                            );

                                                                                        if (
                                                                                            toAssign >
                                                                                            0
                                                                                        ) {
                                                                                            onQuantityChange(
                                                                                                size.id,
                                                                                                toAssign,
                                                                                                orderId,
                                                                                                garment.id,
                                                                                                garment.modelCode,
                                                                                                garment.colorName,
                                                                                                size.code,
                                                                                                size.availableQuantity,
                                                                                            );
                                                                                            remaining -=
                                                                                                toAssign;
                                                                                        }
                                                                                    },
                                                                                );
                                                                            }}
                                                                        >
                                                                            Asignar
                                                                            50%
                                                                        </Button>
                                                                    </motion.div>
                                                                    <motion.div
                                                                        whileHover={{
                                                                            scale: 1.05,
                                                                        }}
                                                                        whileTap={{
                                                                            scale: 0.95,
                                                                        }}
                                                                    >
                                                                        <Button
                                                                            className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 border-green-500/30"
                                                                            size="sm"
                                                                            startContent={
                                                                                <ArrowsPointingOutIcon className="w-4 h-4" />
                                                                            }
                                                                            variant="flat"
                                                                            onPress={() => {
                                                                                // Asignar todo
                                                                                garment.sizesData.forEach(
                                                                                    (
                                                                                        size,
                                                                                    ) => {
                                                                                        onQuantityChange(
                                                                                            size.id,
                                                                                            size.availableQuantity,
                                                                                            orderId,
                                                                                            garment.id,
                                                                                            garment.modelCode,
                                                                                            garment.colorName,
                                                                                            size.code,
                                                                                            size.availableQuantity,
                                                                                        );
                                                                                    },
                                                                                );
                                                                            }}
                                                                        >
                                                                            Asignar
                                                                            Todo
                                                                        </Button>
                                                                    </motion.div>
                                                                    <motion.div
                                                                        whileHover={{
                                                                            scale: 1.05,
                                                                        }}
                                                                        whileTap={{
                                                                            scale: 0.95,
                                                                        }}
                                                                    >
                                                                        <Button
                                                                            className="bg-gradient-to-r from-gray-500/20 to-slate-500/20 hover:from-gray-500/30 hover:to-slate-500/30 border-gray-500/30"
                                                                            size="sm"
                                                                            startContent={
                                                                                <ArrowsPointingInIcon className="w-4 h-4" />
                                                                            }
                                                                            variant="flat"
                                                                            onPress={() => {
                                                                                // Limpiar asignaciones
                                                                                garment.sizesData.forEach(
                                                                                    (
                                                                                        size,
                                                                                    ) => {
                                                                                        onQuantityChange(
                                                                                            size.id,
                                                                                            0,
                                                                                            orderId,
                                                                                            garment.id,
                                                                                            garment.modelCode,
                                                                                            garment.colorName,
                                                                                            size.code,
                                                                                            size.availableQuantity,
                                                                                        );
                                                                                    },
                                                                                );
                                                                            }}
                                                                        >
                                                                            Limpiar
                                                                        </Button>
                                                                    </motion.div>
                                                                </div>
                                                            </motion.div>
                                                        </div>
                                                    </motion.div>
                                                )}
                                            </AnimatePresence>
                                        </div>
                                    </motion.div>
                                );
                            })}
                        </div>

                        {/* Enhanced Global Summary */}
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-6"
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ delay: 0.5, type: "spring" }}
                        >
                            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-xl">
                                {/* Animated background */}
                                <motion.div
                                    animate={{ x: [0, 100, 0] }}
                                    className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"
                                    transition={{
                                        duration: 20,
                                        repeat: Infinity,
                                        ease: "linear",
                                    }}
                                />

                                <div className="relative z-10 p-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <motion.div
                                                className="p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm"
                                                whileHover={{
                                                    scale: 1.1,
                                                    rotate: 5,
                                                }}
                                            >
                                                <InformationCircleIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                            </motion.div>
                                            <div>
                                                <span className="text-sm font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                                    Resumen de Asignación
                                                </span>
                                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                                                    Estado actual de la
                                                    distribución
                                                </p>
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-3 gap-6">
                                            <motion.div
                                                className="text-center"
                                                whileHover={{ scale: 1.05 }}
                                            >
                                                <motion.p
                                                    className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
                                                    style={{
                                                        display: "inline-block",
                                                    }}
                                                >
                                                    {globalStats.totalAssigned}
                                                </motion.p>
                                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                                    Asignadas
                                                </p>
                                            </motion.div>
                                            <motion.div
                                                className="text-center"
                                                whileHover={{ scale: 1.05 }}
                                            >
                                                <p className="text-3xl font-bold text-gray-700 dark:text-gray-300">
                                                    {globalStats.totalAvailable -
                                                        globalStats.totalAssigned}
                                                </p>
                                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                                    Disponibles
                                                </p>
                                            </motion.div>
                                            <motion.div
                                                className="text-center"
                                                whileHover={{ scale: 1.05 }}
                                            >
                                                <motion.p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                                    {Math.round(
                                                        globalStats.avgAssignmentRate,
                                                    )}
                                                    %
                                                </motion.p>
                                                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                                    Completado
                                                </p>
                                            </motion.div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </CardBody>
            </Card>
        </motion.div>
    );
}
