import React, { ReactNode } from "react";
import { But<PERSON> } from "@heroui/react";

import { IconWrapper } from "./IconWrapper";

interface EmptyStateProps {
    title: string;
    description: string;
    buttonText?: string;
    action?: string;
    buttonIcon?: ReactNode;
    onAction?: () => void;
}

export default function EmptyState({
    title,
    description,
    buttonText,
    action,
    buttonIcon,
    onAction,
}: EmptyStateProps) {
    const displayButtonText = buttonText || action;

    return (
        <div className="flex h-[60vh] items-center justify-center">
            <div className="flex flex-col items-center p-8 text-center max-w-md">
                <IconWrapper
                    className="mb-4"
                    color="primary"
                    size="xl"
                    variant="soft"
                >
                    <svg
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                        />
                    </svg>
                </IconWrapper>
                <h2 className="text-2xl font-bold text-foreground mb-2">
                    {title}
                </h2>
                <p className="text-muted-foreground mb-6">{description}</p>

                {displayButtonText && onAction && (
                    <Button
                        color="primary"
                        size="lg"
                        startContent={buttonIcon}
                        onClick={onAction}
                    >
                        {displayButtonText}
                    </Button>
                )}
            </div>
        </div>
    );
}
