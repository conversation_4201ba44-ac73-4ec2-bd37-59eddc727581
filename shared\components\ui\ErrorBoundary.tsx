"use client";

import React, { Component, ReactNode } from "react";

import { Card, CardBody } from "@/shared/components/ui/hero-ui-client";

interface Props {
    children: ReactNode;
    fallback?: ReactNode;
}

interface State {
    hasError: boolean;
    error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // Check if this is the specific HeroUI focus error
        if (
            error.message.includes("Maximum update depth exceeded") &&
            error.stack?.includes("useHasTabbableChild")
        ) {
            console.warn(
                "HeroUI focus management error caught, attempting recovery...",
            );

            // Try to recover by resetting the error state after a brief delay
            setTimeout(() => {
                this.setState({ hasError: false, error: undefined });
            }, 100);

            return;
        }

        console.error("<PERSON>rror<PERSON>ou<PERSON><PERSON> caught an error:", error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            if (this.props.fallback) {
                return this.props.fallback;
            }

            return (
                <Card className="m-4">
                    <CardBody>
                        <div className="text-center py-8">
                            <h2 className="text-lg font-semibold text-red-600 mb-2">
                                Se produjo un error temporal
                            </h2>
                            <p className="text-gray-600 mb-4">
                                Estamos trabajando para resolverlo. La página se
                                recuperará automáticamente.
                            </p>
                            <button
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                                onClick={() =>
                                    this.setState({
                                        hasError: false,
                                        error: undefined,
                                    })
                                }
                            >
                                Reintentar
                            </button>
                        </div>
                    </CardBody>
                </Card>
            );
        }

        return this.props.children;
    }
}
