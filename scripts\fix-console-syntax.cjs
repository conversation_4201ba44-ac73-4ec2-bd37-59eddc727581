#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuración
const config = {
  // Directorios a procesar
  directories: ['app', 'features', 'shared'],
  
  // Archivos a procesar
  extensions: ['ts', 'tsx'],
  
  // Modo dry-run por defecto
  dryRun: process.argv.includes('--dry-run') || !process.argv.includes('--execute'),
};

// Función para procesar un archivo
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  let newLines = [];
  let changes = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    const prevLine = i > 0 ? lines[i - 1] : '';
    const prevTrimmed = prevLine.trim();
    const nextLine = i < lines.length - 1 ? lines[i + 1] : '';
    const nextTrimmed = nextLine.trim();
    
    // Detectar patrón problemático: línea con solo ); precedida por // REMOVED:
    if (trimmedLine === ');' || trimmedLine === ')') {
      // Verificar si la línea anterior contiene // REMOVED:
      let foundRemoved = false;
      let j = i - 1;
      
      // Buscar hacia atrás hasta encontrar // REMOVED: o una línea no relacionada
      while (j >= 0 && j > i - 5) {
        if (lines[j].includes('// REMOVED:')) {
          foundRemoved = true;
          break;
        }
        // Si encontramos algo que no es un string o comentario, parar
        if (lines[j].trim() && !lines[j].trim().startsWith('"') && !lines[j].trim().startsWith("'") && !lines[j].trim().endsWith(',')) {
          break;
        }
        j--;
      }
      
      if (foundRemoved) {
        // Omitir esta línea (no agregarla a newLines)
        changes++;
        console.log(`   🔧 Removiendo línea huérfana ${i + 1}: "${trimmedLine}"`);
        continue;
      }
    }
    
    // Detectar otro patrón: console.log( seguido inmediatamente por );
    if (line.includes('console.log(') && nextTrimmed === ');') {
      // Comentar toda la expresión en una línea
      newLines.push(line.replace(/console\.log\(/, '// REMOVED: console.log()'));
      // Saltar la siguiente línea );
      i++;
      changes++;
      console.log(`   🔧 Corrigiendo console.log en línea ${i + 1}`);
      continue;
    }
    
    // Agregar la línea sin cambios
    newLines.push(line);
  }
  
  if (changes > 0) {
    console.log(`\n📄 ${filePath}`);
    console.log(`   ✅ ${changes} correcciones aplicadas`);
    
    if (!config.dryRun) {
      // Escribir el archivo corregido
      fs.writeFileSync(filePath, newLines.join('\n'), 'utf8');
    }
  }
  
  return changes;
}

// Función principal
function main() {
  console.log('🔍 Buscando errores de sintaxis causados por console.* comentados...\n');
  console.log(`Modo: ${config.dryRun ? 'DRY RUN (no se harán cambios)' : 'EJECUTAR'}`);
  console.log(`Directorios: ${config.directories.join(', ')}\n`);
  
  let totalFiles = 0;
  let totalChanges = 0;
  
  // Procesar cada directorio
  config.directories.forEach((dir) => {
    config.extensions.forEach((ext) => {
      const pattern = path.join(dir, `**/*.${ext}`);
      const files = glob.sync(pattern);
      
      files.forEach((file) => {
        const changes = processFile(file);
        if (changes > 0) {
          totalFiles++;
          totalChanges += changes;
        }
      });
    });
  });
  
  // Resumen
  console.log('\n📊 Resumen:');
  console.log(`   - Archivos con errores corregidos: ${totalFiles}`);
  console.log(`   - Total de correcciones: ${totalChanges}`);
  
  if (config.dryRun) {
    console.log('\n💡 Para ejecutar las correcciones, usa: npm run fix-console-syntax -- --execute');
  }
}

// Ejecutar
main();