"use client";

import React from "react";
import {
    PlusIcon,
    TrashIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { <PERSON><PERSON>, <PERSON> } from "@/shared/components/ui/hero-ui-client";

interface Part {
    code: string;
}

interface OrderPartsStepProps {
    parts: Part[];
    errors: Record<string, string>;
    onAddPart: () => void;
    onRemovePart: (index: number) => void;
    onPartChange: (index: number, value: string) => void;
}

export function OrderPartsStep({
    parts,
    errors,
    onAddPart,
    onRemovePart,
    onPartChange,
}: OrderPartsStepProps) {
    // Check for duplicates
    const duplicates = new Set<string>();
    const seenCodes = new Set<string>();

    parts.forEach((part) => {
        const code = part.code.trim().toUpperCase();

        if (code && seenCodes.has(code)) {
            duplicates.add(code);
        }
        if (code) seenCodes.add(code);
    });

    return (
        <div className="space-y-4">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                    Agrega los códigos de partida para esta orden. Cada partida
                    debe tener un código único.
                </p>
            </div>

            {/* Error Messages */}
            {(errors.parts || errors.parts_duplicate) && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div className="flex items-start gap-2">
                        <ExclamationTriangleIcon className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                        <div className="space-y-1">
                            {errors.parts && (
                                <p className="text-sm text-red-700 dark:text-red-300">
                                    {errors.parts}
                                </p>
                            )}
                            {errors.parts_duplicate && (
                                <p className="text-sm text-red-700 dark:text-red-300">
                                    {errors.parts_duplicate}
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Parts List */}
            <div className="space-y-3">
                <AnimatePresence>
                    {parts.map((part, index) => {
                        const isDuplicate = duplicates.has(
                            part.code.trim().toUpperCase(),
                        );

                        return (
                            <motion.div
                                key={index}
                                animate={{ opacity: 1, y: 0 }}
                                className="group"
                                exit={{ opacity: 0, x: -100 }}
                                initial={{ opacity: 0, y: -20 }}
                                transition={{ duration: 0.2 }}
                            >
                                <div
                                    className={`
                  flex items-center gap-3 p-4 rounded-lg border transition-all
                  ${
                      isDuplicate
                          ? "border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20"
                          : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-primary dark:hover:border-primary"
                  }
                `}
                                >
                                    <div className="flex-shrink-0">
                                        <Chip
                                            color={
                                                isDuplicate
                                                    ? "danger"
                                                    : "primary"
                                            }
                                            size="sm"
                                            variant="flat"
                                        >
                                            {String(index + 1).padStart(2, "0")}
                                        </Chip>
                                    </div>

                                    <div className="flex-1">
                                        <input
                                            className={`
                        w-full px-3 py-2 rounded-lg border bg-transparent
                        focus:outline-none focus:ring-2 transition-all
                        ${
                            isDuplicate
                                ? "border-red-300 dark:border-red-700 focus:ring-red-500"
                                : "border-gray-200 dark:border-gray-700 focus:ring-primary focus:border-primary"
                        }
                      `}
                                            placeholder="Código de partida"
                                            type="text"
                                            value={part.code}
                                            onChange={(e) =>
                                                onPartChange(
                                                    index,
                                                    e.target.value,
                                                )
                                            }
                                        />
                                        {isDuplicate && (
                                            <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                                                Este código ya existe
                                            </p>
                                        )}
                                    </div>

                                    <div className="flex-shrink-0">
                                        <Button
                                            isIconOnly
                                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                                            color="danger"
                                            isDisabled={parts.length === 1}
                                            size="sm"
                                            variant="light"
                                            onPress={() => onRemovePart(index)}
                                        >
                                            <TrashIcon className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                            </motion.div>
                        );
                    })}
                </AnimatePresence>
            </div>

            {/* Add Button */}
            <div className="flex justify-center pt-4">
                <Button
                    className="border-dashed"
                    startContent={<PlusIcon className="w-4 h-4" />}
                    variant="bordered"
                    onPress={onAddPart}
                >
                    Agregar Partida
                </Button>
            </div>

            {/* Summary */}
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Total de partidas:
                    </span>
                    <Chip color="primary" size="sm" variant="flat">
                        {parts.filter((p) => p.code.trim()).length}
                    </Chip>
                </div>
            </div>
        </div>
    );
}
