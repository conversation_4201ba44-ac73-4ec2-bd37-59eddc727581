"use client";

import type { PackingHeaderProps } from "../types";

import React from "react";

import { formatDate } from "../utils/transform";
import styles from "../PackingPreview.module.css";

export const PackingHeader: React.FC<PackingHeaderProps> = ({
    company,
    document,
    editMode,
    onEdit,
}) => {
    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.currentTarget.textContent);
            }
        };

    return (
        <>
            <header className={styles.header}>
                <div className={styles.companyInfo}>
                    <h1>{company.name}</h1>
                    {company.slogan && <p>{company.slogan}</p>}
                    {company.rfc && (
                        <p className={styles.companyDetails}>{company.rfc}</p>
                    )}
                    {company.address && (
                        <p className={styles.companyDetails}>
                            {company.address}
                        </p>
                    )}
                    <div className={styles.companyContact}>
                        {company.phone && <span>Tel: {company.phone}</span>}
                        {company.email && company.phone && <span> | </span>}
                        {company.email && <span>Email: {company.email}</span>}
                    </div>
                </div>

                <div className={styles.documentInfo}>
                    <h2>{document.type}</h2>
                    <p className={styles.folioLine}>
                        <span className={styles.fontBold}>Folio: </span>
                        <span
                            suppressContentEditableWarning
                            className={editMode ? styles.editableField : ""}
                            contentEditable={editMode}
                            onBlur={handleEdit("document.folio")}
                        >
                            {document.folio}
                        </span>
                    </p>
                    <p className={styles.dateLine}>
                        <span className={styles.fontBold}>Fecha: </span>
                        {formatDate(document.date)}
                    </p>
                </div>
            </header>

            {/* Additional styles for print optimization */}
            <style jsx>{`
                @media print {
                    header {
                        margin-bottom: 2rem !important;
                    }
                }
            `}</style>
        </>
    );
};
