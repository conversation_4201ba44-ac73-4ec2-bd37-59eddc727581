"use client";

import React, { useCallback, useMemo } from "react";
import {
    Button,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
} from "@heroui/react";
import {
    XMarkIcon,
    ChevronDownIcon,
    ArrowPathIcon,
    TrashIcon,
    DocumentDuplicateIcon,
    ArrowDownTrayIcon,
    UserGroupIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { OrderStatus } from "@/features/orders/types/orders";

import { useSelection } from "./SelectionContext";

interface BatchActionProps {
    id: string;
    label: string;
    icon: React.ReactNode;
    color?:
        | "default"
        | "primary"
        | "secondary"
        | "success"
        | "warning"
        | "danger";
    handler: () => void;
    disabled?: boolean;
}

interface BatchActionBarProps {
    statuses: OrderStatus[];
    onUpdateStatus: (orderIds: string[], statusId: string) => Promise<void>;
    onAssignContractor: (orderIds: string[]) => Promise<void>;
    onDelete: (orderIds: string[]) => Promise<void>;
    onExport: (orderIds: string[], format: "csv" | "pdf") => Promise<void>;
    onDuplicate: (orderIds: string[]) => Promise<void>;
}

const BatchActionBar: React.FC<BatchActionBarProps> = ({
    statuses,
    onUpdateStatus,
    onAssignContractor,
    onDelete,
    onExport,
    onDuplicate,
}) => {
    const { selectedIds, getSelectedItems, deselectAll } = useSelection();
    const selectedItems = getSelectedItems();

    // Verificar si todos los elementos seleccionados tienen el mismo estado
    const allSameStatus = useMemo(() => {
        if (selectedItems.length <= 1) return true;
        const firstStatus = (selectedItems[0]?.status as any)?.id;

        return selectedItems.every(
            (item) => (item.status as any)?.id === firstStatus,
        );
    }, [selectedItems]);

    // Obtener las acciones disponibles según los elementos seleccionados
    const getAvailableActions = useCallback(() => {
        const primaryActions: BatchActionProps[] = [];
        const secondaryActions: BatchActionProps[] = [];

        // Acción de actualizar estado
        if (allSameStatus) {
            // Si todos tienen el mismo estado, sugerir el siguiente estado lógico
            const currentStatusId = (selectedItems[0]?.status as any)?.id;
            const currentStatusIndex = statuses.findIndex(
                (s) => s.id === currentStatusId,
            );

            if (
                currentStatusIndex >= 0 &&
                currentStatusIndex < statuses.length - 1
            ) {
                const nextStatus = statuses[currentStatusIndex + 1];

                primaryActions.push({
                    id: `status-${nextStatus.id}`,
                    label: `Mover a ${nextStatus.name}`,
                    icon: <ArrowPathIcon className="w-4 h-4" />,
                    color: "primary",
                    handler: () =>
                        onUpdateStatus(Array.from(selectedIds), nextStatus.id),
                });
            }
        } else {
            // Si tienen estados diferentes, mostrar dropdown con todos los estados
            primaryActions.push({
                id: "status-dropdown",
                label: "Actualizar estado",
                icon: <ArrowPathIcon className="w-4 h-4" />,
                color: "primary",
                handler: () => {}, // Esta acción se maneja en el dropdown
            });
        }

        // Acción de asignar contratista
        primaryActions.push({
            id: "assign-contractor",
            label: "Asignar contratista",
            icon: <UserGroupIcon className="w-4 h-4" />,
            handler: () => onAssignContractor(Array.from(selectedIds)),
        });

        // Acción de exportar
        secondaryActions.push({
            id: "export-csv",
            label: "Exportar a CSV",
            icon: <ArrowDownTrayIcon className="w-4 h-4" />,
            handler: () => onExport(Array.from(selectedIds), "csv"),
        });

        secondaryActions.push({
            id: "export-pdf",
            label: "Exportar a PDF",
            icon: <DocumentDuplicateIcon className="w-4 h-4" />,
            handler: () => onExport(Array.from(selectedIds), "pdf"),
        });

        // Acción de duplicar
        secondaryActions.push({
            id: "duplicate",
            label: "Duplicar órdenes",
            icon: <DocumentDuplicateIcon className="w-4 h-4" />,
            handler: () => onDuplicate(Array.from(selectedIds)),
        });

        // Acción de eliminar (siempre última)
        secondaryActions.push({
            id: "delete",
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            color: "danger",
            handler: () => onDelete(Array.from(selectedIds)),
        });

        return { primaryActions, secondaryActions };
    }, [
        selectedItems,
        selectedIds,
        statuses,
        allSameStatus,
        onUpdateStatus,
        onAssignContractor,
        onDelete,
        onExport,
        onDuplicate,
    ]);

    const { primaryActions, secondaryActions } = getAvailableActions();

    if (selectedIds.size === 0) return null;

    return (
        <motion.div
            animate={{ y: 0, opacity: 1 }}
            className="batch-action-bar fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-3 px-4 z-50"
            exit={{ y: 100, opacity: 0 }}
            initial={{ y: 100, opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            <div className="container mx-auto flex items-center justify-between">
                <div className="selected-count text-medium">
                    <span className="text-primary-600 font-bold">
                        {selectedIds.size}
                    </span>
                    <span className="ml-1">
                        {selectedIds.size === 1
                            ? "orden seleccionada"
                            : "órdenes seleccionadas"}
                    </span>
                </div>

                <div className="flex items-center space-x-2">
                    <Button
                        size="sm"
                        startContent={<XMarkIcon className="w-4 h-4" />}
                        variant="ghost"
                        onClick={deselectAll}
                    >
                        Cancelar
                    </Button>

                    <div className="primary-actions flex items-center space-x-2">
                        {/* Mostrar acciones primarias directamente */}
                        {primaryActions.map((action) =>
                            action.id === "status-dropdown" ? (
                                <Dropdown key={action.id}>
                                    <DropdownTrigger>
                                        <Button
                                            color={action.color || "default"}
                                            endContent={
                                                <ChevronDownIcon className="w-4 h-4" />
                                            }
                                            size="sm"
                                            startContent={action.icon}
                                        >
                                            {action.label}
                                        </Button>
                                    </DropdownTrigger>
                                    <DropdownMenu aria-label="Estados disponibles">
                                        {statuses.map((status) => (
                                            <DropdownItem
                                                key={status.id}
                                                startContent={
                                                    <div
                                                        className="w-3 h-3 rounded-full"
                                                        style={{
                                                            backgroundColor:
                                                                status.color ||
                                                                "#cbd5e1",
                                                        }}
                                                    />
                                                }
                                                onPress={() =>
                                                    onUpdateStatus(
                                                        Array.from(selectedIds),
                                                        status.id,
                                                    )
                                                }
                                            >
                                                {status.name}
                                            </DropdownItem>
                                        ))}
                                    </DropdownMenu>
                                </Dropdown>
                            ) : (
                                <Button
                                    key={action.id}
                                    color={action.color || "default"}
                                    disabled={action.disabled}
                                    size="sm"
                                    startContent={action.icon}
                                    onClick={action.handler}
                                >
                                    {action.label}
                                </Button>
                            ),
                        )}
                    </div>

                    {/* Dropdown para acciones secundarias */}
                    {secondaryActions.length > 0 && (
                        <Dropdown>
                            <DropdownTrigger>
                                <Button
                                    endContent={
                                        <ChevronDownIcon className="w-4 h-4" />
                                    }
                                    size="sm"
                                    variant="ghost"
                                >
                                    Más acciones
                                </Button>
                            </DropdownTrigger>
                            <DropdownMenu>
                                {secondaryActions.map((action) => (
                                    <DropdownItem
                                        key={action.id}
                                        color={action.color}
                                        startContent={action.icon}
                                        onPress={action.handler}
                                    >
                                        {action.label}
                                    </DropdownItem>
                                ))}
                            </DropdownMenu>
                        </Dropdown>
                    )}
                </div>
            </div>
        </motion.div>
    );
};

export default BatchActionBar;
