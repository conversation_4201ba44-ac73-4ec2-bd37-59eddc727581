"use server";

import { z } from "zod";
import { revalidatePath } from "next/cache";

import { prisma } from "@/shared/lib/prisma";
import { handleDbError } from "@/shared/lib/db";
import { auth } from "@/lib/auth-helpers";

const deleteRemissionSchema = z.object({
    id: z.string().min(1, "ID de remisión requerido"),
});

export async function deleteRemission(id: string) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            throw new Error("No autorizado");
        }

        // Verificar que el usuario es admin
        const user = await prisma.user.findUnique({
            where: { id: session.user.id },
            include: { role: true },
        });

        if (!user || user.role.name.toLowerCase() !== "admin") {
            throw new Error(
                "Solo los administradores pueden eliminar remisiones",
            );
        }

        const validated = deleteRemissionSchema.parse({ id });

        // Verificar que la remisión existe
        const remission = await prisma.remission.findUnique({
            where: { id: validated.id },
            select: {
                id: true,
                folio: true,
                remissionItems: {
                    select: { id: true },
                },
            },
        });

        if (!remission) {
            throw new Error("Remisión no encontrada");
        }

        // Hard delete con transacción
        const result = await prisma.$transaction(async (tx) => {
            // Registrar en el historial antes de eliminar
            await tx.remissionHistory.create({
                data: {
                    remissionId: validated.id,
                    action: "DELETED",
                    metadata: {
                        deletedAt: new Date().toISOString(),
                        deletedBy: user.email,
                        itemCount: remission.remissionItems.length,
                        folio: remission.folio,
                    },
                },
            });

            // Eliminar físicamente la remisión (esto eliminará en cascada los items y asignaciones)
            await tx.remission.delete({
                where: { id: validated.id },
            });

            return remission;
        });

        revalidatePath("/dashboard/remissions");

        return {
            success: true,
            data: result,
            message: "Remisión eliminada exitosamente",
        };
    } catch (error) {
        return handleDbError(error);
    }
}

// Función de restaurar removida ya que usamos hard delete
