// components/Notes.tsx

import React from "react";
import { Card, Button, Textarea, Select, SelectItem } from "@heroui/react";
import {
    CubeIcon,
    PlusIcon,
    CheckCircleIcon,
    InboxIcon,
    XCircleIcon,
    ClockIcon,
    ExclamationCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    DocumentIcon,
    ArrowPathIcon,
    PencilIcon,
    ArchiveBoxIcon,
    TagIcon,
    PaperAirplaneIcon,
    HomeIcon,
    TruckIcon,
    CheckBadgeIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import {
    NoteEntry,
    NoteStatusOption,
    NoteImportanceOption,
} from "../../core/types/types";

// Icon Mapping
const iconMap: Record<string, JSX.Element> = {
    CheckCircleIcon: <CheckCircleIcon className="w-4 h-4" />,
    InboxIcon: <InboxIcon className="w-4 h-4" />,
    XCircleIcon: <XCircleIcon className="w-4 h-4" />,
    CubeIcon: <CubeIcon className="w-4 h-4" />,
    ClockIcon: <ClockIcon className="w-4 h-4" />,
    ExclamationCircleIcon: <ExclamationCircleIcon className="w-4 h-4" />,
    ExclamationTriangleIcon: <ExclamationTriangleIcon className="w-4 h-4" />,
    DocumentIcon: <DocumentIcon className="w-4 h-4" />,
    InformationCircleIcon: <InformationCircleIcon className="w-4 h-4" />,
    ArrowPathIcon: <ArrowPathIcon className="w-4 h-4" />,
    PencilIcon: <PencilIcon className="w-4 h-4" />,
    ArchiveBoxIcon: <ArchiveBoxIcon className="w-4 h-4" />,
    TagIcon: <TagIcon className="w-4 h-4" />,
    PaperAirplaneIcon: <PaperAirplaneIcon className="w-4 h-4" />,
    HomeIcon: <HomeIcon className="w-4 h-4" />,
    TruckIcon: <TruckIcon className="w-4 h-4" />,
    CheckBadgeIcon: <CheckBadgeIcon className="w-4 h-4" />,
};

interface NotesProps {
    notes: NoteEntry[];
    noteStatuses: NoteStatusOption[];
    noteImportances: NoteImportanceOption[];
    onAddNote: () => void;
    onRemoveNote: (index: number) => void;
    onNoteChange: (
        index: number,
        field: keyof NoteEntry,
        value: string,
    ) => void;
}

const Notes: React.FC<NotesProps> = ({
    notes,
    noteStatuses,
    noteImportances,
    onAddNote,
    onRemoveNote,
    onNoteChange,
}) => {
    return (
        <Card className="shadow-md rounded-lg p-6 bg-white dark:bg-gray-800">
            <div className="flex items-center gap-2 mb-4">
                <CubeIcon className="w-6 h-6" />
                <h2 className="text-2xl font-bold">Notas</h2>
            </div>
            <AnimatePresence>
                {notes.map((note, nIndex) => (
                    <motion.div
                        key={nIndex}
                        animate={{ opacity: 1, y: 0 }}
                        className="border p-4 rounded-lg bg-white/80 dark:bg-gray-900/50 space-y-2 mb-4"
                        exit={{ opacity: 0, y: 10 }}
                        initial={{ opacity: 0, y: -10 }}
                    >
                        <div className="flex justify-between items-center">
                            <h3 className="text-md font-bold flex items-center">
                                <div className="rounded-full w-6 h-6 bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-2 text-sm text-blue-600 dark:text-blue-400 font-medium">
                                    {nIndex + 1}
                                </div>
                                Nota {nIndex + 1}
                            </h3>
                            <Button
                                color="danger"
                                variant="flat"
                                onPress={() => onRemoveNote(nIndex)}
                            >
                                Eliminar Nota
                            </Button>
                        </div>
                        <Textarea
                            label="Contenido de la Nota"
                            placeholder="Escribe aquí..."
                            value={note.content}
                            variant="bordered"
                            onChange={(e) =>
                                onNoteChange(nIndex, "content", e.target.value)
                            }
                        />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Estado de la Nota
                                </p>
                                <Select
                                    classNames={{
                                        trigger:
                                            "h-10 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/70 dark:to-gray-800/70 border border-gray-200 dark:border-gray-700 shadow-sm rounded-lg transition-all hover:shadow-md",
                                        listboxWrapper: "max-h-[300px]",
                                        base: "max-w-xs",
                                        value: "text-gray-700 dark:text-gray-300",
                                        listbox:
                                            "p-0 shadow-xl rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 overflow-hidden",
                                    }}
                                    popoverProps={{
                                        classNames: {
                                            content: "p-0 border-none",
                                        },
                                        motionProps: {
                                            variants: {
                                                enter: {
                                                    y: 0,
                                                    opacity: 1,
                                                    transition: {
                                                        y: {
                                                            type: "spring",
                                                            stiffness: 500,
                                                            damping: 30,
                                                        },
                                                        opacity: {
                                                            duration: 0.2,
                                                        },
                                                    },
                                                },
                                                exit: {
                                                    y: -10,
                                                    opacity: 0,
                                                    transition: {
                                                        duration: 0.2,
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                    renderValue={(_items) => {
                                        const selectedStatus =
                                            noteStatuses.find(
                                                (ns) => ns.id === note.statusId,
                                            );
                                        const IconComp =
                                            selectedStatus?.iconName
                                                ? iconMap[
                                                      selectedStatus.iconName
                                                  ]
                                                : null;

                                        return (
                                            <div className="flex items-center gap-2 py-0.5">
                                                {IconComp && (
                                                    <div className="relative">
                                                        <span
                                                            className="flex items-center justify-center w-7 h-7 rounded-full shadow-sm"
                                                            style={{
                                                                backgroundColor:
                                                                    selectedStatus?.color ||
                                                                    "#CBD5E1",
                                                                color: "#FFFFFF",
                                                            }}
                                                        >
                                                            {IconComp}
                                                        </span>
                                                        <span
                                                            className="absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse"
                                                            style={{
                                                                backgroundColor:
                                                                    selectedStatus?.color ||
                                                                    "#CBD5E1",
                                                            }}
                                                        />
                                                    </div>
                                                )}
                                                <div className="flex flex-col">
                                                    <span className="font-medium text-sm">
                                                        {selectedStatus?.name ||
                                                            "Seleccionar estado"}
                                                    </span>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                                        {selectedStatus
                                                            ? "Estado actual"
                                                            : "Sin seleccionar"}
                                                    </span>
                                                </div>
                                            </div>
                                        );
                                    }}
                                    selectedKeys={new Set([note.statusId])}
                                    variant="flat"
                                    onSelectionChange={(keys) => {
                                        const [val] = Array.from(keys);

                                        onNoteChange(
                                            nIndex,
                                            "statusId",
                                            val as string,
                                        );
                                    }}
                                >
                                    <>
                                        <SelectItem
                                            key="header"
                                            className="p-0 m-0"
                                            textValue=""
                                        >
                                            <div className="p-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                                                <p className="text-xs font-semibold text-gray-500 dark:text-gray-400 px-2">
                                                    SELECCIONA UN ESTADO
                                                </p>
                                            </div>
                                        </SelectItem>

                                        {noteStatuses.map((ns) => {
                                            const IconComp = ns.iconName
                                                ? iconMap[ns.iconName]
                                                : null;

                                            return (
                                                <SelectItem
                                                    key={ns.id}
                                                    classNames={{
                                                        base: "data-[hover=true]:bg-gray-100 dark:data-[hover=true]:bg-gray-800 rounded-none transition-colors m-0 first:mt-1 last:mb-1 py-2.5",
                                                        title: "font-medium text-gray-700 dark:text-gray-300",
                                                    }}
                                                    textValue={ns.name}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <div className="relative">
                                                            {IconComp && (
                                                                <span
                                                                    className="flex items-center justify-center w-8 h-8 rounded-full shadow-sm transition-transform duration-200 hover:scale-110"
                                                                    style={{
                                                                        backgroundColor:
                                                                            ns.color ||
                                                                            "#CBD5E1",
                                                                        color: "#FFFFFF",
                                                                    }}
                                                                >
                                                                    {IconComp}
                                                                </span>
                                                            )}
                                                            <span
                                                                className="absolute top-0 right-0 w-2 h-2 rounded-full"
                                                                style={{
                                                                    backgroundColor:
                                                                        ns.color ||
                                                                        "#CBD5E1",
                                                                }}
                                                            />
                                                        </div>
                                                        <div className="flex flex-col">
                                                            <span className="font-semibold text-gray-800 dark:text-gray-200">
                                                                {ns.name}
                                                            </span>
                                                            <span
                                                                className="text-xs"
                                                                style={{
                                                                    color:
                                                                        ns.color ||
                                                                        "#CBD5E1",
                                                                }}
                                                            >
                                                                {ns.id ===
                                                                    "pending" &&
                                                                    "Para tareas pendientes"}
                                                                {ns.id ===
                                                                    "completed" &&
                                                                    "Para tareas completadas"}
                                                                {ns.id ===
                                                                    "info" &&
                                                                    "Para información general"}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </SelectItem>
                                            );
                                        })}
                                    </>
                                </Select>
                            </div>

                            <div>
                                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Importancia
                                </p>
                                <Select
                                    classNames={{
                                        trigger:
                                            "h-10 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900/70 dark:to-gray-800/70 border border-gray-200 dark:border-gray-700 shadow-sm rounded-lg transition-all hover:shadow-md",
                                        listboxWrapper: "max-h-[300px]",
                                        base: "max-w-xs",
                                        value: "text-gray-700 dark:text-gray-300",
                                        listbox:
                                            "p-0 shadow-xl rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 overflow-hidden",
                                    }}
                                    popoverProps={{
                                        classNames: {
                                            content: "p-0 border-none",
                                        },
                                        motionProps: {
                                            variants: {
                                                enter: {
                                                    y: 0,
                                                    opacity: 1,
                                                    transition: {
                                                        y: {
                                                            type: "spring",
                                                            stiffness: 500,
                                                            damping: 30,
                                                        },
                                                        opacity: {
                                                            duration: 0.2,
                                                        },
                                                    },
                                                },
                                                exit: {
                                                    y: -10,
                                                    opacity: 0,
                                                    transition: {
                                                        duration: 0.2,
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                    renderValue={(_items) => {
                                        const selectedImportance =
                                            noteImportances.find(
                                                (ni) =>
                                                    ni.id === note.importanceId,
                                            );
                                        const IconComp =
                                            selectedImportance?.iconName
                                                ? iconMap[
                                                      selectedImportance
                                                          .iconName
                                                  ]
                                                : null;

                                        return (
                                            <div className="flex items-center gap-2 py-0.5">
                                                {IconComp && (
                                                    <div className="relative">
                                                        <span
                                                            className="flex items-center justify-center w-7 h-7 rounded-full shadow-sm"
                                                            style={{
                                                                backgroundColor:
                                                                    selectedImportance?.color ||
                                                                    "#CBD5E1",
                                                                color: "#FFFFFF",
                                                            }}
                                                        >
                                                            {IconComp}
                                                        </span>
                                                        <span
                                                            className="absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse"
                                                            style={{
                                                                backgroundColor:
                                                                    selectedImportance?.color ||
                                                                    "#CBD5E1",
                                                            }}
                                                        />
                                                    </div>
                                                )}
                                                <div className="flex flex-col">
                                                    <span className="font-medium text-sm">
                                                        {selectedImportance?.name ||
                                                            "Seleccionar importancia"}
                                                    </span>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                                        {selectedImportance
                                                            ? "Importancia actual"
                                                            : "Sin seleccionar"}
                                                    </span>
                                                </div>
                                            </div>
                                        );
                                    }}
                                    selectedKeys={new Set([note.importanceId])}
                                    variant="flat"
                                    onSelectionChange={(keys) => {
                                        const [val] = Array.from(keys);

                                        onNoteChange(
                                            nIndex,
                                            "importanceId",
                                            val as string,
                                        );
                                    }}
                                >
                                    <>
                                        <SelectItem
                                            key="header-importance"
                                            className="p-0 m-0"
                                            textValue=""
                                        >
                                            <div className="p-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                                                <p className="text-xs font-semibold text-gray-500 dark:text-gray-400 px-2">
                                                    SELECCIONA UNA IMPORTANCIA
                                                </p>
                                            </div>
                                        </SelectItem>

                                        {noteImportances.map((ni) => {
                                            const IconComp = ni.iconName
                                                ? iconMap[ni.iconName]
                                                : null;

                                            return (
                                                <SelectItem
                                                    key={ni.id}
                                                    classNames={{
                                                        base: "data-[hover=true]:bg-gray-100 dark:data-[hover=true]:bg-gray-800 rounded-none transition-colors m-0 first:mt-1 last:mb-1 py-2.5",
                                                        title: "font-medium text-gray-700 dark:text-gray-300",
                                                    }}
                                                    textValue={ni.name}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <div className="relative">
                                                            {IconComp && (
                                                                <span
                                                                    className="flex items-center justify-center w-8 h-8 rounded-full shadow-sm transition-transform duration-200 hover:scale-110"
                                                                    style={{
                                                                        backgroundColor:
                                                                            ni.color ||
                                                                            "#CBD5E1",
                                                                        color: "#FFFFFF",
                                                                    }}
                                                                >
                                                                    {IconComp}
                                                                </span>
                                                            )}
                                                            <span
                                                                className="absolute top-0 right-0 w-2 h-2 rounded-full"
                                                                style={{
                                                                    backgroundColor:
                                                                        ni.color ||
                                                                        "#CBD5E1",
                                                                }}
                                                            />
                                                        </div>
                                                        <div className="flex flex-col">
                                                            <span className="font-semibold text-gray-800 dark:text-gray-200">
                                                                {ni.name}
                                                            </span>
                                                            <span
                                                                className="text-xs"
                                                                style={{
                                                                    color:
                                                                        ni.color ||
                                                                        "#CBD5E1",
                                                                }}
                                                            >
                                                                Importancia
                                                            </span>
                                                        </div>
                                                    </div>
                                                </SelectItem>
                                            );
                                        })}
                                    </>
                                </Select>
                            </div>
                        </div>
                    </motion.div>
                ))}
            </AnimatePresence>

            <Button
                className="w-full mt-4"
                color="primary"
                startContent={<PlusIcon className="w-4 h-4" />}
                onPress={onAddNote}
            >
                Agregar Nota
            </Button>
        </Card>
    );
};

export default Notes;
