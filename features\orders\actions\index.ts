"use server";

// Este archivo centraliza todas las exportaciones de las funciones asíncronas de Server Actions
// relacionadas con órdenes para mantener la compatibilidad con el código existente

// Importamos las funciones asíncronas desde los módulos específicos
import {
    getOrders,
    getOrder,
    getFormData,
    getOrderStatuses,
    getUpcomingOrders,
    getOrderTrends,
    getOrderBasicData,
    getOrderEditData,
} from "./query";
import { createOrder, submitOrder } from "./create";
import { updateOrder, updateOrderWithDetails } from "./update";
import { deleteOrder } from "./delete";
import { checkAndCreateOrderParts, migrateAllOrdersToHaveParts } from "./parts";
// No re-exportamos utils porque contiene interfaces y funciones no asíncronas

// Exportamos únicamente las funciones asíncronas para Server Actions
export {
    // Query functions
    getOrders,
    getOrder,
    getFormData,
    getOrderStatuses,
    getUpcomingOrders,
    getOrderTrends,
    getOrderBasicData,
    getOrderEditData,

    // Create functions
    createOrder,
    submitOrder,

    // Update functions
    updateOrder,
    updateOrderWithDetails,

    // Delete functions
    deleteOrder,

    // Parts functions
    checkAndCreateOrderParts,
    migrateAllOrdersToHaveParts,
};
