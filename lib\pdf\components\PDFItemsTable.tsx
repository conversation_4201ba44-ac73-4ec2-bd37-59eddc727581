import React from "react";
import { View, Text } from "@react-pdf/renderer";

import {
    remissionStyles as styles,
    tableColumnWidths,
} from "../styles/remission";

interface RemissionItem {
    orderNumber: string;
    model: string;
    color: string;
    quantity: number;
    quantityDelivered: number;
    pendingQuantity: number;
    unitPrice: number;
    totalPrice: number;
}

interface PDFItemsTableProps {
    items: RemissionItem[];
}

export const PDFItemsTable: React.FC<PDFItemsTableProps> = ({ items }) => {
    const totals = items.reduce(
        (acc, item) => ({
            quantity: acc.quantity + item.quantity,
            delivered: acc.delivered + item.quantityDelivered,
            pending: acc.pending + item.pendingQuantity,
            total: acc.total + item.totalPrice,
        }),
        { quantity: 0, delivered: 0, pending: 0, total: 0 },
    );

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat("es-MX", {
            style: "currency",
            currency: "MXN",
        }).format(amount);
    };

    return (
        <View style={styles.table}>
            {/* Table Header */}
            <View style={styles.tableHeader}>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        { width: tableColumnWidths.orderNumber },
                    ]}
                >
                    PEDIDO
                </Text>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        styles.tableCellModel,
                        { width: tableColumnWidths.model },
                    ]}
                >
                    MODELO
                </Text>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        styles.tableCellColor,
                        { width: tableColumnWidths.color },
                    ]}
                >
                    COLOR
                </Text>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        { width: tableColumnWidths.quantity },
                    ]}
                >
                    CANT.
                </Text>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        { width: tableColumnWidths.delivered },
                    ]}
                >
                    ENTR.
                </Text>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        { width: tableColumnWidths.pending },
                    ]}
                >
                    PEND.
                </Text>
                <Text
                    style={[
                        styles.tableHeaderCell,
                        { width: tableColumnWidths.unitPrice },
                    ]}
                >
                    P. UNIT.
                </Text>
            </View>

            {/* Table Rows */}
            {items.map((item, index) => (
                <View
                    key={index}
                    style={[
                        styles.tableRow,
                        index % 2 === 1 ? styles.tableRowAlternate : {},
                    ]}
                >
                    <Text
                        style={[
                            styles.tableCell,
                            { width: tableColumnWidths.orderNumber },
                        ]}
                    >
                        {item.orderNumber}
                    </Text>
                    <Text
                        style={[
                            styles.tableCell,
                            styles.tableCellModel,
                            { width: tableColumnWidths.model },
                        ]}
                    >
                        {item.model}
                    </Text>
                    <Text
                        style={[
                            styles.tableCell,
                            styles.tableCellColor,
                            { width: tableColumnWidths.color },
                        ]}
                    >
                        {item.color}
                    </Text>
                    <Text
                        style={[
                            styles.tableCell,
                            styles.tableCellQuantity,
                            { width: tableColumnWidths.quantity },
                        ]}
                    >
                        {item.quantity}
                    </Text>
                    <Text
                        style={[
                            styles.tableCell,
                            styles.tableCellQuantity,
                            { width: tableColumnWidths.delivered },
                        ]}
                    >
                        {item.quantityDelivered}
                    </Text>
                    <Text
                        style={[
                            styles.tableCell,
                            styles.tableCellQuantity,
                            { width: tableColumnWidths.pending },
                        ]}
                    >
                        {item.pendingQuantity}
                    </Text>
                    <Text
                        style={[
                            styles.tableCell,
                            { width: tableColumnWidths.unitPrice },
                        ]}
                    >
                        {formatCurrency(item.unitPrice)}
                    </Text>
                </View>
            ))}

            {/* Table Footer with Totals */}
            <View style={styles.tableFooter}>
                <Text style={[styles.totalLabel, { width: "45%" }]}>
                    TOTALES:
                </Text>
                <Text
                    style={[
                        styles.totalValue,
                        { width: tableColumnWidths.quantity },
                    ]}
                >
                    {totals.quantity}
                </Text>
                <Text
                    style={[
                        styles.totalValue,
                        { width: tableColumnWidths.delivered },
                    ]}
                >
                    {totals.delivered}
                </Text>
                <Text
                    style={[
                        styles.totalValue,
                        { width: tableColumnWidths.pending },
                    ]}
                >
                    {totals.pending}
                </Text>
                <Text
                    style={[
                        styles.totalValue,
                        { width: tableColumnWidths.unitPrice },
                    ]}
                >
                    {formatCurrency(totals.total)}
                </Text>
            </View>
        </View>
    );
};
