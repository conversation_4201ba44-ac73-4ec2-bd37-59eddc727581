"use client";

import React from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { RemissionPreview } from "@/types/remission";

interface HeaderProps {
    data: RemissionPreview;
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

export const Header: React.FC<HeaderProps> = ({ data, editMode, onEdit }) => {
    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.currentTarget.textContent);
            }
        };

    return (
        <header className="mb-8">
            <div className="flex justify-between items-start">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800">LOHARI</h1>
                    <p className="text-sm text-gray-600">
                        Tu estilo, nuestra pasión
                    </p>
                </div>

                <div className="text-right">
                    <h2 className="text-xl font-semibold text-gray-800">
                        REMISIÓN
                    </h2>
                    <p className="text-lg text-gray-700">
                        <span className="font-medium">Folio: </span>
                        <span
                            suppressContentEditableWarning
                            className={editMode ? "editable-field" : ""}
                            contentEditable={editMode}
                            onBlur={handleEdit("folio")}
                        >
                            {data.folio}
                        </span>
                    </p>
                    <p className="text-sm text-gray-600">
                        {format(
                            new Date((data as any).date),
                            "d 'de' MMMM 'de' yyyy",
                            {
                                locale: es,
                            },
                        )}
                    </p>
                </div>
            </div>
        </header>
    );
};
