"use client";

import { useState } from "react";
import {
    Card,
    CardBody,
    Input,
    Select,
    SelectItem,
    Button,
    DateRangePicker,
    Chip,
} from "@heroui/react";
import {
    Search,
    Filter,
    X,
    Calendar,
    Package,
    Truck,
    CheckCircle,
} from "lucide-react";

import { PackingFilters } from "@/lib/types/packing";

import { CustomerSubClientSelector } from "./CustomerSubClientSelector";

interface EnhancedPackingFiltersProps {
    filters: PackingFilters;
    onFiltersChange: (filters: PackingFilters) => void;
    onClearFilters: () => void;
    isLoading?: boolean;
}

export function EnhancedPackingFilters({
    filters,
    onFiltersChange,
    onClearFilters,
    isLoading = false,
}: EnhancedPackingFiltersProps) {
    const [isExpanded, setIsExpanded] = useState(false);

    const handleCustomerChange = (value: {
        customerId: string;
        subCustomerId?: string;
    }) => {
        onFiltersChange({
            ...filters,
            customerId: value.customerId,
            subCustomerId: value.subCustomerId,
        });
    };

    const handleDateRangeChange = (range: {
        start: Date | null;
        end: Date | null;
    }) => {
        onFiltersChange({
            ...filters,
            dateFrom: range.start,
            dateTo: range.end,
        });
    };

    const activeFilterCount = Object.values(filters).filter(
        (value) => value !== undefined && value !== null && value !== "",
    ).length;

    return (
        <Card className="mb-6">
            <CardBody>
                <div className="space-y-4">
                    {/* Main search and toggle */}
                    <div className="flex gap-4 items-center">
                        <Input
                            className="flex-1"
                            isDisabled={isLoading}
                            placeholder="Buscar por folio, código o notas..."
                            startContent={
                                <Search className="w-4 h-4 text-gray-400" />
                            }
                            value={filters.search || ""}
                            onChange={(e) =>
                                onFiltersChange({
                                    ...filters,
                                    search: e.target.value,
                                })
                            }
                        />

                        <Button
                            endContent={
                                activeFilterCount > 0 && (
                                    <Chip
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {activeFilterCount}
                                    </Chip>
                                )
                            }
                            startContent={<Filter className="w-4 h-4" />}
                            variant="bordered"
                            onPress={() => setIsExpanded(!isExpanded)}
                        >
                            Filtros
                        </Button>

                        {activeFilterCount > 0 && (
                            <Button
                                color="danger"
                                startContent={<X className="w-4 h-4" />}
                                variant="light"
                                onPress={onClearFilters}
                            >
                                Limpiar
                            </Button>
                        )}
                    </div>

                    {/* Expanded filters */}
                    {isExpanded && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t">
                            {/* Customer and sub-client selector */}
                            <div>
                                <CustomerSubClientSelector
                                    value={{
                                        customerId: filters.customerId,
                                        subCustomerId: filters.subCustomerId,
                                    }}
                                    onChange={handleCustomerChange}
                                />
                            </div>

                            {/* Date range */}
                            <div>
                                <DateRangePicker
                                    label="Rango de fechas"
                                    startContent={
                                        <Calendar className="w-4 h-4 text-gray-400" />
                                    }
                                    value={
                                        filters.dateFrom && filters.dateTo
                                            ? {
                                                  start: filters.dateFrom as any,
                                                  end: filters.dateTo as any,
                                              }
                                            : null
                                    }
                                    onChange={handleDateRangeChange as any}
                                />
                            </div>

                            {/* Status filter */}
                            <div>
                                <Select
                                    label="Estado"
                                    placeholder="Todos los estados"
                                    selectedKeys={
                                        filters.statusId
                                            ? [filters.statusId]
                                            : []
                                    }
                                    startContent={
                                        <Package className="w-4 h-4 text-gray-400" />
                                    }
                                    onChange={(e) =>
                                        onFiltersChange({
                                            ...filters,
                                            statusId: e.target.value,
                                        })
                                    }
                                >
                                    {/* TODO: Load actual statuses */}
                                    <SelectItem key="draft">
                                        Borrador
                                    </SelectItem>
                                    <SelectItem key="confirmed">
                                        Confirmado
                                    </SelectItem>
                                    <SelectItem key="delivered">
                                        Entregado
                                    </SelectItem>
                                    <SelectItem key="cancelled">
                                        Cancelado
                                    </SelectItem>
                                </Select>
                            </div>

                            {/* Quality type filter */}
                            <div>
                                <Select
                                    label="Tipo de calidad"
                                    placeholder="Todas las calidades"
                                    selectedKeys={
                                        filters.qualityType
                                            ? [filters.qualityType]
                                            : []
                                    }
                                    startContent={
                                        <CheckCircle className="w-4 h-4 text-gray-400" />
                                    }
                                    onChange={(e) =>
                                        onFiltersChange({
                                            ...filters,
                                            qualityType: e.target.value as any,
                                        })
                                    }
                                >
                                    <SelectItem key="primera">
                                        Primera
                                    </SelectItem>
                                    <SelectItem key="segunda">
                                        Segunda
                                    </SelectItem>
                                    <SelectItem key="manchada">
                                        Manchada
                                    </SelectItem>
                                    <SelectItem key="incompleta">
                                        Incompleta
                                    </SelectItem>
                                </Select>
                            </div>

                            {/* Packing type filter */}
                            <div>
                                <Select
                                    label="Tipo de empaque"
                                    placeholder="Todos los tipos"
                                    selectedKeys={
                                        filters.packingType
                                            ? [filters.packingType]
                                            : []
                                    }
                                    startContent={
                                        <Package className="w-4 h-4 text-gray-400" />
                                    }
                                    onChange={(e) =>
                                        onFiltersChange({
                                            ...filters,
                                            packingType: e.target.value as any,
                                        })
                                    }
                                >
                                    <SelectItem key="cajas">
                                        Solo Cajas
                                    </SelectItem>
                                    <SelectItem key="bolsas">
                                        Solo Bolsas
                                    </SelectItem>
                                    <SelectItem key="mixto">Mixto</SelectItem>
                                </Select>
                            </div>

                            {/* Transport/Receiver filter */}
                            <div>
                                <Select
                                    label="Firma de recepción"
                                    placeholder="Todos"
                                    startContent={
                                        <Truck className="w-4 h-4 text-gray-400" />
                                    }
                                >
                                    <SelectItem key="with-signature">
                                        Con firma de recibido
                                    </SelectItem>
                                    <SelectItem key="without-signature">
                                        Sin firma de recibido
                                    </SelectItem>
                                </Select>
                            </div>
                        </div>
                    )}
                </div>
            </CardBody>
        </Card>
    );
}
