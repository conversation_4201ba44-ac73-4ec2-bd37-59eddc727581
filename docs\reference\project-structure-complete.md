# Documentación Completa de Estructura del Proyecto Lohari

## 📁 Estructura de Directorios Detallada

### `/app/` - Next.js App Router

#### Estructura:
```
app/
├── (auth)/          # Grupo de rutas de autenticación
│   ├── login/
│   └── register/
├── api/             # Rutas API
│   ├── auth/        # NextAuth endpoints
│   ├── remissions/  # API de remisiones
│   └── health/      # Health check
├── dashboard/       # Aplicación principal
│   ├── orders/      # Gestión de órdenes
│   ├── customers/   # Gestión de clientes
│   ├── contractors/ # Gestión de contratistas
│   └── ...          # Otros módulos CRUD
├── layout.tsx       # Layout raíz
├── page.tsx         # Landing page
└── providers.tsx    # Proveedores React
```

#### Patrones de Archivos:
- `page.tsx` - Página de Next.js
- `layout.tsx` - Layout compartido
- `UnifiedClientPage.tsx` - Componente cliente para listas
- `ClientPage.tsx` - Versión anterior (siendo reemplazada)

### `/features/` - Módulos de Negocio

#### Estructura por Feature:
```
features/[feature-name]/
├── actions/         # Server actions
├── components/      # Componentes React
├── hooks/          # Custom hooks
├── schemas/        # Esquemas Zod
├── types/          # TypeScript types
├── utils/          # Utilidades
├── index.ts        # Exports públicos
└── README.md       # Documentación del módulo
```

#### Módulos Principales:

**1. `assignments/`**
- Wizard multi-paso para asignaciones
- Gestión de folios únicos
- Progress tracking y validación optimista

**2. `auth/`**
- NextAuth v5 configuration
- Credenciales y WebAuthn support
- Hooks de usuario

**3. `colors/`**
- CRUD de colores
- Utilidades de accesibilidad
- Preview y variaciones

**4. `contractors/`**
- Gestión de contratistas
- Métricas de rendimiento
- Historial de asignaciones

**5. `customers/`**
- Base de clientes
- Historial de órdenes
- Información de contacto

**6. `models/`**
- Modelos/diseños de prendas
- Códigos únicos
- Descripciones

**7. `notes/`**
- Sistema completo de notas
- Comentarios anidados
- Likes y importancia
- Bulk actions

**8. `orders/`**
- Dashboard con tabs
- Métricas de negocio
- Calendario de entregas
- Sistema de riesgo

**9. `remissions/`**
- Generación de PDF
- Gestión de entregas
- Historial y estados

**10. `sizes/`**
- Catálogo de tallas
- Códigos únicos
- Gestión simple

### `/shared/` - Código Compartido

#### Estructura:
```
shared/
├── actions/         # Server actions compartidas
├── components/      # Componentes UI reutilizables
├── hooks/          # Hooks compartidos
├── lib/            # Configuraciones y utilidades
├── services/       # Servicios de negocio
├── templates/      # Plantillas CRUD
├── types/          # Tipos compartidos
└── utils/          # Utilidades generales
```

#### Archivos Clave:
- `actions/utils.ts` - `handleDbError` para manejo de errores
- `lib/prisma.ts` - Cliente Prisma singleton
- `templates/CrudListTemplate.tsx` - Plantilla lista unificada
- `utils/sortHelpers.ts` - Utilidades de ordenamiento

### `/prisma/` - Base de Datos

```
prisma/
├── schema.prisma    # Esquema de BD
├── seed.ts         # Datos iniciales
├── migrations/     # Migraciones SQL
└── seed-initial-data.sql
```

### `/memory-bank/` - Documentación del Proyecto

```
memory-bank/
├── activeContext.md      # Contexto activo
├── projectbrief.md       # Brief del proyecto
├── systemPatterns.md     # Patrones arquitectónicos
├── creative-phase/       # Diseños y arquitectura
├── planning/            # Planes de mejora
├── current-session/     # Trabajo en progreso
└── docs/               # Documentación general
```

### `/scripts/` - Scripts de Utilidad

```
scripts/
├── seed-metrics-data.ts  # Datos de prueba
├── migrate-*.js         # Scripts de migración
├── performance/         # Benchmarking
└── check-*.cjs         # Validaciones
```

### `/tests/` - Testing (Mínimo)

```
tests/
├── e2e/            # Tests end-to-end (vacío)
├── integration/    # Tests de integración
├── unit/           # Tests unitarios
└── visual/         # Tests visuales
```

## 📋 Convenciones de Nomenclatura

### Archivos TypeScript/React:
- Componentes: `PascalCase.tsx`
- Hooks: `camelCase.ts` con prefijo `use`
- Actions: `kebab-case.ts`
- Tipos: `PascalCase.types.ts`
- Utils: `camelCase.ts`

### Directorios:
- Features: `kebab-case/`
- Componentes: `PascalCase/` para grupos

### Exports:
- Siempre a través de `index.ts`
- Re-export selectivo, no `export *`

## 🔄 Flujo de Datos

```
Usuario → Página (RSC) → Server Action → Prisma → PostgreSQL
                ↓                ↓
           Client Component  Revalidación
                ↓
              SWR/RQ
```

## 🎯 Patrones de Importación

```typescript
// ✅ Correcto - usar alias
import { createOrder } from '@/features/orders/actions'
import { Button } from '@/shared/components/ui'

// ❌ Evitar - rutas relativas
import { createOrder } from '../../../features/orders/actions'
```

## 🛡️ Patrones de Seguridad

1. **Validación**: Siempre con Zod en server actions
2. **Autenticación**: NextAuth con roles (admin/user)
3. **Autorización**: Verificación en cada server action
4. **Sanitización**: Automática con Prisma
5. **CSRF**: Protección built-in de Next.js

## 📊 Estado de Migración

### ✅ Completado:
- Todos los módulos CRUD migrados a sistema unificado
- Server actions pattern implementado
- Sistema de diseño unificado

### ⚠️ En Progreso:
- Migración de `/lib/` a `/features/`
- Implementación de tests
- Documentación de API

### 🔴 Pendiente:
- TypeScript strict mode
- Tests E2E completos
- Optimización de performance

## 🚀 Scripts NPM Importantes

```bash
npm run dev          # Desarrollo con Turbopack
npm run build        # Build producción
npm run lint         # ESLint con auto-fix
npm run seed         # Seed inicial BD
npx prisma studio    # GUI de BD
```

## 🔧 Variables de Entorno Requeridas

```env
DATABASE_URL          # PostgreSQL connection
NEXTAUTH_SECRET      # Auth secret
NEXTAUTH_URL         # App URL
ANTHROPIC_API_KEY    # AI features
OPENAI_API_KEY       # AI features
```

## 📝 Notas Importantes

1. **Turbopack**: Habilitado para desarrollo rápido
2. **Server Actions**: Timeout de 60s configurado
3. **Prisma**: Connection pooling con reintentos
4. **TypeScript**: Modo no-strict (migrar gradualmente)
5. **Build**: Ignora errores TS/ESLint en producción