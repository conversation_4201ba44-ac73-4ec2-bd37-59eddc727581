"use client";

import React from "react";
import { motion } from "framer-motion";
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/solid";

import { Card } from "@/shared/components/ui/hero-ui-client";

export interface StatCard {
    title: string;
    value: string | number;
    description?: string;
    change?: number;
    changeLabel?: string;
    icon?: React.ReactNode;
    color?: "primary" | "success" | "warning" | "danger" | "default";
}

interface DashboardStatsProps {
    stats: StatCard[];
    columns?: 2 | 3 | 4;
}

const colorClasses = {
    primary: "bg-gradient-to-br from-blue-500 to-blue-600",
    success: "bg-gradient-to-br from-green-500 to-green-600",
    warning: "bg-gradient-to-br from-yellow-500 to-yellow-600",
    danger: "bg-gradient-to-br from-red-500 to-red-600",
    default: "bg-gradient-to-br from-gray-500 to-gray-600",
};

const iconBgClasses = {
    primary: "bg-blue-500/10 text-blue-600 dark:text-blue-400",
    success: "bg-green-500/10 text-green-600 dark:text-green-400",
    warning: "bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",
    danger: "bg-red-500/10 text-red-600 dark:text-red-400",
    default: "bg-gray-500/10 text-gray-600 dark:text-gray-400",
};

export function DashboardStats({ stats, columns = 4 }: DashboardStatsProps) {
    const gridCols = {
        2: "grid-cols-1 md:grid-cols-2",
        3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
        4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
    };

    return (
        <div className={`grid ${gridCols[columns]} gap-4`}>
            {stats.map((stat, index) => (
                <motion.div
                    key={index}
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                    <Card className="relative overflow-hidden bg-white dark:bg-gray-800 border-gray-200/50 dark:border-gray-700/50 hover:shadow-xl transition-shadow duration-300">
                        {/* Background gradient decoration */}
                        <div
                            className={`absolute top-0 right-0 w-32 h-32 ${colorClasses[stat.color || "default"]} opacity-10 rounded-full blur-3xl`}
                        />

                        <div className="relative p-6">
                            <div className="flex items-center justify-between">
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        {stat.title}
                                    </p>
                                    <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                                        {stat.value}
                                    </p>

                                    {(stat.change !== undefined ||
                                        stat.description) && (
                                        <div className="mt-3 flex items-center gap-2">
                                            {stat.change !== undefined && (
                                                <span
                                                    className={`inline-flex items-center gap-1 text-sm font-medium ${
                                                        stat.change >= 0
                                                            ? "text-green-600"
                                                            : "text-red-600"
                                                    }`}
                                                >
                                                    {stat.change >= 0 ? (
                                                        <ArrowUpIcon className="w-3 h-3" />
                                                    ) : (
                                                        <ArrowDownIcon className="w-3 h-3" />
                                                    )}
                                                    {Math.abs(stat.change)}%
                                                </span>
                                            )}
                                            {stat.changeLabel && (
                                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                                    {stat.changeLabel}
                                                </span>
                                            )}
                                            {stat.description &&
                                                !stat.change && (
                                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                                        {stat.description}
                                                    </span>
                                                )}
                                        </div>
                                    )}
                                </div>

                                {stat.icon && (
                                    <div
                                        className={`p-3 rounded-lg ${iconBgClasses[stat.color || "default"]}`}
                                    >
                                        <div className="w-6 h-6">
                                            {stat.icon}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </Card>
                </motion.div>
            ))}
        </div>
    );
}
