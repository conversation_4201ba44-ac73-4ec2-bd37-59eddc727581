"use client";

import { useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

export function GlassRouteLoader() {
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const handleStart = () => setLoading(true);
        const handleComplete = () => {
            setTimeout(() => setLoading(false), 500);
        };

        handleStart();
        const timer = setTimeout(handleComplete, 300);

        return () => clearTimeout(timer);
    }, [pathname, searchParams]);

    return (
        <AnimatePresence>
            {loading && (
                <motion.div
                    animate={{ opacity: 1 }}
                    className="fixed inset-0 z-[9999] pointer-events-none"
                    exit={{ opacity: 0 }}
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    {/* Glassmorphic overlay */}
                    <div className="absolute inset-0 bg-black/5 backdrop-blur-sm" />

                    {/* Animated loader */}
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                        <motion.div
                            animate={{ rotate: 360 }}
                            className="relative w-20 h-20"
                            transition={{
                                duration: 1,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        >
                            {/* Outer ring */}
                            <motion.div
                                animate={{ scale: 1, opacity: 1 }}
                                className="absolute inset-0 rounded-full border-4 border-primary/20"
                                initial={{ scale: 0.8, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                            />

                            {/* Inner spinning arc */}
                            <svg className="absolute inset-0 w-full h-full">
                                <circle
                                    cx="40"
                                    cy="40"
                                    fill="none"
                                    r="36"
                                    stroke="url(#gradient)"
                                    strokeDasharray="80 200"
                                    strokeLinecap="round"
                                    strokeWidth="4"
                                />
                                <defs>
                                    <linearGradient
                                        id="gradient"
                                        x1="0%"
                                        x2="100%"
                                        y1="0%"
                                        y2="100%"
                                    >
                                        <stop offset="0%" stopColor="#006FEE" />
                                        <stop
                                            offset="100%"
                                            stopColor="#0099FF"
                                        />
                                    </linearGradient>
                                </defs>
                            </svg>
                        </motion.div>

                        {/* Optional: Add brand text */}
                        <motion.p
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-4 text-sm font-medium text-primary text-center"
                            initial={{ opacity: 0, y: 10 }}
                            transition={{ delay: 0.2 }}
                        >
                            Cargando...
                        </motion.p>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
