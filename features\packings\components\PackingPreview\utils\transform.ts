// Utility functions for transforming packing data to preview format

import type { PackingWithRelations } from "@/lib/types/packing";
import type {
    PackingPreviewData,
    PackingProductItem,
    PackingSizeDetail,
} from "../types";

/**
 * Transform a packing entity to preview data format
 */
export function transformPackingToPreview(
    packing: PackingWithRelations,
): PackingPreviewData {
    // Group details by model, color, and part
    const itemsMap = new Map<string, PackingProductItem>();

    packing.details.forEach((detail) => {
        const { garmentSize } = detail;
        const key = `${garmentSize.garment.model.code}-${garmentSize.garment.color.name}-${detail.partNumber || "N/A"}`;

        if (!itemsMap.has(key)) {
            itemsMap.set(key, {
                model: garmentSize.garment.model.code,
                color: garmentSize.garment.color.name,
                part: detail.partNumber || undefined,
                sizes: [],
                totals: {
                    pieces: 0,
                    boxes: 0,
                    bags: 0,
                    loosePieces: 0,
                },
            });
        }

        const item = itemsMap.get(key)!;

        // Add size detail
        const sizeDetail: PackingSizeDetail = {
            size: garmentSize.size.code,
            quantity: detail.quantity,
            boxes:
                detail.packagingType === "caja"
                    ? detail.packagingUnits || 0
                    : 0,
            bags:
                detail.packagingType === "bolsa"
                    ? detail.packagingUnits || 0
                    : 0,
            loosePieces: detail.loosePieces || 0,
            quality: (detail.qualityType || "primera") as
                | "primera"
                | "segunda"
                | "manchada"
                | "incompleta",
        };

        item.sizes.push(sizeDetail);

        // Update totals
        item.totals.pieces += detail.quantity;
        item.totals.boxes += sizeDetail.boxes;
        item.totals.bags += sizeDetail.bags;
        item.totals.loosePieces += sizeDetail.loosePieces;
    });

    // Calculate summary
    const qualityBreakdown = {
        primera: 0,
        segunda: 0,
        manchada: 0,
        incompleta: 0,
    };

    let totalPieces = 0;
    let totalBoxes = 0;
    let totalBags = 0;
    let totalLoosePieces = 0;

    packing.details.forEach((detail) => {
        const quality = (detail.qualityType ||
            "primera") as keyof typeof qualityBreakdown;

        qualityBreakdown[quality] += detail.quantity;

        totalPieces += detail.quantity;
        totalBoxes +=
            detail.packagingType === "caja" ? detail.packagingUnits || 0 : 0;
        totalBags +=
            detail.packagingType === "bolsa" ? detail.packagingUnits || 0 : 0;
        totalLoosePieces += detail.loosePieces || 0;
    });

    return {
        company: {
            name: "LOHARI",
            slogan: "Tu estilo, nuestra pasión",
            // These could come from company settings in the future
            rfc: "RFC: XAXX010101000",
            address: "Dirección de la empresa",
            phone: "(*************",
            email: "<EMAIL>",
        },
        document: {
            folio: packing.folio,
            date: packing.createdAt,
            type: "LISTA DE EMPAQUE",
        },
        customer: {
            name: packing.customer.name,
            subName: packing.subCustomer?.name,
            contact: (packing.customer as any).contact || undefined,
            phone: (packing.customer as any).phone || undefined,
            address: (packing.customer as any).address || undefined,
        },
        order: packing.order
            ? {
                  number: packing.order.transferNumber || "N/A",
                  cutOrder: packing.order.cutOrder || undefined,
              }
            : undefined,
        items: Array.from(itemsMap.values()),
        summary: {
            totalPieces,
            totalBoxes,
            totalBags,
            totalLoosePieces,
            qualityBreakdown,
            deliveryDate: packing.deliveryDate,
            notes: packing.notes || undefined,
        },
        transport: {
            notes: packing.transportNotes || undefined,
            signature: packing.transportSignature || undefined,
            signedAt: packing.transportSignedAt || undefined,
        },
        receiver: {
            name: packing.receiverName || undefined,
            signature: packing.receiverSignature || undefined,
            signedAt: packing.receiverSignedAt || undefined,
        },
    };
}

/**
 * Format currency values
 */
export function formatCurrency(amount: number): string {
    return new Intl.NumberFormat("es-MX", {
        style: "currency",
        currency: "MXN",
    }).format(amount);
}

/**
 * Format date for display
 */
export function formatDate(
    date: Date | string,
    format: "short" | "long" = "long",
): string {
    const dateObj = typeof date === "string" ? new Date(date) : date;

    if (format === "short") {
        return dateObj.toLocaleDateString("es-MX");
    }

    return dateObj.toLocaleDateString("es-MX", {
        year: "numeric",
        month: "long",
        day: "numeric",
    });
}

/**
 * Calculate print zoom level based on viewport
 */
export function calculatePrintZoom(
    viewportWidth: number,
    orientation: "portrait" | "landscape",
): number {
    const paperWidth = orientation === "portrait" ? 210 : 297; // A4 width in mm
    const mmToPx = 3.7795275591; // 1mm = 3.7795275591px at 96dpi
    const paperWidthPx = paperWidth * mmToPx;

    // Calculate zoom to fit viewport with some margin
    const margin = 40; // px
    const availableWidth = viewportWidth - margin * 2;
    const zoom = Math.min(100, (availableWidth / paperWidthPx) * 100);

    return Math.round(zoom);
}
