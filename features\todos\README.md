# <PERSON>ó<PERSON>lo Todos

Este módulo gestiona las tareas pendientes (todos) en el sistema Lohari.

## Estructura

```
todos/
├── actions/        # Server actions para operaciones CRUD
├── components/     # Componentes React
├── hooks/          # Custom hooks para manejo de estado
├── schemas/        # Esquemas de validación con Zod
├── types/          # Definiciones de TypeScript
└── utils/          # Utilidades auxiliares
```

## API Pública

### Hooks
- `useTodos(filters)` - Obtener lista de todos con filtros
- `useTodo(id)` - Obtener un todo específico
- `useCreateTodo()` - Crear nuevo todo
- `useUpdateTodo()` - Actualizar todo existente
- `useDeleteTodo()` - Eliminar todo
- `useToggleTodo()` - Cambiar estado completado

### Types
- `Todo` - Interface principal de todo
- `TodosResponse` - Respuesta con paginación
- `CreateTodoInput` - Datos para crear todo
- `UpdateTodoInput` - Datos para actualizar todo
- `TodoFilters` - Filtros de búsqueda

### Componentes
- `TodoList` - Lista de todos con interacciones

## Uso

```typescript
import { useTodos, useCreateTodo } from '@/features/todos';

// En un componente
const { todos, isLoading } = useTodos({ completed: false });
const { createTodo } = useCreateTodo();

// Crear un todo
await createTodo({
    title: "Nueva tarea",
    priority: "high",
    dueDate: new Date()
});
```

## Server Actions

Las acciones del servidor deben importarse directamente:

```typescript
import { getTodos, createTodo, toggleTodoComplete } from '@/features/todos/actions';
```

## Características

- CRUD completo con soft delete
- Filtrado y búsqueda
- Prioridades (low, medium, high)
- Estados (completado/pendiente)
- Fechas de vencimiento
- Paginación
- Asociación con usuarios