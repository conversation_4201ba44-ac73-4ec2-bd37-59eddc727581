"use client";

import type { SortOption } from "@/shared/components/dashboard";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    EyeIcon,
    PrinterIcon,
    DocumentTextIcon,
    ClockIcon,
    CheckCircleIcon,
    CubeTransparentIcon,
    DocumentArrowDownIcon,
    TruckIcon,
    CalendarIcon,
    ExclamationTriangleIcon,
    UserGroupIcon,
    ChartBarIcon,
    BellAlertIcon,
    TrashIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import { useRemissionsData } from "@/features/remissions/hooks";
import { markRemissionPrinted } from "@/features/remissions/actions";
import { deleteRemission } from "@/features/remissions/actions/delete";
import {
    <PERSON>,
    <PERSON>lt<PERSON>,
    Avatar,
    Badge,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    Button,
} from "@/shared/components/ui/hero-ui-client";

// Mapa de colores para estados
const statusColors = {
    ACTIVE: "primary",
    PENDING: "warning",
    DELIVERED: "success",
    CANCELLED: "danger",
    ENTREGADA: "success",
} as const;

// Mapa de textos para estados
const statusLabels = {
    ACTIVE: "Activa",
    PENDING: "Pendiente",
    DELIVERED: "Entregada",
    CANCELLED: "Cancelada",
    ENTREGADA: "Entregada",
} as const;

interface UnifiedRemissionsPageProps {
    userRole?: string;
}

export default function UnifiedRemissionsPage({
    userRole = "user",
}: UnifiedRemissionsPageProps) {
    const router = useRouter();
    const { remissions = [], isLoading, mutate, stats } = useRemissionsData();

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "createdAt-desc",
        label: "Más reciente",
        field: "createdAt",
        direction: "desc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 12;
    const [deleteModal, setDeleteModal] = useState<{
        isOpen: boolean;
        remission: any | null;
    }>({
        isOpen: false,
        remission: null,
    });

    // Calcular estadísticas dinámicas
    const dynamicStats = useMemo(() => {
        return [
            {
                title: "Remisiones Hoy",
                value: stats.todayRemissions,
                icon: <CalendarIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Creadas en las últimas 24h",
            },
            {
                title: "Promedio Diario",
                value: stats.dailyAverage,
                icon: <ChartBarIcon className="w-6 h-6" />,
                color: "default" as const,
                description: "Últimos 7 días",
            },
            {
                title: "Pendientes Impresión",
                value: stats.pendingPrint,
                icon: <PrinterIcon className="w-6 h-6" />,
                color: "warning" as const,
                description: `${stats.total > 0 ? ((stats.pendingPrint / stats.total) * 100).toFixed(0) : 0}% del total`,
            },
            {
                title: "Alertas Urgentes",
                value: stats.urgentAlerts,
                icon: <ExclamationTriangleIcon className="w-6 h-6" />,
                color:
                    stats.urgentAlerts > 0
                        ? ("danger" as const)
                        : ("success" as const),
                description: "Sin imprimir > 48h",
            },
            {
                title: "Entregadas del Mes",
                value: stats.deliveredThisMonth,
                icon: <CheckCircleIcon className="w-6 h-6" />,
                color: "success" as const,
                description: `${stats.onTimeDeliveryRate}% tasa cumplimiento`,
            },
            {
                title: "Total Prendas",
                value: stats.totalItems.toLocaleString("es-MX"),
                icon: <CubeTransparentIcon className="w-6 h-6" />,
                color: "default" as const,
                description: "En todas las remisiones",
            },
            {
                title: "Próximas Entregas",
                value: stats.upcomingDeliveries,
                icon: <TruckIcon className="w-6 h-6" />,
                color:
                    stats.upcomingDeliveries > 5
                        ? ("warning" as const)
                        : ("primary" as const),
                description: "Próximos 3 días",
            },
            {
                title: "Top Contratista",
                value: stats.topContractor.name,
                icon: <UserGroupIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: `${stats.topContractor.count} remisiones`,
            },
            {
                title: "Tiempo Procesamiento",
                value: `${stats.avgProcessingTime}d`,
                icon: <ClockIcon className="w-6 h-6" />,
                color:
                    stats.avgProcessingTime > 3
                        ? ("warning" as const)
                        : ("success" as const),
                description: "Promedio creación-impresión",
            },
            {
                title: "Total Activas",
                value: stats.total,
                icon: <DocumentTextIcon className="w-6 h-6" />,
                color: "primary" as const,
                change:
                    stats.total > 0
                        ? Math.round(
                              (stats.todayRemissions / stats.total) * 100,
                          )
                        : 0,
                changeLabel: "creadas hoy",
                changeType: "neutral" as const,
            },
        ];
    }, [stats]);

    // Columnas de la tabla
    const columns = [
        {
            key: "folio",
            label: "Folio",
            sortable: true,
            render: (remission: any) => {
                const hoursOld =
                    (new Date().getTime() -
                        new Date(remission.createdAt).getTime()) /
                    (1000 * 60 * 60);
                const isUrgent = !remission.printedAt && hoursOld > 48;
                const isWarning = !remission.printedAt && hoursOld > 24;

                return (
                    <div className="flex items-center gap-2">
                        {isUrgent ? (
                            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 animate-pulse" />
                        ) : isWarning ? (
                            <BellAlertIcon className="w-5 h-5 text-orange-500" />
                        ) : (
                            <DocumentTextIcon className="w-5 h-5 text-gray-400" />
                        )}
                        <div>
                            <span className="font-bold text-primary">
                                {remission.folio}
                            </span>
                            {isUrgent && (
                                <Badge
                                    className="ml-2"
                                    color="danger"
                                    size="sm"
                                >
                                    URGENTE
                                </Badge>
                            )}
                        </div>
                    </div>
                );
            },
        },
        {
            key: "contractor",
            label: "Contratista",
            sortable: true,
            render: (remission: any) => (
                <div className="flex items-center gap-3">
                    <Avatar
                        color="secondary"
                        name={remission.contractor?.name || "Sin asignar"}
                        size="sm"
                    />
                    <div>
                        <p className="font-medium">
                            {remission.contractor?.name || "Sin asignar"}
                        </p>
                        {remission.contractor?.email && (
                            <p className="text-xs text-gray-500">
                                {remission.contractor.email}
                            </p>
                        )}
                    </div>
                </div>
            ),
        },
        {
            key: "orders",
            label: "Órdenes",
            render: (remission: any) => {
                const orders =
                    remission.assignments
                        ?.map((ra: any) => ra.assignment?.order)
                        .filter(Boolean) || [];

                if (orders.length === 0) {
                    return <span className="text-gray-400">Sin órdenes</span>;
                }

                return (
                    <div className="space-y-1">
                        {orders.slice(0, 2).map((order: any, idx: number) => (
                            <Badge key={idx} size="sm" variant="flat">
                                {order.code} - {order.customer?.name}
                            </Badge>
                        ))}
                        {orders.length > 2 && (
                            <span className="text-xs text-gray-500">
                                +{orders.length - 2} más
                            </span>
                        )}
                    </div>
                );
            },
        },
        {
            key: "status",
            label: "Estado",
            render: (remission: any) => {
                const color =
                    statusColors[
                        remission.status as keyof typeof statusColors
                    ] || "default";
                const label =
                    statusLabels[
                        remission.status as keyof typeof statusLabels
                    ] || remission.status;

                return (
                    <Chip color={color} size="sm" variant="flat">
                        {label}
                    </Chip>
                );
            },
        },
        {
            key: "items",
            label: "Prendas",
            render: (remission: any) => {
                const totalItems =
                    remission.remissionItems?.reduce(
                        (sum: number, item: any) => sum + item.quantity,
                        0,
                    ) || 0;

                return (
                    <div className="flex items-center gap-2">
                        <CubeTransparentIcon className="w-4 h-4 text-gray-400" />
                        <span className="font-medium">{totalItems}</span>
                        <span className="text-sm text-gray-500">prendas</span>
                    </div>
                );
            },
        },
        {
            key: "dates",
            label: "Fechas y Estado",
            sortable: true,
            render: (remission: any) => {
                const createdDate = new Date(remission.createdAt);
                const hoursOld =
                    (new Date().getTime() - createdDate.getTime()) /
                    (1000 * 60 * 60);
                const daysOld = Math.floor(hoursOld / 24);
                const isUrgent = !remission.printedAt && hoursOld > 48;

                return (
                    <div className="text-sm space-y-1">
                        <div className="flex items-center gap-1 text-gray-600">
                            <CalendarIcon className="w-3 h-3" />
                            <span>
                                Creada:{" "}
                                {format(createdDate, "dd/MM/yyyy", {
                                    locale: es,
                                })}
                            </span>
                            {daysOld > 0 && (
                                <Chip
                                    color={isUrgent ? "danger" : "default"}
                                    size="sm"
                                    variant="flat"
                                >
                                    {daysOld}d
                                </Chip>
                            )}
                        </div>
                        {remission.printedAt ? (
                            <div className="flex items-center gap-1 text-green-600">
                                <PrinterIcon className="w-3 h-3" />
                                <span>
                                    Impresa:{" "}
                                    {format(
                                        new Date(remission.printedAt),
                                        "dd/MM HH:mm",
                                        { locale: es },
                                    )}
                                </span>
                            </div>
                        ) : (
                            <div className="flex items-center gap-1">
                                {isUrgent ? (
                                    <ExclamationTriangleIcon className="w-3 h-3 text-red-600 animate-pulse" />
                                ) : (
                                    <ClockIcon className="w-3 h-3 text-orange-600" />
                                )}
                                <span
                                    className={
                                        isUrgent
                                            ? "text-red-600 font-semibold"
                                            : "text-orange-600"
                                    }
                                >
                                    {isUrgent
                                        ? `URGENTE: ${Math.floor(hoursOld)}h sin imprimir`
                                        : "Pendiente de impresión"}
                                </span>
                            </div>
                        )}
                    </div>
                );
            },
        },
        {
            key: "printStatus",
            label: "Impresión",
            render: (remission: any) =>
                remission.printedAt ? (
                    <Tooltip content="Impresa">
                        <CheckCircleIcon className="w-5 h-5 text-green-500" />
                    </Tooltip>
                ) : (
                    <Tooltip content="Pendiente">
                        <ClockIcon className="w-5 h-5 text-orange-500" />
                    </Tooltip>
                ),
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: async (remission: any) => {
                router.push(`/dashboard/remissions/${remission.id}`);
            },
        },
        {
            label: "Imprimir",
            icon: <PrinterIcon className="w-4 h-4" />,
            onClick: async (remission: any) => {
                if (!remission.printedAt) {
                    const result = await markRemissionPrinted(remission.id);

                    if (result) {
                        mutate();
                        addToast({
                            title: "Remisión marcada como impresa",
                            description: `Folio: ${remission.folio}`,
                            color: "success",
                        });
                    }
                }
                router.push(`/dashboard/remissions/${remission.id}`);
            },
            color: "primary" as const,
        },
        {
            label: "Descargar PDF",
            icon: <DocumentArrowDownIcon className="w-4 h-4" />,
            onClick: async (remission: any) => {
                // Aquí podrías implementar la descarga del PDF
                addToast({
                    title: "Generando PDF",
                    description: "El PDF se descargará en breve",
                    color: "primary",
                });
            },
            color: "default" as const,
        },
    ];

    // Agregar acción de eliminar solo para admins
    if (userRole === "admin") {
        actions.push({
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (remission: any) => {
                setDeleteModal({ isOpen: true, remission });
            },
            color: "default" as const,
        });
    }

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "ACTIVE", label: "Activa" },
                { value: "PENDING", label: "Pendiente" },
                { value: "DELIVERED", label: "Entregada" },
                { value: "CANCELLED", label: "Cancelada" },
            ],
        },
        {
            key: "printStatus",
            label: "Impresión",
            type: "select" as const,
            placeholder: "Todos",
            options: [
                { value: "all", label: "Todos" },
                { value: "printed", label: "Impresas" },
                { value: "pending", label: "Pendientes" },
            ],
        },
        {
            key: "period",
            label: "Periodo",
            type: "select" as const,
            placeholder: "Todo el tiempo",
            options: [
                { value: "all", label: "Todo el tiempo" },
                { value: "today", label: "Hoy" },
                { value: "7", label: "Últimos 7 días" },
                { value: "30", label: "Últimos 30 días" },
                { value: "90", label: "Últimos 90 días" },
            ],
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
        {
            key: "folio-asc",
            label: "Folio A-Z",
            field: "folio",
            direction: "asc" as const,
        },
        {
            key: "folio-desc",
            label: "Folio Z-A",
            field: "folio",
            direction: "desc" as const,
        },
        {
            key: "contractor-asc",
            label: "Contratista A-Z",
            field: "contractor.name",
            direction: "asc" as const,
        },
    ];

    // Filtrar y ordenar datos localmente (además de los filtros del servidor)
    const filteredData = useMemo(() => {
        let filtered = [...remissions];

        // Búsqueda local adicional
        if (searchValue) {
            const search = searchValue.toLowerCase();

            filtered = filtered.filter(
                (remission) =>
                    remission.folio.toLowerCase().includes(search) ||
                    remission.contractor?.name
                        ?.toLowerCase()
                        .includes(search) ||
                    remission.notes?.toLowerCase().includes(search),
            );
        }

        // Filtro por estado de impresión (local)
        if (filterValues.printStatus && filterValues.printStatus !== "all") {
            if (filterValues.printStatus === "printed") {
                filtered = filtered.filter((r) => r.printedAt);
            } else if (filterValues.printStatus === "pending") {
                filtered = filtered.filter((r) => !r.printedAt);
            }
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [remissions, searchValue, filterValues, currentSort]);

    // Componente de remisiones recientes
    const RecentRemissions = () => {
        const recentRemissions = useMemo(() => {
            return [...remissions]
                .filter((r) => !r.printedAt) // Solo pendientes
                .sort(
                    (a, b) =>
                        new Date(b.createdAt).getTime() -
                        new Date(a.createdAt).getTime(),
                )
                .slice(0, 5);
        }, []);

        if (recentRemissions.length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
                initial={{ opacity: 0, y: 20 }}
            >
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <ClockIcon className="w-5 h-5 text-orange-500" />
                    Remisiones Pendientes de Impresión
                </h3>
                <div className="space-y-3">
                    {recentRemissions.map((remission) => (
                        <div
                            key={remission.id}
                            className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                            onClick={() =>
                                router.push(
                                    `/dashboard/remissions/${remission.id}`,
                                )
                            }
                        >
                            <div className="flex items-center gap-3">
                                <DocumentTextIcon className="w-5 h-5 text-gray-400" />
                                <div>
                                    <p className="font-medium">
                                        {remission.folio}
                                    </p>
                                    <p className="text-sm text-gray-500">
                                        {remission.contractor?.name}
                                    </p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className="text-sm text-gray-600">
                                    {formatDistanceToNow(
                                        new Date(remission.createdAt),
                                        {
                                            locale: es,
                                            addSuffix: true,
                                        },
                                    )}
                                </p>
                                <button
                                    className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1 mt-1"
                                    onClick={async (e) => {
                                        e.stopPropagation();
                                        await markRemissionPrinted(
                                            remission.id,
                                        );
                                        mutate();
                                        router.push(
                                            `/dashboard/remissions/${remission.id}`,
                                        );
                                    }}
                                >
                                    <PrinterIcon className="w-3 h-3" />
                                    Imprimir
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </motion.div>
        );
    };

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={columns}
                currentSort={currentSort}
                filterValues={filterValues}
                isLoading={isLoading}
                page={page}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Gestión de remisiones de entrega"
                title="Remisiones"
                totalPages={Math.ceil(filteredData.length / rowsPerPage)}
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onPageChange={setPage}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={actions}
                // Create
                createRoute="/dashboard/remissions/create"
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key] && filterValues[key] !== "all",
                    ).length
                }
                // Pagination
                breadcrumbs={[{ label: "Remisiones" }]}
                // Stats
                stats={dynamicStats}
                createLabel="Nueva Remisión"
                // Filters
                filters={filters}
                data={filteredData}
                // Table
                emptyContent="No hay remisiones registradas"
            />

            {/* Sección de Alertas y Riesgos */}
            {stats.urgentAlerts > 0 && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-800"
                    initial={{ opacity: 0, y: 20 }}
                >
                    <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                            <ExclamationTriangleIcon className="w-8 h-8 text-red-600 dark:text-red-400" />
                        </div>
                        <div className="flex-1">
                            <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-2">
                                Alertas Urgentes - Requieren Atención Inmediata
                            </h3>
                            <p className="text-red-700 dark:text-red-300 mb-4">
                                {stats.urgentAlerts} remisiones sin imprimir por
                                más de 48 horas
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {remissions
                                    .filter((r) => {
                                        if (r.printedAt) return false;
                                        const hoursOld =
                                            (new Date().getTime() -
                                                new Date(
                                                    r.createdAt,
                                                ).getTime()) /
                                            (1000 * 60 * 60);

                                        return hoursOld > 48;
                                    })
                                    .slice(0, 4)
                                    .map((remission) => (
                                        <div
                                            key={remission.id}
                                            className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-red-300 dark:border-red-700 cursor-pointer hover:shadow-md transition-shadow"
                                            onClick={() =>
                                                router.push(
                                                    `/dashboard/remissions/${remission.id}`,
                                                )
                                            }
                                        >
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="font-semibold text-red-900 dark:text-red-100">
                                                    {remission.folio}
                                                </span>
                                                <Badge color="danger" size="sm">
                                                    {Math.floor(
                                                        (new Date().getTime() -
                                                            new Date(
                                                                remission.createdAt,
                                                            ).getTime()) /
                                                            (1000 * 60 * 60),
                                                    )}
                                                    h sin imprimir
                                                </Badge>
                                            </div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {remission.contractor?.name ||
                                                    "Sin contratista"}
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                                Creada:{" "}
                                                {format(
                                                    new Date(
                                                        remission.createdAt,
                                                    ),
                                                    "dd/MM/yyyy HH:mm",
                                                    { locale: es },
                                                )}
                                            </p>
                                        </div>
                                    ))}
                            </div>
                            {stats.urgentAlerts > 4 && (
                                <p className="text-sm text-red-600 dark:text-red-400 mt-4">
                                    Y {stats.urgentAlerts - 4} alertas más...
                                </p>
                            )}
                        </div>
                    </div>
                </motion.div>
            )}

            {/* Remisiones pendientes */}
            {remissions.length > 0 && <RecentRemissions />}

            {/* Modal de confirmación de eliminación */}
            <Modal
                isOpen={deleteModal.isOpen}
                size="md"
                onClose={() =>
                    setDeleteModal({ isOpen: false, remission: null })
                }
            >
                <ModalContent>
                    <ModalHeader className="flex flex-col gap-1">
                        <div className="flex items-center gap-2 text-danger">
                            <ExclamationTriangleIcon className="w-6 h-6" />
                            Confirmar Eliminación
                        </div>
                    </ModalHeader>
                    <ModalBody>
                        <p className="text-gray-600">
                            ¿Estás seguro de que deseas eliminar la remisión{" "}
                            <strong>{deleteModal.remission?.folio}</strong>?
                        </p>
                        <p className="text-sm text-red-600 mt-2">
                            Esta acción no se puede deshacer. La remisión será
                            eliminada permanentemente.
                        </p>
                    </ModalBody>
                    <ModalFooter>
                        <Button
                            color="default"
                            variant="flat"
                            onPress={() =>
                                setDeleteModal({
                                    isOpen: false,
                                    remission: null,
                                })
                            }
                        >
                            Cancelar
                        </Button>
                        <Button
                            color="danger"
                            onPress={async () => {
                                if (deleteModal.remission) {
                                    const result = await deleteRemission(
                                        deleteModal.remission.id,
                                    );

                                    if (result.success) {
                                        addToast({
                                            title: "Remisión eliminada",
                                            description: `La remisión ${deleteModal.remission.folio} ha sido eliminada`,
                                            color: "success",
                                        });
                                        mutate();
                                    } else {
                                        addToast({
                                            title: "Error al eliminar",
                                            description:
                                                ("error" in result
                                                    ? result.error
                                                    : undefined) ||
                                                "No se pudo eliminar la remisión",
                                            color: "warning",
                                        });
                                    }
                                    setDeleteModal({
                                        isOpen: false,
                                        remission: null,
                                    });
                                }
                            }}
                        >
                            Eliminar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </>
    );
}
