# Code Style Standards

This document defines our code style and conventions based on the existing Lohari project patterns.

## General Principles

1. **Language Usage**
   - Code (variables, functions, types): English
   - Database fields and models: Spanish
   - UI strings and comments: Spanish
   - Documentation: Spanish for business context, English for technical

2. **File Organization**
   - Maximum 500 lines per file
   - Use barrel exports (index.ts) for cleaner imports
   - Feature-based structure over technical layers

## TypeScript Conventions

### Type Definitions
```typescript
// Prefer interfaces for objects
interface OrderData {
  id: string;
  numero_orden: string;
  fecha_entrega: Date;
}

// Use types for unions and primitives
type OrderStatus = "pendiente" | "en_proceso" | "completado";
```

### Naming Conventions
```typescript
// PascalCase for types and interfaces
interface OrderForm { }
type ActionResult<T> = { };

// camelCase for variables and functions
const handleSubmit = async () => { };
let isLoading = false;

// UPPER_SNAKE_CASE for constants
const MAX_FILE_SIZE = 5 * 1024 * 1024;
```

## React Patterns

### Component Structure
```typescript
// 1. Imports
import { useState } from "react";
import { Button } from "@heroui/react";

// 2. Types
interface ComponentProps {
  data: string;
}

// 3. Component
export function Component({ data }: ComponentProps) {
  // 4. Hooks
  const [state, setState] = useState("");
  
  // 5. Handlers
  const handleClick = () => { };
  
  // 6. Effects
  useEffect(() => { }, []);
  
  // 7. Render
  return <div>{data}</div>;
}
```

### Server Components vs Client Components
```typescript
// Default to Server Components
export default async function Page() {
  const data = await fetchData();
  return <ServerComponent data={data} />;
}

// Client Components only when needed
"use client";
export function InteractiveForm() {
  const [value, setValue] = useState("");
  return <form>...</form>;
}
```

## Server Actions Pattern

### Standard Structure
```typescript
"use server";

import { z } from "zod";
import { db, handleDbError } from "@/shared/lib/db";

const schema = z.object({
  field: z.string().min(1, "Required"),
});

export async function createEntity(data: unknown) {
  try {
    // 1. Validate input
    const validated = schema.parse(data);
    
    // 2. Business validation
    const existing = await db.entity.findUnique({
      where: { field: validated.field }
    });
    
    if (existing) {
      return { success: false, error: "Already exists" };
    }
    
    // 3. Execute operation
    const result = await db.entity.create({
      data: validated
    });
    
    // 4. Revalidate cache
    revalidatePath("/entities");
    
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors[0]?.message || "Invalid data" 
      };
    }
    return handleDbError(() => { throw error; });
  }
}
```

## Import Organization

```typescript
// 1. External packages
import { useState, useEffect } from "react";
import { z } from "zod";

// 2. Next.js specific
import { redirect } from "next/navigation";
import Image from "next/image";

// 3. Shared/lib imports
import { db } from "@/shared/lib/db";
import { formatDate } from "@/shared/lib/utils";

// 4. Feature imports
import { createOrder } from "@/features/orders/actions";
import { OrderType } from "@/features/orders/types";

// 5. Relative imports
import { LocalComponent } from "./components/LocalComponent";
import styles from "./styles.module.css";
```

## Error Handling

### Consistent Error Messages
```typescript
// User-facing errors in Spanish
return { 
  success: false, 
  error: "El número de orden ya existe" 
};

// Technical logs in English (console/logs)
console.error("Order creation failed:", error);
```

### Error Response Format
```typescript
type ActionResult<T> = 
  | { success: true; data: T }
  | { success: false; error: string };
```

## Database Conventions

### Prisma Models
```prisma
model Orden {
  id              String   @id @default(cuid())
  numero_orden    String   @unique
  fecha_entrega   DateTime
  estado          String   @default("pendiente")
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  // Relations in Spanish
  cliente         Cliente  @relation(fields: [cliente_id], references: [id])
  cliente_id      String
  
  @@map("ordenes")
}
```

### Query Patterns
```typescript
// Include related data
const orderWithDetails = await db.orden.findUnique({
  where: { id },
  include: {
    cliente: true,
    asignaciones: {
      include: {
        contratista: true
      }
    }
  }
});
```

## Comments and Documentation

### Code Comments
```typescript
// Spanish for business logic explanations
// Validar que el contratista tenga capacidad disponible
if (contractor.capacidad < cantidadSolicitada) {
  return { error: "Capacidad insuficiente" };
}

// English for technical comments
// Optimize query by using select instead of include
const lightweightData = await db.orden.findMany({
  select: { id: true, numero_orden: true }
});
```

### JSDoc Comments
```typescript
/**
 * Crea una nueva orden de producción
 * @param data - Datos de la orden validados con Zod
 * @returns Resultado de la operación con la orden creada o error
 */
export async function createOrder(data: OrderInput) {
  // Implementation
}
```

## Testing Conventions (Future)

```typescript
// Test file naming: component.test.tsx or action.test.ts
describe("OrderForm", () => {
  it("should validate required fields", () => {
    // Test implementation
  });
  
  it("debería mostrar error cuando falta número de orden", () => {
    // Spanish for user-facing behavior tests
  });
});
```

## Git Commit Messages

```bash
# Format: <type>(<scope>): <subject>
feat(orders): add bulk assignment feature
fix(auth): resolve session timeout issue
docs(readme): update installation steps
refactor(components): simplify order table logic

# Spanish for user-facing changes
feat(ordenes): agregar exportación a Excel
fix(remisiones): corregir formato de fecha
```