"use server";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

import { modelSchema } from "./schema";
import { validateModelCode } from "./validate";

// Obtener helpers de revalidación para la entidad "model"
const { revalidateCache } = createServerRevalidation("model");

/**
 * Crea un nuevo modelo
 */
export async function createModel(data: {
    code: string;
    description: string;
    basePrice?: number;
}) {
    // Validar datos
    const validation = modelSchema.safeParse(data);

    if (!validation.success) {
        return {
            success: false,
            error:
                validation.error.errors[0]?.message ||
                "Datos de modelo inválidos",
        };
    }

    // Verificar si el código ya existe
    const codeValidationResult = await validateModelCode(data.code);

    if (!codeValidationResult || !codeValidationResult.success) {
        return {
            success: false,
            error:
                codeValidationResult?.error ||
                "Error al validar código de modelo",
        };
    }

    // Verificar el resultado de la validación
    if (
        !codeValidationResult.data ||
        !(codeValidationResult.data as any)?.isValid
    ) {
        return {
            success: false,
            error: "El código del modelo ya existe en el sistema",
        };
    }

    const result = await handleDbError(async () => {
        const model = await db.garmentModel.create({
            data: {
                code: data.code,
                description: data.description,
                basePrice: data.basePrice || 0,
            },
        });

        // Revalidar caché usando el helper genérico
        revalidateCache(model.id);

        return model;
    }, "Error al crear modelo");

    return result;
}
