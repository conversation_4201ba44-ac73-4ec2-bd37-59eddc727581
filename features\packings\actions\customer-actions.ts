"use server";

import { CustomerWithChildren } from "@/lib/types/packing";
import { db } from "@/shared/lib/db";

/**
 * Fetch all customers with their sub-clients (subCustomers)
 * Only returns parent customers with their subCustomers included
 */
export async function getCustomersWithSubClients(): Promise<
    CustomerWithChildren[]
> {
    try {
        const customers = await db.customer.findMany({
            where: {
                parentId: null, // Only get parent customers
            },
            include: {
                subCustomers: {
                    orderBy: {
                        name: "asc",
                    },
                },
            },
            orderBy: {
                name: "asc",
            },
        });

        return customers as CustomerWithChildren[];
    } catch (error) {
        console.error("Error fetching customers with sub-clients:", error);
        throw new Error("Error al cargar clientes");
    }
}

/**
 * Update customer display name based on selected sub-client
 * This helps with UI display like "Becktel - Reebok"
 */
export async function updateCustomerDisplayName(
    customerId: string,
    subCustomerId?: string,
) {
    try {
        const customer = await db.customer.findUnique({
            where: { id: customerId },
            include: { subCustomers: true },
        });

        if (!customer) {
            throw new Error("Cliente no encontrado");
        }

        let displayName = customer.name;

        if (subCustomerId) {
            const subCustomer = customer.subCustomers.find(
                (c) => c.id === subCustomerId,
            );

            if (subCustomer) {
                displayName = `${customer.name} - ${subCustomer.name}`;
            }
        }

        // Update the display name in the database
        await db.customer.update({
            where: { id: customerId },
            data: { displayName },
        });

        return displayName;
    } catch (error) {
        console.error("Error updating customer display name:", error);
        throw error;
    }
}

/**
 * Validate that a sub-client belongs to a parent customer
 */
export async function validateSubClientRelationship(
    customerId: string,
    subCustomerId: string,
): Promise<boolean> {
    try {
        const subCustomer = await db.customer.findFirst({
            where: {
                id: subCustomerId,
                parentId: customerId,
            },
        });

        return !!subCustomer;
    } catch (error) {
        console.error("Error validating sub-client relationship:", error);

        return false;
    }
}

/**
 * Get customer with packing settings
 */
export async function getCustomerPackingSettings(customerId: string) {
    try {
        const customer = await db.customer.findUnique({
            where: { id: customerId },
            select: {
                id: true,
                name: true,
                packingSettings: true,
            },
        });

        if (!customer) {
            throw new Error("Cliente no encontrado");
        }

        // Parse packing settings or return defaults
        const settings = (customer.packingSettings as any) || {
            defaultPiecesPerBox: 50,
            defaultPiecesPerBag: 100,
            preferredPackagingType: "mixto",
            customInstructions: "",
        };

        return {
            ...customer,
            packingSettings: settings,
        };
    } catch (error) {
        console.error("Error fetching customer packing settings:", error);
        throw error;
    }
}

/**
 * Update customer packing settings
 */
export async function updateCustomerPackingSettings(
    customerId: string,
    settings: {
        defaultPiecesPerBox?: number;
        defaultPiecesPerBag?: number;
        preferredPackagingType?: string;
        customInstructions?: string;
    },
) {
    try {
        const updated = await db.customer.update({
            where: { id: customerId },
            data: {
                packingSettings: settings,
            },
        });

        return updated;
    } catch (error) {
        console.error("Error updating customer packing settings:", error);
        throw error;
    }
}
