# Spec Tasks

## Tasks

- [ ] 1. Implementar Server Actions faltantes para CRUD completo
  - [ ] 1.1 Crear tests para update-subcustomer.ts
  - [ ] 1.2 Implementar `/features/customers/actions/update-subcustomer.ts`
  - [ ] 1.3 Crear tests para delete-subcustomer.ts
  - [ ] 1.4 Implementar `/features/customers/actions/delete-subcustomer.ts`
  - [ ] 1.5 Actualizar `/features/customers/actions/index.ts` con nuevos exports
  - [ ] 1.6 Verificar que todos los tests pasan

- [ ] 2. Crear componentes UI para modales CRUD
  - [ ] 2.1 Crear tests para SubcustomerForm.tsx
  - [ ] 2.2 Implementar `/features/customers/components/forms/SubcustomerForm.tsx`
  - [ ] 2.3 Crear tests para SubcustomerModal.tsx
  - [ ] 2.4 Implementar `/features/customers/components/modals/SubcustomerModal.tsx`
  - [ ] 2.5 Integrar modal con SubCustomerList existente
  - [ ] 2.6 Verificar que todos los tests pasan

- [ ] 3. Integrar tab "Subclientes" en página de detalles
  - [ ] 3.1 Crear tests para integración del tab
  - [ ] 3.2 Modificar `/app/dashboard/customers/[id]/details/page.tsx` para agregar Tabs de HeroUI
  - [ ] 3.3 Integrar SubCustomerList existente en el nuevo tab
  - [ ] 3.4 Conectar modales CRUD con el tab
  - [ ] 3.5 Agregar manejo de estados de loading y error
  - [ ] 3.6 Verificar que todos los tests pasan

- [ ] 4. Mostrar indicadores de jerarquía en lista principal
  - [ ] 4.1 Crear tests para indicadores visuales
  - [ ] 4.2 Modificar `/app/dashboard/customers/UnifiedClientPage.tsx` para mostrar "→ Subcliente"
  - [ ] 4.3 Agregar indentación visual con `ml-6` para subclientes
  - [ ] 4.4 Implementar Badge con contador de subclientes
  - [ ] 4.5 Optimizar queries para incluir datos de jerarquía
  - [ ] 4.6 Verificar que todos los tests pasan

- [ ] 5. Actualizar hooks y optimizar CustomerSelector
  - [ ] 5.1 Crear tests para nuevos hooks
  - [ ] 5.2 Agregar hooks para update/delete en `/features/customers/hooks/useCustomer.ts`
  - [ ] 5.3 Verificar funcionamiento de CustomerSelector existente para jerarquía
  - [ ] 5.4 Mejorar CustomerSelector si es necesario para selección de 2 niveles
  - [ ] 5.5 Optimizar performance de queries con SWR
  - [ ] 5.6 Verificar que todos los tests pasan