"use client";

import { useEffect } from "react";
import { Link } from "@heroui/link";
import { Snippet } from "@heroui/snippet";
import { Code } from "@heroui/code";
import { button as buttonStyles } from "@heroui/theme";
import { Spinner } from "@heroui/spinner";
import { Card, CardBody, CardFooter } from "@heroui/card";
import useSWR from "swr";

import { title, subtitle } from "@/shared/utils/primitives";
import {
    getDashboardStats,
    revalidateDashboard,
} from "@/features/dashboard/actions";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";
import { triggerRevalidation } from "@/shared/providers/swr-provider"; // Actualizado a la nueva ubicación

export default function Home() {
    // Utilizar SWR para obtener y cachear los datos mediante Server Action
    const { data, isLoading, error, mutate } = useSWR(
        "dashboard-stats",
        async () => {
            const result = await getDashboardStats();

            if (!result.success) {
                throw new Error(result.error || "Error al cargar estadísticas");
            }

            return result.data;
        },
        {
            revalidateOnFocus: true,
            refreshInterval: 30000, // Revalidar cada 30 segundos
        },
    );

    // Usar el hook existente de revalidación
    // Usamos un efecto para manejar la revalidación de manera compatible con el tipo
    const { isRevalidating } = useRevalidationListener("dashboard");

    // Cuando cambie isRevalidating a true, actualizamos los datos
    useEffect(() => {
        if (isRevalidating) {
            mutate();
        }
    }, [isRevalidating, mutate]);

    // Función para forzar actualización
    const handleRefresh = async () => {
        // Primero actualizar los datos en la UI
        await mutate();

        // Luego revalidar a nivel de servidor para otros usuarios
        await revalidateDashboard();

        // Emitir evento de revalidación compatible con el sistema existente
        triggerRevalidation("dashboard");
    };

    return (
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
            <div className="inline-block max-w-xl text-center justify-center">
                <span className={title()}>Lohari&nbsp;</span>
                <span className={title({ color: "violet" })}>
                    Dashboard&nbsp;
                </span>
                <br />
                <span className={title()}>
                    Sistema de gestión para tu empresa.
                </span>
                <div className={subtitle({ class: "mt-4" })}>
                    Administra tus modelos, colores, clientes y más.
                </div>
            </div>

            <div className="flex gap-3">
                <Link
                    className={buttonStyles({
                        color: "primary",
                        radius: "full",
                        variant: "shadow",
                    })}
                    href="/dashboard"
                >
                    Ir al Dashboard
                </Link>
                <Link
                    className={buttonStyles({
                        variant: "bordered",
                        radius: "full",
                    })}
                    href="/dashboard/models"
                >
                    Ver Modelos
                </Link>
            </div>

            {/* Estadísticas con SWR */}
            <div className="mt-8 w-full max-w-3xl">
                <Card
                    className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20"
                    shadow="sm"
                >
                    <CardBody>
                        <h3 className="text-lg font-semibold mb-4">
                            Estadísticas del Sistema
                        </h3>

                        {isLoading ? (
                            <div className="flex justify-center p-4">
                                <Spinner color="primary" />
                            </div>
                        ) : error ? (
                            <div className="text-red-500 text-center p-4">
                                Error cargando estadísticas
                            </div>
                        ) : (
                            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                                <div className="bg-white dark:bg-gray-800 p-3 rounded-xl shadow-sm">
                                    <p className="text-sm text-gray-500">
                                        Usuarios
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {(data as any)?.counts?.users || 0}
                                    </p>
                                </div>
                                <div className="bg-white dark:bg-gray-800 p-3 rounded-xl shadow-sm">
                                    <p className="text-sm text-gray-500">
                                        Modelos
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {(data as any)?.counts?.models || 0}
                                    </p>
                                </div>
                                <div className="bg-white dark:bg-gray-800 p-3 rounded-xl shadow-sm">
                                    <p className="text-sm text-gray-500">
                                        Colores
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {(data as any)?.counts?.colors || 0}
                                    </p>
                                </div>
                                <div className="bg-white dark:bg-gray-800 p-3 rounded-xl shadow-sm">
                                    <p className="text-sm text-gray-500">
                                        Clientes
                                    </p>
                                    <p className="text-2xl font-bold">
                                        {(data as any)?.counts?.customers || 0}
                                    </p>
                                </div>
                            </div>
                        )}
                    </CardBody>
                    <CardFooter className="text-xs text-gray-500 flex justify-between items-center">
                        <span>
                            Actualizado:{" "}
                            {data
                                ? new Date(
                                      (data as any).updatedAt,
                                  ).toLocaleString()
                                : "-"}
                        </span>
                        <button
                            className="text-primary hover:underline"
                            onClick={handleRefresh}
                        >
                            Actualizar
                        </button>
                    </CardFooter>
                </Card>
            </div>

            <div className="mt-4">
                <Snippet hideCopyButton hideSymbol variant="bordered">
                    <span>
                        Sistema desarrollado con{" "}
                        <Code color="primary">Next.js y SWR</Code>
                    </span>
                </Snippet>
            </div>
        </section>
    );
}
