generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x", "windows"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                String   @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
}

model Authenticator {
  credentialID         String   @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  user                 User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model NoteStatus {
  id        String   @id @default(cuid())
  name      String   @unique
  iconName  String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  notes     Note[]
}

model NoteImportance {
  id        String   @id @default(cuid())
  name      String   @unique
  iconName  String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  notes     Note[]
}

model Role {
  id        String   @id @default(cuid())
  name      String   @unique
  iconName  String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  users     User[]
}

model PackingStatus {
  id        String    @id @default(cuid())
  name      String    @unique
  iconName  String?
  color     String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  packings  Packing[]
}

model RejectionReason {
  id         String           @id @default(cuid())
  name       String           @unique
  iconName   String?
  color      String?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  rejections OrderRejection[]
}

model Contractor {
  id             String       @id @default(cuid())
  name           String       @unique
  notes          String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  firstName      String?
  lastName       String?
  middleName     String?
  secondLastName String?
  email          String?
  phone          String?
  assignments    Assignment[]
  remissions     Remission[]
  packings       Packing[]    @relation("PackingTransporter")
}

model User {
  id                 String               @id @default(cuid())
  name               String?
  email              String               @unique
  password           String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  emailVerified      DateTime?
  image              String?
  roleId             String
  accounts           Account[]
  authenticators     Authenticator[]
  notes              Note[]
  noteComments       NoteComment[]
  noteLikes          NoteLike[]
  rejections         OrderRejection[]
  OrderStatusHistory OrderStatusHistory[]
  operationLogs      OperationLog[]
  sessions           Session[]
  role               Role                 @relation(fields: [roleId], references: [id])
  packedPackings     Packing[]            @relation("PackedBy")
  verifiedPackings   Packing[]            @relation("VerifiedBy")
  qualityCheckPackings Packing[]          @relation("QualityCheckBy")
  packingQualityChecks PackingQualityCheck[]
}

model Customer {
  id           String     @id @default(cuid())
  name         String     @unique
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  parentId     String?
  parent       Customer?  @relation("CustomerHierarchy", fields: [parentId], references: [id])
  subCustomers Customer[] @relation("CustomerHierarchy")
  
  // Enhanced sub-client support
  displayName     String?    // For UI display (e.g., "Becktel - Reebok")
  packingSettings Json?      // Custom settings per customer
  
  orders       Order[]
  orderSubCustomers Order[]  @relation("OrderSubCustomers")  // NEW RELATION
  packings     Packing[]  @relation("CustomerPackings")
  subPackings  Packing[]  @relation("SubCustomerPackings")
}

model GarmentModel {
  id          String    @id @default(cuid())
  code        String    @unique
  description String
  basePrice   Float?    @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  garments    Garment[]
}

model Color {
  id        String    @id @default(cuid())
  name      String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  hexCode   String?
  garments  Garment[]
}

model Size {
  id        String        @id @default(cuid())
  code      String        @unique
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  garments  GarmentSize[]
}

model OrderStatus {
  id        String               @id @default(cuid())
  name      String               @unique
  iconName  String?
  color     String?
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt
  orders    Order[]
  histories OrderStatusHistory[]
}

model Order {
  id                    String               @id @default(cuid())
  transferNumber        String?
  cutOrder              String?
  batch                 String?
  receivedDate          DateTime             @default(now())
  estimatedDeliveryDate DateTime?
  deliveryDate          DateTime?
  customerId            String
  subCustomerId         String?              // NEW FIELD
  statusId              String
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  assignments           Assignment[]
  garments              Garment[]
  notes                 Note[]
  customer              Customer             @relation(fields: [customerId], references: [id])
  subCustomer           Customer?            @relation("OrderSubCustomers", fields: [subCustomerId], references: [id])
  status                OrderStatus          @relation(fields: [statusId], references: [id])
  parts                 OrderPart[]
  rejection             OrderRejection?
  histories             OrderStatusHistory[]
  packings              Packing[]
  packingSummaries      PackingSummary[]
}

model OrderPart {
  id        String   @id @default(cuid())
  code      String
  orderId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model Garment {
  id        String        @id @default(cuid())
  modelId   String
  colorId   String
  orderId   String
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  color     Color         @relation(fields: [colorId], references: [id])
  model     GarmentModel  @relation(fields: [modelId], references: [id])
  order     Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  sizes     GarmentSize[]
}

model GarmentSize {
  id             String          @id @default(cuid())
  totalQuantity  Int
  usedQuantity   Int             @default(0)
  sizeId         String
  garmentId      String
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  assignments    Assignment[]
  garment        Garment         @relation(fields: [garmentId], references: [id], onDelete: Cascade)
  size           Size            @relation(fields: [sizeId], references: [id])
  packingDetails PackingDetail[]
}

model AssignmentProgress {
  id           String     @id @default(cuid())
  type         String
  completed    Int
  assignmentId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  assignment   Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
}

model Assignment {
  id            String               @id @default(cuid())
  folio         String?              @unique
  contractorId  String
  garmentSizeId String
  quantity      Int
  defects       Int?                 @default(0)
  isCompleted   Boolean              @default(false)
  orderId       String
  version       Int                  @default(1)
  status        String               @default("ACTIVE")
  cancelledAt   DateTime?
  cancelReason  String?
  createdAt     DateTime             @default(now())
  updatedAt     DateTime             @updatedAt
  contractor    Contractor           @relation(fields: [contractorId], references: [id])
  garmentSize   GarmentSize          @relation(fields: [garmentSizeId], references: [id], onDelete: Cascade)
  order         Order                @relation(fields: [orderId], references: [id], onDelete: Cascade)
  progress      AssignmentProgress[]
  remissions    RemissionAssignment[]
  
  @@index([version])
  @@index([status])
}

model Remission {
  id             String                @id @default(cuid())
  folio          String                @unique
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @updatedAt
  contractorId   String
  notes          String?
  orderDetails   Json?
  printedAt      DateTime?
  status         String                @default("ACTIVE")
  contractor     Contractor            @relation(fields: [contractorId], references: [id])
  assignments    RemissionAssignment[]
  history        RemissionHistory[]
  remissionItems RemissionItem[]
}

model RemissionAssignment {
  id           String     @id @default(cuid())
  remissionId  String
  assignmentId String
  createdAt    DateTime   @default(now())
  remission    Remission  @relation(fields: [remissionId], references: [id], onDelete: Cascade)
  assignment   Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  
  @@unique([remissionId, assignmentId])
  @@index([remissionId])
  @@index([assignmentId])
}

model RemissionItem {
  id          String    @id @default(cuid())
  remissionId String
  modelCode   String
  colorName   String
  sizeCode    String
  quantity    Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  remission   Remission @relation(fields: [remissionId], references: [id], onDelete: Cascade)
}

model RemissionHistory {
  id          String    @id @default(cuid())
  remissionId String
  action      String
  timestamp   DateTime  @default(now())
  metadata    Json?
  remission   Remission @relation(fields: [remissionId], references: [id], onDelete: Cascade)
}

model FolioSequence {
  id           String   @id @default("single")
  lastDate     String
  lastSequence Int
  updatedAt    DateTime @updatedAt
}

model Packing {
  id            String          @id @default(cuid())
  folio         String          @unique
  code          String          @unique
  orderId       String?
  customerId    String
  subCustomerId String?
  deliveryDate  DateTime
  signatures    Json?
  pdfUrl        String?
  notes         String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  statusId      String
  printedAt     DateTime?
  
  // Información del remitente
  companyInfo      Json?       // Datos de la empresa remitente
  
  // Trazabilidad
  packedById      String?
  verifiedById    String?
  packedAt        DateTime?
  verifiedAt      DateTime?
  
  // Control de calidad
  qualityCheckPassed Boolean      @default(false)
  qualityNotes       String?
  qualityCheckAt     DateTime?
  qualityCheckById   String?
  
  // Información de transporte
  transporterId      String?
  vehicleInfo        String?
  driverName         String?
  driverPhone        String?
  transportNotes     String?     // Notas especiales para transporte
  
  // Información de empaque
  totalBoxes         Int         @default(0)
  totalBags          Int         @default(0)
  packingType        String?     // "mixto", "cajas", "bolsas"
  
  // Enhanced signature tracking
  transportSignature    String?
  transportSignedAt     DateTime?
  receiverSignature     String?
  receiverSignedAt      DateTime?
  receiverName          String?
  
  // Enhanced summary data
  packingSummaryBySize  Json?     // Detailed breakdown by size
  
  // Campos adicionales
  totalWeight        Float?
  totalVolume        Float?
  packagesCount      Int         @default(1)
  trackingNumber     String?
  
  // Relaciones
  order         Order?          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  customer      Customer        @relation("CustomerPackings", fields: [customerId], references: [id])
  subCustomer   Customer?       @relation("SubCustomerPackings", fields: [subCustomerId], references: [id])
  status        PackingStatus   @relation(fields: [statusId], references: [id])
  packedBy      User?           @relation("PackedBy", fields: [packedById], references: [id])
  verifiedBy    User?           @relation("VerifiedBy", fields: [verifiedById], references: [id])
  qualityCheckBy User?          @relation("QualityCheckBy", fields: [qualityCheckById], references: [id])
  transporter   Contractor?     @relation("PackingTransporter", fields: [transporterId], references: [id])
  details       PackingDetail[]
  history       PackingHistory[]
  qualityChecks PackingQualityCheck[]
  summaries     PackingSummary[]
}

model PackingDetail {
  id            String      @id @default(cuid())
  packingId     String
  garmentSizeId String
  quantity      Int
  comments      String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @default(now())
  
  // Información del producto
  modelCode        String?     // Código del modelo
  colorName        String?     // Nombre del color
  partNumber       String?     // Número de partida
  
  // Clasificación de calidad
  qualityType      String      @default("primera") // "primera", "segunda", "manchada", "incompleta"
  qualityPassed    Boolean     @default(true)
  defects          Int         @default(0)
  verifiedQuantity Int?
  
  // Información de empaque
  packagingType    String?     // "caja" o "bolsa"
  packagingUnits   Int         @default(0) // Cantidad de cajas/bolsas
  piecesPerUnit    Int         @default(0) // Piezas por unidad
  loosePieces      Int         @default(0) // Piezas sueltas
  boxNumber        Int?
  weight           Float?
  
  // Enhanced packaging info
  boxesCount      Int?
  bagsFirstCount  Int?
  bagsSecondCount Int?
  loosePiecesInfo Json?  // Detailed info about pieces not in boxes
  
  garmentSize   GarmentSize @relation(fields: [garmentSizeId], references: [id], onDelete: Cascade)
  packing       Packing     @relation(fields: [packingId], references: [id], onDelete: Cascade)
}

model PackingHistory {
  id        String   @id @default(cuid())
  packingId String
  action    String
  timestamp DateTime @default(now())
  metadata  Json?
  packing   Packing  @relation(fields: [packingId], references: [id], onDelete: Cascade)
}

model PackingSequence {
  id           String   @id @default("single")
  lastDate     String
  lastSequence Int
  updatedAt    DateTime @updatedAt
}

model Note {
  id           String         @id @default(cuid())
  content      String
  statusId     String
  authorId     String
  orderId      String
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  importanceId String
  mentions     String[]       @default([])
  tags         String[]       @default([])
  author       User           @relation(fields: [authorId], references: [id])
  importance   NoteImportance @relation(fields: [importanceId], references: [id])
  order        Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  status       NoteStatus     @relation(fields: [statusId], references: [id])
  comments     NoteComment[]
  likes        NoteLike[]

  @@index([orderId, createdAt])
  @@index([authorId])
  @@index([importanceId])
  @@index([statusId])
}

model NoteComment {
  id        String        @id @default(cuid())
  content   String
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  noteId    String
  authorId  String
  parentId  String?
  author    User          @relation(fields: [authorId], references: [id])
  note      Note          @relation(fields: [noteId], references: [id], onDelete: Cascade)
  parent    NoteComment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies   NoteComment[] @relation("CommentReplies")

  @@index([noteId])
  @@index([authorId])
  @@index([parentId])
}

model NoteLike {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  noteId    String
  userId    String
  note      Note     @relation(fields: [noteId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id])

  @@unique([noteId, userId])
  @@index([noteId])
  @@index([userId])
}

model OrderRejection {
  id           String          @id @default(cuid())
  orderId      String          @unique
  description  String?
  rejectedById String
  evidenceUrl  String?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  reasonId     String
  order        Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  reason       RejectionReason @relation(fields: [reasonId], references: [id])
  rejectedBy   User            @relation(fields: [rejectedById], references: [id])

  @@index([reasonId])
  @@index([createdAt])
}

model OrderStatusHistory {
  id        String      @id @default(cuid())
  orderId   String
  statusId  String
  userId    String
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  order     Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
  status    OrderStatus @relation(fields: [statusId], references: [id])
  changedBy User        @relation(fields: [userId], references: [id])

  @@index([createdAt])
}


// Operation Log for transaction integrity and audit trail
model OperationLog {
  id            String    @id @default(cuid())
  operationType String    // 'ASSIGNMENT_BATCH', 'REMISSION_CREATE', etc.
  entityIds     String[]  // Array of affected entity IDs
  userId        String    // Who initiated the operation
  status        String    // 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'COMPENSATED'
  metadata      Json      // Operation-specific data
  error         String?   // Error details if failed
  startedAt     DateTime  @default(now())
  completedAt   DateTime?
  compensatedAt DateTime?
  
  // Relations
  user          User      @relation(fields: [userId], references: [id])
  
  // Indexes for efficient querying
  @@index([status, startedAt])
  @@index([operationType, status])
  @@index([userId])
  @@index([startedAt])
}

// Nueva tabla para control de calidad de packings
model PackingQualityCheck {
  id              String   @id @default(cuid())
  packingId       String
  checkType       String   // 'quantity', 'quality', 'packaging', 'labeling'
  passed          Boolean
  notes           String?
  checkedById     String
  createdAt       DateTime @default(now())
  
  // Relaciones
  packing         Packing  @relation(fields: [packingId], references: [id], onDelete: Cascade)
  checkedBy       User     @relation(fields: [checkedById], references: [id])
  
  // Índices
  @@index([packingId])
  @@index([checkType])
  @@index([createdAt])
}

// Nueva tabla para resumen de empaque por orden
model PackingSummary {
  id                  String    @id @default(cuid())
  packingId           String
  orderId             String?
  cutOrderNumber      String?   // Número de orden de corte
  
  // Totales por calidad
  totalFirstQuality   Int       @default(0)
  totalSecondQuality  Int       @default(0)
  totalDefective      Int       @default(0) // Manchadas
  totalIncomplete     Int       @default(0)
  
  // Información de empaque por talla
  boxesBySize         Json?     // {"S": 5, "M": 10, ...}
  bagsBySize          Json?     // {"S": 2, "M": 4, ...}
  piecesBySize        Json?     // Piezas sueltas {"S": 5, "M": 8, ...}
  
  // Totales generales
  totalBoxes          Int       @default(0)
  totalBags           Int       @default(0)
  totalLoosePieces    Int       @default(0)
  
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  
  packing             Packing   @relation(fields: [packingId], references: [id], onDelete: Cascade)
  order               Order?    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@unique([packingId, orderId])
  @@index([packingId])
  @@index([orderId])
}

// Configuración de la empresa (remitente)
model CompanySettings {
  id              String    @id @default("default")
  companyName     String
  companyLogo     String?   // URL del logo
  rfc             String?   // RFC o identificación fiscal
  address         String?
  city            String?
  state           String?
  postalCode      String?
  country         String?
  phone           String?
  email           String?
  website         String?
  
  // Configuración de empaque por defecto
  defaultBoxCapacity    Int?      @default(25)
  defaultBagCapacity    Int?      @default(20)
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Rate limiting log for tracking API requests
model RateLimitLog {
  id          String   @id @default(cuid())
  namespace   String   // e.g., "auth", "api", etc.
  identifier  String   // IP address or user ID
  createdAt   DateTime @default(now())
  
  @@index([namespace, identifier, createdAt])
  @@index([createdAt])
}
