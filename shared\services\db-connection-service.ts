// Este archivo es mantenido por compatibilidad
// Redirige todo al nuevo sistema unificado

import { PrismaClient } from "@prisma/client";

import { prisma, executeWithRetry } from "../lib/prisma";

// Log de deprecación
if (process.env.NODE_ENV === "development") {
    // REMOVED: console.warn("[Deprecation] db-connection-service.ts está deprecado. Use '../lib/prisma' directamente.");
}

/**
 * Clase mantenida por compatibilidad
 * @deprecated Use las funciones exportadas desde '../lib/prisma'
 */
export class DbConnectionService {
    private static instance: DbConnectionService;

    private constructor() {}

    public static getInstance(): DbConnectionService {
        if (!DbConnectionService.instance) {
            DbConnectionService.instance = new DbConnectionService();
        }

        return DbConnectionService.instance;
    }

    public getPrismaClient(): PrismaClient {
        return prisma;
    }

    public async executeWithRetry<T>(
        operation: () => Promise<T>,
        options: any = {},
    ): Promise<T> {
        return executeWithRetry(operation, {
            maxRetries: options.maxRetries ?? 3,
            retryDelay: options.initialDelay ?? 100,
        });
    }

    public async resetConnection(): Promise<void> {
        // REMOVED: console.warn("[DbConnectionService] resetConnection está deprecado");
        // No hacer nada - el nuevo sistema maneja esto automáticamente
    }

    public async forceConnectionReset(): Promise<void> {
        // REMOVED: console.warn("[DbConnectionService] forceConnectionReset está deprecado");
        // No hacer nada - el nuevo sistema maneja esto automáticamente
    }
}

// Exportaciones por compatibilidad
export const dbService = DbConnectionService.getInstance();
export const db = prisma;
