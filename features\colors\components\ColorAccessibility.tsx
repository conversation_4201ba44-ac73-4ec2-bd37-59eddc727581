"use client";

import React, { useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON>ody,
    Card<PERSON>ooter,
    Accordion,
    AccordionItem,
    <PERSON>,
    <PERSON><PERSON>,
    Switch,
    Tooltip,
} from "@heroui/react";
import {
    ShieldCheckIcon,
    ExclamationTriangleIcon,
    ChevronDownIcon,
    ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import {
    calculateContrastRatio,
    getWCAGLevel,
    simulateColorBlindness,
    suggestAccessibleAlternatives,
    isColorDark,
} from "../utils/colorUtils";

interface ColorAccessibilityProps {
    hexCode: string;
    className?: string;
}

type ColorBlindnessType =
    | "normal"
    | "protanopia"
    | "deuteranopia"
    | "tritanopia"
    | "achromatopsia";

export function ColorAccessibility({
    hexCode,
    className = "",
}: ColorAccessibilityProps) {
    const [expanded, setExpanded] = useState<boolean>(false);
    const [selectedColorblindness, setSelectedColorblindness] =
        useState<ColorBlindnessType>("normal");
    const [contrastBackground, setContrastBackground] = useState<
        "white" | "black"
    >("white");

    // Calcular ratios de contraste
    const whiteContrastRatio = calculateContrastRatio(hexCode, "#FFFFFF");
    const blackContrastRatio = calculateContrastRatio(hexCode, "#000000");

    // Determinar niveles WCAG
    const whiteWcagLevel = getWCAGLevel(whiteContrastRatio);
    const blackWcagLevel = getWCAGLevel(blackContrastRatio);

    // Determinar si hay problemas de accesibilidad
    const hasAccessibilityIssues =
        whiteWcagLevel === "Fail" || blackWcagLevel === "Fail";

    // Simular color para daltonismo
    const simulatedColor =
        selectedColorblindness !== "normal"
            ? simulateColorBlindness(hexCode, selectedColorblindness)
            : hexCode;

    // Sugerir colores alternativos
    const suggestedAlternatives = hasAccessibilityIssues
        ? suggestAccessibleAlternatives(
              hexCode,
              contrastBackground === "white" ? "#FFFFFF" : "#000000",
          )
        : [];

    // Obtener color de fondo para el chip de estado
    const getStatusColor = () => {
        if (whiteWcagLevel === "AAA" || blackWcagLevel === "AAA")
            return "success";
        if (whiteWcagLevel === "AA" || blackWcagLevel === "AA")
            return "primary";
        if (whiteWcagLevel === "A" || blackWcagLevel === "A") return "warning";

        return "danger";
    };

    // Obtener texto de estado
    const getStatusText = () => {
        const bestLevel = ["AAA", "AA", "A", "Fail"].find(
            (level) => whiteWcagLevel === level || blackWcagLevel === level,
        );

        if (bestLevel === "AAA") return "Excelente";
        if (bestLevel === "AA") return "Bueno";
        if (bestLevel === "A") return "Aceptable";

        return "Insuficiente";
    };

    // Color-blindness type name in Spanish
    const getColorBlindnessName = (type: ColorBlindnessType): string => {
        switch (type) {
            case "protanopia":
                return "Protanopía (rojo)";
            case "deuteranopia":
                return "Deuteranopía (verde)";
            case "tritanopia":
                return "Tritanopía (azul)";
            case "achromatopsia":
                return "Acromatopsia (sin color)";
            default:
                return "Normal";
        }
    };

    return (
        <Card className={`overflow-hidden ${className}`}>
            <CardHeader className="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-gray-800/50">
                <div className="flex items-center gap-2">
                    <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">
                        Accesibilidad
                    </h3>
                    <Chip
                        color={getStatusColor()}
                        size="sm"
                        startContent={
                            hasAccessibilityIssues ? (
                                <ExclamationTriangleIcon className="w-3 h-3" />
                            ) : (
                                <ShieldCheckIcon className="w-3 h-3" />
                            )
                        }
                        variant="flat"
                    >
                        {getStatusText()}
                    </Chip>
                </div>
                <Button
                    isIconOnly
                    aria-label={
                        expanded
                            ? "Contraer panel de accesibilidad"
                            : "Expandir panel de accesibilidad"
                    }
                    size="sm"
                    variant="light"
                    onPress={() => setExpanded(!expanded)}
                >
                    {expanded ? (
                        <ChevronDownIcon className="w-4 h-4" />
                    ) : (
                        <ChevronRightIcon className="w-4 h-4" />
                    )}
                </Button>
            </CardHeader>

            <CardBody className={`p-4 ${!expanded ? "hidden" : ""}`}>
                <div className="space-y-4">
                    {/* Ratios de contraste */}
                    <div className="grid grid-cols-2 gap-3">
                        <div
                            className="flex flex-col items-center p-3 border rounded-lg bg-white relative overflow-hidden"
                            style={{
                                color: isColorDark(hexCode) ? "#fff" : "#000",
                            }}
                        >
                            <div
                                className="absolute inset-0 opacity-90"
                                style={{ backgroundColor: hexCode }}
                            />
                            <div className="relative z-10 flex flex-col items-center">
                                <p className="font-semibold">Sobre blanco</p>
                                <div className="text-center mt-1">
                                    <p className="text-lg font-bold">
                                        {whiteContrastRatio.toFixed(2)}:1
                                    </p>
                                    <Chip
                                        color={
                                            whiteWcagLevel === "Fail"
                                                ? "danger"
                                                : "success"
                                        }
                                        size="sm"
                                    >
                                        {whiteWcagLevel}
                                    </Chip>
                                </div>
                            </div>
                        </div>

                        <div
                            className="flex flex-col items-center p-3 border rounded-lg bg-black relative overflow-hidden"
                            style={{
                                color: isColorDark(hexCode) ? "#fff" : "#000",
                            }}
                        >
                            <div
                                className="absolute inset-0 opacity-90"
                                style={{ backgroundColor: hexCode }}
                            />
                            <div className="relative z-10 flex flex-col items-center">
                                <p className="font-semibold">Sobre negro</p>
                                <div className="text-center mt-1">
                                    <p className="text-lg font-bold">
                                        {blackContrastRatio.toFixed(2)}:1
                                    </p>
                                    <Chip
                                        color={
                                            blackWcagLevel === "Fail"
                                                ? "danger"
                                                : "success"
                                        }
                                        size="sm"
                                    >
                                        {blackWcagLevel}
                                    </Chip>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Simulación de daltonismo */}
                    <Accordion
                        className="px-0"
                        itemClasses={{
                            base: "py-0",
                            title: "text-sm font-medium text-gray-700 dark:text-gray-300",
                            trigger: "px-0",
                            content: "px-0",
                        }}
                        variant="light"
                    >
                        <AccordionItem
                            key="color-blindness"
                            aria-label="Simulación de daltonismo"
                            title="Simulación de daltonismo"
                        >
                            <div className="pt-2 pb-4">
                                <div className="flex flex-wrap gap-2 mb-3">
                                    {(
                                        [
                                            "normal",
                                            "protanopia",
                                            "deuteranopia",
                                            "tritanopia",
                                            "achromatopsia",
                                        ] as ColorBlindnessType[]
                                    ).map((type) => (
                                        <Chip
                                            key={type}
                                            className="cursor-pointer"
                                            color={
                                                selectedColorblindness === type
                                                    ? "primary"
                                                    : "default"
                                            }
                                            variant={
                                                selectedColorblindness === type
                                                    ? "solid"
                                                    : "bordered"
                                            }
                                            onClick={() =>
                                                setSelectedColorblindness(type)
                                            }
                                        >
                                            {getColorBlindnessName(type)}
                                        </Chip>
                                    ))}
                                </div>

                                <div className="flex items-center gap-3">
                                    <div
                                        className="w-10 h-10 rounded-md border"
                                        style={{ backgroundColor: hexCode }}
                                    />
                                    <div className="text-gray-500 dark:text-gray-400">
                                        →
                                    </div>
                                    <div
                                        className="w-10 h-10 rounded-md border"
                                        style={{
                                            backgroundColor: simulatedColor,
                                        }}
                                    />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium">
                                            {selectedColorblindness === "normal"
                                                ? "Color original"
                                                : `Percibido con ${getColorBlindnessName(selectedColorblindness).toLowerCase()}`}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </AccordionItem>
                    </Accordion>

                    {/* Recomendaciones */}
                    {hasAccessibilityIssues && (
                        <div className="border border-amber-200 bg-amber-50 dark:bg-amber-900/20 dark:border-amber-900/30 p-3 rounded-lg">
                            <h4 className="font-medium text-amber-800 dark:text-amber-400 flex items-center gap-1 mb-2">
                                <ExclamationTriangleIcon className="w-4 h-4" />
                                Recomendaciones
                            </h4>
                            <p className="text-sm text-amber-700 dark:text-amber-300 mb-3">
                                Este color no cumple con el nivel AA de
                                contraste WCAG sobre fondo{" "}
                                {whiteWcagLevel === "Fail" ? "blanco" : "negro"}
                                . Considera usar una versión{" "}
                                {whiteWcagLevel === "Fail"
                                    ? "más oscura"
                                    : "más clara"}{" "}
                                de este color para texto y elementos
                                interactivos.
                            </p>

                            <div className="mt-4 mb-2">
                                <p className="text-xs text-amber-700 dark:text-amber-300 mb-2">
                                    Alternativas sugeridas:
                                </p>
                                <div className="flex gap-2">
                                    {suggestedAlternatives.map(
                                        (color, index) => (
                                            <Tooltip
                                                key={index}
                                                content={`${color} - Contraste: ${calculateContrastRatio(color, contrastBackground === "white" ? "#FFFFFF" : "#000000").toFixed(2)}:1`}
                                            >
                                                <div
                                                    className="w-8 h-8 rounded-md border border-gray-200 dark:border-gray-700 cursor-pointer hover:scale-110 transition-transform"
                                                    style={{
                                                        backgroundColor: color,
                                                    }}
                                                />
                                            </Tooltip>
                                        ),
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </CardBody>

            {expanded && (
                <CardFooter className="flex justify-end p-2 bg-gray-50 dark:bg-gray-800/30 border-t border-gray-100 dark:border-gray-800">
                    <div className="flex justify-between items-center w-full">
                        <div className="flex items-center gap-2">
                            <motion.div
                                animate={{ opacity: 1 }}
                                className={`w-3 h-3 rounded-full ${hasAccessibilityIssues ? "bg-red-500" : "bg-green-500"}`}
                                initial={{ opacity: 0 }}
                                transition={{ duration: 0.3 }}
                            />
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {hasAccessibilityIssues
                                    ? "Problemas detectados"
                                    : "Sin problemas"}
                            </span>
                        </div>

                        <Switch
                            isSelected={contrastBackground === "black"}
                            size="sm"
                            onValueChange={() =>
                                setContrastBackground(
                                    contrastBackground === "white"
                                        ? "black"
                                        : "white",
                                )
                            }
                        >
                            <span className="text-xs">
                                Evaluar sobre{" "}
                                {contrastBackground === "white"
                                    ? "blanco"
                                    : "negro"}
                            </span>
                        </Switch>
                    </div>
                </CardFooter>
            )}
        </Card>
    );
}
