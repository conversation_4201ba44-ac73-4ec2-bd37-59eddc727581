"use server";

import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

import { modelSchema } from "./schema";
import { validateModelCode } from "./validate";

// Obtener helpers de revalidación para la entidad "model"
const { revalidateCache } = createServerRevalidation("model");

/**
 * Actualiza un modelo existente
 */
export async function updateModel(
    id: string,
    data: { code: string; description: string; basePrice?: number },
) {
    if (!id) return { success: false, error: "ID no válido" };

    try {
        // Validar datos con mejor manejo de errores
        const validatedData = modelSchema.parse(data);

        // Verificar si el código ya existe (excluyendo el modelo actual)
        const codeValidation = await validateModelCode(validatedData.code, id);

        if (!codeValidation || !codeValidation.success) {
            return {
                success: false,
                error:
                    codeValidation?.error ||
                    "Error al validar código de modelo",
            };
        }

        // Verificar el resultado de la validación
        if (!codeValidation.data || !(codeValidation.data as any)?.isValid) {
            return {
                success: false,
                error: "El código del modelo ya existe en el sistema",
            };
        }

        // Actualizar el modelo en la base de datos
        const model = await db.garmentModel.update({
            where: { id },
            data: {
                code: validatedData.code,
                description: validatedData.description,
                basePrice:
                    validatedData.basePrice !== undefined
                        ? validatedData.basePrice
                        : undefined,
            },
        });

        // Revalidar caché usando el helper genérico
        revalidateCache(id);

        return { success: true, data: model };
    } catch (error) {
        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos de modelo inválidos",
            };
        }

        // Usar el helper para manejar otros errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al actualizar modelo");
    }
}
