import { PrismaClient } from "@prisma/client";

/**
 * Utilidad para construir condiciones where para consultas Prisma
 * @param baseCondition Condición base para el where
 * @param conditionalFilters Objetos condicionales que se añaden solo si no son null/undefined
 */
export function buildWhereClause<T>(
    baseCondition: Record<string, any>,
    conditionalFilters: Record<string, any | null | undefined>,
): Record<string, any> {
    // Crear una copia directa del objeto base para no modificarlo
    const where = { ...baseCondition };

    // Añadir solo filtros que no sean undefined/null
    Object.entries(conditionalFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            // Si el valor es un objeto y no contiene operadores de Prisma
            // (como equals, contains, etc.), verificamos si necesita convertirse
            if (
                typeof value === "object" &&
                !Array.isArray(value) &&
                !Object.keys(value).some((k) =>
                    [
                        "equals",
                        "in",
                        "not",
                        "contains",
                        "startsWith",
                        "endsWith",
                        "mode",
                        "gt",
                        "gte",
                        "lt",
                        "lte",
                    ].includes(k),
                )
            ) {
                // Si el objeto es simple y no tiene operadores Prisma, usamos equals
                if (Object.keys(value).length > 0) {
                    where[key] = { equals: value };
                } else {
                    // Si es un objeto vacío, simplemente usamos el valor directo
                    where[key] = value;
                }
            } else {
                // Para valores simples o filtros ya formateados para Prisma
                where[key] = value;
            }
        }
    });

    // Validación adicional: verificar estructura del resultado final
    Object.entries(where).forEach(([key, value]) => {
        // Si el valor es un objeto que contiene 'orderId', 'page', 'pageSize', etc.
        // es probable que sea una confusión de parámetros, los extraemos
        if (
            typeof value === "object" &&
            !Array.isArray(value) &&
            value !== null &&
            (value.hasOwnProperty("orderId") ||
                value.hasOwnProperty("page") ||
                value.hasOwnProperty("pageSize") ||
                value.hasOwnProperty("filters"))
        ) {
            // REMOVED: console.warn(`⚠️ Corrección de estructura para ${key}: `, value);
            // Si hay un 'orderId' dentro del objeto, extraerlo como valor directo
            if (value.orderId && typeof value.orderId === "string") {
                where[key] = value.orderId;
            } else {
                // Si no podemos determinar cómo corregirlo, usamos equals como valor simple
                where[key] = { equals: value };
            }
        }
    });

    return where;
}

/**
 * Utilidad para consultas paginadas que necesitan count + findMany
 * @param model El modelo de Prisma a consultar
 * @param params Parámetros de la consulta incluyendo where, orderBy, include, paginación
 * @returns Resultados paginados con metadatos
 */
export async function paginatedQuery<T extends keyof PrismaClient>(
    model: T,
    params: {
        where: any;
        orderBy?: any;
        include?: any;
        select?: any;
        page?: number;
        pageSize?: number;
    },
    prisma: PrismaClient,
) {
    const {
        where,
        orderBy = { createdAt: "desc" },
        include,
        select,
        page = 1,
        pageSize = 10,
    } = params;

    // Get total count
    const totalCount = await (prisma[model] as any).count({ where });

    // Calculate pagination
    const skip = (page - 1) * pageSize;

    // Create query params
    const queryParams: any = {
        where,
        orderBy,
        skip,
        take: pageSize,
    };

    // Add include or select if provided
    if (include) queryParams.include = include;
    if (select) queryParams.select = select;

    // Get records with pagination
    const records = await (prisma[model] as any).findMany(queryParams);

    return {
        records,
        totalCount,
        hasMore: totalCount > skip + records.length,
        page,
        pageSize,
        totalPages: Math.ceil(totalCount / pageSize),
    };
}

/**
 * Función para construir una condición de búsqueda por texto para múltiples campos
 * @param searchText Texto a buscar
 * @param fields Campos en los que buscar
 * @returns Objeto con condición OR para Prisma
 */
export function buildTextSearchCondition(searchText: string, fields: string[]) {
    if (!searchText || !fields.length) return undefined;

    return {
        OR: fields.map((field) => ({
            [field]: {
                contains: searchText,
                mode: "insensitive",
            },
        })),
    };
}

/**
 * Maneja errores específicos de Prisma y proporciona respuestas estructuradas
 * @param error Error capturado
 * @param defaultMessage Mensaje por defecto
 * @returns Respuesta de error estructurada
 */
export function handlePrismaError(
    error: any,
    defaultMessage = "Error en la operación de base de datos",
) {
    // REMOVED: console.error("Prisma error:", error);

    let errorMessage = defaultMessage;
    let errorCode = "DB_ERROR";

    // Si es un error de validación de Prisma, extraer más detalles
    if (error.constructor?.name === "PrismaClientValidationError") {
        errorMessage = "Error de validación en la consulta";
        errorCode = "VALIDATION_ERROR";

        // Intentar extraer información más específica del mensaje de error
        const errorStr = String(error);

        if (errorStr.includes("Unknown argument")) {
            const match = errorStr.match(/Unknown argument `([^`]+)`/);

            if (match && match[1]) {
                errorMessage += `: Argumento desconocido '${match[1]}'`;
            }
        }
    } else if (error.code) {
        // Manejar códigos de error específicos de Prisma
        switch (error.code) {
            case "P2002":
                errorMessage = "Ya existe un registro con esos datos";
                errorCode = "UNIQUE_CONSTRAINT";
                break;
            case "P2025":
                errorMessage = "Registro no encontrado";
                errorCode = "NOT_FOUND";
                break;
            case "P2003":
                errorMessage =
                    "Error de referencia: el registro relacionado no existe";
                errorCode = "FOREIGN_KEY";
                break;
        }
    } else if (error.constructor?.name === "PrismaClientKnownRequestError") {
        errorMessage = "Error conocido en la consulta";
        errorCode = "KNOWN_ERROR";
    }

    return {
        success: false,
        error: errorMessage,
        errorCode,
        details:
            process.env.NODE_ENV === "development" ? error.message : undefined,
    };
}
