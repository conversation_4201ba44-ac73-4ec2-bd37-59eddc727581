import React from "react";
import { Document, Page, View, Text } from "@react-pdf/renderer";

import { RemissionPDF } from "@/types/remission/pdf";

import { remissionStyles as styles } from "../styles/remission";
import { PDFHeader } from "../components/PDFHeader";
import { PDFContractor } from "../components/PDFContractor";
import { PDFItemsTable } from "../components/PDFItemsTable";
import { PDFFooter } from "../components/PDFFooter";

// Note: Custom fonts would need to be registered here
// Font.register({
//   family: 'Inter',
//   fonts: [
//     { src: '/fonts/Inter-Regular.ttf' },
//     { src: '/fonts/Inter-Bold.ttf', fontWeight: 'bold' },
//   ],
// });

interface RemissionTemplateProps {
    data: RemissionPDF;
    orientation?: "portrait" | "landscape";
}

export const RemissionTemplate: React.FC<RemissionTemplateProps> = ({
    data,
    orientation = "portrait",
}) => {
    return (
        <Document>
            <Page orientation={orientation} size="A4" style={styles.page}>
                <PDFHeader date={(data as any).date} folio={data.folio} />

                <PDFContractor contractor={data.contractor as any} />

                <PDFItemsTable items={data.items as any} />

                {/* Notes Section */}
                {(data as any).externalNotes && (
                    <View style={styles.notesSection}>
                        <Text style={[styles.sectionTitle, styles.mb5]}>
                            OBSERVACIONES
                        </Text>
                        <Text style={styles.notesText}>
                            {(data as any).externalNotes}
                        </Text>
                    </View>
                )}

                <PDFFooter
                    authorizedBy={(data as any).authorizedBy}
                    deliveredBy={(data as any).deliveredBy}
                    receivedBy={(data as any).receivedBy}
                />
            </Page>
        </Document>
    );
};
