"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { ChevronRightIcon, HomeIcon } from "@heroicons/react/24/outline";

interface Breadcrumb {
    label: string;
    href?: string;
    icon?: React.ReactNode;
}

interface DashboardHeaderProps {
    title: string;
    subtitle?: string;
    breadcrumbs?: Breadcrumb[];
    actions?: React.ReactNode;
}

export function DashboardHeader({
    title,
    subtitle,
    breadcrumbs = [],
    actions,
}: DashboardHeaderProps) {
    const allBreadcrumbs = [
        {
            label: "Dashboard",
            href: "/dashboard",
            icon: <HomeIcon className="w-4 h-4" />,
        },
        ...breadcrumbs,
    ];

    return (
        <div className="py-8">
            {/* Breadcrumbs */}
            <nav className="flex items-center space-x-2 text-sm mb-4">
                {allBreadcrumbs.map((crumb, index) => (
                    <React.Fragment key={index}>
                        {index > 0 && (
                            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                        )}
                        <motion.div
                            animate={{ opacity: 1, x: 0 }}
                            initial={{ opacity: 0, x: -10 }}
                            transition={{ delay: index * 0.1 }}
                        >
                            {crumb.href ? (
                                <Link
                                    className="flex items-center gap-1.5 text-gray-600 hover:text-primary transition-colors duration-200"
                                    href={crumb.href}
                                >
                                    {crumb.icon}
                                    <span>{crumb.label}</span>
                                </Link>
                            ) : (
                                <span className="flex items-center gap-1.5 text-gray-900 dark:text-gray-100 font-medium">
                                    {crumb.icon}
                                    {crumb.label}
                                </span>
                            )}
                        </motion.div>
                    </React.Fragment>
                ))}
            </nav>

            {/* Title and Actions */}
            <div className="flex items-center justify-between">
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                >
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-400 bg-clip-text text-transparent">
                        {title}
                    </h1>
                    {subtitle && (
                        <p className="mt-2 text-gray-600 dark:text-gray-400">
                            {subtitle}
                        </p>
                    )}
                </motion.div>

                {actions && (
                    <motion.div
                        animate={{ opacity: 1, x: 0 }}
                        className="flex items-center gap-3"
                        initial={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                    >
                        {actions}
                    </motion.div>
                )}
            </div>
        </div>
    );
}
