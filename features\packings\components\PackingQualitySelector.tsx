"use client";

import type { QualityType } from "@/lib/types/packing";

import React from "react";
import { Card, CardBody, Chip, RadioGroup, Radio } from "@heroui/react";
import { CheckCircle, AlertCircle, XCircle, Package2 } from "lucide-react";

interface PackingQualitySelectorProps {
    value: QualityType;
    onChange: (value: QualityType) => void;
    disabled?: boolean;
    showStats?: boolean;
    stats?: {
        primera: number;
        segunda: number;
        manchada: number;
        incompleta: number;
    };
}

const qualityOptions = [
    {
        value: "primera" as QualityType,
        label: "Primera Calidad",
        description: "Productos en perfectas condiciones",
        icon: CheckCircle,
        color: "success" as const,
        chipColor: "success" as const,
    },
    {
        value: "segunda" as QualityType,
        label: "Segunda Calidad",
        description: "Productos con defectos menores",
        icon: AlertCircle,
        color: "warning" as const,
        chipColor: "warning" as const,
    },
    {
        value: "manchada" as QualityType,
        label: "Manchadas",
        description: "Productos con manchas visibles",
        icon: XCircle,
        color: "danger" as const,
        chipColor: "danger" as const,
    },
    {
        value: "incompleta" as QualityType,
        label: "Incompletas",
        description: "Productos con partes faltantes",
        icon: Package2,
        color: "default" as const,
        chipColor: "default" as const,
    },
];

export function PackingQualitySelector({
    value,
    onChange,
    disabled = false,
    showStats = false,
    stats,
}: PackingQualitySelectorProps) {
    return (
        <div className="space-y-4">
            <RadioGroup
                classNames={{
                    base: "w-full",
                }}
                isDisabled={disabled}
                value={value}
                onValueChange={(val) => onChange(val as QualityType)}
            >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {qualityOptions.map((option) => {
                        const Icon = option.icon;
                        const count = stats?.[option.value] || 0;

                        return (
                            <Radio
                                key={option.value}
                                classNames={{
                                    base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between",
                                    wrapper: "hidden",
                                    labelWrapper: "w-full",
                                }}
                                value={option.value}
                            >
                                <Card
                                    className={`w-full cursor-pointer transition-all ${
                                        value === option.value
                                            ? `ring-2 ring-${option.color} bg-${option.color}-50 dark:bg-${option.color}-900/20`
                                            : "hover:shadow-md"
                                    }`}
                                    isPressable={!disabled}
                                    onPress={() =>
                                        !disabled && onChange(option.value)
                                    }
                                >
                                    <CardBody className="p-4">
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start gap-3">
                                                <div
                                                    className={`p-2 rounded-lg bg-${option.color}-100 dark:bg-${option.color}-900/30`}
                                                >
                                                    <Icon
                                                        className={`w-5 h-5 text-${option.color}-600 dark:text-${option.color}-400`}
                                                    />
                                                </div>
                                                <div className="space-y-1">
                                                    <p className="font-medium text-sm">
                                                        {option.label}
                                                    </p>
                                                    <p className="text-xs text-default-500">
                                                        {option.description}
                                                    </p>
                                                </div>
                                            </div>

                                            {showStats && stats && (
                                                <Chip
                                                    color={option.chipColor}
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    {count} pzs
                                                </Chip>
                                            )}
                                        </div>

                                        {value === option.value && (
                                            <div
                                                className={`mt-3 pt-3 border-t border-${option.color}-200 dark:border-${option.color}-800`}
                                            >
                                                <p className="text-xs text-default-600">
                                                    Seleccionado para este
                                                    empaque
                                                </p>
                                            </div>
                                        )}
                                    </CardBody>
                                </Card>
                            </Radio>
                        );
                    })}
                </div>
            </RadioGroup>

            {showStats && stats && (
                <Card className="bg-default-50 dark:bg-default-100/50">
                    <CardBody className="p-4">
                        <h4 className="text-sm font-medium mb-3">
                            Resumen de Calidad
                        </h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                            {qualityOptions.map((option) => {
                                const count = stats[option.value] || 0;
                                const total = Object.values(stats).reduce(
                                    (sum, val) => sum + val,
                                    0,
                                );
                                const percentage =
                                    total > 0
                                        ? Math.round((count / total) * 100)
                                        : 0;

                                return (
                                    <div
                                        key={option.value}
                                        className="text-center"
                                    >
                                        <p className="text-2xl font-semibold">
                                            {count}
                                        </p>
                                        <p className="text-xs text-default-500">
                                            {option.label}
                                        </p>
                                        <p className="text-xs font-medium text-default-600">
                                            {percentage}%
                                        </p>
                                    </div>
                                );
                            })}
                        </div>
                    </CardBody>
                </Card>
            )}
        </div>
    );
}

// Componente simplificado para usar en tablas o listas
export function QualityBadge({ quality }: { quality: QualityType }) {
    const option = qualityOptions.find((opt) => opt.value === quality);

    if (!option) return null;

    const Icon = option.icon;

    return (
        <Chip
            color={option.chipColor}
            size="sm"
            startContent={<Icon className="w-3 h-3" />}
            variant="flat"
        >
            {option.label}
        </Chip>
    );
}
