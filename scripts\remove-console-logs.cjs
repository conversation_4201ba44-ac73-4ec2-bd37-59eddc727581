#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuración
const config = {
  // Directorios a procesar
  directories: ['app', 'features', 'shared'],
  
  // Archivos a excluir (mantener console en estos)
  excludeFiles: [
    '**/error.tsx',
    '**/error.ts',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/debug*.ts',
    '**/debug*.tsx',
  ],
  
  // Patrones de console a remover
  consolePatterns: [
    /console\s*\.\s*log\s*\([^)]*\)\s*;?/g,
    /console\s*\.\s*error\s*\([^)]*\)\s*;?/g,
    /console\s*\.\s*warn\s*\([^)]*\)\s*;?/g,
    /console\s*\.\s*info\s*\([^)]*\)\s*;?/g,
    /console\s*\.\s*debug\s*\([^)]*\)\s*;?/g,
    /console\s*\.\s*trace\s*\([^)]*\)\s*;?/g,
  ],
  
  // Modo dry-run por defecto
  dryRun: process.argv.includes('--dry-run') || !process.argv.includes('--execute'),
};

// Función para procesar un archivo
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let changes = [];
  
  // Buscar y reemplazar cada patrón
  config.consolePatterns.forEach((pattern) => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach((match) => {
        // Verificar que no esté dentro de un comentario
        const lineStart = content.lastIndexOf('\n', content.indexOf(match));
        const lineEnd = content.indexOf('\n', content.indexOf(match));
        const line = content.substring(lineStart, lineEnd);
        
        if (!line.includes('//') || line.indexOf('//') > line.indexOf(match)) {
          changes.push({
            match,
            line: line.trim(),
          });
          
          if (!config.dryRun) {
            // Reemplazar con un comentario en lugar de eliminar completamente
            newContent = newContent.replace(match, `// REMOVED: ${match}`);
          }
        }
      });
    }
  });
  
  if (changes.length > 0) {
    console.log(`\n📄 ${filePath}`);
    changes.forEach((change) => {
      console.log(`   ❌ ${change.match.trim()}`);
    });
    
    if (!config.dryRun) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`   ✅ Archivo actualizado`);
    }
  }
  
  return changes.length;
}

// Función principal
function main() {
  console.log('🔍 Buscando console.log/error/warn en archivos de producción...\n');
  console.log(`Modo: ${config.dryRun ? 'DRY RUN (no se harán cambios)' : 'EJECUTAR'}`);
  console.log(`Directorios: ${config.directories.join(', ')}\n`);
  
  let totalFiles = 0;
  let totalChanges = 0;
  
  // Procesar cada directorio
  config.directories.forEach((dir) => {
    const pattern = path.join(dir, '**/*.{ts,tsx}');
    const files = glob.sync(pattern, {
      ignore: config.excludeFiles,
    });
    
    files.forEach((file) => {
      const changes = processFile(file);
      if (changes > 0) {
        totalFiles++;
        totalChanges += changes;
      }
    });
  });
  
  // Resumen
  console.log('\n📊 Resumen:');
  console.log(`   - Archivos con console.*: ${totalFiles}`);
  console.log(`   - Total de console.* encontrados: ${totalChanges}`);
  
  if (config.dryRun) {
    console.log('\n💡 Para ejecutar los cambios, usa: npm run remove-console-logs -- --execute');
  }
}

// Verificar si glob está instalado
try {
  require.resolve('glob');
} catch (e) {
  console.error('❌ El paquete "glob" no está instalado.');
  console.error('   Ejecuta: npm install --save-dev glob');
  process.exit(1);
}

// Ejecutar
main();