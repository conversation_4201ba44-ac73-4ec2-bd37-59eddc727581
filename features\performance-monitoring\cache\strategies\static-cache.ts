/**
 * Static Data Cache Strategy
 * For data that rarely changes (product images, static reports)
 */

import { CacheType } from "../cache-types";

import { BaseCacheStrategy } from "./base-strategy";

export class StaticCacheStrategy extends BaseCacheStrategy {
    constructor() {
        super(CacheType.STATIC);
    }

    /**
     * Set with extended TTL for static content
     */
    async setStatic<T>(
        resource: string,
        identifier: string,
        data: T,
        ttl: number = 86400 * 7, // 7 days default
    ): Promise<boolean> {
        return this.set({ resource, identifier }, data, ttl);
    }

    /**
     * Warm cache with multiple static entries
     */
    async warmCache<T>(
        entries: Array<{ resource: string; identifier: string; data: T }>,
    ): Promise<void> {
        const promises = entries.map((entry) =>
            this.setStatic(entry.resource, entry.identifier, entry.data),
        );

        await Promise.all(promises);
    }
}

// Singleton instance
export const staticCache = new StaticCacheStrategy();
