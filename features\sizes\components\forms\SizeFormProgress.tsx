import React from "react";
import { Progress } from "@heroui/react";

interface Step {
    label: string;
    completed: boolean;
}

interface SizeFormProgressProps {
    currentStep: number;
    totalSteps: number;
    steps?: Step[];
}

export function SizeFormProgress({
    currentStep,
    totalSteps,
    steps,
}: SizeFormProgressProps) {
    const progressValue = (currentStep / totalSteps) * 100;

    return (
        <div className="w-full space-y-2">
            <Progress
                aria-label="Progreso del formulario"
                className="max-w-md"
                color="primary"
                size="sm"
                value={progressValue}
            />
            {steps && steps.length > 0 && (
                <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 max-w-md">
                    {steps.map((step, index) => (
                        <span
                            key={index}
                            className={
                                step.completed
                                    ? "text-primary-600 font-medium"
                                    : ""
                            }
                        >
                            {step.label}
                        </span>
                    ))}
                </div>
            )}
        </div>
    );
}
