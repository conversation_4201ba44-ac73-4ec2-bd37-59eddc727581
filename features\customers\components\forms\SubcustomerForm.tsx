"use client";

import React, { useState, useEffect } from "react";
import { Input, Textarea } from "@heroui/react";
import { UserIcon } from "@heroicons/react/24/outline";

interface SubcustomerFormData {
    name: string;
    displayName?: string;
}

interface SubcustomerFormProps {
    initialData?: {
        id?: string;
        name: string;
        displayName?: string | null;
    };
    onSubmit: (data: SubcustomerFormData) => void;
    isSubmitting?: boolean;
    errors?: {
        name?: string;
        displayName?: string;
    };
}

export function SubcustomerForm({
    initialData,
    onSubmit,
    isSubmitting = false,
    errors = {},
}: SubcustomerFormProps) {
    const [formData, setFormData] = useState<SubcustomerFormData>({
        name: initialData?.name || "",
        displayName: initialData?.displayName || "",
    });

    useEffect(() => {
        if (initialData) {
            setFormData({
                name: initialData.name || "",
                displayName: initialData.displayName || "",
            });
        }
    }, [initialData]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form className="space-y-4" onSubmit={handleSubmit}>
            <Input
                isRequired
                description="Nombre único para identificar al subcliente"
                errorMessage={errors.name}
                isDisabled={isSubmitting}
                isInvalid={!!errors.name}
                label="Nombre del Subcliente"
                placeholder="Ej: Reebok"
                startContent={<UserIcon className="w-4 h-4 text-gray-400" />}
                value={formData.name}
                onValueChange={(value) =>
                    setFormData({ ...formData, name: value })
                }
            />

            <Textarea
                description="Cómo se mostrará en la interfaz. Si no se especifica, se generará automáticamente."
                errorMessage={errors.displayName}
                isDisabled={isSubmitting}
                isInvalid={!!errors.displayName}
                label="Nombre para Mostrar (Opcional)"
                maxRows={3}
                minRows={2}
                placeholder="Ej: Becktel - Reebok"
                value={formData.displayName}
                onValueChange={(value) =>
                    setFormData({ ...formData, displayName: value })
                }
            />
        </form>
    );
}
