"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    Input,
    <PERSON><PERSON>,
    <PERSON>dal,
    <PERSON>dalContent,
    <PERSON>dalHeader,
    <PERSON>dalBody,
    ModalFooter,
    useDisclosure,
    addToast,
    Spinner,
} from "@heroui/react";
import {
    ChevronLeftIcon,
    SwatchIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    Card,
    CardBody,
    CardHeader,
} from "@/shared/components/ui/hero-ui-client";
import { useColor, useUpdateColor } from "@/features/colors/hooks/useColor";
import { updateColorSchema } from "@/features/colors/schemas";

type ColorFormValues = z.infer<typeof updateColorSchema>;

// Colores predefinidos comunes
const PRESET_COLORS = [
    { name: "Negro", hex: "#000000" },
    { name: "<PERSON>", hex: "#FFFFFF" },
    { name: "Gris", hex: "#6B7280" },
    { name: "Rojo", hex: "#DC2626" },
    { name: "Rosa", hex: "#EC4899" },
    { name: "Naranja", hex: "#EA580C" },
    { name: "Amarillo", hex: "#EAB308" },
    { name: "Verde", hex: "#16A34A" },
    { name: "Azul", hex: "#2563EB" },
    { name: "Índigo", hex: "#4F46E5" },
    { name: "Púrpura", hex: "#9333EA" },
    { name: "Marrón", hex: "#92400E" },
];

export default function EditColorPage() {
    const router = useRouter();
    const params = useParams();
    const colorId = params.id as string;
    const { isOpen, onOpen, onClose } = useDisclosure();

    const [isSubmitting, setIsSubmitting] = useState(false);
    const { color, isLoading, isError } = useColor(colorId);
    const { updateColor } = useUpdateColor();

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isDirty, isValid },
        reset,
    } = useForm<ColorFormValues>({
        resolver: zodResolver(updateColorSchema),
        mode: "onChange",
    });

    // Cargar datos del color cuando estén disponibles
    useEffect(() => {
        if (color) {
            reset({
                name: color.name,
                hexCode: color.hexCode || "#000000",
            });
        }
    }, [color, reset]);

    const currentHexCode = watch("hexCode") || "#000000";

    const onSubmit = async (data: ColorFormValues) => {
        setIsSubmitting(true);

        try {
            // Get current name if not provided in form
            const updateData = {
                name: data.name || color?.name || "",
                hexCode: data.hexCode,
            };

            const result = await updateColor(colorId, updateData);

            if (!result) {
                throw new Error("No se pudo completar la operación");
            }

            if (!result.success) {
                throw new Error(result.error || "Error al actualizar el color");
            }

            addToast({
                title: "¡Color actualizado!",
                description: `${data.name || color?.name} se ha actualizado correctamente.`,
                color: "success",
            });

            router.push("/dashboard/colors");
            router.refresh();
        } catch (err) {
            addToast({
                title: "Error",
                description:
                    err instanceof Error
                        ? err.message
                        : "Error al actualizar el color",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (isDirty) {
            onOpen();
        } else {
            router.push("/dashboard/colors");
        }
    };

    const selectPresetColor = (preset: { name: string; hex: string }) => {
        setValue("hexCode", preset.hex, { shouldValidate: true });
    };

    if (isLoading) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Colores", href: "/dashboard/colors" },
                    { label: "Editar" },
                ]}
                subtitle="Actualiza la información del color"
                title="Editar Color"
            >
                <div className="flex justify-center items-center min-h-[400px]">
                    <Spinner color="primary" size="lg" />
                </div>
            </DashboardLayout>
        );
    }

    if (isError || !color) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Colores", href: "/dashboard/colors" },
                    { label: "Editar" },
                ]}
                subtitle="Actualiza la información del color"
                title="Editar Color"
            >
                <Card className="max-w-2xl mx-auto">
                    <CardBody className="text-center py-12">
                        <div className="mb-4">
                            <ExclamationTriangleIcon className="w-16 h-16 text-red-500 mx-auto" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">
                            {isError
                                ? "Error al cargar el color"
                                : "Color no encontrado"}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-6">
                            No se pudo encontrar el color solicitado.
                        </p>
                        <Button
                            color="primary"
                            startContent={
                                <ChevronLeftIcon className="w-4 h-4" />
                            }
                            onPress={() => router.push("/dashboard/colors")}
                        >
                            Volver a colores
                        </Button>
                    </CardBody>
                </Card>
            </DashboardLayout>
        );
    }

    return (
        <DashboardLayout
            actions={
                <Button
                    startContent={<ChevronLeftIcon className="w-4 h-4" />}
                    variant="light"
                    onPress={() => router.push("/dashboard/colors")}
                >
                    Volver
                </Button>
            }
            breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Colores", href: "/dashboard/colors" },
                { label: color.name },
                { label: "Editar" },
            ]}
            subtitle={`Editando: ${color.name}`}
            title="Editar Color"
        >
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
            >
                <Card className="shadow-xl">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                                <SwatchIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold">
                                    Editar Color
                                </h2>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Modifica los datos del color existente
                                </p>
                            </div>
                        </div>
                    </CardHeader>

                    <CardBody className="p-6">
                        <form
                            className="space-y-6"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            {/* Vista previa del color */}
                            <div className="flex justify-center">
                                <div className="text-center">
                                    <div
                                        className="w-32 h-32 rounded-2xl shadow-lg border-4 border-white dark:border-gray-700 mb-3"
                                        style={{
                                            backgroundColor: currentHexCode,
                                        }}
                                    />
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        {currentHexCode.toUpperCase()}
                                    </p>
                                </div>
                            </div>

                            {/* Campos del formulario */}
                            <div className="space-y-4">
                                <Input
                                    {...register("name")}
                                    classNames={{
                                        inputWrapper: "shadow-sm",
                                    }}
                                    errorMessage={errors.name?.message}
                                    isInvalid={!!errors.name}
                                    label="Nombre del Color"
                                    placeholder="Ej: Azul Cielo"
                                    size="lg"
                                    variant="bordered"
                                />

                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Código de Color
                                    </label>
                                    <div className="flex gap-3 items-center">
                                        <Input
                                            {...register("hexCode")}
                                            classNames={{
                                                inputWrapper: "shadow-sm",
                                                input: "font-mono",
                                            }}
                                            errorMessage={
                                                errors.hexCode?.message
                                            }
                                            isInvalid={!!errors.hexCode}
                                            placeholder="#000000"
                                            size="lg"
                                            variant="bordered"
                                        />
                                        <label className="relative cursor-pointer">
                                            <input
                                                className="sr-only"
                                                type="color"
                                                value={currentHexCode}
                                                onChange={(e) =>
                                                    setValue(
                                                        "hexCode",
                                                        e.target.value,
                                                        {
                                                            shouldValidate:
                                                                true,
                                                        },
                                                    )
                                                }
                                            />
                                            <div
                                                className="w-14 h-14 rounded-lg shadow-md border-2 border-gray-300 dark:border-gray-600 hover:scale-105 transition-transform"
                                                style={{
                                                    backgroundColor:
                                                        currentHexCode,
                                                }}
                                            />
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Paleta de colores predefinidos */}
                            <div className="space-y-2">
                                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Colores rápidos
                                </p>
                                <div className="grid grid-cols-6 gap-2">
                                    {PRESET_COLORS.map((preset) => (
                                        <button
                                            key={preset.hex}
                                            className="group relative"
                                            type="button"
                                            onClick={() =>
                                                selectPresetColor(preset)
                                            }
                                        >
                                            <div
                                                className="w-full aspect-square rounded-lg shadow-sm border-2 border-gray-200 dark:border-gray-700 hover:scale-105 transition-transform"
                                                style={{
                                                    backgroundColor: preset.hex,
                                                }}
                                            />
                                            <span className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                                <span className="bg-black/75 text-white text-xs px-2 py-1 rounded">
                                                    {preset.name}
                                                </span>
                                            </span>
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Mensaje de sin cambios */}
                            {!isDirty && (
                                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                                    <p className="text-sm text-yellow-800 dark:text-yellow-200">
                                        No se han realizado cambios en el color.
                                    </p>
                                </div>
                            )}

                            {/* Botones de acción */}
                            <div className="flex justify-end gap-3 pt-4">
                                <Button variant="flat" onPress={handleCancel}>
                                    Cancelar
                                </Button>

                                <Button
                                    color="primary"
                                    isDisabled={
                                        !isDirty || !isValid || isSubmitting
                                    }
                                    isLoading={isSubmitting}
                                    startContent={
                                        !isSubmitting && (
                                            <CheckCircleIcon className="w-5 h-5" />
                                        )
                                    }
                                    type="submit"
                                >
                                    {isSubmitting
                                        ? "Actualizando..."
                                        : "Actualizar Color"}
                                </Button>
                            </div>
                        </form>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Modal de confirmación */}
            <Modal isOpen={isOpen} onClose={onClose}>
                <ModalContent>
                    <ModalHeader>Cambios sin guardar</ModalHeader>
                    <ModalBody>
                        ¿Estás seguro de que deseas salir? Los cambios se
                        perderán.
                    </ModalBody>
                    <ModalFooter>
                        <Button variant="flat" onPress={onClose}>
                            Continuar editando
                        </Button>
                        <Button
                            color="danger"
                            onPress={() => {
                                onClose();
                                router.push("/dashboard/colors");
                            }}
                        >
                            Salir sin guardar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </DashboardLayout>
    );
}
