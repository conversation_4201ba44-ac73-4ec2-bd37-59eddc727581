import type { RemissionItemData } from "../types";

/**
 * Pipeline funcional para extraer items de remisión desde asignaciones
 */
export function extractRemissionItems(
    assignments: Array<{
        quantity: number;
        garmentSize: {
            size: { code: string };
            garment: {
                model: { code: string };
                color: { name: string };
            };
        };
    }>,
): RemissionItemData[] {
    return assignments.map((assignment) => ({
        modelCode: assignment.garmentSize.garment.model.code,
        colorName: assignment.garmentSize.garment.color.name,
        sizeCode: assignment.garmentSize.size.code,
        quantity: assignment.quantity,
    }));
}

/**
 * Agrupa items por modelo, color y talla sumando cantidades
 */
export function groupRemissionItems(
    items: RemissionItemData[],
): RemissionItemData[] {
    const grouped = items.reduce(
        (acc, item) => {
            const key = `${item.modelCode}-${item.colorName}-${item.sizeCode}`;

            if (acc[key]) {
                acc[key].quantity += item.quantity;
            } else {
                acc[key] = { ...item };
            }

            return acc;
        },
        {} as Record<string, RemissionItemData>,
    );

    return Object.values(grouped);
}

/**
 * Pipeline completo: extract → group → enrich
 */
export function processAssignmentsForRemission(
    assignments: Array<{
        quantity: number;
        garmentSize: {
            size: { code: string };
            garment: {
                model: { code: string };
                color: { name: string };
            };
        };
    }>,
): RemissionItemData[] {
    // Extract items from assignments
    const items = extractRemissionItems(assignments);

    // Group by model, color, and size
    const groupedItems = groupRemissionItems(items);

    return groupedItems;
}
