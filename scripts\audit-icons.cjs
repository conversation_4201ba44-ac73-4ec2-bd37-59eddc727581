// Script para auditar iconos con padding potencialmente problemático
const fs = require('fs');
const path = require('path');

// Patrones que indican posible padding excesivo alrededor de iconos
const paddingPatterns = [
  /className="[^"]*p-[2-9][^"]*".*?Icon/g,  // p-2 hasta p-9 con Icon
  /className="[^"]*p-\d\d[^"]*".*?Icon/g,   // p-10 o más con Icon
  /className="[^"]*padding[^"]*".*?Icon/g,  // padding custom con Icon
  /rounded-full.*?p-.*?Icon/g,              // rounded-full con padding e Icon
  /bg-.*?p-.*?Icon/g,                       // background con padding e Icon
];

// Patrones de iconos de Heroicons
const iconPatterns = [
  /Icon\s+className="/g,
  /<\w+Icon\s+/g,
];

const results = [];
let totalFiles = 0;
let filesWithIssues = 0;

function scanFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  paddingPatterns.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        // Contar línea
        const lines = content.substring(0, content.indexOf(match)).split('\n');
        const lineNumber = lines.length;
        
        issues.push({
          pattern: pattern.toString(),
          match: match.substring(0, 100) + '...',
          line: lineNumber
        });
      });
    }
  });
  
  if (issues.length > 0) {
    filesWithIssues++;
    results.push({
      file: filePath,
      issues: issues
    });
  }
}

function scanDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // Saltar directorios de dependencias y build
      if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(file)) {
        scanDirectory(fullPath);
      }
    } else if (file.match(/\.(tsx?|jsx?)$/)) {
      totalFiles++;
      scanFile(fullPath);
    }
  });
}

// Directorios a escanear
const dirsToScan = [
  'app',
  'features',
  'shared/components'
];

console.log('🔍 Iniciando auditoría de iconos con padding...\n');

dirsToScan.forEach(dir => {
  if (fs.existsSync(dir)) {
    scanDirectory(dir);
  }
});

// Generar reporte
console.log(`📊 Resumen de Auditoría
━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Total de archivos escaneados: ${totalFiles}
Archivos con padding problemático: ${filesWithIssues}
Total de instancias encontradas: ${results.reduce((acc, r) => acc + r.issues.length, 0)}
`);

if (results.length > 0) {
  console.log('\n📋 Detalle de archivos con issues:\n');
  
  results.forEach(result => {
    console.log(`📄 ${result.file}`);
    result.issues.forEach(issue => {
      console.log(`   Línea ${issue.line}: ${issue.match}`);
    });
    console.log('');
  });
  
  // Guardar reporte en archivo
  const report = {
    date: new Date().toISOString(),
    summary: {
      totalFiles,
      filesWithIssues,
      totalIssues: results.reduce((acc, r) => acc + r.issues.length, 0)
    },
    details: results
  };
  
  fs.writeFileSync(
    'docs/icon-audit-report.json',
    JSON.stringify(report, null, 2)
  );
  
  console.log('📄 Reporte detallado guardado en: docs/icon-audit-report.json');
} else {
  console.log('✅ ¡No se encontraron iconos con padding problemático!');
}
