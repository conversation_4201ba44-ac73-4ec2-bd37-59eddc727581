"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>dalContent, ModalBody } from "@heroui/modal";

import { RemissionPreview as RemissionPreviewType } from "@/types/remission";

import { RemissionPreview } from "../RemissionPreview";

import { Toolbar } from "./components/Toolbar";
import { StatusBar } from "./components/StatusBar";
import { usePreviewState } from "./hooks/usePreviewState";

export interface RemissionPreviewModalProps {
    isOpen: boolean;
    onClose: () => void;
    remissionData: RemissionPreviewType;
    onGeneratePDF: (orientation: "portrait" | "landscape") => Promise<void>;
    onSaveChanges?: (changes: Partial<RemissionPreviewType>) => Promise<void>;
}

export const RemissionPreviewModal: React.FC<RemissionPreviewModalProps> = ({
    isOpen,
    onClose,
    remissionData,
    onGeneratePDF,
    onSaveChanges,
}) => {
    const {
        editMode,
        setEditMode,
        orientation,
        setOrientation,
        zoom,
        setZoom,
        changes,
        handleEdit,
        hasChanges,
        resetChanges,
    } = usePreviewState(remissionData);

    const handleSave = async () => {
        if (onSaveChanges && hasChanges) {
            await onSaveChanges(changes);
            setEditMode(false);
            resetChanges();
        }
    };

    const handleClose = () => {
        if (hasChanges && editMode) {
            // TODO: Show confirmation dialog
            const confirm = window.confirm("¿Descartar cambios sin guardar?");

            if (!confirm) return;
        }
        resetChanges();
        setEditMode(false);
        onClose();
    };

    const handleGeneratePDF = async () => {
        await onGeneratePDF(orientation);
    };

    return (
        <Modal
            hideCloseButton
            classNames={{
                wrapper: "!p-0",
                base: "!m-0 !max-w-full h-screen",
                body: "p-0",
            }}
            isOpen={isOpen}
            scrollBehavior="inside"
            size="full"
            onClose={handleClose}
        >
            <ModalContent className="h-screen flex flex-col">
                <Toolbar
                    editMode={editMode}
                    folio={remissionData.folio}
                    orientation={orientation}
                    onBack={handleClose}
                    onChangeOrientation={setOrientation}
                    onGeneratePDF={handleGeneratePDF}
                    onToggleEdit={
                        editMode ? handleSave : () => setEditMode(true)
                    }
                />

                <ModalBody className="flex-1 overflow-auto bg-gray-100">
                    <div className="py-8 px-4">
                        <RemissionPreview
                            data={remissionData}
                            editMode={editMode}
                            orientation={orientation}
                            zoom={zoom}
                            onEdit={handleEdit}
                        />
                    </div>
                </ModalBody>

                <StatusBar
                    changesCount={Object.keys(changes).length}
                    hasChanges={hasChanges}
                    orientation={orientation}
                    zoom={zoom}
                    onZoomChange={setZoom}
                />
            </ModalContent>
        </Modal>
    );
};
