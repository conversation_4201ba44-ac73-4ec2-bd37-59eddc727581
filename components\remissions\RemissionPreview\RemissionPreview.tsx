"use client";

import React from "react";

import { RemissionPreview as RemissionPreviewType } from "@/types/remission";

import styles from "./styles.module.css";
import { Header } from "./components/Header";
import { ContractorSection } from "./components/ContractorSection";
import { ItemsTable } from "./components/ItemsTable";
import { NotesSection } from "./components/NotesSection";
import { Footer } from "./components/Footer";

export interface RemissionPreviewProps {
    data: RemissionPreviewType;
    orientation?: "portrait" | "landscape";
    editMode?: boolean;
    zoom?: number;
    onEdit?: (field: string, value: any) => void;
    className?: string;
}

export const RemissionPreview: React.FC<RemissionPreviewProps> = ({
    data,
    orientation = "portrait",
    editMode = false,
    zoom = 100,
    onEdit,
    className,
}) => {
    const handleFieldEdit = (field: string, value: any) => {
        if (editMode && onEdit) {
            onEdit(field, value);
        }
    };

    const containerClasses = [
        styles["remission-preview"],
        styles[`remission-preview--${orientation}`],
        editMode && styles["remission-preview--editable"],
        className,
    ]
        .filter(Boolean)
        .join(" ");

    const containerStyle = {
        transform: `scale(${zoom / 100})`,
        transformOrigin: "top center",
    };

    return (
        <div className={containerClasses} style={containerStyle}>
            <Header data={data} editMode={editMode} onEdit={handleFieldEdit} />

            <ContractorSection
                contractor={data.contractor as any}
                editMode={editMode}
                onEdit={handleFieldEdit}
            />

            <ItemsTable
                editMode={editMode}
                items={data.items as any}
                onEdit={handleFieldEdit}
            />

            <NotesSection
                editMode={editMode}
                externalNotes={(data as any).externalNotes}
                internalNotes={(data as any).internalNotes}
                onEdit={handleFieldEdit}
            />

            <Footer data={data} editMode={editMode} onEdit={handleFieldEdit} />
        </div>
    );
};
