"use server";

import { revalidatePath } from "next/cache";

/**
 * Revalida las rutas relacionadas con asignaciones
 */
export async function revalidateAssignmentPaths(specificOrderId?: string) {
    // Array de rutas a revalidar
    const paths = ["/dashboard/assigments", "/dashboard/assigments/new"];

    // Añadir rutas condicionales
    if (specificOrderId) {
        paths.push(`/dashboard/orders/${specificOrderId}`);
    } else {
        paths.push("/dashboard/orders");
    }

    paths.push("/dashboard/contractors");

    // Revalidar todas las rutas en paralelo
    try {
        await Promise.all(paths.map((path) => revalidatePath(path)));
        // REMOVED: console.log(`[Server] Rutas revalidadas: ${paths.join(", ")}`);

        return { success: true };
    } catch (error) {
        // REMOVED: console.error("Error al revalidar rutas:", error);

        return { success: false, error };
    }
}
