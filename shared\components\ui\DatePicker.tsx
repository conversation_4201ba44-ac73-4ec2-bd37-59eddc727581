"use client";

import React, { useState } from "react";
import {
    CalendarIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
} from "@heroicons/react/24/outline";

import {
    Input,
    <PERSON>ton,
    Modal,
    ModalContent,
    <PERSON>dal<PERSON>eader,
    ModalBody,
    ModalFooter,
} from "@/shared/components/ui/hero-ui-client";

interface DatePickerProps {
    label: string;
    placeholder?: string;
    value: string;
    onChange: (date: string) => void;
    startContent?: React.ReactNode;
    errorMessage?: string;
    isRequired?: boolean;
    isDisabled?: boolean;
    minDate?: string;
    maxDate?: string;
}

const MONTHS = [
    "Enero",
    "Febrero",
    "Mar<PERSON>",
    "Abril",
    "Mayo",
    "Jun<PERSON>",
    "Julio",
    "Agosto",
    "Septiembre",
    "Octubre",
    "Noviembre",
    "Diciembre",
];

const WEEKDAYS = ["Dom", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>áb"];

export function DatePicker({
    label,
    placeholder = "Selecciona fecha",
    value,
    onChange,
    startContent = <CalendarIcon className="w-4 h-4 text-gray-400" />,
    errorMessage,
    isRequired = false,
    isDisabled = false,
    minDate,
    maxDate,
}: DatePickerProps) {
    const [isOpen, setIsOpen] = useState(false);

    // Parse current date or use today
    const currentDate = value ? new Date(value + "T00:00:00") : new Date();
    const [viewMonth, setViewMonth] = useState(currentDate.getMonth());
    const [viewYear, setViewYear] = useState(currentDate.getFullYear());

    // Format date for display
    const formatDate = (dateStr: string) => {
        if (!dateStr) return "";
        const date = new Date(dateStr + "T00:00:00");

        return date.toLocaleDateString("es-ES", {
            year: "numeric",
            month: "long",
            day: "numeric",
        });
    };

    // Get days in month
    const getDaysInMonth = (year: number, month: number) => {
        return new Date(year, month + 1, 0).getDate();
    };

    // Get first day of month (0 = Sunday)
    const getFirstDayOfMonth = (year: number, month: number) => {
        return new Date(year, month, 1).getDay();
    };

    // Generate calendar days
    const generateCalendarDays = () => {
        const daysInMonth = getDaysInMonth(viewYear, viewMonth);
        const firstDay = getFirstDayOfMonth(viewYear, viewMonth);
        const days: (number | null)[] = [];

        // Add empty cells for days before month starts
        for (let i = 0; i < firstDay; i++) {
            days.push(null);
        }

        // Add days of month
        for (let i = 1; i <= daysInMonth; i++) {
            days.push(i);
        }

        return days;
    };

    // Handle date selection
    const handleDateSelect = (day: number) => {
        const year = viewYear;
        const month = (viewMonth + 1).toString().padStart(2, "0");
        const dayStr = day.toString().padStart(2, "0");
        const dateStr = `${year}-${month}-${dayStr}`;

        onChange(dateStr);
        setIsOpen(false);
    };

    // Navigate months
    const navigateMonth = (direction: "prev" | "next") => {
        if (direction === "prev") {
            if (viewMonth === 0) {
                setViewMonth(11);
                setViewYear(viewYear - 1);
            } else {
                setViewMonth(viewMonth - 1);
            }
        } else {
            if (viewMonth === 11) {
                setViewMonth(0);
                setViewYear(viewYear + 1);
            } else {
                setViewMonth(viewMonth + 1);
            }
        }
    };

    // Check if date is selected
    const isDateSelected = (day: number) => {
        if (!value || !day) return false;
        const date = new Date(value + "T00:00:00");

        return (
            date.getDate() === day &&
            date.getMonth() === viewMonth &&
            date.getFullYear() === viewYear
        );
    };

    // Check if date is disabled
    const isDateDisabled = (day: number) => {
        if (!day) return true;

        const dateStr = `${viewYear}-${(viewMonth + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
        const date = new Date(dateStr + "T00:00:00");

        if (minDate) {
            const min = new Date(minDate + "T00:00:00");

            if (date < min) return true;
        }

        if (maxDate) {
            const max = new Date(maxDate + "T00:00:00");

            if (date > max) return true;
        }

        return false;
    };

    return (
        <>
            <Input
                readOnly
                classNames={{
                    input: "cursor-pointer",
                    inputWrapper: "hover:bg-gray-50 dark:hover:bg-gray-800",
                }}
                endContent={
                    <Button
                        isIconOnly
                        isDisabled={isDisabled}
                        size="sm"
                        variant="light"
                        onPress={() => !isDisabled && setIsOpen(true)}
                    >
                        <CalendarIcon className="w-4 h-4" />
                    </Button>
                }
                errorMessage={errorMessage}
                isDisabled={isDisabled}
                isRequired={isRequired}
                label={label}
                placeholder={placeholder}
                startContent={startContent}
                value={formatDate(value)}
                onClick={() => !isDisabled && setIsOpen(true)}
            />

            <Modal
                isOpen={isOpen}
                placement="center"
                size="sm"
                onClose={() => setIsOpen(false)}
            >
                <ModalContent>
                    <ModalHeader>{label}</ModalHeader>
                    <ModalBody>
                        <div className="w-full">
                            {/* Month/Year Navigation */}
                            <div className="flex items-center justify-between mb-4">
                                <Button
                                    isIconOnly
                                    size="sm"
                                    variant="light"
                                    onPress={() => navigateMonth("prev")}
                                >
                                    <ChevronLeftIcon className="w-4 h-4" />
                                </Button>

                                <div className="text-lg font-semibold">
                                    {MONTHS[viewMonth]} {viewYear}
                                </div>

                                <Button
                                    isIconOnly
                                    size="sm"
                                    variant="light"
                                    onPress={() => navigateMonth("next")}
                                >
                                    <ChevronRightIcon className="w-4 h-4" />
                                </Button>
                            </div>

                            {/* Weekday Headers */}
                            <div className="grid grid-cols-7 gap-1 mb-2">
                                {WEEKDAYS.map((day) => (
                                    <div
                                        key={day}
                                        className="text-center text-xs font-medium text-gray-500 p-2"
                                    >
                                        {day}
                                    </div>
                                ))}
                            </div>

                            {/* Calendar Days */}
                            <div className="grid grid-cols-7 gap-1">
                                {generateCalendarDays().map((day, index) => (
                                    <div key={index} className="aspect-square">
                                        {day ? (
                                            <button
                                                className={`
                          w-full h-full rounded-lg text-sm font-medium
                          transition-colors duration-200
                          ${
                              isDateSelected(day)
                                  ? "bg-primary text-white"
                                  : isDateDisabled(day)
                                    ? "text-gray-300 cursor-not-allowed"
                                    : "hover:bg-gray-100 dark:hover:bg-gray-800"
                          }
                        `}
                                                disabled={isDateDisabled(day)}
                                                type="button"
                                                onClick={() =>
                                                    handleDateSelect(day)
                                                }
                                            >
                                                {day}
                                            </button>
                                        ) : (
                                            <div />
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </ModalBody>
                    <ModalFooter>
                        <Button
                            variant="light"
                            onPress={() => setIsOpen(false)}
                        >
                            Cancelar
                        </Button>
                        <Button
                            color="primary"
                            onPress={() => {
                                if (!value) {
                                    const today = new Date();
                                    const year = today.getFullYear();
                                    const month = (today.getMonth() + 1)
                                        .toString()
                                        .padStart(2, "0");
                                    const day = today
                                        .getDate()
                                        .toString()
                                        .padStart(2, "0");

                                    onChange(`${year}-${month}-${day}`);
                                }
                                setIsOpen(false);
                            }}
                        >
                            {value ? "Cerrar" : "Hoy"}
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </>
    );
}
