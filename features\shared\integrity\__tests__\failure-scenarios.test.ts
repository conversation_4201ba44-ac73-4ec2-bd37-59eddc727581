import { IntegrityManager } from "../IntegrityManager";

import {
    setupIntegrityManager,
    createVersionMismatchError,
    createTimeoutError,
    createDatabaseError,
    createMockOperationLog,
    mockTransactionBehavior,
} from "./test-utils";

describe("IntegrityManager - Failure Scenarios", () => {
    let mockPrisma: any;
    let integrityManager: IntegrityManager;

    beforeEach(() => {
        const setup = setupIntegrityManager();

        mockPrisma = setup.prisma;
        integrityManager = setup.integrityManager;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("Version Conflicts", () => {
        it("should handle version mismatch during assignment update", async () => {
            // Arrange
            mockPrisma.assignment.findUnique.mockResolvedValue({
                id: "assign-123",
                version: 2, // Current version is 2
            });

            // Act & Assert
            await expect(
                integrityManager.checkVersion(
                    { id: "assign-123", version: 1 }, // Trying to update with version 1
                    mockPrisma.assignment,
                ),
            ).rejects.toThrow("Version mismatch for assignment assign-123");
        });

        it("should retry operation on version conflict with updated version", async () => {
            // Arrange
            let callCount = 0;

            mockPrisma.assignment.findUnique.mockImplementation(() => {
                callCount++;

                return Promise.resolve({
                    id: "assign-123",
                    version: callCount === 1 ? 2 : 3, // Version changes between retries
                });
            });

            mockPrisma.assignment.update.mockImplementation(() => {
                if (callCount === 1) {
                    throw createVersionMismatchError();
                }

                return Promise.resolve({ id: "assign-123", version: 3 });
            });

            // Act
            const mockOperation = async () => {
                await integrityManager.checkVersion(
                    { id: "assign-123", version: 2 },
                    mockPrisma.assignment,
                );

                return mockPrisma.assignment.update({
                    where: { id: "assign-123" },
                    data: { quantity: 20 },
                });
            };

            const result = await mockOperation();

            // Assert
            expect(result).toEqual({ id: "assign-123", version: 3 });
            expect(mockPrisma.assignment.findUnique).toHaveBeenCalledTimes(2);
        });
    });

    describe("Transaction Timeouts", () => {
        it("should handle transaction timeout and trigger compensation", async () => {
            // Arrange
            const operationLog = createMockOperationLog({ id: "op-123" });

            mockPrisma.operationLog.create.mockResolvedValue(operationLog);
            mockPrisma.operationLog.update.mockResolvedValue(operationLog);

            const mockOperation = jest
                .fn()
                .mockRejectedValue(createTimeoutError());
            const mockCompensation = jest.fn().mockResolvedValue(undefined);

            // Act & Assert
            await expect(
                integrityManager.executeWithIntegrity(
                    {
                        type: "ASSIGNMENT_BATCH",
                        userId: "user-123",
                        metadata: { assignmentIds: ["assign-1"] },
                    },
                    mockOperation,
                    mockCompensation,
                ),
            ).rejects.toThrow("Transaction timed out");

            // Verify compensation was called
            expect(mockCompensation).toHaveBeenCalledWith(
                operationLog,
                mockPrisma,
            );

            // Verify operation log was marked as failed
            expect(mockPrisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "op-123" },
                data: {
                    status: "FAILED",
                    failedAt: expect.any(Date),
                    errorMessage: "Transaction timed out after 5000ms",
                },
            });
        });

        it("should handle timeout during compensation gracefully", async () => {
            // Arrange
            const operationLog = createMockOperationLog({ id: "op-123" });

            mockPrisma.operationLog.create.mockResolvedValue(operationLog);
            mockPrisma.operationLog.update.mockResolvedValue(operationLog);

            const mockOperation = jest
                .fn()
                .mockRejectedValue(new Error("Initial failure"));
            const mockCompensation = jest
                .fn()
                .mockRejectedValue(createTimeoutError());

            // Act & Assert
            await expect(
                integrityManager.executeWithIntegrity(
                    {
                        type: "ASSIGNMENT_BATCH",
                        userId: "user-123",
                        metadata: {},
                    },
                    mockOperation,
                    mockCompensation,
                ),
            ).rejects.toThrow("Initial failure");

            // Compensation should have been attempted
            expect(mockCompensation).toHaveBeenCalled();

            // Operation should still be marked as failed
            expect(mockPrisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "op-123" },
                data: {
                    status: "FAILED",
                    failedAt: expect.any(Date),
                    errorMessage: "Initial failure",
                },
            });
        });
    });

    describe("Database Connection Failures", () => {
        it("should handle database connection loss during operation", async () => {
            // Arrange
            const operationLog = createMockOperationLog({ id: "op-123" });

            mockPrisma.operationLog.create.mockResolvedValue(operationLog);

            const mockOperation = jest
                .fn()
                .mockRejectedValue(createDatabaseError());
            const mockCompensation = jest.fn();

            // Act & Assert
            await expect(
                integrityManager.executeWithIntegrity(
                    {
                        type: "ASSIGNMENT_BATCH",
                        userId: "user-123",
                        metadata: {},
                    },
                    mockOperation,
                    mockCompensation,
                ),
            ).rejects.toThrow("Database connection lost");

            // Compensation should not be called for connection errors
            expect(mockCompensation).not.toHaveBeenCalled();
        });

        it("should handle operation log creation failure", async () => {
            // Arrange
            mockPrisma.operationLog.create.mockRejectedValue(
                createDatabaseError(),
            );

            const mockOperation = jest.fn();
            const mockCompensation = jest.fn();

            // Act & Assert
            await expect(
                integrityManager.executeWithIntegrity(
                    {
                        type: "ASSIGNMENT_BATCH",
                        userId: "user-123",
                        metadata: {},
                    },
                    mockOperation,
                    mockCompensation,
                ),
            ).rejects.toThrow("Database connection lost");

            // Operation should not be executed if log creation fails
            expect(mockOperation).not.toHaveBeenCalled();
            expect(mockCompensation).not.toHaveBeenCalled();
        });
    });

    describe("Partial Failures", () => {
        it("should handle partial assignment creation failure", async () => {
            // Arrange
            const operationLog = createMockOperationLog({
                id: "op-123",
                metadata: {
                    assignmentIds: ["assign-1", "assign-2"],
                    successfulIds: ["assign-1"],
                    failedIds: ["assign-2"],
                },
            });

            mockPrisma.operationLog.update.mockResolvedValue(operationLog);
            mockPrisma.assignment.updateMany.mockResolvedValue({ count: 1 });
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            await integrityManager.compensationStrategies.compensateAssignmentBatch(
                operationLog,
                mockPrisma,
            );

            // Assert - Only successful assignments should be compensated
            expect(mockPrisma.assignment.updateMany).toHaveBeenCalledWith({
                where: {
                    id: { in: ["assign-1", "assign-2"] }, // Attempts all, but only 1 succeeds
                },
                data: {
                    status: "CANCELLED",
                    cancelledAt: expect.any(Date),
                    cancelReason: expect.stringContaining("Compensation"),
                },
            });
        });
    });

    describe("Stuck Operations Recovery", () => {
        it("should find stuck operations older than threshold", async () => {
            // Arrange
            const stuckOperations = [
                createMockOperationLog({
                    id: "stuck-1",
                    status: "IN_PROGRESS",
                    startedAt: new Date(Date.now() - 20 * 60 * 1000), // 20 minutes ago
                }),
                createMockOperationLog({
                    id: "stuck-2",
                    status: "IN_PROGRESS",
                    startedAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
                }),
            ];

            mockPrisma.operationLog.findMany.mockResolvedValue(stuckOperations);

            // Act
            const result = await integrityManager.findStuckOperations(15); // 15 minutes threshold

            // Assert
            expect(result).toEqual(stuckOperations);
            expect(mockPrisma.operationLog.findMany).toHaveBeenCalledWith({
                where: {
                    status: "IN_PROGRESS",
                    startedAt: {
                        lt: expect.any(Date),
                    },
                },
                orderBy: {
                    startedAt: "asc",
                },
            });
        });
    });
});
