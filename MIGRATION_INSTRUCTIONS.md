# Migration Instructions: Make Packing orderId Nullable

## Problem
The database constraint requires `orderId` to be NOT NULL, but the business logic needs to create packings that consolidate multiple orders (with orderId = null).

## Solution
Apply a database migration to make the `orderId` column nullable in the `Packing` table.

## Steps to Apply

### Option 1: Using Prisma Migrate (Recommended)
```bash
# This will apply the migration file already created
npx prisma migrate deploy
```

### Option 2: Using Direct Database Push
```bash
# This will sync the schema.prisma with the database
npx prisma db push
```

### Option 3: Manual SQL Execution
If the above commands fail, run this SQL directly on your database:
```sql
ALTER TABLE "Packing" ALTER COLUMN "orderId" DROP NOT NULL;
```

## Verification

After applying the migration, test the functionality:

```bash
# Run the test script
node scripts/test-multi-order-packing.js
```

## What This Enables

1. **Multi-Order Packings**: Create packings that consolidate products from multiple orders
2. **Better Logistics**: Ship products from different orders in a single package
3. **Cost Optimization**: Reduce shipping costs by combining small orders
4. **Inventory Efficiency**: Better management of products across orders

## Technical Details

- The Prisma schema already defines `orderId` as optional (`String?`)
- The business logic sets `orderId` to null when multiple orders are selected
- The `PackingSummary` table tracks individual orders within a multi-order packing
- PDF generation and UI components already handle null `orderId` gracefully