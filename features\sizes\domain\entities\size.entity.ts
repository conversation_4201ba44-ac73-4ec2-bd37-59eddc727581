/**
 * Size Domain Entity
 *
 * Represents a size in the domain model with business logic and validation.
 * This is a pure domain object with no dependencies on infrastructure.
 */

import { SizeName } from "../value-objects/size-name.value-object";
import { SizeOrder } from "../value-objects/size-order.value-object";

export interface SizeProps {
    id?: string;
    name: string;
    displayName: string;
    order: number;
    isActive: boolean;
    category?: "numeric" | "alpha" | "custom";
    measurements?: {
        chest?: number;
        waist?: number;
        hip?: number;
        length?: number;
    };
    equivalents?: {
        us?: string;
        eu?: string;
        uk?: string;
        mx?: string;
    };
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date | null;
}

export class Size {
    private readonly _id?: string;
    private _name: SizeName;
    private _displayName: string;
    private _order: SizeOrder;
    private _isActive: boolean;
    private _category: "numeric" | "alpha" | "custom";
    private _measurements?: {
        chest?: number;
        waist?: number;
        hip?: number;
        length?: number;
    };
    private _equivalents?: {
        us?: string;
        eu?: string;
        uk?: string;
        mx?: string;
    };
    private readonly _createdAt: Date;
    private _updatedAt: Date;
    private _deletedAt?: Date | null;

    constructor(props: SizeProps) {
        this._id = props.id;
        this._name = new SizeName(props.name);
        this._displayName = props.displayName;
        this._order = new SizeOrder(props.order);
        this._isActive = props.isActive;
        this._category = props.category || this.inferCategory(props.name);
        this._measurements = props.measurements;
        this._equivalents = props.equivalents;
        this._createdAt = props.createdAt || new Date();
        this._updatedAt = props.updatedAt || new Date();
        this._deletedAt = props.deletedAt || null;
    }

    // Getters
    get id(): string | undefined {
        return this._id;
    }

    get name(): string {
        return this._name.value;
    }

    get displayName(): string {
        return this._displayName;
    }

    get order(): number {
        return this._order.value;
    }

    get isActive(): boolean {
        return this._isActive;
    }

    get category(): "numeric" | "alpha" | "custom" {
        return this._category;
    }

    get measurements() {
        return this._measurements;
    }

    get equivalents() {
        return this._equivalents;
    }

    get createdAt(): Date {
        return this._createdAt;
    }

    get updatedAt(): Date {
        return this._updatedAt;
    }

    get deletedAt(): Date | null | undefined {
        return this._deletedAt;
    }

    // Business Logic Methods

    /**
     * Activate the size
     */
    activate(): void {
        if (this._deletedAt) {
            throw new Error("Cannot activate a deleted size");
        }
        this._isActive = true;
        this.updateTimestamp();
    }

    /**
     * Deactivate the size
     */
    deactivate(): void {
        this._isActive = false;
        this.updateTimestamp();
    }

    /**
     * Soft delete the size
     */
    delete(): void {
        this._deletedAt = new Date();
        this._isActive = false;
        this.updateTimestamp();
    }

    /**
     * Restore a soft-deleted size
     */
    restore(): void {
        this._deletedAt = null;
        this.updateTimestamp();
    }

    /**
     * Update size properties
     */
    update(props: Partial<SizeProps>): void {
        if (this._deletedAt) {
            throw new Error("Cannot update a deleted size");
        }

        if (props.name !== undefined) {
            this._name = new SizeName(props.name);
        }

        if (props.displayName !== undefined) {
            this._displayName = props.displayName;
        }

        if (props.order !== undefined) {
            this._order = new SizeOrder(props.order);
        }

        if (props.isActive !== undefined) {
            this._isActive = props.isActive;
        }

        if (props.category !== undefined) {
            this._category = props.category;
        }

        if (props.measurements !== undefined) {
            this._measurements = props.measurements;
        }

        if (props.equivalents !== undefined) {
            this._equivalents = props.equivalents;
        }

        this.updateTimestamp();
    }

    /**
     * Check if this size comes before another size in ordering
     */
    comesBefore(other: Size): boolean {
        return this._order.isLessThan(other._order);
    }

    /**
     * Check if this size comes after another size in ordering
     */
    comesAfter(other: Size): boolean {
        return this._order.isGreaterThan(other._order);
    }

    /**
     * Check if this size is equivalent to another size
     */
    isEquivalentTo(other: Size): boolean {
        return this._name.equals(other._name);
    }

    /**
     * Validate the size entity
     */
    validate(): string[] {
        const errors: string[] = [];

        if (!this._name) {
            errors.push("Size name is required");
        }

        if (!this._displayName || this._displayName.trim() === "") {
            errors.push("Display name is required");
        }

        if (this._measurements) {
            const measurements = this._measurements;

            if (measurements.chest !== undefined && measurements.chest < 0) {
                errors.push("Chest measurement must be positive");
            }
            if (measurements.waist !== undefined && measurements.waist < 0) {
                errors.push("Waist measurement must be positive");
            }
            if (measurements.hip !== undefined && measurements.hip < 0) {
                errors.push("Hip measurement must be positive");
            }
            if (measurements.length !== undefined && measurements.length < 0) {
                errors.push("Length measurement must be positive");
            }
        }

        return errors;
    }

    /**
     * Check if the size is valid
     */
    isValid(): boolean {
        return this.validate().length === 0;
    }

    /**
     * Convert to plain object
     */
    toObject(): SizeProps {
        return {
            id: this._id,
            name: this._name.value,
            displayName: this._displayName,
            order: this._order.value,
            isActive: this._isActive,
            category: this._category,
            measurements: this._measurements,
            equivalents: this._equivalents,
            createdAt: this._createdAt,
            updatedAt: this._updatedAt,
            deletedAt: this._deletedAt,
        };
    }

    /**
     * Create a copy of the size
     */
    clone(): Size {
        return new Size(this.toObject());
    }

    // Private Methods

    private updateTimestamp(): void {
        this._updatedAt = new Date();
    }

    private inferCategory(name: string): "numeric" | "alpha" | "custom" {
        // Check if numeric (e.g., "28", "32", "40")
        if (/^\d+$/.test(name)) {
            return "numeric";
        }

        // Check if alpha (e.g., "XS", "S", "M", "L", "XL", "XXL")
        if (/^[XS|S|M|L|XL|XXL|XXXL]+$/i.test(name)) {
            return "alpha";
        }

        // Default to custom
        return "custom";
    }

    // Static Factory Methods

    /**
     * Create a new size with default values
     */
    static create(
        props: Omit<SizeProps, "id" | "createdAt" | "updatedAt">,
    ): Size {
        return new Size({
            ...props,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
    }

    /**
     * Create a numeric size
     */
    static createNumeric(
        value: number,
        displayName?: string,
        measurements?: SizeProps["measurements"],
    ): Size {
        return Size.create({
            name: value.toString(),
            displayName: displayName || value.toString(),
            order: value,
            isActive: true,
            category: "numeric",
            measurements,
        });
    }

    /**
     * Create an alpha size
     */
    static createAlpha(
        name: string,
        order: number,
        measurements?: SizeProps["measurements"],
    ): Size {
        return Size.create({
            name: name.toUpperCase(),
            displayName: name.toUpperCase(),
            order,
            isActive: true,
            category: "alpha",
            measurements,
        });
    }
}
