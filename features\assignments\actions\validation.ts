"use server";

import { db } from "@/shared/lib/db";

import { OptimisticValidationResponse } from "./types";

/**
 * Validación optimista para disponibilidad de tallas
 * Implementa un sistema de control de versiones para detectar cambios entre validaciones
 */
export async function validateOptimistic(
    garmentSizeId: string,
    quantity: number,
): Promise<OptimisticValidationResponse> {
    try {
        // Generar un token de versión basado en timestamp
        const versionToken = Date.now().toString();

        // Buscar la talla y su disponibilidad actual
        const garmentSize = await db.garmentSize.findUnique({
            where: { id: garmentSizeId },
            select: {
                id: true,
                totalQuantity: true,
                usedQuantity: true,
                garment: {
                    select: {
                        model: {
                            select: {
                                code: true,
                            },
                        },
                        color: {
                            select: {
                                name: true,
                            },
                        },
                    },
                },
                size: {
                    select: {
                        code: true,
                    },
                },
            },
        });

        if (!garmentSize) {
            return {
                success: false,
                error: "La talla especificada no existe",
                version: versionToken,
            };
        }

        // Calcular disponibilidad
        const availableQuantity =
            garmentSize.totalQuantity - garmentSize.usedQuantity;

        // Validar si hay suficiente disponibilidad
        if (quantity > availableQuantity) {
            // Proporcionar sugerencias útiles
            return {
                success: false,
                error: `Cantidad insuficiente disponible para talla ${garmentSize.size.code}`,
                version: versionToken,
                availableQuantity,
                suggestions: {
                    maxAvailable: availableQuantity,
                    recommendedQuantity: availableQuantity,
                },
            };
        }

        // Devolver éxito con token de versión para verificaciones posteriores
        return {
            success: true,
            version: versionToken,
            availableQuantity,
            data: {
                garmentSizeId,
                availableQuantity,
                requestedQuantity: quantity,
            } as any,
        };
    } catch (error) {
        // REMOVED: console.error("Error en validación optimista:", error);

        return {
            success: false,
            error: "Error al validar disponibilidad",
        };
    }
}

/**
 * Realiza la validación final y reconciliación cuando el usuario confirma una asignación
 * Verifica si ha habido cambios desde la última validación optimista
 */
export async function validateAndReconcile(
    garmentSizeId: string,
    quantity: number,
    versionToken?: string,
): Promise<OptimisticValidationResponse> {
    try {
        // Buscar la talla y su disponibilidad actual
        const garmentSize = await db.garmentSize.findUnique({
            where: { id: garmentSizeId },
            select: {
                id: true,
                totalQuantity: true,
                usedQuantity: true,
                garment: {
                    select: {
                        model: {
                            select: {
                                code: true,
                            },
                        },
                        color: {
                            select: {
                                name: true,
                            },
                        },
                    },
                },
                size: {
                    select: {
                        code: true,
                    },
                },
            },
        });

        if (!garmentSize) {
            return {
                success: false,
                error: "La talla especificada no existe",
            };
        }

        // Calcular disponibilidad actual
        const availableQuantity =
            garmentSize.totalQuantity - garmentSize.usedQuantity;

        // Si hay token de versión, verificar si ha habido cambios desde la última validación
        if (versionToken) {
            const tokenTimestamp = parseInt(versionToken, 10);
            const currentTimestamp = Date.now();

            // Si han pasado más de 60 segundos desde la última validación, considerar posibles cambios
            const timeThreshold = 60000; // 60 segundos
            const timeDifference = currentTimestamp - tokenTimestamp;

            if (timeDifference > timeThreshold) {
                // Validar nuevamente disponibilidad actual
                if (quantity > availableQuantity) {
                    return {
                        success: false,
                        error: "La disponibilidad ha cambiado desde la última validación",
                        availableQuantity,
                        suggestions: {
                            maxAvailable: availableQuantity,
                            recommendedQuantity: Math.min(
                                quantity,
                                availableQuantity,
                            ),
                        },
                    };
                }
            }
        }

        // Validar disponibilidad final
        if (quantity > availableQuantity) {
            return {
                success: false,
                error: `Cantidad insuficiente disponible para talla ${garmentSize.size.code}`,
                availableQuantity,
                suggestions: {
                    maxAvailable: availableQuantity,
                    recommendedQuantity: availableQuantity,
                },
            };
        }

        // Éxito - disponibilidad confirmada
        return {
            success: true,
            availableQuantity,
            data: {
                garmentSizeId,
                availableQuantity,
                requestedQuantity: quantity,
            } as any,
        };
    } catch (error) {
        // REMOVED: console.error("Error en reconciliación:", error);

        return {
            success: false,
            error: "Error al verificar disponibilidad final",
        };
    }
}

/**
 * Obtiene recomendaciones de cantidad basadas en disponibilidad e histórico
 */
export async function getQuantitySuggestions(
    garmentSizeId: string,
    contractorId: string,
): Promise<{
    suggestedQuantity: number;
    maxQuantity: number;
    recentAssignments: number[];
}> {
    try {
        // Obtener disponibilidad actual
        const garmentSize = await db.garmentSize.findUnique({
            where: { id: garmentSizeId },
            select: {
                totalQuantity: true,
                usedQuantity: true,
                size: {
                    select: {
                        id: true,
                    },
                },
            },
        });

        if (!garmentSize) {
            return {
                suggestedQuantity: 0,
                maxQuantity: 0,
                recentAssignments: [],
            };
        }

        const availableQuantity =
            garmentSize.totalQuantity - garmentSize.usedQuantity;
        const sizeId = garmentSize.size.id;

        // Obtener asignaciones recientes para este contratista y talla similar
        const recentAssignments = await db.assignment.findMany({
            where: {
                contractorId: contractorId,
                garmentSize: {
                    size: {
                        id: sizeId,
                    },
                },
            },
            orderBy: { createdAt: "desc" },
            take: 5,
            select: { quantity: true },
        });

        // Extraer cantidades recientes
        const recentQuantities = recentAssignments.map((a) => a.quantity);

        // Calcular cantidad sugerida basada en histórico y disponibilidad
        let suggestedQuantity = availableQuantity;

        if (recentQuantities.length > 0) {
            // Usar la media de asignaciones recientes como sugerencia
            const avgQuantity = Math.round(
                recentQuantities.reduce((sum, q) => sum + q, 0) /
                    recentQuantities.length,
            );

            // Limitar a disponibilidad actual
            suggestedQuantity = Math.min(avgQuantity, availableQuantity);
        }

        return {
            suggestedQuantity,
            maxQuantity: availableQuantity,
            recentAssignments: recentQuantities,
        };
    } catch (error) {
        // REMOVED: console.error("Error al obtener sugerencias:", error);

        return { suggestedQuantity: 0, maxQuantity: 0, recentAssignments: [] };
    }
}
