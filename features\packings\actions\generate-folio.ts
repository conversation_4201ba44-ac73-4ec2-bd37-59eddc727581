"use server";

import { format } from "date-fns";

import { db } from "@/shared/lib/db";

export async function generatePackingFolio(): Promise<string> {
    const today = format(new Date(), "yyyyMMdd");

    try {
        // Usar transacción para evitar duplicados
        const result = await db.$transaction(async (prisma) => {
            // Obtener o crear la secuencia para hoy
            const sequence = await prisma.packingSequence.upsert({
                where: { id: "single" },
                update: {
                    lastSequence: {
                        increment: 1,
                    },
                    lastDate: today,
                },
                create: {
                    id: "single",
                    lastDate: today,
                    lastSequence: 1,
                },
            });

            // Si el día cambió, reiniciar la secuencia
            if (sequence.lastDate !== today) {
                const updatedSequence = await prisma.packingSequence.update({
                    where: { id: "single" },
                    data: {
                        lastDate: today,
                        lastSequence: 1,
                    },
                });

                return `PKG-${today}-${String(1).padStart(4, "0")}`;
            }

            return `PKG-${today}-${String(sequence.lastSequence).padStart(4, "0")}`;
        });

        return result;
    } catch (error) {
        console.error("Error generating packing folio:", error);
        // Fallback con timestamp para evitar duplicados
        const timestamp = Date.now().toString().slice(-6);

        return `PKG-${today}-${timestamp}`;
    }
}
