import { Metadata } from "next";

import TodoList from "@/features/todos/components/TodoList";
import { Todo } from "@/features/todos/types";

export const metadata: Metadata = {
    title: "Tasks Management | Lohari Manufacturing",
    description:
        "Manage your tasks efficiently with Lohari Manufacturing System",
};

// Sample data for demonstration
const sampleTodos: Todo[] = [
    {
        id: "1",
        title: "Review new customer orders",
        description: "Check and approve new customer orders for production",
        priority: "high",
        completed: false,
        createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        updatedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        dueDate: new Date(Date.now() + 86400000).toISOString(), // 1 day later
    },
    {
        id: "2",
        title: "Assign contractors to new orders",
        description:
            "Match available contractors with pending production orders",
        priority: "medium",
        completed: false,
        createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        updatedAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        dueDate: new Date(Date.now() + 172800000).toISOString(), // 2 days later
    },
    {
        id: "3",
        title: "Generate remissions for completed orders",
        description:
            "Create remission documents for orders that have been completed",
        priority: "low",
        completed: true,
        createdAt: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
        updatedAt: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
    },
    {
        id: "4",
        title: "Inventory check for popular colors",
        description:
            "Verify inventory levels for the most commonly used colors",
        priority: "medium",
        completed: false,
        createdAt: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
        updatedAt: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
        dueDate: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago (overdue)
    },
    {
        id: "5",
        title: "Follow up on delayed contractor deliveries",
        description:
            "Contact contractors who have delayed deliveries and update status",
        priority: "high",
        completed: false,
        createdAt: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
        updatedAt: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
        dueDate: new Date(Date.now()).toISOString(), // Today
    },
];

export default function TodosPage() {
    return (
        <div className="container mx-auto py-6 max-w-4xl">
            <TodoList todos={sampleTodos} />
        </div>
    );
}
