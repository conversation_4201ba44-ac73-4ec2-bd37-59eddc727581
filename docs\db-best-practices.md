# Mejores Prácticas de Base de Datos - Next.js 15 + Prisma

## Resumen

Este documento describe las mejores prácticas implementadas en `shared/lib/db.ts` para manejar conexiones de base de datos con Prisma en Next.js 15.

## Características Principales

### 1. **Manejo de Errores Mejorado**

```typescript
// Uso básico
const result = await handleDbError(
  async () => await prisma.user.create({ data: userData }),
  "Error al crear usuario"
);

if (!result.success) {
  console.error(result.error);
  return;
}

console.log(result.data);
```

### 2. **Transacciones Seguras**

```typescript
// Transacción con opciones avanzadas
const result = await withTransaction(
  async (tx) => {
    const user = await tx.user.create({ data: userData });
    const profile = await tx.profile.create({ 
      data: { ...profileData, userId: user.id } 
    });
    return { user, profile };
  },
  {
    timeout: 10000, // 10 segundos
    isolationLevel: Prisma.TransactionIsolationLevel.Serializable
  }
);
```

### 3. **Operaciones en Lote**

```typescript
// Ejecutar múltiples operaciones independientes
const results = await batchOperations({
  users: () => prisma.user.findMany(),
  orders: () => prisma.order.findMany({ where: { status: 'pending' } }),
  stats: () => prisma.stats.findFirst({ orderBy: { createdAt: 'desc' } })
});

// Cada operación tiene su propio manejo de errores
if (results.users.success) {
  console.log(results.users.data);
}
```

### 4. **Paginación Optimizada**

```typescript
// Paginación con metadatos completos
const result = await paginate(prisma.order, {
  page: 2,
  pageSize: 20,
  where: { status: 'active' },
  orderBy: { createdAt: 'desc' },
  include: { customer: true }
});

console.log(result.data); // Array de órdenes
console.log(result.pagination); // { page, pageSize, total, hasNext, hasPrev }
```

### 5. **Monitoreo y Métricas**

```typescript
// Operación con logging detallado
const user = await dbOperation(
  "createUserWithProfile",
  async () => {
    // Operación compleja
    return prisma.user.create({
      data: userData,
      include: { profile: true }
    });
  },
  {
    logResult: true,
    warnThreshold: 2000 // Advertir si toma más de 2s
  }
);
```

### 6. **Health Checks**

```typescript
// Verificar salud de la base de datos
const health = await healthCheck();

if (health.status === 'healthy') {
  console.log(`Latencia: ${health.latency}ms`);
  console.log(`Queries activas: ${health.metrics?.activeQueries}`);
}
```

## Códigos de Error

El sistema maneja automáticamente estos códigos de error de Prisma:

- `P2002` - Violación de restricción única
- `P2003` - Violación de clave foránea
- `P2025` - Registro no encontrado
- `P1017` - Error de conexión
- `P2024` - Timeout del pool/query
- `P1001` - Base de datos inaccesible
- `P2023` - Datos inválidos

## Ejemplos de Uso en Server Actions

### Server Action con Manejo de Errores

```typescript
"use server";

import { handleDbError, db } from "@/shared/lib/db";
import { revalidatePath } from "next/cache";

export async function createOrder(data: OrderInput) {
  const result = await handleDbError(
    async () => {
      const order = await db.order.create({
        data: {
          ...data,
          status: 'pending',
          createdAt: new Date()
        }
      });
      
      revalidatePath('/orders');
      return order;
    },
    "No se pudo crear la orden"
  );

  return result;
}
```

### Server Action con Transacción

```typescript
"use server";

import { withTransaction, db } from "@/shared/lib/db";

export async function transferInventory(
  fromId: string, 
  toId: string, 
  items: ItemTransfer[]
) {
  return await withTransaction(async (tx) => {
    // Verificar disponibilidad
    const source = await tx.warehouse.findUnique({
      where: { id: fromId },
      include: { inventory: true }
    });

    if (!source) throw new Error("Almacén origen no encontrado");

    // Realizar transferencias
    for (const item of items) {
      await tx.inventory.update({
        where: { id: item.id },
        data: { 
          warehouseId: toId,
          quantity: { decrement: item.quantity }
        }
      });
    }

    // Registrar movimiento
    await tx.movement.create({
      data: {
        type: 'TRANSFER',
        fromId,
        toId,
        items: { create: items },
        createdAt: new Date()
      }
    });

    return { success: true };
  });
}
```

## Configuración Recomendada

### Variables de Entorno (.env)

```env
# Para Supabase con Supavisor
DATABASE_URL="postgresql://[user]:[pass]@[host]:6543/postgres?pgbouncer=true&connection_limit=1"
DIRECT_URL="postgresql://[user]:[pass]@[host]:5432/postgres"
```

### Prisma Schema

```prisma
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
```

## Mejores Prácticas

1. **Siempre usar `handleDbError`** para operaciones que pueden fallar
2. **Usar transacciones** para operaciones que deben ser atómicas
3. **Implementar paginación** para consultas que pueden retornar muchos registros
4. **Monitorear queries lentas** con `dbOperation` y su threshold
5. **Verificar la salud** de la base de datos en endpoints críticos
6. **Usar tipos seguros** con los helpers de TypeScript proporcionados

## Troubleshooting

### Query Timeout

Si encuentras timeouts frecuentes:

1. Verifica índices en la base de datos
2. Ajusta el `warnThreshold` en `dbOperation`
3. Considera usar paginación o limitar resultados

### Conexiones Agotadas

Si el pool se agota:

1. Revisa queries que no se cierran correctamente
2. Verifica el `connection_limit` en la URL
3. Usa el health check para monitorear conexiones activas

### Errores de Transacción

Para errores en transacciones:

1. Ajusta el `timeout` en `withTransaction`
2. Considera el nivel de aislamiento apropiado
3. Divide transacciones muy grandes en partes más pequeñas