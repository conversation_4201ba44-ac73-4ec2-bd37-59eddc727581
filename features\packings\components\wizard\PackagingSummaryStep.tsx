"use client";

import { useState, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Card,
    CardBody,
    CardHeader,
    Chip,
} from "@heroui/react";
import { Package, Box, ShoppingBag } from "lucide-react";

import { OrderForPacking } from "../../actions/select-orders-for-packing";

interface PackagingSummaryStepProps {
    orders: OrderForPacking[];
    onSummaryChange: (summary: PackagingSummaryData[]) => void;
    defaultPiecesPerBox?: number;
    defaultPiecesPerBag?: number;
}

interface SizeBreakdown {
    size: string;
    totalQuantity: number;
    firstQuality: number;
    secondQuality: number;
    boxes: number;
    loosePieces: number;
    bagsFirst: number;
    bagsSecond: number;
}

interface PackagingSummaryData {
    orderId: string;
    orderNumber: string;
    piecesPerBox: number;
    piecesPerBag: number;
    sizeBreakdown: SizeBreakdown[];
    totalBoxes: number;
    totalBagsFirst: number;
    totalBagsSecond: number;
    totalLoosePieces: number;
}

export function PackagingSummaryStep({
    orders,
    onSummaryChange,
    defaultPiecesPerBox = 50,
    defaultPiecesPerBag = 100,
}: PackagingSummaryStepProps) {
    const [summaries, setSummaries] = useState<PackagingSummaryData[]>([]);

    useEffect(() => {
        initializeSummaries();
    }, [orders]);

    useEffect(() => {
        onSummaryChange(summaries);
    }, [summaries, onSummaryChange]);

    const initializeSummaries = () => {
        const initialSummaries = orders.map((order) => {
            const sizeBreakdown = calculateSizeBreakdown(
                order,
                defaultPiecesPerBox,
                defaultPiecesPerBag,
            );

            return {
                orderId: order.id,
                orderNumber: order.transferNumber || "N/A",
                piecesPerBox: defaultPiecesPerBox,
                piecesPerBag: defaultPiecesPerBag,
                sizeBreakdown,
                ...calculateTotals(sizeBreakdown),
            };
        });

        setSummaries(initialSummaries);
    };

    const calculateSizeBreakdown = (
        order: OrderForPacking,
        piecesPerBox: number,
        piecesPerBag: number,
    ): SizeBreakdown[] => {
        const sizeMap = new Map<string, SizeBreakdown>();

        // Group products by size and quality
        order.products?.forEach((product) => {
            product.sizes?.forEach((size) => {
                const key = size.sizeCode;
                const existing = sizeMap.get(key) || {
                    size: size.sizeCode,
                    totalQuantity: 0,
                    firstQuality: 0,
                    secondQuality: 0,
                    boxes: 0,
                    loosePieces: 0,
                    bagsFirst: 0,
                    bagsSecond: 0,
                };

                // Assume 95% first quality, 5% second quality for demo
                const firstQty = Math.floor(size.availableQuantity * 0.95);
                const secondQty = size.availableQuantity - firstQty;

                existing.totalQuantity += size.availableQuantity;
                existing.firstQuality += firstQty;
                existing.secondQuality += secondQty;

                // Calculate boxes for first quality
                const boxesNeeded = Math.floor(firstQty / piecesPerBox);
                const loosePiecesFirst = firstQty % piecesPerBox;

                existing.boxes += boxesNeeded;
                existing.loosePieces += loosePiecesFirst;

                // Calculate bags for loose pieces
                if (loosePiecesFirst > 0) {
                    existing.bagsFirst += 1;
                }

                // Calculate bags for second quality
                if (secondQty > 0) {
                    existing.bagsSecond += Math.ceil(secondQty / piecesPerBag);
                }

                sizeMap.set(key, existing);
            });
        });

        return Array.from(sizeMap.values()).sort(
            (a, b) => getSizeOrder(a.size) - getSizeOrder(b.size),
        );
    };

    const getSizeOrder = (size: string): number => {
        const sizeOrder = ["XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL"];
        const index = sizeOrder.indexOf(size);

        return index === -1 ? 999 : index;
    };

    const calculateTotals = (sizeBreakdown: SizeBreakdown[]) => {
        return sizeBreakdown.reduce(
            (acc, size) => ({
                totalBoxes: acc.totalBoxes + size.boxes,
                totalBagsFirst: acc.totalBagsFirst + size.bagsFirst,
                totalBagsSecond: acc.totalBagsSecond + size.bagsSecond,
                totalLoosePieces: acc.totalLoosePieces + size.loosePieces,
            }),
            {
                totalBoxes: 0,
                totalBagsFirst: 0,
                totalBagsSecond: 0,
                totalLoosePieces: 0,
            },
        );
    };

    const handlePiecesPerBoxChange = (orderId: string, value: number) => {
        setSummaries((prev) =>
            prev.map((summary) => {
                if (summary.orderId === orderId) {
                    const order = orders.find((o) => o.id === orderId);

                    if (!order) return summary;

                    const newSizeBreakdown = calculateSizeBreakdown(
                        order,
                        value,
                        summary.piecesPerBag,
                    );

                    return {
                        ...summary,
                        piecesPerBox: value,
                        sizeBreakdown: newSizeBreakdown,
                        ...calculateTotals(newSizeBreakdown),
                    };
                }

                return summary;
            }),
        );
    };

    const handleCellEdit = (
        orderId: string,
        size: string,
        field: keyof SizeBreakdown,
        value: number,
    ) => {
        setSummaries((prev) =>
            prev.map((summary) => {
                if (summary.orderId === orderId) {
                    const newSizeBreakdown = summary.sizeBreakdown.map((s) => {
                        if (s.size === size) {
                            return { ...s, [field]: value };
                        }

                        return s;
                    });

                    return {
                        ...summary,
                        sizeBreakdown: newSizeBreakdown,
                        ...calculateTotals(newSizeBreakdown),
                    };
                }

                return summary;
            }),
        );
    };

    return (
        <div className="space-y-6">
            {summaries.map((summary) => (
                <Card key={summary.orderId} className="border-1">
                    <CardHeader className="flex flex-row items-center justify-between">
                        <div className="flex items-center gap-3">
                            <Package className="w-5 h-5 text-primary" />
                            <h3 className="text-lg font-semibold">
                                Orden {summary.orderNumber}
                            </h3>
                        </div>
                        <div className="flex items-center gap-4">
                            <Input
                                className="w-32"
                                label="Piezas por caja"
                                min={1}
                                size="sm"
                                type="number"
                                value={summary.piecesPerBox.toString()}
                                onChange={(e) =>
                                    handlePiecesPerBoxChange(
                                        summary.orderId,
                                        parseInt(e.target.value) || 1,
                                    )
                                }
                            />
                        </div>
                    </CardHeader>
                    <CardBody>
                        <Table aria-label="Resumen de empaque por talla">
                            <TableHeader>
                                <TableColumn>TALLA</TableColumn>
                                <TableColumn align="center">CAJAS</TableColumn>
                                <TableColumn align="center">PIEZAS</TableColumn>
                                <TableColumn align="center">
                                    BOLSAS 1°
                                </TableColumn>
                                <TableColumn align="center">
                                    BOLSAS 2°
                                </TableColumn>
                            </TableHeader>
                            <TableBody>
                                {
                                    summary.sizeBreakdown.map((size) => (
                                        <TableRow key={size.size}>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Chip
                                                        size="sm"
                                                        variant="flat"
                                                    >
                                                        {size.size}
                                                    </Chip>
                                                    <span className="text-xs text-gray-500">
                                                        ({size.totalQuantity}{" "}
                                                        pzs)
                                                    </span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    className="w-20 mx-auto"
                                                    min={0}
                                                    size="sm"
                                                    type="number"
                                                    value={size.boxes.toString()}
                                                    onChange={(e) =>
                                                        handleCellEdit(
                                                            summary.orderId,
                                                            size.size,
                                                            "boxes",
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    className="w-20 mx-auto"
                                                    min={0}
                                                    size="sm"
                                                    type="number"
                                                    value={size.loosePieces.toString()}
                                                    onChange={(e) =>
                                                        handleCellEdit(
                                                            summary.orderId,
                                                            size.size,
                                                            "loosePieces",
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    className="w-20 mx-auto"
                                                    min={0}
                                                    size="sm"
                                                    type="number"
                                                    value={size.bagsFirst.toString()}
                                                    onChange={(e) =>
                                                        handleCellEdit(
                                                            summary.orderId,
                                                            size.size,
                                                            "bagsFirst",
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    className="w-20 mx-auto"
                                                    min={0}
                                                    size="sm"
                                                    type="number"
                                                    value={size.bagsSecond.toString()}
                                                    onChange={(e) =>
                                                        handleCellEdit(
                                                            summary.orderId,
                                                            size.size,
                                                            "bagsSecond",
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            </TableCell>
                                        </TableRow>
                                    )) as any
                                }
                                <TableRow>
                                    <TableCell className="font-semibold">
                                        TOTAL
                                    </TableCell>
                                    <TableCell className="font-semibold text-center">
                                        <div className="flex items-center justify-center gap-1">
                                            <Box className="w-4 h-4" />
                                            {summary.totalBoxes}
                                        </div>
                                    </TableCell>
                                    <TableCell className="font-semibold text-center">
                                        {summary.totalLoosePieces}
                                    </TableCell>
                                    <TableCell className="font-semibold text-center">
                                        <div className="flex items-center justify-center gap-1">
                                            <ShoppingBag className="w-4 h-4 text-success" />
                                            {summary.totalBagsFirst}
                                        </div>
                                    </TableCell>
                                    <TableCell className="font-semibold text-center">
                                        <div className="flex items-center justify-center gap-1">
                                            <ShoppingBag className="w-4 h-4 text-warning" />
                                            {summary.totalBagsSecond}
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </CardBody>
                </Card>
            ))}

            <Card className="bg-primary-50 border-primary-200">
                <CardBody>
                    <div className="grid grid-cols-4 gap-4 text-center">
                        <div>
                            <p className="text-sm text-gray-600">Total Cajas</p>
                            <p className="text-2xl font-bold text-primary">
                                {summaries.reduce(
                                    (acc, s) => acc + s.totalBoxes,
                                    0,
                                )}
                            </p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-600">
                                Total Bolsas 1°
                            </p>
                            <p className="text-2xl font-bold text-success">
                                {summaries.reduce(
                                    (acc, s) => acc + s.totalBagsFirst,
                                    0,
                                )}
                            </p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-600">
                                Total Bolsas 2°
                            </p>
                            <p className="text-2xl font-bold text-warning">
                                {summaries.reduce(
                                    (acc, s) => acc + s.totalBagsSecond,
                                    0,
                                )}
                            </p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-600">
                                Piezas Sueltas
                            </p>
                            <p className="text-2xl font-bold">
                                {summaries.reduce(
                                    (acc, s) => acc + s.totalLoosePieces,
                                    0,
                                )}
                            </p>
                        </div>
                    </div>
                </CardBody>
            </Card>
        </div>
    );
}
