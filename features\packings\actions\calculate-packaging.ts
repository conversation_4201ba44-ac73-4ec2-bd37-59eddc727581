"use server";

import type { QualityType } from "@/lib/types/packing";

import { z } from "zod";

import { db } from "@/shared/lib/db";

const calculatePackagingSchema = z.object({
    items: z
        .array(
            z.object({
                garmentSizeId: z.string(),
                quantity: z.number().int().positive(),
                qualityType: z.enum([
                    "primera",
                    "segunda",
                    "manchada",
                    "incompleta",
                ]),
                preferredPackaging: z.enum(["caja", "bolsa"]).optional(),
            }),
        )
        .min(1),
});

export interface PackagingCalculation {
    garmentSizeId: string;
    quantity: number;
    qualityType: QualityType;
    packagingType: "caja" | "bolsa";
    packagingUnits: number;
    piecesPerUnit: number;
    loosePieces: number;
    totalVolume: number;
    totalWeight: number;
}

export interface CalculatePackagingResult {
    success: boolean;
    data?: {
        calculations: PackagingCalculation[];
        summary: {
            totalBoxes: number;
            totalBags: number;
            totalLoosePieces: number;
            totalVolume: number;
            totalWeight: number;
            packingEfficiency: number; // Porcentaje de eficiencia del empaque
        };
        recommendations: string[];
    };
    error?: string;
}

export async function calculatePackaging(
    data: z.infer<typeof calculatePackagingSchema>,
): Promise<CalculatePackagingResult> {
    try {
        const validatedData = calculatePackagingSchema.parse(data);

        // Obtener configuración de la empresa
        const companySettings = await db.companySettings.findUnique({
            where: { id: "default" },
        });

        const defaultBoxCapacity = companySettings?.defaultBoxCapacity || 25;
        const defaultBagCapacity = companySettings?.defaultBagCapacity || 20;

        // Configuración de empaque por tipo de calidad
        const packagingRules = {
            primera: { preferBox: true, allowMix: false },
            segunda: { preferBox: false, allowMix: true },
            manchada: {
                preferBox: false,
                allowMix: false,
                requireSeparate: true,
            },
            incompleta: {
                preferBox: false,
                allowMix: false,
                requireSeparate: true,
            },
        };

        const calculations: PackagingCalculation[] = [];
        const recommendations: string[] = [];

        let totalBoxes = 0;
        let totalBags = 0;
        let totalLoosePieces = 0;
        let totalVolume = 0;
        let totalWeight = 0;

        // Agrupar items por tipo de calidad
        const itemsByQuality = validatedData.items.reduce(
            (acc, item) => {
                if (!acc[item.qualityType]) acc[item.qualityType] = [];
                acc[item.qualityType].push(item);

                return acc;
            },
            {} as Record<QualityType, typeof validatedData.items>,
        );

        // Calcular empaque para cada grupo de calidad
        for (const [qualityType, items] of Object.entries(itemsByQuality)) {
            const rules = packagingRules[qualityType as QualityType];

            // Si requiere empaque separado, agregar recomendación
            if ((rules as any).requireSeparate) {
                recommendations.push(
                    `Los productos ${qualityType} deben empacarse por separado y etiquetarse claramente`,
                );
            }

            for (const item of items) {
                // Obtener información del producto para estimar peso/volumen
                const garmentSize = await db.garmentSize.findUnique({
                    where: { id: item.garmentSizeId },
                    include: {
                        garment: {
                            include: {
                                model: true,
                            },
                        },
                        size: true,
                    },
                });

                if (!garmentSize) continue;

                // Estimación de peso y volumen (estos valores deberían venir de la BD idealmente)
                const estimatedPieceWeight = 0.3; // kg por pieza (ejemplo)
                const estimatedPieceVolume = 0.002; // m³ por pieza (ejemplo)

                // Determinar tipo de empaque
                let packagingType: "caja" | "bolsa";
                let capacity: number;

                if (item.preferredPackaging) {
                    packagingType = item.preferredPackaging;
                    capacity =
                        packagingType === "caja"
                            ? defaultBoxCapacity
                            : defaultBagCapacity;
                } else if (
                    rules.preferBox &&
                    item.quantity >= defaultBoxCapacity
                ) {
                    packagingType = "caja";
                    capacity = defaultBoxCapacity;
                } else {
                    packagingType = "bolsa";
                    capacity = defaultBagCapacity;
                }

                // Calcular unidades necesarias
                const unitsNeeded = Math.floor(item.quantity / capacity);
                const loosePieces = item.quantity % capacity;

                // Si quedan muchas piezas sueltas, agregar una unidad más
                const finalUnits =
                    loosePieces > capacity * 0.3
                        ? unitsNeeded + 1
                        : unitsNeeded;
                const finalLoosePieces =
                    loosePieces > capacity * 0.3 ? 0 : loosePieces;

                // Calcular peso y volumen
                const itemWeight = item.quantity * estimatedPieceWeight;
                const itemVolume = item.quantity * estimatedPieceVolume;

                calculations.push({
                    garmentSizeId: item.garmentSizeId,
                    quantity: item.quantity,
                    qualityType: item.qualityType as QualityType,
                    packagingType,
                    packagingUnits: finalUnits,
                    piecesPerUnit: capacity,
                    loosePieces: finalLoosePieces,
                    totalVolume: itemVolume,
                    totalWeight: itemWeight,
                });

                // Actualizar totales
                if (packagingType === "caja") {
                    totalBoxes += finalUnits;
                } else {
                    totalBags += finalUnits;
                }
                totalLoosePieces += finalLoosePieces;
                totalVolume += itemVolume;
                totalWeight += itemWeight;
            }
        }

        // Calcular eficiencia del empaque
        const totalPieces = validatedData.items.reduce(
            (sum, item) => sum + item.quantity,
            0,
        );
        const totalCapacityUsed =
            totalBoxes * defaultBoxCapacity + totalBags * defaultBagCapacity;
        const packingEfficiency =
            totalCapacityUsed > 0
                ? Math.round((totalPieces / totalCapacityUsed) * 100)
                : 0;

        // Agregar recomendaciones basadas en la eficiencia
        if (packingEfficiency < 70) {
            recommendations.push(
                "La eficiencia del empaque es baja. Considera reorganizar los productos para optimizar el espacio",
            );
        }

        if (totalLoosePieces > 20) {
            recommendations.push(
                `Hay ${totalLoosePieces} piezas sueltas. Considera usar bolsas adicionales para protegerlas`,
            );
        }

        if (totalBoxes > 10) {
            recommendations.push(
                "Gran cantidad de cajas. Verifica que el transporte tenga capacidad suficiente",
            );
        }

        return {
            success: true,
            data: {
                calculations,
                summary: {
                    totalBoxes,
                    totalBags,
                    totalLoosePieces,
                    totalVolume: Math.round(totalVolume * 1000) / 1000, // Redondear a 3 decimales
                    totalWeight: Math.round(totalWeight * 100) / 100, // Redondear a 2 decimales
                    packingEfficiency,
                },
                recommendations,
            },
        };
    } catch (error) {
        console.error("Error calculating packaging:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return { success: false, error: "Error al calcular el empaque" };
    }
}

// Función auxiliar para optimizar el empaque de múltiples órdenes
export async function optimizeMultiOrderPacking(orderIds: string[]) {
    try {
        // Obtener todos los items de las órdenes
        const orders = await db.order.findMany({
            where: { id: { in: orderIds } },
            include: {
                garments: {
                    include: {
                        sizes: {
                            where: {
                                totalQuantity: {
                                    gt: db.garmentSize.fields.usedQuantity,
                                },
                            },
                        },
                    },
                },
            },
        });

        // Agrupar items similares para empaque eficiente
        const itemGroups = new Map<
            string,
            { items: any[]; totalQuantity: number }
        >();

        orders.forEach((order) => {
            order.garments.forEach((garment) => {
                garment.sizes.forEach((size) => {
                    const key = `${garment.modelId}-${garment.colorId}-${size.sizeId}`;
                    const available = size.totalQuantity - size.usedQuantity;

                    if (!itemGroups.has(key)) {
                        itemGroups.set(key, { items: [], totalQuantity: 0 });
                    }

                    const group = itemGroups.get(key)!;

                    group.items.push({ ...size, orderId: order.id });
                    group.totalQuantity += available;
                });
            });
        });

        // Retornar sugerencias de agrupación
        return {
            success: true,
            groups: Array.from(itemGroups.entries()).map(([key, group]) => ({
                key,
                totalQuantity: group.totalQuantity,
                orderCount: new Set(group.items.map((i) => i.orderId)).size,
                suggestion:
                    group.totalQuantity > 50
                        ? "Considera crear un packing consolidado para este producto"
                        : "Puede empacarse junto con otros productos similares",
            })),
        };
    } catch (error) {
        console.error("Error optimizing multi-order packing:", error);

        return { success: false, error: "Error al optimizar empaque múltiple" };
    }
}
