import React from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardProps,
} from "@heroui/react";

// import { cn } from "@/shared/utils/cn"; // Comment out - module doesn't exist
const cn = (...classes: any[]) => classes.filter(Boolean).join(" "); // Simple fallback

export interface ImprovedCardProps extends CardProps {
    variant?: "stats" | "content" | "interactive";
    isLoading?: boolean;
}

/**
 * Card mejorado con variantes consistentes
 * Basado en HeroUI Card con estilos unificados
 */
export const ImprovedCard = React.forwardRef<HTMLDivElement, ImprovedCardProps>(
    (
        {
            variant = "content",
            isLoading = false,
            className,
            children,
            ...props
        },
        ref,
    ) => {
        const variantStyles = {
            stats: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-md transition-shadow",
            content:
                "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-md",
            interactive:
                "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 shadow-sm hover:shadow-lg hover:border-[var(--color-primary-6)] cursor-pointer transition-all",
        };

        if (isLoading) {
            return (
                <Card
                    ref={ref}
                    className={cn(
                        variantStyles[variant],
                        "animate-pulse",
                        className,
                    )}
                    {...props}
                >
                    <CardBody>
                        <div className="space-y-3">
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6" />
                        </div>
                    </CardBody>
                </Card>
            );
        }

        return (
            <Card
                ref={ref}
                className={cn(variantStyles[variant], className)}
                {...props}
            >
                {children}
            </Card>
        );
    },
);

ImprovedCard.displayName = "ImprovedCard";

// Re-export HeroUI Card components con estilos mejorados
export const ImprovedCardHeader = ({
    className,
    ...props
}: React.ComponentProps<typeof CardHeader>) => (
    <CardHeader
        className={cn("text-high-contrast font-medium", className)}
        {...props}
    />
);

export const ImprovedCardBody = ({
    className,
    ...props
}: React.ComponentProps<typeof CardBody>) => (
    <CardBody className={cn("text-medium-contrast", className)} {...props} />
);

export const ImprovedCardFooter = ({
    className,
    ...props
}: React.ComponentProps<typeof CardFooter>) => (
    <CardFooter
        className={cn(
            "border-t border-gray-200 dark:border-gray-700",
            className,
        )}
        {...props}
    />
);

export default ImprovedCard;
