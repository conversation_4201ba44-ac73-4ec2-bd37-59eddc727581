"use client";

import React from "react";
import { Slider } from "@heroui/slider";
import { Chip } from "@heroui/chip";
import { ZoomIn, ZoomOut } from "lucide-react";

interface StatusBarProps {
    zoom: number;
    onZoomChange: (zoom: number) => void;
    orientation: "portrait" | "landscape";
    hasChanges: boolean;
    changesCount: number;
}

export const StatusBar: React.FC<StatusBarProps> = ({
    zoom,
    onZoomChange,
    orientation,
    hasChanges,
    changesCount,
}) => {
    return (
        <div className="flex items-center justify-between px-4 py-3 border-t bg-white">
            {/* Zoom Control */}
            <div className="flex items-center gap-3 min-w-[200px]">
                <ZoomOut className="w-4 h-4 text-gray-500" />
                <Slider
                    className="max-w-[120px]"
                    maxValue={200}
                    minValue={50}
                    size="sm"
                    step={10}
                    value={zoom}
                    onChange={(value) => onZoomChange(value as number)}
                />
                <ZoomIn className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600 min-w-[45px]">
                    {zoom}%
                </span>
            </div>

            {/* Center Info */}
            <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Página 1 de 1</span>
                <span>•</span>
                <span>
                    {orientation === "portrait" ? "Vertical" : "Horizontal"}
                </span>
            </div>

            {/* Changes Indicator */}
            <div className="min-w-[200px] flex justify-end">
                {hasChanges && (
                    <Chip color="warning" size="sm" variant="flat">
                        {changesCount} cambio{changesCount !== 1 ? "s" : ""} sin
                        guardar
                    </Chip>
                )}
            </div>
        </div>
    );
};
