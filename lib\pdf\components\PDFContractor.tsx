import React from "react";
import { View, Text } from "@react-pdf/renderer";

import { remissionStyles as styles } from "../styles/remission";

interface PDFContractorProps {
    contractor: {
        name: string;
        location: string;
        contact?: string;
    };
}

export const PDFContractor: React.FC<PDFContractorProps> = ({ contractor }) => {
    return (
        <View style={styles.section}>
            <Text style={styles.sectionTitle}>INFORMACIÓN DEL CONTRATISTA</Text>

            <View style={styles.row}>
                <Text style={styles.label}>Nombre:</Text>
                <Text style={[styles.value, styles.bold]}>
                    {contractor.name}
                </Text>
            </View>

            <View style={styles.row}>
                <Text style={styles.label}>Ubicación:</Text>
                <Text style={styles.value}>{contractor.location}</Text>
            </View>

            {contractor.contact && (
                <View style={styles.row}>
                    <Text style={styles.label}>Contacto:</Text>
                    <Text style={styles.value}>{contractor.contact}</Text>
                </View>
            )}
        </View>
    );
};
