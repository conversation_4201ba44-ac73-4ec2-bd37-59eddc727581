// Context Handoff System for AUTO-FLOW V5.1
// Permite mantener contexto entre conversaciones de Claude

import { 
  create_entities, 
  open_nodes, 
  search_nodes,
  list_tasks,
  execute_command
} from '../mcp-tools';

interface ContextSnapshot {
  id: string;
  timestamp: number;
  projectPath: string;
  currentBranch: string;
  lastTasks: any;
  recentDecisions: string[];
  modifiedFiles: string[];
  activeWorkflow: string;
  clearThoughtUsage: Record<string, number>;
  conversationSummary: string;
}

export class ContextHandoff {
  private readonly ENTITY_TYPE = 'CONTEXT_SNAPSHOT';
  
  /**
   * Guarda el contexto actual antes de que se acabe el límite de conversación
   */
  async saveContextSnapshot(summary: string = ''): Promise<string> {
    try {
      // 1. Obtener branch actual de Git
      const branchResult = await execute_command({
        command: 'git branch --show-current',
        timeout_ms: 5000
      });
      const currentBranch = branchResult.output?.trim() || 'main';

      // 2. Obtener todas las tareas
      const tasks = await list_tasks({ status: 'all' });

      // 3. Buscar decisiones recientes en Memento
      const decisions = await search_nodes({
        query: 'Decision recent'
      });
      const recentDecisions = decisions.entities
        .slice(0, 10)
        .map(d => d.observations[0]);

      // 4. Obtener archivos modificados
      const gitStatus = await execute_command({
        command: 'git diff --name-only',
        timeout_ms: 5000
      });
      const modifiedFiles = gitStatus.output?.split('\n').filter(f => f) || [];

      // 5. Buscar workflow activo
      const workflows = await search_nodes({
        query: 'WORKFLOW active'
      });
      const activeWorkflow = workflows.entities[0]?.name || '';

      // 6. Obtener uso de Clear Thought (si existe)
      const clearThoughtUsage = await this.getClearThoughtUsage();

      // Crear snapshot
      const snapshot: ContextSnapshot = {
        id: `ContextSnapshot_${Date.now()}`,
        timestamp: Date.now(),
        projectPath: process.cwd(),
        currentBranch,
        lastTasks: tasks,
        recentDecisions,
        modifiedFiles,
        activeWorkflow,
        clearThoughtUsage,
        conversationSummary: summary
      };

      // Guardar en Memento
      await create_entities([{
        name: snapshot.id,
        entityType: this.ENTITY_TYPE,
        observations: [
          JSON.stringify(snapshot),
          `Branch: ${currentBranch}`,
          `Tasks: ${tasks.pending?.length || 0} pending, ${tasks.completed?.length || 0} completed`,
          `Modified files: ${modifiedFiles.length}`,
          `Summary: ${summary}`,
          'Conversation limit reached - context saved'
        ]
      }]);

      console.log(`✅ Context snapshot saved: ${snapshot.id}`);
      return snapshot.id;

    } catch (error) {
      console.error('Error saving context snapshot:', error);
      throw error;
    }
  }

  /**
   * Carga el contexto de una conversación anterior
   */
  async loadContextSnapshot(snapshotId?: string): Promise<ContextSnapshot | null> {
    try {
      let targetSnapshot = snapshotId;

      // Si no se especifica ID, buscar el más reciente
      if (!targetSnapshot) {
        const snapshots = await search_nodes({
          query: `${this.ENTITY_TYPE} recent`
        });
        
        if (snapshots.entities.length === 0) {
          console.log('No context snapshots found');
          return null;
        }

        // Ordenar por timestamp descendente
        const sorted = snapshots.entities.sort((a, b) => {
          const timeA = parseInt(a.name.split('_')[1] || '0');
          const timeB = parseInt(b.name.split('_')[1] || '0');
          return timeB - timeA;
        });

        targetSnapshot = sorted[0].name;
      }

      // Cargar snapshot
      const result = await open_nodes({
        names: [targetSnapshot]
      });

      if (!result.entities || result.entities.length === 0) {
        console.log(`Snapshot ${targetSnapshot} not found`);
        return null;
      }

      const snapshot = JSON.parse(result.entities[0].observations[0]) as ContextSnapshot;
      
      console.log(`✅ Context loaded from: ${targetSnapshot}`);
      console.log(`📅 Saved at: ${new Date(snapshot.timestamp).toLocaleString()}`);
      console.log(`🌿 Branch: ${snapshot.currentBranch}`);
      console.log(`📋 Tasks: ${snapshot.lastTasks.pending?.length || 0} pending`);
      
      return snapshot;

    } catch (error) {
      console.error('Error loading context snapshot:', error);
      return null;
    }
  }

  /**
   * Genera un resumen para la nueva conversación
   */
  generateHandoffMessage(snapshot: ContextSnapshot): string {
    const timeSince = this.getTimeSince(snapshot.timestamp);
    
    return `
# 🔄 Continuando trabajo en proyecto lohari

## 📊 Estado anterior (${timeSince} ago)

**Branch activo:** ${snapshot.currentBranch}
**Tareas pendientes:** ${snapshot.lastTasks.pending?.length || 0}
**Archivos modificados:** ${snapshot.modifiedFiles.length}

## 📝 Resumen
${snapshot.conversationSummary || 'No hay resumen disponible'}

## 🎯 Tareas pendientes
${this.formatPendingTasks(snapshot.lastTasks.pending || [])}

## 🧠 Decisiones recientes
${snapshot.recentDecisions.slice(0, 5).map(d => `- ${d}`).join('\n')}

## 💡 Para continuar:
\`\`\`bash
"AUTO-FLOW LOAD: ${snapshot.id}"
\`\`\`

O simplemente continúa con:
\`\`\`bash
"AUTO-FLOW: [tu siguiente tarea]"
\`\`\`
`;
  }

  /**
   * Obtiene estadísticas de uso de Clear Thought
   */
  private async getClearThoughtUsage(): Promise<Record<string, number>> {
    try {
      const toolMetrics = await search_nodes({
        query: 'CLEAR_THOUGHT_METRIC'
      });

      const usage: Record<string, number> = {};
      
      toolMetrics.entities.forEach(metric => {
        const toolName = metric.observations[0];
        usage[toolName] = (usage[toolName] || 0) + 1;
      });

      return usage;
    } catch {
      return {};
    }
  }

  /**
   * Calcula tiempo transcurrido
   */
  private getTimeSince(timestamp: number): string {
    const minutes = Math.floor((Date.now() - timestamp) / 60000);
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours} hours`;
    const days = Math.floor(hours / 24);
    return `${days} days`;
  }

  /**
   * Formatea tareas pendientes
   */
  private formatPendingTasks(tasks: any[]): string {
    if (!tasks || tasks.length === 0) {
      return 'No hay tareas pendientes';
    }

    return tasks.slice(0, 5).map(task => 
      `- **${task.name}** (ID: ${task.id.slice(0, 8)}...)`
    ).join('\n');
  }
}

// Singleton instance
export const contextHandoff = new ContextHandoff();

// Comandos de AUTO-FLOW
export async function handleAutoFlowContextCommands(command: string): Promise<boolean> {
  // AUTO-FLOW SAVE: snapshot for next conversation
  if (command.includes('SAVE:')) {
    const summary = command.split('SAVE:')[1].trim();
    const snapshotId = await contextHandoff.saveContextSnapshot(summary);
    console.log(`
✅ Context saved successfully!

Copy this command for your next conversation:
\`\`\`
"AUTO-FLOW LOAD: ${snapshotId}"
\`\`\`
    `);
    return true;
  }

  // AUTO-FLOW LOAD: continue from [snapshot-id]
  if (command.includes('LOAD:')) {
    const snapshotId = command.split('LOAD:')[1].trim();
    const snapshot = await contextHandoff.loadContextSnapshot(snapshotId);
    
    if (snapshot) {
      console.log(contextHandoff.generateHandoffMessage(snapshot));
      return true;
    }
  }

  return false;
}