/**
 * Simple toast notification utility
 * Since HeroUI's toast system seems to be in flux, we'll use a simple approach
 */

export interface ToastOptions {
    title: string;
    description?: string;
    color?: "primary" | "success" | "warning" | "danger" | "default";
}

class ToastManager {
    private toasts: Array<ToastOptions & { id: number }> = [];
    private nextId = 1;
    private listeners: Array<
        (toasts: Array<ToastOptions & { id: number }>) => void
    > = [];

    addToast(options: ToastOptions) {
        const toast = { ...options, id: this.nextId++ };

        this.toasts.push(toast);
        this.notifyListeners();

        // Auto-remove after 5 seconds
        setTimeout(() => {
            this.removeToast(toast.id);
        }, 5000);
    }

    removeToast(id: number) {
        this.toasts = this.toasts.filter((t) => t.id !== id);
        this.notifyListeners();
    }

    subscribe(
        listener: (toasts: Array<ToastOptions & { id: number }>) => void,
    ) {
        this.listeners.push(listener);

        return () => {
            this.listeners = this.listeners.filter((l) => l !== listener);
        };
    }

    private notifyListeners() {
        this.listeners.forEach((listener) => listener(this.toasts));
    }

    getToasts() {
        return this.toasts;
    }
}

export const toastManager = new ToastManager();

export function showToast(options: ToastOptions) {
    toastManager.addToast(options);
}
