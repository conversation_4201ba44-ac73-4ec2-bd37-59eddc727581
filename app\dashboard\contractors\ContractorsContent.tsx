"use client";

import { useState, useEffect, useTransition, useCallback } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import {
    Card,
    CardBody,
    Chip,
    Button,
    Input,
    Pagination,
    Select,
    SelectItem,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    addToast,
    Tooltip,
    Kbd,
    Avatar,
    Badge,
    Spinner,
} from "@heroui/react";
import {
    PlusIcon,
    MagnifyingGlassIcon,
    ArrowPathIcon,
    EllipsisVerticalIcon,
    PencilIcon,
    TrashIcon,
    EyeIcon,
    HomeIcon,
    UsersIcon,
    ChevronRightIcon,
    ChevronDownIcon,
    ArrowDownIcon,
    ArrowUpIcon,
    ClipboardDocumentCheckIcon,
    DocumentTextIcon,
    InformationCircleIcon,
    CheckBadgeIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import {
    useContractors,
    useDeleteContractor,
} from "@/features/contractors/hooks/useContractor";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";
import EmptyState from "@/shared/components/ui/EmptyState";
import { Contractor } from "@/features/contractors/schemas/schema";

// Variantes para animaciones
const pageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            staggerChildren: 0.07,
            ease: [0.22, 1, 0.36, 1],
        },
    },
};

const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.4, ease: [0.22, 1, 0.36, 1] },
    },
};

const fadeInVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: { duration: 0.3 },
    },
};

const skeletonPulse = {
    initial: { opacity: 0.6 },
    animate: {
        opacity: [0.6, 0.8, 0.6],
        transition: {
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
        },
    },
};

export default function ContractorsContent() {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [isPending, startTransition] = useTransition();

    // Estados para la UI
    const [searchQuery, setSearchQuery] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [orderBy, setOrderBy] = useState<string>("createdAt");
    const [order, setOrder] = useState<"asc" | "desc">("desc");
    const [isConfirmingDelete, setIsConfirmingDelete] = useState<string | null>(
        null,
    );
    const [filterAssignments, setFilterAssignments] = useState<string | null>(
        null,
    );
    const [isStatsExpanded, setIsStatsExpanded] = useState(false);

    // Obtener parámetros de URL al cargar
    useEffect(() => {
        const params = {
            search: searchParams.get("search"),
            page: searchParams.get("page"),
            perPage: searchParams.get("perPage"),
            sort: searchParams.get("sort"),
            order: searchParams.get("order"),
            assignments: searchParams.get("assignments"),
        };

        if (params.search) setSearchQuery(params.search);
        if (params.page) setCurrentPage(parseInt(params.page));
        if (params.perPage) setPerPage(parseInt(params.perPage));
        if (params.sort) setOrderBy(params.sort);
        if (params.order) setOrder(params.order as "asc" | "desc");
        if (params.assignments) setFilterAssignments(params.assignments);
    }, [searchParams]);

    // Actualizar URL cuando cambian los filtros
    const updateUrl = useCallback(() => {
        startTransition(() => {
            const params = new URLSearchParams();

            if (searchQuery) params.set("search", searchQuery);
            if (currentPage > 1) params.set("page", currentPage.toString());
            if (perPage !== 10) params.set("perPage", perPage.toString());
            if (orderBy !== "createdAt") params.set("sort", orderBy);
            if (order !== "desc") params.set("order", order);
            if (filterAssignments) params.set("assignments", filterAssignments);

            router.push(`${pathname}?${params.toString()}`, { scroll: false });
        });
    }, [
        searchQuery,
        currentPage,
        perPage,
        orderBy,
        order,
        filterAssignments,
        pathname,
        router,
    ]);

    // Actualizar URL cuando cambian los filtros
    useEffect(() => {
        updateUrl();
    }, [
        searchQuery,
        currentPage,
        perPage,
        orderBy,
        order,
        filterAssignments,
        updateUrl,
    ]);

    // Obtener datos de contratistas
    const { contractors, pagination, isLoading, refetch } = useContractors({
        search: searchQuery,
        orderBy,
        order,
        page: currentPage,
        perPage,
    });

    // Escuchar eventos de revalidación
    const { isRevalidating } = useRevalidationListener("contractor");

    // Efecto para revalidar cuando cambia isRevalidating
    useEffect(() => {
        if (isRevalidating) {
            refetch();
        }
    }, [isRevalidating, refetch]);

    // Hook para eliminar contratista
    const { remove } = useDeleteContractor();

    // Función para eliminar un contratista
    const handleDelete = async (id: string) => {
        try {
            await remove(id);
            refetch();
            setIsConfirmingDelete(null);
            addToast({
                title: "Contratista eliminado",
                description: "El contratista ha sido eliminado correctamente",
                color: "success",
            });
        } catch (error) {
            // REMOVED: console.error("Error al eliminar:", error);
            addToast({
                title: "Error",
                description: "No se pudo eliminar el contratista",
                color: "danger",
            });
        }
    };

    // Cambiar ordenamiento
    const handleSortChange = (column: string) => {
        if (orderBy === column) {
            setOrder(order === "asc" ? "desc" : "asc");
        } else {
            setOrderBy(column);
            setOrder("asc");
        }
        setCurrentPage(1);
    };

    // Manejar búsqueda
    const handleSearch = (value: string) => {
        setSearchQuery(value);
        setCurrentPage(1);
    };

    // Comprobar si hay datos o mostrando estado vacío
    const isEmpty = !isLoading && (!contractors || contractors.length === 0);

    // Filtrar contratistas por asignaciones
    const filteredContractors = filterAssignments
        ? contractors.filter((contractor) => {
              if (filterAssignments === "with") {
                  return contractor._count.assignments > 0;
              } else if (filterAssignments === "without") {
                  return contractor._count.assignments === 0;
              }

              return true;
          })
        : contractors;

    // Estadísticas de contratistas
    const stats = {
        total: contractors?.length || 0,
        withAssignments:
            contractors?.filter((c) => c._count.assignments > 0)?.length || 0,
        withoutAssignments:
            contractors?.filter((c) => c._count.assignments === 0)?.length || 0,
    };

    // Atajos de teclado
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // No aplicar atajos cuando se está escribiendo en un input
            if (
                e.target instanceof HTMLInputElement ||
                e.target instanceof HTMLTextAreaElement
            )
                return;

            // Ctrl+F para buscar
            if (e.ctrlKey && e.key === "f") {
                e.preventDefault();
                document.getElementById("search-contractors")?.focus();
            }

            // N para nuevo contratista
            if (e.key === "n" && !e.ctrlKey && !e.altKey && !e.metaKey) {
                e.preventDefault();
                router.push("/dashboard/contractors/new");
            }

            // R para refrescar
            if (e.key === "r" && !e.ctrlKey && !e.altKey && !e.metaKey) {
                e.preventDefault();
                refetch();
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [router, refetch]);

    // Renderizar nombre completo
    const renderFullName = (contractor: Contractor) => {
        const firstName = contractor.firstName || "";
        const middleName = contractor.middleName || "";
        const lastName = contractor.lastName || "";
        const secondLastName = contractor.secondLastName || "";

        const initials =
            `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
        const fullName = `${firstName} ${middleName}`.trim();
        const fullLastName = `${lastName} ${secondLastName}`.trim();

        return (
            <div className="flex items-center gap-3">
                <Avatar
                    isBordered
                    className="transition-transform bg-gradient-to-br from-primary-100 to-blue-100"
                    name={initials}
                    size="sm"
                />
                <div className="flex flex-col">
                    <span className="font-semibold text-foreground line-clamp-1">
                        {fullName}
                    </span>
                    <span className="text-sm text-default-500 line-clamp-1">
                        {fullLastName}
                    </span>
                </div>
            </div>
        );
    };

    // Renderizar skeleton loading
    const renderSkeleton = () => (
        <Card className="shadow-md">
            <CardBody className="p-0">
                <Table aria-label="Tabla de contratistas cargando">
                    <TableHeader>
                        <TableColumn>Nombre</TableColumn>
                        <TableColumn>Fecha de registro</TableColumn>
                        <TableColumn>Asignaciones</TableColumn>
                        <TableColumn className="text-right">
                            Acciones
                        </TableColumn>
                    </TableHeader>
                    <TableBody>
                        {Array(5)
                            .fill(0)
                            .map((_, index) => (
                                <TableRow key={`skeleton-${index}`}>
                                    <TableCell>
                                        <motion.div
                                            animate="animate"
                                            className="flex items-center gap-3"
                                            initial="initial"
                                            variants={skeletonPulse}
                                        >
                                            <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700" />
                                            <div className="flex flex-col gap-2">
                                                <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded" />
                                                <div className="h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded" />
                                            </div>
                                        </motion.div>
                                    </TableCell>
                                    <TableCell>
                                        <motion.div
                                            animate="animate"
                                            className="flex flex-col gap-1"
                                            initial="initial"
                                            variants={skeletonPulse}
                                        >
                                            <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded" />
                                            <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded" />
                                        </motion.div>
                                    </TableCell>
                                    <TableCell>
                                        <motion.div
                                            animate="animate"
                                            className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded"
                                            initial="initial"
                                            variants={skeletonPulse}
                                        />
                                    </TableCell>
                                    <TableCell className="text-right">
                                        <motion.div
                                            animate="animate"
                                            className="h-8 w-8 ml-auto bg-gray-200 dark:bg-gray-700 rounded"
                                            initial="initial"
                                            variants={skeletonPulse}
                                        />
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
            </CardBody>
        </Card>
    );

    return (
        <motion.div
            animate="visible"
            className="container mx-auto px-4 py-6"
            initial="hidden"
            variants={pageVariants}
        >
            {/* Navegación */}
            <motion.div className="mb-6" variants={itemVariants}>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <Button
                        as="a"
                        className="gap-1 text-sm font-normal"
                        href="/dashboard"
                        variant="light"
                    >
                        <HomeIcon className="h-4 w-4" />
                        Inicio
                    </Button>
                    <ChevronRightIcon className="h-3 w-3" />
                    <span className="text-gray-700 dark:text-gray-300 font-medium flex items-center">
                        <UsersIcon className="h-4 w-4 mr-1" />
                        Contratistas
                    </span>
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                            Gestión de Contratistas
                        </h1>
                        <p className="text-gray-500 dark:text-gray-400 mt-1 max-w-xl">
                            Administra y visualiza los contratistas registrados
                            en el sistema
                        </p>
                    </div>

                    <div className="flex flex-wrap gap-2 justify-end">
                        <Tooltip content="Actualizar datos (Tecla R)">
                            <Button
                                isIconOnly
                                className="min-w-unit-10 h-unit-10"
                                variant="flat"
                                onPress={() => refetch()}
                            >
                                <ArrowPathIcon
                                    className={`h-5 w-5 ${isRevalidating ? "animate-spin" : ""}`}
                                />
                            </Button>
                        </Tooltip>

                        <Tooltip content="Crear nuevo contratista (Tecla N)">
                            <Button
                                className="shadow-md hover:shadow-lg transition-all min-w-unit-28"
                                color="primary"
                                startContent={<PlusIcon className="h-5 w-5" />}
                                onPress={() =>
                                    router.push("/dashboard/contractors/new")
                                }
                            >
                                Nuevo
                            </Button>
                        </Tooltip>
                    </div>
                </div>
            </motion.div>

            {/* Estadísticas */}
            <motion.div
                animate={
                    isStatsExpanded ? { height: "auto" } : { height: "auto" }
                }
                className="mb-6"
                transition={{ duration: 0.3 }}
                variants={itemVariants}
            >
                <Card className="shadow-md bg-gradient-to-r from-gray-50 to-blue-50/50 dark:from-gray-900 dark:to-blue-900/20 overflow-hidden">
                    <CardBody className="p-4">
                        <div className="flex justify-between items-center mb-2">
                            <h3 className="text-md font-medium flex items-center gap-1">
                                <InformationCircleIcon className="h-5 w-5 text-primary" />
                                Resumen de contratistas
                            </h3>
                            <Button
                                isIconOnly
                                size="sm"
                                variant="light"
                                onPress={() =>
                                    setIsStatsExpanded(!isStatsExpanded)
                                }
                            >
                                <ChevronDownIcon
                                    className={`h-5 w-5 transform transition-transform ${isStatsExpanded ? "rotate-180" : ""}`}
                                />
                            </Button>
                        </div>

                        <AnimatePresence>
                            {isLoading ? (
                                <motion.div
                                    animate="animate"
                                    className="flex flex-row gap-4 mt-2"
                                    initial="initial"
                                    variants={skeletonPulse}
                                >
                                    {Array(3)
                                        .fill(0)
                                        .map((_, idx) => (
                                            <div
                                                key={`stat-skeleton-${idx}`}
                                                className="flex-1 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm"
                                            >
                                                <div className="h-4 w-20 mb-2 bg-gray-200 dark:bg-gray-700 rounded" />
                                                <div className="h-8 w-14 bg-gray-200 dark:bg-gray-700 rounded" />
                                            </div>
                                        ))}
                                </motion.div>
                            ) : (
                                <motion.div
                                    animate="visible"
                                    className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2"
                                    initial="hidden"
                                    variants={fadeInVariants}
                                >
                                    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                                        <div className="flex justify-between items-center">
                                            <p className="text-sm text-gray-500">
                                                Total contratistas
                                            </p>
                                            <UsersIcon className="h-5 w-5 text-primary" />
                                        </div>
                                        <p className="text-2xl font-bold mt-1">
                                            {stats.total}
                                        </p>
                                        <Button
                                            className="text-xs text-gray-500 mt-2 h-auto min-w-0 p-0"
                                            variant="light"
                                            onPress={() => {
                                                setFilterAssignments(null);
                                                updateUrl();
                                            }}
                                        >
                                            <Badge
                                                color={
                                                    filterAssignments === null
                                                        ? "primary"
                                                        : "default"
                                                }
                                                size="sm"
                                                variant={
                                                    filterAssignments === null
                                                        ? "flat"
                                                        : "flat"
                                                }
                                            >
                                                Ver todos
                                            </Badge>
                                        </Button>
                                    </div>

                                    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                                        <div className="flex justify-between items-center">
                                            <p className="text-sm text-gray-500">
                                                Con asignaciones
                                            </p>
                                            <ClipboardDocumentCheckIcon className="h-5 w-5 text-success" />
                                        </div>
                                        <p className="text-2xl font-bold mt-1">
                                            {stats.withAssignments}
                                        </p>
                                        <Button
                                            className="text-xs text-gray-500 mt-2 h-auto min-w-0 p-0"
                                            variant="light"
                                            onPress={() => {
                                                setFilterAssignments("with");
                                                updateUrl();
                                            }}
                                        >
                                            <Badge
                                                color={
                                                    filterAssignments === "with"
                                                        ? "success"
                                                        : "default"
                                                }
                                                size="sm"
                                                variant={
                                                    filterAssignments === "with"
                                                        ? "flat"
                                                        : "flat"
                                                }
                                            >
                                                {stats.withAssignments > 0
                                                    ? "Ver contratistas activos"
                                                    : "Sin contratistas activos"}
                                            </Badge>
                                        </Button>
                                    </div>

                                    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                                        <div className="flex justify-between items-center">
                                            <p className="text-sm text-gray-500">
                                                Sin asignaciones
                                            </p>
                                            <DocumentTextIcon className="h-5 w-5 text-warning" />
                                        </div>
                                        <p className="text-2xl font-bold mt-1">
                                            {stats.withoutAssignments}
                                        </p>
                                        <Button
                                            className="text-xs text-gray-500 mt-2 h-auto min-w-0 p-0"
                                            variant="light"
                                            onPress={() => {
                                                setFilterAssignments("without");
                                                updateUrl();
                                            }}
                                        >
                                            <Badge
                                                color={
                                                    filterAssignments ===
                                                    "without"
                                                        ? "warning"
                                                        : "default"
                                                }
                                                size="sm"
                                                variant={
                                                    filterAssignments ===
                                                    "without"
                                                        ? "flat"
                                                        : "flat"
                                                }
                                            >
                                                {stats.withoutAssignments > 0
                                                    ? "Ver contratistas sin asignaciones"
                                                    : "Todos tienen asignaciones"}
                                            </Badge>
                                        </Button>
                                    </div>
                                </motion.div>
                            )}
                        </AnimatePresence>

                        {/* Atajos de teclado */}
                        {isStatsExpanded && (
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                className="mt-4 border-t pt-3 text-sm text-gray-500"
                                initial={{ opacity: 0, y: -10 }}
                                transition={{ delay: 0.1 }}
                            >
                                <p className="font-medium mb-2">
                                    Atajos de teclado:
                                </p>
                                <div className="flex flex-wrap gap-3">
                                    <div className="flex items-center gap-1">
                                        <Kbd keys={["ctrl"]}>f</Kbd>
                                        <span>Buscar</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Kbd>n</Kbd>
                                        <span>Nuevo contratista</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Kbd>r</Kbd>
                                        <span>Actualizar</span>
                                    </div>
                                </div>
                            </motion.div>
                        )}
                    </CardBody>
                </Card>
            </motion.div>

            {/* Filtros y controles */}
            <motion.div className="mb-6" variants={itemVariants}>
                <Card className="shadow-md">
                    <CardBody className="p-4">
                        <div className="flex flex-col sm:flex-row gap-4 justify-between items-end">
                            <div className="w-full sm:w-64">
                                <Input
                                    isClearable
                                    classNames={{
                                        inputWrapper: "shadow-sm",
                                    }}
                                    id="search-contractors"
                                    placeholder="Buscar contratistas..."
                                    startContent={
                                        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                                    }
                                    value={searchQuery}
                                    onChange={(e) =>
                                        handleSearch(e.target.value)
                                    }
                                />
                                <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                                    <span>Presiona</span>
                                    <Kbd keys={["ctrl"]}>f</Kbd>
                                    <span>para buscar</span>
                                </div>
                            </div>
                            <div className="flex gap-2 items-center">
                                <Select
                                    aria-label="Registros por página"
                                    className="w-32"
                                    placeholder="Filas"
                                    selectedKeys={[perPage.toString()]}
                                    size="sm"
                                    onChange={(e) => {
                                        setPerPage(Number(e.target.value));
                                        setCurrentPage(1);
                                    }}
                                >
                                    <SelectItem key="5">5 filas</SelectItem>
                                    <SelectItem key="10">10 filas</SelectItem>
                                    <SelectItem key="20">20 filas</SelectItem>
                                    <SelectItem key="50">50 filas</SelectItem>
                                </Select>

                                <Tooltip content="Actualizar datos">
                                    <Button
                                        isIconOnly
                                        className="min-w-unit-9"
                                        size="sm"
                                        variant="light"
                                        onPress={() => refetch()}
                                    >
                                        <ArrowPathIcon
                                            className={`h-5 w-5 ${isRevalidating ? "animate-spin" : ""}`}
                                        />
                                    </Button>
                                </Tooltip>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Tabla de contratistas */}
            <motion.div variants={itemVariants}>
                <AnimatePresence mode="wait">
                    {isLoading ? (
                        <motion.div
                            key="skeleton"
                            animate="visible"
                            exit="hidden"
                            initial="hidden"
                            variants={fadeInVariants}
                        >
                            {renderSkeleton()}
                        </motion.div>
                    ) : isEmpty ? (
                        <motion.div
                            key="empty"
                            animate="visible"
                            exit="hidden"
                            initial="hidden"
                            variants={fadeInVariants}
                        >
                            <EmptyState
                                buttonIcon={<PlusIcon className="h-5 w-5" />}
                                buttonText="Crear Contratista"
                                description={
                                    searchQuery
                                        ? "No hay resultados que coincidan con tu búsqueda. Intenta con otros términos."
                                        : "Aún no hay contratistas registrados en el sistema. Crea uno nuevo para comenzar."
                                }
                                title="No se encontraron contratistas"
                                onAction={() =>
                                    router.push("/dashboard/contractors/new")
                                }
                            />
                        </motion.div>
                    ) : (
                        <motion.div
                            key="table"
                            animate="visible"
                            exit="hidden"
                            initial="hidden"
                            variants={fadeInVariants}
                        >
                            <Card className="shadow-md">
                                <CardBody className="p-0">
                                    <Table
                                        aria-label="Tabla de contratistas"
                                        className="min-h-[300px]"
                                        color="primary"
                                        selectionMode="none"
                                        shadow="none"
                                    >
                                        <TableHeader>
                                            <TableColumn
                                                className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                                onClick={() =>
                                                    handleSortChange(
                                                        "firstName",
                                                    )
                                                }
                                            >
                                                <div className="flex items-center gap-1">
                                                    Nombre
                                                    {orderBy === "firstName" &&
                                                        (order === "asc" ? (
                                                            <ArrowUpIcon className="h-4 w-4" />
                                                        ) : (
                                                            <ArrowDownIcon className="h-4 w-4" />
                                                        ))}
                                                </div>
                                            </TableColumn>
                                            <TableColumn
                                                className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                                onClick={() =>
                                                    handleSortChange(
                                                        "createdAt",
                                                    )
                                                }
                                            >
                                                <div className="flex items-center gap-1">
                                                    Fecha de registro
                                                    {orderBy === "createdAt" &&
                                                        (order === "asc" ? (
                                                            <ArrowUpIcon className="h-4 w-4" />
                                                        ) : (
                                                            <ArrowDownIcon className="h-4 w-4" />
                                                        ))}
                                                </div>
                                            </TableColumn>
                                            <TableColumn>
                                                Asignaciones
                                            </TableColumn>
                                            <TableColumn className="text-right">
                                                Acciones
                                            </TableColumn>
                                        </TableHeader>
                                        <TableBody
                                            emptyContent="No se encontraron contratistas"
                                            loadingContent={
                                                <Spinner color="primary" />
                                            }
                                        >
                                            {filteredContractors.map(
                                                (contractor) => (
                                                    <TableRow
                                                        key={contractor.id}
                                                        className="group hover:bg-gray-50 dark:hover:bg-gray-900/20"
                                                    >
                                                        <TableCell>
                                                            {renderFullName(
                                                                contractor,
                                                            )}
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex flex-col">
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <CalendarIcon className="h-4 w-4 text-gray-400" />
                                                                    <span>
                                                                        {format(
                                                                            new Date(
                                                                                contractor.createdAt,
                                                                            ),
                                                                            "dd MMM yyyy",
                                                                            {
                                                                                locale: es,
                                                                            },
                                                                        )}
                                                                    </span>
                                                                </div>
                                                                <span className="text-xs text-gray-500">
                                                                    {formatDistanceToNow(
                                                                        new Date(
                                                                            contractor.createdAt,
                                                                        ),
                                                                        {
                                                                            addSuffix:
                                                                                true,
                                                                            locale: es,
                                                                        },
                                                                    )}
                                                                </span>
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Chip
                                                                color={
                                                                    contractor
                                                                        ._count
                                                                        .assignments >
                                                                    0
                                                                        ? "success"
                                                                        : "default"
                                                                }
                                                                size="sm"
                                                                startContent={
                                                                    contractor
                                                                        ._count
                                                                        .assignments >
                                                                    0 ? (
                                                                        <CheckBadgeIcon className="h-3 w-3" />
                                                                    ) : (
                                                                        <DocumentTextIcon className="h-3 w-3" />
                                                                    )
                                                                }
                                                                variant={
                                                                    contractor
                                                                        ._count
                                                                        .assignments >
                                                                    0
                                                                        ? "flat"
                                                                        : "flat"
                                                                }
                                                            >
                                                                {
                                                                    contractor
                                                                        ._count
                                                                        .assignments
                                                                }{" "}
                                                                asignación
                                                                {contractor
                                                                    ._count
                                                                    .assignments !==
                                                                1
                                                                    ? "es"
                                                                    : ""}
                                                            </Chip>
                                                        </TableCell>
                                                        <TableCell className="text-right">
                                                            {isConfirmingDelete ===
                                                            contractor.id ? (
                                                                <div className="flex justify-end gap-2">
                                                                    <Button
                                                                        color="danger"
                                                                        size="sm"
                                                                        startContent={
                                                                            <TrashIcon className="h-4 w-4" />
                                                                        }
                                                                        variant="solid"
                                                                        onPress={() =>
                                                                            handleDelete(
                                                                                contractor.id,
                                                                            )
                                                                        }
                                                                    >
                                                                        Confirmar
                                                                    </Button>
                                                                    <Button
                                                                        size="sm"
                                                                        variant="flat"
                                                                        onPress={() =>
                                                                            setIsConfirmingDelete(
                                                                                null,
                                                                            )
                                                                        }
                                                                    >
                                                                        Cancelar
                                                                    </Button>
                                                                </div>
                                                            ) : (
                                                                <Dropdown>
                                                                    <DropdownTrigger>
                                                                        <Button
                                                                            isIconOnly
                                                                            size="sm"
                                                                            variant="light"
                                                                        >
                                                                            <EllipsisVerticalIcon className="h-5 w-5" />
                                                                        </Button>
                                                                    </DropdownTrigger>
                                                                    <DropdownMenu aria-label="Acciones">
                                                                        <DropdownItem
                                                                            key="view"
                                                                            startContent={
                                                                                <EyeIcon className="h-4 w-4" />
                                                                            }
                                                                            onPress={() =>
                                                                                router.push(
                                                                                    `/dashboard/contractors/${contractor.id}`,
                                                                                )
                                                                            }
                                                                        >
                                                                            Ver
                                                                            detalles
                                                                        </DropdownItem>
                                                                        <DropdownItem
                                                                            key="edit"
                                                                            startContent={
                                                                                <PencilIcon className="h-4 w-4" />
                                                                            }
                                                                            onPress={() =>
                                                                                router.push(
                                                                                    `/dashboard/contractors/${contractor.id}/edit`,
                                                                                )
                                                                            }
                                                                        >
                                                                            Editar
                                                                        </DropdownItem>
                                                                        <DropdownItem
                                                                            key="delete"
                                                                            className="text-danger"
                                                                            color="danger"
                                                                            startContent={
                                                                                <TrashIcon className="h-4 w-4" />
                                                                            }
                                                                            onPress={() =>
                                                                                setIsConfirmingDelete(
                                                                                    contractor.id,
                                                                                )
                                                                            }
                                                                        >
                                                                            Eliminar
                                                                        </DropdownItem>
                                                                    </DropdownMenu>
                                                                </Dropdown>
                                                            )}
                                                        </TableCell>
                                                    </TableRow>
                                                ),
                                            )}
                                        </TableBody>
                                    </Table>
                                </CardBody>
                            </Card>
                        </motion.div>
                    )}
                </AnimatePresence>
            </motion.div>

            {/* Paginación */}
            {!isEmpty &&
                filteredContractors.length > 0 &&
                pagination &&
                pagination.totalPages > 1 && (
                    <motion.div
                        className="flex justify-center mt-6"
                        variants={itemVariants}
                    >
                        <Pagination
                            showControls
                            showShadow
                            classNames={{
                                cursor: "bg-primary",
                            }}
                            color="primary"
                            page={currentPage}
                            total={pagination.totalPages}
                            variant="flat"
                            onChange={setCurrentPage}
                        />
                    </motion.div>
                )}

            {/* Información de paginación */}
            {!isEmpty && filteredContractors.length > 0 && pagination && (
                <motion.div
                    className="mt-4 text-center text-sm text-gray-500"
                    variants={itemVariants}
                >
                    {isPending ? (
                        <div className="inline-flex items-center gap-2">
                            <Spinner color="primary" size="sm" />
                            <span>Actualizando...</span>
                        </div>
                    ) : (
                        <span>
                            Mostrando {(currentPage - 1) * perPage + 1} a{" "}
                            {Math.min(currentPage * perPage, pagination.total)}{" "}
                            de {pagination.total} contratistas
                        </span>
                    )}
                </motion.div>
            )}
        </motion.div>
    );
}
