// Script para limpiar las notas de remisiones que contengan el texto por defecto
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function clearDefaultRemissionNotes() {
    try {
        // Buscar remisiones con el texto por defecto
        const remissionsToUpdate = await prisma.remission.findMany({
            where: {
                notes: {
                    contains: 'Remisión generada para'
                }
            }
        });

        console.log(`Encontradas ${remissionsToUpdate.length} remisiones con texto por defecto`);

        // Actualizar cada remisión
        for (const remission of remissionsToUpdate) {
            await prisma.remission.update({
                where: { id: remission.id },
                data: { notes: '' }
            });
            console.log(`Actualizada remisión ${remission.folio}`);
        }

        console.log('Proceso completado');
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

clearDefaultRemissionNotes();