// Masonry Grid Component with Framer Motion animations
"use client";

import React, { useState, useEffect, useRef, ReactNode } from "react";
import { motion, AnimatePresence } from "framer-motion";
import clsx from "clsx";

interface MasonryGridProps {
    children: ReactNode[];
    columns?: {
        xs?: number;
        sm?: number;
        md?: number;
        lg?: number;
        xl?: number;
    };
    gap?: number;
    className?: string;
}

const defaultColumns = {
    xs: 1,
    sm: 2,
    md: 2,
    lg: 3,
    xl: 3,
};

export function MasonryGrid({
    children,
    columns = defaultColumns,
    gap = 16,
    className,
}: MasonryGridProps) {
    const [columnCount, setColumnCount] = useState(1);
    const containerRef = useRef<HTMLDivElement>(null);

    // Calculate number of columns based on viewport width
    useEffect(() => {
        const calculateColumns = () => {
            const width = window.innerWidth;

            if (width < 640) {
                setColumnCount(columns.xs || defaultColumns.xs);
            } else if (width < 768) {
                setColumnCount(columns.sm || defaultColumns.sm);
            } else if (width < 1024) {
                setColumnCount(columns.md || defaultColumns.md);
            } else if (width < 1280) {
                setColumnCount(columns.lg || defaultColumns.lg);
            } else {
                setColumnCount(columns.xl || defaultColumns.xl);
            }
        };

        calculateColumns();
        window.addEventListener("resize", calculateColumns);

        return () => window.removeEventListener("resize", calculateColumns);
    }, [columns]);

    // Distribute children across columns
    const getColumns = () => {
        const cols: ReactNode[][] = Array.from(
            { length: columnCount },
            () => [],
        );

        React.Children.forEach(children, (child, index) => {
            const columnIndex = index % columnCount;

            cols[columnIndex].push(child);
        });

        return cols;
    };

    const columnVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                type: "spring",
                damping: 20,
                stiffness: 100,
            },
        },
    };

    const columnElements = getColumns();

    return (
        <div
            ref={containerRef}
            className={clsx("grid", "transition-all duration-300", className)}
            style={{
                gridTemplateColumns: `repeat(${columnCount}, 1fr)`,
                gap: `${gap}px`,
            }}
        >
            {columnElements.map((column, colIndex) => (
                <motion.div
                    key={`column-${colIndex}`}
                    animate="visible"
                    className="flex flex-col"
                    initial="hidden"
                    style={{ gap: `${gap}px` }}
                    variants={columnVariants}
                >
                    <AnimatePresence mode="popLayout">
                        {column.map((child, itemIndex) => (
                            <motion.div
                                key={`item-${colIndex}-${itemIndex}`}
                                layout
                                layoutId={`masonry-item-${colIndex}-${itemIndex}`}
                                variants={itemVariants}
                            >
                                {child}
                            </motion.div>
                        ))}
                    </AnimatePresence>
                </motion.div>
            ))}
        </div>
    );
}
