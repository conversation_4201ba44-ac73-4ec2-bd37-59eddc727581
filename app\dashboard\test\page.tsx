import { auth } from "@/lib/auth-helpers";

export default async function TestPage() {
    const session = await auth();

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-4">Test Page - Dashboard</h1>
            <div className="bg-white p-4 rounded shadow">
                <h2 className="text-lg font-semibold mb-2">Session Info:</h2>
                <pre className="bg-gray-100 p-4 rounded overflow-auto">
                    {JSON.stringify(session, null, 2)}
                </pre>
            </div>
            <div className="mt-4">
                <a
                    className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    href="/dashboard/packings/new"
                >
                    Go to Packings
                </a>
            </div>
        </div>
    );
}
