const chroma = require("chroma-js") as any;
const colorNamer = require("color-namer") as any;

/**
 * Convierte un color hexadecimal a formato RGB
 */
export function hexToRgb(hex: string): string {
    try {
        const color = chroma(hex);
        const [r, g, b] = color.rgb();

        return `rgb(${r}, ${g}, ${b})`;
    } catch (e) {
        return "rgb(0, 0, 0)";
    }
}

/**
 * Convierte un color hexadecimal a formato HSL
 */
export function hexToHsl(hex: string): string {
    try {
        const color = chroma(hex);
        const [h, s, l] = color
            .hsl()
            .map((v: any, i: any) =>
                i === 0 ? Math.round(v) : Math.round(v * 100) + "%",
            );

        return `hsl(${h}, ${s}, ${l})`;
    } catch (e) {
        return "hsl(0, 0%, 0%)";
    }
}

/**
 * Obtiene un nombre aproximado para el color
 */
export function getColorName(hex: string): string {
    try {
        // Obtenemos los nombres en español e inglés
        const names = colorNamer(hex);

        // Priorizamos nombres en español si están disponibles
        if (names.ntc && names.ntc.length > 0) {
            return names.ntc[0].name;
        }

        // Alternativa: nombres básicos
        return names.basic[0].name;
    } catch (e) {
        return "";
    }
}

/**
 * Genera variaciones de un color (tints y shades)
 */
export function generateColorVariations(
    hex: string,
    count: number = 5,
): string[] {
    try {
        const color = chroma(hex);
        const variations: string[] = [];

        // Generamos tints (más claros)
        const tints = chroma
            .scale(["white", hex])
            .mode("lab")
            .colors(Math.floor(count / 2) + 1);

        variations.push(...tints.slice(1)); // Excluimos el blanco puro

        // Incluimos el color original
        variations.push(hex);

        // Generamos shades (más oscuros)
        const shades = chroma
            .scale([hex, "black"])
            .mode("lab")
            .colors(Math.floor(count / 2) + 1);

        variations.push(...shades.slice(1)); // Excluimos el negro puro

        return variations;
    } catch (e) {
        return [hex];
    }
}

/**
 * Calcula el ratio de contraste contra un fondo
 */
export function calculateContrastRatio(
    color: string,
    background: string = "#FFFFFF",
): number {
    try {
        return chroma.contrast(color, background);
    } catch (e) {
        return 1;
    }
}

/**
 * Verifica si el contraste cumple con WCAG
 * @returns 'AAA', 'AA', 'A' o 'Fail'
 */
export function getWCAGLevel(contrastRatio: number): string {
    if (contrastRatio >= 7) return "AAA";
    if (contrastRatio >= 4.5) return "AA";
    if (contrastRatio >= 3) return "A";

    return "Fail";
}

/**
 * Verifica si un color se considera "oscuro" para determinar
 * si usar texto blanco o negro sobre él
 */
export function isColorDark(hex: string): boolean {
    try {
        return chroma(hex).luminance() < 0.5;
    } catch (e) {
        return false;
    }
}

/**
 * Aplica una simulación de daltonismo al color
 * @param type 'protanopia', 'deuteranopia', 'tritanopia', 'achromatopsia'
 */
export function simulateColorBlindness(hex: string, type: string): string {
    // Implementación básica de simulación de daltonismo
    try {
        const color = chroma(hex);
        const rgb = color.rgb();

        switch (type) {
            case "protanopia": // Deficiencia de rojo
                return chroma(simulateProtanopia(rgb)).hex();
            case "deuteranopia": // Deficiencia de verde
                return chroma(simulateDeuteranopia(rgb)).hex();
            case "tritanopia": // Deficiencia de azul
                return chroma(simulateTritanopia(rgb)).hex();
            case "achromatopsia": // Sin color (blanco y negro)
                const gray = 0.299 * rgb[0] + 0.587 * rgb[1] + 0.114 * rgb[2];

                return chroma([gray, gray, gray]).hex();
            default:
                return hex;
        }
    } catch (e) {
        return hex;
    }
}

// Funciones auxiliares para simulación de daltonismo
function simulateProtanopia(rgb: number[]): number[] {
    const [r, g, b] = rgb;

    return [
        0.567 * r + 0.433 * g + 0.0 * b,
        0.558 * r + 0.442 * g + 0.0 * b,
        0.0 * r + 0.242 * g + 0.758 * b,
    ];
}

function simulateDeuteranopia(rgb: number[]): number[] {
    const [r, g, b] = rgb;

    return [
        0.625 * r + 0.375 * g + 0.0 * b,
        0.7 * r + 0.3 * g + 0.0 * b,
        0.0 * r + 0.3 * g + 0.7 * b,
    ];
}

function simulateTritanopia(rgb: number[]): number[] {
    const [r, g, b] = rgb;

    return [
        0.95 * r + 0.05 * g + 0.0 * b,
        0.0 * r + 0.433 * g + 0.567 * b,
        0.0 * r + 0.475 * g + 0.525 * b,
    ];
}

/**
 * Sugiere colores alternativos que sean accesibles
 */
export function suggestAccessibleAlternatives(
    hex: string,
    backgroundColor: string = "#FFFFFF",
): string[] {
    try {
        const original = chroma(hex);
        const contrast = calculateContrastRatio(hex, backgroundColor);

        // Si ya es accesible, retornamos el original
        if (contrast >= 4.5) return [hex];

        // Sugerimos alternativas más oscuras o más claras
        const suggestions: string[] = [];

        // Si el color es muy claro, oscurecemos
        if (original.luminance() > 0.5) {
            for (let i = 1; i <= 5; i++) {
                const darkened = original.darken(i * 0.5).hex();

                if (calculateContrastRatio(darkened, backgroundColor) >= 4.5) {
                    suggestions.push(darkened);
                    break;
                }
            }
        }
        // Si el color es oscuro, aclaramos
        else {
            for (let i = 1; i <= 5; i++) {
                const lightened = original.brighten(i * 0.5).hex();

                if (calculateContrastRatio(lightened, backgroundColor) >= 4.5) {
                    suggestions.push(lightened);
                    break;
                }
            }
        }

        return suggestions.length ? suggestions : [hex];
    } catch (e) {
        return [hex];
    }
}
