# 👷 Contractors Feature

## 📋 Descripción
Gestión de contratistas, asignaciones de trabajo y métricas de productividad.

## 🏗️ Componentes

### ContractorList
Lista de contratistas activos.

### ContractorMetrics
Dashboard de métricas y productividad.

### ContractorAssignments
Vista de asignaciones por contratista.

## 🪝 Hooks

### useContractors
Hook principal de contratistas.
```tsx
const { contractors, metrics } = useContractors()
```

### useContractorPerformance
Métricas de rendimiento.
```tsx
const { performance } = useContractorPerformance(contractorId)
```

## 🎯 Uso

```tsx
import { ContractorList, useContractors } from '@/features/contractors'

export default function ContractorsPage() {
  const { contractors } = useContractors()
  return <ContractorList data={contractors} />
}
```