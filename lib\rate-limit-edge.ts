import type { RateLimitResult, RateLimitConfig } from "./rate-limit-types";

import { headers } from "next/headers";

/**
 * Edge-compatible rate limiter using in-memory storage
 * This version can be used in middleware and edge functions
 */
class RateLimit {
    private cache: Map<string, { count: number; resetTime: number }> =
        new Map();

    async check(
        request: Request | string,
        limit: number = 10,
        interval: string = "1m",
    ): Promise<RateLimitResult> {
        const identifier = await this.getIdentifier(request);
        const now = Date.now();
        const intervalMs = this.parseInterval(interval);
        const resetTime = now + intervalMs;

        // Get current rate limit info
        const current = this.cache.get(identifier);

        if (!current || current.resetTime < now) {
            // First request or reset time passed
            this.cache.set(identifier, { count: 1, resetTime });

            return {
                success: true,
                limit,
                remaining: limit - 1,
                reset: new Date(resetTime),
            };
        }

        if (current.count >= limit) {
            // Rate limit exceeded
            return {
                success: false,
                limit,
                remaining: 0,
                reset: new Date(current.resetTime),
            };
        }

        // Increment counter
        current.count++;
        this.cache.set(identifier, current);

        return {
            success: true,
            limit,
            remaining: limit - current.count,
            reset: new Date(current.resetTime),
        };
    }

    private async getIdentifier(request: Request | string): Promise<string> {
        if (typeof request === "string") {
            return request;
        }

        // Try to get IP from headers
        const headersList = headers();
        const forwardedFor = headersList.get("x-forwarded-for");
        const realIp = headersList.get("x-real-ip");

        const ip = forwardedFor?.split(",")[0] || realIp || "unknown";

        // Create identifier based on IP + URL path
        const url = new URL(request.url);

        return `${ip}:${url.pathname}`;
    }

    private parseInterval(interval: string): number {
        const unit = interval.slice(-1);
        const value = parseInt(interval.slice(0, -1));

        switch (unit) {
            case "s":
                return value * 1000;
            case "m":
                return value * 60 * 1000;
            case "h":
                return value * 60 * 60 * 1000;
            case "d":
                return value * 24 * 60 * 60 * 1000;
            default:
                return 60 * 1000; // default to 1 minute
        }
    }

    // Clean up old entries periodically
    cleanup(): void {
        const now = Date.now();

        for (const [key, value] of this.cache.entries()) {
            if (value.resetTime < now) {
                this.cache.delete(key);
            }
        }
    }
}

// Create singleton instance
export const rateLimit = new RateLimit();

// Clean up cache every 5 minutes (only in server environment)
if (typeof window === "undefined") {
    setInterval(
        () => {
            rateLimit.cleanup();
        },
        5 * 60 * 1000,
    );
}

// Helper function for API routes
export async function rateLimitMiddleware(
    request: Request,
    config: RateLimitConfig = {},
): Promise<Response | null> {
    const { limit = 10, interval = "1m" } = config;
    const result = await rateLimit.check(request, limit, interval);

    if (!result.success) {
        return new Response(
            JSON.stringify({
                error: "Too many requests",
                message: `Rate limit exceeded. Try again at ${result.reset.toISOString()}`,
            }),
            {
                status: 429,
                headers: {
                    "Content-Type": "application/json",
                    "X-RateLimit-Limit": limit.toString(),
                    "X-RateLimit-Remaining": result.remaining.toString(),
                    "X-RateLimit-Reset": result.reset.toISOString(),
                    "Retry-After": Math.ceil(
                        (result.reset.getTime() - Date.now()) / 1000,
                    ).toString(),
                },
            },
        );
    }

    return null;
}
