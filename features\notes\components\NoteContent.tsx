"use client";

import React from "react";

import { parseNoteContent } from "../utils/content-parser";

interface NoteContentProps {
    content: string;
    onTagClick?: (tag: string) => void;
    onMentionClick?: (mention: string) => void;
    className?: string;
}

export function NoteContent({
    content,
    onTagClick,
    onMentionClick,
    className = "",
}: NoteContentProps) {
    const { html, mentions, tags } = parseNoteContent(content);

    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
        const target = e.target as HTMLElement;

        // Check if clicked on a tag
        if (target.classList.contains("tag") && onTagClick) {
            const tag = target.getAttribute("data-tag");

            if (tag) {
                e.preventDefault();
                onTagClick(tag);
            }
        }

        // Check if clicked on a mention
        if (target.classList.contains("mention") && onMentionClick) {
            const mention = target.getAttribute("data-mention");

            if (mention) {
                e.preventDefault();
                onMentionClick(mention);
            }
        }
    };

    // Custom styles for mentions and tags
    const contentWithStyles = html
        .replace(
            /<span class="mention"/g,
            '<span class="text-primary font-medium cursor-pointer hover:underline bg-primary/10 px-1 rounded transition-colors duration-200 hover:bg-primary/20 dark:bg-primary/20 dark:hover:bg-primary/30"',
        )
        .replace(
            /<span class="tag"/g,
            '<span class="text-primary font-medium cursor-pointer bg-primary/20 hover:bg-primary/30 px-2 py-0.5 rounded-full inline-block mx-0.5 transition-all duration-200 text-sm hover:scale-105 hover:shadow-sm dark:bg-primary/30 dark:hover:bg-primary/40"',
        );

    return (
        <div
            dangerouslySetInnerHTML={{ __html: contentWithStyles }}
            className={`note-content text-foreground ${className}`}
            onClick={handleClick}
        />
    );
}
