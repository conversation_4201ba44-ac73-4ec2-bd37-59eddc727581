import { Order, OrderAssignment } from "@/features/orders/types/orders";
import { createDeprecatedGetter } from "@/shared/utils/deprecation";

import {
    OrderCustomerImpl,
    normalizeOrder,
    type ExtendedOrder,
} from "../components/OrderDetailModal/types/order-modal.types";

// Interfaz compatible con el OrderDetailModal
export interface OrderModalAssignment {
    id: string;
    contractorName: string;
    contractorAvatar?: string;
    quantity: number;
    defectAmount?: number;
    isCompleted: boolean;
    progress: number;
    // Allow additional properties from the original assignment
    [key: string]: any;
}

// Interfaz que espera el OrderDetailModal
export interface OrderForModal extends ExtendedOrder {
    customerName?: string;
    customerAvatar?: string;
    customer?: OrderCustomerImpl;
    assignments?: OrderModalAssignment[];
}

/**
 * Adapta una orden del formato de la API al formato que espera el OrderDetailModal
 */
export function adaptOrderForModal(order: Order | null): OrderForModal | null {
    if (!order) return null;

    // Normalize order structure first
    const normalized = normalizeOrder(order);

    if (!normalized) return null;

    // Create customer with deprecation support
    let customer: OrderCustomerImpl | undefined;

    if (normalized.customer) {
        customer = new OrderCustomerImpl(
            normalized.customer.id,
            normalized.customer.name,
            undefined, // no image in current schema
        );
    }

    // Adapt assignments with proper data
    const adaptedAssignments =
        normalized.assignments?.map(adaptAssignment) || [];

    // Calculate real progress
    const progressPercentage = calculateProgress(
        normalized,
        adaptedAssignments,
    );

    // Create adapted order
    const adapted: OrderForModal = {
        ...normalized,
        customer,
        customerName:
            normalized.customer?.name ||
            (normalized as any).clientName ||
            "Sin cliente",
        customerAvatar: (normalized as any).clientAvatar,
        assignments: adaptedAssignments,
        progressPercentage,

        // Computed properties
        totalQuantity: calculateTotalQuantity(normalized),
        completedQuantity: calculateCompletedQuantity(adaptedAssignments),
        pendingQuantity: 0, // Will be calculated after
    };

    // Calculate pending after we have total and completed
    adapted.pendingQuantity =
        (adapted.totalQuantity || 0) - (adapted.completedQuantity || 0);

    // Add deprecated property warning for description
    Object.defineProperty(
        adapted,
        "description",
        createDeprecatedGetter(
            "Order.description",
            "Use Order.notes for descriptions",
            "2025-07-01",
        ),
    );

    // Add deprecated property warnings for old property names
    Object.defineProperty(
        adapted,
        "OrderGarment",
        createDeprecatedGetter(
            "Order.OrderGarment",
            "Use Order.garments instead",
            "2025-07-01",
        ),
    );

    Object.defineProperty(
        adapted,
        "OrderAssignment",
        createDeprecatedGetter(
            "Order.OrderAssignment",
            "Use Order.assignments instead",
            "2025-07-01",
        ),
    );

    return adapted;
}

/**
 * Adapta una asignación del formato de la API al formato que espera el OrderDetailModal
 */
function adaptAssignment(assignment: any): OrderModalAssignment {
    // Extract data safely with fallbacks
    const quantity = assignment.quantity || 0;
    const defects = assignment.defects || 0;
    const isCompleted = assignment.isCompleted === true;

    // Calculate progress from assignment progress records if available
    let progress = 0;

    if (
        assignment.progress &&
        Array.isArray(assignment.progress) &&
        assignment.progress.length > 0
    ) {
        // Sum all completed progress
        progress = assignment.progress.reduce((sum: number, p: any) => {
            return sum + (p.completed || 0);
        }, 0);
    }

    // Keep the original assignment structure for backward compatibility
    // but also include all the raw data
    return {
        ...assignment, // Include all original data
        id: assignment.id,
        contractorName: assignment.contractor?.name || "Sin contratista",
        contractorAvatar: undefined, // Not in schema
        quantity,
        defectAmount: defects,
        isCompleted,
        progress: Math.min(progress, quantity), // Ensure progress doesn't exceed quantity
    };
}

/**
 * Calculate total order progress percentage
 */
function calculateProgress(
    order: any,
    assignments: OrderModalAssignment[],
): number {
    if (!assignments.length) return 0;

    const totalQuantity = assignments.reduce((sum, a) => sum + a.quantity, 0);

    if (totalQuantity === 0) return 0;

    const completedQuantity = assignments.reduce((sum, a) => {
        return sum + (a.isCompleted ? a.quantity : a.progress);
    }, 0);

    return Math.round((completedQuantity / totalQuantity) * 100);
}

/**
 * Calculate total quantity from garments
 */
function calculateTotalQuantity(order: any): number {
    if (!order.garments || !Array.isArray(order.garments)) return 0;

    return order.garments.reduce((total: number, garment: any) => {
        if (!garment.sizes || !Array.isArray(garment.sizes)) return total;

        const garmentTotal = garment.sizes.reduce((sum: number, size: any) => {
            return sum + (size.totalQuantity || 0);
        }, 0);

        return total + garmentTotal;
    }, 0);
}

/**
 * Calculate completed quantity from assignments
 */
function calculateCompletedQuantity(
    assignments: OrderModalAssignment[],
): number {
    return assignments.reduce((sum, a) => {
        return sum + (a.isCompleted ? a.quantity : 0);
    }, 0);
}

/**
 * Type guard to check if assignment has required fields
 */
export function isValidAssignment(
    assignment: any,
): assignment is OrderAssignment {
    return (
        assignment &&
        typeof assignment.id === "string" &&
        typeof assignment.quantity === "number"
    );
}
