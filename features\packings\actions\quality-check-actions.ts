"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";
import { auth } from "@/lib/auth-helpers";

const qualityCheckSchema = z.object({
    packingId: z.string(),
    checks: z.array(
        z.object({
            checkType: z.enum(["quantity", "quality", "packaging", "labeling"]),
            passed: z.boolean(),
            notes: z.string().optional(),
        }),
    ),
    qualityNotes: z.string().optional(),
    updateDetails: z
        .array(
            z.object({
                detailId: z.string(),
                qualityPassed: z.boolean(),
                defects: z.number().int().min(0),
                verifiedQuantity: z.number().int().min(0),
            }),
        )
        .optional(),
});

export async function performQualityCheck(
    data: z.infer<typeof qualityCheckSchema>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = qualityCheckSchema.parse(data);

        // Verificar que el packing existe
        const packing = await db.packing.findUnique({
            where: { id: validatedData.packingId },
            include: {
                details: true,
                status: true,
            },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        // Verificar que el packing está en un estado que permite control de calidad
        if (
            packing.status.name === "Entregado" ||
            packing.status.name === "Cancelado"
        ) {
            return {
                success: false,
                error: "No se puede realizar control de calidad en este estado",
            };
        }

        // Realizar la transacción
        const result = await db.$transaction(async (prisma) => {
            // Crear registros de control de calidad
            const qualityChecks = await Promise.all(
                validatedData.checks.map((check) =>
                    prisma.packingQualityCheck.create({
                        data: {
                            packingId: validatedData.packingId,
                            checkType: check.checkType,
                            passed: check.passed,
                            notes: check.notes,
                            checkedById: session.user.id,
                        },
                    }),
                ),
            );

            // Actualizar detalles si se proporcionan
            if (
                validatedData.updateDetails &&
                validatedData.updateDetails.length > 0
            ) {
                await Promise.all(
                    validatedData.updateDetails.map((detail) =>
                        prisma.packingDetail.update({
                            where: { id: detail.detailId },
                            data: {
                                qualityPassed: detail.qualityPassed,
                                defects: detail.defects,
                                verifiedQuantity: detail.verifiedQuantity,
                            },
                        }),
                    ),
                );
            }

            // Determinar si todos los checks pasaron
            const allChecksPassed = validatedData.checks.every(
                (check) => check.passed,
            );

            // Actualizar el packing con el resultado del control de calidad
            const updatedPacking = await prisma.packing.update({
                where: { id: validatedData.packingId },
                data: {
                    qualityCheckPassed: allChecksPassed,
                    qualityNotes: validatedData.qualityNotes,
                    qualityCheckAt: new Date(),
                    qualityCheckById: session.user.id,
                    verifiedById: session.user.id,
                    verifiedAt: new Date(),
                },
            });

            // Registrar en el historial
            await prisma.packingHistory.create({
                data: {
                    packingId: validatedData.packingId,
                    action: "QUALITY_CHECK",
                    metadata: {
                        performedBy: session.user.email,
                        checksPerformed: validatedData.checks.length,
                        passed: allChecksPassed,
                        failedChecks: validatedData.checks
                            .filter((c) => !c.passed)
                            .map((c) => c.checkType),
                    },
                },
            });

            // Si pasa el control de calidad, actualizar el estado
            if (allChecksPassed) {
                const readyStatus = await prisma.packingStatus.findFirst({
                    where: { name: "Listo para Entrega" },
                });

                if (readyStatus) {
                    await prisma.packing.update({
                        where: { id: validatedData.packingId },
                        data: { statusId: readyStatus.id },
                    });
                }
            }

            return {
                packing: updatedPacking,
                qualityChecks,
            };
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${validatedData.packingId}`);

        return {
            success: true,
            data: result,
            message: result.packing.qualityCheckPassed
                ? "Control de calidad completado exitosamente"
                : "Control de calidad completado con observaciones",
        };
    } catch (error) {
        console.error("Error performing quality check:", error);

        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        if (error instanceof Error) {
            return { success: false, error: error.message };
        }

        return {
            success: false,
            error: "Error al realizar control de calidad",
        };
    }
}

export async function getQualityCheckHistory(packingId: string) {
    try {
        const qualityChecks = await db.packingQualityCheck.findMany({
            where: { packingId },
            include: {
                checkedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
            orderBy: { createdAt: "desc" },
        });

        // Agrupar por fecha de verificación
        const groupedChecks = qualityChecks.reduce(
            (acc, check) => {
                const date = check.createdAt.toISOString().split("T")[0];

                if (!acc[date]) {
                    acc[date] = [];
                }
                acc[date].push(check);

                return acc;
            },
            {} as Record<string, typeof qualityChecks>,
        );

        return { success: true, data: groupedChecks };
    } catch (error) {
        console.error("Error fetching quality check history:", error);

        return {
            success: false,
            error: "Error al obtener historial de control de calidad",
        };
    }
}

export async function updateQualityStatus(
    packingId: string,
    passed: boolean,
    notes?: string,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const packing = await db.packing.update({
            where: { id: packingId },
            data: {
                qualityCheckPassed: passed,
                qualityNotes: notes,
                qualityCheckAt: new Date(),
                qualityCheckById: session.user.id,
            },
        });

        await db.packingHistory.create({
            data: {
                packingId,
                action: "QUALITY_STATUS_UPDATE",
                metadata: {
                    updatedBy: session.user.email,
                    passed,
                    notes,
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${packingId}`);

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error updating quality status:", error);

        return {
            success: false,
            error: "Error al actualizar estado de calidad",
        };
    }
}
