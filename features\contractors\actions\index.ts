"use server";

// Este archivo centraliza todas las exportaciones de las funciones asíncronas de Server Actions
// relacionadas con contratistas para mantener la compatibilidad con el código existente

// Importamos las funciones asíncronas desde los módulos específicos
import {
    getContractors,
    getContractor,
    getContractorWithDetails,
    getTopContractors,
    getContractorsForFilter,
} from "./query";
import { createContractor } from "./create";
import { updateContractor } from "./update";
import { deleteContractor } from "./delete";
import { getContractorMetrics } from "./getMetrics";
import { validateContractorName } from "./validate";

// Exportamos únicamente las funciones asíncronas para Server Actions
export {
    // Query functions
    getContractors,
    getContractor,
    getContractorWithDetails,
    getTopContractors,
    getContractorsForFilter,

    // Create functions
    createContractor,

    // Update functions
    updateContractor,

    // Delete functions
    deleteContractor,

    // Metrics functions
    getContractorMetrics,

    // Validation functions
    validateContractorName,
};
