"use client";

import React, {
    useCallback,
    useEffect,
    useMemo,
    useState,
    useTransition,
} from "react";
import { motion } from "framer-motion";
import {
    Badge,
    Button,
    Modal,
    ModalContent,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableColumn,
    TableHeader,
    TableRow,
    Tooltip,
    useDisclosure,
    SortDescriptor,
    addToast,
} from "@heroui/react";
import {
    ArrowLongLeftIcon,
    PencilSquareIcon,
    PlusIcon,
    TrashIcon,
    SwatchIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import CubeSpinner from "@/shared/components/ui/CubeSpinner";
// Importar hooks que usan server actions
import {
    useColors,
    useDeleteColor,
    Color,
} from "@/features/colors/hooks/useColor";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

import SearchControls from "./search-controls";

// Importar componente de controles de búsqueda

// Define los tipos de columna para la tabla
type ColumnKey = keyof Color | "actions" | "usage";

// Definición de columnas con mejor tipado
// Eliminamos la definición de COLUMNS ya que ahora usamos un enfoque más directo

const ColorsContent = () => {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [, startTransition] = useTransition();

    const {
        isOpen: isDeleteModalOpen,
        onOpen: onDeleteModalOpen,
        onClose: onDeleteModalClose,
    } = useDisclosure();

    const {
        isOpen: isDetailModalOpen,
        onOpen: onDetailModalOpen,
        onClose: onDetailModalClose,
    } = useDisclosure();

    const [searchTerm, setSearchTerm] = useState("");
    const [colorToDelete, setColorToDelete] = useState<Color | null>(null);
    const [selectedColor, setSelectedColor] = useState<Color | null>(null);
    const [isRefreshing, setIsRefreshing] = useState(false);

    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
        column: "name",
        direction: "ascending",
    });

    const [currentPage, setCurrentPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    // Uso de SWR para obtener colores con opciones optimizadas
    const { colors, isLoading, /* pagination, isError, error, */ mutate } =
        useColors({
            search: searchTerm,
            page: currentPage,
            perPage: rowsPerPage,
            orderBy: sortDescriptor.column?.toString() || "code",
            order: sortDescriptor.direction === "ascending" ? "asc" : "desc",
        });

    // Escuchar eventos de revalidación de colores
    const { isRevalidating } = useRevalidationListener("colors");

    // Efecto para revalidar cuando cambia isRevalidating
    useEffect(() => {
        if (isRevalidating) {
            // Cuando se recibe un evento de revalidación, actualizar la tabla
            // if (process.env.NODE_ENV !== "production") {
            //     console.log("⚡ Revalidando tabla de colores desde evento");
            // }
            mutate();
        }
    }, [isRevalidating, mutate]);

    // Hook para eliminar colores
    const { deleteColor } = useDeleteColor();

    // Añadir cálculo de estadísticas basado en los datos
    const statistics = useMemo(() => {
        const totalColors = colors?.length || 0;
        const usedColors =
            colors?.filter((color: Color) => (color._count?.garments || 0) > 0)
                .length || 0;
        const unusedColors = totalColors - usedColors;

        const usedPercentage = totalColors
            ? Math.round((usedColors / totalColors) * 100)
            : 0;
        const unusedPercentage = totalColors
            ? Math.round((unusedColors / totalColors) * 100)
            : 0;

        return {
            totalColors,
            usedColors,
            unusedColors,
            usedPercentage,
            unusedPercentage,
        };
    }, [colors]);

    useEffect(() => {
        const params = {
            search: searchParams.get("search"),
            page: searchParams.get("page"),
            perPage: searchParams.get("perPage"),
            sort: searchParams.get("sort"),
            order: searchParams.get("order"),
        };

        if (params.search) setSearchTerm(params.search);
        if (params.page) setCurrentPage(parseInt(params.page));
        if (params.perPage) setRowsPerPage(parseInt(params.perPage));
        if (params.sort) {
            setSortDescriptor({
                column: params.sort,
                direction:
                    params.order === "descending" ? "descending" : "ascending",
            });
        }
    }, [searchParams]);

    useEffect(() => {
        startTransition(() => {
            const params = new URLSearchParams();

            if (searchTerm) params.set("search", searchTerm);
            if (currentPage > 1) params.set("page", currentPage.toString());
            if (rowsPerPage !== 10)
                params.set("perPage", rowsPerPage.toString());
            if (sortDescriptor.column) {
                params.set("sort", sortDescriptor.column.toString());
                params.set("order", sortDescriptor.direction);
            }

            router.replace(`${pathname}?${params.toString()}`, {
                scroll: false,
            });
        });
    }, [
        searchTerm,
        currentPage,
        rowsPerPage,
        sortDescriptor,
        pathname,
        router,
        startTransition,
    ]);

    // Función para refrescar los datos manualmente
    const handleRefresh = async () => {
        setIsRefreshing(true);
        try {
            await mutate();
            addToast({
                title: "Datos actualizados",
                description: "La lista de colores ha sido actualizada",
                color: "success",
            });
        } catch {
            // Capturamos el error sin variable para evitar advertencias
            addToast({
                title: "Error",
                description: "No se pudo actualizar la lista de colores",
                color: "danger",
            });
        } finally {
            setIsRefreshing(false);
        }
    };

    // Función para manejar el cambio de ordenación
    const handleSortChange = (column: string) => {
        setSortDescriptor({
            column,
            direction:
                sortDescriptor.column === column
                    ? sortDescriptor.direction === "ascending"
                        ? "descending"
                        : "ascending"
                    : "ascending",
        });
    };

    // Optimizado: filtrado de colores
    const filteredItems = useMemo(() => {
        // Si colors es undefined o null, retornamos un array vacío
        if (!colors) return [];

        // Filtrar por término de búsqueda
        return searchTerm
            ? colors.filter((item: Color) =>
                  item.name.toLowerCase().includes(searchTerm.toLowerCase()),
              )
            : colors;
    }, [colors, searchTerm]);

    // Optimizado: ordenamiento de colores
    const sortedItems = useMemo(() => {
        return [...filteredItems].sort((a, b) => {
            const column = sortDescriptor.column as keyof Color;

            // Si la columna no existe en el objeto, ordenamos por nombre
            if (!a[column] || !b[column]) {
                return sortDescriptor.direction === "ascending"
                    ? a.name.localeCompare(b.name)
                    : b.name.localeCompare(a.name);
            }

            const aValue = String(a[column]);
            const bValue = String(b[column]);

            return sortDescriptor.direction === "ascending"
                ? aValue.localeCompare(bValue)
                : bValue.localeCompare(aValue);
        });
    }, [filteredItems, sortDescriptor]);

    // Optimizado: cálculos de paginación
    const { paginatedItems, pages, startIndex, endIndex } = useMemo(() => {
        const totalPages = Math.ceil(sortedItems.length / rowsPerPage);
        const start = (currentPage - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        return {
            paginatedItems: sortedItems.slice(start, end),
            pages: totalPages,
            startIndex: start,
            endIndex: end,
        };
    }, [sortedItems, currentPage, rowsPerPage]);

    // Handlers de eventos optimizados
    const handleSearch = useCallback((value: string) => {
        setSearchTerm(value);
        setCurrentPage(1); // Resetear a la primera página al buscar
    }, []);

    const handleOpenDetailModal = useCallback(
        (color: Color) => {
            setSelectedColor(color);
            onDetailModalOpen();
        },
        [onDetailModalOpen],
    );

    const handleDeleteColor = useCallback(async () => {
        if (!colorToDelete) return;

        try {
            await deleteColor(colorToDelete.id);
            onDeleteModalClose();

            addToast({
                title: "Color eliminado",
                description: `El color ${colorToDelete.name} ha sido eliminado correctamente`,
                color: "success",
            });
        } catch (error) {
            addToast({
                title: "Error eliminando color",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error desconocido",
                color: "danger",
            });
        } finally {
            setColorToDelete(null);
        }
    }, [colorToDelete, deleteColor, onDeleteModalClose]);

    const handleOpenDeleteModal = useCallback(
        (color: Color) => {
            setColorToDelete(color);
            onDeleteModalOpen();
        },
        [onDeleteModalOpen],
    );

    // Atajo de teclado para búsqueda rápida
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // Ctrl/Cmd + K para enfocar la búsqueda
            if ((e.ctrlKey || e.metaKey) && e.key === "k") {
                e.preventDefault();
                document.getElementById("searchInput")?.focus();
            }

            // Tecla N para nuevo color
            if (
                e.key === "n" &&
                !(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)
            ) {
                e.preventDefault();
                router.push("/dashboard/colors/new");
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [router]);

    // Función para generar un color aleatorio para visualización
    const getColorHex = useCallback((colorName: string) => {
        // Convertir nombre a hash simple
        let hash = 0;

        for (let i = 0; i < colorName.length; i++) {
            hash = colorName.charCodeAt(i) + ((hash << 5) - hash);
        }

        // Convertir a valor hexadecimal
        const c = (hash & 0x00ffffff)
            .toString(16)
            .toUpperCase()
            .padStart(6, "0");

        return `#${c}`;
    }, []);

    // Render cell
    const renderCell = useCallback(
        (color: Color, columnKey: ColumnKey) => {
            switch (columnKey) {
                case "name":
                    // Usar el código hexadecimal del color directamente si existe, de lo contrario usar el método getColorHex
                    const colorHex = color.hexCode || getColorHex(color.name);

                    return (
                        <div className="flex items-center justify-center gap-2">
                            <Button
                                className="font-medium px-2 py-1 rounded-md transition-colors min-w-0 flex items-center gap-2"
                                variant="light"
                                onPress={() => handleOpenDetailModal(color)}
                            >
                                <span
                                    className="w-4 h-4 rounded-full border border-gray-200 dark:border-gray-700"
                                    style={{ backgroundColor: colorHex }}
                                />
                                {color.name}
                            </Button>
                        </div>
                    );

                case "usage":
                    const count = color._count?.garments || 0;

                    return (
                        <div className="flex justify-center">
                            {count > 0 ? (
                                <Badge
                                    className="px-2 py-1"
                                    color="success"
                                    variant="flat"
                                >
                                    {count} {count === 1 ? "prenda" : "prendas"}
                                </Badge>
                            ) : (
                                <Badge
                                    className="px-2 py-1 inline-flex items-center justify-center"
                                    color="warning"
                                    variant="flat"
                                >
                                    <span>Sin uso</span>
                                </Badge>
                            )}
                        </div>
                    );

                case "createdAt":
                case "updatedAt":
                    return (
                        <div className="flex flex-col items-center justify-center">
                            <span className="text-sm text-gray-600 dark:text-gray-300">
                                {format(
                                    new Date(color[columnKey]),
                                    "dd MMM yyyy",
                                    { locale: es },
                                )}
                            </span>
                            <span className="text-xs text-gray-400 dark:text-gray-500">
                                {formatDistanceToNow(
                                    new Date(color[columnKey]),
                                    {
                                        addSuffix: true,
                                        locale: es,
                                    },
                                )}
                            </span>
                        </div>
                    );

                case "actions":
                    const isUsed = (color._count?.garments || 0) > 0;

                    return (
                        <div className="flex gap-2 justify-center">
                            <Tooltip content="Ver detalles">
                                <Button
                                    isIconOnly
                                    className="text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                                    size="sm"
                                    variant="light"
                                    onPress={() => handleOpenDetailModal(color)}
                                >
                                    <InformationCircleIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>

                            <Tooltip content="Editar">
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                                    href={`/dashboard/colors/${color.id}`}
                                    size="sm"
                                    variant="light"
                                >
                                    <PencilSquareIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color={isUsed ? "warning" : "danger"}
                                content={
                                    isUsed
                                        ? "No se puede eliminar (en uso)"
                                        : "Eliminar"
                                }
                            >
                                <Button
                                    isIconOnly
                                    className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                                    color="danger"
                                    isDisabled={isUsed}
                                    size="sm"
                                    variant="light"
                                    onPress={() =>
                                        !isUsed && handleOpenDeleteModal(color)
                                    }
                                >
                                    <TrashIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>
                        </div>
                    );

                default:
                    return color[columnKey]?.toString() || "-";
            }
        },
        [handleOpenDeleteModal, handleOpenDetailModal, getColorHex],
    );

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="max-w-7xl mx-auto p-4 sm:p-6"
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, ease: [0.22, 1, 0.36, 1] }}
        >
            {/* Breadcrumbs mejorados */}
            <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex flex-col gap-2">
                    <motion.nav
                        animate={{ opacity: 1, x: 0 }}
                        className="flex items-center space-x-1 text-sm"
                        initial={{ opacity: 0, x: -10 }}
                        transition={{ delay: 0.1, duration: 0.4 }}
                    >
                        <Link
                            className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors"
                            href="/dashboard"
                        >
                            Dashboard
                        </Link>
                        <span className="text-gray-400 dark:text-gray-600">
                            /
                        </span>
                        <span className="text-gray-900 dark:text-white font-medium">
                            Colores
                        </span>
                    </motion.nav>
                    <motion.h1
                        animate={{ opacity: 1, y: 0 }}
                        className="text-3xl font-bold bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent"
                        initial={{ opacity: 0, y: 10 }}
                        transition={{ delay: 0.2, duration: 0.4 }}
                    >
                        Gestión de Colores
                    </motion.h1>
                </div>

                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex items-center gap-2"
                    initial={{ opacity: 0, scale: 0.9 }}
                    transition={{ delay: 0.3, duration: 0.4 }}
                >
                    <Button
                        aria-label="Volver al dashboard"
                        as={Link}
                        className="relative overflow-hidden bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30"
                        href="/dashboard"
                        size="sm"
                        startContent={
                            <ArrowLongLeftIcon className="w-4 h-4 text-primary/90" />
                        }
                        variant="flat"
                    >
                        <span className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 opacity-0 hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                        <span className="relative z-10 font-medium">
                            Volver
                        </span>
                    </Button>
                </motion.div>
            </div>

            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-white dark:bg-gray-900 rounded-2xl shadow-lg dark:shadow-gray-800/30 border border-gray-100 dark:border-gray-800 overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.3, duration: 0.5 }}
            >
                {/* Header */}
                <div className="p-6 border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20">
                    <div className="flex flex-col md:flex-row justify-between gap-6">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold flex items-center gap-3 text-gray-900 dark:text-white">
                                <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                                    <SwatchIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                </div>
                                Gestión de Colores
                            </h1>
                            <p className="text-gray-500 dark:text-gray-400">
                                Administra los colores disponibles en el sistema
                            </p>
                        </div>

                        <Button
                            as={Link}
                            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:opacity-90 hover:scale-105 shadow-lg text-white dark:text-white transition-transform duration-300 relative overflow-hidden group"
                            color="primary"
                            href="/dashboard/colors/new"
                            startContent={
                                <motion.div
                                    animate={{ rotate: [0, 0] }}
                                    transition={{ duration: 0.2 }}
                                    whileHover={{ rotate: 90 }}
                                >
                                    <PlusIcon className="w-4 h-4" />
                                </motion.div>
                            }
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10 font-medium">
                                Nuevo Color
                            </span>
                        </Button>
                    </div>
                </div>

                {/* Dashboard Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6 bg-gradient-to-b from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10 rounded-t-xl border border-gray-100 dark:border-gray-800 border-b-0">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-sm">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                    Total de Colores
                                </p>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {statistics.totalColors}
                                </h3>
                            </div>
                            <div className="bg-blue-100 dark:bg-blue-800/50 p-2 rounded-lg">
                                <SwatchIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 p-4 rounded-xl border border-green-100 dark:border-green-800/30 shadow-sm">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-green-600 dark:text-green-400">
                                    Colores en Uso
                                </p>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {statistics.usedColors}
                                </h3>
                            </div>
                            <div className="bg-green-100 dark:bg-green-800/50 p-2 rounded-lg">
                                <div className="min-w-[1.25rem] h-5 text-green-600 dark:text-green-400 flex items-center justify-center text-xs font-medium">
                                    {statistics.usedPercentage}%
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 p-4 rounded-xl border border-amber-100 dark:border-amber-800/30 shadow-sm">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-amber-600 dark:text-amber-400">
                                    Colores sin Uso
                                </p>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {statistics.unusedColors}
                                </h3>
                            </div>
                            <div className="bg-amber-100 dark:bg-amber-800/50 p-2 rounded-lg">
                                <div className="min-w-[1.25rem] h-5 text-amber-600 dark:text-amber-400 flex items-center justify-center text-xs font-medium">
                                    {statistics.unusedPercentage}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Controls */}
                <SearchControls
                    handleRefresh={handleRefresh}
                    handleSearch={handleSearch}
                    isRefreshing={isRefreshing}
                    rowsPerPage={rowsPerPage}
                    searchTerm={searchTerm}
                    setCurrentPage={setCurrentPage}
                    setRowsPerPage={setRowsPerPage}
                    setSearchTerm={setSearchTerm}
                />

                {/* Table - with loading overlay */}
                <div className="relative">
                    {isLoading && !isRefreshing && (
                        <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 z-10 flex items-center justify-center">
                            <CubeSpinner text="Cargando colores..." />
                        </div>
                    )}

                    <Table
                        aria-label="Tabla de colores"
                        classNames={{
                            wrapper: "px-6 py-4 rounded-xl shadow-md",
                            base: "border-0 overflow-hidden",
                            th: "bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-700 py-3.5 first:rounded-tl-lg last:rounded-tr-lg",
                            td: "py-4 px-2 group-hover:bg-blue-50/40 dark:group-hover:bg-blue-900/20 text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-800",
                            tr: "transition-all duration-200 hover:bg-blue-50/30 dark:hover:bg-blue-900/10 hover:shadow-sm",
                        }}
                    >
                        <TableHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-t-lg">
                            <TableColumn
                                key="name"
                                allowsSorting={false}
                                className="hover:bg-blue-100/50 dark:hover:bg-blue-900/40 transition-all duration-200 py-3 group"
                            >
                                Nombre
                            </TableColumn>
                            <TableColumn
                                key="hexCode"
                                className="hover:bg-blue-100/50 dark:hover:bg-blue-900/40 transition-all duration-200 py-3 group"
                            >
                                Color
                            </TableColumn>
                            <TableColumn
                                key="createdAt"
                                allowsSorting={false}
                                className="hover:bg-blue-100/50 dark:hover:bg-blue-900/40 transition-all duration-200 py-3 group"
                            >
                                Fecha de registro
                            </TableColumn>
                            <TableColumn
                                key="garments"
                                className="hover:bg-blue-100/50 dark:hover:bg-blue-900/40 transition-all duration-200 py-3 group"
                            >
                                Prendas
                            </TableColumn>
                            <TableColumn
                                key="actions"
                                className="hover:bg-blue-100/50 dark:hover:bg-blue-900/40 transition-all duration-200 py-3 group text-right"
                            >
                                Acciones
                            </TableColumn>
                        </TableHeader>

                        <TableBody
                            emptyContent={
                                isLoading ? (
                                    <div className="h-[200px] flex items-center justify-center">
                                        <CubeSpinner text="Cargando colores..." />
                                    </div>
                                ) : (
                                    <div className="py-12 flex flex-col items-center justify-center text-center">
                                        <div className="bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full p-3 mb-4 shadow-md">
                                            <SwatchIcon className="w-8 h-8 text-primary/70" />
                                        </div>
                                        <p className="text-gray-700 dark:text-gray-300 mb-2 font-medium">
                                            No se encontraron colores
                                        </p>
                                        <p className="text-gray-500 dark:text-gray-400 mb-4 max-w-md">
                                            Puedes crear un nuevo color usando
                                            el botón de abajo
                                        </p>
                                        <Button
                                            as={Link}
                                            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:opacity-90 hover:scale-105 shadow-lg text-white dark:text-white transition-transform duration-300 relative overflow-hidden group"
                                            color="primary"
                                            href="/dashboard/colors/new"
                                            size="sm"
                                            startContent={
                                                <motion.div
                                                    animate={{ rotate: [0, 0] }}
                                                    transition={{
                                                        duration: 0.2,
                                                    }}
                                                    whileHover={{ rotate: 90 }}
                                                >
                                                    <PlusIcon className="w-4 h-4" />
                                                </motion.div>
                                            }
                                        >
                                            <span className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                            <span className="relative z-10 font-medium">
                                                Crear nuevo color
                                            </span>
                                        </Button>
                                    </div>
                                )
                            }
                            items={paginatedItems}
                            loadingContent={
                                <CubeSpinner text="Actualizando..." />
                            }
                            loadingState={isRefreshing ? "loading" : "idle"}
                        >
                            {(item) => (
                                <TableRow
                                    key={item.id}
                                    className="cursor-default group transition-colors"
                                >
                                    {(columnKey) => {
                                        // Renderizar celdas con estilos mejorados
                                        switch (columnKey) {
                                            case "name":
                                                return (
                                                    <TableCell>
                                                        <div className="flex items-center gap-2">
                                                            <div
                                                                className="w-5 h-5 rounded-full border border-gray-200 dark:border-gray-700 shadow-sm group-hover:scale-110 transition-transform duration-200"
                                                                style={{
                                                                    backgroundColor:
                                                                        item.hexCode ||
                                                                        "#E5E7EB",
                                                                }}
                                                            />
                                                            <span className="font-medium text-gray-800 dark:text-gray-200 group-hover:text-primary transition-colors duration-200">
                                                                {item.name}
                                                            </span>
                                                        </div>
                                                    </TableCell>
                                                );
                                            case "hexCode":
                                                return (
                                                    <TableCell>
                                                        <div className="flex items-center justify-center gap-2">
                                                            <div
                                                                className="w-8 h-8 rounded-md shadow-sm border border-gray-200 dark:border-gray-700 group-hover:scale-110 group-hover:rotate-3 transition-all duration-200"
                                                                style={{
                                                                    backgroundColor:
                                                                        item.hexCode ||
                                                                        "#E5E7EB",
                                                                }}
                                                            />
                                                            <span className="font-mono text-sm text-gray-600 dark:text-gray-400 group-hover:text-primary transition-colors duration-200">
                                                                {item.hexCode}
                                                            </span>
                                                        </div>
                                                    </TableCell>
                                                );
                                            case "createdAt":
                                                return (
                                                    <TableCell>
                                                        <div className="flex flex-col items-center">
                                                            <span className="text-gray-700 dark:text-gray-300">
                                                                {format(
                                                                    new Date(
                                                                        item.createdAt,
                                                                    ),
                                                                    "dd/MM/yyyy",
                                                                )}
                                                            </span>
                                                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                                                {formatDistanceToNow(
                                                                    new Date(
                                                                        item.createdAt,
                                                                    ),
                                                                    {
                                                                        locale: es,
                                                                        addSuffix:
                                                                            true,
                                                                    },
                                                                )}
                                                            </span>
                                                        </div>
                                                    </TableCell>
                                                );
                                            case "garments":
                                                return (
                                                    <TableCell>
                                                        <div className="flex items-center justify-center">
                                                            <span className="inline-flex items-center justify-center min-w-[2rem] h-6 px-2 rounded-full bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/30 dark:to-indigo-900/30 text-xs font-medium text-primary/90 group-hover:scale-110 group-hover:shadow-sm transition-all duration-200">
                                                                {item._count
                                                                    ?.garments ||
                                                                    0}
                                                            </span>
                                                        </div>
                                                    </TableCell>
                                                );
                                            case "actions":
                                                return (
                                                    <TableCell>
                                                        <div className="flex items-center justify-end gap-2">
                                                            <Tooltip content="Ver detalles">
                                                                <Button
                                                                    isIconOnly
                                                                    as={Link}
                                                                    className="bg-blue-50/50 dark:bg-blue-900/20 text-primary/80 hover:text-primary hover:scale-110 transition-all duration-200 border border-blue-100/50 dark:border-blue-800/30 shadow-sm hover:shadow-md"
                                                                    href={`/dashboard/colors/${item.id}/details`}
                                                                    size="sm"
                                                                    variant="light"
                                                                >
                                                                    <InformationCircleIcon className="h-4 w-4" />
                                                                </Button>
                                                            </Tooltip>
                                                            <Tooltip content="Editar color">
                                                                <Button
                                                                    isIconOnly
                                                                    as={Link}
                                                                    className="bg-blue-50/50 dark:bg-blue-900/20 text-primary/80 hover:text-primary hover:scale-110 transition-all duration-200 border border-blue-100/50 dark:border-blue-800/30 shadow-sm hover:shadow-md"
                                                                    href={`/dashboard/colors/${item.id}/edit`}
                                                                    size="sm"
                                                                    variant="light"
                                                                >
                                                                    <PencilSquareIcon className="h-4 w-4" />
                                                                </Button>
                                                            </Tooltip>
                                                            <Tooltip
                                                                color="danger"
                                                                content="Eliminar color"
                                                            >
                                                                <Button
                                                                    isIconOnly
                                                                    className="bg-red-50/50 dark:bg-red-900/20 text-red-500/80 hover:text-red-500 hover:scale-110 transition-all duration-200 border border-red-100/50 dark:border-red-800/30 shadow-sm hover:shadow-md"
                                                                    size="sm"
                                                                    variant="light"
                                                                    onPress={() => {
                                                                        setSelectedColor(
                                                                            item,
                                                                        );
                                                                        onDeleteModalOpen();
                                                                    }}
                                                                >
                                                                    <TrashIcon className="h-4 w-4" />
                                                                </Button>
                                                            </Tooltip>
                                                        </div>
                                                    </TableCell>
                                                );
                                            default:
                                                return (
                                                    <TableCell>
                                                        {renderCell(
                                                            item,
                                                            columnKey as ColumnKey,
                                                        )}
                                                    </TableCell>
                                                );
                                        }
                                    }}
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination with status */}
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 border-t border-gray-100 dark:border-gray-800 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                            Mostrando{" "}
                            {filteredItems.length > 0 ? startIndex + 1 : 0}-
                            {Math.min(endIndex, filteredItems.length)} de{" "}
                            {filteredItems.length} resultados
                        </span>
                    </div>

                    <Pagination
                        isCompact
                        showControls
                        classNames={{
                            item: "bg-transparent text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-transform hover:scale-110 duration-200",
                            cursor: "bg-gradient-to-r from-blue-600 to-indigo-600 text-white dark:text-white shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105",
                            prev: "bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30 text-primary/90",
                            next: "bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30 text-primary/90",
                            wrapper: "relative overflow-hidden",
                        }}
                        page={currentPage}
                        total={pages}
                        onChange={setCurrentPage}
                    />
                </div>
            </motion.div>

            {/* Delete Modal */}
            <Modal isOpen={isDeleteModalOpen} onClose={onDeleteModalClose}>
                <ModalContent className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-md text-gray-900 dark:text-gray-100 border border-blue-100/50 dark:border-blue-900/30 shadow-xl rounded-xl overflow-hidden">
                    <div className="p-6 text-center">
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
                            <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                            ¿Eliminar color permanentemente?
                        </h3>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            El color{" "}
                            <span className="font-semibold text-gray-700 dark:text-gray-300">
                                {colorToDelete?.name}
                            </span>{" "}
                            será eliminado permanentemente del sistema.
                        </p>
                    </div>
                    <div className="flex gap-3 justify-center p-6 pt-0">
                        <Button
                            className="relative overflow-hidden bg-gradient-to-r from-white via-gray-50/30 to-white dark:from-gray-900 dark:via-gray-800/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-gray-200/50 dark:border-gray-700/30 text-gray-700 dark:text-gray-300"
                            variant="flat"
                            onPress={onDeleteModalClose}
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-gray-400/0 via-gray-400/10 to-gray-400/0 opacity-0 hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10 font-medium">
                                Cancelar
                            </span>
                        </Button>
                        <Button
                            className="relative overflow-hidden bg-gradient-to-r from-red-600 to-pink-600 text-white dark:text-white shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 group"
                            color="danger"
                            onPress={handleDeleteColor}
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10 font-medium">
                                Confirmar Eliminación
                            </span>
                        </Button>
                    </div>
                </ModalContent>
            </Modal>

            {/* Detail Modal */}
            {selectedColor && (
                <Modal
                    isOpen={isDetailModalOpen}
                    size="lg"
                    onClose={onDetailModalClose}
                >
                    <ModalContent className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-md text-gray-900 dark:text-gray-100 border border-blue-100/50 dark:border-blue-900/30 shadow-xl rounded-xl overflow-hidden">
                        <div className="p-6">
                            <div className="flex items-center mb-4">
                                <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg mr-3">
                                    <SwatchIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                                    Detalles del Color: {selectedColor.name}
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 p-3 rounded-lg border border-blue-100/50 dark:border-blue-800/30 shadow-sm hover:shadow-md transition-all duration-300">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        ID
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedColor.id}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 p-3 rounded-lg border border-blue-100/50 dark:border-blue-800/30 shadow-sm hover:shadow-md transition-all duration-300">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Nombre
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <span
                                            className="w-4 h-4 rounded-full border border-gray-200 dark:border-gray-700"
                                            style={{
                                                backgroundColor:
                                                    selectedColor.hexCode ||
                                                    getColorHex(
                                                        selectedColor.name,
                                                    ),
                                            }}
                                        />
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedColor.name}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Código de color
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <div
                                            className="w-8 h-8 rounded-md border border-gray-200 dark:border-gray-700"
                                            style={{
                                                backgroundColor:
                                                    selectedColor.hexCode ||
                                                    getColorHex(
                                                        selectedColor.name,
                                                    ),
                                            }}
                                        />
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedColor.hexCode ||
                                                getColorHex(selectedColor.name)}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Creación
                                    </p>
                                    <div className="flex flex-col items-center justify-center">
                                        <span className="text-sm text-gray-600 dark:text-gray-300">
                                            {format(
                                                new Date(
                                                    selectedColor.createdAt,
                                                ),
                                                "dd MMM yyyy",
                                                { locale: es },
                                            )}
                                        </span>
                                        <span className="text-xs text-gray-400 dark:text-gray-500">
                                            {formatDistanceToNow(
                                                new Date(
                                                    selectedColor.createdAt,
                                                ),
                                                {
                                                    addSuffix: true,
                                                    locale: es,
                                                },
                                            )}
                                        </span>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700 md:col-span-2">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Vista previa
                                    </p>
                                    <div className="flex flex-col items-center gap-2 mt-2">
                                        <div
                                            className="w-32 h-16 rounded-lg shadow-md"
                                            style={{
                                                backgroundColor:
                                                    selectedColor.hexCode ||
                                                    getColorHex(
                                                        selectedColor.name,
                                                    ),
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ModalContent>
                </Modal>
            )}

            {/* Mostrar spinner de carga condicional para revalidación */}
            {(isLoading || isRevalidating) && (
                <div className="absolute top-4 right-4">
                    <CubeSpinner
                        className="text-primary"
                        text="Actualizando..."
                    />
                </div>
            )}
        </motion.div>
    );
};

export default ColorsContent;
