// Types for OrderDetailModal refactored components

import { deprecatedWarning } from "@/shared/utils/deprecation";

export interface OrderStatus {
    id: string;
    name: string;
    color: string;
    iconName?: string;
}

// Updated interface without non-existent fields
export interface OrderCustomer {
    id?: string;
    name?: string;
    image?: string;
    // Fields that will be added in the future
    // TODO: Migrate DB to include these fields - Issue #XXX
    // @deprecated These fields don't exist in current schema
}

// Implementation class with deprecation warnings
export class OrderCustomerImpl implements OrderCustomer {
    constructor(
        public id?: string,
        public name?: string,
        public image?: string,
    ) {}

    get phone(): string | undefined {
        deprecatedWarning(
            "OrderCustomer.phone",
            "This field will be available after DB migration",
            "2025-07-01",
        );

        return undefined;
    }

    get address(): string | undefined {
        deprecatedWarning(
            "OrderCustomer.address",
            "This field will be available after DB migration",
            "2025-07-01",
        );

        return undefined;
    }

    get company(): string | undefined {
        deprecatedWarning(
            "OrderCustomer.company",
            "This field will be available after DB migration",
            "2025-07-01",
        );

        return undefined;
    }
}

export interface OrderPart {
    id: string;
    code: string;
    progress?: number;
    status?: string;
}

export interface OrderAssignment {
    id: string;
    contractorName: string;
    contractorAvatar?: string;
    quantity: number;
    defectAmount?: number;
    isCompleted: boolean;
    progress: number;
}

export interface OrderGarmentSize {
    id?: string;
    sizeId: string;
    totalQuantity: number;
    usedQuantity?: number;
    size?: {
        id: string;
        code: string;
    };
}

export interface OrderGarment {
    id?: string;
    modelId: string;
    colorId: string;
    model?: {
        id: string;
        code: string;
        description: string;
    };
    color?: {
        id: string;
        name: string;
        hexCode?: string;
    };
    sizes?: OrderGarmentSize[];
}

export interface Order {
    id: string;
    orderCode?: string;
    createdAt?: string | Date;
    updatedAt?: string | Date;
    status?: OrderStatus;
    customerName?: string;
    customerAvatar?: string;
    customer?: OrderCustomer;
    receivedDate?: string | Date;
    estimatedDeliveryDate?: string | Date;
    deliveryDate?: string | Date;
    progressPercentage?: number;
    parts?: OrderPart[];
    cutOrder?: string;
    batch?: string;
    transferNumber?: string;

    // Correct property names according to Prisma schema
    garments?: OrderGarment[]; // NOT OrderGarment
    assignments?: OrderAssignment[]; // NOT OrderAssignment

    // Alternative property names from backend
    OrderAssignment?: any[]; // Temporary compatibility with backend response
    OrderGarment?: any[]; // Temporary compatibility with backend response

    _count?: {
        parts?: number;
        assignments?: number;
        notes?: number;
    };
}

// Extended Order type for internal use with all properties
export interface ExtendedOrder extends Order {
    // Additional computed properties
    totalQuantity?: number;
    completedQuantity?: number;
    pendingQuantity?: number;
    deliveryDate?: string | Date;

    // Include alternative property names for compatibility
    OrderAssignment?: any[];
}

export interface OrderModalProps {
    isOpen: boolean;
    onClose: () => void;
    order: Order | null;
    onEdit?: () => void;
    onDelete?: (orderId: string) => void;
    currentUserId?: string;
    userRole?: string;
    isLoadingOrder?: boolean;
}

// Type guard for validation
export function isValidOrder(order: any): order is Order {
    return (
        order &&
        typeof order.id === "string" &&
        (!order.garments || Array.isArray(order.garments)) &&
        (!order.assignments || Array.isArray(order.assignments))
    );
}

// Helper to ensure order has correct structure
export function normalizeOrder(order: any): Order | null {
    if (!isValidOrder(order)) {
        // REMOVED: console.error("Invalid order structure:", order);

        return null;
    }

    // Ensure correct property names
    const normalized: Order = {
        ...order,
        garments: order.garments || order.OrderGarment || [],
        assignments: order.assignments || order.OrderAssignment || [],
    };

    // Remove old property names if they exist
    delete (normalized as any).OrderGarment;
    delete (normalized as any).OrderAssignment;

    return normalized;
}
