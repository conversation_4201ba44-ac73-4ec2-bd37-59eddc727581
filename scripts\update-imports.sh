# Script para actualizar imports antiguos a la nueva estructura

# Actualizar imports de componentes UI
echo "Actualizando imports de componentes UI..."
sed -i 's|@/components/ui/|@/shared/components/ui/|g' $(find . -name "*.tsx" -o -name "*.ts")

# Actualizar imports de dashboard components
echo "Actualizando imports de dashboard..."
sed -i 's|@/components/dashboard/|@/shared/components/layout/|g' $(find . -name "*.tsx" -o -name "*.ts")

# Actualizar primitives
echo "Actualizando primitives..."
sed -i 's|@/components/primitives|@/shared/utils/primitives|g' $(find . -name "*.tsx" -o -name "*.ts")

# Actualizar theme-switch
echo "Actualizando theme-switch..."
sed -i 's|@/components/theme-switch|@/shared/components/ui/theme-switch|g' $(find . -name "*.tsx" -o -name "*.ts")

# Actualizar icons
echo "Actualizando icons..."
sed -i 's|@/components/icons|@/shared/components/ui/icons|g' $(find . -name "*.tsx" -o -name "*.ts")

echo "✅ Imports actualizados!"