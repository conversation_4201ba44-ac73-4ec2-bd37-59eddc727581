// Archivo de inicialización de la aplicación
// Este archivo debe ser importado en el layout principal o en middleware

import { ensureRolesExist } from "@/shared/lib/ensure-roles";

let initialized = false;

export async function initializeApp() {
    if (initialized) {
        // REMOVED: console.log('⚠️ Aplicación ya inicializada, saltando...');
        return;
    }

    try {
        // REMOVED: console.log('🚀 Inicializando aplicación LOHARI...');
        // REMOVED: console.log('📍 Ejecutando desde:', typeof window === 'undefined' ? 'SERVIDOR' : 'CLIENTE');

        // Solo ejecutar en el servidor
        if (typeof window !== "undefined") {
            // REMOVED: console.log('⚠️ Saltando inicialización en el cliente');
            return;
        }

        // Asegurar que los roles existan
        await ensureRolesExist();

        // Aquí se pueden agregar más inicializaciones en el futuro
        // como datos maestros, configuraciones, etc.

        initialized = true;
        // REMOVED: console.log('✅ Aplicación inicializada correctamente');
    } catch (error) {
        // REMOVED: console.error('❌ Error al inicializar la aplicación:', error);
        // No lanzar el error para no bloquear la aplicación
    }
}

// Llamar la inicialización inmediatamente
initializeApp();
