"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Chip } from "@heroui/react";
import {
    XMarkIcon,
    ClipboardDocumentListIcon,
    ChevronDownIcon,
    SparklesIcon,
    UserIcon,
    CubeIcon,
} from "@heroicons/react/24/outline";

import { Order } from "@/features/assignments/types";

// Extended Order type with additional properties
interface ExtendedOrder extends Order {
    customerName?: string;
    modelName?: string;
}

interface OrderCardProps {
    order: ExtendedOrder;
    onRemove: (id: string) => void;
}

export const OrderCard = ({ order, onRemove }: OrderCardProps) => {
    const [isExpanded, setIsExpanded] = useState(false);

    // Calculate if order has garments information
    const hasGarments = order._count?.garments && order._count.garments > 0;

    // Extract customer name from order
    const customerName = order.customerName || order.customer?.name || "";

    return (
        <motion.div
            aria-label={`Orden ${order.transferNumber}`}
            className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border border-white/50 dark:border-gray-700/50 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-blue-500/30 dark:hover:border-blue-400/30 group"
            transition={{ type: "spring", stiffness: 400, damping: 25 }}
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
        >
            {/* Gradient background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-400/10 dark:via-purple-400/10 dark:to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            {/* Main card content */}
            <div className="p-3 flex items-center justify-between relative z-10">
                <div className="flex items-center gap-3 truncate">
                    <motion.div
                        className="relative"
                        transition={{ duration: 0.5 }}
                        whileHover={{ rotate: 360 }}
                    >
                        <div className="bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl p-2 flex-shrink-0 shadow-md">
                            <ClipboardDocumentListIcon className="w-5 h-5 text-white" />
                        </div>
                        <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur opacity-25 group-hover:opacity-50 transition-opacity" />
                    </motion.div>
                    <div className="truncate">
                        <div
                            className="font-bold text-sm truncate bg-gradient-to-r from-gray-700 to-gray-900 dark:from-gray-200 dark:to-white bg-clip-text text-transparent"
                            title={order.transferNumber}
                        >
                            {order.transferNumber}
                        </div>
                        <div className="text-xs font-medium text-gray-600 dark:text-gray-400 truncate flex items-center gap-1">
                            <SparklesIcon className="w-3 h-3 text-purple-400" />
                            {order.cutOrder || "Sin orden de corte"}
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    {hasGarments && (
                        <motion.div
                            animate={{ scale: 1 }}
                            initial={{ scale: 0 }}
                            transition={{ delay: 0.1 }}
                        >
                            <Chip
                                className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-500/30 dark:border-blue-400/30"
                                size="sm"
                                startContent={<CubeIcon className="w-3 h-3" />}
                                variant="flat"
                            >
                                <span className="font-bold">
                                    {order._count?.garments || 0}
                                </span>
                            </Chip>
                        </motion.div>
                    )}

                    <Tooltip color="danger" content="Eliminar de la selección">
                        <motion.div
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                        >
                            <Button
                                isIconOnly
                                aria-label="Eliminar orden"
                                className="opacity-60 hover:opacity-100 focus:opacity-100 bg-red-500/10 hover:bg-red-500/20 text-red-500 dark:text-red-400 transition-all duration-200"
                                size="sm"
                                variant="flat"
                                onClick={() => onRemove(order.id)}
                            >
                                <XMarkIcon className="w-4 h-4" />
                            </Button>
                        </motion.div>
                    </Tooltip>

                    {hasGarments && (
                        <Tooltip
                            content={
                                isExpanded
                                    ? "Contraer detalles"
                                    : "Expandir detalles"
                            }
                        >
                            <motion.div
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                            >
                                <Button
                                    isIconOnly
                                    aria-expanded={isExpanded}
                                    aria-label="Detalles de la orden"
                                    className="opacity-60 hover:opacity-100 focus:opacity-100 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200"
                                    size="sm"
                                    variant="flat"
                                    onClick={() => setIsExpanded(!isExpanded)}
                                >
                                    <motion.div
                                        animate={{
                                            rotate: isExpanded ? 180 : 0,
                                        }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <ChevronDownIcon className="w-4 h-4" />
                                    </motion.div>
                                </Button>
                            </motion.div>
                        </Tooltip>
                    )}
                </div>
            </div>

            {/* Expandable details with enhanced styling */}
            <AnimatePresence>
                {hasGarments && isExpanded && (
                    <motion.div
                        animate={{ height: "auto", opacity: 1 }}
                        className="overflow-hidden border-t border-gray-200/50 dark:border-gray-700/50"
                        exit={{ height: 0, opacity: 0 }}
                        initial={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: "easeInOut" }}
                    >
                        <div className="p-4 bg-gradient-to-b from-gray-50/50 to-gray-100/50 dark:from-gray-800/30 dark:to-gray-900/30 backdrop-blur-sm">
                            <div className="font-bold text-xs mb-3 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent flex items-center gap-1">
                                <SparklesIcon className="w-4 h-4 text-purple-400" />
                                Información detallada
                            </div>
                            <div className="space-y-2">
                                {customerName && (
                                    <motion.div
                                        animate={{ x: 0, opacity: 1 }}
                                        className="flex items-center justify-between p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg"
                                        initial={{ x: -10, opacity: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                            <UserIcon className="w-4 h-4" />
                                            <span className="text-xs font-medium">
                                                Cliente
                                            </span>
                                        </div>
                                        <span className="text-xs font-bold text-gray-800 dark:text-gray-200">
                                            {customerName}
                                        </span>
                                    </motion.div>
                                )}
                                {order.modelName && (
                                    <motion.div
                                        animate={{ x: 0, opacity: 1 }}
                                        className="flex items-center justify-between p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg"
                                        initial={{ x: -10, opacity: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                            <CubeIcon className="w-4 h-4" />
                                            <span className="text-xs font-medium">
                                                Modelo
                                            </span>
                                        </div>
                                        <span className="text-xs font-bold text-gray-800 dark:text-gray-200">
                                            {order.modelName}
                                        </span>
                                    </motion.div>
                                )}
                                <motion.div
                                    animate={{ x: 0, opacity: 1 }}
                                    className="flex items-center justify-between p-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg"
                                    initial={{ x: -10, opacity: 0 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                                        <CubeIcon className="w-4 h-4" />
                                        <span className="text-xs font-medium">
                                            Total prendas
                                        </span>
                                    </div>
                                    <span className="text-sm font-black bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                                        {order._count?.garments || 0}
                                    </span>
                                </motion.div>
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
};
