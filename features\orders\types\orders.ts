// Tipos unificados para Orders, compartidos entre componentes y hooks
export interface OrderStatus {
    id: string;
    name: string;
    color: string;
    iconName?: string;
}

export interface OrderCustomer {
    id: string;
    name: string;
    // Future fields - DB Migration required - Issue #XXX
    // phone?: string;
    // email?: string;
    // address?: string;
    // company?: string;
}

export interface OrderModel {
    id: string;
    code: string;
    description?: string;
}

export interface OrderColor {
    id: string;
    name: string;
    code: string;
}

export interface OrderPart {
    id: string;
    code: string;
    description?: string;
    color?: OrderColor;
    model?: OrderModel;
    verified?: boolean;
}

export interface OrderNote {
    id: string;
    content: string;
    createdAt: string | Date;
    user?: {
        name: string;
        avatar?: string;
    };
    statusId?: string;
    importanceId?: string;
    status?: {
        id: string;
        name: string;
        color?: string;
        iconName?: string;
    };
    importance?: {
        id: string;
        name: string;
        color?: string;
        iconName?: string;
    };
}

export interface OrderAssignment {
    id: string;
    contractorId: string;
    quantity: number;
    contractor?: {
        name: string;
    };
}

// Definición para tallas en prenda
export interface OrderGarmentSize {
    id?: string;
    sizeId: string;
    size?: {
        id: string;
        code: string;
    };
    totalQuantity?: number;
}

// Definición para prendas
export interface OrderGarment {
    id?: string;
    modelId: string;
    colorId: string;
    model?: OrderModel;
    color?: OrderColor;
    sizes?: OrderGarmentSize[];
}

export interface Order {
    id: string;
    orderCode?: string;
    createdAt?: string | Date;
    updatedAt?: string | Date;
    receivedDate?: string | Date;
    estimatedDeliveryDate?: string | Date;
    deliveryDate?: string | Date;
    cutOrder?: string;
    transferNumber?: string;
    batch?: string;
    status?: string | OrderStatus;
    statusId?: string;
    customer?: OrderCustomer;
    subCustomer?: OrderCustomer;
    clientName?: string;
    clientAvatar?: string;
    progressPercentage?: number;
    assignments?: OrderAssignment[];
    _count?: {
        garments: number;
        parts: number;
        notes: number;
        packings: number;
    };
    parts?: OrderPart[];
    notes?: OrderNote[];
    garments?: OrderGarment[];
}

export interface OrderQueryOptions {
    statusId?: string;
    customerId?: string;
    contractorId?: string;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortDirection?: "asc" | "desc";
    orderId?: string;
}

export interface OrdersData {
    orders: Order[];
    orderStatuses: OrderStatus[];
    total: number;
    page: number;
    pages: number;
}

export interface OrderCreateData {
    customerId: string;
    subCustomerId?: string;
    statusId: string;
    receivedDate: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
    batch?: string | null;
    estimatedDeliveryDate?: string | null;
    parts?: { code: string }[];
    garments?: {
        modelId: string;
        colorId: string;
        sizes: { sizeId: string; quantity: number }[];
    }[];
    notes?: { content: string; statusId: string; importanceId: string }[];
}
