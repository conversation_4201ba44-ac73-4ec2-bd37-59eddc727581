# Client-Side Optimization Guide (Phase 3)

## Overview
This phase focuses on optimizing the client-side performance through:
1. <PERSON><PERSON> to React Query migration
2. Virtual scrolling for large lists
3. Code splitting and lazy loading
4. Bundle size optimization

## Feature Flags Setup

1. Copy environment variables:
```bash
cp features/performance-monitoring/.env.example .env.local
```

2. Enable features gradually:
```env
# Start with orders only
NEXT_PUBLIC_ENABLE_REACT_QUERY_ORDERS=true

# Enable virtual scrolling
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLL=true

# Enable lazy loading
NEXT_PUBLIC_ENABLE_LAZY_LOADING=true
```

## Migration Steps

### 1. Replace SWR Hooks

Use the hybrid hook for gradual migration:

```typescript
// Before
import useSWR from 'swr';
const { data, error, mutate } = useSWR('/api/orders', fetcher);

// After
import { useHybridQuery } from '@/features/performance-monitoring/hooks/useHybridQuery';
const { data, error, mutate } = useHybridQuery('/api/orders', fetcher);
```

### 2. Implement Virtual Scrolling

Replace large lists with virtualized components:

```typescript
// Before
<div>
  {orders.map(order => <OrderItem key={order.id} order={order} />)}
</div>

// After
import { VirtualizedList } from '@/features/performance-monitoring/components/virtualized/VirtualizedList';

<VirtualizedList
  items={orders}
  height={600}
  itemHeight={80}
  renderItem={(order) => <OrderItem order={order} />}
  threshold={100} // Enable virtual scrolling after 100 items
/>
```

### 3. Lazy Loading Components

Use dynamic imports for heavy components:

```typescript
// Before
import { OrdersTable } from '@/features/orders/components/OrdersTable';

// After
import { LazyComponents } from '@/features/performance-monitoring/utils/lazy-loading';
const { OrdersTable } = LazyComponents;
```

### 4. Code Splitting Routes

Next.js automatically code splits at the route level. For additional splitting:

```typescript
// In page.tsx
import dynamic from 'next/dynamic';

const HeavyFeature = dynamic(() => import('./HeavyFeature'), {
  loading: () => <p>Loading...</p>,
  ssr: false,
});
```

## Performance Monitoring

Track improvements using the performance dashboard:

```typescript
import { PerformanceDashboard } from '@/features/performance-monitoring/components/PerformanceDashboard';

// Add to your layout or monitoring page
<PerformanceDashboard />
```

## Migration Checklist

### Orders Module
- [ ] Enable `NEXT_PUBLIC_ENABLE_REACT_QUERY_ORDERS`
- [ ] Replace `useOrders` imports
- [ ] Test order list performance
- [ ] Implement virtual scrolling for order tables
- [ ] Verify infinite scroll functionality

### Virtual Scrolling
- [ ] Identify lists with >100 items
- [ ] Replace with `VirtualizedList`
- [ ] Test scroll performance
- [ ] Adjust `itemHeight` for optimal rendering

### Lazy Loading
- [ ] Enable `NEXT_PUBLIC_ENABLE_LAZY_LOADING`
- [ ] Identify heavy components (>50KB)
- [ ] Convert to lazy loaded components
- [ ] Monitor bundle size reduction

## Expected Improvements

- **Initial Bundle Size**: 20-30% reduction
- **Time to Interactive**: 30-40% improvement
- **List Rendering**: 60-80% faster for large datasets
- **Memory Usage**: 50-70% reduction for large lists

## Rollback Plan

If issues occur, disable feature flags:

```env
# Disable all optimizations
NEXT_PUBLIC_ENABLE_REACT_QUERY=false
NEXT_PUBLIC_ENABLE_VIRTUAL_SCROLL=false
NEXT_PUBLIC_ENABLE_LAZY_LOADING=false
```

All hooks will automatically fall back to SWR.
