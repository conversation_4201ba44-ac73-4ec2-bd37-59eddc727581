# 📚 INSTRUCCIONES DE INSTALACIÓN - Memory Bank System

## 🚀 INSTALACIÓN COMPLETA PASO A PASO

### ✅ Paso 1: Estructura de Archivos (YA CREADA)
La estructura base ya está creada en tu proyecto lohari:
```
lohari/.cursor/
├── rules/isolation_rules/
│   ├── main.mdc              ✅ Archivo principal
│   ├── core/                 ✅ Directorio para reglas centrales
│   ├── modes/                ✅ Directorio para reglas por modo
│   └── visual-maps/          ✅ Directorio para mapas visuales
└── custom_modes/             ✅ Directorio para instrucciones
```

### 📋 Paso 2: Configurar los 6 Modos Personalizados en Cursor

**IMPORTANTE**: Esta es la parte más crítica. Debes crear 6 modos personalizados en Cursor.

#### 🔧 Cómo Agregar un Modo Personalizado:

1. **Abre Cursor** y haz clic en el selector de modo en el panel de chat
2. **Selecciona "Add custom mode"**
3. **Configura cada modo** según las siguientes especificaciones:

### 🔍 MODO 1: VAN (Initialization)
- **Name**: `🔍 VAN`
- **Icon**: Elige el ícono de lupa o similar
- **Shortcut**: `van` (opcional)
- **Tools**: Habilita:
  - ✅ Codebase Search
  - ✅ Read File
  - ✅ Terminal
  - ✅ List Directory
- **Advanced options**: Pega el contenido del archivo `custom_modes/van_instructions.md`

### 📋 MODO 2: PLAN (Task Planning)
- **Name**: `📋 PLAN`
- **Icon**: Elige el ícono de lista o similar
- **Shortcut**: `plan` (opcional)
- **Tools**: Habilita:
  - ✅ Codebase Search
  - ✅ Read File
  - ✅ Terminal
  - ✅ List Directory
- **Advanced options**: Pega el contenido del archivo `custom_modes/plan_instructions.md`

### 🎨 MODO 3: CREATIVE (Design Decisions)
- **Name**: `🎨 CREATIVE`
- **Icon**: Elige el ícono de paleta o similar
- **Shortcut**: `creative` (opcional)
- **Tools**: Habilita:
  - ✅ Codebase Search
  - ✅ Read File
  - ✅ Terminal
  - ✅ List Directory
  - ✅ Edit File
- **Advanced options**: Pega el contenido del archivo `custom_modes/creative_instructions.md`

### ⚒️ MODO 4: IMPLEMENT (Code Implementation)
- **Name**: `⚒️ IMPLEMENT`
- **Icon**: Elige el ícono de martillo o similar
- **Shortcut**: `impl` (opcional)
- **Tools**: Habilita TODAS las herramientas
- **Advanced options**: Pega el contenido del archivo `custom_modes/implement_instructions.md`

### 🔍 MODO 5: REFLECT (Review)
- **Name**: `🔍 REFLECT`
- **Icon**: Elige el ícono de ojo o similar
- **Shortcut**: `reflect` (opcional)
- **Tools**: Habilita:
  - ✅ Codebase Search
  - ✅ Read File
  - ✅ Terminal
  - ✅ List Directory
- **Advanced options**: Pega la sección REFLECT del archivo `custom_modes/reflect_archive_instructions.md`

### 📚 MODO 6: ARCHIVE (Documentation)
- **Name**: `📚 ARCHIVE`
- **Icon**: Elige el ícono de libro o similar
- **Shortcut**: `archive` (opcional)
- **Tools**: Habilita:
  - ✅ Codebase Search
  - ✅ Read File
  - ✅ Terminal
  - ✅ List Directory
  - ✅ Edit File
- **Advanced options**: Pega la sección ARCHIVE del archivo `custom_modes/reflect_archive_instructions.md`

## 🎮 Paso 3: Crear los Memory Bank Files

Ejecuta estos comandos en la terminal desde la raíz de tu proyecto:

```bash
# Crear directorio memory-bank
mkdir -p memory-bank

# Crear archivos iniciales
echo "# Tasks" > memory-bank/tasks.md
echo "# Active Context" > memory-bank/activeContext.md
echo "# Progress" > memory-bank/progress.md
```

## 🚦 Paso 4: Uso Básico del Sistema

### 1️⃣ Iniciar con VAN Mode
- Cambia a VAN mode en Cursor
- Escribe: `VAN`
- El sistema analizará tu proyecto y determinará la complejidad

### 2️⃣ Seguir el Workflow según Complejidad
- **Level 1**: VAN → IMPLEMENT → REFLECT
- **Level 2**: VAN → PLAN → IMPLEMENT → REFLECT
- **Level 3-4**: VAN → PLAN → CREATIVE → IMPLEMENT → REFLECT → ARCHIVE

### 3️⃣ QA en Cualquier Momento
- Desde cualquier modo, escribe: `QA` para validación técnica

## 📝 Comandos Principales

```
VAN - Inicializar proyecto y determinar complejidad
PLAN - Crear plan de implementación detallado
CREATIVE - Explorar opciones de diseño para componentes complejos
IMPLEMENT - Construir sistemáticamente los componentes planeados
REFLECT - Revisar y documentar lecciones aprendidas
ARCHIVE - Crear documentación comprensiva
QA - Validar implementación técnica (desde cualquier modo)
```

## ⚙️ Paso 5: Verificación de Instalación

Para verificar que todo está correctamente instalado:

1. **Verifica los modos**: Deberías ver los 6 modos en el selector de Cursor
2. **Prueba VAN**: Cambia a VAN mode y escribe `VAN`
3. **Verifica Memory Bank**: Comprueba que se creó el directorio `memory-bank/`

## 🆘 Solución de Problemas

### "El modo no responde correctamente"
- Verifica que copiaste las instrucciones completas en "Advanced options"
- Asegúrate de que las herramientas correctas están habilitadas
- Verifica que cambiaste al modo correcto antes de ejecutar comandos

### "No se cargan las reglas"
- Verifica que el directorio `.cursor/rules/isolation_rules/` existe
- Comprueba que los archivos tienen la extensión `.mdc`
- Reinicia Cursor si es necesario

### "Error al ejecutar comandos"
- Asegúrate de estar en el directorio correcto del proyecto
- Verifica que los comandos son compatibles con tu sistema operativo

## 📚 Documentación Adicional

- **README Principal**: `.cursor/rules/README.md`
- **Notas de Versión**: `.cursor/RELEASE_NOTES.md`
- **Guía de Optimizaciones**: `.cursor/MEMORY_BANK_OPTIMIZATIONS.md`

## 🎯 ¡Listo para Usar!

Una vez completados estos pasos, el sistema Memory Bank estará completamente funcional en tu proyecto lohari.

**Siguiente paso recomendado**: Inicia con VAN mode para tu primera tarea.
