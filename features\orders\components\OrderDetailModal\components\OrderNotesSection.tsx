"use client";

import type { ExtendedOrder } from "../types/order-modal.types";

import React, { useEffect, useState } from "react";
import { Card, CardBody } from "@heroui/react";
import { DocumentTextIcon } from "@heroicons/react/24/outline";

import { OrderNotesTab } from "@/features/notes/components/OrderNotesTab";
import { getNoteImportances } from "@/features/notes/actions/importances";
import { getNoteStatuses } from "@/features/notes/actions/statuses";

interface OrderNotesSectionProps {
    order: ExtendedOrder;
    currentUserId?: string;
    userRole?: string;
}

export default function OrderNotesSection({
    order,
    currentUserId,
    userRole,
}: OrderNotesSectionProps) {
    const [importanceOptions, setImportanceOptions] = useState<any[]>([]);
    const [statusOptions, setStatusOptions] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const loadOptions = async () => {
            try {
                const [importancesResult, statusesResult] = await Promise.all([
                    getNoteImportances(),
                    getNoteStatuses(),
                ]);

                if (importancesResult.success && importancesResult.data) {
                    setImportanceOptions(importancesResult.data);
                }

                if (statusesResult.success && statusesResult.data) {
                    setStatusOptions(statusesResult.data);
                }
            } catch (error) {
                console.error("Error loading note options:", error);
            } finally {
                setIsLoading(false);
            }
        };

        loadOptions();
    }, []);

    if (isLoading) {
        return (
            <Card className="border border-default-200">
                <CardBody className="p-8 text-center">
                    <DocumentTextIcon className="w-12 h-12 mx-auto mb-4 text-default-300 animate-pulse" />
                    <p className="text-default-500">Cargando notas...</p>
                </CardBody>
            </Card>
        );
    }

    return (
        <OrderNotesTab
            authorId={currentUserId}
            importanceOptions={importanceOptions}
            orderId={order.id}
            statusOptions={statusOptions}
            userRole={userRole}
        />
    );
}
