// Interfaces para las respuestas de acciones
export interface ActionResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}

// Definiciones para las órdenes
export interface OrderFilters {
    search?: string;
    statusId?: string;
    customerId?: string;
    orderBy?: string;
    order?: "asc" | "desc";
    page?: number;
    perPage?: number;
}

export interface OrderFormDataResponse {
    customers: any[];
    statuses: any[];
    models: any[];
    colors: any[];
    sizes: any[];
    noteStatuses: any[];
    importanceLevels: any[];
}

export interface OrderSizeInput {
    sizeId: string;
    quantity: number;
}

export interface OrderGarmentInput {
    modelId: string;
    colorId: string;
    sizes: OrderSizeInput[];
}

export interface OrderNoteInput {
    content: string;
    statusId: string;
    importanceId: string;
}

export interface OrderCreateInput {
    customerId: string;
    statusId: string;
    receivedDate: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
    batch?: string | null;
    estimatedDeliveryDate?: string | null;
    garments?: OrderGarmentInput[];
    notes?: OrderNoteInput[];
}

export interface OrderUpdateInput extends OrderCreateInput {
    id: string;
}
