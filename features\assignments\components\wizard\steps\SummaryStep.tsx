"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    <PERSON>ton,
    Card,
    CardBody,
    Chip,
    Accordion,
    AccordionItem,
    Badge,
    Avatar,
} from "@heroui/react";
import {
    CheckIcon,
    ExclamationTriangleIcon,
    DocumentDuplicateIcon,
    TagIcon,
    CubeIcon,
    SparklesIcon,
    TruckIcon,
    RocketLaunchIcon,
    ClipboardDocumentCheckIcon,
    ClipboardDocumentListIcon,
    ChartPieIcon,
    CurrencyDollarIcon,
    ClockIcon,
    ArrowRightIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import useSWR from "swr";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";
import { createAssignments } from "@/features/assignments/actions";
import { formatQuantity } from "@/shared/utils/formatters";
import { getOrderBasicData } from "@/features/orders/actions";
import {
    ContextualTooltip,
    tooltipContexts,
} from "@/features/assignments/components/wizard/ui/ContextualTooltip";

import { AssignmentPreview } from "../ui/AssignmentPreview";

interface OrderWithCutOrder {
    orderId: string;
    cutOrder: string;
    parts: {
        id: string;
        code: string;
        createdAt: string;
        updatedAt: string;
    }[];
}

type OrderData = OrderWithCutOrder;

export const SummaryStep = ({ onSuccess }: SummaryStepProps) => {
    const { state, dispatch } = useWizard();
    const router = useRouter();

    const contractorId = state.contractor || "";
    const assignments = state.assignments || [];
    const isSubmitting = state.isSubmitting || false;

    const [error, setError] = useState<string | null>(null);
    const [result, setResult] = useState<{ success: boolean } | null>(null);

    const uniqueOrderIds = useMemo(
        () => Array.from(new Set(assignments.map((a) => a.orderId))),
        [assignments],
    );

    const multipleFetcher = async (keys: string[]) => {
        const orderIds = keys.map((key) => key.replace("order-", ""));
        const results: Record<string, { cutOrder: string; parts: any[] }> = {};

        await Promise.all(
            orderIds.map(async (orderId) => {
                try {
                    const response = await getOrderBasicData(orderId);

                    if (response.success && response.data) {
                        results[orderId] = {
                            cutOrder: response.data.cutOrder || "",
                            parts: response.data.parts || [],
                        };
                    }
                } catch (err) {
                    console.error(`Error fetching order ${orderId}:`, err);
                }
            }),
        );

        return results;
    };

    const orderKeys = uniqueOrderIds.map((id) => `order-${id}`);
    const { data: ordersData, error: ordersError } = useSWR(
        orderKeys.length > 0 ? orderKeys : null,
        multipleFetcher,
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        },
    );

    const {
        data: contractorData,
        error: contractorError,
        isLoading: contractorLoading,
    } = useSWR(
        contractorId ? `/api/contractors/${contractorId}` : null,
        () =>
            fetch(`/api/contractors/${contractorId}`).then((res) => res.json()),
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        },
    );

    const contractorInfo = useMemo(() => {
        if (!contractorData) return null;

        return {
            ...contractorData,
            activeAssignments: contractorData.assignments?.filter(
                (a: any) => !a.isCompleted,
            ).length,
            completedAssignments: contractorData.assignments?.filter(
                (a: any) => a.isCompleted,
            ).length,
        };
    }, [contractorData]);

    const assignmentsByOrder = useMemo(() => {
        const grouped: Record<string, OrderData & { items: any[] }> = {};

        assignments.forEach((assignment) => {
            if (!grouped[assignment.orderId]) {
                const orderData = ordersData?.[assignment.orderId];

                grouped[assignment.orderId] = {
                    orderId: assignment.orderId,
                    cutOrder: orderData?.cutOrder || "",
                    parts: orderData?.parts || [],
                    items: [],
                };
            }
            grouped[assignment.orderId].items.push(assignment);
        });

        return grouped;
    }, [assignments, ordersData]);

    const handleSubmit = useCallback(async () => {
        setError(null);
        dispatch({ type: "SET_IS_SUBMITTING", payload: true });

        try {
            const result = await createAssignments({
                contractorId,
                assignments: assignments.map((a) => ({
                    orderId: a.orderId,
                    garmentSizeId: a.garmentSizeId,
                    quantity: a.quantity,
                })),
            });

            if (!result.success) {
                setError(result.error || "Error al crear las asignaciones");

                return;
            }

            setResult({ success: true });

            if (onSuccess) {
                onSuccess(result.data);
            }

            if (
                (result.data as any)?.assignmentIds &&
                (result.data as any).assignmentIds.length > 0
            ) {
                const assignmentIdsParam = (
                    result.data as any
                ).assignmentIds.join(",");
                const redirectUrl = `/dashboard/remissions/create?assignments=${assignmentIdsParam}&contractor=${contractorId}`;

                setTimeout(() => {
                    router.push(redirectUrl);
                }, 1500);
            }
        } catch (error) {
            setError("Error al procesar la solicitud");
            console.error("Error al crear asignaciones:", error);
        } finally {
            dispatch({ type: "SET_IS_SUBMITTING", payload: false });
        }
    }, [contractorId, assignments, dispatch, onSuccess, router]);

    const stats = useMemo(() => {
        const totalQuantity = assignments.reduce(
            (sum, a) => sum + a.quantity,
            0,
        );
        const uniqueOrders = new Set(assignments.map((a) => a.orderId)).size;
        const uniqueModels = new Set(assignments.map((a) => a.modelCode)).size;
        const uniqueSizes = new Set(assignments.map((a) => a.sizeCode)).size;

        return {
            totalQuantity,
            uniqueOrders,
            uniqueModels,
            uniqueSizes,
        };
    }, [assignments]);

    const getOrderTitleWithParts = (orderId: string): string => {
        const orderData = assignmentsByOrder[orderId];

        if (!orderData || !orderData.cutOrder) {
            return `Orden ${orderId}`;
        }

        const partsText =
            orderData.parts.length > 0
                ? ` - Partes: ${orderData.parts.map((p) => p.code).join(", ")}`
                : "";

        return `Orden de Corte ${orderData.cutOrder}${partsText}`;
    };

    return (
        <motion.div
            animate={{ opacity: 1 }}
            className="space-y-6 max-w-5xl mx-auto"
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
        >
            {/* Header Card */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
            >
                <Card className="bg-gradient-to-br from-white via-green-50/50 to-emerald-50/30 dark:from-gray-900 dark:via-green-950/50 dark:to-emerald-950/30 shadow-xl border-0 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-600/5" />
                    <CardBody className="relative p-8">
                        <div className="flex items-center gap-4 mb-6">
                            <motion.div
                                className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg"
                                transition={{ type: "spring", stiffness: 300 }}
                                whileHover={{ scale: 1.05, rotate: 5 }}
                            >
                                <ClipboardDocumentCheckIcon className="w-7 h-7 text-white" />
                            </motion.div>
                            <div className="flex-1">
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                                    Resumen y Confirmación
                                </h2>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-1.5">
                                    <SparklesIcon className="w-4 h-4 text-green-500" />
                                    Revisa todos los detalles antes de confirmar
                                    las asignaciones
                                </p>
                            </div>
                            <ContextualTooltip
                                content={tooltipContexts.summary.text}
                                variant={tooltipContexts.summary.variant}
                            />
                        </div>

                        {/* Quick Actions */}
                        <div className="flex items-center gap-3">
                            <Chip
                                color="success"
                                startContent={<CheckIcon className="w-4 h-4" />}
                                variant="flat"
                            >
                                Todo listo para confirmar
                            </Chip>
                            <Chip
                                color="default"
                                startContent={<TruckIcon className="w-4 h-4" />}
                                variant="flat"
                            >
                                Generar remisión después
                            </Chip>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Error/Success Messages */}
            <AnimatePresence mode="wait">
                {error && (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        initial={{ opacity: 0, y: -10 }}
                    >
                        <Card className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 border-0 shadow-lg">
                            <CardBody className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-red-100 dark:bg-red-900/50 rounded-xl">
                                        <ExclamationTriangleIcon className="w-6 h-6 text-red-600 dark:text-red-400" />
                                    </div>
                                    <p className="text-red-700 dark:text-red-300 font-medium">
                                        {error}
                                    </p>
                                </div>
                            </CardBody>
                        </Card>
                    </motion.div>
                )}

                {result?.success && !error && (
                    <motion.div
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        initial={{ opacity: 0, scale: 0.95 }}
                    >
                        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-0 shadow-lg">
                            <CardBody className="p-6">
                                <div className="flex items-center gap-4">
                                    <motion.div
                                        animate={{ rotate: [0, 360] }}
                                        className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl"
                                        transition={{ duration: 0.5 }}
                                    >
                                        <CheckIcon className="w-6 h-6 text-white" />
                                    </motion.div>
                                    <div className="flex-1">
                                        <p className="font-bold text-green-700 dark:text-green-300 text-lg">
                                            ¡Asignaciones creadas exitosamente!
                                        </p>
                                        <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                                            Se han creado {assignments.length}{" "}
                                            asignaciones. Redirigiendo a
                                            remisión...
                                        </p>
                                    </div>
                                    <motion.div
                                        animate={{ x: [0, 10, 0] }}
                                        transition={{
                                            repeat: Infinity,
                                            duration: 1.5,
                                        }}
                                    >
                                        <ArrowRightIcon className="w-6 h-6 text-green-600" />
                                    </motion.div>
                                </div>
                            </CardBody>
                        </Card>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Contractor Info Enhanced */}
            {contractorInfo && (
                <motion.div
                    animate={{ y: 0, opacity: 1 }}
                    initial={{ y: 20, opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                >
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-600/5" />
                        <CardBody className="relative p-6">
                            <div className="flex items-center gap-6">
                                <Avatar
                                    className="ring-4 ring-blue-100 dark:ring-blue-900"
                                    classNames={{
                                        base: "bg-gradient-to-br from-blue-500 to-purple-600",
                                    }}
                                    name={state.contractorData?.name || ""}
                                    size="lg"
                                />
                                <div className="flex-1">
                                    <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100">
                                        {state.contractorData?.name}
                                    </h3>
                                    <div className="flex items-center gap-4 mt-2">
                                        <Chip
                                            color="primary"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {contractorInfo.activeAssignments ||
                                                0}{" "}
                                            activas
                                        </Chip>
                                        <Chip
                                            color="success"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {contractorInfo.completedAssignments ||
                                                0}{" "}
                                            completadas
                                        </Chip>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Tiempo promedio
                                    </p>
                                    <p className="text-lg font-bold text-gray-800 dark:text-gray-100">
                                        4 días
                                    </p>
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            )}

            {/* Enhanced Statistics Grid */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-4"
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
            >
                <motion.div
                    transition={{ type: "spring", stiffness: 400 }}
                    whileHover={{ scale: 1.02 }}
                >
                    <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-0 shadow-lg">
                        <CardBody className="p-6">
                            <div className="flex items-center justify-between mb-3">
                                <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-xl">
                                    <CubeIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <ChartPieIcon className="w-4 h-4 text-blue-400" />
                            </div>
                            <p className="text-3xl font-bold text-gray-800 dark:text-gray-100">
                                {formatQuantity(stats.totalQuantity)}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                Total Prendas
                            </p>
                            <div className="mt-2 h-1 bg-blue-200 dark:bg-blue-800 rounded-full overflow-hidden">
                                <motion.div
                                    animate={{ width: "100%" }}
                                    className="h-full bg-blue-600"
                                    initial={{ width: 0 }}
                                    transition={{ duration: 1, delay: 0.5 }}
                                />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>

                <motion.div
                    transition={{ type: "spring", stiffness: 400 }}
                    whileHover={{ scale: 1.02 }}
                >
                    <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/30 dark:to-pink-950/30 border-0 shadow-lg">
                        <CardBody className="p-6">
                            <div className="flex items-center justify-between mb-3">
                                <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-xl">
                                    <DocumentDuplicateIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                </div>
                                <Badge color="secondary" size="sm">
                                    {stats.uniqueOrders}
                                </Badge>
                            </div>
                            <p className="text-3xl font-bold text-gray-800 dark:text-gray-100">
                                {stats.uniqueOrders}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {stats.uniqueOrders === 1 ? "Orden" : "Órdenes"}
                            </p>
                            <div className="mt-2 h-1 bg-purple-200 dark:bg-purple-800 rounded-full overflow-hidden">
                                <motion.div
                                    animate={{
                                        width: `${(stats.uniqueOrders / 10) * 100}%`,
                                    }}
                                    className="h-full bg-purple-600"
                                    initial={{ width: 0 }}
                                    transition={{ duration: 1, delay: 0.6 }}
                                />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>

                <motion.div
                    transition={{ type: "spring", stiffness: 400 }}
                    whileHover={{ scale: 1.02 }}
                >
                    <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-0 shadow-lg">
                        <CardBody className="p-6">
                            <div className="flex items-center justify-between mb-3">
                                <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-xl">
                                    <TagIcon className="w-5 h-5 text-green-600 dark:text-green-400" />
                                </div>
                                <SparklesIcon className="w-4 h-4 text-green-400" />
                            </div>
                            <p className="text-3xl font-bold text-gray-800 dark:text-gray-100">
                                {stats.uniqueModels}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {stats.uniqueModels === 1
                                    ? "Modelo"
                                    : "Modelos"}
                            </p>
                            <div className="mt-2 h-1 bg-green-200 dark:bg-green-800 rounded-full overflow-hidden">
                                <motion.div
                                    animate={{
                                        width: `${(stats.uniqueModels / 10) * 100}%`,
                                    }}
                                    className="h-full bg-green-600"
                                    initial={{ width: 0 }}
                                    transition={{ duration: 1, delay: 0.7 }}
                                />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>

                <motion.div
                    transition={{ type: "spring", stiffness: 400 }}
                    whileHover={{ scale: 1.02 }}
                >
                    <Card className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 border-0 shadow-lg">
                        <CardBody className="p-6">
                            <div className="flex items-center justify-between mb-3">
                                <div className="p-2 bg-amber-100 dark:bg-amber-900/50 rounded-xl">
                                    <CubeIcon className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                                </div>
                                <ClockIcon className="w-4 h-4 text-amber-400" />
                            </div>
                            <p className="text-3xl font-bold text-gray-800 dark:text-gray-100">
                                {stats.uniqueSizes}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {stats.uniqueSizes === 1 ? "Talla" : "Tallas"}
                            </p>
                            <div className="mt-2 h-1 bg-amber-200 dark:bg-amber-800 rounded-full overflow-hidden">
                                <motion.div
                                    animate={{
                                        width: `${(stats.uniqueSizes / 10) * 100}%`,
                                    }}
                                    className="h-full bg-amber-600"
                                    initial={{ width: 0 }}
                                    transition={{ duration: 1, delay: 0.8 }}
                                />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            </motion.div>

            {/* Assignments Accordion Enhanced */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
            >
                <Card className="shadow-xl border-0">
                    <CardBody className="p-8">
                        <div className="flex items-center justify-between mb-6">
                            <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center gap-3">
                                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                                    <ClipboardDocumentListIcon className="w-5 h-5 text-white" />
                                </div>
                                Detalle de Asignaciones
                            </h3>
                            <Badge
                                color="primary"
                                content={assignments.length.toString()}
                                size="lg"
                            >
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                    Items totales
                                </span>
                            </Badge>
                        </div>

                        <Accordion
                            className="gap-4"
                            itemClasses={{
                                base: "shadow-md border-0 data-[hover=true]:shadow-lg transition-shadow",
                                title: "font-semibold text-gray-800 dark:text-gray-100",
                                content: "px-4 pb-4",
                            }}
                            variant="splitted"
                        >
                            {Object.values(assignmentsByOrder).map(
                                (orderGroup, orderIndex) => {
                                    const orderTotal = orderGroup.items.reduce(
                                        (sum, item) => sum + item.quantity,
                                        0,
                                    );

                                    return (
                                        <AccordionItem
                                            key={orderGroup.orderId}
                                            aria-label={`Orden ${orderGroup.orderId}`}
                                            className="bg-white dark:bg-gray-800"
                                            startContent={
                                                <div className="p-2 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-lg">
                                                    <DocumentDuplicateIcon className="w-5 h-5 text-purple-700 dark:text-purple-300" />
                                                </div>
                                            }
                                            title={
                                                <div className="flex items-center justify-between flex-1">
                                                    <span className="text-base">
                                                        {getOrderTitleWithParts(
                                                            orderGroup.orderId,
                                                        )}
                                                    </span>
                                                    <div className="flex items-center gap-3">
                                                        <Chip
                                                            color="primary"
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            {
                                                                orderGroup.items
                                                                    .length
                                                            }{" "}
                                                            items
                                                        </Chip>
                                                        <Chip
                                                            color="success"
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            {orderTotal} prendas
                                                        </Chip>
                                                    </div>
                                                </div>
                                            }
                                        >
                                            <div className="space-y-3 pt-4">
                                                {orderGroup.items.map(
                                                    (assignment, index) => (
                                                        <motion.div
                                                            key={`${assignment.orderId}-${assignment.garmentSizeId}`}
                                                            animate={{
                                                                opacity: 1,
                                                                x: 0,
                                                            }}
                                                            initial={{
                                                                opacity: 0,
                                                                x: -20,
                                                            }}
                                                            transition={{
                                                                delay:
                                                                    orderIndex *
                                                                        0.1 +
                                                                    index *
                                                                        0.05,
                                                                duration: 0.3,
                                                            }}
                                                        >
                                                            <AssignmentPreview
                                                                {...({
                                                                    assignment,
                                                                } as any)}
                                                            />
                                                        </motion.div>
                                                    ),
                                                )}
                                            </div>
                                        </AccordionItem>
                                    );
                                },
                            )}
                        </Accordion>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Submit Card Enhanced */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
            >
                <Card className="bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-blue-950/30 shadow-xl border-0 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-600/5" />
                    <CardBody className="relative p-10 text-center">
                        <motion.div
                            animate={{ scale: 1 }}
                            className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-6 mx-auto"
                            initial={{ scale: 0 }}
                            transition={{
                                type: "spring",
                                stiffness: 200,
                                delay: 0.5,
                            }}
                        >
                            <RocketLaunchIcon className="w-10 h-10 text-white" />
                        </motion.div>

                        <h4 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-3">
                            ¿Todo listo para continuar?
                        </h4>
                        <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                            Revisa que toda la información sea correcta antes de
                            crear las asignaciones. Una vez confirmado, se
                            generará automáticamente la remisión.
                        </p>

                        <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                        >
                            <Button
                                className="min-w-[280px] h-14 font-bold text-lg shadow-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                                color="primary"
                                endContent={
                                    !isSubmitting &&
                                    !result?.success && (
                                        <ArrowRightIcon className="w-5 h-5" />
                                    )
                                }
                                isDisabled={result?.success === true}
                                isLoading={isSubmitting}
                                size="lg"
                                startContent={
                                    !isSubmitting &&
                                    !result?.success && (
                                        <CheckIcon className="w-6 h-6" />
                                    )
                                }
                                onPress={handleSubmit}
                            >
                                {isSubmitting
                                    ? "Creando asignaciones..."
                                    : result?.success
                                      ? "¡Asignaciones creadas!"
                                      : "Confirmar y Crear Asignaciones"}
                            </Button>
                        </motion.div>

                        {result?.success && (
                            <motion.p
                                animate={{ opacity: 1, y: 0 }}
                                className="text-sm text-green-600 dark:text-green-400 mt-6 font-medium"
                                initial={{ opacity: 0, y: 10 }}
                                transition={{ delay: 0.3 }}
                            >
                                Las asignaciones se han creado exitosamente.
                                Preparando remisión...
                            </motion.p>
                        )}

                        {/* Additional Info */}
                        <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                            <div className="grid grid-cols-3 gap-4 max-w-lg mx-auto">
                                <div className="text-center">
                                    <ClockIcon className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                        Tiempo estimado
                                    </p>
                                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        3-5 días
                                    </p>
                                </div>
                                <div className="text-center">
                                    <TruckIcon className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                        Tipo de envío
                                    </p>
                                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Local
                                    </p>
                                </div>
                                <div className="text-center">
                                    <CurrencyDollarIcon className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                        Valor estimado
                                    </p>
                                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Por calcular
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>
        </motion.div>
    );
};

interface SummaryStepProps {
    onSuccess?: (data?: any) => void;
}

export default SummaryStep;
