"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";

const updateSubcustomerSchema = z.object({
    id: z.string().min(1, "ID es requerido"),
    name: z.string().min(1, "El nombre es requerido"),
    displayName: z.string().optional(),
});

export async function updateSubcustomer(
    data: z.infer<typeof updateSubcustomerSchema>,
): Promise<{ success: true; data: any } | { success: false; error: string }> {
    try {
        const validated = updateSubcustomerSchema.parse(data);

        // Verificar que el subcliente existe y es realmente un subcliente
        const subcustomer = await db.customer.findUnique({
            where: { id: validated.id },
            include: { parent: true },
        });

        if (!subcustomer) {
            return { success: false, error: "Subcliente no encontrado" };
        }

        if (!subcustomer.parentId) {
            return {
                success: false,
                error: "No se puede actualizar un cliente principal desde aquí",
            };
        }

        // Verificar nombres únicos (excluyendo el actual)
        const existing = await db.customer.findFirst({
            where: {
                name: validated.name,
                id: { not: validated.id },
            },
        });

        if (existing) {
            return {
                success: false,
                error: "Ya existe un cliente con ese nombre",
            };
        }

        // Actualizar subcliente
        const updated = await db.customer.update({
            where: { id: validated.id },
            data: {
                name: validated.name,
                displayName: validated.displayName || null,
            },
            include: {
                parent: true,
                subCustomers: true,
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
        });

        // Revalidar paths afectados
        revalidatePath("/dashboard/customers");
        revalidatePath(`/dashboard/customers/${validated.id}/details`);
        if (subcustomer.parentId) {
            revalidatePath(
                `/dashboard/customers/${subcustomer.parentId}/details`,
            );
        }

        return { success: true, data: updated };
    } catch (error) {
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos inválidos",
            };
        }
        const dbResult = await handleDbError(() => {
            throw error;
        }, "Error al actualizar subcliente");

        if (dbResult.success) {
            return { success: true, data: dbResult.data };
        } else {
            return {
                success: false,
                error: dbResult.error || "Error al actualizar subcliente",
            };
        }
    }
}
