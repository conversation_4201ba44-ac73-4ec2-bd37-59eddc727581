{"project": {"name": "Lohari", "type": "web-application", "framework": "nextjs", "language": "typescript", "description": "Sistema de gestión de producción textil"}, "standards": {"local": ".agent-os/standards", "global": "~/.agent-os/standards"}, "conventions": {"maxFileLines": 500, "importOrder": ["external", "nextjs", "shared", "features", "relative"], "namingConventions": {"code": "english", "data": "spanish", "ui": "spanish"}}, "features": {"serverActions": true, "appRouter": true, "strictTypeScript": true, "featureModules": true}, "tools": {"linter": "eslint", "formatter": "prettier", "bundler": "webpack/turbopack", "packageManager": "npm"}}