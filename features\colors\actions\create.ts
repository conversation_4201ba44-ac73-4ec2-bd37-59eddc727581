"use server";

import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";

import { createColorSchema } from "../schemas/schema";
import { validateColorUnique } from "../actions/validate";
import { revalidateCache } from "../actions/query";

/**
 * Crea un nuevo color
 */
export async function createColor(data: { name: string; hexCode?: string }) {
    try {
        // Validar datos con parse para lanzar errores directamente
        const validatedData = createColorSchema.parse(data);

        // Verificar si el nombre ya existe
        const uniqueValidation = await validateColorUnique({
            name: validatedData.name,
        });

        if (!uniqueValidation || !uniqueValidation.success) {
            return {
                success: false,
                error:
                    uniqueValidation?.error ||
                    "Error al validar unicidad del color",
            };
        }

        // Verificar el resultado de la validación
        if (!uniqueValidation.data || !(uniqueValidation.data as any).isValid) {
            return {
                success: false,
                error: `El nombre del color ya existe en el sistema`,
            };
        }

        // Crear el color en la base de datos
        const color = await db.color.create({
            data: {
                name: validatedData.name,
                hexCode: validatedData.hexCode,
            },
        });

        // Revalidar rutas y tags
        revalidateCache(color.id);

        return { success: true, data: color };
    } catch (error) {
        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos de color inválidos",
            };
        }

        // Usar el helper para manejar errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al crear color");
    }
}
