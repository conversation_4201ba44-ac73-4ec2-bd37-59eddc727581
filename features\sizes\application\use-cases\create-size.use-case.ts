/**
 * Create Size Use Case
 *
 * Business logic for creating a new size.
 * Handles validation, business rules, and persistence.
 */

// import { injectable, inject } from "tsyringe"; // Commented out - tsyringe not installed

import { Size } from "../../domain/entities/size.entity";
import { SizeRepositoryInterface } from "../../domain/repositories/size.repository.interface";
import {
    CreateSizeDTO,
    SizeResponseDTO,
    toSizeResponse,
} from "../dto/size.dto";

export interface CreateSizeCommand {
    data: CreateSizeDTO;
    userId: string;
}

export interface CreateSizeResult {
    success: boolean;
    data?: SizeResponseDTO;
    error?: string;
    validationErrors?: Array<{ field: string; message: string }>;
}

// @injectable() // Commented out - tsyringe not installed
export class CreateSizeUseCase {
    constructor(
        // @inject("SizeRepository") // Commented out - tsyringe not installed
        private readonly sizeRepository: SizeRepositoryInterface,
    ) {}

    async execute(command: CreateSizeCommand): Promise<CreateSizeResult> {
        try {
            // 1. Validate command
            if (!command.data || !command.userId) {
                return {
                    success: false,
                    error: "Invalid command: missing required data",
                };
            }

            // 2. Check if size name already exists
            const exists = await this.sizeRepository.exists(command.data.name);

            if (exists) {
                return {
                    success: false,
                    error: "Size name already exists",
                    validationErrors: [
                        {
                            field: "name",
                            message: "A size with this name already exists",
                        },
                    ],
                };
            }

            // 3. Create domain entity
            const size = Size.create({
                name: command.data.name,
                displayName: command.data.displayName,
                order: command.data.order,
                isActive: command.data.isActive ?? true,
                category: command.data.category,
                measurements: command.data.measurements,
                equivalents: command.data.equivalents,
            });

            // 4. Validate domain entity
            const validationErrors = size.validate();

            if (validationErrors.length > 0) {
                return {
                    success: false,
                    error: "Validation failed",
                    validationErrors: validationErrors.map((error) => ({
                        field: "general",
                        message: error,
                    })),
                };
            }

            // 5. Apply business rules
            // If no order specified, get next available order
            if (
                command.data.order === undefined ||
                command.data.order === null
            ) {
                const nextOrder = await this.sizeRepository.getNextOrderValue();

                size.update({ order: nextOrder });
            }

            // 6. Save to repository
            const savedSize = await this.sizeRepository.save(size);

            // 7. Return success result
            return {
                success: true,
                data: toSizeResponse(savedSize.toObject() as any),
            };
        } catch (error) {
            // Log error for monitoring
            console.error("CreateSizeUseCase error:", error);

            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Create multiple sizes in bulk
     */
    async executeBulk(
        sizes: CreateSizeDTO[],
        userId: string,
    ): Promise<{
        success: boolean;
        created: SizeResponseDTO[];
        failed: Array<{ index: number; error: string }>;
    }> {
        const created: SizeResponseDTO[] = [];
        const failed: Array<{ index: number; error: string }> = [];

        // Begin transaction
        await this.sizeRepository.beginTransaction();

        try {
            for (let i = 0; i < sizes.length; i++) {
                const result = await this.execute({
                    data: sizes[i],
                    userId,
                });

                if (result.success && result.data) {
                    created.push(result.data);
                } else {
                    failed.push({
                        index: i,
                        error: result.error || "Unknown error",
                    });
                }
            }

            // Commit if all successful
            if (failed.length === 0) {
                await this.sizeRepository.commit();
            } else {
                // Rollback if any failed
                await this.sizeRepository.rollback();

                return {
                    success: false,
                    created: [],
                    failed,
                };
            }

            return {
                success: true,
                created,
                failed: [],
            };
        } catch (error) {
            await this.sizeRepository.rollback();
            throw error;
        }
    }
}
