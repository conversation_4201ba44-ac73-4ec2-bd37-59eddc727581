"use client";

import type { SWRConfiguration } from "swr";

import { useState } from "react";
import useSWR, { mutate } from "swr";

import {
    getSize,
    getSizes,
    validateSizeCode as validateCode,
    createSize as create,
    updateSize as update,
    deleteSize as remove,
} from "@/features/sizes/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

// Obtener helpers de revalidación para el cliente
const { revalidateData } = createClientRevalidation("size");

export interface Size {
    id: string;
    code: string;
    createdAt: string;
    updatedAt: string;
    _count?: {
        garments: number;
    };
}

export interface SizesResponse {
    sizes: Size[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

/**
 * Función global para revalidar todos los datos relacionados con tallas
 * @param id ID opcional de la talla específica a revalidar
 */
export async function revalidateSizeData(id?: string) {
    // Lista de promesas de revalidación
    const revalidationPromises: Promise<any>[] = [];

    // 1. Revalidar lista completa de tallas (cualquier consulta/filtro)
    // Esto afecta a todas las listas de tallas en la aplicación
    revalidationPromises.push(
        mutate((key) => Array.isArray(key) && key[0] === "sizes", undefined, {
            revalidate: true,
            populateCache: true,
        }),
    );

    // 2. Si hay ID, revalidar también la talla específica
    if (id) {
        revalidationPromises.push(
            mutate(["size", id], undefined, {
                revalidate: true,
                populateCache: true,
            }),
        );
    }

    // 3. Revalidar recursos que puedan depender de tallas
    // Por ejemplo, si las prendas tienen tallas
    revalidationPromises.push(
        mutate(
            (key) =>
                Array.isArray(key) &&
                (key[0] === "garments" || key[0] === "products"),
            undefined,
            { revalidate: true },
        ),
    );

    // 4. Revalidar el caché global para forzar actualización
    revalidationPromises.push(
        mutate(
            undefined, // Revalidar todo el cache
            undefined,
            {
                // Solo revalidar, no repoblar para evitar sobrecarga
                revalidate: true,
                populateCache: false,
            },
        ),
    );

    // Ejecutar todas las revalidaciones en paralelo
    // REMOVED: console.log(`🔄 Revalidando datos de tallas${id ? ` (ID: ${id})` : ""}...`);

    try {
        await Promise.all(revalidationPromises);
        // REMOVED: console.log("✅ Datos revalidados correctamente");
    } catch (error) {
        // REMOVED: console.error("❌ Error al revalidar datos:", error);
        // Intentar forzar actualización global en caso de error
        await mutate(undefined, undefined, { revalidate: true });
    }
}

/**
 * Hook para obtener todas las tallas
 * @param options Opciones de filtrado y paginación
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de tallas, paginación, estado de carga y errores
 */
export function useSizes(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
    config?: SWRConfiguration,
) {
    // Convertir opciones a cadena para clave de caché
    const optionsKey = JSON.stringify(options);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        ["sizes", optionsKey],
        async () => getSizes(options),
        {
            // Configuración de revalidación mejorada
            revalidateOnFocus: true,
            revalidateIfStale: true,
            revalidateOnReconnect: true,
            // Combinar con configuración personalizada
            ...config,
        },
    );

    // Escuchar eventos de revalidación
    const { isRevalidating } = useRevalidationListener("sizes");

    return {
        sizes: (data as any)?.data?.sizes || [],
        pagination: (data as any)?.data?.pagination,
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para obtener la información de una talla por su ID
 * @param id ID de la talla a cargar
 * @param config Configuración opcional para SWR
 * @returns Objeto con los datos de la talla, estado de carga y errores
 */
export function useSize(
    id: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        id ? ["size", id] : null,
        async () => (id ? getSize(id) : null),
        {
            // Configuración de revalidación mejorada
            revalidateOnFocus: true,
            revalidateIfStale: true,
            revalidateOnReconnect: true,
            // Combinar con configuración personalizada
            ...config,
        },
    );

    // Escuchar eventos de revalidación específicos para esta talla
    const { isRevalidating } = useRevalidationListener("sizes");

    return {
        size: data?.data,
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para comprobar si un código de talla es válido
 * @param code Código de la talla a validar
 * @param excludeId ID de la talla a excluir de la validación (para edición)
 * @returns Objeto con resultado de validación y estado
 */
export function useValidateSizeCode(code: string | null, excludeId?: string) {
    // Utilizar Server Action como fetcher
    const { data, error, isLoading } = useSWR(
        code && code.length >= 1 ? ["validateSizeCode", code, excludeId] : null,
        async () =>
            code && code.length >= 1 ? validateCode(code, excludeId) : null,
        {
            revalidateOnFocus: false,
            dedupingInterval: 10000,
        },
    );

    // La respuesta tiene directamente isValid
    const validationResult = data as { isValid: boolean } | undefined;

    return {
        isValid: validationResult?.isValid || false,
        isValidating: isLoading,
        error,
    };
}

/**
 * Hook para crear una nueva talla
 * @returns Función para crear una talla y estado de la operación
 */
export function useCreateSize() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createSize = async (data: { code: string }) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await create(data);

            // Si se creó correctamente, revalidar todos los datos relacionados
            if (result?.success) {
                if (process.env.NODE_ENV !== "production") {
                    // REMOVED: console.log("Talla creada correctamente, revalidando datos...");
                }

                // Usar el nuevo sistema de revalidación
                if ((result as any)?.data?.id) {
                    await revalidateData((result as any).data.id);
                }
            } else {
                setError(
                    result?.error || "Error desconocido al crear la talla",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al crear la talla";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createSize,
        isCreating: isLoading,
        createError: error,
    };
}

/**
 * Hook para actualizar una talla existente
 * @returns Función para actualizar una talla y estado de la operación
 */
export function useUpdateSize() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateSize = async (id: string, data: { code: string }) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await update(id, data);

            // Si se actualizó correctamente, revalidar todos los datos relacionados
            if (result?.success) {
                if (process.env.NODE_ENV !== "production") {
                    // REMOVED: console.log(`Talla ${id} actualizada correctamente, revalidando datos...`);
                }

                // Usar el nuevo sistema de revalidación
                await revalidateData(id);
            } else {
                setError(
                    result?.error || "Error desconocido al actualizar la talla",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al actualizar la talla";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        updateSize,
        isUpdating: isLoading,
        updateError: error,
    };
}

/**
 * Hook para eliminar una talla
 * @returns Función para eliminar una talla y estado de la operación
 */
export function useDeleteSize() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteSize = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await remove(id);

            // Si se eliminó correctamente, revalidar todos los datos relacionados
            if (result?.success) {
                if (process.env.NODE_ENV !== "production") {
                    // REMOVED: console.log(`Talla ${id} eliminada correctamente, revalidando datos...`);
                }

                // Usar el nuevo sistema de revalidación
                await revalidateData();
            } else {
                setError(
                    result?.error || "Error desconocido al eliminar la talla",
                );
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error desconocido al eliminar la talla";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        deleteSize,
        isDeleting: isLoading,
        deleteError: error,
    };
}
