"use client";

import React from "react";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Avatar,
    Chip,
    Button,
    Divider,
} from "@heroui/react";
import {
    BuildingOfficeIcon,
    ChevronUpIcon,
    ChevronDownIcon,
    LinkIcon,
} from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

interface CustomerNode {
    id: string;
    name: string;
    displayName?: string | null;
    parentId?: string | null;
    parent?: CustomerNode | null;
    subCustomers?: CustomerNode[];
    _count?: {
        orders: number;
        packings: number;
        subCustomers: number;
    };
}

interface CustomerHierarchyProps {
    customer: CustomerNode;
    onEditHierarchy?: () => void;
}

export function CustomerHierarchy({
    customer,
    onEditHierarchy,
}: CustomerHierarchyProps) {
    const router = useRouter();
    const [expanded, setExpanded] = React.useState(true);

    const renderCustomerNode = (node: CustomerNode, level = 0) => {
        const hasSubCustomers =
            node.subCustomers && node.subCustomers.length > 0;
        const isMainCustomer = node.id === customer.id;

        return (
            <motion.div
                key={node.id}
                animate={{ opacity: 1, x: 0 }}
                initial={{ opacity: 0, x: -20 }}
                style={{ marginLeft: level > 0 ? `${level * 24}px` : 0 }}
                transition={{ delay: level * 0.1 }}
            >
                <Card
                    className={`mb-2 ${isMainCustomer ? "border-2 border-primary" : ""}`}
                    isPressable={!isMainCustomer}
                    onPress={() =>
                        !isMainCustomer &&
                        router.push(`/dashboard/customers/${node.id}/details`)
                    }
                >
                    <CardBody className="p-3">
                        <div className="flex items-center gap-3">
                            <Avatar
                                color={isMainCustomer ? "primary" : "default"}
                                name={node.name}
                                size="sm"
                            />

                            <div className="flex-1">
                                <div className="flex items-center gap-2">
                                    <div>
                                        <h4 className="font-medium text-sm">
                                            {node.displayName || node.name}
                                        </h4>
                                        {node.displayName &&
                                            node.displayName !== node.name && (
                                                <p className="text-xs text-gray-500">
                                                    {node.name}
                                                </p>
                                            )}
                                    </div>
                                    {isMainCustomer && (
                                        <Chip
                                            color="primary"
                                            size="sm"
                                            variant="flat"
                                        >
                                            Cliente Actual
                                        </Chip>
                                    )}
                                    {level > 0 && (
                                        <Chip size="sm" variant="dot">
                                            Nivel {level}
                                        </Chip>
                                    )}
                                </div>

                                {node._count && (
                                    <div className="flex gap-3 mt-1 text-xs text-gray-500">
                                        <span>
                                            {node._count.orders} órdenes
                                        </span>
                                        <span>
                                            {node._count.packings} packings
                                        </span>
                                        {node._count.subCustomers > 0 && (
                                            <span>
                                                {node._count.subCustomers}{" "}
                                                subclientes
                                            </span>
                                        )}
                                    </div>
                                )}
                            </div>

                            {hasSubCustomers && (
                                <Button
                                    isIconOnly
                                    size="sm"
                                    variant="light"
                                    onPress={() => setExpanded(!expanded)}
                                >
                                    {expanded ? (
                                        <ChevronUpIcon className="w-4 h-4" />
                                    ) : (
                                        <ChevronDownIcon className="w-4 h-4" />
                                    )}
                                </Button>
                            )}
                        </div>
                    </CardBody>
                </Card>

                {hasSubCustomers && expanded && (
                    <div className="ml-4 border-l-2 border-gray-200 dark:border-gray-700 pl-2">
                        {node.subCustomers!.map((subCustomer) =>
                            renderCustomerNode(subCustomer, level + 1),
                        )}
                    </div>
                )}
            </motion.div>
        );
    };

    return (
        <Card>
            <CardHeader className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <BuildingOfficeIcon className="w-5 h-5" />
                    <h3 className="text-lg font-semibold">
                        Jerarquía de Cliente
                    </h3>
                </div>
                {onEditHierarchy && (
                    <Button
                        size="sm"
                        startContent={<LinkIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={onEditHierarchy}
                    >
                        Editar Jerarquía
                    </Button>
                )}
            </CardHeader>
            <Divider />
            <CardBody>
                {customer.parent && (
                    <>
                        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                Cliente Principal:
                            </p>
                            <div
                                className="flex items-center gap-2 cursor-pointer hover:opacity-80"
                                onClick={() =>
                                    router.push(
                                        `/dashboard/customers/${customer.parent!.id}/details`,
                                    )
                                }
                            >
                                <Avatar name={customer.parent.name} size="sm" />
                                <span className="font-medium">
                                    {customer.parent.name}
                                </span>
                            </div>
                        </div>
                        <Divider className="mb-4" />
                    </>
                )}

                {renderCustomerNode(customer)}
            </CardBody>
        </Card>
    );
}
