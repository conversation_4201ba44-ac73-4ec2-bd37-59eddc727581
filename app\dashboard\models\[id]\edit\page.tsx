"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    Input,
    Button,
    Textarea,
    Card,
    CardBody,
    addToast,
    Spinner,
    Modal,
    ModalContent,
    <PERSON>dal<PERSON>eader,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON>dal<PERSON>ooter,
} from "@heroui/react";
import {
    DocumentTextIcon,
    ChevronLeftIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    useModel,
    useUpdateModel,
    useValidateModelCode,
    type Model,
} from "@/features/models/hooks/useModel";
import {
    ModelFormProgress,
    calculateModelProgress,
    ModelFormHeader,
    ModelInfoMessage,
    modelFormMessages,
} from "@/features/models/components/forms";
import { updateModelSchema } from "@/features/models/schemas";

type ModelFormData = z.infer<typeof updateModelSchema>;

// Animaciones simples
const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.3 },
};

export default function EditModelPage() {
    const router = useRouter();
    const params = useParams();
    const modelId = params.id as string;

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [showExitModal, setShowExitModal] = useState(false);
    const [originalData, setOriginalData] = useState<ModelFormData | null>(
        null,
    );

    // Hooks para datos y acciones
    const { model, isLoading } = useModel(modelId);
    const { updateModel } = useUpdateModel();

    const {
        register,
        handleSubmit,
        watch,
        formState: { errors, isDirty },
        setError,
        clearErrors,
        reset,
    } = useForm<ModelFormData>({
        resolver: zodResolver(updateModelSchema),
        mode: "onChange",
    });

    // Observar todos los campos para el progreso
    const watchedFields = watch();
    const formProgress = calculateModelProgress(watchedFields);

    // Validar código duplicado
    const code = watch("code");
    const { isValid: isCodeValid, isValidating } = useValidateModelCode(
        code && code !== (model as Model | undefined)?.code ? code : null,
        modelId,
    );

    // Cargar datos del modelo cuando esté disponible
    useEffect(() => {
        if (
            model &&
            typeof model === "object" &&
            model !== null &&
            "code" in model
        ) {
            const validModel = model as Model;
            const initialData = {
                code: validModel.code,
                description: validModel.description || "",
                basePrice: validModel.basePrice || 0,
            };

            setOriginalData(initialData);
            reset(initialData);
        }
    }, [model, reset]);

    // Validar código duplicado
    useEffect(() => {
        if (
            code &&
            code.length >= 2 &&
            code !== (model as Model | undefined)?.code
        ) {
            if (!isCodeValid && !isValidating) {
                setError("code", {
                    type: "manual",
                    message: "Ya existe un modelo con este código",
                });
            } else if (isCodeValid) {
                if (errors.code?.type === "manual") {
                    clearErrors("code");
                }
            }
        }
    }, [
        code,
        isCodeValid,
        isValidating,
        (model as Model | undefined)?.code,
        setError,
        clearErrors,
        errors.code,
    ]);

    const onSubmit = async (data: ModelFormData) => {
        setIsSubmitting(true);
        try {
            // Asegurar que los campos requeridos estén presentes
            const currentModel = model as Model | undefined;
            const updateData = {
                code: data.code || currentModel?.code || "",
                description:
                    data.description || currentModel?.description || "",
                basePrice: data.basePrice,
            };
            const result = await updateModel(modelId, updateData);

            if (!result || !result.success) {
                if (
                    result?.error?.includes("ya existe") ||
                    result?.error?.includes("duplicado")
                ) {
                    setError("code", {
                        type: "manual",
                        message: "Ya existe un modelo con este código",
                    });
                } else {
                    throw new Error(
                        result?.error || "Error al actualizar el modelo",
                    );
                }
                setIsSubmitting(false);

                return;
            }

            setIsSuccess(true);

            // Redirigir después de 2 segundos
            setTimeout(() => {
                router.push("/dashboard/models");
            }, 2000);
        } catch (error) {
            setIsSubmitting(false);
            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "No se pudo actualizar el modelo",
                color: "danger",
            });
        }
    };

    // Manejar salida con cambios sin guardar
    const handleExit = () => {
        if (isDirty && !isSubmitting) {
            setShowExitModal(true);
        } else {
            router.push("/dashboard/models");
        }
    };

    // Validar formulario
    const isFormValid =
        !errors.code &&
        !errors.description &&
        !errors.basePrice &&
        (code !== (model as Model | undefined)?.code ? isCodeValid : true) &&
        isDirty;

    // Loading state
    if (isLoading) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Modelos", href: "/dashboard/models" },
                    { label: "Editar" },
                ]}
                subtitle="Cargando información del modelo..."
                title="Editar Modelo"
            >
                <div className="flex items-center justify-center h-96">
                    <div className="text-center">
                        <Spinner className="mb-4" size="lg" />
                        <p className="text-gray-600 dark:text-gray-400">
                            Cargando modelo...
                        </p>
                    </div>
                </div>
            </DashboardLayout>
        );
    }

    // Error state
    if (!model) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Modelos", href: "/dashboard/models" },
                    { label: "Editar" },
                ]}
                subtitle="No se pudo cargar el modelo"
                title="Error"
            >
                <Card className="max-w-2xl mx-auto">
                    <CardBody className="text-center py-12">
                        <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
                        <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                            Modelo no encontrado
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-6">
                            No se ha encontrado el modelo con el ID
                            especificado.
                        </p>
                        <Button
                            color="primary"
                            onPress={() => router.push("/dashboard/models")}
                        >
                            Volver a la lista
                        </Button>
                    </CardBody>
                </Card>
            </DashboardLayout>
        );
    }

    // Verificar que el modelo es válido antes de renderizar
    if (
        !model ||
        typeof model !== "object" ||
        model === null ||
        !("code" in model)
    ) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Modelos", href: "/dashboard/models" },
                    { label: "Editar" },
                ]}
                subtitle="Error al cargar el modelo"
                title="Error"
            >
                <Card>
                    <CardBody>
                        <p>No se pudo cargar el modelo para editar.</p>
                        <Button
                            as="a"
                            className="mt-4"
                            color="primary"
                            href="/dashboard/models"
                        >
                            Volver a modelos
                        </Button>
                    </CardBody>
                </Card>
            </DashboardLayout>
        );
    }

    const validModel = model as Model;

    return (
        <DashboardLayout
            breadcrumbs={[
                { label: "Modelos", href: "/dashboard/models" },
                {
                    label: validModel.code,
                    href: `/dashboard/models/${validModel.id}`,
                },
                { label: "Editar" },
            ]}
            subtitle={`Actualiza la información del modelo ${validModel.code}`}
            title="Editar Modelo"
        >
            <motion.div {...fadeIn} className="max-w-4xl mx-auto">
                <Card className="shadow-sm border border-gray-200 dark:border-gray-700">
                    <CardBody className="p-6">
                        <AnimatePresence mode="wait">
                            {isSuccess ? (
                                <div className="text-center py-16">
                                    <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full mb-6">
                                        <CheckCircleIcon className="w-10 h-10 text-green-600 dark:text-green-400" />
                                    </div>
                                    <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                        ¡Modelo actualizado exitosamente!
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-400">
                                        Redirigiendo al listado de modelos...
                                    </p>
                                </div>
                            ) : (
                                <div className="space-y-8">
                                    {/* Header */}
                                    <ModelFormHeader
                                        mode="edit"
                                        modelCode={validModel.code}
                                    />

                                    {/* Progress */}
                                    <ModelFormProgress
                                        className="mb-6"
                                        value={formProgress}
                                    />

                                    {/* Form */}
                                    <form
                                        className="space-y-8"
                                        onSubmit={handleSubmit(onSubmit)}
                                    >
                                        {/* Sección: Información del Modelo */}
                                        <section>
                                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                                <DocumentTextIcon className="w-5 h-5 text-purple-600" />
                                                Información del Modelo
                                            </h3>

                                            <div className="space-y-4">
                                                <Input
                                                    {...register("code")}
                                                    isRequired
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                        input: "uppercase",
                                                    }}
                                                    description="Código único para identificar el modelo"
                                                    errorMessage={
                                                        errors.code?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={!!errors.code}
                                                    label="Código del Modelo"
                                                    placeholder="Ej: MDL-001"
                                                    startContent={
                                                        <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                                                    }
                                                    variant="bordered"
                                                />

                                                <Textarea
                                                    {...register("description")}
                                                    isRequired
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    description={`${watchedFields.description?.length || 0}/100 caracteres`}
                                                    errorMessage={
                                                        errors.description
                                                            ?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={
                                                        !!errors.description
                                                    }
                                                    label="Descripción"
                                                    maxRows={6}
                                                    minRows={3}
                                                    placeholder="Describe este modelo de prenda..."
                                                    startContent={
                                                        <div className="pt-1">
                                                            <DocumentTextIcon className="w-4 h-4 text-gray-400" />
                                                        </div>
                                                    }
                                                    variant="bordered"
                                                />

                                                <Input
                                                    {...register("basePrice", {
                                                        valueAsNumber: true,
                                                    })}
                                                    classNames={{
                                                        inputWrapper:
                                                            "bg-gray-50 dark:bg-gray-800",
                                                    }}
                                                    description="Precio mínimo de confección para este modelo"
                                                    endContent={
                                                        <span className="text-default-400 text-small">
                                                            MXN
                                                        </span>
                                                    }
                                                    errorMessage={
                                                        errors.basePrice
                                                            ?.message
                                                    }
                                                    isDisabled={isSubmitting}
                                                    isInvalid={
                                                        !!errors.basePrice
                                                    }
                                                    label="Precio Base"
                                                    min="0"
                                                    placeholder="0.00"
                                                    startContent={
                                                        <span className="text-gray-400">
                                                            $
                                                        </span>
                                                    }
                                                    step="0.01"
                                                    type="number"
                                                    variant="bordered"
                                                />
                                            </div>

                                            {/* Mensajes informativos */}
                                            <div className="mt-4 space-y-2">
                                                {/* Mostrar info de precio base solo si hay cambios */}
                                                {watchedFields.basePrice !==
                                                    originalData?.basePrice && (
                                                    <ModelInfoMessage
                                                        {...modelFormMessages.basePriceInfo}
                                                    />
                                                )}
                                                {errors.code?.type ===
                                                    "manual" && (
                                                    <ModelInfoMessage
                                                        {...modelFormMessages.duplicateCode}
                                                        className="mt-2"
                                                    />
                                                )}
                                            </div>
                                        </section>

                                        {/* Mostrar cambios si hay alguno */}
                                        {isDirty && (
                                            <ModelInfoMessage
                                                description="Los cambios se aplicarán cuando presiones Actualizar Modelo"
                                                title="Cambios detectados"
                                                type="info"
                                            />
                                        )}

                                        {/* Botones */}
                                        <div className="flex gap-3 pt-6">
                                            <Button
                                                isDisabled={isSubmitting}
                                                size="lg"
                                                startContent={
                                                    <ChevronLeftIcon className="w-4 h-4" />
                                                }
                                                type="button"
                                                variant="flat"
                                                onPress={handleExit}
                                            >
                                                Cancelar
                                            </Button>

                                            <Button
                                                className="flex-1"
                                                color="primary"
                                                isDisabled={
                                                    !isFormValid ||
                                                    isSubmitting ||
                                                    isValidating
                                                }
                                                isLoading={isSubmitting}
                                                size="lg"
                                                type="submit"
                                            >
                                                {isSubmitting
                                                    ? "Actualizando..."
                                                    : "Actualizar Modelo"}
                                            </Button>
                                        </div>
                                    </form>
                                </div>
                            )}
                        </AnimatePresence>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Modal de confirmación para salir sin guardar */}
            <Modal
                isOpen={showExitModal}
                onClose={() => setShowExitModal(false)}
            >
                <ModalContent>
                    <ModalHeader className="flex gap-2">
                        <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
                        Cambios sin guardar
                    </ModalHeader>
                    <ModalBody>
                        <p>
                            Tienes cambios sin guardar. ¿Estás seguro de que
                            quieres salir?
                        </p>
                    </ModalBody>
                    <ModalFooter>
                        <Button
                            color="default"
                            variant="flat"
                            onPress={() => setShowExitModal(false)}
                        >
                            Seguir editando
                        </Button>
                        <Button
                            color="danger"
                            variant="solid"
                            onPress={() => {
                                setShowExitModal(false);
                                router.push("/dashboard/models");
                            }}
                        >
                            Salir sin guardar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </DashboardLayout>
    );
}
