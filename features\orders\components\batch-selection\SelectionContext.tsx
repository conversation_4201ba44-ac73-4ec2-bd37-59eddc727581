"use client";

import React, {
    createContext,
    useContext,
    useState,
    useCallback,
    ReactNode,
} from "react";

import { Order } from "@/features/orders/types/orders";

// Tipos para el contexto de selección
export interface SelectionState {
    selectedIds: Set<string>;
    lastSelectedId: string | null;
    selectionMode: "single" | "multiple" | "none";
    selectionSource: "manual" | "filter" | "range" | "all";
}

export interface SelectionContextType {
    // Estado
    selectedIds: Set<string>;
    lastSelectedId: string | null;
    selectionMode: "single" | "multiple" | "none";
    selectionSource: "manual" | "filter" | "range" | "all";
    getSelectedItems: () => Order[];
    isAllSelected: () => boolean;
    isPartiallySelected: () => boolean;

    // Acciones
    select: (id: string) => void;
    deselect: (id: string) => void;
    toggle: (id: string) => void;
    selectRange: (startId: string, endId: string) => void;
    selectAll: () => void;
    deselectAll: () => void;
    selectByPredicate: (predicate: (item: Order) => boolean) => void;
    selectByStatus: (status: string) => void;
    selectByDateRange: (start: Date, end: Date) => void;
    selectByCustomer: (customerId: string) => void;
    selectBySearch: (searchQuery: string) => void;
}

// Crear el contexto con un valor por defecto
const SelectionContext = createContext<SelectionContextType | undefined>(
    undefined,
);

export interface SelectionProviderProps {
    children: ReactNode;
    items: Order[];
    initialSelection?: string[];
}

export const SelectionProvider: React.FC<SelectionProviderProps> = ({
    children,
    items,
    initialSelection = [],
}) => {
    // Estado para los IDs seleccionados
    const [selectedIds, setSelectedIds] = useState<Set<string>>(
        new Set(initialSelection),
    );
    const [lastSelectedId, setLastSelectedId] = useState<string | null>(null);
    const [selectionMode, setSelectionMode] = useState<
        "single" | "multiple" | "none"
    >("none");
    const [selectionSource, setSelectionSource] = useState<
        "manual" | "filter" | "range" | "all"
    >("manual");

    // Función para verificar si todos los elementos están seleccionados
    const isAllSelected = useCallback((): boolean => {
        return items.length > 0 && selectedIds.size === items.length;
    }, [items, selectedIds]);

    // Función para verificar si algunos elementos están seleccionados
    const isPartiallySelected = useCallback((): boolean => {
        return selectedIds.size > 0 && selectedIds.size < items.length;
    }, [items, selectedIds]);

    // Obtener los elementos seleccionados
    const getSelectedItems = useCallback((): Order[] => {
        return items.filter((item) => selectedIds.has(item.id));
    }, [items, selectedIds]);

    // Seleccionar un elemento
    const select = useCallback((id: string) => {
        setSelectedIds((prev) => {
            const newSet = new Set(prev);

            newSet.add(id);

            return newSet;
        });
        setLastSelectedId(id);
        setSelectionMode("multiple");
        setSelectionSource("manual");
    }, []);

    // Deseleccionar un elemento
    const deselect = useCallback((id: string) => {
        setSelectedIds((prev) => {
            const newSet = new Set(prev);

            newSet.delete(id);

            return newSet;
        });
        setSelectionSource("manual");
    }, []);

    // Alternar la selección de un elemento
    const toggle = useCallback((id: string) => {
        setSelectedIds((prev) => {
            const newSet = new Set(prev);

            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }

            return newSet;
        });
        setLastSelectedId(id);
        setSelectionMode((prev) => (prev === "none" ? "single" : "multiple"));
        setSelectionSource("manual");
    }, []);

    // Seleccionar un rango de elementos
    const selectRange = useCallback(
        (startId: string, endId: string) => {
            if (!startId || !endId) return;

            const startIndex = items.findIndex((item) => item.id === startId);
            const endIndex = items.findIndex((item) => item.id === endId);

            if (startIndex === -1 || endIndex === -1) return;

            const start = Math.min(startIndex, endIndex);
            const end = Math.max(startIndex, endIndex);

            const itemsInRange = items.slice(start, end + 1);

            setSelectedIds((prev) => {
                const newSet = new Set(prev);

                itemsInRange.forEach((item) => newSet.add(item.id));

                return newSet;
            });

            setLastSelectedId(endId);
            setSelectionMode("multiple");
            setSelectionSource("range");
        },
        [items],
    );

    // Seleccionar todos los elementos
    const selectAll = useCallback(() => {
        const allIds = new Set(items.map((item) => item.id));

        setSelectedIds(allIds);
        setSelectionMode("multiple");
        setSelectionSource("all");
    }, [items]);

    // Deseleccionar todos los elementos
    const deselectAll = useCallback(() => {
        setSelectedIds(new Set());
        setLastSelectedId(null);
        setSelectionMode("none");
        setSelectionSource("manual");
    }, []);

    // Seleccionar elementos basados en un predicado
    const selectByPredicate = useCallback(
        (predicate: (item: Order) => boolean) => {
            const filteredIds = items.filter(predicate).map((item) => item.id);

            setSelectedIds(new Set(filteredIds));
            setSelectionMode("multiple");
            setSelectionSource("filter");
        },
        [items],
    );

    // Seleccionar por estado
    const selectByStatus = useCallback(
        (status: string) => {
            if (!status) return;
            selectByPredicate((item) => (item.status as any)?.id === status);
        },
        [selectByPredicate],
    );

    // Seleccionar por rango de fechas
    const selectByDateRange = useCallback(
        (start: Date, end: Date) => {
            if (!start || !end) return;

            selectByPredicate((item) => {
                if (!item.receivedDate) return false;
                const itemDate = new Date(item.receivedDate);

                return itemDate >= start && itemDate <= end;
            });
        },
        [selectByPredicate],
    );

    // Seleccionar por cliente
    const selectByCustomer = useCallback(
        (customerId: string) => {
            if (!customerId) return;
            selectByPredicate((item) => item.customer?.id === customerId);
        },
        [selectByPredicate],
    );

    // Seleccionar por búsqueda
    const selectBySearch = useCallback(
        (searchQuery: string) => {
            if (!searchQuery.trim()) return;

            const query = searchQuery.toLowerCase();

            selectByPredicate(
                (item) =>
                    item.cutOrder?.toLowerCase().includes(query) ||
                    false ||
                    item.transferNumber?.toLowerCase().includes(query) ||
                    false ||
                    item.batch?.toLowerCase().includes(query) ||
                    false ||
                    item.customer?.name?.toLowerCase().includes(query) ||
                    false,
            );
        },
        [selectByPredicate],
    );

    // Valor del contexto
    const contextValue: SelectionContextType = {
        // Estado
        selectedIds,
        lastSelectedId,
        selectionMode,
        selectionSource,
        getSelectedItems,
        isAllSelected,
        isPartiallySelected,

        // Acciones
        select,
        deselect,
        toggle,
        selectRange,
        selectAll,
        deselectAll,
        selectByPredicate,
        selectByStatus,
        selectByDateRange,
        selectByCustomer,
        selectBySearch,
    };

    return (
        <SelectionContext.Provider value={contextValue}>
            {children}
        </SelectionContext.Provider>
    );
};

// Hook personalizado para usar el contexto de selección
export const useSelection = (): SelectionContextType => {
    const context = useContext(SelectionContext);

    if (context === undefined) {
        throw new Error(
            "useSelection debe usarse dentro de un SelectionProvider",
        );
    }

    return context;
};
