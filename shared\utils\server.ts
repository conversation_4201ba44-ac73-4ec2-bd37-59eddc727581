"use server";

import bcrypt from "bcryptjs";

import { prisma } from "@/shared/lib/prisma";

type SafeUser = {
    id: string;
    name: string | null;
    email: string;
    role: string;
    contractorId?: string | null;
};

export const hashPassword = async (password: string): Promise<string> => {
    const saltRounds = 10;

    return await bcrypt.hash(password, saltRounds);
};

export const verifyPassword = async (
    password: string,
    hashedPassword: string,
): Promise<boolean> => {
    return await bcrypt.compare(password, hashedPassword);
};

export const getUserFromDb = async (
    email: string,
    password: string,
): Promise<SafeUser | null> => {
    let retryCount = 0;
    const maxRetries = 2;

    const attemptGetUser = async (): Promise<SafeUser | null> => {
        try {
            // REMOVED: console.log(`[getUserFromDb] Intentando obtener usuario: ${email}`);

            const user = await prisma.user.findUnique({
                where: { email },
                include: {
                    role: true,
                },
            });

            if (!user) {
                throw new Error("Usuario no registrado");
            }

            if (!user.password) {
                throw new Error("Credenciales incorrectas");
            }

            const passwordMatch = await bcrypt.compare(password, user.password);

            if (!passwordMatch) {
                throw new Error("Contraseña incorrecta");
            }

            return {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.roleId, // Usar roleId en lugar del objeto role completo
                contractorId: null, // Ya no usamos la relación contractors
            };
        } catch (error) {
            // REMOVED: console.error(
            //     `[getUserFromDb] Error al obtener usuario (intento ${retryCount + 1}):`,
            //     error,
            // );

            // Si es un error de conexión a Prisma, lanzarlo para intentar reconectar
            if (
                error instanceof Error &&
                (error.message.includes("prepared statement") ||
                    error.message.includes("connection") ||
                    error.message.includes("database"))
            ) {
                throw error;
            } else {
                // Otros errores los propagamos directamente
                throw error;
            }
        }
    };

    while (retryCount < maxRetries) {
        try {
            return await attemptGetUser();
        } catch (error) {
            if (
                error instanceof Error &&
                (error.message.includes("prepared statement") ||
                    error.message.includes("connection") ||
                    error.message.includes("database"))
            ) {
                // REMOVED: console.log(
                //     `[getUserFromDb] Error de conexión detectado, intentando reconectar...`,
                // );
                // Intentar resetear la conexión de Prisma
                try {
                    // @ts-ignore - Importación dinámica
                    const { resetPrismaConnection } = await import(
                        "@/shared/lib/prisma"
                    );

                    await resetPrismaConnection();
                    // REMOVED: console.log(`[getUserFromDb] Reconexión exitosa`);
                } catch (reconnectError) {
                    // REMOVED: console.error(
                    //     `[getUserFromDb] Error al reconectar:`,
                    //     reconnectError,
                    // );
                }

                retryCount++;
                // Esperar un poco antes de reintentar
                await new Promise((resolve) => setTimeout(resolve, 500));
            } else {
                // Si no es un error de conexión, lo propagamos
                throw error;
            }
        }
    }

    // Si llegamos aquí es porque agotamos los reintentos
    throw new Error(
        "No se pudo conectar a la base de datos después de varios intentos",
    );
};
