"use server";

import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

import { createContractorSchema } from "../schemas/contractor.schema";

import { validateContractorName } from "./validate";

// Obtener helpers de revalidación para la entidad "contractor"
const { revalidateCache } = createServerRevalidation("contractor");

/**
 * Crea un nuevo contratista
 * @param data Datos del contratista a crear
 * @param forceCreate Si es true, ignorará la validación de nombre único
 */
export async function createContractor(
    data: {
        firstName: string;
        middleName?: string | null;
        lastName: string;
        secondLastName?: string | null;
        email?: string | null;
        phone?: string | null;
        notes?: string | null;
    },
    forceCreate = false,
) {
    try {
        // Validar datos con Zod schema
        const validatedData = createContractorSchema.parse(data);

        // Verificar si el nombre ya existe (solo si no se está forzando la creación)
        if (!forceCreate) {
            const nameValidation = await validateContractorName(
                validatedData.firstName,
                validatedData.lastName,
            );

            // Si la validación del nombre falla o el nombre no es válido
            if (
                !nameValidation ||
                !nameValidation.success ||
                (nameValidation.data && !nameValidation.data.isValid)
            ) {
                return {
                    success: false,
                    error: "Ya existe un contratista con este nombre y apellido",
                };
            }
        }

        // Construir el nombre completo para guardar en el campo 'name'
        const fullName =
            `${validatedData.firstName} ${validatedData.middleName || ""} ${validatedData.lastName} ${validatedData.secondLastName || ""}`
                .trim()
                .replace(/\s+/g, " ");

        try {
            // Crear el contratista con todos los campos
            const contractor = await db.contractor.create({
                data: {
                    name: fullName,
                    firstName: validatedData.firstName,
                    middleName: validatedData.middleName,
                    lastName: validatedData.lastName,
                    secondLastName: validatedData.secondLastName,
                    email: validatedData.email,
                    phone: validatedData.phone,
                    notes: validatedData.notes,
                },
            });

            // Transformar fechas para evitar problemas de serialización
            const serializedContractor = {
                ...contractor,
                createdAt: contractor.createdAt.toISOString(),
                updatedAt: contractor.updatedAt.toISOString(),
            };

            // Revalidar caché usando el helper genérico
            revalidateCache(contractor.id);

            return { success: true, data: serializedContractor };
        } catch (error) {
            console.error("[CreateContractor] Error al crear:", error);

            return {
                success: false,
                error: "Error al crear contratista. Intente nuevamente.",
            };
        }
    } catch (error) {
        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos inválidos",
            };
        }

        // Usar el helper para manejar errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al crear contratista");
    }
}
