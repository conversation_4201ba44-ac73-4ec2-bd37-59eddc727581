# Stack Tecnológico Completo - Proyecto Lohari

## 🚀 Framework Principal

### Next.js 15.3.0-canary.31
- **App Router** para routing moderno
- **Server Components** por defecto
- **Server Actions** para mutaciones
- **Turbopack** habilitado para desarrollo rápido
- React 18.3.1 como base

## 🎨 UI/UX Stack

### Componentes UI
- **@heroui/react v2.7.5** - Sistema de componentes principal
  - button, input, navbar, listbox, etc.
  - Theme system integrado
  - Toast notifications
- **@radix-ui** - Componentes headless
  - dialog, checkbox, select, slot
- **lucide-react v0.475.0** - Iconos modernos
- **react-icons v5.5.0** - Biblioteca de iconos adicional
- **@heroicons/react v2.2.0** - Iconos de Tailwind

### Estilos y Animaciones
- **tailwindcss v3.4.16** - Utility-first CSS
- **tailwind-merge v3.3.0** - Merge de clases condicionales
- **tailwindcss-animate v1.0.7** - Animaciones predefinidas
- **@tailwindcss/typography v0.5.16** - Estilos de texto
- **framer-motion v11.18.2** - Animaciones avanzadas
- **class-variance-authority v0.7.1** - Variantes de componentes

### Temas y Colores
- **next-themes v0.4.4** - Sistema de temas dark/light
- **@radix-ui/colors v3.0.0** - Paleta de colores accesibles
- **chroma-js v2.4.2** - Manipulación de colores
- **color v5.0.0** - Utilidades de color
- **color-namer v1.4.0** - Nombres de colores

## 📊 Estado y Data Fetching

### Cliente
- **swr v2.3.3** - Data fetching y caché
- **@tanstack/react-query v5.79.0** - Estado servidor avanzado
- **@tanstack/react-query-devtools** - DevTools

### Caché y Performance
- **ioredis v5.6.1** - Cliente Redis para caché
- **web-vitals v5.0.2** - Métricas de performance
- **react-window v1.8.11** - Virtualización de listas

## 📝 Formularios y Validación

- **react-hook-form v7.57.0** - Gestión de formularios
- **@hookform/resolvers v4.1.3** - Integración con Zod
- **zod v3.25.48** - Validación de esquemas

## 🗄️ Base de Datos

### ORM y Cliente
- **@prisma/client v6.8.2** - ORM principal
- **prisma v6.8.2** (dev) - CLI y herramientas
- **pg v8.11.3** - Driver PostgreSQL
- **@auth/prisma-adapter v2.8.0** - Adapter para NextAuth

### Integración Externa
- **@supabase/supabase-js v2.49.1** - Cliente Supabase

## 🔐 Autenticación y Seguridad

- **next-auth v5.0.0-beta.25** - Autenticación completa
- **bcryptjs v3.0.2** - Hashing de contraseñas
- **uuid v11.1.0** - Generación de IDs únicos

## 📄 Generación de Documentos

- **@react-pdf/renderer v4.3.0** - Generación de PDFs React
- **jspdf v3.0.1** - Generación de PDFs JavaScript
- **html2canvas v1.4.1** - Screenshots HTML
- **react-to-print v3.1.0** - Impresión de componentes

## 📅 Manejo de Fechas

- **luxon v3.6.1** - Biblioteca de fechas principal
- **date-fns v4.1.0** - Utilidades de fecha adicionales
- **date-fns-tz v3.2.0** - Zonas horarias
- **@internationalized/date v3.7.0** - Fechas internacionales

## 📊 Visualización de Datos

- **recharts v2.15.2** - Gráficos React
- **@fullcalendar/react v6.1.17** - Calendario completo
  - core, daygrid, timegrid, interaction

## 🤖 Inteligencia Artificial

- **@anthropic-ai/sdk v0.39.0** - Claude AI
- **openai v4.86.1** - OpenAI GPT
- **@modelcontextprotocol/server-sequential-thinking v0.6.2** - MCP

## 🛠️ Herramientas de Desarrollo

### TypeScript y Linting
- **typescript v5.6.3** - Lenguaje principal
- **@typescript-eslint/eslint-plugin v8.32.1**
- **@typescript-eslint/parser v8.32.1**
- **eslint v8.57.0** + plugins varios
- **prettier v3.3.3** - Formateo de código

### Build y Bundling
- **tsx v4.19.4** - TypeScript execution
- **ts-node v10.9.2** - Node TypeScript
- **autoprefixer v10.4.19** - CSS prefixes
- **postcss v8.4.49** - CSS processing

### CLI y Utilidades
- **commander v11.1.0** - CLI framework
- **ora v7.0.1** - Spinners CLI
- **chalk v5.4.1** - Colores en terminal
- **boxen v7.1.1** - Cajas en terminal
- **cli-table3 v0.6.3** - Tablas CLI
- **figlet v1.7.0** - ASCII art
- **gradient-string v2.0.2** - Gradientes en texto

## 📦 Utilidades Generales

- **lodash v4.17.21** - Utilidades JavaScript
- **axios v1.8.3** - Cliente HTTP
- **dotenv v16.4.7** - Variables de entorno
- **nodemailer v6.10.0** - Envío de emails
- **intl-messageformat v10.5.0** - Formateo i18n

## 🔧 DevDependencies Clave

### Tipos TypeScript
- **@types/react v18.3.20**
- **@types/node v20.5.7**
- **@types/luxon v3.6.2**
- Muchos más tipos específicos...

### Testing (Configurado pero no implementado)
- Estructura para tests e2e, integration, unit
- No hay framework de testing instalado

## 📋 Scripts Principales

```json
{
  "dev": "next dev --turbopack",
  "build": "next build",
  "start": "next start",
  "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix",
  "seed": "tsx prisma/seed.ts",
  "db:validate": "tsx scripts/validate-config-data.ts"
}
```

## 🔍 Observaciones Importantes

1. **Versiones Canary/Beta**
   - Next.js 15.3.0-canary (bleeding edge)
   - NextAuth v5 beta (no estable)

2. **Dependencias Pesadas**
   - FullCalendar suite completa
   - Múltiples bibliotecas de UI
   - Dos SDKs de AI

3. **Redundancias**
   - 3 bibliotecas de iconos diferentes
   - Múltiples bibliotecas de fechas
   - 2 bibliotecas de PDF

4. **Performance Considerations**
   - Bundle size puede ser grande
   - React Query + SWR (¿redundante?)
   - Muchas animaciones (Framer Motion)

5. **Seguridad**
   - bcryptjs en lugar de bcrypt (JavaScript puro)
   - Dependencias actualizadas
   - NextAuth beta puede tener cambios breaking

## 🎯 Recomendaciones

1. **Consolidar Dependencias**
   - Elegir una biblioteca de iconos
   - Unificar manejo de fechas
   - Decidir entre SWR o React Query

2. **Actualizar a Versiones Estables**
   - Esperar Next.js 15 estable
   - Migrar a NextAuth v5 estable cuando salga

3. **Optimizar Bundle**
   - Analizar tamaño del bundle
   - Tree-shaking agresivo
   - Lazy loading de componentes pesados

4. **Implementar Testing**
   - Agregar Jest/Vitest
   - Playwright para E2E
   - Testing Library para componentes