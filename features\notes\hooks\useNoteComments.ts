"use client";

import { useState } from "react";
import useSWR, { mutate } from "swr";

import {
    createComment,
    getComments,
    updateComment,
    deleteComment,
} from "../actions/comments";

export function useNoteComments(noteId: string) {
    const [isCreating, setIsCreating] = useState(false);
    const [page, setPage] = useState(1);
    const pageSize = 10;

    const { data, error, isLoading } = useSWR(
        noteId ? [`/api/notes/${noteId}/comments`, page] : null,
        async () => {
            const result = await getComments({ noteId, page, pageSize });

            if (result.success && result.data) {
                return result.data;
            }
            throw new Error(result.error || "Failed to fetch comments");
        },
        {
            revalidateOnFocus: false,
            keepPreviousData: true,
        },
    );

    const comments = data?.comments || [];
    const hasMore = data?.hasMore || false;
    const total = data?.totalCount || 0;

    const handleCreateComment = async (content: string, parentId?: string) => {
        setIsCreating(true);
        try {
            const result = await createComment({
                noteId,
                content,
                parentId,
            });

            if (result.success) {
                // Revalidate all pages
                for (let i = 1; i <= page; i++) {
                    mutate([`/api/notes/${noteId}/comments`, i]);
                }
            }

            return result;
        } finally {
            setIsCreating(false);
        }
    };

    const handleUpdateComment = async (commentId: string, content: string) => {
        const result = await updateComment({
            commentId,
            content,
        });

        if (result.success) {
            // Revalidate all pages
            for (let i = 1; i <= page; i++) {
                mutate([`/api/notes/${noteId}/comments`, i]);
            }
        }

        return result;
    };

    const handleDeleteComment = async (commentId: string) => {
        const result = await deleteComment(commentId);

        if (result.success) {
            // Revalidate all pages
            for (let i = 1; i <= page; i++) {
                mutate([`/api/notes/${noteId}/comments`, i]);
            }
        }

        return result;
    };

    const loadMore = () => {
        if (hasMore) {
            setPage((prev) => prev + 1);
        }
    };

    return {
        comments,
        total,
        hasMore,
        isLoading,
        isCreating,
        error,
        createComment: handleCreateComment,
        updateComment: handleUpdateComment,
        deleteComment: handleDeleteComment,
        loadMore,
    };
}
