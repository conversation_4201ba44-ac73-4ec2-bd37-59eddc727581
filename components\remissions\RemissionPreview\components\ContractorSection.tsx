"use client";

import React from "react";
import { Card, CardBody } from "@heroui/card";

interface ContractorSectionProps {
    contractor: {
        name: string;
        location: string;
        contact?: string;
    };
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

export const ContractorSection: React.FC<ContractorSectionProps> = ({
    contractor,
    editMode,
    onEdit,
}) => {
    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(`contractor.${field}`, e.currentTarget.textContent);
            }
        };

    return (
        <Card className="mb-6">
            <CardBody>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Información del Contratista
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm text-gray-600">Nombre:</p>
                        <p
                            suppressContentEditableWarning
                            className={`text-base font-medium text-gray-800 ${editMode ? "editable-field" : ""}`}
                            contentEditable={editMode}
                            onBlur={handleEdit("name")}
                        >
                            {contractor.name}
                        </p>
                    </div>

                    <div>
                        <p className="text-sm text-gray-600">Ubicación:</p>
                        <p
                            suppressContentEditableWarning
                            className={`text-base text-gray-800 ${editMode ? "editable-field" : ""}`}
                            contentEditable={editMode}
                            onBlur={handleEdit("location")}
                        >
                            {contractor.location}
                        </p>
                    </div>

                    {contractor.contact && (
                        <div className="md:col-span-2">
                            <p className="text-sm text-gray-600">Contacto:</p>
                            <p
                                suppressContentEditableWarning
                                className={`text-base text-gray-800 ${editMode ? "editable-field" : ""}`}
                                contentEditable={editMode}
                                onBlur={handleEdit("contact")}
                            >
                                {contractor.contact}
                            </p>
                        </div>
                    )}
                </div>
            </CardBody>
        </Card>
    );
};
