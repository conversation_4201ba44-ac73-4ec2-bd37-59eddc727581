-- Add enhanced fields for packing system

-- Add new fields to Customer table
ALTER TABLE "Customer" ADD COLUMN IF NOT EXISTS "displayName" TEXT;
ALTER TABLE "Customer" ADD COLUMN IF NOT EXISTS "packingSettings" JSONB;

-- Add new signature tracking fields to Packing table
ALTER TABLE "Packing" ADD COLUMN IF NOT EXISTS "transportSignature" TEXT;
ALTER TABLE "Packing" ADD COLUMN IF NOT EXISTS "transportSignedAt" TIMESTAMP(3);
ALTER TABLE "Packing" ADD COLUMN IF NOT EXISTS "receiverSignature" TEXT;
ALTER TABLE "Packing" ADD COLUMN IF NOT EXISTS "receiverSignedAt" TIMESTAMP(3);
ALTER TABLE "Packing" ADD COLUMN IF NOT EXISTS "receiverName" TEXT;
ALTER TABLE "Packing" ADD COLUMN IF NOT EXISTS "packingSummaryBySize" JSONB;

-- Add new fields to PackingDetail table
ALTER TABLE "PackingDetail" ADD COLUMN IF NOT EXISTS "boxesCount" INTEGER;
ALTER TABLE "PackingDetail" ADD COLUMN IF NOT EXISTS "bagsFirstCount" INTEGER;
ALTER TABLE "PackingDetail" ADD COLUMN IF NOT EXISTS "bagsSecondCount" INTEGER;
ALTER TABLE "PackingDetail" ADD COLUMN IF NOT EXISTS "loosePiecesInfo" JSONB;

-- Add comments for documentation
COMMENT ON COLUMN "Customer"."displayName" IS 'For UI display (e.g., "Becktel - Reebok")';
COMMENT ON COLUMN "Customer"."packingSettings" IS 'Custom settings per customer';
COMMENT ON COLUMN "Packing"."packingSummaryBySize" IS 'Detailed breakdown by size';
COMMENT ON COLUMN "PackingDetail"."loosePiecesInfo" IS 'Detailed info about pieces not in boxes';