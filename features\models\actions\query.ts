"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Obtiene todos los modelos con opciones de filtrado y paginación
 */
export async function getModels(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) {
    const {
        search,
        orderBy = "code",
        order = "asc",
        page = 1,
        perPage = 50,
    } = options;

    return await handleDbError(async () => {
        // Construir filtros
        const where: Prisma.GarmentModelWhereInput = search
            ? {
                  OR: [
                      {
                          code: {
                              contains: search,
                              mode: "insensitive" as Prisma.QueryMode,
                          },
                      },
                      {
                          description: {
                              contains: search,
                              mode: "insensitive" as Prisma.QueryMode,
                          },
                      },
                  ],
              }
            : {};

        // Obtener datos con paginación
        const models = await db.garmentModel.findMany({
            where,
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
            select: {
                id: true,
                code: true,
                description: true,
                basePrice: true,
                createdAt: true,
                updatedAt: true,
                _count: {
                    select: { garments: true },
                },
            },
        });

        // Obtener total para paginación
        const total = await db.garmentModel.count({ where });

        return {
            models,
            pagination: {
                total,
                currentPage: page,
                lastPage: Math.ceil(total / perPage),
            },
        };
    }, "Error al obtener modelos");
}

/**
 * Obtiene un modelo por ID
 */
export async function getModel(id: string | null | undefined) {
    if (!id) return { success: false, error: "ID no válido" };

    return await handleDbError(async () => {
        const model = await db.garmentModel.findUnique({
            where: { id },
            select: {
                id: true,
                code: true,
                description: true,
                basePrice: true,
                createdAt: true,
                updatedAt: true,
                _count: {
                    select: { garments: true },
                },
            },
        });

        if (!model) {
            throw new Error("Modelo no encontrado");
        }

        return model;
    }, "Error al obtener modelo");
}
