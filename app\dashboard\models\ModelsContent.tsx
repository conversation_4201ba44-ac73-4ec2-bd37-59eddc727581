"use client";

import React, {
    useCallback,
    useEffect,
    useMemo,
    useState,
    useTransition,
} from "react";
import {
    Badge,
    Button,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownTrigger,
    Input,
    Modal,
    ModalContent,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableColumn,
    TableHeader,
    TableRow,
    Tooltip,
    useDisclosure,
    SortDescriptor,
    addToast,
    Kbd,
} from "@heroui/react";
import {
    ArrowLongLeftIcon,
    ArrowPathIcon,
    ChevronDownIcon,
    MagnifyingGlassIcon,
    PencilSquareIcon,
    PlusIcon,
    TrashIcon,
    DocumentTextIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import CubeSpinner from "@/shared/components/ui/CubeSpinner";
import {
    Model,
    useDeleteModel,
    useModels,
} from "@/features/models/hooks/useModel";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

// Define los tipos de columna para la tabla
type ColumnKey = keyof Model | "actions" | "usage";

// Definición de columnas con mejor tipado
interface Column {
    name: string;
    uid: string;
    sortable: boolean;
}

const COLUMNS: Column[] = [
    { name: "CÓDIGO", uid: "code", sortable: true },
    { name: "DESCRIPCIÓN", uid: "description", sortable: true },
    { name: "USO", uid: "usage", sortable: false },
    { name: "CREACIÓN", uid: "createdAt", sortable: true },
    { name: "ACCIONES", uid: "actions", sortable: false },
];

const ModelsContent = () => {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    const [, startTransition] = useTransition();

    const {
        isOpen: isDeleteModalOpen,
        onOpen: onDeleteModalOpen,
        onClose: onDeleteModalClose,
    } = useDisclosure();

    const {
        isOpen: isDetailModalOpen,
        onOpen: onDetailModalOpen,
        onClose: onDetailModalClose,
    } = useDisclosure();

    const [searchTerm, setSearchTerm] = useState("");
    const [modelToDelete, setModelToDelete] = useState<Model | null>(null);
    const [selectedModel, setSelectedModel] = useState<Model | null>(null);

    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
        column: "code",
        direction: "ascending",
    });

    const [currentPage, setCurrentPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    // Uso de SWR para obtener los modelos con opciones optimizadas
    const { models, isLoading, mutate } = useModels({
        search: searchTerm,
        page: currentPage,
        perPage: rowsPerPage,
        orderBy: sortDescriptor.column?.toString() || "code",
        order: sortDescriptor.direction === "ascending" ? "asc" : "desc",
    });

    // Hook para eliminar modelos
    const { deleteModel } = useDeleteModel();

    // Escuchar eventos de revalidación de modelos
    const { isRevalidating } = useRevalidationListener("models");

    // Efecto para revalidar cuando cambia isRevalidating
    useEffect(() => {
        if (isRevalidating) {
            // Cuando se recibe un evento de revalidación, actualizar la tabla
            mutate();
        }
    }, [isRevalidating, mutate]);

    // Escuchar cambios globales de revalidación
    useEffect(() => {
        const handleRevalidationEvent = (event: any) => {
            if (event.detail?.entity === "model") {
                mutate();
            }
        };

        // Registrar listener para eventos de revalidación
        window.addEventListener("revalidation-event", handleRevalidationEvent);

        // Limpiar listener al desmontar
        return () => {
            window.removeEventListener(
                "revalidation-event",
                handleRevalidationEvent,
            );
        };
    }, [mutate]);

    // Añadir cálculo de estadísticas basado en los datos de modelos
    const statistics = useMemo(() => {
        const totalModels = models.length;
        const usedModels = models.filter(
            (model: Model) => (model._count?.garments || 0) > 0,
        ).length;
        const unusedModels = totalModels - usedModels;

        const usedPercentage = totalModels
            ? Math.round((usedModels / totalModels) * 100)
            : 0;
        const unusedPercentage = totalModels
            ? Math.round((unusedModels / totalModels) * 100)
            : 0;

        return {
            totalModels,
            usedModels,
            unusedModels,
            usedPercentage,
            unusedPercentage,
        };
    }, [models]);

    useEffect(() => {
        const params = {
            search: searchParams.get("search"),
            page: searchParams.get("page"),
            perPage: searchParams.get("perPage"),
            sort: searchParams.get("sort"),
            order: searchParams.get("order"),
        };

        if (params.search) setSearchTerm(params.search);
        if (params.page) setCurrentPage(parseInt(params.page));
        if (params.perPage) setRowsPerPage(parseInt(params.perPage));
        if (params.sort) {
            setSortDescriptor({
                column: params.sort,
                direction:
                    params.order === "descending" ? "descending" : "ascending",
            });
        }
    }, [searchParams]);

    useEffect(() => {
        startTransition(() => {
            const params = new URLSearchParams();

            if (searchTerm) params.set("search", searchTerm);
            if (currentPage > 1) params.set("page", currentPage.toString());
            if (rowsPerPage !== 10)
                params.set("perPage", rowsPerPage.toString());
            if (sortDescriptor.column) {
                params.set("sort", sortDescriptor.column.toString());
                params.set("order", sortDescriptor.direction);
            }

            router.replace(`${pathname}?${params.toString()}`, {
                scroll: false,
            });
        });
    }, [
        searchTerm,
        currentPage,
        rowsPerPage,
        sortDescriptor,
        pathname,
        router,
        startTransition,
    ]);

    // Handlers de eventos optimizados
    const handleSearch = useCallback((value: string) => {
        setSearchTerm(value);
        setCurrentPage(1); // Resetear a la primera página al buscar
    }, []);

    const handleOpenDetailModal = useCallback(
        (model: Model) => {
            setSelectedModel(model);
            onDetailModalOpen();
        },
        [onDetailModalOpen],
    );

    const handleDeleteModel = useCallback(async () => {
        if (!modelToDelete) return;

        try {
            // Mostrar toast de procesamiento
            addToast({
                title: "Procesando",
                description: "Eliminando modelo, por favor espere...",
                color: "primary",
            });

            // Usar Server Action para eliminar modelo
            const result = await deleteModel(modelToDelete.id);

            // Verificar si hay errores
            if (!result || !result.success) {
                throw new Error(result?.error || "Error al eliminar el modelo");
            }

            // Cerrar modal y limpiar selección
            onDeleteModalClose();
            setModelToDelete(null);

            // Forzar revalidación inmediata para actualizar la UI
            await mutate();

            // Enviar evento de revalidación
            if (typeof window !== "undefined") {
                const event = new CustomEvent("revalidation-event", {
                    detail: { entity: "model" },
                });

                window.dispatchEvent(event);
            }

            // Realizar una segunda revalidación después de un pequeño retraso
            setTimeout(async () => {
                await mutate();
            }, 300);

            // Mostrar mensaje de éxito
            addToast({
                title: "Modelo eliminado",
                description: `El modelo ${modelToDelete.code} ha sido eliminado correctamente`,
                color: "success",
            });
        } catch (unused) {
            addToast({
                title: "Error eliminando modelo",
                description:
                    unused instanceof Error
                        ? unused.message
                        : "Error desconocido",
                color: "danger",
            });
        } finally {
            setModelToDelete(null);
        }
    }, [modelToDelete, onDeleteModalClose, deleteModel, mutate]);

    const handleOpenDeleteModal = useCallback(
        (model: Model) => {
            setModelToDelete(model);
            onDeleteModalOpen();
        },
        [onDeleteModalOpen],
    );

    // Función para refrescar los datos
    const refreshData = useCallback(async () => {
        try {
            // Mostrar toast de carga
            addToast({
                title: "Actualizando datos",
                description: "Obteniendo modelos actualizados...",
                color: "primary",
            });

            // Llamar a mutate para refrescar los datos
            await mutate(undefined, {
                revalidate: true,
            });

            // Mostrar toast de éxito
            addToast({
                title: "Datos actualizados",
                description: `Lista de ${models.length} modelos actualizada`,
                color: "success",
            });
        } catch (_) {
            // Error al actualizar modelos
            addToast({
                title: "Error",
                description: "No se pudieron actualizar los datos",
                color: "danger",
            });
        }
    }, [mutate, models.length]);

    // Atajo de teclado para búsqueda rápida
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // Ctrl/Cmd + K para enfocar la búsqueda
            if ((e.ctrlKey || e.metaKey) && e.key === "k") {
                e.preventDefault();
                document.getElementById("searchInput")?.focus();
            }

            // Tecla N para nuevo modelo
            if (
                e.key === "n" &&
                !(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)
            ) {
                e.preventDefault();
                router.push("/dashboard/models/new");
            }
        };

        window.addEventListener("keydown", handleKeyDown);

        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [router]);

    // Render cell
    const renderCell = useCallback(
        (model: Model, columnKey: ColumnKey) => {
            switch (columnKey) {
                case "code":
                    return (
                        <div className="flex items-center justify-center gap-2">
                            <Button
                                className="font-mono font-medium bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-md text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50 transition-colors min-w-0"
                                variant="light"
                                onPress={() => handleOpenDetailModal(model)}
                            >
                                {model.code}
                            </Button>
                        </div>
                    );

                case "description":
                    return (
                        <div className="flex justify-center">
                            <div className="max-w-xs truncate text-center">
                                {model.description}
                            </div>
                        </div>
                    );

                case "usage":
                    const count = model._count?.garments || 0;

                    return (
                        <div className="flex justify-center">
                            {count > 0 ? (
                                <Badge
                                    className="px-2 py-1"
                                    color="success"
                                    variant="flat"
                                >
                                    {count} {count === 1 ? "prenda" : "prendas"}
                                </Badge>
                            ) : (
                                <Badge
                                    className="px-2 py-1 inline-flex items-center justify-center"
                                    color="warning"
                                    variant="flat"
                                >
                                    <span>Sin uso</span>
                                </Badge>
                            )}
                        </div>
                    );

                case "createdAt":
                case "updatedAt":
                    return (
                        <div className="flex flex-col items-center justify-center">
                            <span className="text-sm text-gray-600 dark:text-gray-300">
                                {format(
                                    new Date(model[columnKey]),
                                    "dd MMM yyyy",
                                    { locale: es },
                                )}
                            </span>
                            <span className="text-xs text-gray-400 dark:text-gray-500">
                                {formatDistanceToNow(
                                    new Date(model[columnKey]),
                                    {
                                        addSuffix: true,
                                        locale: es,
                                    },
                                )}
                            </span>
                        </div>
                    );

                case "actions":
                    const isUsed = (model._count?.garments || 0) > 0;

                    return (
                        <div className="flex gap-2 justify-center">
                            <Tooltip
                                color="primary"
                                content="Ver detalles"
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="relative overflow-hidden bg-blue-50/80 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-blue-200/50 dark:border-blue-800/30 group"
                                    href={`/dashboard/models/${model.id}/details`}
                                    size="sm"
                                    variant="flat"
                                >
                                    <span className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                    <InformationCircleIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color="primary"
                                content="Editar"
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    as={Link}
                                    className="relative overflow-hidden bg-indigo-50/80 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 hover:scale-105 hover:shadow-md transition-all duration-200 border border-indigo-200/50 dark:border-indigo-800/30 group"
                                    href={`/dashboard/models/${model.id}/edit`}
                                    size="sm"
                                    variant="flat"
                                >
                                    <span className="absolute inset-0 bg-gradient-to-r from-indigo-400/0 via-indigo-400/20 to-indigo-400/0 opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                                    <PencilSquareIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>

                            <Tooltip
                                color={isUsed ? "warning" : "danger"}
                                content={
                                    isUsed
                                        ? "No se puede eliminar (en uso)"
                                        : "Eliminar"
                                }
                                placement="top"
                            >
                                <Button
                                    isIconOnly
                                    className={`relative overflow-hidden ${isUsed ? "bg-amber-50/80 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400" : "bg-red-50/80 dark:bg-red-900/30 text-red-600 dark:text-red-400"} hover:scale-105 hover:shadow-md transition-all duration-200 border ${isUsed ? "border-amber-200/50 dark:border-amber-800/30" : "border-red-200/50 dark:border-red-800/30"} group`}
                                    isDisabled={isUsed}
                                    size="sm"
                                    variant="flat"
                                    onPress={() =>
                                        !isUsed && handleOpenDeleteModal(model)
                                    }
                                >
                                    <span
                                        className={`absolute inset-0 bg-gradient-to-r ${isUsed ? "from-amber-400/0 via-amber-400/20 to-amber-400/0" : "from-red-400/0 via-red-400/20 to-red-400/0"} opacity-0 group-hover:opacity-100 animate-shimmer transition-opacity duration-300`}
                                    />
                                    <TrashIcon className="w-4 h-4 relative z-10" />
                                </Button>
                            </Tooltip>
                        </div>
                    );

                default:
                    return model[columnKey]?.toString() || "-";
            }
        },
        [handleOpenDeleteModal, handleOpenDetailModal],
    );

    // Calcular paginación para la lista filtrada
    const { pagesCount, currentItems } = useMemo(() => {
        // Para paginación, usamos la información del servidor
        const totalPages =
            models.length > 0 ? Math.ceil(models.length / rowsPerPage) : 1;

        return {
            pagesCount: totalPages,
            currentItems: models,
        };
    }, [models, rowsPerPage]);

    return (
        <div className="max-w-7xl mx-auto p-4 sm:p-6">
            {/* Breadcrumbs mejorados */}
            <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex flex-col gap-2">
                    <nav className="flex items-center space-x-1 text-sm">
                        <Link
                            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                            href="/dashboard"
                        >
                            Dashboard
                        </Link>
                        <span className="text-gray-400 dark:text-gray-600">
                            /
                        </span>
                        <span className="text-gray-900 dark:text-white font-medium">
                            Modelos
                        </span>
                    </nav>
                    <h1 className="sr-only">Gestión de Modelos</h1>
                </div>

                <div className="flex items-center gap-2">
                    <Button
                        aria-label="Volver al dashboard"
                        as={Link}
                        className="text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                        href="/dashboard"
                        size="sm"
                        startContent={<ArrowLongLeftIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Volver
                    </Button>
                </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-sm dark:shadow-gray-800/30 border border-gray-100 dark:border-gray-800">
                {/* Header */}
                <div className="p-6 border-b border-gray-100 dark:border-gray-800">
                    <div className="flex flex-col md:flex-row justify-between gap-6">
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold flex items-center gap-3 text-gray-900 dark:text-white">
                                <div className="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                                    <DocumentTextIcon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                                </div>
                                Gestión de Modelos
                            </h1>
                            <p className="text-gray-500 dark:text-gray-400">
                                Administra los modelos de prendas disponibles en
                                el sistema
                            </p>
                        </div>

                        <Button
                            as={Link}
                            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:opacity-90 shadow-lg text-white dark:text-white"
                            color="primary"
                            href="/dashboard/models/new"
                            startContent={<PlusIcon className="w-4 h-4" />}
                        >
                            Nuevo Modelo
                        </Button>
                    </div>
                </div>

                {/* Dashboard Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-6">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-sm">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                    Total de Modelos
                                </p>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {statistics.totalModels}
                                </h3>
                            </div>
                            <div className="bg-blue-100 dark:bg-blue-800/50 p-2 rounded-lg">
                                <DocumentTextIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 p-4 rounded-xl border border-green-100 dark:border-green-800/30 shadow-sm">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-green-600 dark:text-green-400">
                                    Modelos en Uso
                                </p>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {statistics.usedModels}
                                </h3>
                            </div>
                            <div className="bg-green-100 dark:bg-green-800/50 p-2 rounded-lg">
                                <div className="min-w-[1.25rem] h-5 text-green-600 dark:text-green-400 flex items-center justify-center text-xs font-medium">
                                    {statistics.usedPercentage}%
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 p-4 rounded-xl border border-amber-100 dark:border-amber-800/30 shadow-sm">
                        <div className="flex justify-between items-start">
                            <div>
                                <p className="text-sm font-medium text-amber-600 dark:text-amber-400">
                                    Modelos sin Uso
                                </p>
                                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {statistics.unusedModels}
                                </h3>
                            </div>
                            <div className="bg-amber-100 dark:bg-amber-800/50 p-2 rounded-lg">
                                <div className="min-w-[1.25rem] h-5 text-amber-600 dark:text-amber-400 flex items-center justify-center text-xs font-medium">
                                    {statistics.unusedPercentage}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Controls */}
                <div className="p-6 border-t border-b border-gray-100 dark:border-gray-800">
                    <div className="flex flex-col md:flex-row gap-4 justify-between">
                        <div className="relative w-full max-w-lg">
                            <Input
                                isClearable
                                aria-label="Buscar modelos"
                                autoComplete="off"
                                classNames={{
                                    base: "max-w-full",
                                    mainWrapper: "h-10",
                                    input: "text-small pl-10 pr-16 h-10",
                                    inputWrapper:
                                        "h-10 font-normal bg-white dark:bg-gray-800 text-sm",
                                }}
                                id="searchInput"
                                placeholder="Buscar por código o descripción..."
                                radius="lg"
                                size="sm"
                                startContent={
                                    <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 dark:text-gray-500" />
                                }
                                value={searchTerm}
                                variant="bordered"
                                onClear={() => setSearchTerm("")}
                                onValueChange={handleSearch}
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 hidden md:flex items-center pointer-events-none">
                                <Kbd className="text-xs">Ctrl</Kbd>
                                <span className="mx-1 text-gray-400">+</span>
                                <Kbd className="text-xs">K</Kbd>
                            </div>
                        </div>

                        <div className="flex flex-wrap gap-3 items-center">
                            <Tooltip
                                content={
                                    isLoading
                                        ? "Actualizando..."
                                        : "Recargar datos"
                                }
                            >
                                <Button
                                    isIconOnly
                                    aria-label="Recargar datos"
                                    className="text-gray-700 dark:text-gray-300"
                                    isLoading={isLoading || isRevalidating}
                                    size="sm"
                                    variant="flat"
                                    onPress={refreshData}
                                >
                                    <ArrowPathIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>

                            <Dropdown>
                                <DropdownTrigger>
                                    <Button
                                        className="text-gray-700 dark:text-gray-300"
                                        endContent={
                                            <ChevronDownIcon className="w-4 h-4" />
                                        }
                                        variant="flat"
                                    >
                                        Mostrar: {rowsPerPage}
                                    </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                    disallowEmptySelection
                                    aria-label="Seleccionar registros por página"
                                    selectedKeys={
                                        new Set([rowsPerPage.toString()])
                                    }
                                    selectionMode="single"
                                    variant="flat"
                                    onAction={(key) => {
                                        const value = Number(key);

                                        if (!isNaN(value)) {
                                            setRowsPerPage(value);
                                            setCurrentPage(1);
                                        }
                                    }}
                                >
                                    {[5, 10, 20, 30, 40, 50].map((option) => (
                                        <DropdownItem
                                            key={option.toString()}
                                            textValue={option.toString()}
                                        >
                                            {option}
                                        </DropdownItem>
                                    ))}
                                </DropdownMenu>
                            </Dropdown>
                        </div>
                    </div>
                </div>

                {/* Table - with loading overlay */}
                <div className="relative">
                    {isLoading && (
                        <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 z-10 flex items-center justify-center">
                            <CubeSpinner text="Cargando modelos..." />
                        </div>
                    )}

                    <Table
                        aria-label="Tabla de modelos"
                        classNames={{
                            wrapper: "px-6 py-4 rounded-b-xl",
                            base: "border-0",
                            th: "bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 text-center border-b border-gray-100 dark:border-gray-700",
                            td: "py-4 group-hover:bg-gray-50 dark:group-hover:bg-gray-800/50 text-center text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-800",
                        }}
                    >
                        <TableHeader>
                            {COLUMNS.map((column) => (
                                <TableColumn
                                    key={column.uid}
                                    allowsSorting={false}
                                    className="text-sm font-semibold text-center"
                                >
                                    {column.name}
                                </TableColumn>
                            ))}
                        </TableHeader>

                        <TableBody
                            emptyContent={
                                isLoading ? (
                                    <div className="h-[200px] flex items-center justify-center">
                                        <CubeSpinner text="Cargando modelos..." />
                                    </div>
                                ) : (
                                    <div className="py-12 flex flex-col items-center justify-center text-center">
                                        <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-3 mb-4">
                                            <DocumentTextIcon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                                        </div>
                                        <p className="text-gray-500 dark:text-gray-400 mb-2">
                                            No se encontraron modelos
                                        </p>
                                        <Button
                                            as={Link}
                                            color="primary"
                                            href="/dashboard/models/new"
                                            size="sm"
                                            startContent={
                                                <PlusIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                        >
                                            Crear nuevo modelo
                                        </Button>
                                    </div>
                                )
                            }
                            items={currentItems}
                        >
                            {(item: Model) => (
                                <TableRow
                                    key={item.id}
                                    className="cursor-default group transition-colors"
                                >
                                    {(columnKey) => (
                                        <TableCell className="text-center">
                                            {renderCell(
                                                item,
                                                columnKey as ColumnKey,
                                            )}
                                        </TableCell>
                                    )}
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination with status */}
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 border-t border-gray-100 dark:border-gray-800">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                            Mostrando página {currentPage} de {pagesCount || 1}
                            {models.length > 0
                                ? ` (${models.length} total)`
                                : ""}
                        </span>
                    </div>

                    <Pagination
                        isCompact
                        showControls
                        classNames={{
                            item: "bg-transparent text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800",
                            cursor: "bg-gradient-to-r from-purple-600 to-blue-600 text-white dark:text-white",
                            prev: "bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 text-gray-600 dark:text-gray-300 hover:opacity-90",
                            next: "bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 text-gray-600 dark:text-gray-300 hover:opacity-90",
                        }}
                        page={currentPage}
                        total={pagesCount || 1}
                        onChange={setCurrentPage}
                    />
                </div>
            </div>

            {/* Delete Modal */}
            <Modal isOpen={isDeleteModalOpen} onClose={onDeleteModalClose}>
                <ModalContent className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                    <div className="p-6 text-center">
                        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30">
                            <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                            ¿Eliminar modelo permanentemente?
                        </h3>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            El modelo{" "}
                            <span className="font-semibold text-gray-700 dark:text-gray-300">
                                {modelToDelete?.code}
                            </span>{" "}
                            será eliminado permanentemente del sistema.
                        </p>
                    </div>
                    <div className="flex gap-3 justify-center p-6 pt-0">
                        <Button
                            className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                            variant="flat"
                            onPress={onDeleteModalClose}
                        >
                            Cancelar
                        </Button>
                        <Button
                            className="bg-gradient-to-r from-red-600 to-pink-600 text-white dark:text-white shadow-danger-md"
                            color="danger"
                            onPress={handleDeleteModel}
                        >
                            Confirmar Eliminación
                        </Button>
                    </div>
                </ModalContent>
            </Modal>

            {/* Detail Modal - Enhanced with copy button and better layout */}
            {selectedModel && (
                <Modal
                    isOpen={isDetailModalOpen}
                    size="lg"
                    onClose={onDetailModalClose}
                >
                    <ModalContent className="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                        <div className="p-6">
                            <div className="flex items-center mb-4">
                                <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg mr-3">
                                    <DocumentTextIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                                    Detalles del Modelo: {selectedModel.code}
                                </h3>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        ID
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedModel.id}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Código
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedModel.code}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Descripción
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedModel.description}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Fecha de Creación
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {format(
                                                new Date(
                                                    selectedModel.createdAt,
                                                ),
                                                "dd MMM yyyy",
                                                { locale: es },
                                            )}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Fecha de Actualización
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {format(
                                                new Date(
                                                    selectedModel.updatedAt,
                                                ),
                                                "dd MMM yyyy",
                                                { locale: es },
                                            )}
                                        </p>
                                    </div>
                                </div>
                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Uso
                                    </p>
                                    <div className="flex items-center gap-2">
                                        <p className="font-mono text-xs text-gray-800 dark:text-gray-300">
                                            {selectedModel._count?.garments ||
                                                0}{" "}
                                            prendas
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ModalContent>
                </Modal>
            )}

            {/* Mostrar spinner de carga condicional para revalidación */}
            {(isLoading || isRevalidating) && (
                <div className="absolute top-4 right-4">
                    <CubeSpinner
                        className="text-primary"
                        text="Actualizando..."
                    />
                </div>
            )}
        </div>
    );
};

export default ModelsContent;
