"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
    revalidateData,
} from "@/shared/utils/actions-utils";
import {
    extractMentions,
    extractTags,
} from "@/features/notes/utils/content-parser";

/**
 * Interface para los datos de actualización de una nota
 */
interface UpdateNoteData {
    id: string;
    content: string;
    orderId: string;
    statusId: string;
    importanceId: string;
}

/**
 * Actualiza una nota existente
 */
export async function updateNote(
    data: UpdateNoteData,
): Promise<ActionResponse<any>> {
    try {
        // Verificar sesión del usuario
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No autorizado: Debes iniciar sesión",
            };
        }

        // Obtener la nota actual para verificar permisos
        const existingNote = await prisma.note.findUnique({
            where: { id: data.id },
            select: {
                id: true,
                authorId: true,
            },
        });

        if (!existingNote) {
            return {
                success: false,
                error: "Nota no encontrada",
            };
        }

        // Verificar si el usuario es el autor de la nota o un administrador
        const isAuthor = existingNote.authorId === user.id;
        const isAdmin = (user.role as any)?.name === "ADMIN";

        if (!isAuthor && !isAdmin) {
            return {
                success: false,
                error: "No tienes permiso para editar esta nota",
            };
        }

        // Validar datos básicos
        if (!data.content.trim()) {
            return {
                success: false,
                error: "El contenido de la nota es obligatorio",
            };
        }

        if (!data.orderId) {
            return {
                success: false,
                error: "Debes seleccionar una orden",
            };
        }

        if (!data.statusId) {
            return {
                success: false,
                error: "Debes seleccionar un estado",
            };
        }

        if (!data.importanceId) {
            return {
                success: false,
                error: "Debes seleccionar una importancia",
            };
        }

        // Extraer menciones y tags del contenido
        const mentions = extractMentions(data.content);
        const tags = extractTags(data.content);

        // Actualizar la nota
        const updatedNote = await prisma.note.update({
            where: {
                id: data.id,
            },
            data: {
                content: data.content,
                orderId: data.orderId,
                statusId: data.statusId,
                importanceId: data.importanceId,
                mentions: mentions,
                tags: tags,
                updatedAt: new Date(),
            },
            include: {
                order: true,
                importance: true,
                status: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });

        // Revalidar rutas relacionadas
        revalidateData([
            "/dashboard/notes",
            `/dashboard/notes/${data.id}`,
            `/dashboard/notes/${data.id}/edit`,
            `/dashboard/orders/${data.orderId}`,
        ]);

        return {
            success: true,
            data: updatedNote,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
