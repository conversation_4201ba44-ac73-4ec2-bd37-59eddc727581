"use server";

import { PackagingSettings, SizeBreakdown } from "@/lib/types/packing";

interface OrderItem {
    size: string;
    quantity: number;
    qualityType: string;
}

interface PackagingCalculationResult {
    bySize: SizeBreakdown[];
    totals: {
        totalBoxes: number;
        totalBagsFirst: number;
        totalBagsSecond: number;
        totalLoosePieces: number;
    };
}

export async function calculatePackagingEnhanced(
    orderItems: OrderItem[],
    settings: PackagingSettings,
): Promise<PackagingCalculationResult> {
    try {
        // TODO: Group items by size
        const sizeMap = new Map<
            string,
            {
                totalQuantity: number;
                firstQuality: number;
                secondQuality: number;
                defective: number;
                incomplete: number;
            }
        >();

        // Process each item
        orderItems.forEach((item) => {
            const existing = sizeMap.get(item.size) || {
                totalQuantity: 0,
                firstQuality: 0,
                secondQuality: 0,
                defective: 0,
                incomplete: 0,
            };

            existing.totalQuantity += item.quantity;

            // Categorize by quality type
            switch (item.qualityType) {
                case "primera":
                    existing.firstQuality += item.quantity;
                    break;
                case "segunda":
                    existing.secondQuality += item.quantity;
                    break;
                case "manchada":
                    existing.defective += item.quantity;
                    break;
                case "incompleta":
                    existing.incomplete += item.quantity;
                    break;
            }

            sizeMap.set(item.size, existing);
        });

        // TODO: Calculate optimal box/bag distribution
        const sizeBreakdown: SizeBreakdown[] = [];
        let totalBoxes = 0;
        let totalBagsFirst = 0;
        let totalBagsSecond = 0;
        let totalLoosePieces = 0;

        // Sort sizes in standard order
        const sizeOrder = ["XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL"];
        const sortedSizes = Array.from(sizeMap.entries()).sort((a, b) => {
            const indexA = sizeOrder.indexOf(a[0]);
            const indexB = sizeOrder.indexOf(b[0]);

            return (
                (indexA === -1 ? 999 : indexA) - (indexB === -1 ? 999 : indexB)
            );
        });

        // Calculate packaging for each size
        sortedSizes.forEach(([size, quantities]) => {
            // Calculate boxes for first quality
            const boxesNeeded = Math.floor(
                quantities.firstQuality / settings.defaultPiecesPerBox,
            );
            const loosePiecesFirst =
                quantities.firstQuality % settings.defaultPiecesPerBox;

            // Calculate bags for loose pieces and second quality
            const bagsFirst = loosePiecesFirst > 0 ? 1 : 0;
            const bagsSecond = Math.ceil(
                quantities.secondQuality / settings.defaultPiecesPerBag,
            );

            const breakdown: SizeBreakdown = {
                size,
                totalQuantity: quantities.totalQuantity,
                firstQuality: quantities.firstQuality,
                secondQuality: quantities.secondQuality,
                boxes: boxesNeeded,
                loosePieces: loosePiecesFirst,
                bagsFirst,
                bagsSecond,
            };

            sizeBreakdown.push(breakdown);

            // Update totals
            totalBoxes += boxesNeeded;
            totalBagsFirst += bagsFirst;
            totalBagsSecond += bagsSecond;
            totalLoosePieces += loosePiecesFirst;
        });

        return {
            bySize: sizeBreakdown,
            totals: {
                totalBoxes,
                totalBagsFirst,
                totalBagsSecond,
                totalLoosePieces,
            },
        };
    } catch (error) {
        console.error("Error calculating packaging:", error);
        throw new Error("Error al calcular el empaque");
    }
}
