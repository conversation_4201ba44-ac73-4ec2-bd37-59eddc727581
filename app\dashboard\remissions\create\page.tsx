"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Divider,
    Chip,
    Progress,
    Skeleton,
    Avatar,
    Textarea,
    addToast,
} from "@heroui/react";
import {
    DocumentTextIcon,
    UserCircleIcon,
    ClipboardDocumentCheckIcon,
    ArrowRightIcon,
    ArrowLeftIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
    SparklesIcon,
} from "@heroicons/react/24/outline";

import { DashboardShell } from "@/shared/components/layout/Shell";
import { DashboardHeader } from "@/shared/components/layout/Header";
import { createRemission } from "@/features/remissions/actions";
// import { getAssignmentsByIds } from "@/features/assignments/actions/query";
import { getAssignmentsByIdsV2 as getAssignmentsByIds } from "@/features/assignments/actions/getAssignmentsByIdsV2";
import { getContractor } from "@/features/contractors/actions/query";
import RemissionOrderItems from "@/features/remissions/components/RemissionOrderItems";
import { SearchParamsWrapper } from "@/shared/components/ui";

// Loading skeleton component
const LoadingSkeleton = () => (
    <div className="space-y-6">
        <Skeleton className="h-32 w-full rounded-xl" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-48 w-full rounded-xl" />
            <Skeleton className="h-48 w-full rounded-xl" />
        </div>
        <Skeleton className="h-96 w-full rounded-xl" />
    </div>
);

// Main content component
function RemissionCreateContent() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [isLoading, setIsLoading] = useState(true);
    const [isCreating, setIsCreating] = useState(false);
    const [assignments, setAssignments] = useState<any[]>([]);
    const [contractor, setContractor] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);
    const [step, setStep] = useState<"review" | "creating" | "success">(
        "review",
    );
    const [createdRemission, setCreatedRemission] = useState<any>(null);
    const [notes, setNotes] = useState<string>("");

    // Get query parameters
    const assignmentIds = searchParams.get("assignmentIds")?.split(",") || [];
    const contractorId = searchParams.get("contractor") || "";

    useEffect(() => {
        const loadData = async () => {
            if (assignmentIds.length === 0 || !contractorId) {
                setError("Faltan parámetros necesarios");
                setIsLoading(false);

                return;
            }

            try {
                // Load assignments and contractor data in parallel
                const [assignmentsResult, contractorResult] = await Promise.all(
                    [
                        getAssignmentsByIds(assignmentIds),
                        getContractor(contractorId),
                    ],
                );

                if (!assignmentsResult.success || !contractorResult.success) {
                    setError("Error al cargar los datos");

                    return;
                }

                setAssignments(assignmentsResult.data?.assignments || []);
                setContractor(contractorResult.data);
            } catch (err) {
                setError("Error al cargar los datos");
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        };

        loadData();
    }, [assignmentIds, contractorId]);

    // Calculate totals
    const totalItems = assignments.reduce((sum, a) => sum + a.quantity, 0);
    const uniqueModels = new Set(
        assignments.map((a) => a.garmentSize?.garment?.model?.code),
    ).size;
    const uniqueSizes = new Set(
        assignments.map((a) => a.garmentSize?.size?.code),
    ).size;

    // Group assignments by order
    const orderGroups = React.useMemo(() => {
        const groups = assignments.reduce(
            (acc, assignment) => {
                const orderId = assignment.order?.id;

                if (!orderId) return acc;

                if (!acc[orderId]) {
                    acc[orderId] = {
                        orderId,
                        cutOrder:
                            assignment.order.cutOrder ||
                            `ID-${orderId.slice(-6)}`,
                        parts: assignment.order.parts || [],
                        assignments: [],
                    };
                }
                acc[orderId].assignments.push(assignment);

                return acc;
            },
            {} as Record<string, any>,
        );

        return Object.values(groups);
    }, [assignments]);

    const handleCreateRemission = async () => {
        setStep("creating");
        setIsCreating(true);

        try {
            // Prepare remission items from assignments
            const items = assignments.map((assignment) => ({
                modelCode: assignment.garmentSize?.garment?.model?.code || "",
                colorName: assignment.garmentSize?.garment?.color?.name || "",
                sizeCode: assignment.garmentSize?.size?.code || "",
                quantity: assignment.quantity,
            }));

            // Get order details from first assignment
            const firstOrder = assignments[0]?.order;
            const orderDetails = firstOrder
                ? {
                      id: firstOrder.id,
                      cutOrder: firstOrder.cutOrder,
                      creationDate: firstOrder.createdAt,
                      parts: firstOrder.parts || [],
                  }
                : undefined;

            const result = await createRemission({
                assignmentIds,
                contractorId,
                items,
                notes: notes || "", // Usar las observaciones ingresadas por el usuario
                orderDetails: orderDetails!,
            } as any);

            if (result) {
                setCreatedRemission(result);
                setStep("success");
                addToast({
                    title: "Éxito",
                    description: "Remisión creada exitosamente",
                    color: "success",
                    icon: <CheckCircleIcon className="w-5 h-5" />,
                });

                // Redirect to remission detail page after a delay
                setTimeout(() => {
                    router.push(`/dashboard/remissions/${result.id}`);
                }, 2000);
            } else {
                throw new Error("Error al crear la remisión");
            }
        } catch (err) {
            setError("Error al crear la remisión");
            setStep("review");
            addToast({
                title: "Error",
                description: "Error al crear la remisión",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
            console.error(err);
        } finally {
            setIsCreating(false);
        }
    };

    const springAnimation = {
        type: "spring",
        stiffness: 300,
        damping: 30,
    };

    if (isLoading) {
        return <LoadingSkeleton />;
    }

    if (error) {
        return (
            <Card className="max-w-2xl mx-auto">
                <CardBody className="py-12 text-center">
                    <ExclamationCircleIcon className="w-16 h-16 mx-auto mb-4 text-red-500" />
                    <h3 className="text-xl font-semibold mb-2">Error</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                        {error}
                    </p>
                    <Button
                        color="primary"
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={() => router.back()}
                    >
                        Volver
                    </Button>
                </CardBody>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Progress indicator */}
            <Card className="border-2 border-blue-100 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-900">
                <CardBody className="p-4">
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">
                            {step === "review" && "Revisión de remisión"}
                            {step === "creating" && "Creando remisión..."}
                            {step === "success" && "¡Remisión creada!"}
                        </span>
                        <Chip
                            color={step === "success" ? "success" : "primary"}
                            size="sm"
                            variant="flat"
                        >
                            {step === "review" && "1/2"}
                            {step === "creating" && "2/2"}
                            {step === "success" && "Completado"}
                        </Chip>
                    </div>
                    <Progress
                        className="mb-1"
                        color={step === "success" ? "success" : "primary"}
                        size="sm"
                        value={
                            step === "review"
                                ? 50
                                : step === "creating"
                                  ? 75
                                  : 100
                        }
                    />
                </CardBody>
            </Card>

            <AnimatePresence mode="wait">
                {step === "review" && (
                    <motion.div
                        key="review"
                        animate={{ opacity: 1, y: 0 }}
                        className="space-y-6"
                        exit={{ opacity: 0, y: -20 }}
                        initial={{ opacity: 0, y: 20 }}
                        transition={springAnimation}
                    >
                        {/* Header Card */}
                        <Card className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-none">
                            <CardBody className="p-8">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="p-4 bg-white/80 dark:bg-gray-800/80 rounded-2xl shadow-lg backdrop-blur-sm">
                                            <DocumentTextIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <div>
                                            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                                Nueva Remisión
                                            </h2>
                                            <p className="text-gray-600 dark:text-gray-400">
                                                Revisa los detalles antes de
                                                generar la remisión
                                            </p>
                                        </div>
                                    </div>
                                    <SparklesIcon className="w-8 h-8 text-purple-500 animate-pulse" />
                                </div>
                            </CardBody>
                        </Card>

                        {/* Contractor and Summary Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Contractor Card */}
                            <motion.div
                                animate={{ opacity: 1, x: 0 }}
                                initial={{ opacity: 0, x: -20 }}
                                transition={{ ...springAnimation, delay: 0.1 }}
                            >
                                <Card className="h-full hover:shadow-lg transition-shadow">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-2">
                                            <UserCircleIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                            <h3 className="text-lg font-semibold">
                                                Contratista
                                            </h3>
                                        </div>
                                    </CardHeader>
                                    <Divider />
                                    <CardBody className="pt-4">
                                        {contractor && (
                                            <div className="flex items-center gap-4">
                                                <Avatar
                                                    className="ring-2 ring-white dark:ring-gray-800"
                                                    name={contractor.name}
                                                    size="lg"
                                                />
                                                <div>
                                                    <p className="font-semibold text-lg">
                                                        {contractor.name}
                                                    </p>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                                        {contractor.email}
                                                    </p>
                                                    {contractor.phone && (
                                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                                            {contractor.phone}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </CardBody>
                                </Card>
                            </motion.div>

                            {/* Summary Card */}
                            <motion.div
                                animate={{ opacity: 1, x: 0 }}
                                initial={{ opacity: 0, x: 20 }}
                                transition={{ ...springAnimation, delay: 0.2 }}
                            >
                                <Card className="h-full hover:shadow-lg transition-shadow">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-2">
                                            <ClipboardDocumentCheckIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                            <h3 className="text-lg font-semibold">
                                                Resumen
                                            </h3>
                                        </div>
                                    </CardHeader>
                                    <Divider />
                                    <CardBody className="pt-4">
                                        <div className="space-y-3">
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600 dark:text-gray-400">
                                                    Total de prendas:
                                                </span>
                                                <span className="font-semibold text-lg">
                                                    {totalItems}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600 dark:text-gray-400">
                                                    Modelos únicos:
                                                </span>
                                                <span className="font-semibold">
                                                    {uniqueModels}
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <span className="text-gray-600 dark:text-gray-400">
                                                    Tallas diferentes:
                                                </span>
                                                <span className="font-semibold">
                                                    {uniqueSizes}
                                                </span>
                                            </div>
                                        </div>
                                    </CardBody>
                                </Card>
                            </motion.div>
                        </div>

                        {/* Observations Card */}
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ ...springAnimation, delay: 0.3 }}
                        >
                            <Card className="hover:shadow-lg transition-shadow">
                                <CardHeader className="pb-3">
                                    <div className="flex items-center gap-2">
                                        <DocumentTextIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                        <h3 className="text-lg font-semibold">
                                            Observaciones
                                        </h3>
                                    </div>
                                </CardHeader>
                                <Divider />
                                <CardBody>
                                    <Textarea
                                        classNames={{
                                            input: "resize-y",
                                            inputWrapper:
                                                "hover:border-primary",
                                        }}
                                        maxRows={6}
                                        minRows={3}
                                        placeholder="Agrega observaciones adicionales para esta remisión (opcional)"
                                        value={notes}
                                        variant="bordered"
                                        onChange={(e) =>
                                            setNotes(e.target.value)
                                        }
                                    />
                                </CardBody>
                            </Card>
                        </motion.div>

                        {/* Items Table */}
                        {orderGroups && orderGroups.length > 0 ? (
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                initial={{ opacity: 0, y: 20 }}
                                transition={{ ...springAnimation, delay: 0.4 }}
                            >
                                <RemissionOrderItems
                                    orderGroups={orderGroups as any}
                                    readOnly={true}
                                    showRowAnimations={true}
                                />
                            </motion.div>
                        ) : (
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                initial={{ opacity: 0, y: 20 }}
                                transition={{ ...springAnimation, delay: 0.4 }}
                            >
                                <Card className="p-8 text-center">
                                    <CardBody>
                                        <ExclamationCircleIcon className="w-12 h-12 mx-auto mb-4 text-warning" />
                                        <p className="text-default-500">
                                            No hay items para mostrar
                                        </p>
                                    </CardBody>
                                </Card>
                            </motion.div>
                        )}

                        {/* Action Buttons */}
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className="flex justify-between items-center"
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ ...springAnimation, delay: 0.6 }}
                        >
                            <Button
                                color="default"
                                size="lg"
                                startContent={
                                    <ArrowLeftIcon className="w-4 h-4" />
                                }
                                variant="flat"
                                onPress={() => router.back()}
                            >
                                Cancelar
                            </Button>

                            <Button
                                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                                color="primary"
                                endContent={
                                    <ArrowRightIcon className="w-4 h-4" />
                                }
                                size="lg"
                                onPress={handleCreateRemission}
                            >
                                Generar Remisión
                            </Button>
                        </motion.div>
                    </motion.div>
                )}

                {step === "creating" && (
                    <motion.div
                        key="creating"
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        initial={{ opacity: 0, scale: 0.9 }}
                        transition={springAnimation}
                    >
                        <Card className="max-w-md mx-auto">
                            <CardBody className="py-12 text-center">
                                <div className="relative w-24 h-24 mx-auto mb-6">
                                    <motion.div
                                        animate={{ rotate: 360 }}
                                        className="absolute inset-0"
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                            ease: "linear",
                                        }}
                                    >
                                        <DocumentTextIcon className="w-24 h-24 text-blue-500" />
                                    </motion.div>
                                    <motion.div
                                        animate={{ scale: [1, 1.2, 1] }}
                                        className="absolute inset-0 flex items-center justify-center"
                                        transition={{
                                            duration: 1.5,
                                            repeat: Infinity,
                                        }}
                                    >
                                        <SparklesIcon className="w-12 h-12 text-yellow-500" />
                                    </motion.div>
                                </div>
                                <h3 className="text-xl font-semibold mb-2">
                                    Generando remisión...
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400">
                                    Por favor espera un momento
                                </p>
                            </CardBody>
                        </Card>
                    </motion.div>
                )}

                {step === "success" && createdRemission && (
                    <motion.div
                        key="success"
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        initial={{ opacity: 0, scale: 0.9 }}
                        transition={springAnimation}
                    >
                        <Card className="max-w-md mx-auto border-2 border-green-200 dark:border-green-800">
                            <CardBody className="py-12 text-center">
                                <motion.div
                                    animate={{ scale: 1 }}
                                    initial={{ scale: 0 }}
                                    transition={{
                                        type: "spring",
                                        stiffness: 200,
                                        delay: 0.2,
                                    }}
                                >
                                    <CheckCircleIcon className="w-24 h-24 mx-auto mb-6 text-green-500" />
                                </motion.div>
                                <h3 className="text-2xl font-bold mb-2">
                                    ¡Remisión Creada!
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 mb-2">
                                    Folio:{" "}
                                    <span className="font-semibold">
                                        {createdRemission.folio}
                                    </span>
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-500">
                                    Redirigiendo a los detalles...
                                </p>
                            </CardBody>
                        </Card>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}

// Main page component with Suspense boundary
export default function RemissionCreatePage() {
    return (
        <DashboardShell>
            <DashboardHeader
                heading="Crear Remisión"
                text="Genera una remisión para las asignaciones seleccionadas"
            />
            <SearchParamsWrapper>
                <RemissionCreateContent />
            </SearchParamsWrapper>
        </DashboardShell>
    );
}
