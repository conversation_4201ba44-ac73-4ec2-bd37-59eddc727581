import type { RemissionFilters } from "../types";

import use<PERSON><PERSON> from "swr";

import { getRemission, getRemissionByFolio, listRemissions } from "../actions";

// SWR fetcher function that wraps server actions
const fetcher = (action: (...args: any[]) => Promise<any>) => {
    return async (...args: any[]) => {
        try {
            return await action(...args);
        } catch (error) {
            // REMOVED: console.error("SWR fetcher error:", error);
            throw error;
        }
    };
};

/**
 * Hook for fetching a single remission by ID
 */
export function useRemission(id: string | null) {
    return useSWR(
        id ? `remission/${id}` : null,
        () => (id ? fetcher(getRemission)(id) : null),
        {
            revalidateOnFocus: false,
            dedupingInterval: 10000, // 10 seconds
        },
    );
}

/**
 * Hook for fetching a remission by folio
 */
export function useRemissionByFolio(folio: string | null) {
    return useSWR(
        folio ? `remission/folio/${folio}` : null,
        () => (folio ? fetcher(getRemissionByFolio)(folio) : null),
        {
            revalidateOnFocus: false,
            dedupingInterval: 10000,
        },
    );
}

/**
 * Hook for fetching remissions with filters
 */
export function useRemissions(filters: RemissionFilters = {}) {
    // Create a stable key based on filters
    const filtersKey = JSON.stringify(filters);

    return useSWR(
        `remissions${filtersKey}`,
        () => fetcher(listRemissions)(filters),
        {
            revalidateOnFocus: true,
            dedupingInterval: 5000, // 5 seconds
        },
    );
}
