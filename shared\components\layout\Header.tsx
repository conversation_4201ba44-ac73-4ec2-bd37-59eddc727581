"use client";

import React from "react";
import { useEffect } from "react";

/**
 * Hook opcional para configuración del dashboard
 */
export function useDashboardConfig({
    title,
    description,
}: {
    title?: string;
    description?: string;
}) {
    useEffect(() => {
        if (title) {
            document.title = `${title} | Dashboard`;
        }
    }, [title]);

    // Aquí podrían agregarse más configuraciones
    return { title, description };
}

/**
 * Componente Header para el dashboard
 * Muestra un título, texto descriptivo opcional y acciones
 */
export function DashboardHeader({
    heading,
    text,
    children,
}: {
    heading: string;
    text?: string;
    children?: React.ReactNode;
}) {
    // Usar el hook opcional para configurar título de página, etc.
    useDashboardConfig({ title: heading, description: text });

    return (
        <div className="mb-8 lg:mb-10 pb-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                        {heading}
                    </h1>
                    {text && (
                        <p className="mt-2 text-base text-gray-500 dark:text-gray-400">
                            {text}
                        </p>
                    )}
                </div>
                <div className="flex items-center space-x-4">{children}</div>
            </div>
        </div>
    );
}
