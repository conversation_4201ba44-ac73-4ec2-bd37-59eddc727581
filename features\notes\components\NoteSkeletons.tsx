// Modern skeleton loaders with glassmorphism
"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardBody, Skeleton } from "@heroui/react";
import clsx from "clsx";

import { createGlassClass } from "@/shared/utils/ui/glass-morphism";
import { itemVariants } from "@/shared/utils/ui/animations";

interface NoteSkeletonProps {
    count?: number;
    className?: string;
}

export function NoteSkeleton({ className }: { className?: string }) {
    return (
        <Card
            className={clsx(createGlassClass(), "overflow-hidden", className)}
        >
            <CardBody className="p-4">
                {/* Header skeleton */}
                <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-3">
                        <Skeleton className="w-10 h-10 rounded-full" />
                        <div className="space-y-2">
                            <Skeleton className="w-32 h-4 rounded-lg" />
                            <Skeleton className="w-24 h-3 rounded-lg" />
                        </div>
                    </div>
                    <div className="flex gap-2">
                        <Skeleton className="w-20 h-6 rounded-full" />
                        <Skeleton className="w-16 h-6 rounded-full" />
                    </div>
                </div>

                {/* Content skeleton with animated gradient */}
                <div className="space-y-2">
                    <motion.div
                        animate={{
                            backgroundPosition: ["0% 0%", "200% 0%"],
                        }}
                        className="h-4 rounded-lg overflow-hidden"
                        style={{
                            background:
                                "linear-gradient(90deg, #e5e7eb 25%, #f3f4f6 50%, #e5e7eb 75%)",
                            backgroundSize: "200% 100%",
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                        }}
                    />
                    <motion.div
                        animate={{
                            backgroundPosition: ["0% 0%", "200% 0%"],
                        }}
                        className="h-4 rounded-lg overflow-hidden w-5/6"
                        style={{
                            background:
                                "linear-gradient(90deg, #e5e7eb 25%, #f3f4f6 50%, #e5e7eb 75%)",
                            backgroundSize: "200% 100%",
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                            delay: 0.2,
                        }}
                    />
                    <motion.div
                        animate={{
                            backgroundPosition: ["0% 0%", "200% 0%"],
                        }}
                        className="h-4 rounded-lg overflow-hidden w-4/6"
                        style={{
                            background:
                                "linear-gradient(90deg, #e5e7eb 25%, #f3f4f6 50%, #e5e7eb 75%)",
                            backgroundSize: "200% 100%",
                        }}
                        transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear",
                            delay: 0.4,
                        }}
                    />
                </div>

                {/* Footer skeleton */}
                <div className="mt-4 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
                    <Skeleton className="w-28 h-4 rounded-lg" />
                </div>
            </CardBody>
        </Card>
    );
}

export function NoteSkeletonList({ count = 3, className }: NoteSkeletonProps) {
    return (
        <motion.div
            animate="visible"
            className={clsx("space-y-4", className)}
            initial="hidden"
            variants={{
                hidden: { opacity: 0 },
                visible: {
                    opacity: 1,
                    transition: {
                        when: "beforeChildren",
                        staggerChildren: 0.1,
                    },
                },
            }}
        >
            {Array.from({ length: count }).map((_, index) => (
                <motion.div key={index} variants={itemVariants}>
                    <NoteSkeleton />
                </motion.div>
            ))}
        </motion.div>
    );
}

// Header skeleton for the notes section
export function NotesHeaderSkeleton() {
    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={clsx(
                "flex flex-col sm:flex-row justify-between gap-3 p-4 rounded-xl",
                createGlassClass(),
            )}
            initial={{ opacity: 0, y: -20 }}
        >
            <div className="flex items-center gap-3">
                <Skeleton className="w-8 h-8 rounded-lg" />
                <Skeleton className="w-20 h-6 rounded-lg" />
                <Skeleton className="w-8 h-6 rounded-full" />
            </div>
            <div className="flex gap-2">
                <Skeleton className="w-24 h-8 rounded-lg" />
                <Skeleton className="w-28 h-8 rounded-lg" />
            </div>
        </motion.div>
    );
}

// Enhanced skeleton with pulse glow effect
export function GlowingSkeleton({ className }: { className?: string }) {
    return (
        <motion.div
            animate={{
                boxShadow: [
                    "0 0 0 0 rgba(59, 130, 246, 0)",
                    "0 0 20px 10px rgba(59, 130, 246, 0.3)",
                    "0 0 0 0 rgba(59, 130, 246, 0)",
                ],
            }}
            className={clsx("relative", className)}
            transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
            }}
        >
            <Skeleton className={clsx("w-full h-full", className)} />
        </motion.div>
    );
}
