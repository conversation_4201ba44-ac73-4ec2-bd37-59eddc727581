import fs from "fs/promises";
import path from "path";

import { getRedisClient } from "../../cache/redis-client";

interface CacheMetrics {
    timestamp: Date;
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
    hitRate: number;
    avgResponseTime: number;
    keysByType: {
        STATIC: number;
        USER_SPECIFIC: number;
        COMPUTED: number;
        REAL_TIME: number;
    };
}

export class CacheMonitor {
    private redis: any;
    private metrics: CacheMetrics[] = [];

    constructor() {
        this.redis = getRedisClient();
    }

    async collectMetrics(): Promise<CacheMetrics> {
        const info = await this.redis.info("stats");
        const keysByType = await this.getKeysByType();

        // Parse Redis stats
        const stats = this.parseRedisStats(info);

        const metrics: CacheMetrics = {
            timestamp: new Date(),
            totalRequests: stats.totalCommands,
            cacheHits: stats.keyspaceHits,
            cacheMisses: stats.keyspaceMisses,
            hitRate: this.calculateHitRate(
                stats.keyspaceHits,
                stats.keyspaceMisses,
            ),
            avgResponseTime: stats.avgResponseTime,
            keysByType,
        };

        this.metrics.push(metrics);

        return metrics;
    }

    private async getKeysByType() {
        const types = ["STATIC", "USER_SPECIFIC", "COMPUTED", "REAL_TIME"];
        const result: any = {};

        for (const type of types) {
            const keys = await this.redis.smembers(`cache:type:${type}`);

            result[type] = keys.length;
        }

        return result;
    }

    private parseRedisStats(info: string): any {
        const lines = info.split("\r\n");
        const stats: any = {};

        lines.forEach((line) => {
            if (line.includes(":")) {
                const [key, value] = line.split(":");

                stats[key] = value;
            }
        });

        return {
            totalCommands: parseInt(stats.total_commands_processed || "0"),
            keyspaceHits: parseInt(stats.keyspace_hits || "0"),
            keyspaceMisses: parseInt(stats.keyspace_misses || "0"),
            avgResponseTime:
                parseFloat(stats.instantaneous_ops_per_sec || "0") / 1000,
        };
    }

    private calculateHitRate(hits: number, misses: number): number {
        const total = hits + misses;

        return total === 0 ? 0 : (hits / total) * 100;
    }

    async generateReport(): Promise<void> {
        const reportPath = path.join(
            process.cwd(),
            "features/performance-monitoring/testing/reports",
            `cache-metrics-${Date.now()}.json`,
        );

        await fs.writeFile(reportPath, JSON.stringify(this.metrics, null, 2));
        console.log(`Cache metrics report saved to: ${reportPath}`);
    }
}
