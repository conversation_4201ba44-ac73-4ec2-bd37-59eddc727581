// Feature flags configuration for LOHARI system
export const FEATURES = {
    // Campos extendidos de customer (phone, address, company)
    CUSTOMER_EXTENDED_FIELDS:
        process.env.NEXT_PUBLIC_EXTENDED_CUSTOMER === "true",

    // Mostrar deprecation warnings en console
    SHOW_DEPRECATION_WARNINGS: process.env.NODE_ENV === "development",

    // Mostrar card "próximamente" en OrderModal
    SHOW_COMING_SOON_CARD: true, // Siempre true por ahora

    // Rollout percentage (0-100)
    SCHEMA_ALIGNMENT_ROLLOUT: parseInt(
        process.env.NEXT_PUBLIC_ROLLOUT || "100",
    ),
} as const;

// Type for feature flags
export type FeatureFlag = keyof typeof FEATURES;
