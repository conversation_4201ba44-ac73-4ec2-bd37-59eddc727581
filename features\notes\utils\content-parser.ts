/**
 * Utilidad para parsear contenido de notas y detectar menciones (@usuario) y tags (#tag)
 */

export interface ParsedContent {
    formatted: string;
    mentions: string[];
    tags: string[];
    html: string;
}

/**
 * Parsea el contenido de una nota para detectar menciones y tags
 * @param content - El contenido de texto plano de la nota
 * @returns Objeto con el contenido formateado, lista de menciones y tags
 */
export function parseNoteContent(content: string): ParsedContent {
    if (!content) {
        return {
            formatted: "",
            mentions: [],
            tags: [],
            html: "",
        };
    }

    // Detectar menciones (@usuario)
    const mentionRegex = /@(\w+)/g;
    const mentions = Array.from(content.matchAll(mentionRegex)).map(
        (match) => match[1],
    );

    // Detectar tags (#tag)
    const tagRegex = /#(\w+)/g;
    const tags = Array.from(content.matchAll(tagRegex)).map(
        (match) => match[1],
    );

    // Escapar HTML para prevenir XSS
    const escapeHtml = (str: string) => {
        const div = document.createElement("div");

        div.textContent = str;

        return div.innerHTML;
    };

    // Formatear el contenido con spans para menciones y tags
    let html = escapeHtml(content);

    // Reemplazar menciones con spans estilizados
    html = html.replace(
        /@(\w+)/g,
        '<span class="mention" data-mention="$1">@$1</span>',
    );

    // Reemplazar tags con spans estilizados
    html = html.replace(
        /#(\w+)/g,
        '<span class="tag" data-tag="$1">#$1</span>',
    );

    // Mantener saltos de línea
    html = html.replace(/\n/g, "<br />");

    return {
        formatted: content,
        mentions: [...new Set(mentions)], // Eliminar duplicados
        tags: [...new Set(tags)], // Eliminar duplicados
        html,
    };
}

/**
 * Extrae solo las menciones de un texto
 */
export function extractMentions(content: string): string[] {
    const mentionRegex = /@(\w+)/g;
    const mentions = Array.from(content.matchAll(mentionRegex)).map(
        (match) => match[1],
    );

    return [...new Set(mentions)];
}

/**
 * Extrae solo los tags de un texto
 */
export function extractTags(content: string): string[] {
    const tagRegex = /#(\w+)/g;
    const tags = Array.from(content.matchAll(tagRegex)).map(
        (match) => match[1],
    );

    return [...new Set(tags)];
}

/**
 * Verifica si un texto contiene una mención específica
 */
export function containsMention(content: string, username: string): boolean {
    const mentions = extractMentions(content);

    return mentions.includes(username);
}

/**
 * Verifica si un texto contiene un tag específico
 */
export function containsTag(content: string, tag: string): boolean {
    const tags = extractTags(content);

    return tags.includes(tag);
}
