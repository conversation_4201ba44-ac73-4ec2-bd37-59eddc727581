#!/usr/bin/env node
// Script para generar template de handoff automáticamente

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 Generando template de handoff...\n');

try {
  // 1. Obtener branch actual
  const currentBranch = execSync('git branch --show-current', { encoding: 'utf-8' }).trim();
  
  // 2. Obtener archivos modificados
  const modifiedFiles = execSync('git diff --name-only', { encoding: 'utf-8' })
    .split('\n')
    .filter(f => f)
    .length;

  // 3. Obtener último commit
  const lastCommit = execSync('git log -1 --oneline', { encoding: 'utf-8' }).trim();

  // 4. Verificar si hay cambios sin commit
  const hasUncommitted = execSync('git status --porcelain', { encoding: 'utf-8' }).trim().length > 0;

  // 5. Obtener timestamp
  const timestamp = Date.now();
  const date = new Date().toLocaleString();

  // Generar template
  const template = `
# 🔄 Continuando trabajo en proyecto lohari

**Generado:** ${date}

## 📊 Estado actual del proyecto

- **Branch activo:** ${currentBranch}
- **Archivos modificados:** ${modifiedFiles}
- **Último commit:** ${lastCommit}
- **Cambios sin commit:** ${hasUncommitted ? 'Sí ⚠️' : 'No ✅'}

## 🚀 Para continuar en nueva conversación:

### Opción 1: Guardar contexto primero (recomendado)
\`\`\`bash
"AUTO-FLOW SAVE: [describe tu trabajo actual]"
\`\`\`

### Opción 2: Usar este comando directamente
\`\`\`
Hola! Soy el desarrollador del proyecto lohari.

Información del proyecto:
- Ruta: ${process.cwd()}
- Branch: ${currentBranch}
- Sistema: AUTO-FLOW V5.1 con Clear Thought MCP

Por favor ejecuta:
"AUTO-FLOW LOAD: recent"

O continúa directamente con:
"AUTO-FLOW: [mi siguiente tarea]"
\`\`\`

## 📋 Notas adicionales:
[Añade aquí cualquier contexto importante sobre lo que estabas haciendo]

---
*Template generado automáticamente por generate-handoff.js*
`;

  // Guardar en archivo
  const outputPath = path.join(__dirname, '..', 'HANDOFF_TEMPLATE_CURRENT.md');
  fs.writeFileSync(outputPath, template);

  console.log('✅ Template generado exitosamente!');
  console.log(`📄 Guardado en: ${outputPath}`);
  console.log('\n📋 Template (copia esto):\n');
  console.log(template);

  // También copiar al clipboard si es posible (opcional)
  try {
    if (process.platform === 'win32') {
      execSync(`echo ${template} | clip`);
      console.log('\n✅ ¡Template copiado al clipboard!');
    }
  } catch (e) {
    // Ignorar si no se puede copiar
  }

} catch (error) {
  console.error('❌ Error generando template:', error.message);
  process.exit(1);
}
