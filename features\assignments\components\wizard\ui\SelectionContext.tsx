"use client";

import { useCallback, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, Card, CardBody, Divider, Badge } from "@heroui/react";
import {
    ChevronRightIcon,
    ChevronLeftIcon,
    UserIcon,
    DocumentDuplicateIcon,
    CalculatorIcon,
} from "@heroicons/react/24/outline";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";

// Custom hook for responsive design
function useMediaQuery(query: string): boolean {
    const [matches, setMatches] = useState(false);

    useEffect(() => {
        if (typeof window !== "undefined") {
            const media = window.matchMedia(query);

            setMatches(media.matches);

            const listener = (e: MediaQueryListEvent) => {
                setMatches(e.matches);
            };

            media.addEventListener("change", listener);

            return () => media.removeEventListener("change", listener);
        }

        return undefined;
    }, [query]);

    return matches;
}

interface SelectionContextProps {
    className?: string;
}

export const SelectionContext = ({ className = "" }: SelectionContextProps) => {
    const {
        state: {
            contractor,
            contractorData,
            selectedOrders,
            assignments,
            currentStep,
        },
        nextStep,
        activeContractor,
        canGoNext,
    } = useWizard();

    const [isCollapsed, setIsCollapsed] = useState(false);
    const isMobile = useMediaQuery("(max-width: 768px)");

    // Auto-collapse on mobile, unless user has explicitly expanded
    useEffect(() => {
        if (isMobile) {
            setIsCollapsed(true);
        }
    }, [isMobile]);

    const toggleCollapse = useCallback(() => {
        setIsCollapsed((prev) => !prev);
    }, []);

    // Resumen de cantidades
    const totalQuantity = assignments.reduce((sum, a) => sum + a.quantity, 0);
    const garmentVarieties = new Set(assignments.map((a) => a.garmentId)).size;

    // Variantes para animación de contenido
    const contentVariants = {
        expanded: { opacity: 1, x: 0, width: "auto" },
        collapsed: { opacity: 0, x: 20, width: 0 },
    };

    // Determine current section highlight
    const getHighlightSection = () => {
        return currentStep;
    };

    const highlightedSection = getHighlightSection();

    // Manejar la acción de continuar
    const handleContinue = useCallback(() => {
        if (canGoNext()) {
            nextStep();
        }
    }, [canGoNext, nextStep]);

    return (
        <Card
            className={`shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}
            shadow="sm"
        >
            <CardBody className="p-0">
                <div className="flex items-stretch h-full">
                    {/* Botón de colapsar */}
                    <Button
                        isIconOnly
                        aria-label={
                            isCollapsed
                                ? "Expandir panel de contexto"
                                : "Colapsar panel de contexto"
                        }
                        className="h-auto rounded-none border-r border-gray-200 dark:border-gray-700"
                        color="default"
                        size="sm"
                        variant="light"
                        onClick={toggleCollapse}
                    >
                        {isCollapsed ? (
                            <ChevronRightIcon className="w-4 h-4" />
                        ) : (
                            <ChevronLeftIcon className="w-4 h-4" />
                        )}
                    </Button>

                    {/* Panel de contexto */}
                    <AnimatePresence initial={false}>
                        {!isCollapsed && (
                            <motion.div
                                animate="expanded"
                                className="flex-1 p-4 overflow-hidden"
                                exit="collapsed"
                                initial="collapsed"
                                transition={{ duration: 0.3 }}
                                variants={contentVariants}
                            >
                                <motion.h3
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3"
                                    initial={{ opacity: 0, y: -5 }}
                                    transition={{ delay: 0.1 }}
                                >
                                    Resumen de Asignación
                                </motion.h3>

                                {/* Contratista */}
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className={`mb-3 p-2 rounded-lg transition-colors ${
                                        highlightedSection === "contractor"
                                            ? "bg-blue-50 dark:bg-blue-900/20"
                                            : ""
                                    }`}
                                    initial={{ opacity: 0, y: 5 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    <div className="flex items-center text-sm mb-1">
                                        <UserIcon className="w-4 h-4 mr-2 text-gray-500" />
                                        <span className="text-gray-600 dark:text-gray-400 font-medium">
                                            Contratista
                                        </span>
                                    </div>

                                    {activeContractor ? (
                                        <div className="pl-6 text-sm">
                                            <span className="font-medium">
                                                {activeContractor.name}
                                            </span>
                                            {activeContractor.email && (
                                                <span className="text-xs text-gray-500 block truncate max-w-full">
                                                    {activeContractor.email}
                                                </span>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="pl-6 text-sm text-gray-400">
                                            No seleccionado
                                        </div>
                                    )}
                                </motion.div>

                                <Divider className="my-2" />

                                {/* Órdenes */}
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className={`mb-3 p-2 rounded-lg transition-colors ${
                                        highlightedSection === "orders"
                                            ? "bg-blue-50 dark:bg-blue-900/20"
                                            : ""
                                    }`}
                                    initial={{ opacity: 0, y: 5 }}
                                    transition={{ delay: 0.3 }}
                                >
                                    <div className="flex items-center text-sm mb-1">
                                        <DocumentDuplicateIcon className="w-4 h-4 mr-2 text-gray-500" />
                                        <span className="text-gray-600 dark:text-gray-400 font-medium">
                                            Órdenes
                                        </span>
                                        {selectedOrders.length > 0 && (
                                            <Badge
                                                className="ml-2"
                                                color="primary"
                                                size="sm"
                                                variant="flat"
                                            >
                                                {selectedOrders.length}
                                            </Badge>
                                        )}
                                    </div>

                                    {selectedOrders.length > 0 ? (
                                        <div className="pl-6 text-xs text-gray-500">
                                            {selectedOrders.length === 1
                                                ? "1 orden seleccionada"
                                                : `${selectedOrders.length} órdenes seleccionadas`}
                                        </div>
                                    ) : (
                                        <div className="pl-6 text-sm text-gray-400">
                                            No hay órdenes seleccionadas
                                        </div>
                                    )}
                                </motion.div>

                                <Divider className="my-2" />

                                {/* Cantidades */}
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className={`mb-2 p-2 rounded-lg transition-colors ${
                                        highlightedSection === "quantities" ||
                                        highlightedSection === "summary"
                                            ? "bg-blue-50 dark:bg-blue-900/20"
                                            : ""
                                    }`}
                                    initial={{ opacity: 0, y: 5 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <div className="flex items-center text-sm mb-1">
                                        <CalculatorIcon className="w-4 h-4 mr-2 text-gray-500" />
                                        <span className="text-gray-600 dark:text-gray-400 font-medium">
                                            Cantidades
                                        </span>
                                    </div>

                                    {assignments.length > 0 ? (
                                        <div className="pl-6 text-xs">
                                            <div className="flex justify-between mb-1">
                                                <span className="text-gray-500">
                                                    Total:
                                                </span>
                                                <span className="font-medium">
                                                    {totalQuantity} unidades
                                                </span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-gray-500">
                                                    Prendas:
                                                </span>
                                                <span className="font-medium">
                                                    {garmentVarieties}{" "}
                                                    variedades
                                                </span>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="pl-6 text-sm text-gray-400">
                                            No hay cantidades asignadas
                                        </div>
                                    )}
                                </motion.div>

                                {/* Visible only on desktop, a button to continue when fixed in sidebar */}
                                {!isMobile && currentStep !== "summary" && (
                                    <motion.div
                                        animate={{ opacity: 1 }}
                                        className="mt-6"
                                        initial={{ opacity: 0 }}
                                        transition={{ delay: 0.5 }}
                                    >
                                        <Button
                                            className="w-full mt-4"
                                            color="primary"
                                            isDisabled={!canGoNext()}
                                            size="sm"
                                            onPress={handleContinue}
                                        >
                                            Continuar
                                        </Button>
                                    </motion.div>
                                )}
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>
            </CardBody>
        </Card>
    );
};
