/**
 * Size DTOs (Data Transfer Objects)
 *
 * Define the shape of data for API requests and responses.
 * These DTOs ensure type safety and validation at the application boundary.
 */

import { z } from "zod";

/**
 * Base size DTO schema
 */
export const SizeBaseSchema = z.object({
    name: z
        .string()
        .min(1, "Name is required")
        .max(10, "Name cannot exceed 10 characters")
        .regex(
            /^[A-Za-z0-9\-\/]+$/,
            "Name can only contain letters, numbers, hyphens, and forward slashes",
        ),
    displayName: z
        .string()
        .min(1, "Display name is required")
        .max(50, "Display name cannot exceed 50 characters"),
    order: z
        .number()
        .int("Order must be an integer")
        .min(0, "Order cannot be negative")
        .max(9999, "Order cannot exceed 9999"),
    isActive: z.boolean().default(true),
    category: z.enum(["numeric", "alpha", "custom"]).optional(),
});

/**
 * Measurements schema
 */
export const MeasurementsSchema = z
    .object({
        chest: z.number().positive().optional(),
        waist: z.number().positive().optional(),
        hip: z.number().positive().optional(),
        length: z.number().positive().optional(),
    })
    .optional();

/**
 * Equivalents schema
 */
export const EquivalentsSchema = z
    .object({
        us: z.string().optional(),
        eu: z.string().optional(),
        uk: z.string().optional(),
        mx: z.string().optional(),
    })
    .optional();

/**
 * Create size DTO
 */
export const CreateSizeSchema = SizeBaseSchema.extend({
    measurements: MeasurementsSchema,
    equivalents: EquivalentsSchema,
});

export type CreateSizeDTO = z.infer<typeof CreateSizeSchema>;

/**
 * Update size DTO
 */
export const UpdateSizeSchema = SizeBaseSchema.partial().extend({
    measurements: MeasurementsSchema,
    equivalents: EquivalentsSchema,
});

export type UpdateSizeDTO = z.infer<typeof UpdateSizeSchema>;

/**
 * Size response DTO
 */
export const SizeResponseSchema = z.object({
    id: z.string(),
    name: z.string(),
    displayName: z.string(),
    order: z.number(),
    isActive: z.boolean(),
    category: z.enum(["numeric", "alpha", "custom"]),
    measurements: MeasurementsSchema.nullable(),
    equivalents: EquivalentsSchema.nullable(),
    createdAt: z.date().or(z.string()),
    updatedAt: z.date().or(z.string()),
});

export type SizeResponseDTO = z.infer<typeof SizeResponseSchema>;

/**
 * List sizes query DTO
 */
export const ListSizesQuerySchema = z.object({
    isActive: z
        .enum(["true", "false"])
        .transform((val) => val === "true")
        .optional(),
    category: z.enum(["numeric", "alpha", "custom"]).optional(),
    includeDeleted: z
        .enum(["true", "false"])
        .transform((val) => val === "true")
        .optional(),
    orderBy: z
        .enum(["order", "name", "createdAt", "updatedAt"])
        .default("order"),
    orderDirection: z.enum(["asc", "desc"]).default("asc"),
    limit: z
        .string()
        .transform(Number)
        .pipe(z.number().positive().max(100))
        .optional(),
    offset: z.string().transform(Number).pipe(z.number().min(0)).optional(),
});

export type ListSizesQueryDTO = z.infer<typeof ListSizesQuerySchema>;

/**
 * Reorder sizes DTO
 */
export const ReorderSizesSchema = z.object({
    sizes: z
        .array(
            z.object({
                id: z.string(),
                order: z.number().int().min(0),
            }),
        )
        .min(1, "At least one size must be provided"),
});

export type ReorderSizesDTO = z.infer<typeof ReorderSizesSchema>;

/**
 * Bulk create sizes DTO
 */
export const BulkCreateSizesSchema = z.object({
    sizes: z.array(CreateSizeSchema).min(1).max(100),
});

export type BulkCreateSizesDTO = z.infer<typeof BulkCreateSizesSchema>;

/**
 * Size exists query DTO
 */
export const SizeExistsQuerySchema = z.object({
    name: z.string(),
    excludeId: z.string().optional(),
});

export type SizeExistsQueryDTO = z.infer<typeof SizeExistsQuerySchema>;

/**
 * Paginated response DTO
 */
export const PaginatedSizesResponseSchema = z.object({
    data: z.array(SizeResponseSchema),
    total: z.number(),
    limit: z.number(),
    offset: z.number(),
    hasMore: z.boolean(),
});

export type PaginatedSizesResponseDTO = z.infer<
    typeof PaginatedSizesResponseSchema
>;

/**
 * Size validation error DTO
 */
export const SizeValidationErrorSchema = z.object({
    field: z.string(),
    message: z.string(),
    code: z.string().optional(),
});

export type SizeValidationErrorDTO = z.infer<typeof SizeValidationErrorSchema>;

/**
 * API Error response DTO
 */
export const ApiErrorResponseSchema = z.object({
    error: z.string(),
    message: z.string(),
    statusCode: z.number(),
    timestamp: z.string(),
    path: z.string().optional(),
    validationErrors: z.array(SizeValidationErrorSchema).optional(),
});

export type ApiErrorResponseDTO = z.infer<typeof ApiErrorResponseSchema>;

/**
 * Success response DTO
 */
export const SuccessResponseSchema = z.object({
    success: z.boolean(),
    message: z.string().optional(),
});

export type SuccessResponseDTO = z.infer<typeof SuccessResponseSchema>;

/**
 * Transform Size entity to response DTO
 */
export function toSizeResponse(size: {
    id?: string;
    name: string;
    displayName: string;
    order: number;
    isActive: boolean;
    category: "numeric" | "alpha" | "custom";
    measurements?: any;
    equivalents?: any;
    createdAt: Date;
    updatedAt: Date;
}): SizeResponseDTO {
    return {
        id: size.id || "",
        name: size.name,
        displayName: size.displayName,
        order: size.order,
        isActive: size.isActive,
        category: size.category,
        measurements: size.measurements || null,
        equivalents: size.equivalents || null,
        createdAt: size.createdAt,
        updatedAt: size.updatedAt,
    };
}
