"use client";

import React from "react";
import { Input, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from "@heroui/react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { Suggestion } from "../hooks/useAutocomplete";

interface AutocompleteInputProps {
    label: string;
    placeholder: string;
    value: string;
    onChange: (value: string) => void;
    suggestions: Suggestion[];
    isLoading: boolean;
    showSuggestions: boolean;
    selectedIndex: number;
    onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    onSelect: (suggestion: Suggestion) => void;
    onBlur: () => void;
    onFocus: () => void;
    inputRef: React.RefObject<HTMLInputElement>;
    icon?: React.ReactNode;
    renderSuggestion?: (suggestion: Suggestion) => React.ReactNode;
}

export function AutocompleteInput({
    label,
    placeholder,
    value,
    onChange,
    suggestions,
    isLoading,
    showSuggestions,
    selectedIndex,
    onKeyDown,
    onSelect,
    onBlur,
    onFocus,
    inputRef,
    icon = <MagnifyingGlassIcon className="w-4 h-4" />,
    renderSuggestion,
}: AutocompleteInputProps) {
    return (
        <div className="relative">
            <Input
                ref={inputRef}
                endContent={isLoading && <Spinner size="sm" />}
                label={label}
                placeholder={placeholder}
                startContent={icon}
                value={value}
                onBlur={onBlur}
                onFocus={onFocus}
                onKeyDown={onKeyDown}
                onValueChange={onChange}
            />

            <AnimatePresence>
                {showSuggestions && suggestions.length > 0 && (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className="absolute z-50 w-full mt-1"
                        exit={{ opacity: 0, y: -10 }}
                        initial={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.15 }}
                    >
                        <Card className="shadow-lg">
                            <CardBody className="p-0">
                                <ul className="divide-y divide-gray-100 dark:divide-gray-700">
                                    {suggestions.map((suggestion, index) => (
                                        <li key={suggestion.id}>
                                            <button
                                                className={`
                          w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800
                          transition-colors flex items-center justify-between
                          ${index === selectedIndex ? "bg-primary-50 dark:bg-primary-900/20" : ""}
                        `}
                                                type="button"
                                                onClick={() =>
                                                    onSelect(suggestion)
                                                }
                                            >
                                                <div className="flex-1">
                                                    {renderSuggestion ? (
                                                        renderSuggestion(
                                                            suggestion,
                                                        )
                                                    ) : (
                                                        <>
                                                            <div className="flex items-center gap-2">
                                                                <span className="font-medium">
                                                                    {
                                                                        suggestion.label
                                                                    }
                                                                </span>
                                                                {suggestion.type && (
                                                                    <Badge
                                                                        color={
                                                                            suggestion.type ===
                                                                            "cutOrder"
                                                                                ? "primary"
                                                                                : "secondary"
                                                                        }
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        {suggestion.type ===
                                                                        "cutOrder"
                                                                            ? "Orden de corte"
                                                                            : "Partida"}
                                                                    </Badge>
                                                                )}
                                                            </div>

                                                            {suggestion.metadata && (
                                                                <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                                                    {suggestion
                                                                        .metadata
                                                                        .customerName && (
                                                                        <span>
                                                                            Cliente:{" "}
                                                                            {
                                                                                suggestion
                                                                                    .metadata
                                                                                    .customerName
                                                                            }
                                                                        </span>
                                                                    )}
                                                                    {suggestion
                                                                        .metadata
                                                                        .modelCode && (
                                                                        <span>
                                                                            Modelo:{" "}
                                                                            {
                                                                                suggestion
                                                                                    .metadata
                                                                                    .modelCode
                                                                            }
                                                                        </span>
                                                                    )}
                                                                    {suggestion
                                                                        .metadata
                                                                        .orderDate && (
                                                                        <span>
                                                                            Fecha:{" "}
                                                                            {
                                                                                suggestion
                                                                                    .metadata
                                                                                    .orderDate
                                                                            }
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </>
                                                    )}
                                                </div>

                                                {index === selectedIndex && (
                                                    <span className="text-xs text-gray-400">
                                                        Enter para seleccionar
                                                    </span>
                                                )}
                                            </button>
                                        </li>
                                    ))}
                                </ul>
                            </CardBody>
                        </Card>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}
