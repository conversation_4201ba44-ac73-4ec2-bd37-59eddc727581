"use client";

import type { PackingSignaturesProps } from "../types";

import React from "react";

import { formatDate } from "../utils/transform";
import styles from "../PackingPreview.module.css";

export const PackingSignatures: React.FC<PackingSignaturesProps> = ({
    transport,
    receiver,
    editMode,
    onEdit,
}) => {
    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.currentTarget.textContent);
            }
        };

    return (
        <>
            {/* Transport Notes if any */}
            {transport.notes && (
                <div className={styles.transportNotes}>
                    <h3 className={styles.sectionTitle}>Notas de Transporte</h3>
                    <p
                        suppressContentEditableWarning
                        className={`${styles.notesText} ${editMode ? styles.editableField : ""}`}
                        contentEditable={editMode}
                        onBlur={handleEdit("transport.notes")}
                    >
                        {transport.notes}
                    </p>
                </div>
            )}

            {/* Signature Section */}
            <div className={styles.signatures}>
                <div className={styles.signatureBox}>
                    <div className={styles.signatureContainer}>
                        {transport.signature ? (
                            <div className={styles.signatureInfo}>
                                <p className={styles.signedName}>
                                    {transport.signature}
                                </p>
                                {transport.signedAt && (
                                    <p className={styles.signedDate}>
                                        {formatDate(
                                            transport.signedAt,
                                            "short",
                                        )}
                                    </p>
                                )}
                            </div>
                        ) : (
                            <div className={styles.signatureSpace} />
                        )}
                    </div>
                    <div className={styles.signatureLine} />
                    <p className={styles.signatureLabel}>Transportista</p>
                    <p className={styles.signatureSubLabel}>Nombre y Firma</p>
                </div>

                <div className={styles.signatureBox}>
                    <div className={styles.signatureContainer}>
                        {receiver.signature || receiver.name ? (
                            <div className={styles.signatureInfo}>
                                <p className={styles.signedName}>
                                    {receiver.name || receiver.signature}
                                </p>
                                {receiver.signedAt && (
                                    <p className={styles.signedDate}>
                                        {formatDate(receiver.signedAt, "short")}
                                    </p>
                                )}
                            </div>
                        ) : (
                            <div className={styles.signatureSpace} />
                        )}
                    </div>
                    <div className={styles.signatureLine} />
                    <p className={styles.signatureLabel}>Recibió Conformidad</p>
                    <p className={styles.signatureSubLabel}>Nombre y Firma</p>
                </div>
            </div>

            {/* Footer */}
            <footer className={styles.footer}>
                <p>
                    Este documento es una lista de empaque y no representa un
                    comprobante fiscal.
                </p>
                <p>Favor de verificar la mercancía al momento de recibirla.</p>
            </footer>
        </>
    );
};
