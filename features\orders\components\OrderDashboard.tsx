"use client";

import React, {
    useState,
    useMemo,
    useC<PERSON>back,
    useEffect,
    useRef,
} from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>er,
    Card<PERSON>ody,
    Card<PERSON>ooter,
    Button,
    Input,
    Chip,
    Pagination,
    ButtonGroup,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    ArrowPathIcon,
    ChevronDownIcon,
    EllipsisVerticalIcon,
    EyeIcon,
    PencilIcon,
    TrashIcon,
    UserGroupIcon,
    CalendarIcon,
    ChartBarIcon,
    FunnelIcon,
    XCircleIcon,
    DocumentTextIcon,
    InboxIcon,
    PlusCircleIcon,
    CheckBadgeIcon,
    Squares2X2Icon,
    TableCellsIcon,
    PlusIcon,
} from "@heroicons/react/24/outline";
import { differenceInDays } from "date-fns";
import Link from "next/link";
import { Select, SelectItem } from "@heroui/react";

// Importar componentes y tipos
import { Order } from "@/features/orders/types/orders";
import { DateDisplayType } from "@/shared/utils";

import { DateDisplay } from "./DateDisplay";
import { OrderStatusChip } from "./OrderStatusChip";
import MetricsDashboard from "./ContractorMetrics";
import FullCalendarView from "./FullCalendarView";

// Definir el contexto para los datos del dashboard
interface OrderDashboardContextType {
    orderStats: {
        total: number;
        new: number;
        inProcess: number;
        inProduction: number;
        delivered: number;
        canceled: number;
        overdue: number;
    };
}

// Crear un contexto básico para el componente
const OrderDashboardContext = React.createContext<
    OrderDashboardContextType | undefined
>(undefined);

// Hook para usar el contexto
const useOrderDashboardContext = () => {
    const context = React.useContext(OrderDashboardContext);

    if (context === undefined) {
        throw new Error(
            "useOrderDashboardContext debe ser usado dentro de un OrderDashboardProvider",
        );
    }

    return context;
};

// Provider del contexto
const OrderDashboardProvider: React.FC<{
    children: React.ReactNode;
    value: OrderDashboardContextType;
}> = ({ children, value }) => {
    return (
        <OrderDashboardContext.Provider value={value}>
            {children}
        </OrderDashboardContext.Provider>
    );
};

// Definición para SortDescriptor simplificada
interface SortDescriptor {
    column: string | null;
    direction: "ascending" | "descending";
}

// Opciones de estado para filtrado según los nuevos estados proporcionados
const statusOptions = [
    { name: "Todos", uid: "", iconName: "InboxIcon", color: "#6B7280" },
    {
        name: "Nuevo",
        uid: "cm9d2whlo000kfcac9tnr64lm",
        iconName: "PlusCircleIcon",
        color: "#3B82F6",
    },
    {
        name: "En Proceso",
        uid: "cm9d2whlo000lfcacx15zxcmq",
        iconName: "ArrowPathIcon",
        color: "#F59E0B",
    },
    {
        name: "En Producción",
        uid: "cm9d2whlo000mfcacnz8f2guw",
        iconName: "BuildingFactoryIcon",
        color: "#6366F1",
    },
    {
        name: "Listo para Envío",
        uid: "cm9d2whlo000nfcacr4vimys7",
        iconName: "TruckIcon",
        color: "#8B5CF6",
    },
    {
        name: "Enviado",
        uid: "cm9d2whlo000ofcacfnsasus3",
        iconName: "PaperAirplaneIcon",
        color: "#EC4899",
    },
    {
        name: "Entregado",
        uid: "cm9d2whlo000pfcacdepnz9ds",
        iconName: "CheckBadgeIcon",
        color: "#16A34A",
    },
    {
        name: "Cancelado",
        uid: "cm9d2whlo000qfcaczceezkai",
        iconName: "XCircleIcon",
        color: "#EF4444",
    },
    {
        name: "Retrasado",
        uid: "cm9d2whlo000rfcac795v84lp",
        iconName: "ClockIcon",
        color: "#D97706",
    },
];

// Definir el tipo TimeFilter
type TimeFilter = "7d" | "30d" | "90d" | "1y";

// Interfaces para tipos de props
interface OrderDashboardProps {
    orders: Order[];
    isLoading: boolean;
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
    onPageChange?: (page: number) => void;
    onPerPageChange?: (perPage: number) => void;
    perPage?: number;
    onSelectOrder?: (order: Order) => void;
    onEditOrder?: (order: Order) => void;
    onDeleteOrder?: (order: Order) => void;
    refreshData?: () => void;
    isRefreshing?: boolean;
    onViewOrder?: (order: Order) => void;
    onOpenDetail?: (order: Order) => void;
    // Nuevos props para comunicar cambios al padre
    onSortChange?: (
        column: string | null,
        direction: "ascending" | "descending",
    ) => void;
    onFilterChange?: (filter: string) => void;
    onSearchChange?: (search: string) => void;
    // Props para controlar el estado desde el padre
    searchValue?: string;
    filterValue?: string;
    initialSortColumn?: string | null;
    initialSortDirection?: "ascending" | "descending";
    viewMode?: "grid" | "list";
    onViewModeChange?: (mode: "grid" | "list") => void;
    // Prop para crear nueva orden
    onCreateNewOrder?: () => void;
}

export default function OrderDashboard({
    orders,
    isLoading,
    pagination,
    onPageChange,
    onPerPageChange,
    perPage = 10,
    onSelectOrder,
    onEditOrder,
    onDeleteOrder,
    refreshData,
    isRefreshing = false,
    onViewOrder,
    onOpenDetail,
    // Nuevos props
    onSortChange,
    onFilterChange,
    onSearchChange,
    searchValue,
    filterValue: externalFilterValue,
    initialSortColumn,
    initialSortDirection,
    viewMode: externalViewMode,
    onViewModeChange,
    onCreateNewOrder,
}: OrderDashboardProps) {
    // Estados locales del componente que podrían ser controlados externamente
    const [filterValue, setFilterValue] = useState(searchValue || "");
    const [debouncedFilterValue, setDebouncedFilterValue] = useState(
        searchValue || "",
    );
    const [statusFilter, setStatusFilter] = useState(externalFilterValue || "");
    const [viewMode, setViewMode] = useState(externalViewMode || "grid");
    const [page, setPage] = useState(pagination?.currentPage || 1);
    const [rowsPerPage, setRowsPerPage] = useState(perPage);
    const [isChangingStatus, setIsChangingStatus] = useState(false);
    const [activeTab, setActiveTab] = useState("orders");
    const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

    // Ordenamiento local - utilizando los props iniciales si están disponibles
    const [sortColumn, setSortColumn] = useState<string | null>(
        initialSortColumn || null,
    );
    const [sortDirection, setSortDirection] = useState<
        "ascending" | "descending"
    >(initialSortDirection || "ascending");

    // Manejar cambios en la prop search externa
    useEffect(() => {
        if (
            searchValue !== undefined &&
            searchValue !== debouncedFilterValue &&
            !isChangingStatus
        ) {
            setFilterValue(searchValue);
            setDebouncedFilterValue(searchValue);

            // Actualizar también el campo de búsqueda visible
            const searchInput = document.getElementById(
                "search-input",
            ) as HTMLInputElement;

            if (searchInput && searchInput.value !== searchValue) {
                searchInput.value = searchValue;
            }
        }
    }, [searchValue, debouncedFilterValue, isChangingStatus]);

    // Determinar si hay filtro de búsqueda usando el valor con debounce
    const hasSearchFilter = Boolean(debouncedFilterValue);

    // Elementos filtrados según criterios de búsqueda y estado
    const filteredItems = useMemo(() => {
        // Si estamos cargando datos, no realizar filtrado
        if (isLoading) return [];

        // Crear una nueva referencia solo si es necesario
        if (!hasSearchFilter && !statusFilter) {
            return orders;
        }

        let filteredOrders = [...orders];

        // Primero filtrar por búsqueda
        if (hasSearchFilter) {
            const searchTermLower = debouncedFilterValue.toLowerCase();

            filteredOrders = filteredOrders.filter((order) => {
                return (
                    // Optimizar búsqueda con comprobaciones directas
                    (order.cutOrder &&
                        order.cutOrder
                            .toLowerCase()
                            .includes(searchTermLower)) ||
                    (order.batch &&
                        order.batch.toLowerCase().includes(searchTermLower)) ||
                    (order.customer?.name &&
                        order.customer.name
                            .toLowerCase()
                            .includes(searchTermLower)) ||
                    (order.transferNumber &&
                        order.transferNumber
                            .toLowerCase()
                            .includes(searchTermLower))
                );
            });
        }

        // Luego filtrar por estado usando IDs en lugar de nombres
        if (statusFilter) {
            filteredOrders = filteredOrders.filter((order) => {
                if (!order.status) return false;

                // Filtrar por ID de estado directamente
                if (statusFilter) {
                    return (order.status as any).id === statusFilter;
                }

                return true;
            });
        }

        // Ordenar solo si hay columna seleccionada
        if (sortColumn) {
            filteredOrders.sort((a, b) => {
                let aValue: any;
                let bValue: any;

                // Manejar propiedades anidadas como status.name
                if (sortColumn.includes(".")) {
                    const [parentProp, childProp] = sortColumn.split(".");

                    aValue =
                        (a[parentProp as keyof Order] as any)?.[childProp] ||
                        "";
                    bValue =
                        (b[parentProp as keyof Order] as any)?.[childProp] ||
                        "";
                } else {
                    aValue = a[sortColumn as keyof Order] || "";
                    bValue = b[sortColumn as keyof Order] || "";
                }

                // Para fechas, hacer una comparación especial
                if (
                    sortColumn === "receivedDate" ||
                    sortColumn === "estimatedDeliveryDate"
                ) {
                    const dateA = aValue ? new Date(aValue).getTime() : 0;
                    const dateB = bValue ? new Date(bValue).getTime() : 0;

                    return sortDirection === "ascending"
                        ? dateA - dateB
                        : dateB - dateA;
                }

                // Para otros valores, convertir a string y comparar
                aValue = String(aValue).toLowerCase();
                bValue = String(bValue).toLowerCase();

                if (aValue < bValue)
                    return sortDirection === "ascending" ? -1 : 1;
                if (aValue > bValue)
                    return sortDirection === "ascending" ? 1 : -1;

                return 0;
            });
        }

        return filteredOrders;
    }, [
        orders,
        debouncedFilterValue,
        statusFilter,
        sortColumn,
        sortDirection,
        hasSearchFilter,
        isLoading,
    ]);

    // Utilizar directamente los datos de paginación del servidor en lugar de hacer paginación en el cliente
    const pages =
        pagination?.lastPage || Math.ceil(filteredItems.length / rowsPerPage);
    const items = orders; // Usar directamente las órdenes proporcionadas por el servidor

    // Estadísticas de órdenes para dashboard - optimizadas
    const orderStats = useMemo(() => {
        if (isLoading) {
            return {
                total: 0,
                new: 0,
                inProcess: 0,
                inProduction: 0,
                delivered: 0,
                canceled: 0,
                overdue: 0,
            };
        }

        // Calcular estadísticas en una sola pasada
        const stats = {
            total: filteredItems.length,
            new: 0,
            inProcess: 0,
            inProduction: 0,
            delivered: 0,
            canceled: 0,
            overdue: 0,
        };

        const today = new Date();

        filteredItems.forEach((order) => {
            const statusId = (order.status as any)?.id;

            // Clasificar según el ID del estado
            if (statusId === "cm9d2whlo000kfcac9tnr64lm")
                stats.new++; // Nuevo
            else if (statusId === "cm9d2whlo000lfcacx15zxcmq")
                stats.inProcess++; // En Proceso
            else if (statusId === "cm9d2whlo000mfcacnz8f2guw")
                stats.inProduction++; // En Producción
            else if (statusId === "cm9d2whlo000pfcacdepnz9ds")
                stats.delivered++; // Entregado
            else if (statusId === "cm9d2whlo000qfcaczceezkai") stats.canceled++; // Cancelado

            // Verificar si está vencida (órdenes con fecha de entrega pasada que no están entregadas ni canceladas)
            if (
                order.estimatedDeliveryDate &&
                statusId !== "cm9d2whlo000pfcacdepnz9ds" && // No está entregada
                statusId !== "cm9d2whlo000qfcaczceezkai"
            ) {
                // No está cancelada
                const deliveryDate = new Date(order.estimatedDeliveryDate);

                if (deliveryDate < today) stats.overdue++;
            }
        });

        return stats;
    }, [filteredItems, isLoading]);

    // Manejadores de eventos - modificados para resolver el problema con el input
    const handleSearchChange = (value: string) => {
        // 1. Primero actualizamos el estado local inmediatamente
        setFilterValue(value);

        // 2. Cancelamos cualquier debounce pendiente
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
            debounceTimeout.current = null;
        }

        // 3. Si el valor está vacío, actualizamos inmediatamente todo
        if (!value) {
            setDebouncedFilterValue("");
            setPage(1);
            // Notificar al padre sin esperar el debounce
            if (onSearchChange) {
                onSearchChange("");
            }
        } else {
            // 4. Para valores no vacíos, configuramos un nuevo debounce
            debounceTimeout.current = setTimeout(() => {
                // Solo si el input sigue teniendo el mismo valor
                const currentInputValue = (
                    document.getElementById("search-input") as HTMLInputElement
                )?.value;

                // Esto es importante para evitar que el estado se filtre al campo de búsqueda
                if (currentInputValue === value) {
                    setDebouncedFilterValue(value);
                    if (onSearchChange) {
                        onSearchChange(value);
                    }
                } else if (
                    currentInputValue &&
                    currentInputValue !== statusFilter
                ) {
                    // Si el valor cambió pero no es el filtro de estado, actualizamos
                    setDebouncedFilterValue(currentInputValue);
                    if (onSearchChange) {
                        onSearchChange(currentInputValue);
                    }
                }
            }, 300);
        }
    };

    const handleClearSearch = () => {
        // 1. Resetear los valores de estado primero
        setFilterValue("");
        setDebouncedFilterValue("");
        setPage(1);

        // 2. Verificar que el input realmente se limpie en el DOM
        const searchInput = document.getElementById(
            "search-input",
        ) as HTMLInputElement;

        if (searchInput) {
            searchInput.value = "";
            // Disparar un evento de cambio para asegurar que React registre el cambio
            const event = new Event("input", { bubbles: true });

            searchInput.dispatchEvent(event);
        }

        // 3. Cancelar cualquier debounce pendiente
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
            debounceTimeout.current = null;
        }

        // 4. Notificar al padre inmediatamente
        if (onSearchChange) {
            onSearchChange("");
        }

        // 5. Enfocar el campo para mejor UX
        setTimeout(() => {
            if (searchInput) {
                searchInput.focus();
            }
        }, 0);
    };

    // Manejador para el cambio de estado que NO modifica el valor de búsqueda
    const handleStatusFilterChange = (status: string) => {
        // Indicar que estamos cambiando el estado para evitar efectos secundarios
        setIsChangingStatus(true);

        // Actualizar solo el estado de filtro, no el valor de búsqueda
        setStatusFilter(status);
        setPage(1);

        // Guardar el valor actual del campo de búsqueda para restaurarlo después
        const currentSearchValue = debouncedFilterValue;

        // Notificar al padre sin modificar el valor de búsqueda
        onFilterChange?.(status);

        // Asegurarse de que el campo de búsqueda mantenga su valor
        // Esto es importante porque los cambios de URL podrían afectar el valor del campo
        setTimeout(() => {
            const searchInput = document.getElementById(
                "search-input",
            ) as HTMLInputElement;

            if (searchInput && searchInput.value !== currentSearchValue) {
                searchInput.value = currentSearchValue;
            }

            // Después de restaurar el valor, quitar la bandera
            setIsChangingStatus(false);
        }, 50);
    };

    // Manejador para cambios en ordenamiento - modificado para comunicar al padre
    const handleSortChange = (column: string) => {
        let newDirection: "ascending" | "descending";
        const newColumn: string | null = column;

        if (sortColumn === column) {
            // Si ya estamos ordenando por esta columna, cambiar dirección
            newDirection =
                sortDirection === "ascending" ? "descending" : "ascending";
        } else {
            // Si es una columna nueva, establecerla y reiniciar dirección
            newDirection = "ascending";
        }

        // Actualizar estado local
        setSortColumn(newColumn);
        setSortDirection(newDirection);

        // Notificar al padre
        onSortChange?.(newColumn, newDirection);
    };

    // Manejador para cambio de vista modificado para comunicar al padre
    const handleViewModeChange = (mode: "grid" | "list") => {
        setViewMode(mode);
        onViewModeChange?.(mode);
    };

    // Función para renderizar un encabezado ordenable
    const renderSortableHeader = (
        column: string,
        title: string,
        colSpan: number = 1,
    ) => {
        const isSorted = sortColumn === column;

        return (
            <div
                className={`col-span-${colSpan} flex items-center gap-1 cursor-pointer hover:text-gray-900 dark:hover:text-white transition-colors`}
                onClick={() => handleSortChange(column)}
            >
                <span>{title}</span>
                <div className="flex flex-col h-3 justify-center">
                    {isSorted ? (
                        <ChevronDownIcon
                            className={`h-3 w-3 ${sortDirection === "descending" ? "text-primary-500" : "text-gray-400"} transform ${sortDirection === "ascending" ? "rotate-180" : ""}`}
                        />
                    ) : (
                        <ChevronDownIcon className="h-3 w-3 text-gray-400 opacity-50" />
                    )}
                </div>
            </div>
        );
    };

    // Renderizar tarjetas de órdenes
    const renderOrderCard = useCallback(
        (order: Order) => {
            // Calcular días hasta entrega o vencimiento
            const daysUntilDelivery = order.estimatedDeliveryDate
                ? differenceInDays(
                      new Date(order.estimatedDeliveryDate),
                      new Date(),
                  )
                : null;

            const isDue = daysUntilDelivery !== null && daysUntilDelivery < 0;
            const isCloseToDelivery =
                daysUntilDelivery !== null &&
                daysUntilDelivery >= 0 &&
                daysUntilDelivery <= 3;

            return (
                <Card
                    key={order.id}
                    className={`border-l-4 ${isDue ? "border-l-danger" : isCloseToDelivery ? "border-l-warning" : "border-l-primary"} shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer`}
                    onClick={() => onOpenDetail?.(order)}
                >
                    <CardHeader className="flex justify-between bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 py-3">
                        <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-2">
                                <span className="text-lg font-bold">
                                    Orden de Trabajo:{" "}
                                    {order.cutOrder || "Sin código"}
                                </span>
                            </div>
                            <p className="text-sm text-gray-500">
                                {order.batch || "Sin lote"}
                            </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                            {order.status && (
                                <OrderStatusChip
                                    showIcon
                                    size="sm"
                                    status={order.status as any}
                                />
                            )}
                            <div className="relative">
                                <div
                                    aria-label="Acciones"
                                    className="text-default-500 cursor-pointer p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                                    role="button"
                                    tabIndex={0}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        const menu = document.getElementById(
                                            `order-menu-${order.id}`,
                                        );

                                        if (menu) {
                                            menu.classList.toggle("hidden");
                                        }
                                    }}
                                    onKeyDown={(e) => {
                                        if (
                                            e.key === "Enter" ||
                                            e.key === " "
                                        ) {
                                            e.preventDefault();
                                            const menu =
                                                document.getElementById(
                                                    `order-menu-${order.id}`,
                                                );

                                            if (menu) {
                                                menu.classList.toggle("hidden");
                                            }
                                        }
                                    }}
                                >
                                    <EllipsisVerticalIcon className="w-4 h-4" />
                                </div>

                                <div
                                    className="absolute right-0 top-full mt-1 w-48 bg-white shadow-lg rounded-lg z-10 hidden"
                                    id={`order-menu-${order.id}`}
                                >
                                    <div className="py-1">
                                        {onOpenDetail && (
                                            <div
                                                aria-label="Ver detalles de la orden"
                                                className="flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 cursor-pointer"
                                                role="button"
                                                tabIndex={0}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    document
                                                        .getElementById(
                                                            `order-menu-${order.id}`,
                                                        )
                                                        ?.classList.add(
                                                            "hidden",
                                                        );
                                                    onOpenDetail(order);
                                                }}
                                                onKeyDown={(e) => {
                                                    if (
                                                        e.key === "Enter" ||
                                                        e.key === " "
                                                    ) {
                                                        e.preventDefault();
                                                        document
                                                            .getElementById(
                                                                `order-menu-${order.id}`,
                                                            )
                                                            ?.classList.add(
                                                                "hidden",
                                                            );
                                                        onOpenDetail(order);
                                                    }
                                                }}
                                            >
                                                <EyeIcon className="w-4 h-4 mr-2" />
                                                Ver detalles
                                            </div>
                                        )}

                                        {onEditOrder && (
                                            <div
                                                aria-label="Editar orden"
                                                className="flex items-center px-4 py-2 text-sm w-full text-left hover:bg-gray-100 cursor-pointer"
                                                role="button"
                                                tabIndex={0}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    document
                                                        .getElementById(
                                                            `order-menu-${order.id}`,
                                                        )
                                                        ?.classList.add(
                                                            "hidden",
                                                        );
                                                    onEditOrder(order);
                                                }}
                                                onKeyDown={(e) => {
                                                    if (
                                                        e.key === "Enter" ||
                                                        e.key === " "
                                                    ) {
                                                        e.preventDefault();
                                                        document
                                                            .getElementById(
                                                                `order-menu-${order.id}`,
                                                            )
                                                            ?.classList.add(
                                                                "hidden",
                                                            );
                                                        onEditOrder(order);
                                                    }
                                                }}
                                            >
                                                <PencilIcon className="w-4 h-4 mr-2" />
                                                Editar
                                            </div>
                                        )}

                                        {onDeleteOrder && (
                                            <div
                                                aria-label="Eliminar orden"
                                                className="flex items-center px-4 py-2 text-sm w-full text-left text-red-600 hover:bg-gray-100 cursor-pointer"
                                                role="button"
                                                tabIndex={0}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    document
                                                        .getElementById(
                                                            `order-menu-${order.id}`,
                                                        )
                                                        ?.classList.add(
                                                            "hidden",
                                                        );
                                                    onDeleteOrder(order);
                                                }}
                                                onKeyDown={(e) => {
                                                    if (
                                                        e.key === "Enter" ||
                                                        e.key === " "
                                                    ) {
                                                        e.preventDefault();
                                                        document
                                                            .getElementById(
                                                                `order-menu-${order.id}`,
                                                            )
                                                            ?.classList.add(
                                                                "hidden",
                                                            );
                                                        onDeleteOrder(order);
                                                    }
                                                }}
                                            >
                                                <TrashIcon className="w-4 h-4 mr-2" />
                                                Eliminar
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardBody className="py-3">
                        <div className="grid grid-cols-2 gap-3">
                            <div>
                                <p className="text-xs font-medium text-gray-500">
                                    Cliente
                                </p>
                                <p className="font-medium">
                                    {order.customer?.name || "Sin cliente"}
                                </p>
                            </div>
                            <div>
                                <p className="text-xs font-medium text-gray-500">
                                    Fecha Entrega
                                </p>
                                {order.estimatedDeliveryDate ? (
                                    <DateDisplay
                                        chipSize="sm"
                                        date={order.estimatedDeliveryDate}
                                        showRelative={true}
                                        type={DateDisplayType.estimated}
                                    />
                                ) : (
                                    <span className="text-gray-500 text-xs">
                                        Sin fecha
                                    </span>
                                )}
                            </div>
                            <div>
                                <p className="text-xs font-medium text-gray-500">
                                    Partidas
                                </p>
                                <div className="flex flex-wrap gap-1 mt-1">
                                    {order.parts && order.parts.length > 0 ? (
                                        order.parts.map((part) => (
                                            <Chip
                                                key={part.id}
                                                color="primary"
                                                size="sm"
                                                variant="flat"
                                            >
                                                {part.code}
                                            </Chip>
                                        ))
                                    ) : order._count?.parts &&
                                      order._count.parts > 0 ? (
                                        <Chip
                                            color="primary"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {order._count.parts} partidas
                                        </Chip>
                                    ) : (
                                        <span className="text-sm text-gray-400">
                                            Sin partidas
                                        </span>
                                    )}
                                </div>
                            </div>
                            <div>
                                <p className="text-xs font-medium text-gray-500">
                                    Asignaciones
                                </p>
                                <div className="flex flex-wrap gap-1 mt-1">
                                    {order.assignments &&
                                    order.assignments.length > 0 ? (
                                        <Chip
                                            color="success"
                                            size="sm"
                                            variant="flat"
                                        >
                                            {order.assignments.length}{" "}
                                            asignaciones
                                        </Chip>
                                    ) : (
                                        <span className="text-sm text-gray-400">
                                            Sin asignar
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </CardBody>
                    <CardFooter className="flex justify-between py-2 border-t">
                        <div className="flex flex-col">
                            <span className="text-xs text-gray-500">
                                Recibido:
                            </span>
                            {order.receivedDate ? (
                                <DateDisplay
                                    className="text-xs"
                                    date={order.receivedDate}
                                    format="dd MMM yyyy"
                                    showIcon={false}
                                    showRelative={false}
                                    type={DateDisplayType.regular}
                                />
                            ) : (
                                <span className="text-xs text-gray-400">
                                    Pendiente
                                </span>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <div
                                aria-label="Ver detalles"
                                className="text-default-500 cursor-pointer p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                                role="button"
                                tabIndex={0}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onOpenDetail?.(order);
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter" || e.key === " ") {
                                        e.preventDefault();
                                        onOpenDetail?.(order);
                                    }
                                }}
                            >
                                <EyeIcon className="w-4 h-4" />
                            </div>
                            <div
                                aria-label="Editar orden"
                                className="text-default-500 cursor-pointer p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                                role="button"
                                tabIndex={0}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onEditOrder?.(order);
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter" || e.key === " ") {
                                        e.preventDefault();
                                        onEditOrder?.(order);
                                    }
                                }}
                            >
                                <PencilIcon className="w-4 h-4" />
                            </div>
                        </div>
                    </CardFooter>
                </Card>
            );
        },
        [onOpenDetail, onEditOrder, onDeleteOrder],
    );

    // Contenido para la vista de órdenes
    const OrdersTabContent = () => (
        <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
                <Card className="bg-blue-50 dark:bg-blue-900/20 shadow-sm">
                    <CardBody className="py-3">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-500">
                                    Total Órdenes
                                </p>
                                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {orderStats.total}
                                </p>
                            </div>
                            <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                                <InboxIcon className="w-6 h-6 text-blue-600 dark:text-blue-300" />
                            </div>
                        </div>
                    </CardBody>
                </Card>

                <Card className="bg-blue-50 dark:bg-blue-900/20 shadow-sm">
                    <CardBody className="py-3">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-500">Nuevo</p>
                                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {orderStats.new}
                                </p>
                            </div>
                            <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                                <PlusCircleIcon className="w-6 h-6 text-blue-600 dark:text-blue-300" />
                            </div>
                        </div>
                    </CardBody>
                </Card>

                <Card className="bg-amber-50 dark:bg-amber-900/20 shadow-sm">
                    <CardBody className="py-3">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-500">
                                    En Proceso
                                </p>
                                <p className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                                    {orderStats.inProcess}
                                </p>
                            </div>
                            <div className="p-2 bg-amber-100 dark:bg-amber-800 rounded-lg">
                                <ArrowPathIcon className="w-6 h-6 text-amber-600 dark:text-amber-300" />
                            </div>
                        </div>
                    </CardBody>
                </Card>

                <Card className="bg-indigo-50 dark:bg-indigo-900/20 shadow-sm">
                    <CardBody className="py-3">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-500">
                                    En Producción
                                </p>
                                <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                                    {orderStats.inProduction}
                                </p>
                            </div>
                            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg">
                                <DocumentTextIcon className="w-6 h-6 text-indigo-600 dark:text-indigo-300" />
                            </div>
                        </div>
                    </CardBody>
                </Card>

                <Card className="bg-green-50 dark:bg-green-900/20 shadow-sm">
                    <CardBody className="py-3">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-500">
                                    Entregado
                                </p>
                                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {orderStats.delivered}
                                </p>
                            </div>
                            <div className="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                                <CheckBadgeIcon className="w-6 h-6 text-green-600 dark:text-green-300" />
                            </div>
                        </div>
                    </CardBody>
                </Card>

                <Card className="bg-red-50 dark:bg-red-900/20 shadow-sm">
                    <CardBody className="py-3">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-sm text-gray-500">
                                    Cancelado
                                </p>
                                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                                    {orderStats.canceled}
                                </p>
                            </div>
                            <div className="p-2 bg-red-100 dark:bg-red-800 rounded-lg">
                                <XCircleIcon className="w-6 h-6 text-red-600 dark:text-red-300" />
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </div>

            {viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {isLoading ? (
                        Array.from({ length: 6 }).map((_, index) => (
                            <Card
                                key={`skeleton-${index}`}
                                className="opacity-50"
                            >
                                <CardHeader className="h-16 bg-gray-100 dark:bg-gray-800" />
                                <CardBody className="h-36 py-3" />
                                <CardFooter className="h-12 py-2 border-t" />
                            </Card>
                        ))
                    ) : items.length > 0 ? (
                        items.map((order) => renderOrderCard(order))
                    ) : (
                        <div className="col-span-3 flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                            <InboxIcon className="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600" />
                            <p className="text-xl font-medium">
                                No hay órdenes para mostrar
                            </p>
                            <p className="text-sm mt-2">
                                Intenta cambiar los filtros o crea una nueva
                                orden
                            </p>
                        </div>
                    )}
                </div>
            ) : (
                <Card className="shadow-md">
                    <CardBody className="p-0">
                        <div className="flex flex-col divide-y">
                            <div className="grid grid-cols-12 bg-gray-50 dark:bg-gray-800 px-4 py-2 font-medium text-xs uppercase text-gray-600 dark:text-gray-300">
                                {renderSortableHeader(
                                    "cutOrder",
                                    "Orden de trabajo / Cliente",
                                    3,
                                )}
                                <div className="col-span-2">Partidas</div>
                                {renderSortableHeader(
                                    "status.name",
                                    "Estado",
                                    2,
                                )}
                                {renderSortableHeader(
                                    "receivedDate",
                                    "Recibido",
                                    2,
                                )}
                                {renderSortableHeader(
                                    "estimatedDeliveryDate",
                                    "Entrega",
                                    2,
                                )}
                                <div className="col-span-1 text-right">
                                    Acciones
                                </div>
                            </div>

                            {isLoading ? (
                                Array.from({ length: 5 }).map((_, index) => (
                                    <div
                                        key={`list-skeleton-${index}`}
                                        className="grid grid-cols-12 px-4 py-3 opacity-50"
                                    >
                                        <div className="col-span-3 h-10 bg-gray-100 dark:bg-gray-800 rounded" />
                                        <div className="col-span-2 h-6 bg-gray-100 dark:bg-gray-800 rounded" />
                                        <div className="col-span-2 h-6 bg-gray-100 dark:bg-gray-800 rounded" />
                                        <div className="col-span-2 h-6 bg-gray-100 dark:bg-gray-800 rounded" />
                                        <div className="col-span-2 h-6 bg-gray-100 dark:bg-gray-800 rounded" />
                                        <div className="col-span-1 h-6 bg-gray-100 dark:bg-gray-800 rounded" />
                                    </div>
                                ))
                            ) : items.length > 0 ? (
                                items.map((order) => (
                                    <div
                                        key={order.id}
                                        className="grid grid-cols-12 px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                                        onClick={() => onOpenDetail?.(order)}
                                    >
                                        <div className="col-span-3">
                                            <div className="font-medium">
                                                {order.cutOrder || "Sin código"}
                                            </div>
                                            <div className="text-xs text-gray-500">
                                                {order.customer?.name ||
                                                    "Sin cliente"}
                                            </div>
                                        </div>
                                        <div className="col-span-2 flex items-center">
                                            {order.parts &&
                                            order.parts.length > 0 ? (
                                                <div className="flex flex-wrap gap-1">
                                                    {order.parts
                                                        .slice(0, 2)
                                                        .map((part) => (
                                                            <Chip
                                                                key={part.id}
                                                                color="primary"
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {part.code}
                                                            </Chip>
                                                        ))}
                                                    {order.parts.length > 2 && (
                                                        <Chip
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            +
                                                            {order.parts
                                                                .length - 2}
                                                        </Chip>
                                                    )}
                                                </div>
                                            ) : order._count?.parts &&
                                              order._count.parts > 0 ? (
                                                <Chip
                                                    color="primary"
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    {order._count.parts}
                                                </Chip>
                                            ) : (
                                                <span className="text-sm text-gray-400">
                                                    —
                                                </span>
                                            )}
                                        </div>
                                        <div className="col-span-2 flex items-center">
                                            {order.status && (
                                                <OrderStatusChip
                                                    showIcon
                                                    size="sm"
                                                    status={order.status as any}
                                                />
                                            )}
                                        </div>
                                        <div className="col-span-2 flex items-center">
                                            {order.receivedDate ? (
                                                <span className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                                    <DateDisplay
                                                        date={
                                                            order.receivedDate
                                                        }
                                                        format="dd MMM yyyy"
                                                        showRelative={false}
                                                        type={
                                                            DateDisplayType.regular
                                                        }
                                                    />
                                                </span>
                                            ) : (
                                                <span className="text-sm text-gray-400">
                                                    —
                                                </span>
                                            )}
                                        </div>
                                        <div className="col-span-2 flex items-center">
                                            {order.estimatedDeliveryDate ? (
                                                <DateDisplay
                                                    chipSize="sm"
                                                    date={
                                                        order.estimatedDeliveryDate
                                                    }
                                                    showRelative={true}
                                                    type={
                                                        DateDisplayType.estimated
                                                    }
                                                />
                                            ) : (
                                                <span className="text-sm text-gray-400">
                                                    —
                                                </span>
                                            )}
                                        </div>
                                        <div className="col-span-1 flex justify-end items-center gap-1">
                                            <div
                                                aria-label="Ver detalles"
                                                className="text-default-500 cursor-pointer p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                                                role="button"
                                                tabIndex={0}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onOpenDetail?.(order);
                                                }}
                                                onKeyDown={(e) => {
                                                    if (
                                                        e.key === "Enter" ||
                                                        e.key === " "
                                                    ) {
                                                        e.preventDefault();
                                                        onOpenDetail?.(order);
                                                    }
                                                }}
                                            >
                                                <EyeIcon className="w-4 h-4" />
                                            </div>
                                            <div
                                                aria-label="Editar orden"
                                                className="text-default-500 cursor-pointer p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
                                                role="button"
                                                tabIndex={0}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onEditOrder?.(order);
                                                }}
                                                onKeyDown={(e) => {
                                                    if (
                                                        e.key === "Enter" ||
                                                        e.key === " "
                                                    ) {
                                                        e.preventDefault();
                                                        onEditOrder?.(order);
                                                    }
                                                }}
                                            >
                                                <PencilIcon className="w-4 h-4" />
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                                    <InboxIcon className="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600" />
                                    <p className="text-xl font-medium">
                                        No hay órdenes para mostrar
                                    </p>
                                    <p className="text-sm mt-2">
                                        Intenta cambiar los filtros o crea una
                                        nueva orden
                                    </p>
                                </div>
                            )}
                        </div>
                    </CardBody>
                </Card>
            )}

            {/* Paginación */}
            {pages > 1 && (
                <div className="flex justify-between items-center mt-4">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">
                            Filas por página:
                        </span>
                        <select
                            className="text-sm border rounded-md px-2 py-1 border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800"
                            value={rowsPerPage}
                            onChange={(e) => {
                                const newPerPage = parseInt(e.target.value, 10);

                                setRowsPerPage(newPerPage);
                                setPage(1);
                                onPerPageChange?.(newPerPage);
                            }}
                        >
                            {[5, 10, 25, 50].map((value) => (
                                <option key={value} value={value}>
                                    {value}
                                </option>
                            ))}
                        </select>
                    </div>

                    <Pagination
                        showControls
                        classNames={{
                            cursor: "bg-primary text-white font-medium",
                        }}
                        initialPage={1}
                        page={page}
                        size="sm"
                        total={pages}
                        onChange={(newPage) => {
                            setPage(newPage);
                            onPageChange?.(newPage);
                        }}
                    />
                </div>
            )}
        </div>
    );

    // Contenido para la vista de contratistas (placeholder)
    const ContractorsTabContent = () => (
        <div className="flex flex-col items-center justify-center py-12">
            <UserGroupIcon className="w-16 h-16 mb-4 text-gray-300 dark:text-gray-600" />
            <p className="text-xl font-medium text-gray-500 dark:text-gray-400">
                Vista de contratistas en desarrollo
            </p>
            <p className="text-sm mt-2 text-gray-500 dark:text-gray-400">
                Esta sección mostrará información detallada de contratistas y
                sus asignaciones
            </p>
        </div>
    );

    // Contenido para la vista de calendario
    const CalendarTabContent: React.FC<{
        orders: Order[];
        onOpenDetail?: (order: Order) => void;
    }> = ({ orders, onOpenDetail }) => {
        // Estado para controlar el mensaje de carga
        const [isLoading, setIsLoading] = useState(false);

        // Definir una función que maneja la apertura de detalles de forma segura
        const handleOpenDetail = (order: Order) => {
            // Indicar que está cargando
            setIsLoading(true);

            // Llamar al callback si existe
            if (onOpenDetail) {
                // Pequeño retraso para permitir que se muestre el estado de carga
                setTimeout(() => {
                    onOpenDetail(order);
                    // Restaurar el estado después de un breve período
                    setTimeout(() => {
                        setIsLoading(false);
                    }, 500);
                }, 100);
            }
        };

        // Renderizar el componente con la función segura y un indicador de carga
        return (
            <>
                {isLoading && (
                    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/20 backdrop-blur-sm">
                        <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow-lg flex items-center gap-3">
                            <ArrowPathIcon className="w-5 h-5 animate-spin text-primary" />
                            <p>Cargando detalles de la orden...</p>
                        </div>
                    </div>
                )}
                <FullCalendarView
                    orders={orders}
                    onOpenDetail={handleOpenDetail}
                />
            </>
        );
    };

    // Usar nuestro nuevo componente de métricas para contratistas
    const MetricsTabContent = () => <MetricsDashboard />;

    // Componente envuelto con el Provider del contexto
    return (
        <OrderDashboardProvider
            value={{
                orderStats: {
                    total: orderStats.total,
                    new: orderStats.new,
                    inProcess: orderStats.inProcess,
                    inProduction: orderStats.inProduction,
                    delivered: orderStats.delivered,
                    canceled: orderStats.canceled,
                    overdue: orderStats.overdue,
                },
            }}
        >
            <div className="space-y-4">
                {/* Barra superior con controles - Mejorada para responsividad */}
                <div className="bg-white dark:bg-gray-900 p-3 sm:p-4 rounded-xl shadow-md border border-gray-100 dark:border-gray-800">
                    <div className="flex flex-col gap-3 sm:gap-4 md:flex-row md:justify-between md:items-start">
                        {/* Búsqueda y filtros - Columna izquierda */}
                        <div className="flex flex-col gap-3 w-full md:w-3/4 xl:w-auto">
                            {/* Barra de búsqueda mejorada */}
                            <Input
                                isClearable
                                className="w-full sm:w-64"
                                classNames={{
                                    inputWrapper:
                                        "focus-within:!border-primary shadow-sm",
                                    base: "max-w-full w-full sm:max-w-xs",
                                }}
                                id="search-input"
                                placeholder="Buscar orden, cliente o lote..."
                                size="sm"
                                startContent={
                                    <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
                                }
                                value={filterValue}
                                onClear={handleClearSearch}
                                onValueChange={handleSearchChange}
                            />

                            {/* Vista móvil: Dropdown para filtros */}
                            <div className="md:hidden">
                                <Select
                                    classNames={{
                                        trigger: "shadow-sm",
                                    }}
                                    placeholder="Filtrar por estado"
                                    selectedKeys={
                                        statusFilter ? [statusFilter] : [""]
                                    }
                                    size="sm"
                                    startContent={
                                        <FunnelIcon className="w-4 h-4 text-gray-400" />
                                    }
                                    onChange={(e) =>
                                        handleStatusFilterChange(e.target.value)
                                    }
                                >
                                    {statusOptions.map((status) => (
                                        <SelectItem key={status.uid}>
                                            {status.name}
                                        </SelectItem>
                                    ))}
                                </Select>
                            </div>

                            {/* Vista tablet/desktop: Filtros mejorados */}
                            <div className="hidden md:flex relative">
                                <div className="overflow-y-auto max-h-24 w-full">
                                    <div className="flex flex-wrap gap-1.5 pb-1">
                                        {statusOptions.map((status) => {
                                            const isSelected =
                                                statusFilter === status.uid ||
                                                (statusFilter === "" &&
                                                    status.uid === "");

                                            return (
                                                <Button
                                                    key={status.uid}
                                                    className={`whitespace-nowrap mb-1 px-3 transition-all ${isSelected ? "shadow-md transform scale-105" : "hover:scale-105"}`}
                                                    color={
                                                        isSelected
                                                            ? "primary"
                                                            : "default"
                                                    }
                                                    radius="full"
                                                    size="sm"
                                                    variant={
                                                        isSelected
                                                            ? "solid"
                                                            : "flat"
                                                    }
                                                    onPress={() =>
                                                        handleStatusFilterChange(
                                                            status.uid,
                                                        )
                                                    }
                                                >
                                                    {status.name}
                                                </Button>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Botones de acción - Columna derecha */}
                        <div className="flex flex-wrap items-center gap-2 w-full md:w-auto justify-between md:justify-end">
                            {activeTab === "orders" && (
                                <div className="flex-none order-1 bg-gray-50 dark:bg-gray-800/40 rounded-lg p-1">
                                    {/* Vista móvil: Dropdown para opciones de vista */}
                                    <div className="sm:hidden">
                                        <Select
                                            aria-label="Cambiar vista"
                                            classNames={{
                                                trigger:
                                                    "min-w-0 h-9 px-2 bg-white dark:bg-gray-800 shadow-sm",
                                            }}
                                            selectedKeys={[viewMode]}
                                            size="sm"
                                            onChange={(e) =>
                                                handleViewModeChange(
                                                    e.target.value as
                                                        | "grid"
                                                        | "list",
                                                )
                                            }
                                        >
                                            <SelectItem
                                                key="grid"
                                                textValue="Tarjetas"
                                            >
                                                <div className="flex items-center gap-2">
                                                    <Squares2X2Icon className="w-4 h-4" />
                                                    <span>Tarjetas</span>
                                                </div>
                                            </SelectItem>
                                            <SelectItem
                                                key="list"
                                                textValue="Lista"
                                            >
                                                <div className="flex items-center gap-2">
                                                    <TableCellsIcon className="w-4 h-4" />
                                                    <span>Lista</span>
                                                </div>
                                            </SelectItem>
                                        </Select>
                                    </div>

                                    {/* Vista tablet/desktop: Botones para opciones de vista */}
                                    <ButtonGroup
                                        className="hidden sm:inline-flex"
                                        size="sm"
                                    >
                                        <Button
                                            key="grid-view"
                                            className="shadow-sm"
                                            color={
                                                viewMode === "grid"
                                                    ? "primary"
                                                    : "default"
                                            }
                                            variant={
                                                viewMode === "grid"
                                                    ? "solid"
                                                    : "flat"
                                            }
                                            onPress={() =>
                                                handleViewModeChange("grid")
                                            }
                                        >
                                            <Squares2X2Icon className="w-4 h-4 sm:mr-1" />
                                            <span className="hidden sm:inline">
                                                Tarjetas
                                            </span>
                                        </Button>
                                        <Button
                                            key="list-view"
                                            className="shadow-sm"
                                            color={
                                                viewMode === "list"
                                                    ? "primary"
                                                    : "default"
                                            }
                                            variant={
                                                viewMode === "list"
                                                    ? "solid"
                                                    : "flat"
                                            }
                                            onPress={() =>
                                                handleViewModeChange("list")
                                            }
                                        >
                                            <TableCellsIcon className="w-4 h-4 sm:mr-1" />
                                            <span className="hidden sm:inline">
                                                Lista
                                            </span>
                                        </Button>
                                    </ButtonGroup>
                                </div>
                            )}

                            <div className="flex flex-wrap items-center gap-2 order-2 ml-auto">
                                {/* Botón Actualizar - Móvil */}
                                <Button
                                    aria-label="Actualizar datos"
                                    className="sm:hidden shadow-sm hover:shadow-md transition-shadow"
                                    isIconOnly={true}
                                    isLoading={isRefreshing}
                                    size="sm"
                                    startContent={
                                        <ArrowPathIcon className="w-4 h-4" />
                                    }
                                    variant="flat"
                                    onPress={refreshData}
                                />

                                {/* Botón Actualizar - Desktop */}
                                <Button
                                    className="hidden sm:flex shadow-sm hover:shadow-md transition-all hover:bg-gray-100 dark:hover:bg-gray-800"
                                    isLoading={isRefreshing}
                                    size="sm"
                                    startContent={
                                        <ArrowPathIcon
                                            className={`w-4 h-4 ${isRefreshing ? "" : "animate-pulse"}`}
                                        />
                                    }
                                    variant="flat"
                                    onPress={refreshData}
                                >
                                    Actualizar
                                </Button>

                                {/* Botón Nueva Orden - Móvil */}
                                <Button
                                    aria-label="Nueva orden"
                                    as={Link}
                                    className="sm:hidden shadow-lg hover:shadow-xl transition-shadow bg-[var(--color-primary-9)] hover:bg-[var(--color-primary-10)] text-white"
                                    href="/dashboard/orders/new"
                                    isIconOnly={true}
                                    size="md"
                                    startContent={
                                        <PlusIcon className="w-5 h-5" />
                                    }
                                />

                                {/* Botón Nueva Orden - Desktop */}
                                <Button
                                    as={Link}
                                    className="hidden sm:flex shadow-lg hover:shadow-xl transition-all hover:scale-105 bg-[var(--color-primary-9)] hover:bg-[var(--color-primary-10)] text-white font-medium"
                                    href="/dashboard/orders/new"
                                    size="md"
                                    startContent={
                                        <PlusIcon className="w-5 h-5" />
                                    }
                                >
                                    Nueva Orden
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Pestañas principales - Mejoradas para móviles */}
            <div className="border-b border-divider relative">
                {/* Indicadores de scroll */}
                <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-white dark:from-gray-900 to-transparent pointer-events-none z-10 sm:hidden" />
                <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-white dark:from-gray-900 to-transparent pointer-events-none z-10 sm:hidden" />

                {/* Contenedor de tabs con scroll snap */}
                <div className="flex gap-3 sm:gap-6 overflow-x-auto scrollbar-hide pb-0.5 scroll-px-3 snap-x">
                    <div
                        aria-label="Ver órdenes"
                        className={`flex items-center gap-1 sm:gap-2 px-3 py-3 relative cursor-pointer snap-start ${activeTab === "orders" ? "text-primary border-b-2 border-primary" : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"}`}
                        role="button"
                        tabIndex={0}
                        onClick={() => setActiveTab("orders")}
                        onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                setActiveTab("orders");
                            }
                        }}
                    >
                        <InboxIcon className="w-4 h-4" />
                        <span className="whitespace-nowrap">Órdenes</span>
                        <Chip color="primary" size="sm" variant="flat">
                            {orderStats.total}
                        </Chip>
                    </div>
                    <div
                        aria-label="Ver contratistas"
                        className={`flex items-center gap-1 sm:gap-2 px-3 py-3 relative cursor-pointer snap-start ${activeTab === "contractors" ? "text-primary border-b-2 border-primary" : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"}`}
                        role="button"
                        tabIndex={0}
                        onClick={() => setActiveTab("contractors")}
                        onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                setActiveTab("contractors");
                            }
                        }}
                    >
                        <UserGroupIcon className="w-4 h-4" />
                        <span className="whitespace-nowrap">Contratistas</span>
                    </div>
                    <div
                        aria-label="Ver calendario"
                        className={`flex items-center gap-1 sm:gap-2 px-3 py-3 relative cursor-pointer snap-start ${activeTab === "calendar" ? "text-primary border-b-2 border-primary" : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"}`}
                        role="button"
                        tabIndex={0}
                        onClick={() => setActiveTab("calendar")}
                        onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                setActiveTab("calendar");
                            }
                        }}
                    >
                        <CalendarIcon className="w-4 h-4" />
                        <span className="whitespace-nowrap">Calendario</span>
                    </div>
                    <div
                        aria-label="Ver métricas"
                        className={`flex items-center gap-1 sm:gap-2 px-3 py-3 relative cursor-pointer snap-start ${activeTab === "metrics" ? "text-primary border-b-2 border-primary" : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"}`}
                        role="button"
                        tabIndex={0}
                        onClick={() => setActiveTab("metrics")}
                        onKeyDown={(e) => {
                            if (e.key === "Enter" || e.key === " ") {
                                e.preventDefault();
                                setActiveTab("metrics");
                            }
                        }}
                    >
                        <ChartBarIcon className="w-4 h-4" />
                        <span className="whitespace-nowrap">Métricas</span>
                    </div>
                </div>
            </div>

            {/* Contenido según la pestaña seleccionada */}
            <div className="mt-4">
                {activeTab === "orders" && <OrdersTabContent />}
                {activeTab === "contractors" && <ContractorsTabContent />}
                {activeTab === "calendar" && (
                    <CalendarTabContent
                        orders={orders}
                        onOpenDetail={onOpenDetail}
                    />
                )}
                {activeTab === "metrics" && <MetricsTabContent />}
            </div>
        </OrderDashboardProvider>
    );
}
