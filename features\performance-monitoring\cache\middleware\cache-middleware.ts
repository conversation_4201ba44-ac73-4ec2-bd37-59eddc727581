/**
 * Redis Cache Middleware for Next.js API Routes
 * Automatically caches API responses based on configuration
 */

import { NextRequest, NextResponse } from "next/server";

import {
    staticCache,
    userCache,
    computedCache,
    CacheType,
} from "../strategies";
import { cacheInvalidator, CacheTags } from "../cache-invalidator";
import { featureFlags } from "../../feature-flags";
import { CacheKeyOptions } from "../cache-types";

interface CacheConfig {
    type: CacheType;
    ttl?: number;
    tags?: string[];
    keyGenerator?: (req: NextRequest) => string;
}

// Route cache configuration
const routeCacheConfig: Record<string, CacheConfig> = {
    "/api/orders": {
        type: CacheType.USER_SPECIFIC,
        ttl: 300, // 5 minutes
        tags: [CacheTags.ORDERS],
    },
    "/api/customers": {
        type: CacheType.COMPUTED,
        ttl: 600, // 10 minutes
        tags: [CacheTags.CUSTOMERS],
    },
    "/api/products": {
        type: CacheType.STATIC,
        ttl: 3600, // 1 hour
        tags: [CacheTags.PRODUCTS],
    },
    "/api/order-statuses": {
        type: CacheType.STATIC,
        ttl: 86400, // 24 hours
        tags: ["order-statuses"],
    },
};

/**
 * Get cache strategy based on type
 */
function getCacheStrategy(type: CacheType) {
    switch (type) {
        case CacheType.STATIC:
            return staticCache;
        case CacheType.USER_SPECIFIC:
            return userCache;
        case CacheType.COMPUTED:
            return computedCache;
        default:
            return null;
    }
}

/**
 * Redis cache middleware
 */
export async function withRedisCache(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
): Promise<NextResponse> {
    // Check if Redis is enabled
    if (!featureFlags.ENABLE_REDIS) {
        return handler();
    }

    const pathname = request.nextUrl.pathname;
    const config = routeCacheConfig[pathname];

    // No cache config for this route
    if (!config || config.type === CacheType.REAL_TIME) {
        return handler();
    }

    const strategy = getCacheStrategy(config.type);

    if (!strategy) {
        return handler();
    }

    // Generate cache key
    const cacheKey = config.keyGenerator
        ? config.keyGenerator(request)
        : `${pathname}:${request.nextUrl.searchParams.toString()}`;

    // Try to get from cache
    const keyOptions: CacheKeyOptions = {
        resource: pathname,
        identifier: cacheKey,
    };

    // Add user context for user-specific cache
    if (config.type === CacheType.USER_SPECIFIC) {
        // Extract user ID from session/auth
        const userId = request.headers.get("x-user-id") || "anonymous";

        keyOptions.tenant = userId;
    }

    const cached = await strategy.get(keyOptions);

    if (cached) {
        // Return cached response
        return NextResponse.json(cached, {
            headers: {
                "X-Cache": "HIT",
                "X-Cache-TTL": config.ttl?.toString() || "default",
            },
        });
    }

    // Execute handler and cache result
    const response = await handler();

    // Only cache successful responses
    if (response.status === 200) {
        try {
            const data = await response.json();

            // Set in cache
            await strategy.set(keyOptions, data, config.ttl);

            // Tag the cache entry
            if (config.tags) {
                const fullKey = `${keyOptions.resource}:${keyOptions.identifier}`;

                await cacheInvalidator.tagKey(fullKey, config.tags);
            }

            // Return new response with cache headers
            return NextResponse.json(data, {
                headers: {
                    "X-Cache": "MISS",
                    "X-Cache-TTL": config.ttl?.toString() || "default",
                },
            });
        } catch (error) {
            console.error("Cache middleware error:", error);

            return response;
        }
    }

    return response;
}

/**
 * Invalidate cache for specific routes
 */
export async function invalidateRouteCache(pathname: string): Promise<void> {
    const config = routeCacheConfig[pathname];

    if (config?.tags) {
        await cacheInvalidator.invalidateTags(config.tags);
    }
}
