"use client";

import React from "react";
import { Progress } from "@heroui/react";

interface ModelFormProgressProps {
    value: number;
    label?: string;
    showPercentage?: boolean;
    className?: string;
}

// Función para calcular el progreso del formulario
export function calculateModelProgress(data: {
    code?: string;
    description?: string;
    basePrice?: number;
}): number {
    let progress = 0;

    // Código: 33% del progreso
    if (data.code && data.code.trim().length >= 2) {
        progress += 33;
    }

    // Descripción: 33% del progreso
    if (data.description && data.description.trim().length >= 5) {
        progress += 33;
    }

    // Precio base: 34% del progreso
    if (
        data.basePrice !== undefined &&
        data.basePrice !== null &&
        data.basePrice >= 0
    ) {
        progress += 34;
    }

    return Math.min(progress, 100);
}

export function ModelFormProgress({
    value,
    label = "Progreso del formulario",
    showPercentage = true,
    className = "",
}: ModelFormProgressProps) {
    return (
        <div className={className}>
            <div className="space-y-2">
                <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {label}
                    </span>
                    {showPercentage && (
                        <span className="text-sm font-semibold text-purple-600 dark:text-purple-400">
                            {value}%
                        </span>
                    )}
                </div>
                <Progress
                    aria-label={label}
                    classNames={{
                        base: "max-w-full",
                        track: "bg-gray-200 dark:bg-gray-700",
                        indicator: "bg-purple-600",
                    }}
                    color="secondary"
                    size="sm"
                    value={value}
                />
                {value === 100 && (
                    <p className="text-xs text-green-600 dark:text-green-400 font-medium text-center mt-1">
                        Formulario completo ✓
                    </p>
                )}
            </div>
        </div>
    );
}
