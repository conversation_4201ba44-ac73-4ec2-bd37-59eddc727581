import { z } from "zod";

// Schema para validar datos de items de remisión
export const remissionItemSchema = z.object({
    modelCode: z.string().min(1, "El código del modelo es obligatorio"),
    colorName: z.string().min(1, "El nombre del color es obligatorio"),
    sizeCode: z.string().min(1, "El código de talla es obligatorio"),
    quantity: z.number().min(1, "La cantidad debe ser mayor a 0"),
});

// Schema para validar detalles de orden
export const orderDetailsSchema = z.object({
    id: z.string().min(1, "El ID de orden es obligatorio"),
    cutOrder: z.string().optional(),
    creationDate: z.date().optional(),
    parts: z
        .array(
            z.object({
                id: z.string(),
                code: z.string(),
            }),
        )
        .optional(),
});

// Schema para validar creación de remisión
export const createRemissionSchema = z.object({
    assignmentIds: z
        .array(z.string())
        .min(1, "Debe incluir al menos una asignación"),
    contractorId: z.string().min(1, "El ID del contratista es obligatorio"),
    notes: z.string().optional(),
    orderDetails: orderDetailsSchema,
    items: z.array(remissionItemSchema).min(1, "Debe incluir al menos un item"),
});

// Schema para validar datos de remisión en transacción
export const remissionInTransactionSchema = z.object({
    assignmentIds: z.array(z.string()).min(1),
    contractorId: z.string().min(1),
    orderId: z.string().min(1),
    items: z.array(remissionItemSchema).min(1),
    notes: z.string().optional(),
});

// Schema para validar actualización de items
export const updateRemissionItemSchema = z.object({
    id: z.string().min(1, "El ID del item es obligatorio"),
    quantity: z.number().min(0, "La cantidad no puede ser negativa"),
});

// Schema para validar actualización de remisión
export const updateRemissionSchema = z.object({
    remissionId: z.string().min(1, "El ID de remisión es obligatorio"),
    notes: z.string().optional(),
    items: z.array(updateRemissionItemSchema).optional(),
});

// Función helper para validar datos antes de crear remisión
export function validateRemissionData(data: unknown) {
    return remissionInTransactionSchema.safeParse(data);
}

// Función helper para validar datos antes de actualizar remisión
export function validateUpdateRemissionData(data: unknown) {
    return updateRemissionSchema.safeParse(data);
}
