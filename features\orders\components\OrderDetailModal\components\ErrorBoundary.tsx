import React from "react";
import { <PERSON>, CardBody, Button } from "@heroui/react";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

interface Props {
    children: React.ReactNode;
    fallback?: React.ReactNode;
}

interface State {
    hasError: boolean;
    error?: Error;
}

export class OrderModalErrorBoundary extends React.Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        // REMOVED: console.error("OrderModal Error:", error, errorInfo);

        // Track error in analytics if available
        if (typeof window !== "undefined" && "gtag" in window) {
            (window as any).gtag("event", "exception", {
                description: error.toString(),
                fatal: false,
                error_source: "OrderModal",
            });
        }
    }

    render() {
        if (this.state.hasError) {
            return (
                this.props.fallback || (
                    <Card className="border-danger">
                        <CardBody className="text-center py-8">
                            <ExclamationTriangleIcon className="w-12 h-12 text-danger mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">
                                Error al cargar información
                            </h3>
                            <p className="text-sm text-default-500 mb-4">
                                Ocurrió un error al mostrar los detalles de la
                                orden
                            </p>
                            <Button
                                color="primary"
                                size="sm"
                                onClick={() => window.location.reload()}
                            >
                                Recargar página
                            </Button>
                        </CardBody>
                    </Card>
                )
            );
        }

        return this.props.children;
    }
}
