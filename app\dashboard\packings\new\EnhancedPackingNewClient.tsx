"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Input,
    Select,
    SelectItem,
    Textarea,
    DatePicker,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Chip,
    Divider,
    addToast,
} from "@heroui/react";
import {
    ArrowLeftIcon,
    DocumentPlusIcon,
    CubeIcon,
    BuildingOfficeIcon,
    TruckIcon,
    ScaleIcon,
    ArchiveBoxIcon,
    PlusIcon,
    TrashIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { parseDate } from "@internationalized/date";

import { DashboardHeader } from "@/shared/components/dashboard";
import { createPackingV2 } from "@/features/packings/actions";
import { useCustomers } from "@/features/customers/hooks/useCustomer";
import { useContractors } from "@/features/contractors/hooks/useContractors";

interface PackingDetail {
    garmentSizeId: string;
    quantity: number;
    boxNumber?: number;
    weight?: number;
    comments?: string;
}

const steps = [
    { id: 1, name: "Cliente y Entrega", icon: BuildingOfficeIcon },
    { id: 2, name: "Selección de Artículos", icon: CubeIcon },
    { id: 3, name: "Información de Transporte", icon: TruckIcon },
    { id: 4, name: "Revisión y Confirmación", icon: DocumentPlusIcon },
];

export default function EnhancedPackingNewClient() {
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(1);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { customers } = useCustomers();
    const { contractors } = useContractors();

    // Form data
    const [formData, setFormData] = useState({
        customerId: "",
        subCustomerId: "",
        orderId: "",
        deliveryDate: parseDate(
            new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split("T")[0],
        ),
        notes: "",
        transporterId: "",
        vehicleInfo: "",
        driverName: "",
        driverPhone: "",
        totalWeight: undefined as number | undefined,
        totalVolume: undefined as number | undefined,
        packagesCount: 1,
    });

    const [details, setDetails] = useState<PackingDetail[]>([]);
    const [availableItems, setAvailableItems] = useState<any[]>([]);

    // Obtener subclientes cuando se selecciona un cliente
    const selectedCustomer = customers?.find(
        (c: any) => c.id === formData.customerId,
    );
    const subCustomers = selectedCustomer?.subCustomers || [];

    // Simulación de items disponibles (en producción vendría de la base de datos)
    useEffect(() => {
        if (formData.customerId) {
            // Aquí se cargarían los items disponibles del cliente/orden
            setAvailableItems([
                {
                    id: "1",
                    garmentSizeId: "gs1",
                    model: "T-001",
                    color: "Blanco",
                    size: "M",
                    availableQuantity: 100,
                },
                {
                    id: "2",
                    garmentSizeId: "gs2",
                    model: "T-001",
                    color: "Blanco",
                    size: "L",
                    availableQuantity: 80,
                },
                {
                    id: "3",
                    garmentSizeId: "gs3",
                    model: "P-002",
                    color: "Negro",
                    size: "M",
                    availableQuantity: 50,
                },
            ]);
        }
    }, [formData.customerId]);

    const handleAddDetail = () => {
        if (availableItems.length > 0) {
            setDetails([
                ...details,
                {
                    garmentSizeId: availableItems[0].garmentSizeId,
                    quantity: 1,
                    boxNumber: 1,
                },
            ]);
        }
    };

    const handleRemoveDetail = (index: number) => {
        setDetails(details.filter((_, i) => i !== index));
    };

    const handleDetailChange = (
        index: number,
        field: keyof PackingDetail,
        value: any,
    ) => {
        const updated = [...details];

        updated[index] = {
            ...updated[index],
            [field]: value,
        };
        setDetails(updated);
    };

    const validateStep = (step: number): boolean => {
        switch (step) {
            case 1:
                return !!formData.customerId && !!formData.deliveryDate;
            case 2:
                return (
                    details.length > 0 && details.every((d) => d.quantity > 0)
                );
            case 3:
                return true; // Transporte es opcional
            case 4:
                return true;
            default:
                return false;
        }
    };

    const handleNext = () => {
        if (validateStep(currentStep)) {
            setCurrentStep(currentStep + 1);
        } else {
            addToast({
                title: "Error",
                description: "Por favor complete todos los campos requeridos",
                color: "danger",
            });
        }
    };

    const handlePrevious = () => {
        setCurrentStep(currentStep - 1);
    };

    const handleSubmit = async () => {
        setIsSubmitting(true);

        try {
            const packingData = {
                ...formData,
                deliveryDate: new Date(formData.deliveryDate.toString()),
                totalWeight: formData.totalWeight || undefined,
                totalVolume: formData.totalVolume || undefined,
                details: details.map((d) => ({
                    ...d,
                    weight: d.weight || undefined,
                })),
            };

            const result = await createPackingV2(packingData);

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description: "Packing creado exitosamente",
                    color: "success",
                });
                router.push("/dashboard/packings");
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al crear el packing",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error inesperado",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 1:
                return (
                    <Card>
                        <CardHeader>
                            <h3 className="text-lg font-semibold">
                                Información del Cliente y Entrega
                            </h3>
                        </CardHeader>
                        <CardBody className="space-y-4">
                            <Select
                                isRequired
                                label="Cliente"
                                placeholder="Seleccione un cliente"
                                selectedKeys={
                                    formData.customerId
                                        ? [formData.customerId]
                                        : []
                                }
                                startContent={
                                    <BuildingOfficeIcon className="w-4 h-4 text-gray-400" />
                                }
                                onSelectionChange={(keys) => {
                                    const value = Array.from(keys)[0] as string;

                                    setFormData({
                                        ...formData,
                                        customerId: value,
                                        subCustomerId: "",
                                    });
                                }}
                            >
                                {customers?.map((customer: any) => (
                                    <SelectItem key={customer.id}>
                                        {customer.name}
                                    </SelectItem>
                                ))}
                            </Select>

                            {subCustomers.length > 0 && (
                                <Select
                                    label="Subcliente"
                                    placeholder="Seleccione un subcliente (opcional)"
                                    selectedKeys={
                                        formData.subCustomerId
                                            ? [formData.subCustomerId]
                                            : []
                                    }
                                    startContent={
                                        <BuildingOfficeIcon className="w-4 h-4 text-gray-400" />
                                    }
                                    onSelectionChange={(keys) => {
                                        const value = Array.from(
                                            keys,
                                        )[0] as string;

                                        setFormData({
                                            ...formData,
                                            subCustomerId: value,
                                        });
                                    }}
                                >
                                    {subCustomers.map((sub: any) => (
                                        <SelectItem key={sub.id}>
                                            {sub.name}
                                        </SelectItem>
                                    ))}
                                </Select>
                            )}

                            <DatePicker
                                isRequired
                                label="Fecha de Entrega"
                                value={formData.deliveryDate}
                                onChange={(value) =>
                                    setFormData({
                                        ...formData,
                                        deliveryDate:
                                            value || formData.deliveryDate,
                                    })
                                }
                            />

                            <Textarea
                                label="Notas"
                                minRows={3}
                                placeholder="Notas adicionales sobre el packing..."
                                value={formData.notes}
                                onValueChange={(value) =>
                                    setFormData({ ...formData, notes: value })
                                }
                            />
                        </CardBody>
                    </Card>
                );

            case 2:
                return (
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between w-full">
                                <h3 className="text-lg font-semibold">
                                    Selección de Artículos
                                </h3>
                                <Button
                                    color="primary"
                                    size="sm"
                                    startContent={
                                        <PlusIcon className="w-4 h-4" />
                                    }
                                    variant="flat"
                                    onPress={handleAddDetail}
                                >
                                    Agregar Artículo
                                </Button>
                            </div>
                        </CardHeader>
                        <CardBody>
                            {details.length === 0 ? (
                                <div className="text-center py-8">
                                    <CubeIcon className="w-12 h-12 mx-auto text-gray-400 mb-2" />
                                    <p className="text-gray-500">
                                        No hay artículos agregados
                                    </p>
                                    <p className="text-sm text-gray-400">
                                        Haga clic en "Agregar Artículo" para
                                        comenzar
                                    </p>
                                </div>
                            ) : (
                                <Table aria-label="Artículos del packing">
                                    <TableHeader>
                                        <TableColumn>ARTÍCULO</TableColumn>
                                        <TableColumn align="center">
                                            CANTIDAD
                                        </TableColumn>
                                        <TableColumn align="center">
                                            CAJA
                                        </TableColumn>
                                        <TableColumn align="center">
                                            PESO (kg)
                                        </TableColumn>
                                        <TableColumn align="center">
                                            ACCIONES
                                        </TableColumn>
                                    </TableHeader>
                                    <TableBody>
                                        {details.map((detail, index) => {
                                            const item = availableItems.find(
                                                (i) =>
                                                    i.garmentSizeId ===
                                                    detail.garmentSizeId,
                                            );

                                            return (
                                                <TableRow key={index}>
                                                    <TableCell>
                                                        <Select
                                                            size="sm"
                                                            value={
                                                                detail.garmentSizeId
                                                            }
                                                            onChange={(e) =>
                                                                handleDetailChange(
                                                                    index,
                                                                    "garmentSizeId",
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                        >
                                                            {availableItems.map(
                                                                (item: any) => (
                                                                    <SelectItem
                                                                        key={
                                                                            item.garmentSizeId
                                                                        }
                                                                        textValue={`${item.model} - ${item.color} - ${item.size}`}
                                                                    >
                                                                        <div className="flex items-center justify-between">
                                                                            <span>
                                                                                {
                                                                                    item.model
                                                                                }{" "}
                                                                                -{" "}
                                                                                {
                                                                                    item.color
                                                                                }{" "}
                                                                                -{" "}
                                                                                {
                                                                                    item.size
                                                                                }
                                                                            </span>
                                                                            <Chip
                                                                                size="sm"
                                                                                variant="flat"
                                                                            >
                                                                                {
                                                                                    item.availableQuantity
                                                                                }{" "}
                                                                                disp.
                                                                            </Chip>
                                                                        </div>
                                                                    </SelectItem>
                                                                ),
                                                            )}
                                                        </Select>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Input
                                                            className="w-20"
                                                            max={
                                                                item?.availableQuantity ||
                                                                999
                                                            }
                                                            min={1}
                                                            size="sm"
                                                            type="number"
                                                            value={detail.quantity.toString()}
                                                            onChange={(e) =>
                                                                handleDetailChange(
                                                                    index,
                                                                    "quantity",
                                                                    parseInt(
                                                                        e.target
                                                                            .value,
                                                                    ) || 0,
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Input
                                                            className="w-20"
                                                            min={1}
                                                            size="sm"
                                                            type="number"
                                                            value={
                                                                detail.boxNumber?.toString() ||
                                                                ""
                                                            }
                                                            onChange={(e) =>
                                                                handleDetailChange(
                                                                    index,
                                                                    "boxNumber",
                                                                    parseInt(
                                                                        e.target
                                                                            .value,
                                                                    ) ||
                                                                        undefined,
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Input
                                                            className="w-20"
                                                            min={0}
                                                            size="sm"
                                                            step={0.1}
                                                            type="number"
                                                            value={
                                                                detail.weight?.toString() ||
                                                                ""
                                                            }
                                                            onChange={(e) =>
                                                                handleDetailChange(
                                                                    index,
                                                                    "weight",
                                                                    parseFloat(
                                                                        e.target
                                                                            .value,
                                                                    ) ||
                                                                        undefined,
                                                                )
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button
                                                            isIconOnly
                                                            color="danger"
                                                            size="sm"
                                                            variant="light"
                                                            onPress={() =>
                                                                handleRemoveDetail(
                                                                    index,
                                                                )
                                                            }
                                                        >
                                                            <TrashIcon className="w-4 h-4" />
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            )}
                        </CardBody>
                    </Card>
                );

            case 3:
                return (
                    <Card>
                        <CardHeader>
                            <h3 className="text-lg font-semibold">
                                Información de Transporte (Opcional)
                            </h3>
                        </CardHeader>
                        <CardBody className="space-y-4">
                            <Select
                                label="Transportista"
                                placeholder="Seleccione un transportista"
                                selectedKeys={
                                    formData.transporterId
                                        ? [formData.transporterId]
                                        : []
                                }
                                startContent={
                                    <TruckIcon className="w-4 h-4 text-gray-400" />
                                }
                                onSelectionChange={(keys) => {
                                    const value = Array.from(keys)[0] as string;

                                    setFormData({
                                        ...formData,
                                        transporterId: value,
                                    });
                                }}
                            >
                                {contractors?.map((contractor: any) => (
                                    <SelectItem key={contractor.id}>
                                        {contractor.name}
                                    </SelectItem>
                                ))}
                            </Select>

                            {formData.transporterId && (
                                <>
                                    <Input
                                        label="Nombre del Conductor"
                                        placeholder="Ingrese el nombre del conductor"
                                        value={formData.driverName}
                                        onValueChange={(value) =>
                                            setFormData({
                                                ...formData,
                                                driverName: value,
                                            })
                                        }
                                    />

                                    <Input
                                        label="Teléfono del Conductor"
                                        placeholder="Ingrese el teléfono del conductor"
                                        value={formData.driverPhone}
                                        onValueChange={(value) =>
                                            setFormData({
                                                ...formData,
                                                driverPhone: value,
                                            })
                                        }
                                    />

                                    <Textarea
                                        label="Información del Vehículo"
                                        minRows={2}
                                        placeholder="Marca, modelo, placas, etc."
                                        value={formData.vehicleInfo}
                                        onValueChange={(value) =>
                                            setFormData({
                                                ...formData,
                                                vehicleInfo: value,
                                            })
                                        }
                                    />
                                </>
                            )}

                            <Divider className="my-4" />

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Input
                                    label="Peso Total (kg)"
                                    min={0}
                                    placeholder="0.0"
                                    startContent={
                                        <ScaleIcon className="w-4 h-4 text-gray-400" />
                                    }
                                    step={0.1}
                                    type="number"
                                    value={
                                        formData.totalWeight?.toString() || ""
                                    }
                                    onValueChange={(value) =>
                                        setFormData({
                                            ...formData,
                                            totalWeight:
                                                parseFloat(value) || undefined,
                                        })
                                    }
                                />

                                <Input
                                    label="Volumen Total (m³)"
                                    min={0}
                                    placeholder="0.0"
                                    startContent={
                                        <ArchiveBoxIcon className="w-4 h-4 text-gray-400" />
                                    }
                                    step={0.01}
                                    type="number"
                                    value={
                                        formData.totalVolume?.toString() || ""
                                    }
                                    onValueChange={(value) =>
                                        setFormData({
                                            ...formData,
                                            totalVolume:
                                                parseFloat(value) || undefined,
                                        })
                                    }
                                />

                                <Input
                                    label="Número de Paquetes"
                                    min={1}
                                    placeholder="1"
                                    startContent={
                                        <ArchiveBoxIcon className="w-4 h-4 text-gray-400" />
                                    }
                                    type="number"
                                    value={formData.packagesCount.toString()}
                                    onValueChange={(value) =>
                                        setFormData({
                                            ...formData,
                                            packagesCount: parseInt(value) || 1,
                                        })
                                    }
                                />
                            </div>
                        </CardBody>
                    </Card>
                );

            case 4:
                const totalPieces = details.reduce(
                    (sum, d) => sum + d.quantity,
                    0,
                );
                const totalWeight = details.reduce(
                    (sum, d) => sum + (d.weight || 0) * d.quantity,
                    0,
                );

                return (
                    <div className="space-y-4">
                        <Card>
                            <CardHeader>
                                <h3 className="text-lg font-semibold">
                                    Resumen del Packing
                                </h3>
                            </CardHeader>
                            <CardBody className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm text-gray-500">
                                            Cliente
                                        </p>
                                        <p className="font-medium">
                                            {
                                                customers?.find(
                                                    (c: any) =>
                                                        c.id ===
                                                        formData.customerId,
                                                )?.name
                                            }
                                        </p>
                                        {formData.subCustomerId && (
                                            <p className="text-sm text-gray-500">
                                                {
                                                    subCustomers.find(
                                                        (s: any) =>
                                                            s.id ===
                                                            formData.subCustomerId,
                                                    )?.name
                                                }
                                            </p>
                                        )}
                                    </div>

                                    <div>
                                        <p className="text-sm text-gray-500">
                                            Fecha de Entrega
                                        </p>
                                        <p className="font-medium">
                                            {new Date(
                                                formData.deliveryDate.toString(),
                                            ).toLocaleDateString("es-MX", {
                                                weekday: "long",
                                                year: "numeric",
                                                month: "long",
                                                day: "numeric",
                                            })}
                                        </p>
                                    </div>
                                </div>

                                {formData.transporterId && (
                                    <div className="p-3 bg-secondary-50 dark:bg-secondary-900/20 rounded-lg">
                                        <p className="text-sm font-medium mb-1">
                                            Transporte Asignado
                                        </p>
                                        <p>
                                            {
                                                contractors?.find(
                                                    (c) =>
                                                        c.id ===
                                                        formData.transporterId,
                                                )?.name
                                            }
                                        </p>
                                        {formData.driverName && (
                                            <p className="text-sm text-gray-500">
                                                Conductor: {formData.driverName}{" "}
                                                - {formData.driverPhone}
                                            </p>
                                        )}
                                    </div>
                                )}

                                <Divider />

                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                    <div>
                                        <p className="text-2xl font-bold">
                                            {totalPieces}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            Piezas Totales
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-2xl font-bold">
                                            {details.length}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            Líneas
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-2xl font-bold">
                                            {formData.packagesCount}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            Paquetes
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-2xl font-bold">
                                            {formData.totalWeight ||
                                                totalWeight.toFixed(1)}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            Peso (kg)
                                        </p>
                                    </div>
                                </div>

                                {formData.notes && (
                                    <>
                                        <Divider />
                                        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                            <p className="text-sm font-medium mb-1">
                                                Notas
                                            </p>
                                            <p className="text-sm">
                                                {formData.notes}
                                            </p>
                                        </div>
                                    </>
                                )}
                            </CardBody>
                        </Card>

                        <Card className="bg-primary-50 dark:bg-primary-900/20 border-primary">
                            <CardBody>
                                <div className="flex items-center gap-2">
                                    <InformationCircleIcon className="w-5 h-5 text-primary" />
                                    <p className="text-sm">
                                        Al confirmar, se creará el packing y se
                                        actualizará el inventario
                                        automáticamente. El control de calidad
                                        se podrá realizar posteriormente.
                                    </p>
                                </div>
                            </CardBody>
                        </Card>
                    </div>
                );
        }
    };

    return (
        <div className="space-y-6">
            <DashboardHeader
                actions={
                    <Button
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        variant="light"
                        onPress={() => router.push("/dashboard/packings")}
                    >
                        Volver
                    </Button>
                }
                subtitle="Crea un nuevo packing list con control de trazabilidad"
                title="Nuevo Packing"
            />

            {/* Stepper */}
            <div className="w-full px-4">
                <div className="flex items-center justify-between mb-8">
                    {steps.map((step, index) => (
                        <div key={step.id} className="flex items-center">
                            <motion.div
                                animate={{ scale: 1 }}
                                className={`relative flex items-center justify-center w-12 h-12 rounded-full ${
                                    currentStep >= step.id
                                        ? "bg-primary text-white"
                                        : "bg-gray-200 dark:bg-gray-700 text-gray-500"
                                }`}
                                initial={{ scale: 0 }}
                                transition={{ delay: index * 0.1 }}
                            >
                                <step.icon className="w-6 h-6" />
                                {currentStep > step.id && (
                                    <motion.div
                                        animate={{ scale: 1 }}
                                        className="absolute -top-1 -right-1 w-5 h-5 bg-success rounded-full flex items-center justify-center"
                                        initial={{ scale: 0 }}
                                    >
                                        <span className="text-white text-xs">
                                            ✓
                                        </span>
                                    </motion.div>
                                )}
                            </motion.div>

                            {index < steps.length - 1 && (
                                <div
                                    className={`flex-1 h-1 mx-2 ${
                                        currentStep > step.id
                                            ? "bg-primary"
                                            : "bg-gray-200 dark:bg-gray-700"
                                    }`}
                                />
                            )}
                        </div>
                    ))}
                </div>

                <div className="text-center mb-6">
                    <h2 className="text-xl font-semibold">
                        {steps[currentStep - 1].name}
                    </h2>
                    <p className="text-sm text-gray-500">
                        Paso {currentStep} de {steps.length}
                    </p>
                </div>
            </div>

            {/* Content */}
            <motion.div
                key={currentStep}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                initial={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
            >
                {renderStepContent()}
            </motion.div>

            {/* Navigation */}
            <div className="flex items-center justify-between">
                <Button
                    isDisabled={currentStep === 1}
                    startContent={<ArrowLeftIcon className="w-4 h-4" />}
                    variant="light"
                    onPress={handlePrevious}
                >
                    Anterior
                </Button>

                {currentStep < steps.length ? (
                    <Button
                        color="primary"
                        isDisabled={!validateStep(currentStep)}
                        onPress={handleNext}
                    >
                        Siguiente
                    </Button>
                ) : (
                    <Button
                        color="success"
                        isLoading={isSubmitting}
                        startContent={<DocumentPlusIcon className="w-5 h-5" />}
                        onPress={handleSubmit}
                    >
                        Crear Packing
                    </Button>
                )}
            </div>
        </div>
    );
}
