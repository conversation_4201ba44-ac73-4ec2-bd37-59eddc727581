"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    Input,
    <PERSON>ton,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
    Card,
    CardBody,
    CardHeader,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "@heroui/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { Ruler, Save } from "lucide-react";

import { useSize, useUpdateSize } from "@/features/sizes/hooks";
import { sizeSchema, type SizeFormData } from "@/features/sizes/schemas";
import { SizeFormHeader, SizeInfoMessage } from "@/features/sizes/components";

export default function EditSizePage() {
    const params = useParams();
    const sizeId = params.id as string;
    const router = useRouter();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Obtener datos de la talla
    const { size, isLoading, isError } = useSize(sizeId);
    const { updateSize } = useUpdateSize();

    const {
        register,
        handleSubmit,
        formState: { errors, isDirty },
        setValue,
        watch,
        reset,
    } = useForm<SizeFormData>({
        resolver: zodResolver(sizeSchema),
        mode: "onChange",
        defaultValues: {
            code: "",
        },
    });

    const codeValue = watch("code");

    // Cargar datos cuando estén disponibles
    useEffect(() => {
        if (
            size &&
            typeof size === "object" &&
            size !== null &&
            "code" in size
        ) {
            reset({ code: (size as any).code });
        }
    }, [size, reset]);

    // Convertir a mayúsculas automáticamente
    const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const upperValue = e.target.value.toUpperCase();

        setValue("code", upperValue, { shouldValidate: true });
    };

    const onSubmit = async (data: SizeFormData) => {
        // Si el código no ha cambiado, no hacer nada
        if (
            size &&
            typeof size === "object" &&
            size !== null &&
            "code" in size &&
            (size as any).code === data.code.trim()
        ) {
            addToast({
                title: "Sin cambios",
                description: "No se detectaron cambios en el código",
                color: "warning",
            });

            return;
        }

        setIsSubmitting(true);

        try {
            const result = await updateSize(sizeId, {
                code: data.code.trim(),
            });

            if (!result.success) {
                throw new Error(result.error || "Error al actualizar la talla");
            }

            addToast({
                title: "Talla actualizada",
                description: `La talla se ha actualizado exitosamente`,
                color: "success",
            });

            // Redirigir después de un breve delay para mostrar el toast
            setTimeout(() => {
                router.push("/dashboard/sizes");
                router.refresh();
            }, 1000);
        } catch (error) {
            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error al actualizar la talla",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (isDirty) {
            onOpen();
        } else {
            router.push("/dashboard/sizes");
        }
    };

    // Mostrar spinner mientras carga
    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[60vh]">
                <Spinner label="Cargando talla..." size="lg" />
            </div>
        );
    }

    // Mostrar error si no se encuentra la talla
    if (isError || !size) {
        return (
            <div className="container mx-auto max-w-2xl p-6">
                <Card className="shadow-lg">
                    <CardBody className="text-center py-12">
                        <p className="text-danger text-lg">
                            No se pudo cargar la información de la talla
                        </p>
                        <Button
                            className="mt-4"
                            onPress={() => router.push("/dashboard/sizes")}
                        >
                            Volver a tallas
                        </Button>
                    </CardBody>
                </Card>
            </div>
        );
    }

    return (
        <div className="container mx-auto max-w-2xl p-6">
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
            >
                <Card className="shadow-lg">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <SizeFormHeader
                            description="Modifica los datos de la talla"
                            title="Editar Talla"
                        />
                    </CardHeader>

                    <CardBody className="p-6">
                        <form
                            className="space-y-6"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            {/* Campo de código */}
                            <div className="space-y-2">
                                <Input
                                    {...register("code")}
                                    isRequired
                                    classNames={{
                                        inputWrapper: "shadow-sm",
                                        input: "uppercase font-medium",
                                    }}
                                    description="Solo letras, números y guiones. Máximo 10 caracteres."
                                    errorMessage={errors.code?.message}
                                    isInvalid={!!errors.code}
                                    label="Código de Talla"
                                    placeholder="Ej: S, M, L, XL, 36, 38"
                                    size="lg"
                                    startContent={
                                        <Ruler className="text-gray-400 w-4 h-4" />
                                    }
                                    value={codeValue}
                                    variant="bordered"
                                    onChange={handleCodeChange}
                                />
                            </div>

                            {/* Mensaje informativo */}
                            <SizeInfoMessage
                                message="Al actualizar el código, asegúrate de que sea único en el sistema. La talla no podrá ser eliminada si está asociada a prendas."
                                title="Información importante"
                                type="info"
                            />
                        </form>
                    </CardBody>

                    <CardFooter className="border-t border-gray-200 dark:border-gray-700 px-6 py-4">
                        <div className="flex justify-end gap-3 w-full">
                            <Button variant="flat" onPress={handleCancel}>
                                Cancelar
                            </Button>
                            <Button
                                color="primary"
                                isDisabled={isSubmitting}
                                isLoading={isSubmitting}
                                startContent={
                                    !isSubmitting && (
                                        <Save className="w-4 h-4" />
                                    )
                                }
                                type="submit"
                                onPress={() => handleSubmit(onSubmit)()}
                            >
                                {isSubmitting
                                    ? "Actualizando..."
                                    : "Actualizar Talla"}
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </motion.div>

            {/* Modal de confirmación */}
            <Modal
                backdrop="blur"
                classNames={{
                    base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                    header: "border-b border-gray-200 dark:border-gray-700",
                    body: "py-6",
                    footer: "border-t border-gray-200 dark:border-gray-700",
                }}
                isOpen={isOpen}
                placement="center"
                onClose={onClose}
            >
                <ModalContent>
                    <ModalHeader>Cambios sin guardar</ModalHeader>
                    <ModalBody>
                        ¿Estás seguro de que deseas salir? Los cambios se
                        perderán.
                    </ModalBody>
                    <ModalFooter>
                        <Button variant="flat" onPress={onClose}>
                            Continuar editando
                        </Button>
                        <Button
                            color="danger"
                            onPress={() => {
                                onClose();
                                router.push("/dashboard/sizes");
                            }}
                        >
                            Salir sin guardar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </div>
    );
}
