import { useReducer, useEffect, useMemo } from "react";

import { useOrdersParams } from "./useOrdersParams";

// Tipos para nuestro estado
export type ViewType = "dashboard" | "calendar";
type SortDirection = "ascending" | "descending";

export interface OrdersState {
    search: string;
    status: string;
    contractor: string;
    page: number;
    limit: number;
    sortBy: string;
    sortDirection: SortDirection;
    view: ViewType;
}

// Acciones tipadas para el reducer
type OrdersAction =
    | { type: "SET_SEARCH"; payload: string }
    | { type: "SET_STATUS"; payload: string }
    | { type: "SET_CONTRACTOR"; payload: string }
    | { type: "SET_PAGE"; payload: number }
    | { type: "SET_LIMIT"; payload: number }
    | {
          type: "SET_SORT";
          payload: { column: string; direction: SortDirection };
      }
    | { type: "SET_VIEW"; payload: ViewType }
    | { type: "RESET_FILTERS" };

// Reducer para gestionar transiciones de estado de forma predecible
function ordersReducer(state: OrdersState, action: OrdersAction): OrdersState {
    switch (action.type) {
        case "SET_SEARCH":
            return { ...state, search: action.payload, page: 1 };
        case "SET_STATUS":
            return { ...state, status: action.payload, page: 1 };
        case "SET_CONTRACTOR":
            return { ...state, contractor: action.payload, page: 1 };
        case "SET_PAGE":
            return { ...state, page: action.payload };
        case "SET_LIMIT":
            return { ...state, limit: action.payload, page: 1 };
        case "SET_SORT":
            return {
                ...state,
                sortBy: action.payload.column,
                sortDirection: action.payload.direction,
            };
        case "SET_VIEW":
            // Limpiar parámetros específicos según la vista
            if (action.payload === "dashboard") {
                return {
                    ...state,
                    view: action.payload,
                    // No necesitamos parámetros específicos de calendario
                };
            } else {
                return {
                    ...state,
                    view: action.payload,
                    // Reseteamos parámetros específicos de dashboard
                    page: 1,
                };
            }
        case "RESET_FILTERS":
            return {
                ...state,
                search: "",
                status: "",
                contractor: "",
                page: 1,
            };
        default:
            return state;
    }
}

/**
 * Hook que integra parámetros URL y state para órdenes
 */
export function useOrdersState() {
    // Obtener parámetros de URL
    const { params, updateParams } = useOrdersParams();

    // Inicializar reducer con valores de URL
    const [state, dispatch] = useReducer(ordersReducer, params);

    // Sincronizar cambios de estado con URL usando debounce, excepto en vista de calendario
    useEffect(() => {
        // Si estamos en vista de calendario, no actualizar URL para evitar ciclos
        if (state.view === "calendar") {
            return;
        }

        // Verificar si realmente hubo cambios significativos comparando con los params actuales
        const hasChanges =
            state.search !== params.search ||
            state.status !== params.status ||
            state.contractor !== params.contractor ||
            state.page !== params.page ||
            state.limit !== params.limit ||
            state.sortBy !== params.sortBy ||
            state.sortDirection !== params.sortDirection ||
            state.view !== params.view;

        // Solo actualizar URL si hubo cambios reales
        if (!hasChanges) {
            return;
        }

        // Solo para otras vistas, actualizamos los parámetros URL
        const debounceTimer = setTimeout(() => {
            updateParams(state);
        }, 300);

        return () => clearTimeout(debounceTimer);
    }, [state, params, updateParams]);

    // API de acciones para mejorar legibilidad
    const actions = useMemo(
        () => ({
            setSearch: (search: string) =>
                dispatch({ type: "SET_SEARCH", payload: search }),
            setStatus: (status: string) =>
                dispatch({ type: "SET_STATUS", payload: status }),
            setContractor: (contractor: string) =>
                dispatch({ type: "SET_CONTRACTOR", payload: contractor }),
            setPage: (page: number) =>
                dispatch({ type: "SET_PAGE", payload: page }),
            setLimit: (limit: number) =>
                dispatch({ type: "SET_LIMIT", payload: limit }),
            setSort: (column: string, direction: SortDirection) =>
                dispatch({ type: "SET_SORT", payload: { column, direction } }),
            setView: (view: ViewType) =>
                dispatch({ type: "SET_VIEW", payload: view }),
            resetFilters: () => dispatch({ type: "RESET_FILTERS" }),
        }),
        [dispatch],
    );

    return { state, actions };
}
