"use client";

import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { debounce } from "lodash";

export interface Suggestion {
    id: string;
    value: string;
    label: string;
    type:
        | "cutOrder"
        | "orderPart"
        | "orderCode"
        | "customer"
        | "part"
        | "model";
    category?: string;
    icon?: string;
    metadata?: {
        customerName?: string;
        orderDate?: string;
        modelCode?: string;
        cutOrder?: string;
        ordersCount?: number;
        orderCode?: string;
        description?: string;
        orderId?: string;
        recentOrderId?: string;
        recentCutOrder?: string;
    };
}

interface UseAutocompleteProps {
    searchValue: string;
    onSearchChange: (value: string) => void;
    getSuggestions: (term: string) => Promise<Suggestion[]>;
    onSelect: (suggestion: Suggestion) => void;
    debounceMs?: number;
    minChars?: number;
}

export function useAutocomplete({
    searchValue,
    onSearchChange,
    getSuggestions,
    onSelect,
    debounceMs = 300,
    minChars = 2,
}: UseAutocompleteProps) {
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const inputRef = useRef<HTMLInputElement>(null);

    // Debounced search function
    const debouncedSearch = useMemo(
        () =>
            debounce(async (term: string) => {
                if (term.length < minChars) {
                    setSuggestions([]);
                    setShowSuggestions(false);

                    return;
                }

                setIsLoading(true);
                try {
                    const results = await getSuggestions(term);

                    setSuggestions(results);
                    setShowSuggestions(true);
                    setSelectedIndex(-1);
                } catch {
                    // Error getting suggestions
                    setSuggestions([]);
                } finally {
                    setIsLoading(false);
                }
            }, debounceMs),
        [getSuggestions, minChars, debounceMs],
    );

    // Effect to trigger search when value changes
    useEffect(() => {
        debouncedSearch(searchValue);

        return () => {
            debouncedSearch.cancel();
        };
    }, [searchValue, debouncedSearch]);

    // Keyboard navigation handler
    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (!showSuggestions || suggestions.length === 0) return;

            switch (e.key) {
                case "ArrowDown":
                    e.preventDefault();
                    setSelectedIndex((prev) =>
                        prev < suggestions.length - 1 ? prev + 1 : 0,
                    );
                    break;

                case "ArrowUp":
                    e.preventDefault();
                    setSelectedIndex((prev) =>
                        prev > 0 ? prev - 1 : suggestions.length - 1,
                    );
                    break;

                case "Enter":
                    e.preventDefault();
                    if (
                        selectedIndex >= 0 &&
                        selectedIndex < suggestions.length
                    ) {
                        handleSelect(suggestions[selectedIndex]);
                    }
                    break;

                case "Escape":
                    e.preventDefault();
                    setShowSuggestions(false);
                    setSelectedIndex(-1);
                    break;

                case "Tab":
                    if (suggestions.length > 0 && selectedIndex === -1) {
                        e.preventDefault();
                        handleSelect(suggestions[0]);
                    }
                    break;
            }
        },
        [showSuggestions, suggestions, selectedIndex],
    );

    // Handle suggestion selection
    const handleSelect = useCallback(
        (suggestion: Suggestion) => {
            onSearchChange(suggestion.value);
            onSelect(suggestion);
            setShowSuggestions(false);
            setSelectedIndex(-1);

            // Keep focus on input
            setTimeout(() => {
                inputRef.current?.focus();
            }, 0);
        },
        [onSearchChange, onSelect],
    );

    // Handle input blur
    const handleBlur = useCallback(() => {
        // Delay to allow click on suggestion
        setTimeout(() => {
            setShowSuggestions(false);
            setSelectedIndex(-1);
        }, 200);
    }, []);

    // Handle input focus
    const handleFocus = useCallback(() => {
        if (searchValue.length >= minChars && suggestions.length > 0) {
            setShowSuggestions(true);
        }
    }, [searchValue, minChars, suggestions]);

    return {
        suggestions,
        isLoading,
        showSuggestions,
        selectedIndex,
        inputRef,
        handleKeyDown,
        handleSelect,
        handleBlur,
        handleFocus,
        setShowSuggestions,
    };
}
