import { RemissionPreview } from "@/types/remission";
import {
    generateRemissionPDF,
    downloadRemissionPDF,
    previewRemissionPDF,
} from "@/lib/pdf/generator";

/**
 * Service for handling PDF generation of remissions
 */
export class RemissionPDFService {
    /**
     * Transform RemissionPreview data to RemissionPDF format
     */
    static transformToRemissionPDF(preview: any): any {
        return {
            // Basic info
            folio: preview.folio,
            date: preview.date,

            // Contractor
            contractor: preview.contractor,

            // Items with calculated totals
            items: preview.items,

            // Notes (only external notes go to PDF)
            externalNotes: preview.externalNotes,

            // Signatures
            deliveredBy: preview.deliveredBy,
            receivedBy: preview.receivedBy,
            authorizedBy: preview.authorizedBy,

            // Metadata
            metadata: {
                generatedAt: new Date().toISOString(),
                version: "1.0",
            },
        };
    }

    /**
     * Generate PDF blob from remission data
     */
    static async generatePDF(
        preview: RemissionPreview,
        orientation: "portrait" | "landscape" = "portrait",
    ): Promise<Blob> {
        const pdfData = this.transformToRemissionPDF(preview);

        return generateRemissionPDF({
            data: pdfData,
            orientation,
        });
    }

    /**
     * Generate and download PDF
     */
    static async downloadPDF(
        preview: RemissionPreview,
        orientation: "portrait" | "landscape" = "portrait",
        fileName?: string,
    ): Promise<void> {
        const pdfData = this.transformToRemissionPDF(preview);

        await downloadRemissionPDF({
            data: pdfData,
            orientation,
            fileName: fileName || `remision-${preview.folio}.pdf`,
        });
    }

    /**
     * Generate and preview PDF in new tab
     */
    static async previewPDF(
        preview: RemissionPreview,
        orientation: "portrait" | "landscape" = "portrait",
    ): Promise<void> {
        const pdfData = this.transformToRemissionPDF(preview);

        await previewRemissionPDF({
            data: pdfData,
            orientation,
        });
    }

    /**
     * Generate PDF and return as base64 string
     */
    static async generateBase64(
        preview: RemissionPreview,
        orientation: "portrait" | "landscape" = "portrait",
    ): Promise<string> {
        const blob = await this.generatePDF(preview, orientation);

        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onloadend = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    /**
     * Validate remission data before PDF generation
     */
    static validateRemissionData(preview: RemissionPreview): {
        isValid: boolean;
        errors: string[];
    } {
        const errors: string[] = [];

        if (!preview.folio) errors.push("Folio es requerido");
        if (!preview.contractor?.name)
            errors.push("Nombre del contratista es requerido");
        if (!preview.items || preview.items.length === 0) {
            errors.push("Debe tener al menos un item");
        }

        // Validate items
        preview.items?.forEach((item: any, index: number) => {
            if (!item.model)
                errors.push(`Item ${index + 1}: Modelo es requerido`);
            if (!item.color)
                errors.push(`Item ${index + 1}: Color es requerido`);
            if (item.quantity <= 0)
                errors.push(`Item ${index + 1}: Cantidad debe ser mayor a 0`);
        });

        return {
            isValid: errors.length === 0,
            errors,
        };
    }
}

// Export for convenience
export const {
    transformToRemissionPDF,
    generatePDF,
    downloadPDF,
    previewPDF,
    generateBase64,
    validateRemissionData,
} = RemissionPDFService;
