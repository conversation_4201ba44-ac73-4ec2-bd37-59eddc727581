import { Suspense } from "react";

import { auth } from "@/lib/auth-helpers";
import { Spinner } from "@/shared/components/ui/hero-ui-client";

import UnifiedOrdersClientPage from "./UnifiedClientPage";

/**
 * Página de órdenes con el nuevo sistema de diseño unificado
 * Esta página demuestra cómo usar los nuevos componentes del dashboard
 */
export default async function UnifiedOrdersPage() {
    const session = await auth();
    const currentUserId = session?.user?.id;
    const userRole = session?.user?.role?.name || session?.user?.role?.id;

    return (
        <Suspense
            fallback={
                <div className="flex items-center justify-center min-h-[50vh]">
                    <Spinner label="Cargando órdenes..." size="lg" />
                </div>
            }
        >
            <UnifiedOrdersClientPage
                currentUserId={currentUserId}
                userRole={userRole}
            />
        </Suspense>
    );
}
