"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";
import { auth } from "@/lib/auth-helpers";

const assignTransporterSchema = z.object({
    packingId: z.string(),
    transporterId: z.string(),
    vehicleInfo: z.string().optional(),
    driverName: z.string().min(1, "Nombre del conductor requerido"),
    driverPhone: z.string().min(1, "Teléfono del conductor requerido"),
});

export async function assignTransporter(
    data: z.infer<typeof assignTransporterSchema>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = assignTransporterSchema.parse(data);

        // Verificar que el packing existe y está en estado apropiado
        const packing = await db.packing.findUnique({
            where: { id: validatedData.packingId },
            include: { status: true },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        if (
            packing.status.name === "Entregado" ||
            packing.status.name === "Cancelado"
        ) {
            return {
                success: false,
                error: "No se puede asignar transporte en este estado",
            };
        }

        // Verificar que el transportista existe
        const transporter = await db.contractor.findUnique({
            where: { id: validatedData.transporterId },
        });

        if (!transporter) {
            return { success: false, error: "Transportista no encontrado" };
        }

        // Actualizar packing con información de transporte
        const updatedPacking = await db.packing.update({
            where: { id: validatedData.packingId },
            data: {
                transporterId: validatedData.transporterId,
                vehicleInfo: validatedData.vehicleInfo,
                driverName: validatedData.driverName,
                driverPhone: validatedData.driverPhone,
            },
            include: {
                transporter: true,
                status: true,
            },
        });

        // Registrar en historial
        await db.packingHistory.create({
            data: {
                packingId: validatedData.packingId,
                action: "TRANSPORT_ASSIGNED",
                metadata: {
                    assignedBy: session.user.email,
                    transporterName: transporter.name,
                    driverName: validatedData.driverName,
                    vehicleInfo: validatedData.vehicleInfo,
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${validatedData.packingId}`);

        return {
            success: true,
            data: updatedPacking,
            message: `Transporte asignado: ${transporter.name}`,
        };
    } catch (error) {
        console.error("Error assigning transporter:", error);

        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        if (error instanceof Error) {
            return { success: false, error: error.message };
        }

        return { success: false, error: "Error al asignar transportista" };
    }
}

const updateTransportInfoSchema = z.object({
    packingId: z.string(),
    vehicleInfo: z.string().optional(),
    driverName: z.string().optional(),
    driverPhone: z.string().optional(),
});

export async function updateTransportInfo(
    data: z.infer<typeof updateTransportInfoSchema>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = updateTransportInfoSchema.parse(data);

        const updatedPacking = await db.packing.update({
            where: { id: validatedData.packingId },
            data: {
                vehicleInfo: validatedData.vehicleInfo,
                driverName: validatedData.driverName,
                driverPhone: validatedData.driverPhone,
            },
        });

        await db.packingHistory.create({
            data: {
                packingId: validatedData.packingId,
                action: "TRANSPORT_INFO_UPDATED",
                metadata: {
                    updatedBy: session.user.email,
                    changes: validatedData,
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${validatedData.packingId}`);

        return { success: true, data: updatedPacking };
    } catch (error) {
        console.error("Error updating transport info:", error);

        return {
            success: false,
            error: "Error al actualizar información de transporte",
        };
    }
}

const confirmDeliverySchema = z.object({
    packingId: z.string(),
    recipientName: z.string().min(1, "Nombre del receptor requerido"),
    recipientSignature: z.string().optional(), // Base64 de la firma
    deliveryNotes: z.string().optional(),
    deliveryEvidence: z.string().optional(), // URL de foto de evidencia
});

export async function confirmDelivery(
    data: z.infer<typeof confirmDeliverySchema>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = confirmDeliverySchema.parse(data);

        // Verificar que el packing existe
        const packing = await db.packing.findUnique({
            where: { id: validatedData.packingId },
            include: {
                status: true,
                details: true,
            },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        // Obtener estado "Entregado"
        const deliveredStatus = await db.packingStatus.findFirst({
            where: { name: "Entregado" },
        });

        if (!deliveredStatus) {
            return {
                success: false,
                error: "Estado 'Entregado' no configurado",
            };
        }

        // Actualizar packing
        const updatedPacking = await db.$transaction(async (prisma) => {
            // Actualizar signatures con información de entrega
            const signatures = (packing.signatures as any) || {};

            signatures.delivery = {
                recipientName: validatedData.recipientName,
                recipientSignature: validatedData.recipientSignature,
                deliveryDate: new Date(),
                deliveryNotes: validatedData.deliveryNotes,
                deliveryEvidence: validatedData.deliveryEvidence,
                confirmedBy: session.user.email,
            };

            const updated = await prisma.packing.update({
                where: { id: validatedData.packingId },
                data: {
                    statusId: deliveredStatus.id,
                    signatures,
                },
            });

            // Registrar en historial
            await prisma.packingHistory.create({
                data: {
                    packingId: validatedData.packingId,
                    action: "DELIVERED",
                    metadata: {
                        confirmedBy: session.user.email,
                        recipientName: validatedData.recipientName,
                        deliveryDate: new Date(),
                        hasSignature: !!validatedData.recipientSignature,
                        hasEvidence: !!validatedData.deliveryEvidence,
                    },
                },
            });

            // Actualizar log de operación si existe
            await prisma.operationLog.create({
                data: {
                    operationType: "PACKING_DELIVERY",
                    entityIds: [validatedData.packingId],
                    userId: session.user.id,
                    status: "COMPLETED",
                    metadata: {
                        folio: packing.folio,
                        recipientName: validatedData.recipientName,
                        totalItems: packing.details.length,
                    },
                    completedAt: new Date(),
                },
            });

            return updated;
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${validatedData.packingId}`);

        return {
            success: true,
            data: updatedPacking,
            message: "Entrega confirmada exitosamente",
        };
    } catch (error) {
        console.error("Error confirming delivery:", error);

        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        if (error instanceof Error) {
            return { success: false, error: error.message };
        }

        return { success: false, error: "Error al confirmar entrega" };
    }
}

export async function getTransportMetrics(transporterId?: string) {
    try {
        const where = transporterId ? { transporterId } : {};

        const [total, pending, delivered, delayed] = await Promise.all([
            db.packing.count({ where }),
            db.packing.count({
                where: {
                    ...where,
                    status: {
                        name: {
                            in: [
                                "Pendiente",
                                "En Proceso",
                                "Listo para Entrega",
                            ],
                        },
                    },
                },
            }),
            db.packing.count({
                where: {
                    ...where,
                    status: { name: "Entregado" },
                },
            }),
            db.packing.count({
                where: {
                    ...where,
                    deliveryDate: { lt: new Date() },
                    status: { name: { not: "Entregado" } },
                },
            }),
        ]);

        return {
            success: true,
            data: {
                total,
                pending,
                delivered,
                delayed,
                deliveryRate: total > 0 ? (delivered / total) * 100 : 0,
            },
        };
    } catch (error) {
        console.error("Error fetching transport metrics:", error);

        return {
            success: false,
            error: "Error al obtener métricas de transporte",
        };
    }
}
