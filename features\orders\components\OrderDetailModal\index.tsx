"use client";

import React from "react";
import {
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON>ontent,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Spinner,
    addToast,
} from "@/shared/components/ui/hero-ui-client";

import OrderModalHeader from "./components/OrderModalHeader";
import OrderModalTabs from "./components/OrderModalTabs";
import CompactProgressBar from "./components/CompactProgressBar";
import OrderInfoSection from "./components/OrderInfoSection";
import OrderAssignmentsList from "./components/OrderAssignmentsList";
import OrderNotesSection from "./components/OrderNotesSection";
import OrderHistoryTimeline from "./components/OrderHistoryTimeline";
import { OrderModalErrorBoundary } from "./components/ErrorBoundary";
import { useOrderModal } from "./hooks/useOrderModal";
import { OrderModalProps } from "./types/order-modal.types";

export default function OrderDetailModal({
    isOpen,
    onClose,
    order,
    onEdit,
    onDelete,
    currentUserId,
    userRole,
    isLoadingOrder = false,
}: OrderModalProps) {
    const { activeTab, counts, setActiveTab, isDeleting, setIsDeleting } =
        useOrderModal({
            order,
            initialTab: "general",
        });

    const handleDelete = async () => {
        if (!order?.id || !onDelete) return;

        try {
            setIsDeleting(true);
            await onDelete(order.id);
            addToast({
                title: "Éxito",
                description: "Orden eliminada exitosamente",
                color: "success",
                icon: <CheckCircleIcon className="w-5 h-5" />,
            });
            onClose();
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al eliminar la orden",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        } finally {
            setIsDeleting(false);
        }
    };

    if (!order) return null;

    return (
        <Modal
            backdrop="blur"
            classNames={{
                backdrop: "bg-black/50 backdrop-blur-sm",
                base: "bg-background max-h-[95vh] max-w-7xl",
                body: "p-0 overflow-y-auto",
                wrapper: "overflow-y-hidden",
            }}
            isDismissable={false}
            isOpen={isOpen}
            shouldBlockScroll={true}
            size="5xl"
            onClose={onClose}
        >
            <ModalContent>
                {(onCloseModal) => (
                    <OrderModalErrorBoundary>
                        {/* Header with glassmorphism */}
                        <OrderModalHeader
                            createdAt={order.createdAt}
                            customer={order.customer}
                            customerAvatar={order.customerAvatar}
                            customerName={order.customerName}
                            cutOrder={order.cutOrder}
                            orderCode={order.orderCode}
                            status={order.status}
                            onDelete={handleDelete}
                            onEdit={onEdit}
                        />

                        {/* Tabs with icons and badges */}
                        <OrderModalTabs
                            activeTab={activeTab}
                            assignmentsCount={counts.assignmentsCount}
                            notesCount={counts.notesCount}
                            onTabChange={setActiveTab}
                        />

                        <ModalBody className="p-6 overflow-y-auto max-h-[calc(95vh-220px)]">
                            {isLoadingOrder ? (
                                <div className="flex items-center justify-center py-20">
                                    <div className="text-center">
                                        <Spinner color="primary" size="lg" />
                                        <p className="mt-4 text-sm text-gray-500">
                                            Cargando detalles completos...
                                        </p>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-6">
                                    {/* Progress Bar - Compact and modern */}
                                    <div className="px-4">
                                        <CompactProgressBar
                                            progress={
                                                order.progressPercentage || 0
                                            }
                                            status={order.status?.name.toLowerCase()}
                                        />
                                    </div>

                                    {/* Tab Content */}
                                    {activeTab === "general" && (
                                        <OrderInfoSection order={order} />
                                    )}

                                    {activeTab === "assignments" && (
                                        <OrderAssignmentsList order={order} />
                                    )}

                                    {activeTab === "notes" && (
                                        <OrderNotesSection
                                            currentUserId={currentUserId}
                                            order={order}
                                            userRole={userRole}
                                        />
                                    )}

                                    {activeTab === "history" && (
                                        <OrderHistoryTimeline order={order} />
                                    )}
                                </div>
                            )}
                        </ModalBody>

                        <ModalFooter>
                            <Button
                                isDisabled={isDeleting}
                                variant="light"
                                onPress={onCloseModal}
                            >
                                Cerrar
                            </Button>
                        </ModalFooter>
                    </OrderModalErrorBoundary>
                )}
            </ModalContent>
        </Modal>
    );
}
