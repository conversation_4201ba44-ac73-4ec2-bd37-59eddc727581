"use client";

import { motion } from "framer-motion";
import {
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
    MinusIcon,
} from "@heroicons/react/24/outline";

interface StatCardProps {
    label: string;
    value: number | string;
    icon?: React.ReactNode;
    trend?: "up" | "down" | "neutral";
    trendValue?: string;
    color?: "blue" | "purple" | "green" | "orange" | "pink";
    delay?: number;
}

const colorVariants = {
    blue: {
        gradient: "from-blue-500 to-indigo-600",
        bg: "from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",
        text: "text-blue-600 dark:text-blue-400",
        border: "border-blue-200 dark:border-blue-700",
    },
    purple: {
        gradient: "from-purple-500 to-pink-600",
        bg: "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20",
        text: "text-purple-600 dark:text-purple-400",
        border: "border-purple-200 dark:border-purple-700",
    },
    green: {
        gradient: "from-green-500 to-emerald-600",
        bg: "from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20",
        text: "text-green-600 dark:text-green-400",
        border: "border-green-200 dark:border-green-700",
    },
    orange: {
        gradient: "from-orange-500 to-red-600",
        bg: "from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20",
        text: "text-orange-600 dark:text-orange-400",
        border: "border-orange-200 dark:border-orange-700",
    },
    pink: {
        gradient: "from-pink-500 to-rose-600",
        bg: "from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20",
        text: "text-pink-600 dark:text-pink-400",
        border: "border-pink-200 dark:border-pink-700",
    },
};

export function StatCard({
    label,
    value,
    icon,
    trend,
    trendValue,
    color = "blue",
    delay = 0,
}: StatCardProps) {
    const colors = colorVariants[color];

    const getTrendIcon = () => {
        switch (trend) {
            case "up":
                return (
                    <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />
                );
            case "down":
                return (
                    <ArrowTrendingDownIcon className="w-4 h-4 text-red-500" />
                );
            case "neutral":
                return <MinusIcon className="w-4 h-4 text-gray-500" />;
            default:
                return null;
        }
    };

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="relative overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            transition={{
                delay,
                duration: 0.4,
                type: "spring",
                stiffness: 100,
            }}
            whileHover={{
                scale: 1.02,
                transition: { duration: 0.2 },
            }}
        >
            <div
                className={`p-6 rounded-2xl backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 border ${colors.border} shadow-xl hover:shadow-2xl transition-all duration-300`}
            >
                {/* Background gradient */}
                <div
                    className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-50 pointer-events-none`}
                />

                {/* Content */}
                <div className="relative z-10">
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                            {icon && (
                                <motion.div
                                    className={`p-2 bg-gradient-to-br ${colors.gradient} rounded-xl shadow-lg`}
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.1, rotate: 5 }}
                                >
                                    <div className="w-6 h-6 text-white">
                                        {icon}
                                    </div>
                                </motion.div>
                            )}
                            <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                    {label}
                                </p>
                            </div>
                        </div>

                        {trend && (
                            <motion.div
                                animate={{ scale: 1 }}
                                className="flex items-center gap-1"
                                initial={{ scale: 0 }}
                                transition={{
                                    delay: delay + 0.2,
                                    type: "spring",
                                }}
                            >
                                {getTrendIcon()}
                                {trendValue && (
                                    <span
                                        className={`text-xs font-medium ${
                                            trend === "up"
                                                ? "text-green-600"
                                                : trend === "down"
                                                  ? "text-red-600"
                                                  : "text-gray-600"
                                        }`}
                                    >
                                        {trendValue}
                                    </span>
                                )}
                            </motion.div>
                        )}
                    </div>

                    <motion.div
                        animate={{ scale: 1, opacity: 1 }}
                        initial={{ scale: 0.5, opacity: 0 }}
                        transition={{
                            delay: delay + 0.1,
                            duration: 0.4,
                            type: "spring",
                            stiffness: 200,
                        }}
                    >
                        <p
                            className={`text-3xl font-bold bg-gradient-to-r ${colors.gradient} bg-clip-text text-transparent`}
                        >
                            {value}
                        </p>
                    </motion.div>
                </div>

                {/* Decorative elements */}
                <motion.div
                    animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.3, 0.5, 0.3],
                    }}
                    className="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-white/20 to-transparent rounded-full blur-2xl"
                    transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut",
                    }}
                />
            </div>
        </motion.div>
    );
}
