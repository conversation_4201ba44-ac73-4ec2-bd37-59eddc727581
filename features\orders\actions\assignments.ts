"use server";

import { prisma } from "@/shared/lib/prisma";

import { ActionResponse, createErrorResponse, revalidateCache } from "./utils";

/**
 * Crea una nueva asignación para una orden
 */
export async function createOrderAssignment(
    orderId: string,
    data: {
        contractorId: string;
        garmentSizeId: string;
        quantity: number;
    },
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id: orderId },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${orderId}`,
            };
        }

        // Verificar que el contratista exista
        const contractor = await prisma.contractor.findUnique({
            where: { id: data.contractorId },
        });

        if (!contractor) {
            return {
                success: false,
                error: `No se encontró el contratista con ID ${data.contractorId}`,
            };
        }

        // Verificar que la talla de prenda exista
        const garmentSize = await prisma.garmentSize.findUnique({
            where: { id: data.garmentSizeId },
            include: { garment: true },
        });

        if (!garmentSize) {
            return {
                success: false,
                error: `No se encontró la talla de prenda con ID ${data.garmentSizeId}`,
            };
        }

        // Verificar que la talla de prenda pertenezca a la orden
        if (garmentSize.garment.orderId !== orderId) {
            return {
                success: false,
                error: "La talla de prenda seleccionada no pertenece a la orden",
            };
        }

        // Crear la asignación
        const assignment = await prisma.assignment.create({
            data: {
                orderId,
                contractorId: data.contractorId,
                garmentSizeId: data.garmentSizeId,
                quantity: data.quantity,
            },
            include: {
                contractor: true,
                garmentSize: {
                    include: {
                        size: true,
                        garment: {
                            include: {
                                model: true,
                                color: true,
                            },
                        },
                    },
                },
            },
        });

        // Revalidar caché para esta orden
        revalidateCache(orderId);

        return {
            success: true,
            data: assignment,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Actualiza una asignación existente
 */
export async function updateOrderAssignment(
    id: string,
    data: {
        contractorId?: string;
        garmentSizeId?: string;
        quantity?: number;
    },
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la asignación exista
        const assignment = await prisma.assignment.findUnique({
            where: { id },
        });

        if (!assignment) {
            return {
                success: false,
                error: `No se encontró la asignación con ID ${id}`,
            };
        }

        // Si se proporciona un nuevo contratista, verificar que exista
        if (data.contractorId) {
            const contractor = await prisma.contractor.findUnique({
                where: { id: data.contractorId },
            });

            if (!contractor) {
                return {
                    success: false,
                    error: `No se encontró el contratista con ID ${data.contractorId}`,
                };
            }
        }

        // Si se proporciona una nueva talla de prenda, verificar que exista y pertenezca a la orden
        if (data.garmentSizeId) {
            const garmentSize = await prisma.garmentSize.findUnique({
                where: { id: data.garmentSizeId },
                include: { garment: true },
            });

            if (!garmentSize) {
                return {
                    success: false,
                    error: `No se encontró la talla de prenda con ID ${data.garmentSizeId}`,
                };
            }

            // Verificar que la talla de prenda pertenezca a la misma orden
            if (garmentSize.garment.orderId !== assignment.orderId) {
                return {
                    success: false,
                    error: "La talla de prenda seleccionada no pertenece a la orden",
                };
            }
        }

        // Actualizar la asignación
        const updatedAssignment = await prisma.assignment.update({
            where: { id },
            data: {
                contractorId: data.contractorId,
                garmentSizeId: data.garmentSizeId,
                quantity: data.quantity,
            },
            include: {
                contractor: true,
                garmentSize: {
                    include: {
                        size: true,
                    },
                },
            },
        });

        // Revalidar caché para la orden relacionada
        revalidateCache(assignment.orderId);

        return {
            success: true,
            data: updatedAssignment,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Elimina una asignación
 */
export async function deleteOrderAssignment(
    id: string,
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la asignación exista
        const assignment = await prisma.assignment.findUnique({
            where: { id },
        });

        if (!assignment) {
            return {
                success: false,
                error: `No se encontró la asignación con ID ${id}`,
            };
        }

        // Guardar el ID de la orden para revalidar después
        const orderId = assignment.orderId;

        // Eliminar la asignación
        await prisma.assignment.delete({
            where: { id },
        });

        // Revalidar caché para la orden relacionada
        revalidateCache(orderId);

        return {
            success: true,
            data: { id, orderId },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene todas las asignaciones de una orden
 */
export async function getOrderAssignments(
    orderId: string,
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id: orderId },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${orderId}`,
            };
        }

        // Obtener las asignaciones
        const assignments = await prisma.assignment.findMany({
            where: { orderId },
            include: {
                contractor: true,
                garmentSize: {
                    include: {
                        size: true,
                        garment: {
                            include: {
                                model: true,
                                color: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return {
            success: true,
            data: assignments,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene un resumen de asignaciones agrupadas por contratista
 */
export async function getOrderAssignmentsSummary(
    orderId: string,
): Promise<ActionResponse<any>> {
    try {
        // Verificar que la orden exista
        const order = await prisma.order.findUnique({
            where: { id: orderId },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${orderId}`,
            };
        }

        // Obtener las asignaciones
        const assignments = await prisma.assignment.findMany({
            where: { orderId },
            include: {
                contractor: true,
                garmentSize: {
                    include: {
                        size: true,
                    },
                },
            },
        });

        // Calcular totales por contratista
        const contractorTotals = new Map<
            string,
            {
                contractor: any;
                totalQuantity: number;
                assignmentCount: number;
                sizes: Map<string, number>;
            }
        >();

        assignments.forEach((assignment) => {
            const contractorId = assignment.contractorId;
            const existing = contractorTotals.get(contractorId);

            if (existing) {
                existing.totalQuantity += assignment.quantity;
                existing.assignmentCount += 1;

                // Agregar talla al conteo
                const sizeId = assignment.garmentSize.size.id;
                const currentSizeCount = existing.sizes.get(sizeId) || 0;

                existing.sizes.set(
                    sizeId,
                    currentSizeCount + assignment.quantity,
                );
            } else {
                const sizes = new Map<string, number>();

                sizes.set(assignment.garmentSize.size.id, assignment.quantity);

                contractorTotals.set(contractorId, {
                    contractor: assignment.contractor,
                    totalQuantity: assignment.quantity,
                    assignmentCount: 1,
                    sizes,
                });
            }
        });

        // Convertir el mapa a un array con formato adecuado para JSON
        const summary = Array.from(contractorTotals.entries()).map(
            ([_, data]) => ({
                contractor: data.contractor,
                totalQuantity: data.totalQuantity,
                assignmentCount: data.assignmentCount,
                sizeBreakdown: Array.from(data.sizes.entries()).map(
                    ([sizeId, quantity]) => ({
                        sizeId,
                        quantity,
                    }),
                ),
            }),
        );

        // Calcular el total general
        const totalAssigned = summary.reduce(
            (sum, item) => sum + item.totalQuantity,
            0,
        );

        return {
            success: true,
            data: {
                details: summary,
                totalAssigned,
                totalContractors: summary.length,
                totalAssignments: assignments.length,
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
