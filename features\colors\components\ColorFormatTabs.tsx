"use client";

import React, { useState } from "react";
import { Ta<PERSON>, Tab, But<PERSON>, addToast } from "@heroui/react";
import { ClipboardIcon, CheckIcon } from "@heroicons/react/24/outline";

import { hexToRgb, hexToHsl } from "../utils/colorUtils";

interface ColorFormatTabsProps {
    hexCode: string;
    className?: string;
}

export function ColorFormatTabs({
    hexCode,
    className = "",
}: ColorFormatTabsProps) {
    const [activeTab, setActiveTab] = useState("hex");
    const [copiedFormat, setCopiedFormat] = useState<string | null>(null);

    // Calculamos diferentes formatos
    const formats = {
        hex: hexCode,
        rgb: hexToRgb(hexCode),
        hsl: hexToHsl(hexCode),
    };

    const handleCopy = async (format: string) => {
        const value = formats[format as keyof typeof formats];

        try {
            await navigator.clipboard.writeText(value);
            setCopiedFormat(format);

            addToast({
                title: "Formato copiado",
                description: `${value} copiado al portapapeles`,
                color: "success",
            });

            setTimeout(() => setCopiedFormat(null), 2000);
        } catch (err) {
            addToast({
                title: "Error al copiar",
                description: "No se pudo copiar al portapapeles",
                color: "danger",
            });
        }
    };

    return (
        <div className={`w-full ${className}`}>
            <Tabs
                aria-label="Formatos de color"
                classNames={{
                    base: "w-full",
                    tabList: "bg-gray-50 dark:bg-gray-800/50 p-0.5 rounded-lg",
                    cursor: "bg-white dark:bg-gray-700 shadow-sm",
                    tab: "px-3 py-2 text-sm font-medium data-[selected=true]:text-blue-600 dark:data-[selected=true]:text-blue-400",
                    panel: "pt-3",
                }}
                selectedKey={activeTab}
                onSelectionChange={(key) => setActiveTab(key as string)}
            >
                <Tab key="hex" title="HEX">
                    <div className="flex items-center justify-between">
                        <code className="font-mono text-sm bg-gray-50 dark:bg-gray-800/50 px-3 py-1.5 rounded-md">
                            {formats.hex}
                        </code>
                        <Button
                            isIconOnly
                            className="min-w-0 ml-2"
                            size="sm"
                            variant="flat"
                            onPress={() => handleCopy("hex")}
                        >
                            {copiedFormat === "hex" ? (
                                <CheckIcon className="w-4 h-4 text-green-500" />
                            ) : (
                                <ClipboardIcon className="w-4 h-4" />
                            )}
                        </Button>
                    </div>
                </Tab>

                <Tab key="rgb" title="RGB">
                    <div className="flex items-center justify-between">
                        <code className="font-mono text-sm bg-gray-50 dark:bg-gray-800/50 px-3 py-1.5 rounded-md">
                            {formats.rgb}
                        </code>
                        <Button
                            isIconOnly
                            className="min-w-0 ml-2"
                            size="sm"
                            variant="flat"
                            onPress={() => handleCopy("rgb")}
                        >
                            {copiedFormat === "rgb" ? (
                                <CheckIcon className="w-4 h-4 text-green-500" />
                            ) : (
                                <ClipboardIcon className="w-4 h-4" />
                            )}
                        </Button>
                    </div>
                </Tab>

                <Tab key="hsl" title="HSL">
                    <div className="flex items-center justify-between">
                        <code className="font-mono text-sm bg-gray-50 dark:bg-gray-800/50 px-3 py-1.5 rounded-md">
                            {formats.hsl}
                        </code>
                        <Button
                            isIconOnly
                            className="min-w-0 ml-2"
                            size="sm"
                            variant="flat"
                            onPress={() => handleCopy("hsl")}
                        >
                            {copiedFormat === "hsl" ? (
                                <CheckIcon className="w-4 h-4 text-green-500" />
                            ) : (
                                <ClipboardIcon className="w-4 h-4" />
                            )}
                        </Button>
                    </div>
                </Tab>
            </Tabs>
        </div>
    );
}
