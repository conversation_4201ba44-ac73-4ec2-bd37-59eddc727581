// lib/zod.ts
import { z } from "zod";

export const signInSchema = z.object({
    email: z
        .string({ required_error: "El correo electrónico es obligatorio" })
        .min(1, "El correo electrónico es obligatorio")
        .email("Correo electrónico inválido"),
    password: z
        .string({ required_error: "La contraseña es obligatoria" })
        .min(1, "La contraseña es obligatoria"),
    // .min(8, "La contraseña debe tener al menos 8 caracteres")
    // .max(32, "La contraseña no puede exceder los 32 caracteres"),
});

export const signUpSchema = signInSchema
    .extend({
        name: z
            .string()
            .min(1, "El nombre es obligatorio")
            .max(50, "El nombre no puede exceder los 50 caracteres"),
        confirmPassword: z
            .string()
            .min(1, "La confirmación de contraseña es obligatoria"),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Las contraseñas no coinciden",
        path: ["confirmPassword"],
    });
