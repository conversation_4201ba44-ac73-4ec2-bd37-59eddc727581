"use client";

import { forwardRef, HTMLAttributes, ReactNode } from "react";

import { cn } from "@/lib/utils";

interface AuthCardProps extends HTMLAttributes<HTMLDivElement> {
    children: ReactNode;
}

export const AuthCard = forwardRef<HTMLDivElement, AuthCardProps>(
    ({ children, className, ...props }, ref) => {
        return (
            <div className="relative w-full max-w-md mx-auto">
                {/* Efecto glass sutil en el fondo */}
                <div
                    className="absolute inset-0 bg-gradient-to-r from-primary-400 to-primary-600 
                     dark:from-blue-600 dark:to-purple-600
                     shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl
                     opacity-20 dark:opacity-10 blur-xl"
                />

                {/* Card principal con glassmorphism */}
                <div
                    ref={ref}
                    className={cn(
                        "relative bg-white/70 dark:bg-slate-800/50 backdrop-filter backdrop-blur-sm",
                        "border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-xl",
                        "p-8 sm:p-10",
                        className,
                    )}
                    {...props}
                >
                    {children}
                </div>
            </div>
        );
    },
);

AuthCard.displayName = "AuthCard";
