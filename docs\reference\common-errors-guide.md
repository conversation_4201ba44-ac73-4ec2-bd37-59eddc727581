# Guía de Errores Comunes y Soluciones - Proyecto Lohari

## 🚨 Errores de Base de Datos (Prisma)

### Error: P2002 - Violación de constraint único
```
PrismaClientKnownRequestError: Unique constraint failed on the fields: (`name`)
```

**Causa**: Intentar crear un registro con un valor que ya existe en un campo único.

**Solución**:
```typescript
// Verificar antes de crear
const exists = await prisma.model.findUnique({
  where: { name: data.name }
});

if (exists) {
  return { success: false, error: "El nombre ya existe" };
}
```

### Error: P2003 - Violación de foreign key
```
Foreign key constraint failed on the field: `customerId`
```

**Causa**: Intentar referenciar un registro que no existe.

**Solución**:
```typescript
// Verificar que la referencia existe
const customer = await prisma.customer.findUnique({
  where: { id: customerId }
});

if (!customer) {
  return { success: false, error: "Cliente no encontrado" };
}
```

### Error: P2025 - Registro no encontrado
```
An operation failed because it depends on one or more records that were required but not found
```

**Causa**: Intentar actualizar/eliminar un registro que no existe.

**Solución**:
```typescript
try {
  const result = await prisma.model.update({
    where: { id },
    data: updateData
  });
} catch (error) {
  if (error.code === 'P2025') {
    return { success: false, error: "Registro no encontrado" };
  }
}
```

### Error: Connection pool timeout
```
Error: P1017: Server has closed the connection
```

**Causa**: Conexiones agotadas o timeout en la base de datos.

**Solución**:
1. Usar el servicio de conexión con reintentos:
```typescript
import { db } from '@/shared/lib/db';
// El servicio maneja reintentos automáticamente
```

2. Verificar variables de entorno:
```env
DATABASE_URL="postgresql://..."
DATABASE_URL_DIRECT="postgresql://..."  # Para migraciones
```

## 🔐 Errores de Autenticación

### Error: NEXTAUTH_URL missing
```
[next-auth][error][CLIENT_FETCH_ERROR] 
https://example.com/api/auth/session TypeError: Failed to fetch
```

**Causa**: Variable de entorno NEXTAUTH_URL no configurada.

**Solución**:
```env
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
```

### Error: JWT decode failed
```
[next-auth][error][JWT_SESSION_ERROR] 
JWT decode failed: invalid signature
```

**Causa**: NEXTAUTH_SECRET cambió o no coincide.

**Solución**:
1. Generar nuevo secret:
```bash
openssl rand -base64 32
```
2. Actualizar en todas las instancias

### Error: Callback URL not allowed
```
Error: The provided callbackUrl is not allowed
```

**Causa**: URL de callback no está en la lista permitida.

**Solución**:
```typescript
// auth.ts
callbacks: {
  async redirect({ url, baseUrl }) {
    if (url.startsWith(baseUrl)) return url;
    if (url.startsWith("/")) return new URL(url, baseUrl).toString();
    return baseUrl;
  }
}
```

## ⚛️ Errores de React/Next.js

### Error: Hydration mismatch
```
Warning: Text content did not match. Server: "X" Client: "Y"
```

**Causa**: Contenido diferente entre servidor y cliente.

**Solución**:
```typescript
// Usar estado mounted para contenido dinámico
const [mounted, setMounted] = useState(false);

useEffect(() => {
  setMounted(true);
}, []);

if (!mounted) return <Skeleton />;
```

### Error: Can't access server functions from client
```
Error: Server actions must be async functions
```

**Causa**: Intentar usar server action sin "use server".

**Solución**:
```typescript
// ✅ Correcto
"use server";

export async function serverAction() {
  // código del servidor
}

// ❌ Incorrecto - falta async o "use server"
export function serverAction() {
  // esto fallará
}
```

### Error: Dynamic server usage
```
Error: Dynamic server usage: headers
```

**Causa**: Usar funciones dinámicas en contexto estático.

**Solución**:
```typescript
// Marcar como dinámico si es necesario
export const dynamic = 'force-dynamic';

// O usar en server component
async function ServerComponent() {
  const headersList = headers();
  // ...
}
```

## 📝 Errores de Formularios

### Error: Zod validation failed
```
ZodError: Invalid input
```

**Causa**: Datos no cumplen el esquema.

**Solución**:
```typescript
try {
  const validated = schema.parse(data);
} catch (error) {
  if (error instanceof z.ZodError) {
    return {
      success: false,
      error: error.errors[0]?.message || "Datos inválidos"
    };
  }
}
```

### Error: Form submission blocked
```
Form submission canceled because the form is not connected
```

**Causa**: Formulario desmontado durante submit.

**Solución**:
```typescript
const onSubmit = async (data) => {
  // Prevenir desmontaje prematuro
  event?.preventDefault();
  
  try {
    await submitAction(data);
  } catch (error) {
    // manejar error sin navegar
  }
};
```

## 🎨 Errores de UI/UX

### Error: Missing HeroUI theme
```
Error: useTheme must be used within a NextUIProvider
```

**Causa**: Componente fuera del provider.

**Solución**:
```typescript
// app/providers.tsx
export function Providers({ children }) {
  return (
    <NextUIProvider>
      <NextThemesProvider attribute="class" defaultTheme="light">
        {children}
      </NextThemesProvider>
    </NextUIProvider>
  );
}
```

### Error: Framer Motion hydration
```
Warning: Prop `style` did not match
```

**Causa**: Animaciones ejecutándose en servidor.

**Solución**:
```typescript
// Usar isLoaded para animaciones
const [isLoaded, setIsLoaded] = useState(false);

useEffect(() => {
  setIsLoaded(true);
}, []);

return (
  <motion.div
    initial={isLoaded ? "initial" : false}
    animate={isLoaded ? "animate" : false}
  />
);
```

## 🔄 Errores de Estado (SWR)

### Error: SWR mutation failed
```
Error: Failed to mutate
```

**Causa**: Mutación optimista sin rollback.

**Solución**:
```typescript
const { mutate } = useSWR(key, fetcher);

await mutate(
  async (currentData) => {
    const result = await serverAction(data);
    if (!result.success) throw new Error(result.error);
    return result.data;
  },
  {
    optimisticData: tempData,
    rollbackOnError: true,  // Importante!
    revalidate: true
  }
);
```

### Error: Infinite revalidation loop
```
Warning: Maximum update depth exceeded
```

**Causa**: Dependencias incorrectas en SWR.

**Solución**:
```typescript
// ✅ Correcto - key estable
const { data } = useSWR(
  ['orders', filters], // Array como key
  fetcher,
  {
    revalidateOnFocus: false,
    revalidateOnReconnect: false
  }
);

// ❌ Incorrecto - key inestable
const { data } = useSWR(
  { type: 'orders', ...filters }, // Objeto nuevo cada render
  fetcher
);
```

## 🚀 Errores de Performance

### Error: Large bundle size
```
Warning: The page /dashboard/orders has a large bundle size
```

**Causa**: Importaciones no optimizadas.

**Solución**:
```typescript
// ✅ Lazy loading
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Skeleton />
});

// ✅ Importación específica
import { Button } from '@heroui/button';

// ❌ Evitar
import * as HeroUI from '@heroui/react';
```

### Error: Memory leak
```
Warning: Can't perform a React state update on an unmounted component
```

**Causa**: Actualizaciones después de desmontar.

**Solución**:
```typescript
useEffect(() => {
  let mounted = true;
  
  const fetchData = async () => {
    const data = await getData();
    if (mounted) {
      setData(data);
    }
  };
  
  fetchData();
  
  return () => {
    mounted = false;
  };
}, []);
```

## 🛠️ Errores de Build

### Error: Module not found
```
Module not found: Can't resolve '@/features/orders'
```

**Causa**: Path alias incorrecto o export faltante.

**Solución**:
1. Verificar tsconfig.json:
```json
{
  "paths": {
    "@/features/*": ["./features/*"]
  }
}
```

2. Verificar export en index.ts:
```typescript
// features/orders/index.ts
export * from './components';
export * from './hooks';
export * from './actions';
```

### Error: Type error in production build
```
Type error: Property 'X' does not exist on type 'Y'
```

**Causa**: TypeScript strict deshabilitado en desarrollo.

**Solución**:
```typescript
// Ser explícito con tipos
interface Props {
  data: OrderData;
  onAction?: (id: string) => void;
}

// Evitar any
const data: unknown = await fetchData();
const typed = data as OrderData; // Validar primero!
```

## 📋 Checklist de Prevención

### Antes de Desarrollar
- [ ] Variables de entorno configuradas
- [ ] Base de datos migrada y seed ejecutado
- [ ] Dependencias actualizadas
- [ ] TypeScript paths configurados

### Durante el Desarrollo
- [ ] Usar `handleDbError` para errores de BD
- [ ] Validar con Zod antes de procesar
- [ ] Verificar autenticación en server actions
- [ ] Manejar estados de carga y error

### Antes de Commit
- [ ] Ejecutar `npm run lint`
- [ ] Ejecutar `npm run build` localmente
- [ ] Verificar no hay console.logs
- [ ] Revisar que no hay datos hardcodeados

### En Producción
- [ ] Verificar todas las variables de entorno
- [ ] Logs configurados correctamente
- [ ] Backups de base de datos activos
- [ ] Monitoreo de errores activo

## 🔍 Herramientas de Debugging

### Para Base de Datos
```bash
# Ver queries de Prisma
DEBUG=prisma:query npm run dev

# Prisma Studio
npx prisma studio
```

### Para React/Next.js
```typescript
// Debug renders
useEffect(() => {
  console.log('Component rendered:', { props, state });
});

// Debug server actions
console.log('[ServerAction]', { action: 'create', data });
```

### Para Performance
```typescript
// Medir tiempos
console.time('operation');
await heavyOperation();
console.timeEnd('operation');

// React DevTools Profiler
// Chrome DevTools Performance
```

## 💡 Tips Generales

1. **Siempre manejar errores**: No asumir happy path
2. **Mensajes claros**: Errores en español para usuarios
3. **Logging útil**: Incluir contexto en logs
4. **Validar entrada**: Nunca confiar en datos del cliente
5. **Testear edge cases**: Null, undefined, arrays vacíos
6. **Documentar soluciones**: Actualizar esta guía