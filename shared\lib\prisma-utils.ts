import { Prisma } from "@prisma/client";

import { prisma } from "./prisma";

// Definimos los roles válidos como constantes
export const VALID_ROLES = ["ADMIN", "STAFF", "CUSTOMER"] as const;
export type Role = (typeof VALID_ROLES)[number];

export type PaginationParams = {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
};

export type PaginationMeta = {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasMore: boolean;
};

export class PrismaUtils {
    static validatePagination(page: number, limit: number) {
        if (page < 1 || limit < 1 || limit > 100) {
            throw new Error("INVALID_PAGINATION");
        }

        return { page, limit };
    }

    static validateRole(role: string): string | undefined {
        if (!role) return undefined;
        if (VALID_ROLES.includes(role as any)) {
            return role;
        }
        throw new Error("INVALID_ROLE");
    }

    static async withTransaction<T>(
        callback: (tx: Prisma.TransactionClient) => Promise<T>,
    ): Promise<T> {
        return prisma.$transaction(callback);
    }

    static calculatePaginationMeta(
        total: number,
        page: number,
        limit: number,
    ): PaginationMeta {
        const totalPages = Math.ceil(total / limit);

        return {
            total,
            page,
            limit,
            totalPages,
            hasMore: page < totalPages,
        };
    }

    static buildSearchFilter(
        search: string,
        fields: string[],
    ): Prisma.UserWhereInput {
        if (!search) return {};

        return {
            OR: fields.map((field) => ({
                [field]: {
                    contains: search,
                    mode: "insensitive",
                },
            })),
        };
    }
}
