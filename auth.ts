// auth.ts
import type { NextAuthOptions } from "next-auth";

import Credentials from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { Role } from "@prisma/client";

// Importar el cliente de Prisma de manera condicional para que sea compatible con Edge
// En lugar de importar directamente de prisma.ts, usamos una importación dinámica
// que será resuelta en tiempo de ejecución
let prisma: any;

// Si estamos en el navegador o en Edge Runtime, no inicializar Prisma
if (typeof window === "undefined" && !process.env.NEXT_RUNTIME) {
    // Entorno Node.js normal - importar el cliente Prisma completo
    const { prisma: fullPrisma } = require("@/shared/lib/prisma");

    prisma = fullPrisma;
} else {
    // Entorno Edge o navegador - usar versión segura
    prisma = {}; // Cliente vacío para Edge
}

import { verifyPassword } from "@/features/auth/utils";

// Duración de la sesión
const REGULAR_SESSION_TIME = 24 * 60 * 60; // 24 horas en segundos
const REMEMBER_ME_SESSION_TIME = 30 * 24 * 60 * 60; // 30 días en segundos

export const authConfig = {
    // Adapter para Prisma (solo se carga en runtime completo, no en Edge)
    adapter:
        typeof window === "undefined" && !process.env.NEXT_RUNTIME && prisma
            ? PrismaAdapter(prisma)
            : undefined,
    cookies: {
        sessionToken: {
            name: `authjs.session-token`,
            options: {
                httpOnly: true,
                sameSite: "lax",
                path: "/",
                secure: process.env.NODE_ENV === "production",
            },
        },
        callbackUrl: {
            name: `authjs.callback-url`,
            options: {
                sameSite: "lax",
                path: "/",
                secure: process.env.NODE_ENV === "production",
            },
        },
        csrfToken: {
            name: `authjs.csrf-token`,
            options: {
                httpOnly: true,
                sameSite: "lax",
                path: "/",
                secure: process.env.NODE_ENV === "production",
            },
        },
    },
    providers: [
        Credentials({
            id: "credentials",
            name: "credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                remember: { label: "Remember me", type: "checkbox" },
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }

                try {
                    // Este código solo se ejecutará en un entorno Server, no en Edge
                    // El middleware de Edge no llega a llamar a authorize
                    const { prisma } = await import("@/shared/lib/prisma");

                    const user = await prisma.user.findUnique({
                        where: { email: credentials.email as string },
                        include: {
                            role: true,
                        },
                    });

                    if (!user?.password) {
                        return null;
                    }

                    const isValid = await verifyPassword(
                        credentials.password as string,
                        user.password,
                    );

                    if (!isValid) return null;

                    // Añadimos el remember a los datos del usuario para usarlo en el callback jwt
                    return {
                        id: user.id,
                        email: user.email,
                        name: user.name || "",
                        role: user.role,
                        image: user.image || "",
                        remember: credentials.remember === "true",
                    };
                } catch (error) {
                    return null;
                }
            },
        }),
    ],
    session: {
        strategy: "jwt",
        maxAge: REGULAR_SESSION_TIME, // Por defecto 24 horas
        updateAge: 60 * 60, // Actualizar la sesión cada hora
    },
    pages: {
        signIn: "/login",
        error: "/error",
    },
    callbacks: {
        async jwt({ token, user, trigger, session }) {
            if (user) {
                token.role = user.role;
                token.id = user.id;
                // Si el usuario marcó "recuérdame", extendemos la duración del token
                if (user.remember) {
                    token.maxAge = REMEMBER_ME_SESSION_TIME;
                }
            }
            if (trigger === "update" && session) {
                token.role = session.user.role;
            }

            return token;
        },
        async session({ session, token }) {
            if (session?.user) {
                session.user.id = token.id as string;
                session.user.role = token.role as Role;
            }

            return session;
        },
    },
    events: {
        async signIn({ user }) {
            try {
                if (user.id) {
                    // Importación dinámica para ejecutar solo en server, no en Edge
                    const { prisma } = await import("@/shared/lib/prisma");

                    await prisma.user.update({
                        where: { id: user.id },
                        data: { updatedAt: new Date() },
                    });
                }
            } catch (error) {}
        },
    },
    debug: false, // process.env.NODE_ENV === "development",
} satisfies NextAuthOptions;
