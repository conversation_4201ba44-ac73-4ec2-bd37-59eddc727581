"use server";

import { db, handleDbError } from "@/shared/lib/db";

import { revalidateCache } from "./query";

/**
 * Elimina un color por ID
 */
export async function deleteColor(id: string | null | undefined) {
    if (!id) return { success: false, error: "ID no válido" };

    return await handleDbError(async () => {
        // Verificar si el color tiene garments asociadas
        const colorWithCount = await db.color.findUnique({
            where: { id },
            include: {
                _count: {
                    select: { garments: true },
                },
            },
        });

        if (!colorWithCount) {
            return { success: false, error: "Color no encontrado" };
        }

        // Verificar si el color está siendo utilizado
        if (colorWithCount._count.garments > 0) {
            return {
                success: false,
                error: `No se puede eliminar este color porque está siendo utilizado en ${colorWithCount._count.garments} prendas`,
            };
        }

        // Eliminar el color
        await db.color.delete({ where: { id } });

        // Revalidar rutas
        revalidateCache();

        return { success: true };
    }, "Error al eliminar color");
}
