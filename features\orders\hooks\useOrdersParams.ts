import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { useCallback, useMemo } from "react";

/**
 * Hook para gestionar los parámetros de URL de las órdenes de manera centralizada
 */
export function useOrdersParams() {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Extraer parámetros de la URL de forma coherente
    const params = useMemo(
        () => ({
            search: searchParams.get("search") || "",
            status: searchParams.get("status") || "",
            contractor: searchParams.get("contractor") || "",
            page: parseInt(searchParams.get("page") || "1", 10),
            limit: parseInt(searchParams.get("limit") || "10", 10),
            sortBy: searchParams.get("sortBy") || "estimatedDeliveryDate",
            sortDirection: (searchParams.get("sortDirection") ||
                "ascending") as "ascending" | "descending",
            view: (searchParams.get("view") || "dashboard") as
                | "dashboard"
                | "calendar",
        }),
        [searchParams],
    );

    // Función centralizada para actualizar parámetros
    const updateParams = useCallback(
        (newParams: Partial<typeof params>) => {
            const urlParams = new URLSearchParams(searchParams.toString());
            let hasChanges = false;

            // Procesar los nuevos parámetros de forma predecible
            Object.entries(newParams).forEach(([key, value]) => {
                const currentValue = searchParams.get(key);
                const newValue =
                    value === undefined || value === "" || value === null
                        ? null
                        : String(value);

                if (currentValue !== newValue) {
                    hasChanges = true;
                    if (newValue === null) {
                        urlParams.delete(key);
                    } else {
                        urlParams.set(key, newValue);
                    }
                }
            });

            // Solo actualizar la URL si hubo cambios reales
            if (hasChanges) {
                router.replace(`${pathname}?${urlParams.toString()}`, {
                    scroll: false,
                });
            }
        },
        [router, pathname, searchParams],
    );

    return { params, updateParams };
}
