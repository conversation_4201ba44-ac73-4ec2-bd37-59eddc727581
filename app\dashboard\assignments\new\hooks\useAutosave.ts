"use client";

import { useState, useEffect, useCallback, useRef } from "react";

import { showToast } from "../utils/toast";

interface AutosaveOptions<T> {
    interval?: number;
    debounceMs?: number;
    onSave?: () => void;
    onRestore?: (data: T) => void;
    onDraftFound?: (
        draft: { data: T; timestamp: number; version: number },
        restore: () => void,
        discard: () => void,
    ) => void;
    shouldSave?: (data: T) => boolean;
    maxAge?: number; // Edad máxima del borrador en días
}

export function useAutosave<T>(
    key: string,
    data: T,
    options: AutosaveOptions<T> = {},
) {
    const {
        interval = 30000, // 30 segundos
        debounceMs = 2000, // 2 segundos
        onSave,
        onRestore,
        onDraftFound,
        shouldSave,
        maxAge = 7, // 7 días
    } = options;

    const [lastSaved, setLastSaved] = useState<Date | null>(null);
    const [isRestored, setIsRestored] = useState(false);
    const timeoutRef = useRef<NodeJS.Timeout>();

    // Función para guardar en localStorage
    const saveDraft = useCallback(() => {
        try {
            // Verificar si se debe guardar
            if (shouldSave && !shouldSave(data)) {
                return;
            }

            const draft = {
                data,
                timestamp: Date.now(),
                version: 1,
            };

            localStorage.setItem(key, JSON.stringify(draft));
            setLastSaved(new Date());

            if (onSave) {
                onSave();
            }
        } catch {
            // Error saving draft
        }
    }, [data, key, onSave, shouldSave]);

    // Función para guardar con debounce
    const debouncedSave = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
            saveDraft();
        }, debounceMs);
    }, [saveDraft, debounceMs]);

    // Función para recuperar borrador
    const restoreDraft = useCallback(() => {
        try {
            const stored = localStorage.getItem(key);

            if (!stored) {
                return null;
            }

            const draft = JSON.parse(stored);

            // Verificar edad del borrador
            const age = Date.now() - draft.timestamp;
            const maxAgeMs = maxAge * 24 * 60 * 60 * 1000;

            if (age > maxAgeMs) {
                localStorage.removeItem(key);

                return null;
            }

            setLastSaved(new Date(draft.timestamp));
            setIsRestored(true);

            if (onRestore) {
                onRestore(draft.data);
            }

            // Mostrar notificación
            showToast({
                title: "Borrador recuperado",
                description: "Se ha recuperado tu trabajo anterior",
                color: "primary",
            });

            return draft.data;
        } catch {
            // Error restoring draft

            return null;
        }
    }, [key, maxAge, onRestore]);

    // Función para limpiar borrador
    const clearDraft = useCallback(
        (silent = false) => {
            try {
                localStorage.removeItem(key);
                setLastSaved(null);
                setIsRestored(false);

                if (!silent) {
                    showToast({
                        title: "Borrador eliminado",
                        description: "Se ha descartado el borrador guardado",
                        color: "default",
                    });
                }
            } catch {
                // Error clearing draft
            }
        },
        [key],
    );

    // Auto-guardar en intervalo
    useEffect(() => {
        const intervalId = setInterval(() => {
            saveDraft();
        }, interval);

        return () => {
            clearInterval(intervalId);
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [saveDraft, interval]);

    // Guardar cuando cambian los datos (con debounce)
    useEffect(() => {
        debouncedSave();
    }, [data, debouncedSave]);

    // Intentar recuperar al montar
    useEffect(() => {
        const stored = localStorage.getItem(key);

        if (stored) {
            const draft = JSON.parse(stored);
            const age = Date.now() - draft.timestamp;
            const ageInMinutes = Math.floor(age / 60000);

            // Verificar si el borrador debe considerarse importante
            if (shouldSave && !shouldSave(draft.data)) {
                // Borrador no importante, limpiar silenciosamente
                clearDraft(true);

                return;
            }

            // Si hay callback personalizado para manejar el draft
            if (onDraftFound) {
                onDraftFound(
                    draft,
                    () => restoreDraft(),
                    () => clearDraft(),
                );
            } else {
                // Comportamiento por defecto (fallback)
                // Solo mostrar diálogo si el borrador tiene menos de 1 hora
                if (ageInMinutes < 60) {
                    const shouldRestore = window.confirm(
                        `Se encontró un borrador guardado hace ${ageInMinutes} minutos. ¿Deseas recuperarlo?`,
                    );

                    if (shouldRestore) {
                        restoreDraft();
                    } else {
                        clearDraft();
                    }
                } else {
                    // Borrador muy antiguo, recuperar automáticamente
                    restoreDraft();
                }
            }
        }
    }, []); // Solo ejecutar al montar

    return {
        lastSaved,
        isRestored,
        saveDraft,
        clearDraft,
        restoreDraft,
    };
}
