"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON>eader,
    ModalBody,
    ModalFooter,
    useDisclosure,
    addToast,
    Card,
    CardBody,
    CardHeader,
    CardFooter,
    Spinner,
    Select,
    SelectItem,
    Autocomplete,
    AutocompleteItem,
    Textarea,
} from "@heroui/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { ClipboardDocumentIcon, CheckIcon } from "@heroicons/react/24/outline";

import { useCreateNote } from "@/features/notes/hooks/useNotes";
import { getOrders } from "@/features/orders/actions";
import { getNoteStatuses, getNoteImportances } from "@/features/notes/actions";
import { noteSchema, type NoteFormData } from "@/features/notes/schemas";
import {
    NoteForm<PERSON>eader,
    NoteInfoMessage,
} from "@/features/notes/components/forms";

interface OrderOption {
    id: string;
    label: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
    parts?: Array<{ id: string; code: string }>;
}

interface NoteStatus {
    id: string;
    name: string;
    color?: string | null;
}

interface NoteImportance {
    id: string;
    name: string;
    color?: string | null;
}

export default function NewNotePage() {
    const router = useRouter();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { createNote } = useCreateNote();

    // Estados para las opciones
    const [orders, setOrders] = useState<OrderOption[]>([]);
    const [statuses, setStatuses] = useState<NoteStatus[]>([]);
    const [importances, setImportances] = useState<NoteImportance[]>([]);
    const [isLoadingOptions, setIsLoadingOptions] = useState(true);

    const {
        register,
        handleSubmit,
        formState: { errors, isDirty },
        setValue,
        watch,
    } = useForm<NoteFormData>({
        resolver: zodResolver(noteSchema),
        mode: "onChange",
        defaultValues: {
            content: "",
            orderId: "",
            statusId: "",
            importanceId: "",
        },
    });

    // Cargar datos iniciales
    useEffect(() => {
        const loadData = async () => {
            try {
                setIsLoadingOptions(true);

                // Cargar órdenes
                const ordersResult = await getOrders();

                if (ordersResult.success && ordersResult.data) {
                    setOrders(
                        ordersResult.data.orders.map((order: any) => ({
                            id: order.id,
                            label: `${order.cutOrder || "Sin código"} - ${
                                order.parts?.length > 0
                                    ? order.parts
                                          .map((p: any) => p.code)
                                          .join(", ")
                                    : "Sin partidas"
                            }`,
                            transferNumber: order.transferNumber,
                            cutOrder: order.cutOrder,
                            parts: order.parts || [],
                        })),
                    );
                }

                // Cargar estados
                const statusesResult = await getNoteStatuses();

                if (statusesResult.success && statusesResult.data) {
                    setStatuses(statusesResult.data);
                }

                // Cargar importancias
                const importancesResult = await getNoteImportances();

                if (importancesResult.success && importancesResult.data) {
                    setImportances(importancesResult.data);
                }
            } catch (error) {
                addToast({
                    title: "Error",
                    description: "Error cargando datos",
                    color: "danger",
                });
            } finally {
                setIsLoadingOptions(false);
            }
        };

        loadData();
    }, []);

    const onSubmit = async (data: NoteFormData) => {
        setIsSubmitting(true);

        try {
            const result = await createNote(data);

            if (!result.success) {
                throw new Error(result.error || "Error al crear la nota");
            }

            addToast({
                title: "Nota creada",
                description: "La nota se ha creado exitosamente",
                color: "success",
            });

            router.push("/dashboard/notes");
            router.refresh();
        } catch (error) {
            addToast({
                title: "Error",
                description:
                    error instanceof Error
                        ? error.message
                        : "Error al crear la nota",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (isDirty) {
            onOpen();
        } else {
            router.push("/dashboard/notes");
        }
    };

    if (isLoadingOptions) {
        return (
            <div className="flex items-center justify-center min-h-[60vh]">
                <Spinner label="Cargando formulario..." size="lg" />
            </div>
        );
    }

    return (
        <div className="container mx-auto max-w-2xl p-6">
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
            >
                <Card className="shadow-lg">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 pb-6">
                        <NoteFormHeader
                            description="Registra información importante sobre una orden"
                            title="Nueva Nota"
                        />
                    </CardHeader>

                    <CardBody className="p-6">
                        <form
                            className="space-y-6"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            {/* Selector de orden */}
                            <Autocomplete
                                isRequired
                                classNames={{
                                    base: "max-w-full",
                                }}
                                defaultItems={orders}
                                description="Selecciona la orden asociada a esta nota"
                                errorMessage={errors.orderId?.message}
                                isInvalid={!!errors.orderId}
                                label="Orden"
                                placeholder="Buscar orden..."
                                selectedKey={watch("orderId")}
                                size="lg"
                                startContent={
                                    <ClipboardDocumentIcon className="w-4 h-4 text-gray-400" />
                                }
                                variant="bordered"
                                onSelectionChange={(key) =>
                                    setValue("orderId", key as string)
                                }
                            >
                                {(order) => (
                                    <AutocompleteItem
                                        key={order.id}
                                        textValue={order.label}
                                    >
                                        <div className="flex flex-col">
                                            <span className="font-medium">
                                                {order.cutOrder || "Sin código"}
                                            </span>
                                            <span className="text-xs text-gray-500">
                                                Partidas:{" "}
                                                {order.parts &&
                                                order.parts.length > 0
                                                    ? order.parts
                                                          .map((p) => p.code)
                                                          .join(", ")
                                                    : "Sin partidas"}
                                            </span>
                                        </div>
                                    </AutocompleteItem>
                                )}
                            </Autocomplete>

                            {/* Selector de estado */}
                            <Select
                                isRequired
                                description="Define el estado actual de la nota"
                                errorMessage={errors.statusId?.message}
                                isInvalid={!!errors.statusId}
                                label="Estado"
                                placeholder="Selecciona un estado"
                                selectedKeys={
                                    watch("statusId") ? [watch("statusId")] : []
                                }
                                size="lg"
                                variant="bordered"
                                onSelectionChange={(keys) => {
                                    const selectedKey = Array.from(keys)[0];

                                    setValue("statusId", selectedKey as string);
                                }}
                            >
                                {statuses.map((status) => (
                                    <SelectItem
                                        key={status.id}
                                        startContent={
                                            <div
                                                className="w-3 h-3 rounded-full"
                                                style={{
                                                    backgroundColor:
                                                        status.color || "#888",
                                                }}
                                            />
                                        }
                                    >
                                        {status.name}
                                    </SelectItem>
                                ))}
                            </Select>

                            {/* Selector de importancia */}
                            <Select
                                isRequired
                                description="Define el nivel de prioridad de la nota"
                                errorMessage={errors.importanceId?.message}
                                isInvalid={!!errors.importanceId}
                                label="Importancia"
                                placeholder="Selecciona la importancia"
                                selectedKeys={
                                    watch("importanceId")
                                        ? [watch("importanceId")]
                                        : []
                                }
                                size="lg"
                                variant="bordered"
                                onSelectionChange={(keys) => {
                                    const selectedKey = Array.from(keys)[0];

                                    setValue(
                                        "importanceId",
                                        selectedKey as string,
                                    );
                                }}
                            >
                                {importances.map((importance) => (
                                    <SelectItem
                                        key={importance.id}
                                        startContent={
                                            <div
                                                className="w-3 h-3 rounded-full"
                                                style={{
                                                    backgroundColor:
                                                        importance.color ||
                                                        "#888",
                                                }}
                                            />
                                        }
                                    >
                                        {importance.name}
                                    </SelectItem>
                                ))}
                            </Select>

                            {/* Contenido de la nota */}
                            <Textarea
                                {...register("content")}
                                isRequired
                                classNames={{
                                    inputWrapper: "shadow-sm",
                                }}
                                description="Detalla la información que deseas registrar"
                                errorMessage={errors.content?.message}
                                isInvalid={!!errors.content}
                                label="Contenido"
                                maxRows={8}
                                minRows={4}
                                placeholder="Escribe el contenido de la nota..."
                                size="lg"
                                variant="bordered"
                            />

                            {/* Mensaje informativo */}
                            <NoteInfoMessage
                                message="Las notas son visibles para todos los usuarios y ayudan a mantener un seguimiento detallado de las órdenes."
                                title="Consejo"
                                type="info"
                            />
                        </form>
                    </CardBody>

                    <CardFooter className="border-t border-gray-200 dark:border-gray-700 px-6 py-4">
                        <div className="flex justify-end gap-3 w-full">
                            <Button variant="flat" onPress={handleCancel}>
                                Cancelar
                            </Button>
                            <Button
                                color="primary"
                                isDisabled={isSubmitting}
                                isLoading={isSubmitting}
                                startContent={
                                    !isSubmitting && (
                                        <CheckIcon className="w-4 h-4" />
                                    )
                                }
                                type="submit"
                                onPress={() => handleSubmit(onSubmit)()}
                            >
                                {isSubmitting ? "Creando..." : "Crear Nota"}
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </motion.div>

            {/* Modal de confirmación */}
            <Modal
                backdrop="blur"
                classNames={{
                    base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                    header: "border-b border-gray-200 dark:border-gray-700",
                    body: "py-6",
                    footer: "border-t border-gray-200 dark:border-gray-700",
                }}
                isOpen={isOpen}
                placement="center"
                onClose={onClose}
            >
                <ModalContent>
                    <ModalHeader>Cambios sin guardar</ModalHeader>
                    <ModalBody>
                        ¿Estás seguro de que deseas salir? Los cambios se
                        perderán.
                    </ModalBody>
                    <ModalFooter>
                        <Button variant="flat" onPress={onClose}>
                            Continuar editando
                        </Button>
                        <Button
                            color="danger"
                            onPress={() => {
                                onClose();
                                router.push("/dashboard/notes");
                            }}
                        >
                            Salir sin guardar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </div>
    );
}
