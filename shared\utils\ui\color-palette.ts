// Dynamic color palette system for modern UI
import Color from "color";

// Base colors
export const baseColors = {
    primary: "#3B82F6", // Blue
    secondary: "#8B5CF6", // Purple
    success: "#10B981", // Green
    warning: "#F59E0B", // Amber
    danger: "#EF4444", // Red
    info: "#06B6D4", // <PERSON>an
} as const;

// Generate color shades
export function generateColorShades(baseColor: string) {
    const color = Color(baseColor);

    return {
        50: color.lightness(95).hex(),
        100: color.lightness(90).hex(),
        200: color.lightness(80).hex(),
        300: color.lightness(70).hex(),
        400: color.lightness(60).hex(),
        500: baseColor,
        600: color.lightness(40).hex(),
        700: color.lightness(30).hex(),
        800: color.lightness(20).hex(),
        900: color.lightness(10).hex(),
        950: color.lightness(5).hex(),
    };
}

// Generate complementary colors
export function getComplementaryColor(color: string): string {
    return Color(color).rotate(180).hex();
}

// Generate triadic colors
export function getTriadicColors(color: string): [string, string] {
    const baseColor = Color(color);

    return [baseColor.rotate(120).hex(), baseColor.rotate(240).hex()];
}

// Generate analogous colors
export function getAnalogousColors(color: string): [string, string] {
    const baseColor = Color(color);

    return [baseColor.rotate(30).hex(), baseColor.rotate(-30).hex()];
}

// Dynamic gradient generator
export function createGradient(
    color1: string,
    color2: string,
    angle: number = 45,
): string {
    return `linear-gradient(${angle}deg, ${color1}, ${color2})`;
}

// Vibrant gradient presets
export const gradients = {
    sunrise: createGradient("#FF6B6B", "#FFE66D", 45),
    sunset: createGradient("#FF6B6B", "#4ECDC4", 135),
    ocean: createGradient("#1A535C", "#4ECDC4", 90),
    forest: createGradient("#2D6A4F", "#95D5B2", 120),
    galaxy: createGradient("#5E60CE", "#C77DFF", 180),
    fire: createGradient("#FF006E", "#FFBE0B", 45),
    ice: createGradient("#4CC9F0", "#7209B7", 135),
    aurora: createGradient("#00F5FF", "#FF0080", 90),
} as const;

// Neon colors for dark mode
export const neonColors = {
    cyan: "#00F5FF",
    pink: "#FF0080",
    yellow: "#FFFC00",
    green: "#00FF88",
    purple: "#9D4EDD",
    orange: "#FF6700",
} as const;

// Glass overlay colors
export function getGlassOverlay(
    isDark: boolean,
    opacity: number = 0.7,
): string {
    return isDark
        ? `rgba(17, 24, 39, ${opacity})` // gray-900
        : `rgba(255, 255, 255, ${opacity})`;
}

// Dynamic shadow colors
export function getDynamicShadow(color: string, isDark: boolean): string {
    const baseColor = Color(color);
    const shadowColor = isDark
        ? baseColor.darken(0.5).alpha(0.3)
        : baseColor.lighten(0.3).alpha(0.2);

    return `0 10px 40px ${shadowColor.string()}`;
}

// Contrast checker
export function hasGoodContrast(
    foreground: string,
    background: string,
    threshold: number = 4.5,
): boolean {
    return Color(foreground).contrast(Color(background)) >= threshold;
}

// Auto text color based on background
export function getContrastTextColor(background: string): string {
    const luminance = Color(background).luminosity();

    return luminance > 0.5 ? "#000000" : "#FFFFFF";
}

// Export all color utilities
export const colorUtils = {
    generateColorShades,
    getComplementaryColor,
    getTriadicColors,
    getAnalogousColors,
    createGradient,
    getGlassOverlay,
    getDynamicShadow,
    hasGoodContrast,
    getContrastTextColor,
};
