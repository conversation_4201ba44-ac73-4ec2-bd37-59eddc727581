"use client";

import React, { useState } from "react";
import {
    Button,
    Input,
    Card,
    CardBody,
    CardHeader,
    Divider,
    Checkbox,
    Link as HeroLink,
    addToast,
} from "@heroui/react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { EnvelopeIcon, LockClosedIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

export default function LoginClientPage() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";
    const [isLoading, setIsLoading] = useState(false);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [isEmailValid, setIsEmailValid] = useState(true);

    const validateEmail = (email: string) => {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        return regex.test(email);
    };

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        setEmail(value);
        if (value) setIsEmailValid(validateEmail(value));
        else setIsEmailValid(true);
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!validateEmail(email)) {
            setIsEmailValid(false);

            return;
        }

        setIsLoading(true);

        try {
            const response = await signIn("credentials", {
                email,
                password,
                redirect: false,
            });

            if (!response?.error) {
                addToast({
                    title: "¡Éxito!",
                    description: "Inicio de sesión exitoso",
                    color: "success",
                });
                router.push(callbackUrl);
                router.refresh();
            } else {
                addToast({
                    title: "Error",
                    description: "Credenciales inválidas",
                    color: "danger",
                });
            }
        } catch {
            addToast({
                title: "Error",
                description: "Ocurrió un error al iniciar sesión",
                color: "danger",
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-[85vh] flex items-center justify-center p-6 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-slate-900 dark:to-slate-950 rounded-xl">
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="w-full max-w-md"
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5 }}
            >
                <Card className="shadow-xl border-none overflow-hidden dark:bg-slate-800/50 dark:backdrop-blur-sm">
                    <CardHeader className="flex flex-col items-center justify-center pb-0 pt-6 gap-2">
                        <div className="w-16 h-16 mb-2 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
                            <LockClosedIcon className="w-8 h-8 text-primary dark:text-primary-400" />
                        </div>
                        <h1 className="text-2xl font-bold text-foreground dark:text-slate-100">
                            ¡Bienvenido de nuevo!
                        </h1>
                        <p className="text-small text-muted-foreground dark:text-slate-400 text-center">
                            Ingresa tus credenciales para acceder a tu cuenta
                        </p>
                    </CardHeader>
                    <CardBody className="py-6 px-8">
                        <form
                            className="flex flex-col gap-4"
                            onSubmit={handleSubmit}
                        >
                            <Input
                                fullWidth
                                isRequired
                                autoComplete="email"
                                classNames={{
                                    inputWrapper:
                                        "shadow-sm dark:bg-slate-900/50 dark:border-slate-700",
                                }}
                                errorMessage={
                                    !isEmailValid &&
                                    "Ingresa un correo electrónico válido"
                                }
                                isInvalid={!isEmailValid}
                                label="Correo electrónico"
                                placeholder="<EMAIL>"
                                startContent={
                                    <EnvelopeIcon className="text-default-400 dark:text-slate-500 w-5 h-5" />
                                }
                                type="email"
                                value={email}
                                variant="bordered"
                                onChange={handleEmailChange}
                            />

                            <Input
                                fullWidth
                                isRequired
                                autoComplete="current-password"
                                classNames={{
                                    inputWrapper:
                                        "shadow-sm dark:bg-slate-900/50 dark:border-slate-700",
                                }}
                                label="Contraseña"
                                placeholder="••••••••"
                                startContent={
                                    <LockClosedIcon className="text-default-400 dark:text-slate-500 w-5 h-5" />
                                }
                                type="password"
                                value={password}
                                variant="bordered"
                                onChange={(e) => setPassword(e.target.value)}
                            />

                            <div className="flex items-center justify-between mt-1">
                                <Checkbox
                                    className="dark:text-slate-400"
                                    size="sm"
                                >
                                    <span className="text-sm text-default-500 dark:text-slate-400">
                                        Recordarme
                                    </span>
                                </Checkbox>
                                <HeroLink
                                    className="text-primary dark:text-blue-400 cursor-pointer"
                                    href="/forgot-password"
                                    size="sm"
                                >
                                    ¿Olvidaste tu contraseña?
                                </HeroLink>
                            </div>

                            <Button
                                fullWidth
                                className="mt-2 font-medium dark:bg-blue-600 dark:hover:bg-blue-700"
                                color="primary"
                                isLoading={isLoading}
                                radius="sm"
                                size="lg"
                                startContent={
                                    !isLoading && (
                                        <EnvelopeIcon className="w-5 h-5" />
                                    )
                                }
                                type="submit"
                            >
                                {isLoading
                                    ? "Iniciando sesión..."
                                    : "Iniciar sesión"}
                            </Button>

                            <div className="relative my-2">
                                <Divider className="my-4 dark:bg-slate-700" />
                                <p className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-background dark:bg-slate-800 px-2 text-tiny text-default-500 dark:text-slate-400">
                                    O continúa con
                                </p>
                            </div>

                            <Button
                                fullWidth
                                className="font-medium dark:border-slate-700 dark:text-slate-300"
                                radius="sm"
                                size="lg"
                                startContent={
                                    <svg
                                        className="w-5 h-5 text-red-500"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                                    </svg>
                                }
                                variant="bordered"
                                onPress={() =>
                                    signIn("google", { callbackUrl })
                                }
                            >
                                Google
                            </Button>

                            <p className="text-center text-small text-default-500 dark:text-slate-400 mt-3">
                                ¿No tienes una cuenta?{" "}
                                <HeroLink
                                    className="text-primary dark:text-blue-400 cursor-pointer font-medium"
                                    href="/register"
                                    size="sm"
                                >
                                    Regístrate aquí
                                </HeroLink>
                            </p>
                        </form>
                    </CardBody>
                </Card>
            </motion.div>
        </div>
    );
}
