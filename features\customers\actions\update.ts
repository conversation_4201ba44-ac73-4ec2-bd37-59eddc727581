"use server";

import { z } from "zod";
import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

import { customerSchema } from "./schema";
import { validateCustomerName } from "./validate";

// Obtener helpers de revalidación para la entidad "customer"
const { revalidateCache } = createServerRevalidation("customer");

/**
 * Actualiza un cliente existente
 * @param id ID del cliente a actualizar
 * @param data Datos actualizados del cliente
 * @param forceUpdate Si es true, ignorará la validación de nombre único (para casos especiales)
 */
export async function updateCustomer(
    id: string,
    data: { name: string; displayName?: string | null },
    forceUpdate = false,
) {
    console.log(
        `[updateCustomer] Iniciando actualización de cliente: ID=${id}, nombre=${data.name}, forceUpdate=${forceUpdate}`,
    );

    if (!id) {
        console.log(`[updateCustomer] ID no válido: ${id}`);

        return { success: false, error: "ID no válido" };
    }

    try {
        // Validar datos con parse en lugar de safeParse para generar errores
        const validatedData = customerSchema.parse(data);

        console.log(
            `[updateCustomer] Datos validados correctamente:`,
            validatedData,
        );

        // Verificar si el nombre ya existe (excluyendo el cliente actual), solo si no se está forzando
        if (!forceUpdate) {
            const nameValidation = await validateCustomerName(
                validatedData.name,
                id,
            );

            console.log(
                `[updateCustomer] Validación de nombre (excluyendo ID ${id}):`,
                nameValidation,
            );

            if (
                !nameValidation ||
                !nameValidation.data ||
                !(nameValidation.data as any)?.isValid
            ) {
                console.log(
                    `[updateCustomer] Nombre no válido, retornando error`,
                );

                return {
                    success: false,
                    error: "El nombre del cliente ya existe en el sistema",
                };
            }
        } else {
            console.log(
                `[updateCustomer] Saltando validación de nombre (forceUpdate=true)`,
            );
        }

        // Si estamos forzando la actualización, intentar limpiar posibles conflictos
        if (forceUpdate) {
            try {
                // Eliminar cualquier otro cliente con el mismo nombre excepto el actual
                await db.customer.deleteMany({
                    where: {
                        name: {
                            equals: validatedData.name,
                            mode: Prisma.QueryMode.insensitive,
                        },
                        id: {
                            not: id,
                        },
                    },
                });
            } catch (_unused) {
                // Ignora errores de esta operación, es solo por seguridad
                // // REMOVED: console.log(
                //     "No se encontraron clientes conflictivos para eliminar",
                // );
            }
        }

        // Actualizar el cliente en la base de datos
        console.log(
            `[updateCustomer] Actualizando cliente en la base de datos...`,
        );
        const customer = await db.customer.update({
            where: { id },
            data: {
                name: validatedData.name,
                displayName: validatedData.displayName || null,
            },
        });

        console.log(
            `[updateCustomer] Cliente actualizado exitosamente:`,
            customer,
        );

        // Revalidar caché
        revalidateCache(id);
        console.log(`[updateCustomer] Caché revalidado para ID: ${id}`);

        return { success: true, data: customer };
    } catch (error) {
        console.error(`[updateCustomer] Error capturado:`, error);

        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            console.log(
                `[updateCustomer] Error de validación Zod:`,
                error.errors,
            );

            return {
                success: false,
                error: error.errors[0]?.message || "Datos de cliente inválidos",
            };
        }

        // Usar el helper para manejar errores de DB
        console.log(`[updateCustomer] Manejando error con handleDbError...`);
        const result = handleDbError(() => {
            throw error;
        }, "Error al actualizar cliente");

        console.log(`[updateCustomer] Resultado de handleDbError:`, result);

        return result;
    }
}
