"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    InformationCircleIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

type MessageType = "info" | "success" | "warning";

interface ContractorInfoMessageProps {
    type?: MessageType;
    title: string;
    description?: string;
    isVisible?: boolean;
    className?: string;
}

const iconMap = {
    info: InformationCircleIcon,
    success: CheckCircleIcon,
    warning: ExclamationTriangleIcon,
};

const colorMap = {
    info: {
        bg: "bg-blue-50 dark:bg-blue-950/30",
        border: "border-blue-200 dark:border-blue-800",
        icon: "text-blue-600 dark:text-blue-400",
        title: "text-blue-900 dark:text-blue-100",
        description: "text-blue-700 dark:text-blue-300",
    },
    success: {
        bg: "bg-green-50 dark:bg-green-950/30",
        border: "border-green-200 dark:border-green-800",
        icon: "text-green-600 dark:text-green-400",
        title: "text-green-900 dark:text-green-100",
        description: "text-green-700 dark:text-green-300",
    },
    warning: {
        bg: "bg-amber-50 dark:bg-amber-950/30",
        border: "border-amber-200 dark:border-amber-800",
        icon: "text-amber-600 dark:text-amber-400",
        title: "text-amber-900 dark:text-amber-100",
        description: "text-amber-700 dark:text-amber-300",
    },
};

export function ContractorInfoMessage({
    type = "info",
    title,
    description,
    isVisible = true,
    className = "",
}: ContractorInfoMessageProps) {
    const Icon = iconMap[type];
    const colors = colorMap[type];

    return (
        <AnimatePresence>
            {isVisible && (
                <motion.div
                    animate={{ opacity: 1, y: 0, height: "auto" }}
                    className={`${colors.bg} ${colors.border} border rounded-lg p-4 ${className}`}
                    exit={{ opacity: 0, y: -10, height: 0 }}
                    initial={{ opacity: 0, y: -10, height: 0 }}
                    transition={{ duration: 0.3 }}
                >
                    <div className="flex gap-3">
                        <motion.div
                            animate={{ scale: 1, rotate: 0 }}
                            initial={{ scale: 0.8, rotate: -45 }}
                            transition={{ type: "spring", stiffness: 200 }}
                        >
                            <Icon
                                className={`w-5 h-5 ${colors.icon} flex-shrink-0 mt-0.5`}
                            />
                        </motion.div>
                        <div className="flex-1 space-y-1">
                            <motion.h4
                                animate={{ opacity: 1, x: 0 }}
                                className={`font-semibold text-sm ${colors.title}`}
                                initial={{ opacity: 0, x: -10 }}
                                transition={{ delay: 0.1 }}
                            >
                                {title}
                            </motion.h4>
                            {description && (
                                <motion.p
                                    animate={{ opacity: 1, x: 0 }}
                                    className={`text-sm ${colors.description}`}
                                    initial={{ opacity: 0, x: -10 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    {description}
                                </motion.p>
                            )}
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}

// Mensajes predefinidos para el formulario de contratistas
export const contractorFormMessages = {
    emailOptional: {
        type: "info" as MessageType,
        title: "Email opcional",
        description:
            "El email es opcional pero recomendado para facilitar la comunicación.",
    },
    duplicateName: {
        type: "warning" as MessageType,
        title: "Nombre duplicado",
        description:
            "Ya existe un contratista con este nombre. Verifica que no sea un duplicado.",
    },
    formComplete: {
        type: "success" as MessageType,
        title: "¡Formulario completo!",
        description:
            "Todos los campos obligatorios están completos. Puedes crear el contratista.",
    },
    phoneFormat: {
        type: "info" as MessageType,
        title: "Formato de teléfono",
        description:
            "Puedes incluir código de país, por ejemplo: +52 ************",
    },
};
