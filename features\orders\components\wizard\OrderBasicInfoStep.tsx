"use client";

import React from "react";
import {
    CalendarIcon,
    TagIcon,
    CheckCircleIcon,
    XCircleIcon,
} from "@heroicons/react/24/outline";

import {
    Input,
    Select,
    SelectItem,
} from "@/shared/components/ui/hero-ui-client";
import { DatePicker } from "@/shared/components/ui/DatePicker";
import { CustomerSelector } from "@/features/customers/components/hierarchy/CustomerSelector";

interface OrderBasicInfoStepProps {
    formData: {
        customerId: string;
        subCustomerId?: string;
        statusId: string;
        transferNumber: string;
        cutOrder: string;
        batch: string;
    };
    receivedDate: string;
    estimatedDeliveryDate: string;
    customers: Array<{ id: string; name: string }>;
    orderStatuses: Array<{
        id: string;
        name: string;
        color?: string;
        iconName?: string;
    }>;
    errors: Record<string, string>;
    onChange: (field: string, value: any) => void;
    onDateChange: (
        field: "receivedDate" | "estimatedDeliveryDate",
        value: string,
    ) => void;
}

// Status icon mapping
const getStatusIcon = (iconName?: string) => {
    const icons: Record<string, React.ReactNode> = {
        CheckCircleIcon: <CheckCircleIcon className="w-4 h-4" />,
        ClockIcon: <CalendarIcon className="w-4 h-4" />,
        XCircleIcon: <XCircleIcon className="w-4 h-4" />,
    };

    return iconName ? icons[iconName] || null : null;
};

export function OrderBasicInfoStep({
    formData,
    receivedDate,
    estimatedDeliveryDate,
    customers,
    orderStatuses,
    errors,
    onChange,
    onDateChange,
}: OrderBasicInfoStepProps) {
    return (
        <div className="space-y-6">
            {/* Dates Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <DatePicker
                    label="Fecha de recepción"
                    value={receivedDate}
                    onChange={(date) => onDateChange("receivedDate", date)}
                />

                <DatePicker
                    isRequired
                    errorMessage={errors.estimatedDeliveryDate}
                    label="Fecha estimada de entrega"
                    value={estimatedDeliveryDate}
                    onChange={(date) =>
                        onDateChange("estimatedDeliveryDate", date)
                    }
                />
            </div>

            {/* Main Info Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="lg:col-span-2">
                    <CustomerSelector
                        isRequired
                        label="Cliente / Subcliente"
                        placeholder="Seleccione el destinatario"
                        value={{
                            customerId: formData.customerId,
                            subCustomerId: formData.subCustomerId,
                        }}
                        onChange={({ customerId, subCustomerId }) => {
                            onChange("customerId", customerId);
                            onChange("subCustomerId", subCustomerId);
                        }}
                    />
                </div>

                <div className="lg:col-span-1">
                    <Select
                        errorMessage={errors.statusId}
                        label={
                            <>
                                Estado del Pedido{" "}
                                <span className="text-red-500">*</span>
                            </>
                        }
                        placeholder="Selecciona un estado"
                        renderValue={(items: any) => {
                            const selectedStatus = orderStatuses.find(
                                (s) => s.id === formData.statusId,
                            );

                            if (!selectedStatus) return null;

                            return (
                                <div className="flex items-center gap-2">
                                    {selectedStatus.iconName && (
                                        <span
                                            className="flex items-center justify-center w-6 h-6 rounded-full"
                                            style={{
                                                backgroundColor:
                                                    selectedStatus.color ||
                                                    "#CBD5E1",
                                                color: "#FFFFFF",
                                            }}
                                        >
                                            {getStatusIcon(
                                                selectedStatus.iconName,
                                            )}
                                        </span>
                                    )}
                                    <span>{selectedStatus.name}</span>
                                </div>
                            );
                        }}
                        selectedKeys={
                            formData.statusId ? [formData.statusId] : []
                        }
                        startContent={
                            <TagIcon className="w-4 h-4 text-gray-400" />
                        }
                        variant="bordered"
                        onChange={(e) => onChange("statusId", e.target.value)}
                    >
                        {orderStatuses.map((status) => (
                            <SelectItem key={status.id} textValue={status.name}>
                                <div className="flex items-center gap-2">
                                    {status.iconName && (
                                        <span
                                            className="flex items-center justify-center w-7 h-7 rounded-full"
                                            style={{
                                                backgroundColor:
                                                    status.color || "#CBD5E1",
                                                color: "#FFFFFF",
                                            }}
                                        >
                                            {getStatusIcon(status.iconName)}
                                        </span>
                                    )}
                                    <span>{status.name}</span>
                                </div>
                            </SelectItem>
                        ))}
                    </Select>
                </div>
            </div>

            {/* Additional Info Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                    label="Número de Transferencia"
                    placeholder="Opcional"
                    startContent={<TagIcon className="w-4 h-4 text-gray-400" />}
                    value={formData.transferNumber}
                    variant="bordered"
                    onChange={(e) => onChange("transferNumber", e.target.value)}
                />

                <Input
                    label="Orden de Corte"
                    placeholder="Opcional"
                    startContent={<TagIcon className="w-4 h-4 text-gray-400" />}
                    value={formData.cutOrder}
                    variant="bordered"
                    onChange={(e) => onChange("cutOrder", e.target.value)}
                />

                <Input
                    label="Lote"
                    placeholder="Opcional"
                    startContent={<TagIcon className="w-4 h-4 text-gray-400" />}
                    value={formData.batch}
                    variant="bordered"
                    onChange={(e) => onChange("batch", e.target.value)}
                />
            </div>
        </div>
    );
}
