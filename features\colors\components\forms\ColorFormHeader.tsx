import React from "react";
import { SwatchIcon } from "@heroicons/react/24/outline";

interface ColorFormHeaderProps {
    title: string;
    subtitle?: string;
    mode?: "create" | "edit";
}

export function ColorFormHeader({
    title,
    subtitle,
    mode = "create",
}: ColorFormHeaderProps) {
    return (
        <div className="flex items-center gap-4">
            <div
                className={`
                p-3 rounded-xl
                ${
                    mode === "create"
                        ? "bg-gradient-to-br from-primary-500/20 to-secondary-500/20"
                        : "bg-gradient-to-br from-blue-500/20 to-indigo-500/20"
                }
            `}
            >
                <SwatchIcon
                    className={`
                    w-6 h-6
                    ${
                        mode === "create"
                            ? "text-primary-600 dark:text-primary-400"
                            : "text-blue-600 dark:text-blue-400"
                    }
                `}
                />
            </div>
            <div>
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                    {title}
                </h2>
                {subtitle && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-0.5">
                        {subtitle}
                    </p>
                )}
            </div>
        </div>
    );
}
