/* PackingPreview Print-Optimized Styles */
.packingPreview {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 2rem auto;
  padding: 2rem;
  max-width: 1200px;
  position: relative;
  transition: transform 0.2s ease;
  color: black;
  font-family: 'Arial', 'Helvetica', sans-serif;
  line-height: 1.5;
}

/* A4 Dimensions - Portrait */
.packingPreviewPortrait {
  width: 210mm;
  min-height: 297mm;
  padding: 20mm 15mm;
}

/* A4 Dimensions - Landscape */
.packingPreviewLandscape {
  width: 297mm;
  min-height: 210mm;
  padding: 15mm 20mm;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #333;
}

.companyInfo h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #1a1a1a;
  margin: 0;
  letter-spacing: 0.5px;
}

.companyInfo p {
  font-size: 0.875rem;
  color: #666;
  margin: 0.25rem 0 0 0;
}

.documentInfo {
  text-align: right;
}

.documentInfo h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.documentInfo p {
  font-size: 1rem;
  color: #333;
  margin: 0.25rem 0;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.875rem;
}

.table th {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #333;
}

.table td {
  border: 1px solid #ddd;
  padding: 0.75rem;
  color: #333;
}

.table tbody tr:nth-child(even) {
  background-color: #fafafa;
}

/* Summary Section */
.summary {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.summarySection {
  margin-bottom: 1.5rem;
}

.summarySection:last-child {
  margin-bottom: 0;
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.summaryItem:last-child {
  border-bottom: none;
}

.summaryLabel {
  font-weight: 600;
  color: #555;
}

.summaryValue {
  color: #333;
}

.summaryNotes {
  color: #333;
  line-height: 1.5;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  min-height: 3rem;
}

/* Transport Notes */
.transportNotes {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.notesText {
  color: #333;
  line-height: 1.5;
  padding: 0.5rem;
  background-color: white;
  border-radius: 4px;
  min-height: 2rem;
}

/* Signatures Section */
.signatures {
  margin-top: 3rem;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.signatureBox {
  text-align: center;
  padding-top: 1rem;
}

.signatureContainer {
  min-height: 4rem;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.signatureSpace {
  height: 3rem;
}

.signatureInfo {
  text-align: center;
}

.signedName {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.signedDate {
  font-size: 0.75rem;
  color: #666;
  margin: 0.25rem 0 0 0;
}

.signatureLine {
  border-top: 2px solid #333;
  margin: 0.5rem auto;
  width: 80%;
}

.signatureLabel {
  font-size: 0.875rem;
  color: #333;
  font-weight: 600;
  margin: 0.5rem 0 0.25rem 0;
}

.signatureSubLabel {
  font-size: 0.75rem;
  color: #666;
  margin: 0;
}

/* Footer */
.footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #ddd;
  text-align: center;
  font-size: 0.75rem;
  color: #666;
}

/* Editable Mode */
.packingPreviewEditable .editableField {
  position: relative;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  cursor: text;
}

.packingPreviewEditable .editableField:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.packingPreviewEditable .editableField:focus-within {
  background-color: rgba(59, 130, 246, 0.2);
  outline: 2px solid rgb(59, 130, 246);
}

/* Print Styles */
@media print {
  @page {
    size: A4 landscape;
    margin: 10mm;
  }

  .packingPreview {
    margin: 0;
    box-shadow: none;
    border-radius: 0;
    page-break-after: always;
    page-break-inside: avoid;
    width: 100% !important;
    max-width: none !important;
    transform: none !important;
  }

  .packingPreviewPortrait {
    width: 190mm;
    min-height: 277mm;
    padding: 10mm;
  }

  .packingPreviewLandscape {
    width: 277mm;
    min-height: 190mm;
    padding: 10mm;
  }

  /* Table optimizations for print */
  .packingTable {
    font-size: 0.7rem;
    page-break-inside: avoid;
  }

  .packingTable th,
  .packingTable td {
    padding: 0.5rem 0.25rem;
  }

  /* Ensure headers repeat on page breaks */
  .packingTable thead {
    display: table-header-group;
  }

  .packingTable tfoot {
    display: table-footer-group;
  }

  .packingTable tr {
    page-break-inside: avoid;
  }

  /* Summary sections */
  .summary,
  .summarySection {
    page-break-inside: avoid;
  }

  /* Signatures at bottom */
  .signatures {
    position: fixed;
    bottom: 10mm;
    left: 10mm;
    right: 10mm;
    page-break-inside: avoid;
  }

  /* Hide screen-only elements */
  .noprint,
  .screenOnly,
  .heroUITable,
  .tableCard {
    display: none !important;
  }

  /* Force colors */
  .packingTable th {
    background-color: #f5f5f5 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .packingTable tbody tr:nth-child(even) {
    background-color: #fafafa !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Ensure text is readable */
  .packingPreview * {
    color: black !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .header h1,
  .header h2,
  .sectionTitle {
    color: black !important;
  }

  /* Page numbers */
  .footer {
    text-align: center;
    font-size: 0.75rem;
    color: #666;
  }
}

/* Mobile Responsive (< 640px) */
@media (max-width: 639px) {
  .packingPreview {
    width: 100%;
    min-height: auto;
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  .header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .companyInfo h1 {
    font-size: 1.5rem;
  }

  .documentInfo {
    text-align: center;
    margin-top: 1rem;
  }

  .documentInfo h2 {
    font-size: 1.25rem;
  }

  /* Hide table on mobile, show simplified view */
  .tableWrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .packingTable {
    font-size: 0.7rem;
    min-width: 600px;
  }

  .packingTable th,
  .packingTable td {
    padding: 0.375rem 0.25rem;
  }

  .signatures {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .summarySection {
    margin-bottom: 1rem;
  }

  .sectionTitle {
    font-size: 0.875rem;
  }
}

/* Tablet Responsive (640px - 1024px) */
@media (min-width: 640px) and (max-width: 1023px) {
  .packingPreview {
    width: 100%;
    max-width: 800px;
    padding: 1.5rem;
  }

  .packingPreviewPortrait,
  .packingPreviewLandscape {
    width: 100%;
    min-height: auto;
  }

  .header {
    flex-direction: row;
    gap: 1rem;
  }

  .packingTable {
    font-size: 0.8rem;
  }

  .summaryGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .signatures {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Desktop (1024px - 1280px) */
@media (min-width: 1024px) and (max-width: 1279px) {
  .packingPreview {
    max-width: 1000px;
  }

  .packingTable {
    font-size: 0.85rem;
  }
}

/* Large Desktop (> 1280px) */
@media (min-width: 1280px) {
  .packingPreview {
    max-width: 1200px;
  }

  /* Ensure proper scaling on large screens */
  .packingPreviewLandscape {
    transform-origin: top center;
  }
}

/* Special handling for very small screens */
@media (max-width: 480px) {
  .packingPreview {
    padding: 0.5rem;
  }

  .companyInfo h1 {
    font-size: 1.25rem;
  }

  .documentInfo h2 {
    font-size: 1rem;
  }

  .packingTable {
    font-size: 0.625rem;
  }

  .summaryItem {
    font-size: 0.75rem;
    padding: 0.375rem 0;
  }

  .signatureLabel {
    font-size: 0.75rem;
  }
}

/* Company Details */
.companyDetails {
  font-size: 0.875rem;
  color: #666;
  margin: 0.125rem 0;
}

.companyContact {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.5rem;
}

.folioLine {
  font-size: 1.125rem;
  color: #333;
  margin: 0.5rem 0;
}

.dateLine {
  font-size: 0.875rem;
  color: #555;
  margin: 0.25rem 0;
}

/* Table Container */
.tableContainer {
  margin: 1.5rem 0;
  overflow-x: auto;
}

/* Table Card */
.tableCard {
  margin: 1.5rem 0;
  background: white;
  border: 1px solid #ddd;
}

.tableCardBody {
  padding: 1rem;
}

/* Table Wrapper */
.tableWrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Packing Table */
.packingTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.packingTable th,
.packingTable td {
  border: 1px solid #ddd;
  padding: 0.75rem 0.5rem;
  text-align: left;
}

.packingTable th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

.packingTable tbody tr:nth-child(even) {
  background-color: #fafafa;
}

.packingTable tbody tr:hover {
  background-color: #f0f0f0;
}

/* Column Types */
.sizeColumn {
  text-align: center !important;
  min-width: 3rem;
}

.numberColumn {
  text-align: center !important;
  min-width: 4rem;
}

.totalColumn {
  text-align: center !important;
  font-weight: 600;
  min-width: 5rem;
}

/* Total Row */
.totalRow {
  background-color: #f0f0f0 !important;
  font-weight: 600;
}

.totalRow td {
  border-top: 2px solid #333;
}

/* Screen Only (Hidden in print) */
.screenOnly {
  display: block;
}

@media print {
  .screenOnly {
    display: none !important;
  }
}

/* HeroUI Table Customization */
.heroUITable {
  margin-top: 2rem;
}

.tableHeader {
  background-color: #f5f5f5;
  font-weight: 600;
}

.tableCell {
  padding: 0.75rem 0.5rem;
}

/* Utility Classes */
.textCenter {
  text-align: center;
}

.textRight {
  text-align: right;
}

.fontBold {
  font-weight: bold;
}

.mt1 {
  margin-top: 1rem;
}

.mt2 {
  margin-top: 2rem;
}

.mb1 {
  margin-bottom: 1rem;
}

.mb2 {
  margin-bottom: 2rem;
}