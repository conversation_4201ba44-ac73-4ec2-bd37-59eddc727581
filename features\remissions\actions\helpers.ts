"use server";

import type { RemissionItemData, OrderDetails } from "../types";

import { Prisma } from "@prisma/client";

import { validateRemissionData } from "../schemas/validation";

/**
 * Obtiene los detalles de la orden optimizadamente
 */
export async function getOrderDetailsForRemission(
    orderId: string,
    tx: Prisma.TransactionClient,
): Promise<OrderDetails> {
    const order = await tx.order.findUnique({
        where: { id: orderId },
        select: {
            id: true,
            cutOrder: true,
            createdAt: true,
            parts: {
                select: {
                    id: true,
                    code: true,
                },
            },
        },
    });

    if (!order) {
        throw new Error(`Order ${orderId} not found`);
    }

    return {
        id: order.id,
        cutOrder: order.cutOrder || undefined,
        creationDate: order.createdAt,
        parts: order.parts,
    };
}

/**
 * Crea una remisión dentro de una transacción existente
 */
export async function createRemissionInTransaction(
    tx: Prisma.TransactionClient,
    data: {
        assignmentIds: string[];
        contractorId: string;
        orderId: string;
        items: RemissionItemData[];
        notes?: string;
    },
) {
    // Validate input data
    const validation = validateRemissionData(data);

    if (!validation.success) {
        throw new Error(`Invalid remission data: ${validation.error.message}`);
    }

    const { generateFolio } = await import("./index");

    // Get order details
    const orderDetails = await getOrderDetailsForRemission(data.orderId, tx);

    // Generate folio (this has its own transaction, but it's isolated)
    const folio = await generateFolio();

    // Create the remission
    const remission = await tx.remission.create({
        data: {
            folio,
            contractorId: data.contractorId,
            notes: data.notes,
            orderDetails: orderDetails as unknown as Prisma.InputJsonValue,
            status: "ACTIVE",
        },
        include: {
            remissionItems: true,
        },
    });

    // Create RemissionAssignment records for each assignment
    if (data.assignmentIds && data.assignmentIds.length > 0) {
        // Verify all assignments exist before creating relationships
        const validAssignments = await tx.assignment.findMany({
            where: {
                id: { in: data.assignmentIds },
            },
            select: { id: true },
        });

        if (validAssignments.length !== data.assignmentIds.length) {
            const validIds = validAssignments.map((a) => a.id);
            const invalidIds = data.assignmentIds.filter(
                (id) => !validIds.includes(id),
            );

            throw new Error(
                `Invalid assignment IDs found: ${invalidIds.join(", ")}`,
            );
        }

        await tx.remissionAssignment.createMany({
            data: data.assignmentIds.map((assignmentId) => ({
                remissionId: remission.id,
                assignmentId,
            })),
        });
    }

    // Create remission items
    if (data.items.length > 0) {
        await tx.remissionItem.createMany({
            data: data.items.map((item) => ({
                remissionId: remission.id,
                modelCode: item.modelCode,
                colorName: item.colorName,
                sizeCode: item.sizeCode,
                quantity: item.quantity,
            })),
        });
    }

    // Create history entry
    await tx.remissionHistory.create({
        data: {
            remissionId: remission.id,
            action: "CREATED",
            metadata: {
                createdAt: new Date().toISOString(),
                source: "assignment_creation",
            },
        },
    });

    return remission;
}
