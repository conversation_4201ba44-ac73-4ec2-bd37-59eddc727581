// Animation utilities for framer-motion
import { Variants } from "framer-motion";

// Card animations
export const cardVariants: Variants = {
    hidden: {
        opacity: 0,
        y: 20,
        scale: 0.95,
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            type: "spring",
            damping: 20,
            stiffness: 100,
        },
    },
    hover: {
        scale: 1.02,
        y: -5,
        transition: {
            type: "spring",
            damping: 15,
            stiffness: 400,
        },
    },
    tap: {
        scale: 0.98,
    },
};

// Staggered list animations
export const listVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            when: "beforeChildren",
            staggerChildren: 0.1,
        },
    },
};

export const itemVariants: Variants = {
    hidden: {
        opacity: 0,
        x: -20,
        filter: "blur(10px)",
    },
    visible: {
        opacity: 1,
        x: 0,
        filter: "blur(0px)",
        transition: {
            type: "spring",
            damping: 25,
            stiffness: 120,
        },
    },
};

// Badge pulse animation
export const pulseVariants: Variants = {
    initial: { scale: 1 },
    animate: {
        scale: [1, 1.1, 1],
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
        },
    },
};

// 3D rotation on hover
export const rotate3DVariants: Variants = {
    initial: {
        rotateX: 0,
        rotateY: 0,
    },
    hover: {
        rotateX: -15,
        rotateY: 15,
        transition: {
            type: "spring",
            damping: 20,
            stiffness: 300,
        },
    },
};

// Fade in/out variants
export const fadeVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: { duration: 0.3 },
    },
    exit: {
        opacity: 0,
        transition: { duration: 0.2 },
    },
};

// Skeleton loading animation
export const skeletonVariants: Variants = {
    initial: {
        backgroundPosition: "200% 0",
    },
    animate: {
        backgroundPosition: "-200% 0",
        transition: {
            duration: 2,
            ease: "linear",
            repeat: Infinity,
        },
    },
};

// Spring configurations
export const springConfig = {
    gentle: { type: "spring", damping: 30, stiffness: 200 },
    wobbly: { type: "spring", damping: 15, stiffness: 100 },
    stiff: { type: "spring", damping: 40, stiffness: 400 },
    slow: { type: "spring", damping: 50, stiffness: 50 },
};

// Gesture animations
export const tapAnimation = {
    whileTap: { scale: 0.95 },
    transition: springConfig.stiff,
};

export const hoverAnimation = {
    whileHover: { scale: 1.05 },
    transition: springConfig.gentle,
};

// Utility function for viewport-based animations
export const createViewportAnimation = (delay = 0) => ({
    initial: "hidden",
    whileInView: "visible",
    viewport: { once: true, amount: 0.3 },
    transition: { delay },
});
