"use client";

import type {
    Order,
    OrderStatus as OrderStatus<PERSON>ype,
    <PERSON><PERSON><PERSON>,
    GarmentSize,
} from "@/features/assignments/types/types";
import type { OrderPart } from "@/shared/types/common";

import React, { useState, useMemo } from "react";
import {
    Checkbox,
    Card,
    CardBody,
    Chip,
    Button,
    ScrollShadow,
    Progress,
} from "@heroui/react";
import {
    CalendarIcon,
    UserIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    CheckIcon,
    MagnifyingGlassIcon,
    XMarkIcon,
    TagIcon,
} from "@heroicons/react/24/outline";
import { Tooltip } from "@heroui/react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";

interface OrderWithExtras extends Order {
    code?: string;
    parts?: OrderPart[];
    deliveryDate: string;
}

interface ProcessedOrder extends OrderWithExtras {
    urgency: "overdue" | "urgent" | "soon" | "normal";
    totalQuantity: number;
    availableQuantity: number;
    availablePercentage: number;
    models: string;
    partsDisplay: {
        visible: OrderPart[];
        overflow: number;
    };
    formattedDate: string;
}

interface OrdersSelectorProps {
    orders: OrderWithExtras[];
    selectedIds: string[];
    onToggle: (orderId: string) => void;
    error?: string;
    onClearFilters?: () => void;
    onShowAllOrders?: () => void;
}

// Helper functions
const formatCutOrder = (order: OrderWithExtras) => {
    if (order.cutOrder) return `OT: ${order.cutOrder}`;

    return `Orden #${order.code}`;
};

const getModelCodes = (garments?: Garment[]) => {
    if (!garments || garments.length === 0) return "Sin modelo";
    const codes = garments.map((g) => g.model?.code).filter(Boolean);

    return codes.length > 0 ? codes.join(", ") : "Sin modelo";
};

const getAvailabilityColor = (percentage: number) => {
    if (percentage === 0) return "danger";
    if (percentage <= 30) return "warning";
    if (percentage <= 70) return "primary";

    return "success";
};

const formatPartsDisplay = (parts?: OrderPart[], limit = 4) => {
    if (!parts || parts.length === 0) return { visible: [], overflow: 0 };

    return {
        visible: parts.slice(0, limit),
        overflow: Math.max(0, parts.length - limit),
    };
};

const getStatusColor = (status?: OrderStatusType) => {
    const colorMap: Record<
        string,
        "success" | "warning" | "danger" | "primary" | "default" | "secondary"
    > = {
        green: "success",
        yellow: "warning",
        red: "danger",
        blue: "primary",
        gray: "default",
        purple: "secondary",
    };

    return status?.color ? colorMap[status.color] || "default" : "default";
};

export function OrdersSelector({
    orders,
    selectedIds,
    onToggle,
    error,
    onClearFilters,
    onShowAllOrders,
}: OrdersSelectorProps) {
    const [viewMode, setViewMode] = useState<"list" | "grid">("list");

    // Calcular estadísticas de selección
    const selectionStats = useMemo(() => {
        const selectedOrders = orders.filter((o) => selectedIds.includes(o.id));
        const totalPieces = selectedOrders.reduce((sum, order) => {
            return (
                sum +
                (order.garments?.reduce((gSum: number, g: Garment) => {
                    return (
                        gSum +
                        (g.sizes || []).reduce(
                            (sSum: number, s: GarmentSize) =>
                                sSum + s.totalQuantity,
                            0,
                        )
                    );
                }, 0) || 0)
            );
        }, 0);

        return {
            count: selectedIds.length,
            totalPieces,
            customers: new Set(
                selectedOrders.map((o) => o.customer?.id).filter(Boolean),
            ).size,
        };
    }, [orders, selectedIds]);

    // Función para seleccionar/deseleccionar todos
    const toggleAll = () => {
        if (
            orders.length ===
            selectedIds.filter((id) => orders.some((o) => o.id === id)).length
        ) {
            // Deseleccionar todos
            orders.forEach((order) => {
                if (selectedIds.includes(order.id)) {
                    onToggle(order.id);
                }
            });
        } else {
            // Seleccionar todos
            orders.forEach((order) => {
                if (!selectedIds.includes(order.id)) {
                    onToggle(order.id);
                }
            });
        }
    };

    // Función para calcular urgencia
    const getUrgencyLevel = (order: OrderWithExtras) => {
        const deliveryDate = new Date(
            order.estimatedDeliveryDate || order.deliveryDate,
        );
        const today = new Date();

        today.setHours(0, 0, 0, 0);
        const diffDays = Math.ceil(
            (deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
        );

        if (diffDays < 0) return "overdue";
        if (diffDays <= 3) return "urgent";
        if (diffDays <= 7) return "soon";

        return "normal";
    };

    // Función para obtener badge de urgencia
    const getUrgencyBadge = (urgency: string) => {
        switch (urgency) {
            case "overdue":
                return (
                    <Chip
                        color="danger"
                        size="sm"
                        startContent={
                            <ExclamationTriangleIcon className="w-3 h-3" />
                        }
                        variant="flat"
                    >
                        Vencida
                    </Chip>
                );
            case "urgent":
                return (
                    <Chip color="warning" size="sm" variant="flat">
                        Urgente
                    </Chip>
                );
            case "soon":
                return (
                    <Chip color="primary" size="sm" variant="flat">
                        Próxima
                    </Chip>
                );
            default:
                return null;
        }
    };

    // Procesar datos de órdenes con memoización
    const processedOrders = useMemo<ProcessedOrder[]>(() => {
        return orders.map((order) => {
            const totalQuantity =
                order.garments?.reduce(
                    (sum: number, g: Garment) =>
                        sum +
                        (g.sizes || []).reduce(
                            (sSum: number, s: GarmentSize) =>
                                sSum + s.totalQuantity,
                            0,
                        ),
                    0,
                ) || 0;
            const availableQuantity =
                order.garments?.reduce(
                    (sum: number, g: Garment) =>
                        sum +
                        (g.sizes || []).reduce(
                            (sSum: number, s: GarmentSize) =>
                                sSum + (s.totalQuantity - s.usedQuantity),
                            0,
                        ),
                    0,
                ) || 0;
            const availablePercentage =
                totalQuantity > 0
                    ? (availableQuantity / totalQuantity) * 100
                    : 0;

            return {
                ...order,
                urgency: getUrgencyLevel(order),
                totalQuantity,
                availableQuantity,
                availablePercentage,
                models: getModelCodes(order.garments),
                partsDisplay: formatPartsDisplay(order.parts),
                formattedDate: format(
                    new Date(order.estimatedDeliveryDate || order.deliveryDate),
                    "dd MMM",
                    { locale: es },
                ),
            };
        });
    }, [orders]);

    // Check if all visible orders are selected
    const isAllSelected =
        orders.length > 0 &&
        orders.every((order) => selectedIds.includes(order.id));

    return (
        <div className="flex flex-col h-full">
            {/* Barra de herramientas sticky */}
            <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm pb-2 space-y-2">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <Button
                            size="sm"
                            startContent={<CheckIcon className="w-4 h-4" />}
                            variant={isAllSelected ? "solid" : "bordered"}
                            onPress={toggleAll}
                        >
                            {isAllSelected ? "Deseleccionar" : "Seleccionar"}{" "}
                            todo
                        </Button>
                        <span className="text-sm text-default-500">
                            Mostrando {orders.length}{" "}
                            {orders.length === 1 ? "orden" : "órdenes"}
                        </span>
                    </div>

                    <div className="flex gap-1">
                        <Button
                            isIconOnly
                            size="sm"
                            variant={viewMode === "list" ? "solid" : "light"}
                            onPress={() => setViewMode("list")}
                        >
                            <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    d="M4 6h16M4 12h16M4 18h16"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                />
                            </svg>
                        </Button>
                        <Button
                            isIconOnly
                            size="sm"
                            variant={viewMode === "grid" ? "solid" : "light"}
                            onPress={() => setViewMode("grid")}
                        >
                            <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                />
                            </svg>
                        </Button>
                    </div>
                </div>

                {/* Estadísticas de selección inline */}
                {selectionStats.count > 0 && (
                    <div className="px-3 py-1 bg-primary-50 dark:bg-primary-900/20 rounded-medium text-sm">
                        <span className="font-medium">
                            {selectionStats.count} seleccionadas
                        </span>
                        <span className="text-default-500 mx-2">•</span>
                        <span>
                            {selectionStats.totalPieces.toLocaleString("es-ES")}{" "}
                            prendas
                        </span>
                        <span className="text-default-500 mx-2">•</span>
                        <span>
                            {selectionStats.customers}{" "}
                            {selectionStats.customers === 1
                                ? "cliente"
                                : "clientes"}
                        </span>
                    </div>
                )}
            </div>

            {/* Error */}
            {error && (
                <div className="p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg flex items-center gap-2 text-danger">
                    <ExclamationTriangleIcon className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                </div>
            )}

            {/* Lista de órdenes */}
            <ScrollShadow className="flex-1 min-h-0">
                <div
                    className={
                        viewMode === "grid"
                            ? "grid grid-cols-1 md:grid-cols-2 gap-4 p-2"
                            : "space-y-2 p-2"
                    }
                >
                    <AnimatePresence mode="popLayout">
                        {processedOrders.map((order, index) => {
                            const cardClassName = `
                cursor-pointer transition-all duration-200 ease-in-out
                ${
                    selectedIds.includes(order.id)
                        ? "ring-2 ring-primary bg-primary-50/50 dark:bg-primary-900/20"
                        : "hover:shadow-lg hover:-translate-y-0.5 hover:bg-default-50 dark:hover:bg-default-900/20"
                }
              `;

                            // Vista lista (completa)
                            if (viewMode === "list") {
                                return (
                                    <motion.div
                                        key={order.id}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, scale: 0.95 }}
                                        initial={{ opacity: 0, y: 20 }}
                                        transition={{
                                            delay: index * 0.01,
                                            duration: 0.2,
                                        }}
                                    >
                                        <Card
                                            isPressable
                                            className={cardClassName}
                                            onPress={() => onToggle(order.id)}
                                        >
                                            <CardBody className="p-3">
                                                <div className="flex gap-3">
                                                    <Checkbox
                                                        className="mt-1 data-[selected=true]:scale-110 transition-transform"
                                                        color="primary"
                                                        isSelected={selectedIds.includes(
                                                            order.id,
                                                        )}
                                                        onValueChange={() =>
                                                            onToggle(order.id)
                                                        }
                                                    />

                                                    <div className="flex-1 space-y-2">
                                                        {/* Header Row */}
                                                        <div className="flex items-start justify-between">
                                                            <div className="flex items-baseline gap-2">
                                                                <h3 className="text-lg font-bold text-foreground">
                                                                    {formatCutOrder(
                                                                        order,
                                                                    )}
                                                                </h3>
                                                                {order.code && (
                                                                    <span className="text-xs text-default-500">
                                                                        #
                                                                        {
                                                                            order.code
                                                                        }
                                                                    </span>
                                                                )}
                                                            </div>
                                                            {getUrgencyBadge(
                                                                order.urgency,
                                                            )}
                                                        </div>

                                                        {/* Parts Row */}
                                                        {order.partsDisplay
                                                            .visible.length >
                                                            0 && (
                                                            <div className="flex items-center gap-1 flex-wrap">
                                                                <span className="text-sm font-medium text-default-700">
                                                                    Partidas:
                                                                </span>
                                                                {order.partsDisplay.visible.map(
                                                                    (part) => (
                                                                        <Chip
                                                                            key={
                                                                                part.id
                                                                            }
                                                                            color="secondary"
                                                                            size="sm"
                                                                            startContent={
                                                                                <TagIcon className="w-3 h-3" />
                                                                            }
                                                                            variant="flat"
                                                                        >
                                                                            {
                                                                                part.code
                                                                            }
                                                                        </Chip>
                                                                    ),
                                                                )}
                                                                {order
                                                                    .partsDisplay
                                                                    .overflow >
                                                                    0 && (
                                                                    <Chip
                                                                        color="default"
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        +
                                                                        {
                                                                            order
                                                                                .partsDisplay
                                                                                .overflow
                                                                        }{" "}
                                                                        más
                                                                    </Chip>
                                                                )}
                                                            </div>
                                                        )}

                                                        {/* Info Row */}
                                                        <div className="flex flex-wrap items-center gap-2 text-sm">
                                                            <span className="font-medium">
                                                                Modelos:
                                                            </span>
                                                            <span className="text-default-600">
                                                                {order.models}
                                                            </span>
                                                            <span className="text-default-400">
                                                                •
                                                            </span>
                                                            <Chip
                                                                color={getStatusColor(
                                                                    order.status,
                                                                )}
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {order.status
                                                                    ?.name ||
                                                                    "Sin estado"}
                                                            </Chip>
                                                            <span className="text-default-400">
                                                                •
                                                            </span>
                                                            <Tooltip
                                                                closeDelay={0}
                                                                content={`Entrega estimada: ${format(new Date(order.estimatedDeliveryDate || order.deliveryDate), "EEEE dd 'de' MMMM 'de' yyyy", { locale: es })}`}
                                                                delay={0}
                                                                placement="top"
                                                            >
                                                                <span className="flex items-center gap-1 cursor-help">
                                                                    <CalendarIcon className="w-3 h-3" />
                                                                    {
                                                                        order.formattedDate
                                                                    }
                                                                </span>
                                                            </Tooltip>
                                                        </div>

                                                        {/* Availability Row */}
                                                        <div className="flex items-center gap-2">
                                                            {order.availablePercentage ===
                                                                100 && (
                                                                <Chip
                                                                    color="success"
                                                                    size="sm"
                                                                    startContent={
                                                                        <CheckCircleIcon className="w-3 h-3" />
                                                                    }
                                                                    variant="flat"
                                                                >
                                                                    Full
                                                                </Chip>
                                                            )}
                                                            {order.availablePercentage ===
                                                                0 && (
                                                                <Chip
                                                                    color="danger"
                                                                    size="sm"
                                                                    startContent={
                                                                        <ExclamationTriangleIcon className="w-3 h-3" />
                                                                    }
                                                                    variant="flat"
                                                                >
                                                                    Agotado
                                                                </Chip>
                                                            )}
                                                            <Tooltip
                                                                closeDelay={0}
                                                                content={`${order.availableQuantity.toLocaleString("es-ES")} disponibles de ${order.totalQuantity.toLocaleString("es-ES")} totales`}
                                                                delay={0}
                                                                placement="top"
                                                            >
                                                                <div className="flex-1">
                                                                    <Progress
                                                                        className="cursor-help"
                                                                        color={getAvailabilityColor(
                                                                            order.availablePercentage,
                                                                        )}
                                                                        size="sm"
                                                                        value={
                                                                            order.availablePercentage
                                                                        }
                                                                    />
                                                                </div>
                                                            </Tooltip>
                                                            <span className="text-sm font-medium text-default-700 min-w-fit">
                                                                {order.availableQuantity.toLocaleString(
                                                                    "es-ES",
                                                                )}
                                                                /
                                                                {order.totalQuantity.toLocaleString(
                                                                    "es-ES",
                                                                )}
                                                            </span>
                                                        </div>

                                                        {/* Customer (de-emphasized) */}
                                                        <div className="text-xs text-default-500 flex items-center gap-1">
                                                            <UserIcon className="w-3 h-3" />
                                                            {order.customer
                                                                ?.name ||
                                                                "Sin cliente"}
                                                        </div>
                                                    </div>
                                                </div>
                                            </CardBody>
                                        </Card>
                                    </motion.div>
                                );
                            }

                            // Vista grid (compacta)
                            return (
                                <motion.div
                                    key={order.id}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, scale: 0.95 }}
                                    initial={{ opacity: 0, y: 20 }}
                                    transition={{ delay: index * 0.01 }}
                                >
                                    <Card
                                        isPressable
                                        className={cardClassName}
                                        onPress={() => onToggle(order.id)}
                                    >
                                        <CardBody className="p-2">
                                            <div className="space-y-1">
                                                {/* Compact header */}
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-2">
                                                        <Checkbox
                                                            isSelected={selectedIds.includes(
                                                                order.id,
                                                            )}
                                                            size="sm"
                                                            onValueChange={() =>
                                                                onToggle(
                                                                    order.id,
                                                                )
                                                            }
                                                        />
                                                        <h4 className="font-bold text-sm">
                                                            {formatCutOrder(
                                                                order,
                                                            )}
                                                        </h4>
                                                    </div>
                                                    {order.urgency !==
                                                        "normal" && (
                                                        <Chip
                                                            className="h-5"
                                                            color={
                                                                order.urgency ===
                                                                "overdue"
                                                                    ? "danger"
                                                                    : "warning"
                                                            }
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            {order.urgency ===
                                                            "overdue"
                                                                ? "Venc."
                                                                : "Urg."}
                                                        </Chip>
                                                    )}
                                                </div>

                                                {/* Compact parts - show only first 2 */}
                                                {order.partsDisplay.visible
                                                    .length > 0 && (
                                                    <div className="flex gap-1">
                                                        {order.partsDisplay.visible
                                                            .slice(0, 2)
                                                            .map((part) => (
                                                                <Chip
                                                                    key={
                                                                        part.id
                                                                    }
                                                                    className="h-5 text-xs"
                                                                    color="secondary"
                                                                    size="sm"
                                                                    variant="flat"
                                                                >
                                                                    {part.code}
                                                                </Chip>
                                                            ))}
                                                        {order.partsDisplay
                                                            .visible.length >
                                                            2 && (
                                                            <span className="text-xs text-default-500">
                                                                +
                                                                {order
                                                                    .partsDisplay
                                                                    .visible
                                                                    .length - 2}
                                                            </span>
                                                        )}
                                                    </div>
                                                )}

                                                {/* Models and date in one line */}
                                                <div className="text-xs text-default-600">
                                                    {order.models
                                                        .split(",")[0]
                                                        .trim()}
                                                    {order.models.includes(
                                                        ",",
                                                    ) && "..."}{" "}
                                                    • {order.formattedDate}
                                                </div>

                                                {/* Compact progress */}
                                                <Progress
                                                    className="h-1"
                                                    color={getAvailabilityColor(
                                                        order.availablePercentage,
                                                    )}
                                                    size="sm"
                                                    value={
                                                        order.availablePercentage
                                                    }
                                                />
                                            </div>
                                        </CardBody>
                                    </Card>
                                </motion.div>
                            );
                        })}
                    </AnimatePresence>
                </div>

                {orders.length === 0 && (
                    <div className="flex flex-col items-center justify-center py-12 px-4">
                        <div className="w-16 h-16 mb-4 rounded-full bg-default-100 flex items-center justify-center">
                            <MagnifyingGlassIcon className="w-8 h-8 text-default-500" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">
                            No se encontraron órdenes
                        </h3>
                        <p className="text-sm text-default-500 text-center mb-6 max-w-md">
                            No hay órdenes que coincidan con los filtros
                            actuales. Intenta ajustar los criterios de búsqueda.
                        </p>
                        {(onClearFilters || onShowAllOrders) && (
                            <div className="flex gap-2">
                                {onClearFilters && (
                                    <Button
                                        size="sm"
                                        startContent={
                                            <XMarkIcon className="w-4 h-4" />
                                        }
                                        variant="light"
                                        onPress={onClearFilters}
                                    >
                                        Limpiar filtros
                                    </Button>
                                )}
                                {onShowAllOrders && (
                                    <Button
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                        onPress={onShowAllOrders}
                                    >
                                        Ver todas las órdenes
                                    </Button>
                                )}
                            </div>
                        )}
                    </div>
                )}
            </ScrollShadow>
        </div>
    );
}
