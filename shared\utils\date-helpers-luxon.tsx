import { DateTime } from "luxon";

/**
 * Parsea una fecha desde varios formatos posibles
 * @param dateInput Fecha en formato string, Date o null/undefined
 * @returns DateTime o null si no es válida
 */
export function parseDate(
    dateInput: string | Date | null | undefined,
): DateTime | null {
    if (!dateInput) return null;

    try {
        let dt: DateTime;

        if (dateInput instanceof Date) {
            dt = DateTime.fromJSDate(dateInput);
        } else if (typeof dateInput === "string") {
            // Intentar ISO primero
            dt = DateTime.fromISO(dateInput);

            // Si no es válido, intentar otros formatos
            if (!dt.isValid) {
                dt = DateTime.fromFormat(dateInput, "yyyy-MM-dd");
            }

            // Si sigue sin ser válido, intentar formato DD/MM/YYYY
            if (!dt.isValid) {
                dt = DateTime.fromFormat(dateInput, "dd/MM/yyyy");
            }
        } else {
            return null;
        }

        return dt.isValid ? dt.startOf("day") : null;
    } catch (error) {
        // REMOVED: console.error("Error parsing date:", error);

        return null;
    }
}

/**
 * Determina si una fecha es hoy
 * @param date Fecha a comprobar
 * @returns true si es hoy, false en caso contrario
 */
export function isToday(date: DateTime): boolean {
    const today = DateTime.now().startOf("day");

    return date.hasSame(today, "day");
}

/**
 * Determina si una fecha es mañana
 * @param date Fecha a comprobar
 * @returns true si es mañana, false en caso contrario
 */
export function isTomorrow(date: DateTime): boolean {
    const tomorrow = DateTime.now().plus({ days: 1 }).startOf("day");

    return date.hasSame(tomorrow, "day");
}

/**
 * Determina si una fecha es ayer
 * @param date Fecha a comprobar
 * @returns true si es ayer, false en caso contrario
 */
export function isYesterday(date: DateTime): boolean {
    const yesterday = DateTime.now().minus({ days: 1 }).startOf("day");

    return date.hasSame(yesterday, "day");
}

/**
 * Calcula la diferencia en días entre dos fechas
 * @param date1 Primera fecha
 * @param date2 Segunda fecha (por defecto: hoy)
 * @returns Número de días de diferencia (positivo si date1 > date2, negativo si date1 < date2)
 */
export function getDaysDifference(
    date1: DateTime,
    date2: DateTime = DateTime.now().startOf("day"),
): number {
    return Math.round(date1.diff(date2, "days").days);
}

/**
 * Genera un texto relativo para una fecha (ej: "Hoy", "Mañana", "En 3 días")
 * @param date Fecha para la que generar el texto
 * @param type Tipo de fecha (recibida o estimada)
 * @returns Texto relativo
 */
export function getRelativeText(
    date: DateTime,
    type: "received" | "estimated" = "estimated",
): string {
    if (isToday(date)) {
        return type === "estimated" ? "Para hoy" : "Hoy";
    }

    if (isTomorrow(date)) {
        return type === "estimated" ? "Para mañana" : "Mañana";
    }

    if (isYesterday(date)) {
        return "Ayer";
    }

    const diffDays = getDaysDifference(date);

    if (diffDays < 0) {
        // Fecha pasada
        const days = Math.abs(diffDays);

        return type === "estimated"
            ? `Atrasada ${days} día${days !== 1 ? "s" : ""}`
            : `Hace ${days} día${days !== 1 ? "s" : ""}`;
    }

    if (diffDays > 0 && diffDays <= 7) {
        // Próximos días (hasta una semana)
        return `En ${diffDays} día${diffDays !== 1 ? "s" : ""}`;
    }

    // Más de una semana
    if (diffDays <= 30) {
        const weeks = Math.floor(diffDays / 7);

        return `En ${weeks} semana${weeks !== 1 ? "s" : ""}`;
    }

    // Más de un mes
    const months = Math.floor(diffDays / 30);

    return `En ${months} mes${months !== 1 ? "es" : ""}`;
}

/**
 * Formatea una fecha como DD/MM/YYYY
 * @param date Fecha a formatear
 * @returns Fecha formateada como DD/MM/YYYY
 */
export function formatAsDDMMYYYY(date: DateTime): string {
    return date.toFormat("dd/MM/yyyy");
}

/**
 * Formatea una fecha como DD MMM (ej: 07 abr)
 * @param date Fecha a formatear
 * @returns Fecha formateada como DD MMM
 */
export function formatAsDDMMM(date: DateTime, locale: string = "es"): string {
    return date.setLocale(locale).toFormat("dd MMM");
}

/**
 * Ajusta una fecha para compensar el desfase de zona horaria
 * @param date Fecha a ajustar
 * @returns Fecha ajustada
 */
export function adjustTimezoneOffset(
    dateInput: string | Date | null | undefined,
): DateTime | null {
    const date = parseDate(dateInput);

    if (!date) return null;

    // Asegurarse de que la fecha se interprete en la zona horaria local
    return date.toLocal();
}
