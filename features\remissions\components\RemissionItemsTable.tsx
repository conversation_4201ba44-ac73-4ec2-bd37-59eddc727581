"use client";

import React from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Chip,
    Card,
    CardBody,
    CardHeader,
    Divider,
} from "@heroui/react";
import { motion } from "framer-motion";

interface RemissionItem {
    id?: string;
    modelCode: string;
    colorName: string;
    sizeCode: string;
    quantity: number;
}

interface RemissionItemsTableProps {
    items: RemissionItem[];
    showCard?: boolean;
    title?: string;
    icon?: React.ReactNode;
    showRowAnimations?: boolean;
    showTableFooter?: boolean;
    cardClassName?: string;
}

export default function RemissionItemsTable({
    items,
    showCard = true,
    title = "Detalle de Prendas",
    icon,
    showRowAnimations = false,
    showTableFooter = false,
    cardClassName = "",
}: RemissionItemsTableProps) {
    const columns = [
        { key: "model", label: "MODELO" },
        { key: "color", label: "COLOR" },
        { key: "size", label: "TALLA" },
        { key: "quantity", label: "CANTIDAD" },
    ];

    const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0);

    const springAnimation = {
        type: "spring",
        stiffness: 300,
        damping: 30,
    };

    const renderCell = (item: RemissionItem, columnKey: React.Key) => {
        switch (columnKey) {
            case "model":
                return (
                    <div className="flex items-center gap-2">
                        <span className="font-medium">{item.modelCode}</span>
                    </div>
                );
            case "color":
                return <span>{item.colorName}</span>;
            case "size":
                return (
                    <Chip size="sm" variant="flat">
                        {item.sizeCode}
                    </Chip>
                );
            case "quantity":
                return (
                    <div className="text-right font-semibold">
                        {item.quantity}
                    </div>
                );
            default:
                return null;
        }
    };

    const TableRowWrapper = ({
        children,
        index,
    }: {
        children: React.ReactNode;
        index: number;
    }) => {
        if (showRowAnimations) {
            return (
                <motion.div
                    animate={{ opacity: 1, x: 0 }}
                    initial={{ opacity: 0, x: -20 }}
                    transition={{ ...springAnimation, delay: 0.05 * index }}
                >
                    {children}
                </motion.div>
            );
        }

        return <>{children}</>;
    };

    const tableContent = (
        <Table
            aria-label="Tabla de items de remisión"
            bottomContent={
                showTableFooter && (
                    <div className="flex w-full justify-between items-center px-4 py-3 bg-gray-50 dark:bg-gray-800/50">
                        <span className="font-semibold">Total</span>
                        <span className="font-bold text-lg">
                            {totalQuantity}
                        </span>
                    </div>
                )
            }
            classNames={{
                wrapper: "shadow-none",
            }}
        >
            <TableHeader columns={columns}>
                {(column) => (
                    <TableColumn
                        key={column.key}
                        align={column.key === "quantity" ? "end" : "start"}
                    >
                        {column.label}
                    </TableColumn>
                )}
            </TableHeader>
            <TableBody items={items}>
                {
                    ((item: any, index: number) => (
                        <TableRow
                            key={
                                item.id ||
                                `${item.modelCode}-${item.colorName}-${item.sizeCode}-${index}`
                            }
                        >
                            {(columnKey) => (
                                <TableCell>
                                    <TableRowWrapper index={index}>
                                        {renderCell(item, columnKey)}
                                    </TableRowWrapper>
                                </TableCell>
                            )}
                        </TableRow>
                    )) as any
                }
            </TableBody>
        </Table>
    );

    if (!showCard) {
        return tableContent;
    }

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={cardClassName}
            initial={{ opacity: 0, y: 20 }}
            transition={springAnimation}
        >
            <Card className="hover:shadow-lg transition-shadow">
                {icon && (
                    <>
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                {icon}
                                <h3 className="text-lg font-semibold">
                                    {title}
                                </h3>
                            </div>
                        </CardHeader>
                        <Divider />
                    </>
                )}
                <CardBody className={icon ? "pt-4" : ""}>
                    {!icon && (
                        <h3 className="text-lg font-semibold mb-4">{title}</h3>
                    )}
                    {tableContent}
                    {!showTableFooter && (
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                    Total de items: {items.length}
                                </span>
                                <span className="text-lg font-bold">
                                    Total prendas: {totalQuantity}
                                </span>
                            </div>
                        </div>
                    )}
                </CardBody>
            </Card>
        </motion.div>
    );
}
