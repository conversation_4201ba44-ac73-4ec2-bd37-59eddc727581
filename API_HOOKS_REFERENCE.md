# 🔌 API & Hooks Reference - Sistema Lohari

## 📚 Tabla de Contenidos

1. [Server Actions API](#-server-actions-api)
2. [REST API Routes](#-rest-api-routes)
3. [Custom Hooks](#-custom-hooks)
4. [Shared Utilities](#-shared-utilities)
5. [Type Definitions](#-type-definitions)

---

## 🚀 Server Actions API

Server Actions son funciones asíncronas que se ejecutan en el servidor y pueden ser llamadas directamente desde componentes.

### 📦 Orders API

#### createOrder
Crea una nueva orden de producción.

```typescript
import { createOrder } from '@/features/orders/actions/create'

// Signature
async function createOrder(data: CreateOrderInput): Promise<{
  success: boolean
  order?: Order
  error?: string
}>

// Usage
const result = await createOrder({
  customerId: 'cuid-123',
  transferNumber: 'TRF-2024-001',
  cutOrder: 'CUT-2024-001',
  batch: 'BATCH-001',
  estimatedDeliveryDate: new Date('2024-12-31'),
  garments: [
    {
      modelId: 'model-shirt',
      colorId: 'color-blue',
      sizes: {
        'S': 10,
        'M': 20,
        'L': 15
      }
    }
  ],
  parts: ['P001', 'P002'],
  notes: 'Orden urgente'
})

if (result.success) {
  console.log('Orden creada:', result.order)
} else {
  console.error('Error:', result.error)
}
```

#### updateOrder
Actualiza una orden existente.

```typescript
import { updateOrder } from '@/features/orders/actions/update'

// Signature
async function updateOrder(
  id: string, 
  data: UpdateOrderInput
): Promise<{
  success: boolean
  order?: Order
  error?: string
}>

// Usage
await updateOrder('order-123', {
  statusId: 'status-completed',
  deliveryDate: new Date(),
  notes: 'Entregada con éxito'
})
```

#### deleteOrder
Elimina una orden (soft delete).

```typescript
import { deleteOrder } from '@/features/orders/actions/delete'

// Signature
async function deleteOrder(id: string): Promise<{
  success: boolean
  error?: string
}>

// Usage
await deleteOrder('order-123')
```

#### getOrders
Obtiene lista de órdenes con filtros.

```typescript
import { getOrders } from '@/features/orders/actions/query'

// Signature
async function getOrders(params?: {
  page?: number
  limit?: number
  search?: string
  statusId?: string
  customerId?: string
  dateFrom?: Date
  dateTo?: Date
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}): Promise<{
  orders: Order[]
  total: number
  pages: number
}>

// Usage
const { orders, total, pages } = await getOrders({
  page: 1,
  limit: 20,
  statusId: 'status-active',
  sortBy: 'createdAt',
  sortOrder: 'desc'
})
```

#### batchUpdateOrders
Actualiza múltiples órdenes.

```typescript
import { batchUpdateOrders } from '@/features/orders/actions/batch-actions'

// Signature
async function batchUpdateOrders(
  orderIds: string[],
  data: BatchUpdateData
): Promise<{
  success: boolean
  updated: number
  errors?: Array<{ id: string; error: string }>
}>

// Usage
await batchUpdateOrders(
  ['order-1', 'order-2', 'order-3'],
  { statusId: 'status-in-production' }
)
```

### 👥 Customers API

#### createCustomer
Crea un nuevo cliente.

```typescript
import { createCustomer } from '@/features/customers/actions/create'

// Signature
async function createCustomer(data: {
  name: string
  parentId?: string
  notes?: string
}): Promise<{
  success: boolean
  customer?: Customer
  error?: string
}>

// Usage
const result = await createCustomer({
  name: 'Cliente Nuevo SA',
  notes: 'Cliente importante'
})
```

#### createSubCustomer
Crea un sub-cliente.

```typescript
import { createSubCustomer } from '@/features/customers/actions/create-subcustomer'

// Signature
async function createSubCustomer(
  parentId: string,
  data: SubCustomerInput
): Promise<{
  success: boolean
  subCustomer?: Customer
  error?: string
}>

// Usage
await createSubCustomer('parent-123', {
  name: 'Sucursal Norte',
  displayName: 'Cliente Principal - Sucursal Norte'
})
```

#### getCustomerHierarchy
Obtiene la jerarquía completa de un cliente.

```typescript
import { getCustomerHierarchy } from '@/features/customers/actions/query'

// Signature
async function getCustomerHierarchy(
  customerId: string
): Promise<CustomerWithHierarchy>

// Usage
const hierarchy = await getCustomerHierarchy('customer-123')
// Returns customer with parent and subCustomers populated
```

### 👷 Contractors API

#### createContractor
Crea un nuevo contratista.

```typescript
import { createContractor } from '@/features/contractors/actions/create'

// Signature
async function createContractor(data: {
  name: string
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  notes?: string
}): Promise<{
  success: boolean
  contractor?: Contractor
  error?: string
}>

// Usage
await createContractor({
  name: 'Maquila Express',
  firstName: 'Juan',
  lastName: 'Pérez',
  email: '<EMAIL>',
  phone: '+52 555 1234567'
})
```

#### getContractorMetrics
Obtiene métricas de rendimiento de un contratista.

```typescript
import { getContractorMetrics } from '@/features/contractors/actions/getMetrics'

// Signature
async function getContractorMetrics(
  contractorId: string,
  dateRange?: { from: Date; to: Date }
): Promise<ContractorMetrics>

// Usage
const metrics = await getContractorMetrics('contractor-123', {
  from: new Date('2024-01-01'),
  to: new Date('2024-12-31')
})

// Returns
{
  totalAssignments: 150,
  completedAssignments: 145,
  completionRate: 96.7,
  averageDefectRate: 2.3,
  onTimeDeliveryRate: 94.5,
  currentActiveAssignments: 5,
  totalGarmentsProduced: 15000,
  averageTurnaroundTime: 5.2, // days
  qualityScore: 4.8 // out of 5
}
```

### 📋 Assignments API

#### createAssignmentV2
Crea asignaciones con validación mejorada.

```typescript
import { createAssignmentV2 } from '@/features/assignments/actions/create-v2'

// Signature
async function createAssignmentV2(data: {
  orderId: string
  assignments: Array<{
    contractorId: string
    garmentSizeId: string
    quantity: number
  }>
  generateFolios?: boolean
}): Promise<{
  success: boolean
  assignments?: Assignment[]
  folios?: string[]
  error?: string
}>

// Usage
const result = await createAssignmentV2({
  orderId: 'order-123',
  assignments: [
    {
      contractorId: 'contractor-1',
      garmentSizeId: 'garment-size-1',
      quantity: 50
    },
    {
      contractorId: 'contractor-2',
      garmentSizeId: 'garment-size-2',
      quantity: 30
    }
  ],
  generateFolios: true
})
```

#### updateAssignmentWithLocking
Actualiza asignación con control de concurrencia.

```typescript
import { updateAssignmentWithLocking } from '@/features/assignments/actions/update-with-locking'

// Signature
async function updateAssignmentWithLocking(
  id: string,
  data: UpdateAssignmentData,
  expectedVersion: number
): Promise<{
  success: boolean
  assignment?: Assignment
  error?: string
  conflictData?: Assignment
}>

// Usage
const result = await updateAssignmentWithLocking(
  'assignment-123',
  {
    quantity: 60,
    defects: 2,
    isCompleted: true
  },
  currentVersion // Prevents concurrent updates
)

if (!result.success && result.conflictData) {
  // Handle optimistic locking conflict
  console.log('Conflict detected, current version:', result.conflictData.version)
}
```

### 📄 Remissions API

#### createRemission
Crea una nueva remisión.

```typescript
import { createRemission } from '@/features/remissions/actions/create'

// Signature
async function createRemission(data: {
  contractorId: string
  assignmentIds: string[]
  notes?: string
  generatePDF?: boolean
}): Promise<{
  success: boolean
  remission?: Remission
  pdfUrl?: string
  error?: string
}>

// Usage
const result = await createRemission({
  contractorId: 'contractor-123',
  assignmentIds: ['assign-1', 'assign-2', 'assign-3'],
  notes: 'Entrega parcial del lote',
  generatePDF: true
})
```

#### generateRemissionPDF
Genera PDF de una remisión.

```typescript
import { generateRemissionPDF } from '@/features/remissions/actions/pdf'

// Signature
async function generateRemissionPDF(
  remissionId: string,
  options?: {
    includeSignatures?: boolean
    includeQR?: boolean
    copies?: number
  }
): Promise<Blob>

// Usage
const pdfBlob = await generateRemissionPDF('remission-123', {
  includeSignatures: true,
  includeQR: true,
  copies: 2
})
```

### 📝 Notes API

#### createNote
Crea una nueva nota.

```typescript
import { createNote } from '@/features/notes/actions/create'

// Signature
async function createNote(data: {
  content: string
  orderId?: string
  importanceId: string
  statusId?: string
  mentions?: string[]
  tags?: string[]
}): Promise<{
  success: boolean
  note?: Note
  error?: string
}>

// Usage
const result = await createNote({
  content: 'Revisar calidad del lote @supervisor #urgente',
  orderId: 'order-123',
  importanceId: 'importance-high',
  mentions: ['@supervisor'],
  tags: ['#urgente']
})
```

#### bulkDeleteNotes
Elimina múltiples notas.

```typescript
import { bulkDeleteNotes } from '@/features/notes/actions/bulk-delete'

// Signature
async function bulkDeleteNotes(
  noteIds: string[]
): Promise<{
  success: boolean
  deleted: number
  errors?: string[]
}>

// Usage
await bulkDeleteNotes(['note-1', 'note-2', 'note-3'])
```

#### createComment
Crea un comentario en una nota.

```typescript
import { createComment } from '@/features/notes/actions/comments/create'

// Signature
async function createComment(data: {
  noteId: string
  content: string
  parentId?: string
}): Promise<{
  success: boolean
  comment?: NoteComment
  error?: string
}>

// Usage
await createComment({
  noteId: 'note-123',
  content: 'Totalmente de acuerdo con esta observación',
  parentId: null // null for root comment
})
```

#### toggleLike
Alterna el like de una nota.

```typescript
import { toggleLike } from '@/features/notes/actions/likes/toggle'

// Signature
async function toggleLike(noteId: string): Promise<{
  success: boolean
  hasLiked: boolean
  totalLikes: number
  error?: string
}>

// Usage
const result = await toggleLike('note-123')
console.log(`Like ${result.hasLiked ? 'agregado' : 'removido'}`)
```

---

## 🌐 REST API Routes

Rutas API tradicionales para casos especiales.

### Authentication API

#### POST /api/auth/login
Autenticación de usuario.

```typescript
// Request
POST /api/auth/login
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "password123",
  "remember": true
}

// Response
{
  "success": true,
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "name": "Usuario",
    "role": "EMPLOYEE"
  },
  "token": "jwt-token..."
}
```

#### POST /api/auth/register
Registro de nuevo usuario.

```typescript
// Request
POST /api/auth/register
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "name": "Nuevo Usuario"
}

// Response
{
  "success": true,
  "user": {
    "id": "user-456",
    "email": "<EMAIL>",
    "name": "Nuevo Usuario",
    "role": "GUEST"
  }
}
```

#### GET /api/auth/me
Obtiene usuario actual.

```typescript
// Request
GET /api/auth/me
Authorization: Bearer {token}

// Response
{
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "name": "Usuario",
    "role": {
      "id": "role-123",
      "name": "EMPLOYEE"
    }
  }
}
```

### Remissions PDF API

#### GET /api/remissions/pdf
Genera PDF de remisión.

```typescript
// Request
GET /api/remissions/pdf?id=remission-123&format=letter

// Response
Content-Type: application/pdf
Content-Disposition: attachment; filename="remision-2024-001.pdf"
[Binary PDF data]
```

### Health Check API

#### GET /api/health
Verifica el estado del sistema.

```typescript
// Request
GET /api/health

// Response
{
  "status": "healthy",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "storage": "healthy"
  },
  "version": "0.0.1",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## 🪝 Custom Hooks

Hooks personalizados para gestión de estado y lógica reutilizable.

### 📦 Order Hooks

#### useOrders
Hook principal para gestión de órdenes.

```typescript
import { useOrders } from '@/features/orders/hooks/useOrders'

// Usage
function OrdersPage() {
  const {
    // Data
    orders,
    totalOrders,
    totalPages,
    
    // Loading states
    isLoading,
    isValidating,
    error,
    
    // Filters & Pagination
    filters,
    setFilters,
    page,
    setPage,
    
    // Actions
    createOrder,
    updateOrder,
    deleteOrder,
    refresh
  } = useOrders({
    initialPage: 1,
    initialFilters: {
      status: 'active'
    }
  })
  
  return (
    <div>
      {orders.map(order => (
        <OrderCard key={order.id} order={order} />
      ))}
    </div>
  )
}
```

#### useOrdersData
Hook optimizado con SWR para datos de órdenes.

```typescript
import { useOrdersData } from '@/features/orders/hooks/useOrdersData'

// Usage
function OrdersList() {
  const {
    data,
    isLoading,
    isValidating,
    error,
    mutate
  } = useOrdersData({
    page: 1,
    limit: 20,
    status: 'active',
    search: 'cliente'
  })
  
  // Manual refresh
  const handleRefresh = () => {
    mutate()
  }
  
  if (isLoading) return <Skeleton />
  if (error) return <Error />
  
  return <OrderTable orders={data.orders} />
}
```

#### useOrdersParams
Sincronización con URL params.

```typescript
import { useOrdersParams } from '@/features/orders/hooks/useOrdersParams'

// Usage
function OrdersWithUrlSync() {
  const { params, updateParams } = useOrdersParams()
  
  // Read from URL
  console.log(params) // { page: 1, status: 'active', search: 'test' }
  
  // Update URL
  const handleFilterChange = (key: string, value: any) => {
    updateParams({ [key]: value })
  }
  
  return <Filters onChange={handleFilterChange} />
}
```

#### useOrdersState
Estado local de órdenes con persistencia.

```typescript
import { useOrdersState } from '@/features/orders/hooks/useOrdersState'

// Usage
function OrdersWithState() {
  const {
    state,
    setState,
    updateFilter,
    resetFilters,
    toggleView
  } = useOrdersState({
    view: 'grid',
    filters: {
      status: 'all'
    }
  })
  
  return (
    <div>
      <button onClick={toggleView}>
        Vista: {state.view}
      </button>
      <button onClick={resetFilters}>
        Limpiar filtros
      </button>
    </div>
  )
}
```

### 🔐 Auth Hooks

#### useUser
Hook para obtener usuario actual.

```typescript
import { useUser } from '@/features/auth/hooks/useUser'

// Usage
function UserProfile() {
  const { 
    user, 
    isLoading, 
    isAuthenticated,
    hasRole,
    canAccess,
    logout
  } = useUser()
  
  if (isLoading) return <Loading />
  if (!isAuthenticated) return <LoginPrompt />
  
  return (
    <div>
      <p>Hola, {user.name}</p>
      <p>Rol: {user.role.name}</p>
      
      {hasRole('ADMIN') && (
        <AdminPanel />
      )}
      
      {canAccess('orders.create') && (
        <CreateOrderButton />
      )}
      
      <button onClick={logout}>Cerrar Sesión</button>
    </div>
  )
}
```

#### useAuthForm
Hook para formularios de autenticación.

```typescript
import { useAuthForm } from '@/features/auth/hooks/useAuthForm'

// Usage
function LoginForm() {
  const {
    register,
    handleSubmit,
    errors,
    isSubmitting,
    login
  } = useAuthForm()
  
  const onSubmit = handleSubmit(async (data) => {
    const result = await login(data)
    if (result.success) {
      router.push('/dashboard')
    }
  })
  
  return (
    <form onSubmit={onSubmit}>
      <Input
        {...register('email')}
        error={errors.email}
        placeholder="Email"
      />
      <Input
        {...register('password')}
        type="password"
        error={errors.password}
        placeholder="Contraseña"
      />
      <Checkbox {...register('remember')}>
        Recordarme
      </Checkbox>
      <Button type="submit" loading={isSubmitting}>
        Iniciar Sesión
      </Button>
    </form>
  )
}
```

### 📝 Notes Hooks

#### useNotes
Hook principal para notas.

```typescript
import { useNotes } from '@/features/notes/hooks/useNotes'

// Usage
function NotesSection({ orderId }) {
  const {
    notes,
    isLoading,
    error,
    createNote,
    updateNote,
    deleteNote,
    refresh
  } = useNotes({
    orderId,
    importance: 'high'
  })
  
  const handleCreate = async (content: string) => {
    await createNote({
      content,
      orderId,
      importanceId: 'importance-high'
    })
  }
  
  return (
    <div>
      <NoteForm onSubmit={handleCreate} />
      {notes.map(note => (
        <NoteCard 
          key={note.id} 
          note={note}
          onDelete={() => deleteNote(note.id)}
        />
      ))}
    </div>
  )
}
```

#### useNoteComments
Hook para comentarios de notas.

```typescript
import { useNoteComments } from '@/features/notes/hooks/useNoteComments'

// Usage
function NoteComments({ noteId }) {
  const {
    comments,
    isLoading,
    addComment,
    deleteComment,
    refresh
  } = useNoteComments(noteId)
  
  return (
    <CommentList 
      comments={comments}
      onAdd={addComment}
      onDelete={deleteComment}
    />
  )
}
```

#### useNoteLikes
Hook para sistema de likes.

```typescript
import { useNoteLikes } from '@/features/notes/hooks/useNoteLikes'

// Usage
function NoteLikeButton({ noteId }) {
  const {
    likes,
    hasLiked,
    isToggling,
    toggleLike
  } = useNoteLikes(noteId)
  
  return (
    <Button
      onClick={toggleLike}
      disabled={isToggling}
      variant={hasLiked ? 'solid' : 'ghost'}
    >
      <HeartIcon /> {likes}
    </Button>
  )
}
```

#### useNoteBulkActions
Hook para acciones masivas.

```typescript
import { useNoteBulkActions } from '@/features/notes/hooks/useNoteBulkActions'

// Usage
function NotesBulkActions({ notes }) {
  const {
    selectedNotes,
    isAllSelected,
    selectNote,
    deselectNote,
    selectAll,
    clearSelection,
    bulkDelete,
    bulkUpdateImportance,
    bulkUpdateStatus,
    isProcessing
  } = useNoteBulkActions()
  
  return (
    <div>
      <Checkbox
        checked={isAllSelected}
        onChange={(e) => e.target.checked ? selectAll(notes) : clearSelection()}
      >
        Seleccionar todo
      </Checkbox>
      
      <Button
        onClick={() => bulkDelete(selectedNotes)}
        disabled={selectedNotes.length === 0 || isProcessing}
      >
        Eliminar seleccionados ({selectedNotes.length})
      </Button>
    </div>
  )
}
```

### 🛠️ Shared Hooks

#### useRevalidationListener
Hook para escuchar cambios y revalidar datos.

```typescript
import { useRevalidationListener } from '@/shared/hooks/useRevalidationListener'

// Usage
function RealtimeOrders() {
  const { data, mutate } = useSWR('/api/orders', fetcher)
  
  // Listen for order changes and revalidate
  useRevalidationListener({
    channel: 'orders',
    onRevalidate: () => mutate(),
    throttleTime: 1000
  })
  
  return <OrderList orders={data} />
}
```

#### useFeatureFlag
Hook para feature flags.

```typescript
import { useFeatureFlag } from '@/shared/hooks/useFeatureFlag'

// Usage
function NewFeature() {
  const { isEnabled, isLoading } = useFeatureFlag('new-ui-design')
  
  if (isLoading) return null
  
  return isEnabled ? <NewUI /> : <OldUI />
}
```

#### useDebounce
Hook para debounce de valores.

```typescript
import { useDebounce } from '@/shared/hooks/useDebounce'

// Usage
function SearchInput() {
  const [search, setSearch] = useState('')
  const debouncedSearch = useDebounce(search, 500)
  
  // Use debouncedSearch for API calls
  useEffect(() => {
    if (debouncedSearch) {
      searchOrders(debouncedSearch)
    }
  }, [debouncedSearch])
  
  return (
    <Input
      value={search}
      onChange={(e) => setSearch(e.target.value)}
      placeholder="Buscar..."
    />
  )
}
```

---

## 🧰 Shared Utilities

Utilidades compartidas entre módulos.

### handleDbError
Manejo consistente de errores de base de datos.

```typescript
import { handleDbError } from '@/shared/actions/utils'

// Usage in Server Actions
export async function createOrder(data: OrderInput) {
  try {
    const order = await prisma.order.create({ data })
    return { success: true, order }
  } catch (error) {
    return handleDbError(error)
  }
}

// Returns formatted error
{
  success: false,
  error: "El registro ya existe" // User-friendly message
}
```

### revalidateData
Revalidación de caché de Next.js.

```typescript
import { revalidateData } from '@/shared/lib/revalidation'

// Usage
await revalidateData({
  paths: ['/dashboard/orders', '/dashboard'],
  tags: ['orders', 'metrics']
})
```

### formatters
Utilidades de formato.

```typescript
import { 
  formatCurrency, 
  formatDate, 
  formatNumber,
  formatPhone 
} from '@/shared/utils/formatters'

// Usage
formatCurrency(1234.56) // "$1,234.56"
formatDate(new Date()) // "15 Ene 2024"
formatNumber(1234567) // "1,234,567"
formatPhone('+525551234567') // "+52 ************"
```

### sortHelpers
Utilidades de ordenamiento.

```typescript
import { sortData, createSortComparator } from '@/shared/utils/sortHelpers'

// Usage
const sortedOrders = sortData(orders, 'createdAt', 'desc')

// Custom comparator
const comparator = createSortComparator([
  { field: 'priority', order: 'desc' },
  { field: 'createdAt', order: 'asc' }
])
orders.sort(comparator)
```

### dateHelpers
Utilidades de fecha con Luxon.

```typescript
import { 
  toLocalDate, 
  formatRelative,
  getDateRange,
  isOverdue 
} from '@/shared/utils/dateHelpers'

// Usage
toLocalDate('2024-01-15T10:30:00Z') // DateTime object
formatRelative(date) // "hace 2 días"
getDateRange('thisWeek') // { start: DateTime, end: DateTime }
isOverdue(order.estimatedDeliveryDate) // true/false
```

---

## 📝 Type Definitions

Tipos TypeScript principales del sistema.

### Order Types

```typescript
// Base Order type
interface Order {
  id: string
  transferNumber?: string
  cutOrder?: string
  batch?: string
  receivedDate: Date
  estimatedDeliveryDate?: Date
  deliveryDate?: Date
  customerId: string
  statusId: string
  createdAt: Date
  updatedAt: Date
  
  // Relations
  customer: Customer
  status: OrderStatus
  garments: Garment[]
  parts: OrderPart[]
  assignments: Assignment[]
  notes: Note[]
  rejection?: OrderRejection
  histories: OrderStatusHistory[]
}

// Input types
interface CreateOrderInput {
  customerId: string
  transferNumber?: string
  cutOrder?: string
  batch?: string
  estimatedDeliveryDate?: Date
  garments: Array<{
    modelId: string
    colorId: string
    sizes: Record<string, number>
  }>
  parts?: string[]
  notes?: string
}

interface UpdateOrderInput {
  statusId?: string
  deliveryDate?: Date
  estimatedDeliveryDate?: Date
  notes?: string
}

// Query types
interface OrderFilters {
  search?: string
  statusId?: string
  customerId?: string
  dateFrom?: Date
  dateTo?: Date
  hasAssignments?: boolean
  hasNotes?: boolean
}

interface OrderWithMetrics extends Order {
  metrics: {
    totalGarments: number
    completedGarments: number
    progressPercentage: number
    daysUntilDelivery: number
    isOverdue: boolean
    riskLevel: 'low' | 'medium' | 'high'
  }
}
```

### User & Auth Types

```typescript
// User type
interface User {
  id: string
  email: string
  name?: string
  emailVerified?: Date
  image?: string
  roleId: string
  createdAt: Date
  updatedAt: Date
  
  // Relations
  role: Role
}

// Role type
interface Role {
  id: string
  name: 'ADMIN' | 'EMPLOYEE' | 'CONTRACTOR' | 'GUEST'
  iconName?: string
  color?: string
  createdAt: Date
  updatedAt: Date
}

// Session type
interface SessionUser {
  id: string
  email: string
  name?: string
  role: string
  permissions?: string[]
}

// Auth types
interface LoginInput {
  email: string
  password: string
  remember?: boolean
}

interface RegisterInput {
  email: string
  password: string
  name?: string
}
```

### Assignment Types

```typescript
// Assignment type
interface Assignment {
  id: string
  folio?: string
  contractorId: string
  garmentSizeId: string
  quantity: number
  defects?: number
  isCompleted: boolean
  orderId: string
  version: number
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED'
  createdAt: Date
  updatedAt: Date
  
  // Relations
  contractor: Contractor
  garmentSize: GarmentSize
  order: Order
  progress?: AssignmentProgress
}

// Progress tracking
interface AssignmentProgress {
  id: string
  assignmentId: string
  completedQuantity: number
  reportedDefects: number
  lastUpdateAt: Date
  notes?: string
}
```

### Note Types

```typescript
// Note type
interface Note {
  id: string
  content: string
  orderId?: string
  userId: string
  importanceId: string
  statusId?: string
  createdAt: Date
  updatedAt: Date
  
  // Relations
  user: User
  order?: Order
  importance: NoteImportance
  status?: NoteStatus
  comments: NoteComment[]
  likes: NoteLike[]
  
  // Computed
  mentions?: string[]
  tags?: string[]
}

// Comment type
interface NoteComment {
  id: string
  content: string
  noteId: string
  userId: string
  parentId?: string
  createdAt: Date
  updatedAt: Date
  
  // Relations
  user: User
  note: Note
  parent?: NoteComment
  children: NoteComment[]
}

// Like type
interface NoteLike {
  id: string
  noteId: string
  userId: string
  createdAt: Date
  
  // Relations
  note: Note
  user: User
}
```

### Response Types

```typescript
// Generic response
interface ActionResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  errors?: Record<string, string[]>
}

// Paginated response
interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Batch response
interface BatchActionResponse {
  success: boolean
  processed: number
  failed: number
  errors?: Array<{
    id: string
    error: string
  }>
}
```

---

## 🔗 API Integration Examples

### Complete Order Flow

```typescript
// 1. Create customer
const customer = await createCustomer({
  name: 'Cliente ABC SA'
})

// 2. Create order
const order = await createOrder({
  customerId: customer.id,
  estimatedDeliveryDate: addDays(new Date(), 30),
  garments: [
    {
      modelId: 'shirt-basic',
      colorId: 'blue',
      sizes: { 'M': 100, 'L': 150 }
    }
  ]
})

// 3. Create assignments
const assignments = await createAssignmentV2({
  orderId: order.id,
  assignments: [
    {
      contractorId: 'contractor-1',
      garmentSizeId: 'garment-m',
      quantity: 100
    },
    {
      contractorId: 'contractor-2',
      garmentSizeId: 'garment-l',
      quantity: 150
    }
  ],
  generateFolios: true
})

// 4. Update assignment progress
await updateAssignmentWithLocking(
  assignments[0].id,
  { 
    quantity: 100,
    defects: 2,
    isCompleted: true 
  },
  assignments[0].version
)

// 5. Create remission
const remission = await createRemission({
  contractorId: 'contractor-1',
  assignmentIds: [assignments[0].id],
  notes: 'Primera entrega',
  generatePDF: true
})

// 6. Update order status
await updateOrder(order.id, {
  statusId: 'status-completed',
  deliveryDate: new Date()
})
```

### React Component Integration

```typescript
// OrderManagement.tsx
'use client'

import { useState } from 'react'
import { useOrders } from '@/features/orders/hooks/useOrders'
import { useToast } from '@/shared/hooks/useToast'

export function OrderManagement() {
  const [isCreating, setIsCreating] = useState(false)
  const { orders, createOrder, refresh } = useOrders()
  const { showToast } = useToast()
  
  const handleCreateOrder = async (data: CreateOrderInput) => {
    setIsCreating(true)
    
    try {
      const result = await createOrder(data)
      
      if (result.success) {
        showToast({
          title: 'Éxito',
          description: 'Orden creada correctamente',
          type: 'success'
        })
        refresh()
      } else {
        showToast({
          title: 'Error',
          description: result.error || 'Error al crear orden',
          type: 'error'
        })
      }
    } finally {
      setIsCreating(false)
    }
  }
  
  return (
    <div>
      <OrderForm 
        onSubmit={handleCreateOrder}
        isSubmitting={isCreating}
      />
      <OrderList orders={orders} />
    </div>
  )
}
```

---

**Última actualización**: Enero 2025  
**Versión API**: 1.0.0