-- Migration: Add customer fields to Packing table
-- Description: Adds customerId and subCustomerId fields to support customer hierarchy in packings

-- First, check if there are existing packings and handle the NOT NULL constraint
DO $$
BEGIN
    -- Add customerId as nullable first
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Packing' AND column_name = 'customerId') THEN
        ALTER TABLE "Packing" ADD COLUMN "customerId" TEXT;
    END IF;
    
    -- Add subCustomerId
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Packing' AND column_name = 'subCustomerId') THEN
        ALTER TABLE "Packing" ADD COLUMN "subCustomerId" TEXT;
    END IF;
    
    -- If there are existing packings, we need to set a default customerId
    -- You'll need to update this with an actual customer ID from your database
    IF EXISTS (SELECT 1 FROM "Packing" WHERE "customerId" IS NULL) THEN
        -- Try to use the first customer as default, or create a placeholder
        UPDATE "Packing" 
        SET "customerId" = COALESCE(
            (SELECT "id" FROM "Customer" LIMIT 1),
            'placeholder-customer-id'
        )
        WHERE "customerId" IS NULL;
    END IF;
END $$;

-- Now make customerId NOT NULL after setting values
ALTER TABLE "Packing" ALTER COLUMN "customerId" SET NOT NULL;

-- Add foreign key constraints
DO $$
BEGIN
    -- Add foreign key for customerId if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'Packing_customerId_fkey') THEN
        ALTER TABLE "Packing" ADD CONSTRAINT "Packing_customerId_fkey" 
        FOREIGN KEY ("customerId") REFERENCES "Customer"("id") 
        ON DELETE RESTRICT ON UPDATE CASCADE;
    END IF;
    
    -- Add foreign key for subCustomerId if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'Packing_subCustomerId_fkey') THEN
        ALTER TABLE "Packing" ADD CONSTRAINT "Packing_subCustomerId_fkey" 
        FOREIGN KEY ("subCustomerId") REFERENCES "Customer"("id") 
        ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS "Packing_customerId_idx" ON "Packing"("customerId");
CREATE INDEX IF NOT EXISTS "Packing_subCustomerId_idx" ON "Packing"("subCustomerId" ) WHERE "subCustomerId" IS NOT NULL;