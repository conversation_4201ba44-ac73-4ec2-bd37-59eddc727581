# 📚 Índice de Documentación - Sistema Lohari

Bienvenido a la documentación completa del Sistema Lohari. Esta guía te ayudará a navegar por toda la documentación disponible del proyecto.

## 🎯 Documentación Principal

### 📖 [PROJECT_DOCUMENTATION.md](./PROJECT_DOCUMENTATION.md)
**Documentación General del Proyecto**
- Visión general del sistema
- Propósito y características principales
- Arquitectura general
- Modelo de datos completo
- Sistema de diseño
- Módulos del sistema
- Sistema de autenticación
- Guía de instalación rápida
- Guía de uso
- Roadmap del proyecto

### 🏗️ [TECHNICAL_ARCHITECTURE.md](./TECHNICAL_ARCHITECTURE.md)
**Arquitectura Técnica Detallada**
- Principios arquitectónicos
- Stack tecnológico en profundidad
- Patrones de diseño implementados
- Arquitectura de componentes
- Server Actions y API Routes
- Estrategias de seguridad
- Optimización de performance
- Testing strategy
- Monitoring y observabilidad

### 📦 [FEATURES_GUIDE.md](./FEATURES_GUIDE.md)
**Guía Completa de Features y Módulos**
- Documentación detallada de cada módulo:
  - Orders (Órdenes)
  - Customers (Clientes)
  - Contractors (Contratistas)
  - Assignments (Asignaciones)
  - Remissions (Remisiones)
  - Notes (Notas)
  - Packings (Empaquetado)
  - Auth (Autenticación)
  - Colors, Sizes, Models
- Estructura de cada módulo
- Componentes principales
- Hooks disponibles
- Server Actions
- Ejemplos de uso

### 🔌 [API_HOOKS_REFERENCE.md](./API_HOOKS_REFERENCE.md)
**Referencia de API y Hooks**
- Server Actions API completa
- REST API Routes
- Custom Hooks detallados
- Utilidades compartidas
- Type definitions
- Ejemplos de integración
- Patrones de uso

### 🚀 [DEPLOYMENT_OPERATIONS_GUIDE.md](./DEPLOYMENT_OPERATIONS_GUIDE.md)
**Guía de Deployment y Operaciones**
- Instalación local paso a paso
- Configuración de entorno
- Opciones de deployment (Vercel, Railway, Docker)
- Operaciones y mantenimiento
- Monitoreo y logs
- Backup y recuperación
- Troubleshooting
- Scripts útiles

## 📁 Documentación Existente del Proyecto

### Documentación de Diseño
- [designsystem.md](./designsystem.md) - Sistema de diseño SEO Grove inspired
- [docs/design-system.md](./docs/design-system.md) - Sistema de diseño unificado implementado

### Documentación de Referencia
- [docs/reference/project-structure-complete.md](./docs/reference/project-structure-complete.md) - Estructura detallada del proyecto
- [docs/reference/business-modules-complete.md](./docs/reference/business-modules-complete.md) - Módulos de negocio
- [docs/reference/authentication-system-complete.md](./docs/reference/authentication-system-complete.md) - Sistema de autenticación
- [docs/reference/technology-stack-complete.md](./docs/reference/technology-stack-complete.md) - Stack tecnológico
- [docs/reference/database-schema-complete.md](./docs/reference/database-schema-complete.md) - Esquema de base de datos
- [docs/reference/design-system-complete.md](./docs/reference/design-system-complete.md) - Sistema de diseño completo

### Guías de Desarrollo
- [docs/development-patterns.md](./docs/development-patterns.md) - Patrones de desarrollo
- [docs/db-best-practices.md](./docs/db-best-practices.md) - Mejores prácticas de base de datos
- [CONTRIBUTING.md](./CONTRIBUTING.md) - Guía de contribución
- [CLAUDE.md](./CLAUDE.md) - Instrucciones para el asistente de código

## 🚦 Por Dónde Empezar

### Para Nuevos Desarrolladores
1. Lee [PROJECT_DOCUMENTATION.md](./PROJECT_DOCUMENTATION.md) para entender el sistema
2. Sigue la guía de instalación en [DEPLOYMENT_OPERATIONS_GUIDE.md](./DEPLOYMENT_OPERATIONS_GUIDE.md)
3. Revisa [FEATURES_GUIDE.md](./FEATURES_GUIDE.md) para entender los módulos
4. Consulta [API_HOOKS_REFERENCE.md](./API_HOOKS_REFERENCE.md) al desarrollar

### Para DevOps/SysAdmin
1. Comienza con [DEPLOYMENT_OPERATIONS_GUIDE.md](./DEPLOYMENT_OPERATIONS_GUIDE.md)
2. Revisa [TECHNICAL_ARCHITECTURE.md](./TECHNICAL_ARCHITECTURE.md) para la infraestructura
3. Configura monitoreo según la sección correspondiente

### Para Product Managers
1. Lee la visión general en [PROJECT_DOCUMENTATION.md](./PROJECT_DOCUMENTATION.md)
2. Revisa los módulos en [FEATURES_GUIDE.md](./FEATURES_GUIDE.md)
3. Consulta el roadmap al final de PROJECT_DOCUMENTATION.md

### Para Diseñadores
1. Revisa el sistema de diseño en [designsystem.md](./designsystem.md)
2. Consulta [docs/design-system.md](./docs/design-system.md) para la implementación
3. Ve los componentes UI en [FEATURES_GUIDE.md](./FEATURES_GUIDE.md)

## 📊 Estado del Proyecto

### Versión Actual
- **Versión**: 0.0.1
- **Estado**: Beta/Producción
- **Última actualización**: Enero 2025

### Módulos Completados
- ✅ Orders (Órdenes)
- ✅ Customers (Clientes)
- ✅ Contractors (Contratistas)
- ✅ Assignments (Asignaciones)
- ✅ Remissions (Remisiones)
- ✅ Notes (Notas)
- ✅ Auth (Autenticación)
- ✅ Colors, Sizes, Models
- 🚧 Packings (En desarrollo)

### Tecnologías Principales
- **Frontend**: Next.js 14.2.3, React 18.3, TypeScript
- **UI**: HeroUI 2.7.5, TailwindCSS 3.4
- **Backend**: Next.js API Routes, Prisma 6.8.2
- **Database**: PostgreSQL (Supabase)
- **Auth**: NextAuth 4.24.7

## 🔍 Búsqueda Rápida

### Temas Comunes

**¿Cómo crear una orden?**
→ Ver [FEATURES_GUIDE.md#-orders-órdenes](./FEATURES_GUIDE.md#-orders-órdenes)

**¿Cómo configurar autenticación?**
→ Ver [PROJECT_DOCUMENTATION.md#-sistema-de-autenticación](./PROJECT_DOCUMENTATION.md#-sistema-de-autenticación)

**¿Cómo hacer deploy?**
→ Ver [DEPLOYMENT_OPERATIONS_GUIDE.md#-deployment-en-producción](./DEPLOYMENT_OPERATIONS_GUIDE.md#-deployment-en-producción)

**¿Cómo usar los hooks?**
→ Ver [API_HOOKS_REFERENCE.md#-custom-hooks](./API_HOOKS_REFERENCE.md#-custom-hooks)

**¿Cómo está estructurado el proyecto?**
→ Ver [TECHNICAL_ARCHITECTURE.md#-visión-arquitectónica](./TECHNICAL_ARCHITECTURE.md#-visión-arquitectónica)

## 📞 Soporte

Si no encuentras lo que buscas:
1. Revisa los archivos de documentación en `/docs`
2. Busca en el código fuente
3. Consulta los issues en GitHub
4. Contacta al equipo de desarrollo

---

**Tip**: Usa `Ctrl+F` (o `Cmd+F` en Mac) para buscar términos específicos en cada documento.

**Última actualización de este índice**: Enero 2025