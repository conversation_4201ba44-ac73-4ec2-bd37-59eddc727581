/**
 * Utilidades para trabajar con Prisma de manera segura
 *
 * Este archivo proporciona funciones auxiliares y utilidades para
 * operaciones comunes con Prisma, especialmente enfocadas en
 * prevenir y manejar errores relacionados con prepared statements.
 */

import { Prisma } from "@prisma/client";

import { prisma } from "@/shared/lib/prisma";

// import { observeError } from "@/shared/services/db/error-observer"; // Commented out - module doesn't exist
const observeError = (error: any, context: string, metadata?: any) => {
    console.error(`[${context}] Database error:`, error, metadata);
};

// Tipos para consultas seguras
type RawQueryResult<T> = T extends Array<infer U> ? U[] : T;

/**
 * Funciones para ejecutar SQL raw directamente cuando sea necesario
 * como alternativa a los métodos de Prisma que pueden fallar con
 * errores de prepared statements.
 */
export const rawSql = {
    /**
     * Ejecuta una consulta SELECT a una tabla con condiciones básicas
     */
    async select<T = any>(
        table: string,
        where: Record<string, any> = {},
        select: string[] = ["*"],
        options: {
            orderBy?: string;
            limit?: number;
            offset?: number;
        } = {},
    ): Promise<T[]> {
        try {
            // Use prisma directly from import

            // Construir lista de columnas a seleccionar
            const columns =
                select.length === 0 ||
                (select.length === 1 && select[0] === "*")
                    ? "*"
                    : select.join(", ");

            // Construir cláusula WHERE
            let whereClause = "";
            const whereParams: any[] = [];

            if (Object.keys(where).length > 0) {
                const conditions = Object.entries(where).map(
                    ([key, value], index) => {
                        if (value === null) {
                            return `"${key}" IS NULL`;
                        }
                        whereParams.push(value);

                        return `"${key}" = $${index + 1}`;
                    },
                );

                whereClause = `WHERE ${conditions.join(" AND ")}`;
            }

            // Construir cláusulas ORDER BY, LIMIT, OFFSET
            const orderByClause = options.orderBy
                ? `ORDER BY ${options.orderBy}`
                : "";
            const limitClause = options.limit ? `LIMIT ${options.limit}` : "";
            const offsetClause = options.offset
                ? `OFFSET ${options.offset}`
                : "";

            // Construir consulta completa
            const query = `
        SELECT ${columns}
        FROM "${table}"
        ${whereClause}
        ${orderByClause}
        ${limitClause}
        ${offsetClause}
      `;

            // Ejecutar consulta raw
            const result = await prisma.$queryRawUnsafe(query, ...whereParams);

            return result as T[];
        } catch (error: any) {
            // Registrar error en el observador
            observeError(error, "rawSql.select", { table });
            throw error;
        }
    },

    /**
     * Inserta un registro en una tabla
     */
    async insert<T = any>(
        table: string,
        data: Record<string, any>,
    ): Promise<T> {
        try {
            // Use prisma directly from import

            const columns = Object.keys(data)
                .map((key) => `"${key}"`)
                .join(", ");
            const placeholders = Object.keys(data)
                .map((_, i) => `$${i + 1}`)
                .join(", ");
            const values = Object.values(data);

            const query = `
        INSERT INTO "${table}" (${columns})
        VALUES (${placeholders})
        RETURNING *
      `;

            const result = await prisma.$queryRawUnsafe(query, ...values);

            return Array.isArray(result) && result.length > 0
                ? (result[0] as T)
                : (result as T);
        } catch (error: any) {
            // Registrar error en el observador
            observeError(error, "rawSql.insert", { table });
            throw error;
        }
    },

    /**
     * Actualiza registros en una tabla
     */
    async update<T = any>(
        table: string,
        where: Record<string, any>,
        data: Record<string, any>,
    ): Promise<T> {
        try {
            // Use prisma directly from import

            // Construir SET
            const setEntries = Object.entries(data);
            const setClauses = setEntries
                .map(([key], i) => `"${key}" = $${i + 1}`)
                .join(", ");

            // Construir WHERE
            const whereEntries = Object.entries(where);
            const whereClauses = whereEntries
                .map(([key], i) => {
                    const paramIndex = i + setEntries.length + 1;

                    return `"${key}" = $${paramIndex}`;
                })
                .join(" AND ");

            // Parámetros
            const params = [...Object.values(data), ...Object.values(where)];

            // Construir consulta
            const query = `
        UPDATE "${table}"
        SET ${setClauses}
        WHERE ${whereClauses}
        RETURNING *
      `;

            const result = await prisma.$queryRawUnsafe(query, ...params);

            return Array.isArray(result) && result.length > 0
                ? (result[0] as T)
                : (result as T);
        } catch (error: any) {
            // Registrar error en el observador
            observeError(error, "rawSql.update", { table });
            throw error;
        }
    },

    /**
     * Ejecuta una consulta upsert (insert or update)
     */
    async upsert<T = any>(
        table: string,
        where: Record<string, any>,
        create: Record<string, any>,
        update: Record<string, any>,
    ): Promise<T> {
        try {
            // Use prisma directly from import

            // Intentar primero un SELECT para ver si el registro existe
            const whereClause = Object.entries(where)
                .map(([key], i) => `"${key}" = $${i + 1}`)
                .join(" AND ");
            const whereParams = Object.values(where);

            const selectQuery = `SELECT 1 FROM "${table}" WHERE ${whereClause} LIMIT 1`;
            const exists = await prisma.$queryRawUnsafe(
                selectQuery,
                ...whereParams,
            );

            // Si existe, actualizar
            if (Array.isArray(exists) && exists.length > 0) {
                return this.update<T>(table, where, update);
            }

            // Si no existe, crear
            return this.insert<T>(table, { ...where, ...create });
        } catch (error: any) {
            // Registrar error en el observador
            observeError(error, "rawSql.upsert", { table });
            throw error;
        }
    },
};

/**
 * Mock data for testing purposes
 */
const mockData: Record<string, any[]> = {};

/**
 * Reset Prisma connection - mock implementation
 */
const resetPrismaConnection = async () => {
    console.log("Resetting Prisma connection...");
};

/**
 * Obtiene datos mock para una tabla específica
 */
export function getMockData<T = any>(tableName: string): T[] {
    const normalizedName = tableName.toLowerCase();

    // Intentar encontrar datos mock por el nombre de la tabla
    for (const [key, value] of Object.entries(mockData)) {
        // Convertir el nombre de la tabla a singular o buscar coincidencia parcial
        if (
            key.toLowerCase() === normalizedName ||
            key.toLowerCase() === normalizedName.replace(/s$/, "") || // Quitar 's' final
            normalizedName.includes(key.toLowerCase())
        ) {
            return value as T[];
        }
    }

    // Si no hay datos mock específicos, retornar un array vacío
    // REMOVED: console.warn(`No hay datos mock disponibles para la tabla ${tableName}`);

    return [] as T[];
}

/**
 * Verifica si un error está relacionado con prepared statements
 */
export function isPreparedStatementError(error: Error | string): boolean {
    const errorMessage = typeof error === "string" ? error : error.message;

    return /prepared statement .* (already exists|does not exist)/i.test(
        errorMessage,
    );
}

/**
 * Ejecuta una operación con reintento automático
 * si ocurre un error de prepared statement
 */
export async function executeSafely<T>(
    operation: () => Promise<T>,
    options: {
        maxRetries?: number;
        onRetry?: (error: Error, attempt: number) => void;
        fallbackFn?: () => Promise<T>;
        mockFn?: () => T;
    } = {},
): Promise<T> {
    const { maxRetries = 3, onRetry = () => {}, fallbackFn, mockFn } = options;

    let lastError: Error | null = null;

    // Intentar la operación original con reintentos
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            // Si no es el primer intento y el error fue de prepared statement,
            // intentar resetear la conexión
            if (
                attempt > 1 &&
                lastError &&
                isPreparedStatementError(lastError)
            ) {
                try {
                    await resetPrismaConnection();
                } catch (resetError) {
                    // REMOVED: console.warn("Error al resetear conexión:", resetError);
                }
            }

            return await operation();
        } catch (error: any) {
            lastError = error;

            // Registrar en el observador
            observeError(error, "executeSafely", { attempt });

            // Si no es el último intento, reintentar
            if (attempt < maxRetries) {
                onRetry(error, attempt);

                // Esperar antes del siguiente intento con backoff exponencial
                const delay = Math.min(100 * Math.pow(2, attempt - 1), 5000);

                await new Promise((resolve) => setTimeout(resolve, delay));
                continue;
            }
        }
    }

    // Si llegamos aquí, los reintentos fallaron
    // REMOVED: console.warn(`Operación falló después de ${maxRetries} intentos, probando fallback...`);

    // Intentar función de fallback si está disponible
    if (fallbackFn) {
        try {
            return await fallbackFn();
        } catch (fallbackError: any) {
            // REMOVED: console.error("Error en función de fallback:", fallbackError);

            // Último recurso: datos mock
            if (mockFn) {
                // REMOVED: console.warn("Usando datos mock como último recurso");

                return mockFn();
            }
        }
    } else if (mockFn) {
        // Si no hay fallback pero sí hay mock, usarlo directamente
        // REMOVED: console.warn("No hay fallback, usando datos mock");

        return mockFn();
    }

    // Si no hay fallback ni mock, o ambos fallaron, lanzar el último error
    throw lastError!;
}

/**
 * Construye una consulta SQL para buscar registros por ID
 */
export function buildFindByIdQuery(
    table: string,
    id: string,
    select: string[] = ["*"],
): { query: string; params: string[] } {
    const columns =
        select.length === 0 || (select.length === 1 && select[0] === "*")
            ? "*"
            : select.join(", ");

    return {
        query: `SELECT ${columns} FROM "${table}" WHERE id = $1 LIMIT 1`,
        params: [id],
    };
}

/**
 * Construye una cláusula WHERE para consultas SQL a partir de un objeto
 */
export function buildWhereClause(conditions: Record<string, any>): {
    whereClause: string;
    params: any[];
} {
    const params: any[] = [];

    if (Object.keys(conditions).length === 0) {
        return { whereClause: "", params: [] };
    }

    const clauses = Object.entries(conditions).map(([key, value], index) => {
        if (value === null) {
            return `"${key}" IS NULL`;
        }
        params.push(value);

        return `"${key}" = $${index + 1}`;
    });

    return {
        whereClause: `WHERE ${clauses.join(" AND ")}`,
        params,
    };
}

/**
 * Alias para las funciones de raw SQL más utilizadas
 */
export const sql = {
    select: rawSql.select,
    insert: rawSql.insert,
    update: rawSql.update,
    upsert: rawSql.upsert,
};

/**
 * Comprueba si un error es un error de Prisma
 *
 * @param error Error a verificar
 * @returns True si es un error de Prisma
 */
export function isPrismaError(error: any): boolean {
    return (
        error instanceof Prisma.PrismaClientKnownRequestError ||
        error instanceof Prisma.PrismaClientUnknownRequestError ||
        error instanceof Prisma.PrismaClientRustPanicError ||
        error instanceof Prisma.PrismaClientInitializationError ||
        error instanceof Prisma.PrismaClientValidationError
    );
}

/**
 * Obtiene un mensaje de error amigable para el usuario a partir de un error de Prisma
 *
 * @param error Error de Prisma
 * @param defaultMessage Mensaje predeterminado si no se puede determinar
 * @returns Mensaje de error amigable
 */
export function getPrismaErrorMessage(
    error: any,
    defaultMessage: string = "Error en la operación de base de datos",
): string {
    if (!isPrismaError(error)) {
        return error?.message || defaultMessage;
    }

    observeError(error, "prisma-utils", { function: "getPrismaErrorMessage" });

    if (error instanceof Prisma.PrismaClientValidationError) {
        return "Datos inválidos para la operación solicitada";
    }

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
            case "P2002":
                return "Ya existe un registro con estos datos";
            case "P2003":
                return "Operación restringida por referencias a otros datos";
            case "P2025":
                return "Registro no encontrado";
            default:
                return `Error en la operación: ${error.code}`;
        }
    }

    return defaultMessage;
}

/**
 * Ejecuta una consulta Prisma con manejo de errores
 *
 * @param queryFn Función que ejecuta la consulta Prisma
 * @param errorMessage Mensaje de error personalizado
 * @returns Resultado de la consulta o respuesta de error
 */
export async function withErrorHandling<T>(
    queryFn: () => Promise<T>,
    errorMessage: string = "Error en la operación de base de datos",
): Promise<{ success: boolean; data?: T; error?: string }> {
    try {
        const result = await queryFn();

        return { success: true, data: result };
    } catch (error: any) {
        observeError(error, "prisma-utils", { function: "withErrorHandling" });
        const friendlyMessage = getPrismaErrorMessage(error, errorMessage);

        return { success: false, error: friendlyMessage };
    }
}

/**
 * Construye una condición WHERE de Prisma para búsqueda de texto
 * en múltiples campos
 *
 * @param searchText Texto a buscar
 * @param fields Campos donde buscar
 * @returns Condición Prisma para OR de búsquedas
 */
export function buildSearchCondition(
    searchText: string | null | undefined,
    fields: string[],
): any {
    if (!searchText || searchText.trim() === "") {
        return {};
    }

    const trimmedSearch = searchText.trim();

    const OR = fields.map((field) => {
        const condition: any = {};

        condition[field] = { contains: trimmedSearch, mode: "insensitive" };

        return condition;
    });

    return { OR };
}

/**
 * Ejecuta una consulta Prisma con reintento automático para errores
 * de prepared statements
 *
 * @param queryFn Función que ejecuta la consulta
 * @param maxRetries Número máximo de reintentos
 * @returns Resultado de la consulta
 */
export async function withPreparedStatementRetry<T>(
    queryFn: () => Promise<T>,
    maxRetries: number = 3,
): Promise<T> {
    let lastError: any = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await queryFn();
        } catch (error: any) {
            lastError = error;

            if (error?.message?.includes("prepared statement")) {
                observeError(error, "prisma-utils", {
                    function: "withPreparedStatementRetry",
                    attempt,
                });

                if (attempt === maxRetries) {
                    try {
                        // @ts-ignore - Acceder a propiedades internas
                        if (db._engine) {
                            // @ts-ignore
                            await db._engine.reset();
                        }
                    } catch (resetError) {
                        // REMOVED: console.error("Error al reiniciar conexión:", resetError);
                    }
                }

                const delay = Math.pow(2, attempt) * 100;

                await new Promise((resolve) => setTimeout(resolve, delay));
                continue;
            }

            throw error;
        }
    }

    throw lastError;
}
