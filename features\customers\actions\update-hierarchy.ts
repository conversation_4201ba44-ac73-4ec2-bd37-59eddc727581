"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/shared/lib/db";

const updateHierarchySchema = z.object({
    customerId: z.string().min(1, "El ID del cliente es requerido"),
    parentId: z.string().nullable(),
});

export async function updateCustomerHierarchy(
    data: z.infer<typeof updateHierarchySchema>,
) {
    try {
        const validatedData = updateHierarchySchema.parse(data);

        // Verificar que el cliente existe
        const customer = await db.customer.findUnique({
            where: { id: validatedData.customerId },
        });

        if (!customer) {
            return { success: false, error: "Cliente no encontrado" };
        }

        // Si se está asignando un padre
        if (validatedData.parentId) {
            // Verificar que el padre existe
            const parentCustomer = await db.customer.findUnique({
                where: { id: validatedData.parentId },
            });

            if (!parentCustomer) {
                return { success: false, error: "Cliente padre no encontrado" };
            }

            // Verificar que no se está creando una referencia circular
            if (validatedData.parentId === validatedData.customerId) {
                return {
                    success: false,
                    error: "Un cliente no puede ser su propio padre",
                };
            }

            // Verificar que el padre no sea un descendiente del cliente actual
            const isDescendant = await checkIfDescendant(
                validatedData.customerId,
                validatedData.parentId,
            );

            if (isDescendant) {
                return {
                    success: false,
                    error: "No se puede crear una referencia circular en la jerarquía",
                };
            }
        }

        // Actualizar la jerarquía
        const updatedCustomer = await db.customer.update({
            where: { id: validatedData.customerId },
            data: {
                parentId: validatedData.parentId,
            },
            include: {
                parent: true,
                subCustomers: true,
            },
        });

        revalidatePath("/dashboard/customers");
        revalidatePath(
            `/dashboard/customers/${validatedData.customerId}/details`,
        );
        if (customer.parentId) {
            revalidatePath(`/dashboard/customers/${customer.parentId}/details`);
        }
        if (validatedData.parentId) {
            revalidatePath(
                `/dashboard/customers/${validatedData.parentId}/details`,
            );
        }

        return { success: true, data: updatedCustomer };
    } catch (error) {
        console.error("Error updating customer hierarchy:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return {
            success: false,
            error: "Error al actualizar la jerarquía del cliente",
        };
    }
}

// Función auxiliar para verificar si un cliente es descendiente de otro
async function checkIfDescendant(
    ancestorId: string,
    possibleDescendantId: string,
): Promise<boolean> {
    const customer = await db.customer.findUnique({
        where: { id: possibleDescendantId },
        include: {
            subCustomers: true,
        },
    });

    if (!customer) return false;

    // Verificar hijos directos
    if (customer.subCustomers.some((sub) => sub.id === ancestorId)) {
        return true;
    }

    // Verificar descendientes recursivamente
    for (const subCustomer of customer.subCustomers) {
        if (await checkIfDescendant(ancestorId, subCustomer.id)) {
            return true;
        }
    }

    return false;
}

export async function removeFromHierarchy(customerId: string) {
    try {
        const customer = await db.customer.update({
            where: { id: customerId },
            data: {
                parentId: null,
            },
            include: {
                parent: true,
                subCustomers: true,
            },
        });

        revalidatePath("/dashboard/customers");
        revalidatePath(`/dashboard/customers/${customerId}/details`);

        return { success: true, data: customer };
    } catch (error) {
        console.error("Error removing customer from hierarchy:", error);

        return {
            success: false,
            error: "Error al remover el cliente de la jerarquía",
        };
    }
}
