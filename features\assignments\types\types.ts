// Exportar tipos desde schema
export * from "../schemas/schema";

// Definición de tipo para contratistas
export interface Contractor {
    id: string;
    name: string;
    address?: string;
}

// Definición de tipo para orden de status
export interface OrderStatus {
    id: string;
    name: string;
    color?: string;
    iconName?: string;
}

// Definición de tipo para colores
export interface Color {
    id: string;
    name: string;
    hexCode?: string;
}

// Definición de tipo para modelos
export interface GarmentModel {
    id: string;
    code: string;
    description?: string;
}

// Definición de tipo para tallas
export interface Size {
    id: string;
    code: string;
}

// Definición de tipo para tamaños de prendas
export interface GarmentSize {
    id: string;
    size: Size;
    totalQuantity: number;
    usedQuantity: number;
}

// Definición de tipo para prendas
export interface Garment {
    id: string;
    model?: GarmentModel;
    color?: Color;
    sizes?: GarmentSize[];
}

// Definición de tipo para órdenes
export interface Order {
    id: string;
    transferNumber: string;
    cutOrder: string | null;
    batch: string | null;
    receivedDate: string | null;
    estimatedDeliveryDate?: string | null;
    status?: OrderStatus;
    customer: {
        id: string;
        name: string;
    };
    garments?: Garment[];
    _count?: {
        garments: number;
    };
}

// Añadir el tipo OptimisticValidationResponse para validaciones
import { AssignmentResponse } from "../schemas/schema";

/**
 * Interfaz para la respuesta de la validación optimista
 */
export interface OptimisticValidationResponse extends AssignmentResponse {
    version?: string;
    availableQuantity?: number;
    suggestions?: {
        maxAvailable?: number;
        recommendedQuantity?: number;
    };
}
