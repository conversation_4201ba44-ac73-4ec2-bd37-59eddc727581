"use client";

import React from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import Image from "next/image";

interface PackingDocumentProps {
    packing: {
        id: string;
        folio: string;
        deliveryDate: string;
        createdAt: string;
        notes?: string | null;
        customer: {
            name: string;
        };
        subCustomer?: {
            name: string;
        } | null;
        details: Array<{
            quantity: number;
            garmentSize: {
                size: { code: string };
                garment: {
                    model: { code: string; description?: string };
                    color: { name: string };
                };
            };
        }>;
    };
    showSignatures?: boolean;
}

export function PackingDocument({
    packing,
    showSignatures = true,
}: PackingDocumentProps) {
    // Agrupar detalles por modelo y color
    const groupedDetails = packing.details.reduce(
        (acc, detail) => {
            const key = `${detail.garmentSize.garment.model.code}-${detail.garmentSize.garment.color.name}`;

            if (!acc[key]) {
                acc[key] = {
                    model: detail.garmentSize.garment.model,
                    color: detail.garmentSize.garment.color.name,
                    sizes: {},
                    total: 0,
                };
            }

            const sizeCode = detail.garmentSize.size.code;

            if (!acc[key].sizes[sizeCode]) {
                acc[key].sizes[sizeCode] = 0;
            }
            acc[key].sizes[sizeCode] += detail.quantity;
            acc[key].total += detail.quantity;

            return acc;
        },
        {} as Record<string, any>,
    );

    // Obtener todas las tallas únicas ordenadas
    const allSizes = new Set<string>();

    Object.values(groupedDetails).forEach((group: any) => {
        Object.keys(group.sizes).forEach((size) => allSizes.add(size));
    });
    const sortedSizes = Array.from(allSizes).sort();

    const totalPieces = Object.values(groupedDetails).reduce(
        (sum: number, group: any) => sum + group.total,
        0,
    );

    return (
        <div
            className="bg-white p-8 max-w-4xl mx-auto"
            id={`packing-document-${packing.id}`}
        >
            {/* Header */}
            <div className="flex items-start justify-between mb-8">
                <div className="flex items-center gap-4">
                    <Image
                        alt="Lohari Logo"
                        className="object-contain"
                        height={60}
                        src="/LOGO-LOHARI.svg"
                        width={120}
                    />
                </div>

                <div className="text-right">
                    <h1 className="text-2xl font-bold mb-2">PACKING LIST</h1>
                    <div className="space-y-1">
                        <p className="text-sm">
                            <span className="font-semibold">Folio:</span>{" "}
                            {packing.folio}
                        </p>
                        <p className="text-sm">
                            <span className="font-semibold">Fecha:</span>{" "}
                            {format(new Date(packing.createdAt), "dd/MM/yyyy")}
                        </p>
                    </div>
                </div>
            </div>

            {/* Información del cliente */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <p className="text-sm font-semibold mb-1">Cliente:</p>
                        <p className="text-lg">{packing.customer.name}</p>
                        {packing.subCustomer && (
                            <p className="text-sm text-gray-600">
                                {packing.subCustomer.name}
                            </p>
                        )}
                    </div>
                    <div>
                        <p className="text-sm font-semibold mb-1">
                            Fecha de Entrega:
                        </p>
                        <p className="text-lg">
                            {format(
                                new Date(packing.deliveryDate),
                                "EEEE, dd 'de' MMMM 'de' yyyy",
                                { locale: es },
                            )}
                        </p>
                    </div>
                </div>

                {packing.notes && (
                    <div className="mt-3 pt-3 border-t">
                        <p className="text-sm font-semibold mb-1">Notas:</p>
                        <p className="text-sm">{packing.notes}</p>
                    </div>
                )}
            </div>

            {/* Tabla de artículos */}
            <div className="mb-6">
                <table className="w-full border-collapse">
                    <thead>
                        <tr className="bg-gray-100">
                            <th className="border p-2 text-left text-sm font-semibold">
                                Modelo
                            </th>
                            <th className="border p-2 text-left text-sm font-semibold">
                                Color
                            </th>
                            {sortedSizes.map((size) => (
                                <th
                                    key={size}
                                    className="border p-2 text-center text-sm font-semibold w-16"
                                >
                                    {size}
                                </th>
                            ))}
                            <th className="border p-2 text-center text-sm font-semibold w-20">
                                Total
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {Object.entries(groupedDetails).map(
                            ([key, group]: [string, any], index) => (
                                <tr
                                    key={key}
                                    className={
                                        index % 2 === 0
                                            ? "bg-white"
                                            : "bg-gray-50"
                                    }
                                >
                                    <td className="border p-2 text-sm">
                                        <div>
                                            <p className="font-medium">
                                                {group.model.code}
                                            </p>
                                            {group.model.description && (
                                                <p className="text-xs text-gray-600">
                                                    {group.model.description}
                                                </p>
                                            )}
                                        </div>
                                    </td>
                                    <td className="border p-2 text-sm">
                                        {group.color}
                                    </td>
                                    {sortedSizes.map((size) => (
                                        <td
                                            key={size}
                                            className="border p-2 text-center text-sm"
                                        >
                                            {group.sizes[size] || "-"}
                                        </td>
                                    ))}
                                    <td className="border p-2 text-center text-sm font-semibold">
                                        {group.total}
                                    </td>
                                </tr>
                            ),
                        )}
                    </tbody>
                    <tfoot>
                        <tr className="bg-gray-100">
                            <td
                                className="border p-2 text-right font-semibold"
                                colSpan={2}
                            >
                                TOTAL GENERAL:
                            </td>
                            {sortedSizes.map((size) => {
                                const sizeTotal = Object.values(
                                    groupedDetails,
                                ).reduce(
                                    (sum: number, group: any) =>
                                        sum + (group.sizes[size] || 0),
                                    0,
                                );

                                return (
                                    <td
                                        key={size}
                                        className="border p-2 text-center font-semibold text-sm"
                                    >
                                        {sizeTotal || "-"}
                                    </td>
                                );
                            })}
                            <td className="border p-2 text-center font-bold text-lg">
                                {totalPieces}
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            {/* Firmas */}
            {showSignatures && (
                <div className="mt-12 grid grid-cols-3 gap-8">
                    <div className="text-center">
                        <div className="border-t-2 border-gray-400 pt-2">
                            <p className="text-sm font-semibold">Entrega</p>
                            <p className="text-xs text-gray-600 mt-1">
                                Nombre y Firma
                            </p>
                        </div>
                    </div>
                    <div className="text-center">
                        <div className="border-t-2 border-gray-400 pt-2">
                            <p className="text-sm font-semibold">
                                Transportista
                            </p>
                            <p className="text-xs text-gray-600 mt-1">
                                Nombre y Firma
                            </p>
                        </div>
                    </div>
                    <div className="text-center">
                        <div className="border-t-2 border-gray-400 pt-2">
                            <p className="text-sm font-semibold">Recibe</p>
                            <p className="text-xs text-gray-600 mt-1">
                                Nombre y Firma
                            </p>
                        </div>
                    </div>
                </div>
            )}

            {/* Footer */}
            <div className="mt-8 pt-4 border-t text-center text-xs text-gray-500">
                <p>Este documento es un comprobante de entrega de mercancía</p>
                <p>Lohari - Sistema de Control de Producción</p>
            </div>
        </div>
    );
}
