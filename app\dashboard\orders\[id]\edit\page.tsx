"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    TagIcon,
    DocumentIcon,
    ShoppingBagIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";

import { CrudFormTemplate } from "@/shared/templates";
import {
    OrderBasicInfoStep,
    OrderPartsStep,
    OrderGarmentsStep,
    OrderReviewStep,
} from "@/features/orders/components/wizard";
import { addToast } from "@/shared/components/ui/hero-ui-client";
import { useUpdateOrder } from "@/features/orders/hooks/useOrder";
import { useOrderEdit } from "@/features/orders/hooks/useOrderEdit";
import { SizeOption, OrderFormData } from "@/core/types/types";

// Size order for sorting
const sizeOrder = ["XS", "S", "M", "L", "XL", "XXL"];

function sortSizes(sizes: SizeOption[]): SizeOption[] {
    return [...sizes].sort((a, b) => {
        const ia = sizeOrder.indexOf(a.code.toUpperCase());
        const ib = sizeOrder.indexOf(b.code.toUpperCase());
        const va = ia >= 0 ? ia : sizeOrder.length;
        const vb = ib >= 0 ? ib : sizeOrder.length;

        return va - vb;
    });
}

export default function EditOrderPage() {
    const router = useRouter();
    const params = useParams();
    const orderId = params.id as string;
    const { updateOrder } = useUpdateOrder();

    // Cargar todos los datos necesarios con un solo hook
    const { formData, orderData, isLoading, error } = useOrderEdit(orderId);

    // Form state
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    const [receivedDate, setReceivedDate] = useState<string>(formattedDate);
    const [estimatedDeliveryDate, setEstimatedDeliveryDate] =
        useState<string>(formattedDate);
    const [orderFormData, setOrderFormData] = useState<OrderFormData>({
        transferNumber: "",
        cutOrder: "",
        batch: "1",
        customerId: "",
        subCustomerId: undefined,
        statusId: "",
        garments: [
            { modelId: "", colorId: "", sizes: [{ sizeId: "", quantity: "" }] },
        ],
        parts: [{ code: "" }],
        notes: [],
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [validationErrors, setValidationErrors] = useState<
        Record<string, string>
    >({});

    // Wizard state
    const [currentStep, setCurrentStep] = useState(0);
    const [completedSteps, setCompletedSteps] = useState<Set<string>>(
        new Set(),
    );

    // Extraer opciones del formulario
    const customers = formData?.customers || [];
    const orderStatuses = formData?.orderStatuses || [];
    const models = formData?.models || [];
    const colors = formData?.colors || [];
    const sizesOptions = formData ? sortSizes(formData.sizes || []) : [];

    // Cargar datos existentes de la orden cuando estén disponibles
    useEffect(() => {
        if (!orderData || !orderId) return;

        // Actualizar estado del formulario con datos de la orden
        setOrderFormData({
            transferNumber: orderData.transferNumber || "",
            cutOrder: orderData.cutOrder || "",
            batch: orderData.batch || "1",
            customerId: orderData.customer?.id || "",
            subCustomerId: orderData.subCustomer?.id || undefined,
            statusId: orderData.status?.id || "",
            garments: Array.isArray(orderData.garments)
                ? orderData.garments.map((g: any) => ({
                      id: g.id, // Preservar ID para actualizaciones
                      modelId: g.model?.id || "",
                      colorId: g.color?.id || "",
                      sizes: Array.isArray(g.sizes)
                          ? g.sizes.map((s: any) => ({
                                sizeId: s.size?.id || "",
                                quantity: (s.totalQuantity || 0).toString(),
                            }))
                          : [{ sizeId: "", quantity: "" }],
                  }))
                : [
                      {
                          modelId: "",
                          colorId: "",
                          sizes: [{ sizeId: "", quantity: "" }],
                      },
                  ],
            parts: Array.isArray(orderData.parts)
                ? orderData.parts.map((p: any) => ({
                      id: p.id, // Preservar ID para actualizaciones
                      code: p.code || "",
                  }))
                : [{ code: "" }],
            notes: [],
        });

        // Establecer fechas
        if (orderData.receivedDate) {
            const receivedDate = new Date(orderData.receivedDate);

            setReceivedDate(receivedDate.toISOString().split("T")[0]);
        }

        if (orderData.estimatedDeliveryDate) {
            const deliveryDate = new Date(orderData.estimatedDeliveryDate);

            setEstimatedDeliveryDate(deliveryDate.toISOString().split("T")[0]);
        }
    }, [orderData, orderId]);

    // Field change handlers
    const handleFieldChange = useCallback((key: string, value: any) => {
        setOrderFormData((prev) => ({ ...prev, [key]: value }));
    }, []);

    const handleDateChange = useCallback(
        (field: "receivedDate" | "estimatedDeliveryDate", value: string) => {
            if (field === "receivedDate") {
                setReceivedDate(value);
            } else {
                setEstimatedDeliveryDate(value);
            }
        },
        [],
    );

    // Parts handlers
    const addOrderPart = useCallback(() => {
        setOrderFormData((prev) => ({
            ...prev,
            parts: [...prev.parts, { code: "" }],
        }));
    }, []);

    const removeOrderPart = useCallback((index: number) => {
        setOrderFormData((prev) => ({
            ...prev,
            parts: prev.parts.filter((_, i) => i !== index),
        }));
    }, []);

    const handleOrderPartChange = useCallback(
        (index: number, value: string) => {
            setOrderFormData((prev) => {
                const updated = [...prev.parts];

                updated[index] = { ...updated[index], code: value };

                return { ...prev, parts: updated };
            });
        },
        [],
    );

    // Garments handlers
    const addGarment = useCallback(() => {
        setOrderFormData((prev) => ({
            ...prev,
            garments: [
                ...prev.garments,
                {
                    modelId: "",
                    colorId: "",
                    sizes: [{ sizeId: "", quantity: "" }],
                },
            ],
        }));
    }, []);

    const removeGarment = useCallback((index: number) => {
        setOrderFormData((prev) => ({
            ...prev,
            garments: prev.garments.filter((_, i) => i !== index),
        }));
    }, []);

    const handleGarmentChange = useCallback(
        (garmentIndex: number, field: string, value: string) => {
            setOrderFormData((prev) => {
                const updated = [...prev.garments];

                if (field === "modelId" || field === "colorId") {
                    updated[garmentIndex] = {
                        ...updated[garmentIndex],
                        [field]: value,
                    };
                }

                return { ...prev, garments: updated };
            });
        },
        [],
    );

    // Sizes handlers
    const addSizeToGarment = useCallback(
        (garmentIndex: number) => {
            setOrderFormData((prev) => {
                const updated = [...prev.garments];
                const updatedGarment = { ...updated[garmentIndex] };

                updatedGarment.sizes = [
                    ...updatedGarment.sizes,
                    {
                        sizeId:
                            sizesOptions.length > 0 ? sizesOptions[0].id : "",
                        quantity: "",
                    },
                ];
                updated[garmentIndex] = updatedGarment;

                return { ...prev, garments: updated };
            });
        },
        [sizesOptions],
    );

    const removeSizeFromGarment = useCallback(
        (garmentIndex: number, sizeIndex: number) => {
            setOrderFormData((prev) => {
                const updated = [...prev.garments];
                const updatedGarment = { ...updated[garmentIndex] };

                updatedGarment.sizes = updatedGarment.sizes.filter(
                    (_, i) => i !== sizeIndex,
                );
                updated[garmentIndex] = updatedGarment;

                return { ...prev, garments: updated };
            });
        },
        [],
    );

    const handleSizeChange = useCallback(
        (
            garmentIndex: number,
            sizeIndex: number,
            field: string,
            value: string,
        ) => {
            setOrderFormData((prev) => {
                const updated = [...prev.garments];
                const updatedGarment = { ...updated[garmentIndex] };
                const updatedSizes = [...updatedGarment.sizes];

                updatedSizes[sizeIndex] = {
                    ...updatedSizes[sizeIndex],
                    [field]: value,
                };
                updatedGarment.sizes = updatedSizes;
                updated[garmentIndex] = updatedGarment;

                return { ...prev, garments: updated };
            });
        },
        [],
    );

    // Pure validation functions (no side effects)
    const getBasicInfoErrors = useCallback(() => {
        const errors: Record<string, string> = {};

        if (!orderFormData.customerId) {
            errors.customerId = "Debes seleccionar un cliente";
        }
        if (!orderFormData.statusId) {
            errors.statusId = "Debes seleccionar un estado";
        }
        if (!estimatedDeliveryDate) {
            errors.estimatedDeliveryDate = "La fecha de entrega es obligatoria";
        }

        return errors;
    }, [
        orderFormData.customerId,
        orderFormData.statusId,
        estimatedDeliveryDate,
    ]);

    // Validation functions with side effects (for events)
    const validateBasicInfo = useCallback(() => {
        const errors = getBasicInfoErrors();

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }, [getBasicInfoErrors]);

    const getPartsErrors = useCallback(() => {
        const errors: Record<string, string> = {};

        const partCodes = orderFormData.parts
            .filter((p) => p.code.trim())
            .map((p) => p.code.trim().toUpperCase());

        if (partCodes.length === 0) {
            errors.parts = "Agrega al menos una partida con código";
        } else if (new Set(partCodes).size !== partCodes.length) {
            errors.parts_duplicate =
                "No se permiten códigos de partidas duplicados";
        }

        return errors;
    }, [orderFormData.parts]);

    const validateParts = useCallback(() => {
        const errors = getPartsErrors();

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }, [getPartsErrors]);

    const getGarmentsErrors = useCallback(() => {
        const errors: Record<string, string> = {};

        if (orderFormData.garments.length === 0) {
            errors.garments = "Agrega al menos una prenda";
        } else {
            orderFormData.garments.forEach((garment, idx) => {
                if (!garment.modelId) {
                    errors[`garment_${idx}_model`] = "Selecciona un modelo";
                }
                if (!garment.colorId) {
                    errors[`garment_${idx}_color`] = "Selecciona un color";
                }

                if (garment.sizes.length === 0) {
                    errors[`garment_${idx}_sizes`] =
                        "Agrega al menos una talla";
                } else {
                    const sizeIds = garment.sizes.map((sz) => sz.sizeId);

                    if (new Set(sizeIds).size !== sizeIds.length) {
                        errors[`garment_${idx}_duplicate`] =
                            "No se permiten tallas duplicadas";
                    }

                    garment.sizes.forEach((size, sizeIdx) => {
                        const qty = parseInt(size.quantity, 10);

                        if (!size.sizeId) {
                            errors[`garment_${idx}_size_${sizeIdx}_id`] =
                                "Selecciona una talla";
                        }
                        if (isNaN(qty) || qty <= 0) {
                            errors[`garment_${idx}_size_${sizeIdx}_qty`] =
                                "Cantidad debe ser mayor a 0";
                        }
                    });
                }
            });
        }

        return errors;
    }, [orderFormData.garments]);

    const validateGarments = useCallback(() => {
        const errors = getGarmentsErrors();

        setValidationErrors(errors);

        return Object.keys(errors).length === 0;
    }, [getGarmentsErrors]);

    // Wizard steps configuration
    const wizardSteps = [
        {
            id: "basic-info",
            title: "Información Básica",
            subtitle: "Datos principales de la orden",
            icon: <TagIcon className="w-5 h-5" />,
            component: (
                <OrderBasicInfoStep
                    customers={customers}
                    errors={validationErrors}
                    estimatedDeliveryDate={estimatedDeliveryDate}
                    formData={orderFormData}
                    orderStatuses={orderStatuses}
                    receivedDate={receivedDate}
                    onChange={handleFieldChange}
                    onDateChange={handleDateChange}
                />
            ),
            validation: () => Object.keys(getBasicInfoErrors()).length === 0,
        },
        {
            id: "parts",
            title: "Partidas",
            subtitle: "Códigos de partida",
            icon: <DocumentIcon className="w-5 h-5" />,
            component: (
                <OrderPartsStep
                    errors={validationErrors}
                    parts={orderFormData.parts}
                    onAddPart={addOrderPart}
                    onPartChange={handleOrderPartChange}
                    onRemovePart={removeOrderPart}
                />
            ),
            validation: () => Object.keys(getPartsErrors()).length === 0,
        },
        {
            id: "garments",
            title: "Prendas",
            subtitle: "Modelos, colores y tallas",
            icon: <ShoppingBagIcon className="w-5 h-5" />,
            component: (
                <OrderGarmentsStep
                    colors={colors}
                    errors={validationErrors}
                    garments={orderFormData.garments}
                    models={models}
                    sizesOptions={sizesOptions}
                    onAddGarment={addGarment}
                    onAddSize={addSizeToGarment}
                    onGarmentChange={handleGarmentChange}
                    onRemoveGarment={removeGarment}
                    onRemoveSize={removeSizeFromGarment}
                    onSizeChange={handleSizeChange}
                />
            ),
            validation: () => Object.keys(getGarmentsErrors()).length === 0,
        },
        {
            id: "review",
            title: "Revisión",
            subtitle: "Confirma los cambios",
            icon: <CheckCircleIcon className="w-5 h-5" />,
            component: (
                <OrderReviewStep
                    colors={colors}
                    customers={customers}
                    estimatedDeliveryDate={estimatedDeliveryDate}
                    formData={orderFormData}
                    models={models}
                    orderStatuses={orderStatuses}
                    receivedDate={receivedDate}
                    sizesOptions={sizesOptions}
                    onEditStep={setCurrentStep}
                />
            ),
            validation: () => true,
        },
    ];

    // Handle step change
    const handleStepChange = (step: number) => {
        // Validate current step if moving forward
        if (step > currentStep) {
            let isValid = false;

            switch (currentStep) {
                case 0:
                    isValid = validateBasicInfo();
                    break;
                case 1:
                    isValid = validateParts();
                    break;
                case 2:
                    isValid = validateGarments();
                    break;
                default:
                    isValid = true;
            }

            if (isValid) {
                setCompletedSteps(
                    (prev) => new Set([...prev, wizardSteps[currentStep].id]),
                );
                setCurrentStep(step);
            }
        } else {
            // Allow going back without validation
            setCurrentStep(step);
        }
    };

    // Submit handler for updating order
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate all steps
        const allValid = wizardSteps.every((step, index) => {
            setCurrentStep(index);

            return step.validation();
        });

        if (!allValid) {
            addToast({
                title: "Error",
                description: "Por favor, corrige los errores en el formulario",
                color: "danger",
            });

            return;
        }

        setIsSubmitting(true);

        try {
            // Prepare order data for update
            const orderData = {
                customerId: orderFormData.customerId,
                subCustomerId: orderFormData.subCustomerId || undefined,
                statusId: orderFormData.statusId,
                receivedDate: receivedDate,
                estimatedDeliveryDate: estimatedDeliveryDate || undefined,
                transferNumber: orderFormData.transferNumber || undefined,
                cutOrder: orderFormData.cutOrder || undefined,
                batch: orderFormData.batch || undefined,
                parts: orderFormData.parts
                    .filter((part) => part.code.trim() !== "")
                    .map((part) => ({
                        id: (part as any).id, // Include ID if exists for updates
                        code: part.code.trim(),
                    })),
                garments: orderFormData.garments
                    .filter(
                        (garment) =>
                            garment.modelId &&
                            garment.colorId &&
                            garment.sizes.some(
                                (size) =>
                                    size.sizeId && parseInt(size.quantity) > 0,
                            ),
                    )
                    .map((garment) => ({
                        id: (garment as any).id, // Include ID if exists for updates
                        modelId: garment.modelId,
                        colorId: garment.colorId,
                        sizes: garment.sizes
                            .filter(
                                (size) =>
                                    size.sizeId && parseInt(size.quantity) > 0,
                            )
                            .map((size) => ({
                                sizeId: size.sizeId,
                                quantity: parseInt(size.quantity),
                            })),
                    })),
                notes: [],
            };

            // Update order
            const result = await updateOrder(orderId, orderData);

            if (!result.success) {
                throw new Error(result.error || "Error al actualizar la orden");
            }

            // Success
            addToast({
                title: "Orden actualizada",
                description: "La orden ha sido actualizada exitosamente",
                color: "success",
            });

            // Redirect
            router.push("/dashboard/orders");
        } catch (error: any) {
            console.error("Error updating order:", error);
            addToast({
                title: "Error",
                description:
                    error.message || "Ocurrió un error al actualizar la orden",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    // Estados de carga y error
    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-400">
                        Cargando datos de la orden...
                    </p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-600 mb-4">Error: {error}</p>
                    <button
                        className="text-primary hover:underline"
                        onClick={() => router.push("/dashboard/orders")}
                    >
                        Volver a órdenes
                    </button>
                </div>
            </div>
        );
    }

    if (!orderData) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-600 mb-4">No se encontró la orden</p>
                    <button
                        className="text-primary hover:underline"
                        onClick={() => router.push("/dashboard/orders")}
                    >
                        Volver a órdenes
                    </button>
                </div>
            </div>
        );
    }

    return (
        <CrudFormTemplate
            allowStepNavigation
            showProgress
            backLabel="Volver a órdenes"
            backRoute="/dashboard/orders"
            breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Órdenes", href: "/dashboard/orders" },
                { label: "Editar" },
            ]}
            completedSteps={completedSteps}
            currentStep={currentStep}
            isSubmitting={isSubmitting}
            steps={wizardSteps}
            subtitle="Modifica los datos de la orden existente"
            title="Editar Orden"
            onStepChange={handleStepChange}
            onSubmit={handleSubmit}
        />
    );
}
