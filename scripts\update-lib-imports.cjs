// Script para actualizar imports de @/lib a nueva estructura
// Ejecutar con: node scripts/update-lib-imports.js

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Mapeo de imports antiguos a nuevos
const importMappings = {
  // Hooks
  '@/lib/hooks/useRevalidationListener': '@/shared/hooks/useRevalidationListener',
  '@/lib/hooks/useNotes': '@/features/notes/hooks/useNotes',
  '@/lib/hooks/useNoteBulkActions': '@/features/notes/hooks/useNoteBulkActions',
  '@/lib/hooks/useOrder': '@/features/orders/hooks/useOrder',
  '@/lib/hooks/useSize': '@/features/garments/hooks/useSize',
  '@/lib/hooks/useUser': '@/features/auth/hooks/useUser',
  '@/lib/hooks/useAssignments': '@/features/assignments/hooks/useAssignments',
  '@/lib/hooks/useRemission': '@/features/orders/hooks/useRemission',
  
  // Actions - Notes
  '@/lib/actions/notes/create': '@/features/notes/actions/create',
  '@/lib/actions/notes/update': '@/features/notes/actions/update',
  '@/lib/actions/notes/delete': '@/features/notes/actions/delete',
  '@/lib/actions/notes/list': '@/features/notes/actions/list',
  '@/lib/actions/notes/statuses': '@/features/notes/actions/statuses',
  '@/lib/actions/notes/importances': '@/features/notes/actions/importances',
  '@/lib/actions/notes/bulk-delete': '@/features/notes/actions/bulk-delete',
  '@/lib/actions/notes/bulk-update-status': '@/features/notes/actions/bulk-update-status',
  '@/lib/actions/notes/bulk-update-importance': '@/features/notes/actions/bulk-update-importance',
  '@/lib/actions/notes/modal-notes': '@/features/notes/actions/modal-notes',
  '@/lib/actions/notes': '@/features/notes/actions',
  
  // Actions - Assignments
  '@/lib/actions/assignments': '@/features/assignments/actions',
  
  // Actions - Others
  '@/lib/actions/sizes': '@/features/garments/actions/sizes',
  '@/lib/actions/remissions': '@/features/orders/actions/remissions',
  '@/lib/actions/contractors': '@/features/contractors/actions',
  '@/lib/actions/users': '@/features/auth/actions/users',
  '@/lib/actions/utils': '@/shared/utils/actions-utils',
  '@/lib/actions/dashboard': '@/features/dashboard/actions',
  
  // Utils
  '@/lib/utils': '@/shared/utils',
  '@/lib/client-utils': '@/shared/utils/client',
  '@/lib/server-utils': '@/shared/utils/server',
  '@/lib/auth-utils': '@/features/auth/utils',
  '@/lib/swr-provider': '@/shared/providers/swr-provider',
  '@/lib/cacheKeys': '@/shared/utils/cacheKeys',
  
  // Context
  '@/lib/context/notes-selection-context': '@/shared/context/notes-selection-context',
  
  // Services
  '@/lib/services/db/error-observer': '@/shared/services/db/error-observer',
  
  // Auth
  '@/lib/auth/auth-actions': '@/features/auth/actions/auth-actions',
  '@/lib/auth/nextauth-config': '@/features/auth/config/nextauth-config',
};

function updateImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  for (const [oldImport, newImport] of Object.entries(importMappings)) {
    const regex = new RegExp(`from ['"]${oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
    if (content.match(regex)) {
      content = content.replace(regex, `from '${newImport}'`);
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated: ${filePath}`);
  }
  
  return updated;
}

// Buscar todos los archivos .ts y .tsx
const files = glob.sync('**/*.{ts,tsx}', {
  ignore: ['node_modules/**', '.next/**', 'dist/**', 'scripts/**'],
  cwd: process.cwd()
});

console.log(`Found ${files.length} files to check...`);

let updatedCount = 0;
files.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (updateImports(filePath)) {
    updatedCount++;
  }
});

console.log(`\n✨ Updated ${updatedCount} files!`);