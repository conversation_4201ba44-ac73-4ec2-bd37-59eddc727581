"use client";

import React from "react";
import { Progress } from "@heroui/react";

interface NoteFormProgressProps {
    value: number;
    className?: string;
}

export function NoteFormProgress({
    value,
    className = "",
}: NoteFormProgressProps) {
    return (
        <div className={`w-full ${className}`}>
            <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
                <span>Progreso del formulario</span>
                <span>{value}%</span>
            </div>
            <Progress
                className="max-w-full"
                color={
                    value < 25 ? "danger" : value < 75 ? "warning" : "success"
                }
                size="sm"
                value={value}
            />
        </div>
    );
}
