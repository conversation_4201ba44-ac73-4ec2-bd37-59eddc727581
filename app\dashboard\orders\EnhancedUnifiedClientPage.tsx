"use client";

import React, { useState, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    CubeIcon,
    CheckCircleIcon,
    XCircleIcon,
    SparklesIcon,
    TruckIcon,
    CogIcon,
    ExclamationTriangleIcon,
    ChartBarIcon,
    PlusIcon,
    Squares2X2Icon,
    TableCellsIcon,
    CalendarIcon,
    ClipboardDocumentListIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

import {
    adjustTimezoneOffset,
    getDaysDifference,
} from "@/shared/utils/dateUtils";
import {
    Tabs,
    Tab,
    Button,
    ButtonGroup,
    Card,
    CardBody,
    Progress,
    Tooltip,
    Pagination,
} from "@/shared/components/ui/hero-ui-client";
import {
    DashboardLayout,
    DashboardFilters,
    DashboardTable,
} from "@/shared/components/dashboard";
import { Order } from "@/features/orders/types/orders";
import MultiSelectDropdown from "@/shared/components/ui/MultiSelectDropdown";
import { useOrdersData } from "@/features/orders/hooks/useOrdersData";
import { useOrdersState } from "@/features/orders/hooks/useOrdersState";
import { useContractorsForFilter } from "@/features/contractors/hooks/useContractors";
import OrderDetailModal from "@/features/orders/components/OrderDetailModal";
import { EnhancedOrderCardV2 } from "@/features/orders/components/EnhancedOrderCardV2";
import CompactAlerts from "@/features/orders/components/CompactAlerts";
// Temporalmente usar placeholders hasta que los componentes estén listos
import MetricsPlaceholder from "@/features/orders/components/MetricsPlaceholder";
import CalendarPlaceholder from "@/features/orders/components/CalendarPlaceholder";
import { Chip } from "@/shared/components/ui/hero-ui-client";
import { SortOption, sortData } from "@/shared/utils/sortHelpers";
import { calculateRiskScore } from "@/features/orders/utils/riskCalculator";
import RiskIndicator from "@/features/orders/components/RiskIndicator";
import { getStatusConfig } from "@/features/orders/utils/statusIcons";

// Extender temporalmente Order con campos adicionales
interface ExtendedOrder extends Order {
    deliveryDate?: string | Date;
    quantity?: number;
    model?: {
        id: string;
        name: string;
    };
    status: string;
}

interface EnhancedUnifiedOrdersClientPageProps {
    currentUserId?: string;
    userRole?: string;
}

// Helper functions para calcular asignaciones
const calculateTotalAssigned = (order: Order): number => {
    if (!order.assignments || order.assignments.length === 0) return 0;

    return order.assignments.reduce(
        (sum, assignment) => sum + (assignment.quantity || 0),
        0,
    );
};

const calculateTotalPieces = (order: Order): number => {
    // Opción 1: Si la orden tiene el total precalculado
    if ((order as any).totalQuantity) {
        return (order as any).totalQuantity;
    }

    // Opción 2: Sumar todas las cantidades de los garments
    if (order.garments && order.garments.length > 0) {
        const total = order.garments.reduce((sum, garment) => {
            // Sumar totalQuantity de cada size
            const garmentTotal =
                garment.sizes?.reduce((sizeSum, size) => {
                    return sizeSum + (size.totalQuantity || 0);
                }, 0) || 0;

            return sum + garmentTotal;
        }, 0);

        if (total > 0) return total;
    }

    // Opción 3: Si no hay garments o sizes, intentar con assignments como total esperado
    if (order.assignments && order.assignments.length > 0) {
        return order.assignments.reduce(
            (sum, assignment) => sum + (assignment.quantity || 0),
            0,
        );
    }

    return 0;
};

const calculateAssignmentPercentage = (order: Order): number => {
    const total = calculateTotalPieces(order);

    if (total === 0) return 0;
    const assigned = calculateTotalAssigned(order);

    return Math.round((assigned / total) * 100);
};

const getProgressColor = (
    percentage: number,
): "success" | "warning" | "danger" => {
    if (percentage === 100) return "success";
    if (percentage >= 50) return "warning";

    return "danger";
};

export default function EnhancedUnifiedOrdersClientPage({
    currentUserId,
    userRole,
}: EnhancedUnifiedOrdersClientPageProps) {
    const router = useRouter();
    const { state, actions: stateActions } = useOrdersState();
    const {
        data: { orders, orderStatuses },
        status: { isLoading },
        operations: { deleteOrder, fetchOrder, mutate },
    } = useOrdersData(state);

    // Removed debug log that might contribute to render loops

    // Hook para contratistas
    const { contractors, isLoading: loadingContractors } =
        useContractorsForFilter();

    // Estados locales
    const [selectedOrder, setSelectedOrder] = useState<any>(null);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
    const [isLoadingOrder, setIsLoadingOrder] = useState(false);
    const [activeTab, setActiveTab] = useState("orders");
    const [viewMode, setViewMode] = useState<"grid" | "list">("list");
    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [selectedContractors, setSelectedContractors] = useState<string[]>(
        [],
    );
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "createdAt-desc",
        label: "Más reciente",
        field: "createdAt",
        direction: "desc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 12;

    // Funciones de cálculo para estadísticas expandidas
    const getNewOrders = useCallback(
        (days: number) => {
            try {
                const cutoffDate = new Date();

                cutoffDate.setDate(cutoffDate.getDate() - days);

                return orders.filter(
                    (o: Order) => new Date(o.createdAt || 0) > cutoffDate,
                ).length;
            } catch (error) {
                return 0;
            }
        },
        [orders],
    );

    const getByStatus = useCallback(
        (status: string) => {
            return orders.filter((o: Order) => {
                if (typeof o.status === "string") {
                    return o.status === status;
                }

                return o.status?.id === status || o.status?.name === status;
            }).length;
        },
        [orders],
    );

    const getDelayedOrders = useCallback(() => {
        return orders.filter((order: Order) => {
            const deliveryDate =
                order.estimatedDeliveryDate ||
                (order as ExtendedOrder).deliveryDate;

            if (
                !deliveryDate ||
                order.status === "delivered" ||
                order.status === "cancelled"
            ) {
                return false;
            }
            const date = adjustTimezoneOffset(deliveryDate);

            if (!date) return false;
            const daysLeft = getDaysDifference(date);

            return daysLeft < 0;
        }).length;
    }, [orders]);

    const getComplianceRate = useCallback(() => {
        const deliveredOrders = orders.filter((o: Order) => {
            if (typeof o.status === "string") {
                return o.status === "delivered";
            }

            return o.status?.name === "delivered";
        });

        if (deliveredOrders.length === 0) return 100;

        const onTimeDeliveries = deliveredOrders.filter((order: Order) => {
            if (!order.deliveryDate) return false;

            // Aquí deberías comparar con la fecha real de entrega
            return true; // Placeholder
        }).length;

        return Math.round((onTimeDeliveries / deliveredOrders.length) * 100);
    }, [orders]);

    // Estadísticas expandidas (8 cards)
    const extendedStats = useMemo(
        () => [
            {
                title: "Total de Órdenes",
                value: orders.length,
                icon: <CubeIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Órdenes activas en el sistema",
            },
            {
                title: "Nuevas (7 días)",
                value: getNewOrders(7),
                icon: <SparklesIcon className="w-6 h-6" />,
                color: "secondary" as const,
                change: 15,
                changeLabel: "vs. semana anterior",
                changeType: "increase" as const,
            },
            {
                title: "En Proceso",
                value: getByStatus("in_progress"),
                icon: <CogIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Órdenes en producción",
            },
            {
                title: "En Producción",
                value: getByStatus("in_production"),
                icon: <TruckIcon className="w-6 h-6" />,
                color: "warning" as const,
                description: "En línea de producción",
            },
            {
                title: "Entregadas",
                value: getByStatus("delivered"),
                icon: <CheckCircleIcon className="w-6 h-6" />,
                color: "success" as const,
                change: 23,
                changeLabel: "este mes",
                changeType: "increase" as const,
            },
            {
                title: "Canceladas",
                value: getByStatus("cancelled"),
                icon: <XCircleIcon className="w-6 h-6" />,
                color: "danger" as const,
                change: -2,
                changeLabel: "vs. mes anterior",
                changeType: "decrease" as const,
            },
            {
                title: "Retrasadas",
                value: getDelayedOrders(),
                icon: <ExclamationTriangleIcon className="w-6 h-6" />,
                color: "danger" as const,
                description: "¡Requieren atención inmediata!",
            },
            {
                title: "Tasa de Cumplimiento",
                value: getComplianceRate(),
                icon: <ChartBarIcon className="w-6 h-6" />,
                color: "success" as const,
                description: "Entregas a tiempo",
                suffix: "%",
            },
        ],
        [
            orders,
            getNewOrders,
            getByStatus,
            getDelayedOrders,
            getComplianceRate,
        ],
    );

    // Columnas mejoradas de la tabla
    const columns = [
        {
            key: "order",
            label: "Orden",
            sortable: true,
            render: (order: Order) => {
                const deliveryDate =
                    order.estimatedDeliveryDate ||
                    (order as ExtendedOrder).deliveryDate;
                const date = deliveryDate
                    ? adjustTimezoneOffset(deliveryDate)
                    : null;
                const daysLeft = date ? getDaysDifference(date) : null;
                const isOverdue =
                    daysLeft !== null &&
                    daysLeft < 0 &&
                    order.status !== "delivered";
                const isUrgent =
                    daysLeft !== null &&
                    daysLeft <= 3 &&
                    order.status !== "delivered";

                return (
                    <div className="flex items-center gap-3">
                        <div className="flex-1">
                            <div className="flex items-center gap-2">
                                <ClipboardDocumentListIcon className="w-4 h-4 text-primary" />
                                <span className="font-bold text-sm">
                                    {order.cutOrder ||
                                        `ORD-${order.id.substring(0, 6)}`}
                                </span>
                                {(isOverdue || isUrgent) && (
                                    <Tooltip
                                        content={
                                            isOverdue
                                                ? "Orden retrasada"
                                                : "Urgente"
                                        }
                                    >
                                        <ExclamationTriangleIcon
                                            className={`w-4 h-4 ${isOverdue ? "text-danger" : "text-warning"}`}
                                        />
                                    </Tooltip>
                                )}
                            </div>
                            <span className="text-xs text-gray-500">
                                {order.customer?.name || "Sin cliente"}
                            </span>
                        </div>
                    </div>
                );
            },
        },
        {
            key: "contractors",
            label: "Contratistas",
            render: (order: Order) => {
                if (!order.assignments || order.assignments.length === 0) {
                    return (
                        <span className="text-gray-400 text-sm">
                            Sin asignar
                        </span>
                    );
                }

                const contractorsWithQuantity = order.assignments.map((a) => ({
                    name: a.contractor?.name || "Sin nombre",
                    quantity: a.quantity || 0,
                }));

                if (contractorsWithQuantity.length <= 2) {
                    return (
                        <div className="flex flex-wrap gap-1">
                            {contractorsWithQuantity.map((contractor, i) => (
                                <Chip
                                    key={i}
                                    className="text-xs"
                                    size="sm"
                                    variant="flat"
                                >
                                    {contractor.name} ({contractor.quantity})
                                </Chip>
                            ))}
                        </div>
                    );
                }

                // Si hay más de 2, mostrar los primeros 2 con tooltip
                const totalQuantity = contractorsWithQuantity.reduce(
                    (sum, c) => sum + c.quantity,
                    0,
                );

                return (
                    <Tooltip
                        content={
                            <div className="space-y-1">
                                <p className="font-medium mb-2">
                                    Todos los contratistas:
                                </p>
                                {contractorsWithQuantity.map(
                                    (contractor, i) => (
                                        <div
                                            key={i}
                                            className="flex justify-between gap-4"
                                        >
                                            <span>{contractor.name}</span>
                                            <span className="font-mono text-sm">
                                                {contractor.quantity} pzs
                                            </span>
                                        </div>
                                    ),
                                )}
                                <div className="pt-2 mt-2 border-t border-gray-200">
                                    <div className="flex justify-between gap-4 font-medium">
                                        <span>Total:</span>
                                        <span className="font-mono">
                                            {totalQuantity} pzs
                                        </span>
                                    </div>
                                </div>
                            </div>
                        }
                    >
                        <div className="flex flex-wrap gap-1 cursor-help">
                            {contractorsWithQuantity
                                .slice(0, 2)
                                .map((contractor, i) => (
                                    <Chip
                                        key={i}
                                        className="text-xs"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {contractor.name} ({contractor.quantity}
                                        )
                                    </Chip>
                                ))}
                            <Chip
                                className="text-xs"
                                color="default"
                                size="sm"
                                variant="flat"
                            >
                                +{contractorsWithQuantity.length - 2}
                            </Chip>
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            key: "assignment",
            label: "Asignación",
            render: (order: Order) => {
                const totalPieces = calculateTotalPieces(order);
                const totalAssigned = calculateTotalAssigned(order);
                const percentage = calculateAssignmentPercentage(order);
                const color = getProgressColor(percentage);

                return (
                    <div className="space-y-1 min-w-[100px]">
                        <Progress
                            className="max-w-md"
                            color={color}
                            size="sm"
                            value={percentage}
                        />
                        <div className="flex justify-between text-xs">
                            <span className="text-gray-600">
                                {totalAssigned}/{totalPieces} pzs
                            </span>
                            <span
                                className={`font-medium ${
                                    color === "success"
                                        ? "text-success"
                                        : color === "warning"
                                          ? "text-warning"
                                          : "text-danger"
                                }`}
                            >
                                {percentage}%
                            </span>
                        </div>
                    </div>
                );
            },
        },
        {
            key: "product",
            label: "Producto",
            render: (order: Order) => {
                if (!order.garments || order.garments.length === 0) {
                    return (
                        <div className="flex flex-col">
                            <span className="text-gray-400 text-sm">
                                Sin modelo
                            </span>
                        </div>
                    );
                }

                const firstGarment = order.garments[0];
                const modelCode = firstGarment?.model?.code || "Sin código";
                const colorName =
                    firstGarment?.color?.name || "Color no disponible";

                return (
                    <div className="flex flex-col">
                        <span className="font-medium text-sm">{modelCode}</span>
                        <span className="text-xs text-gray-500">
                            {colorName}
                        </span>
                    </div>
                );
            },
        },
        {
            key: "parts",
            label: "Partidas",
            render: (order: Order) => {
                const partCodes =
                    order.parts?.map((p) => p.code).filter(Boolean) || [];

                if (partCodes.length === 0)
                    return <span className="text-gray-400">-</span>;

                // Si hay muchas partidas, mostrar con tooltip
                if (partCodes.length > 3) {
                    return (
                        <Tooltip
                            content={
                                <div className="max-w-xs">
                                    <p className="font-medium mb-1">
                                        Todas las partidas:
                                    </p>
                                    <div className="flex flex-wrap gap-1">
                                        {partCodes.map((code, i) => (
                                            <span
                                                key={i}
                                                className="bg-gray-700 px-1.5 py-0.5 rounded text-xs"
                                            >
                                                {code}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            }
                        >
                            <div className="cursor-help">
                                <div className="flex flex-wrap gap-1">
                                    {partCodes.slice(0, 3).map((code, i) => (
                                        <span
                                            key={i}
                                            className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono"
                                        >
                                            {code}
                                        </span>
                                    ))}
                                    <span className="text-xs text-gray-500">
                                        +{partCodes.length - 3}
                                    </span>
                                </div>
                            </div>
                        </Tooltip>
                    );
                }

                return (
                    <div className="flex flex-wrap gap-1">
                        {partCodes.map((code, i) => (
                            <span
                                key={i}
                                className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono"
                            >
                                {code}
                            </span>
                        ))}
                    </div>
                );
            },
        },
        {
            key: "quantity",
            label: "Cantidad",
            sortable: true,
            render: (order: Order) => {
                const quantity =
                    order._count?.garments ||
                    order.garments?.length ||
                    order._count?.parts ||
                    0;

                return (
                    <div className="flex flex-col">
                        <span className="font-semibold text-lg">
                            {quantity}
                        </span>
                        <span className="text-xs text-gray-500">piezas</span>
                    </div>
                );
            },
        },
        {
            key: "deliveryDate",
            label: "Entrega",
            sortable: true,
            render: (order: Order) => {
                const deliveryDate =
                    order.estimatedDeliveryDate ||
                    (order as ExtendedOrder).deliveryDate;

                if (!deliveryDate)
                    return (
                        <span className="text-gray-400 text-sm">Sin fecha</span>
                    );

                const date = adjustTimezoneOffset(deliveryDate);

                if (!date)
                    return (
                        <span className="text-gray-400 text-sm">
                            Fecha inválida
                        </span>
                    );

                const daysLeft = getDaysDifference(date);
                const isOverdue = daysLeft < 0 && order.status !== "delivered";
                const isUrgent = daysLeft <= 3 && order.status !== "delivered";

                return (
                    <div className="flex items-center gap-2">
                        <CalendarIcon
                            className={`w-4 h-4 ${isOverdue ? "text-danger" : isUrgent ? "text-warning" : "text-gray-500"}`}
                        />
                        <div>
                            <span
                                className={`text-sm font-medium ${isOverdue ? "text-danger" : ""}`}
                            >
                                {format(date, "dd MMM", { locale: es })}
                            </span>
                            {order.status !== "delivered" &&
                                order.status !== "cancelled" && (
                                    <div
                                        className={`text-xs ${isOverdue ? "text-danger font-bold" : isUrgent ? "text-warning" : "text-gray-500"}`}
                                    >
                                        {isOverdue
                                            ? `${Math.abs(daysLeft)}d retraso`
                                            : daysLeft === 0
                                              ? "Hoy"
                                              : daysLeft === 1
                                                ? "Mañana"
                                                : `${daysLeft}d`}
                                    </div>
                                )}
                        </div>
                    </div>
                );
            },
        },
        {
            key: "progress",
            label: "Progreso",
            render: (order: Order) => {
                const progress =
                    order.status === "completed" || order.status === "delivered"
                        ? 100
                        : order.status === "in_production"
                          ? 75
                          : order.status === "in_progress"
                            ? 50
                            : order.status === "pending"
                              ? 25
                              : 0;

                return (
                    <div className="w-full">
                        <Progress
                            className="max-w-md"
                            color={
                                progress === 100
                                    ? "success"
                                    : progress >= 50
                                      ? "warning"
                                      : "primary"
                            }
                            size="sm"
                            value={progress}
                        />
                        <span className="text-xs text-gray-500 mt-1">
                            {progress}%
                        </span>
                    </div>
                );
            },
        },
        {
            key: "risk",
            label: "Riesgo",
            sortable: true,
            render: (order: Order) => {
                const riskAssessment = calculateRiskScore(order);

                return (
                    <RiskIndicator
                        assessment={riskAssessment}
                        showDetails={false}
                        size="sm"
                    />
                );
            },
        },
        {
            key: "status",
            label: "Estado",
            render: (order: Order) => {
                const config = getStatusConfig(order.status);
                const statusColors: Record<string, any> = {
                    pending: "warning",
                    in_progress: "primary",
                    in_production: "secondary",
                    delivered: "success",
                    cancelled: "danger",
                };

                const statusKey =
                    typeof order.status === "string"
                        ? order.status
                        : order.status?.name || order.status?.id || "";

                return (
                    <Chip
                        color={statusColors[statusKey] || "default"}
                        size="sm"
                        startContent={
                            config.icon ? (
                                <config.icon className="w-4 h-4" />
                            ) : undefined
                        }
                        variant="flat"
                    >
                        {config.label}
                    </Chip>
                );
            },
        },
    ];

    // Acciones de la tabla
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (order: Order) => handleOpenDetail(order),
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (order: Order) => {
                router.push(`/dashboard/orders/${order.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (order: Order) => {
                if (confirm("¿Estás seguro de eliminar esta orden?")) {
                    await deleteOrder(order.id);
                }
            },
            color: "danger" as const,
            isDisabled: (order: Order) => userRole !== "admin",
        },
    ];

    // Filtros mejorados
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "pending", label: "Pendiente" },
                { value: "in_progress", label: "En Proceso" },
                { value: "in_production", label: "En Producción" },
                { value: "delivered", label: "Entregada" },
                { value: "cancelled", label: "Cancelada" },
            ],
        },
        {
            key: "period",
            label: "Período",
            type: "select" as const,
            placeholder: "Seleccionar período",
            options: [
                { value: "all", label: "Todo el tiempo" },
                { value: "7d", label: "Últimos 7 días" },
                { value: "30d", label: "Últimos 30 días" },
                { value: "90d", label: "Últimos 90 días" },
            ],
        },
        {
            key: "urgency",
            label: "Urgencia",
            type: "select" as const,
            placeholder: "Filtrar por urgencia",
            options: [
                { value: "all", label: "Todas" },
                { value: "overdue", label: "Retrasadas" },
                { value: "today", label: "Vencen hoy" },
                { value: "week", label: "Vencen esta semana" },
            ],
        },
    ];

    // Opciones de ordenamiento mejoradas
    const sortOptions = [
        {
            key: "cutOrder-asc",
            label: "Número de Corte A-Z",
            field: "cutOrder",
            direction: "asc" as const,
        },
        {
            key: "cutOrder-desc",
            label: "Número de Corte Z-A",
            field: "cutOrder",
            direction: "desc" as const,
        },
        {
            key: "deliveryDate-asc",
            label: "Entrega más próxima",
            field: "deliveryDate",
            direction: "asc" as const,
        },
        {
            key: "deliveryDate-desc",
            label: "Entrega más lejana",
            field: "deliveryDate",
            direction: "desc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antigua",
            field: "createdAt",
            direction: "asc" as const,
        },
        {
            key: "quantity-desc",
            label: "Mayor cantidad",
            field: "quantity",
            direction: "desc" as const,
        },
        {
            key: "quantity-asc",
            label: "Menor cantidad",
            field: "quantity",
            direction: "asc" as const,
        },
    ];

    // Filtrar y ordenar datos
    const filteredData = useMemo(() => {
        let filtered = [...orders];

        // Búsqueda
        if (searchValue) {
            const search = searchValue.toLowerCase();

            filtered = filtered.filter(
                (order) =>
                    order.cutOrder?.toLowerCase().includes(search) ||
                    order.customer?.name?.toLowerCase().includes(search) ||
                    order.id.toLowerCase().includes(search),
            );
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            filtered = filtered.filter((o: Order) => {
                if (typeof o.status === "string") {
                    return o.status === filterValues.status;
                }

                return (
                    o.status?.id === filterValues.status ||
                    o.status?.name === filterValues.status
                );
            });
        }

        // Filtro por contratista(s)
        if (selectedContractors.length > 0) {
            filtered = filtered.filter((order: Order) =>
                order.assignments?.some((assignment) =>
                    selectedContractors.includes(assignment.contractorId),
                ),
            );
        }

        // Filtro por período
        if (filterValues.period && filterValues.period !== "all") {
            const days = parseInt(filterValues.period);
            const cutoffDate = new Date();

            cutoffDate.setDate(cutoffDate.getDate() - days);
            filtered = filtered.filter(
                (o: Order) => new Date(o.createdAt || 0) >= cutoffDate,
            );
        }

        // Filtro por urgencia
        if (filterValues.urgency && filterValues.urgency !== "all") {
            filtered = filtered.filter((order: Order) => {
                const deliveryDate =
                    order.estimatedDeliveryDate ||
                    (order as ExtendedOrder).deliveryDate;

                if (
                    !deliveryDate ||
                    order.status === "delivered" ||
                    order.status === "cancelled"
                ) {
                    return false;
                }
                const date = adjustTimezoneOffset(deliveryDate);

                if (!date) return false;
                const daysLeft = getDaysDifference(date);

                switch (filterValues.urgency) {
                    case "overdue":
                        return daysLeft < 0;
                    case "today":
                        return daysLeft === 0;
                    case "week":
                        return daysLeft >= 0 && daysLeft <= 7;
                    default:
                        return true;
                }
            });
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [orders, searchValue, filterValues, selectedContractors, currentSort]);

    // Paginación
    const paginatedData = useMemo(() => {
        const startIndex = (page - 1) * rowsPerPage;

        return filteredData.slice(startIndex, startIndex + rowsPerPage);
    }, [filteredData, page, rowsPerPage]);

    const totalPages = Math.ceil(filteredData.length / rowsPerPage);

    // Handler para abrir detalles
    const handleOpenDetail = useCallback(
        async (order: Order) => {
            // Abrir modal inmediatamente con datos básicos
            setSelectedOrder(order);
            setIsDetailModalOpen(true);

            // Cargar detalles completos en segundo plano
            setIsLoadingOrder(true);
            try {
                const fullOrder = await fetchOrder(order.id, false);

                if (fullOrder) {
                    setSelectedOrder(fullOrder);
                }
            } catch (error) {
                // El modal ya está abierto, solo mostramos el error
            } finally {
                setIsLoadingOrder(false);
            }
        },
        [fetchOrder],
    );

    // Renderizar contenido según la pestaña activa
    const renderTabContent = () => {
        switch (activeTab) {
            case "orders":
                return (
                    <div className="space-y-4">
                        {/* Filtros y búsqueda - se muestran para ambas vistas */}
                        <DashboardFilters
                            activeFiltersCount={
                                Object.keys(filterValues).filter(
                                    (key) =>
                                        filterValues[key] &&
                                        filterValues[key] !== "all",
                                ).length
                            }
                            currentSort={currentSort}
                            filterValues={filterValues}
                            filters={filters}
                            searchValue={searchValue}
                            sortOptions={sortOptions}
                            onClearFilters={() => {
                                setSearchValue("");
                                setFilterValues({});
                                setPage(1);
                            }}
                            onFilterChange={(key, value) => {
                                setFilterValues((prev) => ({
                                    ...prev,
                                    [key]: value,
                                }));
                                setPage(1); // Reset a primera página al filtrar

                                // Sincronizar con estado global para filtros del servidor
                                if (key === "status") {
                                    stateActions.setStatus(
                                        value === "all" ? "" : value,
                                    );
                                }
                            }}
                            onSearchChange={(value) => {
                                setSearchValue(value);
                                stateActions.setSearch(value);
                            }}
                            onSortChange={(sort) => {
                                setCurrentSort(sort);
                                setPage(1); // Reset a primera página al ordenar
                            }}
                        >
                            {/* Toggle de vista dentro de DashboardFilters */}
                            <ButtonGroup size="sm">
                                <Button
                                    isIconOnly
                                    variant={
                                        viewMode === "list"
                                            ? "solid"
                                            : "bordered"
                                    }
                                    onPress={() => setViewMode("list")}
                                >
                                    <TableCellsIcon className="w-4 h-4" />
                                </Button>
                                <Button
                                    isIconOnly
                                    variant={
                                        viewMode === "grid"
                                            ? "solid"
                                            : "bordered"
                                    }
                                    onPress={() => setViewMode("grid")}
                                >
                                    <Squares2X2Icon className="w-4 h-4" />
                                </Button>
                            </ButtonGroup>
                        </DashboardFilters>

                        {/* Filtro de contratistas multiselect */}
                        <div className="flex items-center gap-4">
                            <MultiSelectDropdown
                                isLoading={loadingContractors}
                                label="Contratistas"
                                options={contractors.map((c) => ({
                                    value: c.id,
                                    label: c.name,
                                }))}
                                placeholder={
                                    loadingContractors
                                        ? "Cargando..."
                                        : "Todos los contratistas"
                                }
                                value={selectedContractors}
                                onChange={(value) => {
                                    setSelectedContractors(value);
                                    setPage(1);
                                }}
                            />
                        </div>

                        {/* Vista de tabla o grid */}
                        {viewMode === "list" ? (
                            <DashboardTable
                                actions={actions as any}
                                columns={columns as any}
                                data={paginatedData}
                                emptyContent="No hay órdenes registradas"
                                isLoading={isLoading}
                            />
                        ) : (
                            <div
                                className={
                                    viewMode === "grid"
                                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                                        : "space-y-4"
                                }
                            >
                                {paginatedData.map((order) => (
                                    <EnhancedOrderCardV2
                                        key={order.id}
                                        order={order}
                                        viewMode={viewMode}
                                        onView={handleOpenDetail}
                                    />
                                ))}
                            </div>
                        )}

                        {/* Paginación para ambas vistas */}
                        {totalPages > 1 && (
                            <div className="flex justify-center mt-6">
                                <Pagination
                                    showControls
                                    classNames={{
                                        wrapper: "gap-0 overflow-visible h-8",
                                        item: "w-8 h-8 text-small rounded-none first:rounded-s-medium last:rounded-e-medium",
                                        cursor: "bg-gradient-to-br from-primary to-primary-600 text-white font-bold",
                                    }}
                                    page={page}
                                    total={totalPages}
                                    onChange={(newPage) => {
                                        setPage(newPage);
                                        window.scrollTo({
                                            top: 0,
                                            behavior: "smooth",
                                        });
                                    }}
                                />
                            </div>
                        )}
                    </div>
                );

            case "metrics":
                return <MetricsPlaceholder orders={orders} />;

            case "calendar":
                return (
                    <CalendarPlaceholder
                        orders={orders}
                        onSelectOrder={handleOpenDetail}
                    />
                );

            default:
                return null;
        }
    };

    return (
        <>
            <DashboardLayout
                actions={
                    <div className="flex items-center gap-3">
                        <CompactAlerts
                            orders={orders}
                            onViewOrder={handleOpenDetail}
                        />
                        <Button
                            color="primary"
                            startContent={<PlusIcon className="w-5 h-5" />}
                            onPress={() => router.push("/dashboard/orders/new")}
                        >
                            Nueva Orden
                        </Button>
                    </div>
                }
                breadcrumbs={[{ label: "Órdenes" }]}
                subtitle="Gestión integral de órdenes de producción"
                title="Centro de Comando de Órdenes"
            >
                {/* Estadísticas mejoradas */}
                <div className="mb-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
                        {extendedStats.map((stat, index) => (
                            <motion.div
                                key={index}
                                animate={{ opacity: 1, y: 0 }}
                                initial={{ opacity: 0, y: 20 }}
                                transition={{ delay: index * 0.05 }}
                            >
                                <Card className="hover:shadow-lg transition-shadow">
                                    <CardBody className="p-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <div
                                                className={`p-2 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}
                                            >
                                                {stat.icon}
                                            </div>
                                            {stat.change !== undefined && (
                                                <div
                                                    className={`text-xs ${stat.changeType === "increase" ? "text-success" : "text-danger"}`}
                                                >
                                                    {stat.change > 0 ? "+" : ""}
                                                    {stat.change}%
                                                </div>
                                            )}
                                        </div>
                                        <p className="text-2xl font-bold">
                                            {stat.value}
                                            {stat.suffix || ""}
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                            {stat.title}
                                        </p>
                                        {stat.description && (
                                            <p className="text-xs text-gray-400 mt-0.5">
                                                {stat.description}
                                            </p>
                                        )}
                                    </CardBody>
                                </Card>
                            </motion.div>
                        ))}
                    </div>
                </div>

                {/* Sistema de tabs */}
                <Card className="shadow-sm">
                    <CardBody className="p-0">
                        <Tabs
                            classNames={{
                                tabList:
                                    "gap-6 w-full relative rounded-none p-0 border-b border-divider px-6",
                                cursor: "w-full bg-primary/10 dark:bg-primary/20",
                                tab: "max-w-fit px-4 h-12 data-[selected=true]:text-primary",
                                tabContent:
                                    "group-data-[selected=true]:text-primary group-data-[selected=true]:font-semibold",
                            }}
                            selectedKey={activeTab}
                            onSelectionChange={(key) => {
                                const newTab = key as string;

                                if (newTab !== activeTab) {
                                    setActiveTab(newTab);
                                }
                            }}
                        >
                            <Tab
                                key="orders"
                                title={
                                    <div className="flex items-center space-x-2">
                                        <CubeIcon className="w-4 h-4" />
                                        <span>Órdenes</span>
                                    </div>
                                }
                            />
                            <Tab
                                key="metrics"
                                title={
                                    <div className="flex items-center space-x-2">
                                        <ChartBarIcon className="w-4 h-4" />
                                        <span>Métricas</span>
                                    </div>
                                }
                            />
                            <Tab
                                key="calendar"
                                title={
                                    <div className="flex items-center space-x-2">
                                        <CalendarIcon className="w-4 h-4" />
                                        <span>Calendario</span>
                                    </div>
                                }
                            />
                        </Tabs>

                        <div className="p-6">{renderTabContent()}</div>
                    </CardBody>
                </Card>
            </DashboardLayout>

            {/* Modal de detalles */}
            <OrderDetailModal
                currentUserId={currentUserId}
                isLoadingOrder={isLoadingOrder}
                isOpen={isDetailModalOpen && selectedOrder !== null}
                order={selectedOrder}
                userRole={userRole}
                onClose={() => {
                    setIsDetailModalOpen(false);
                    // Pequeño delay antes de limpiar selectedOrder para evitar conflictos
                    setTimeout(() => setSelectedOrder(null), 300);
                }}
                onDelete={async () => {
                    if (selectedOrder) {
                        await deleteOrder(selectedOrder.id);
                        setIsDetailModalOpen(false);
                        setTimeout(() => setSelectedOrder(null), 300);
                    }
                }}
            />
        </>
    );
}
