"use server";

import { db } from "@/shared/lib/db";

interface GetLikesResult {
    success: boolean;
    count?: number;
    isLiked?: boolean;
    error?: string;
}

/**
 * Obtiene el conteo de likes de una nota y si el usuario actual la ha likeado
 */
export async function getNoteLikes(
    noteId: string,
    userId?: string | null,
): Promise<GetLikesResult> {
    try {
        // Contar el total de likes
        const count = await db.noteLike.count({
            where: { noteId },
        });

        let isLiked = false;

        // Si hay un userId, verificar si ha dado like
        if (userId) {
            const userLike = await db.noteLike.findUnique({
                where: {
                    noteId_userId: {
                        noteId,
                        userId,
                    },
                },
            });

            isLiked = !!userLike;
        }

        return {
            success: true,
            count,
            isLiked,
        };
    } catch (error) {
        return {
            success: false,
            error: "Error al obtener los likes",
        };
    }
}
