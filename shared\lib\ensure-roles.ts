// Utilidad para asegurar que los roles existan en la base de datos
import { ROLE_CONFIG, ROLE_NAMES } from "@/shared/types/roles";

import { prisma } from "./prisma";

export async function ensureRolesExist() {
    // REMOVED: console.log('🔍 Verificando roles en la base de datos...');

    try {
        // Verificar si existen roles
        const existingRolesCount = await prisma.role.count();
        // REMOVED: console.log(`📊 Roles existentes: ${existingRolesCount}`);

        if (existingRolesCount === 0) {
            // REMOVED: console.log('⚠️ No se encontraron roles, creando roles por defecto...');

            // Crear todos los roles usando la configuración centralizada
            const rolesToCreate = ROLE_NAMES.map((name) => ({
                name,
                ...ROLE_CONFIG[name],
            }));

            const createdRoles = await prisma.$transaction(
                rolesToCreate.map((role) =>
                    prisma.role.create({
                        data: role,
                    }),
                ),
            );

            // REMOVED: console.log('✅ Roles creados exitosamente:', createdRoles.map(r => r.name).join(', '));
        } else {
            // Mostrar los roles existentes
            const existingRoles = await prisma.role.findMany({
                select: { name: true },
            });
            // REMOVED: console.log('✅ Roles ya existen:', existingRoles.map(r => r.name).join(', '));
        }
    } catch (error) {
        // REMOVED: console.error('❌ Error al verificar/crear roles:', error);
        // No lanzar el error para no bloquear la aplicación
        // pero sí registrarlo para debugging
    }
}
