"use client";

import React, { createContext, useContext, useReducer } from "react";

import { QuantityAssignment } from "@/features/assignments/types";

// Tipos de pasos del asistente
export type WizardStep = "contractor" | "orders" | "quantities" | "summary";

// Estado del wizard
interface WizardState {
    currentStep: WizardStep;
    contractor: string | null;
    contractorData: WizardContractor | null;
    selectedOrders: string[];
    quantities: Record<string, number>;
    assignments: QuantityAssignment[];
    isSubmitting: boolean;
    validationErrors: Record<string, string>;
    // Historial para función deshacer
    history: WizardState[];
    historyIndex: number;
    canUndo: boolean;
    canRedo: boolean;
    // Estado de borrador
    isDraft: boolean;
    draftId: string | null;
    lastSavedAt: Date | null;
}

// Acciones para el reducer
type WizardAction =
    | { type: "SET_CURRENT_STEP"; payload: WizardStep }
    | { type: "SET_CONTRACTOR"; payload: string | null }
    | { type: "SET_CONTRACTOR_DATA"; payload: Wizard<PERSON><PERSON>ractor | null }
    | { type: "SET_SELECTED_ORDERS"; payload: string[] }
    | { type: "SET_QUANTITIES"; payload: Record<string, number> }
    | { type: "SET_ASSIGNMENTS"; payload: QuantityAssignment[] }
    | { type: "ADD_ASSIGNMENT"; payload: QuantityAssignment }
    | { type: "UPDATE_ASSIGNMENT"; payload: QuantityAssignment }
    | { type: "REMOVE_ASSIGNMENT"; payload: string }
    | { type: "SET_IS_SUBMITTING"; payload: boolean }
    | {
          type: "SET_VALIDATION_ERROR";
          payload: { id: string; error: string | null };
      }
    | { type: "RESET_WIZARD" }
    | { type: "SAVE_TO_HISTORY" }
    | { type: "UNDO" }
    | { type: "REDO" }
    | { type: "SAVE_DRAFT"; payload: { draftId: string } }
    | { type: "LOAD_DRAFT"; payload: WizardState }
    | { type: "CLEAR_DRAFT" };

// Estado inicial
const initialState: WizardState = {
    currentStep: "contractor",
    contractor: null,
    contractorData: null,
    selectedOrders: [],
    quantities: {},
    assignments: [],
    isSubmitting: false,
    validationErrors: {},
    history: [],
    historyIndex: -1,
    canUndo: false,
    canRedo: false,
    isDraft: false,
    draftId: null,
    lastSavedAt: null,
};

// Reducer para manejar las acciones
function wizardReducer(state: WizardState, action: WizardAction): WizardState {
    switch (action.type) {
        case "SET_CURRENT_STEP":
            return { ...state, currentStep: action.payload };
        case "SET_CONTRACTOR":
            return { ...state, contractor: action.payload };
        case "SET_CONTRACTOR_DATA":
            return { ...state, contractorData: action.payload };
        case "SET_SELECTED_ORDERS":
            return { ...state, selectedOrders: action.payload };
        case "SET_QUANTITIES":
            return { ...state, quantities: action.payload };
        case "SET_ASSIGNMENTS":
            return { ...state, assignments: action.payload };
        case "ADD_ASSIGNMENT": {
            // Verificar si ya existe
            const exists = state.assignments.some(
                (a) => a.garmentSizeId === action.payload.garmentSizeId,
            );

            if (exists) return state;

            return {
                ...state,
                assignments: [...state.assignments, action.payload],
            };
        }
        case "UPDATE_ASSIGNMENT": {
            const index = state.assignments.findIndex(
                (a) => a.garmentSizeId === action.payload.garmentSizeId,
            );

            if (index === -1) {
                return {
                    ...state,
                    assignments: [...state.assignments, action.payload],
                };
            }
            const newAssignments = [...state.assignments];

            newAssignments[index] = action.payload;

            // Guardar en historial solo para cambios de cantidad
            const shouldSaveHistory =
                state.assignments[index]?.quantity !== action.payload.quantity;

            if (shouldSaveHistory) {
                const historyEntry = {
                    ...state,
                    history: state.history.slice(0, state.historyIndex + 1),
                    historyIndex: state.historyIndex,
                    canUndo: state.canUndo,
                    canRedo: state.canRedo,
                };

                const newHistory = [
                    ...state.history.slice(0, state.historyIndex + 1),
                    historyEntry,
                ];
                const maxHistory = 10; // Límite de 10 cambios

                return {
                    ...state,
                    assignments: newAssignments,
                    history: newHistory.slice(-maxHistory),
                    historyIndex: Math.min(
                        state.historyIndex + 1,
                        maxHistory - 1,
                    ),
                    canUndo: true,
                    canRedo: false,
                };
            }

            return { ...state, assignments: newAssignments };
        }
        case "REMOVE_ASSIGNMENT": {
            const newAssignments = state.assignments.filter(
                (a) => a.garmentSizeId !== action.payload,
            );
            // También eliminar error si existe
            const newErrors = { ...state.validationErrors };

            delete newErrors[action.payload];

            return {
                ...state,
                assignments: newAssignments,
                validationErrors: newErrors,
            };
        }
        case "SET_IS_SUBMITTING":
            return { ...state, isSubmitting: action.payload };
        case "SET_VALIDATION_ERROR": {
            const newErrors = { ...state.validationErrors };

            if (action.payload.error === null) {
                // Only update if there's an error to delete
                if (newErrors[action.payload.id]) {
                    delete newErrors[action.payload.id];

                    return { ...state, validationErrors: newErrors };
                }
            } else {
                // Only update if the error message changed
                if (newErrors[action.payload.id] !== action.payload.error) {
                    newErrors[action.payload.id] = action.payload.error;

                    return { ...state, validationErrors: newErrors };
                }
            }

            // If no changes needed, return the same state reference
            return state;
        }
        case "RESET_WIZARD":
            return initialState;
        case "UNDO": {
            if (!state.canUndo || state.historyIndex < 0) return state;

            const previousState = state.history[state.historyIndex];

            return {
                ...previousState,
                history: state.history,
                historyIndex: state.historyIndex - 1,
                canUndo: state.historyIndex > 0,
                canRedo: true,
            };
        }
        case "REDO": {
            if (
                !state.canRedo ||
                state.historyIndex >= state.history.length - 2
            )
                return state;

            const nextIndex = state.historyIndex + 2;
            const nextState = state.history[nextIndex];

            return {
                ...nextState,
                history: state.history,
                historyIndex: nextIndex - 1,
                canUndo: true,
                canRedo: nextIndex < state.history.length - 1,
            };
        }
        case "SAVE_DRAFT": {
            return {
                ...state,
                isDraft: true,
                draftId: action.payload.draftId,
                lastSavedAt: new Date(),
            };
        }
        case "LOAD_DRAFT": {
            return {
                ...action.payload,
                history: [],
                historyIndex: -1,
                canUndo: false,
                canRedo: false,
            };
        }
        case "CLEAR_DRAFT": {
            return {
                ...state,
                isDraft: false,
                draftId: null,
                lastSavedAt: null,
            };
        }
        default:
            return state;
    }
}

// Incluir type para contratista en el context
export interface WizardContractor {
    id: string;
    name: string;
    email?: string;
    phone?: string;
}

// Asegurar que el tipo WizardContextValue incluya:
export type WizardContextValue = {
    state: WizardState;
    dispatch: React.Dispatch<WizardAction>;
    nextStep: () => void;
    prevStep: () => void;
    canGoNext: () => boolean;
    canGoBack: () => boolean;
    selectedOrderIds: string[];
    assignments: QuantityAssignment[];
    addAssignment: (assignment: QuantityAssignment) => void;
    updateAssignment: (
        garmentSizeId: string,
        data: Partial<QuantityAssignment>,
    ) => void;
    removeAssignment: (assignmentId: string) => void;
    validationErrors: Record<string, string>;
    setValidationError: (id: string, error: string | null) => void;
    activeContractor: WizardContractor | null;
    undo: () => void;
    redo: () => void;
    canUndo: boolean;
    canRedo: boolean;
    saveDraft: () => string;
    loadDraft: (draftId: string) => boolean;
    clearDraft: () => void;
};

// Crear el contexto
const WizardContext = createContext<WizardContextValue | undefined>(undefined);

// Proveedor del contexto
export function WizardProvider({ children }: { children: React.ReactNode }) {
    const [state, dispatch] = useReducer(wizardReducer, initialState);

    // Orden de los pasos
    const steps: WizardStep[] = [
        "contractor",
        "orders",
        "quantities",
        "summary",
    ];

    // Avanzar al siguiente paso
    const nextStep = () => {
        const currentIndex = steps.indexOf(state.currentStep);

        if (currentIndex < steps.length - 1) {
            dispatch({
                type: "SET_CURRENT_STEP",
                payload: steps[currentIndex + 1],
            });
        }
    };

    // Retroceder al paso anterior
    const prevStep = () => {
        const currentIndex = steps.indexOf(state.currentStep);

        if (currentIndex > 0) {
            dispatch({
                type: "SET_CURRENT_STEP",
                payload: steps[currentIndex - 1],
            });
        }
    };

    // Ir a un paso específico (commented out - not used)
    // const goToStep = (step: WizardStep) => {
    //     dispatch({ type: "SET_CURRENT_STEP", payload: step });
    // };

    // Validación de pasos
    const canGoNext = () => {
        switch (state.currentStep) {
            case "contractor":
                return !!state.contractor;
            case "orders":
                return state.selectedOrders.length > 0;
            case "quantities":
                return (
                    state.assignments.length > 0 &&
                    Object.keys(state.validationErrors).length === 0
                );
            case "summary":
                return true;
            default:
                return false;
        }
    };

    const canGoBack = () => {
        return steps.indexOf(state.currentStep) > 0;
    };

    // const isStepCompleted = (step: WizardStep) => {
    //     switch (step) {
    //         case "contractor":
    //             return !!state.contractor;
    //         case "orders":
    //             return state.selectedOrders.length > 0;
    //         case "quantities":
    //             return (
    //                 state.assignments.length > 0 &&
    //                 Object.keys(state.validationErrors).length === 0
    //             );
    //         case "summary":
    //             return false;
    //         default:
    //             return false;
    //     }
    // };

    // Agregar implementaciones para las funciones y valores faltantes
    const addAssignment = (assignment: QuantityAssignment) => {
        dispatch({
            type: "ADD_ASSIGNMENT",
            payload: assignment,
        });
    };

    const updateAssignment = (
        garmentSizeId: string,
        data: Partial<QuantityAssignment>,
    ) => {
        // Encontrar la asignación existente
        const existing = state.assignments.find(
            (a) => a.garmentSizeId === garmentSizeId,
        );

        if (existing) {
            // Si existe, actualizarla
            dispatch({
                type: "UPDATE_ASSIGNMENT",
                payload: { ...existing, ...data },
            });
        } else if ("quantity" in data && garmentSizeId) {
            // Si no existe pero tenemos datos suficientes, crearla
            dispatch({
                type: "ADD_ASSIGNMENT",
                payload: {
                    garmentSizeId,
                    ...data,
                } as QuantityAssignment,
            });
        }
    };

    const removeAssignment = (assignmentId: string) => {
        dispatch({
            type: "REMOVE_ASSIGNMENT",
            payload: assignmentId,
        });
    };

    const setValidationError = (id: string, error: string | null) => {
        dispatch({
            type: "SET_VALIDATION_ERROR",
            payload: { id, error },
        });
    };

    // Funciones para deshacer/rehacer
    const undo = () => {
        dispatch({ type: "UNDO" });
    };

    const redo = () => {
        dispatch({ type: "REDO" });
    };

    // Funciones para guardar y cargar borradores
    const saveDraft = () => {
        const draftId = `draft-${Date.now()}`;
        const draftData = {
            id: draftId,
            state: {
                contractor: state.contractor,
                contractorData: state.contractorData,
                selectedOrders: state.selectedOrders,
                assignments: state.assignments,
                currentStep: state.currentStep,
            },
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 horas
        };

        // Guardar en localStorage temporalmente
        localStorage.setItem(
            `assignment-draft-${draftId}`,
            JSON.stringify(draftData),
        );

        dispatch({ type: "SAVE_DRAFT", payload: { draftId } });

        return draftId;
    };

    const loadDraft = (draftId: string) => {
        const draftData = localStorage.getItem(`assignment-draft-${draftId}`);

        if (draftData) {
            const parsed = JSON.parse(draftData);

            dispatch({ type: "LOAD_DRAFT", payload: parsed.state });

            return true;
        }

        return false;
    };

    const clearDraft = () => {
        if (state.draftId) {
            localStorage.removeItem(`assignment-draft-${state.draftId}`);
        }
        dispatch({ type: "CLEAR_DRAFT" });
    };

    const contextValue: WizardContextValue = {
        state,
        dispatch,
        nextStep,
        prevStep,
        canGoNext,
        canGoBack,
        selectedOrderIds: state.selectedOrders,
        assignments: state.assignments || [],
        addAssignment,
        updateAssignment,
        removeAssignment,
        validationErrors: state.validationErrors || {},
        setValidationError,
        activeContractor: state.contractorData,
        undo,
        redo,
        canUndo: state.canUndo,
        canRedo: state.canRedo,
        saveDraft,
        loadDraft,
        clearDraft,
    };

    return (
        <WizardContext.Provider value={contextValue}>
            {children}
        </WizardContext.Provider>
    );
}

// Hook para usar el contexto
export function useWizard() {
    const context = useContext(WizardContext);

    if (context === undefined) {
        throw new Error("useWizard debe usarse dentro de un WizardProvider");
    }

    return context;
}

// Hook para calcular tiempo estimado
export function useEstimatedTime() {
    const { state } = useWizard();

    const calculateEstimatedTime = (): {
        min: number;
        max: number;
        formatted: string;
    } => {
        const baseTime = 1; // 1 minuto base
        let additionalTime = 0;

        // Tiempo por paso (not used currently)
        // const stepTimes: Record<WizardStep, number> = {
        //     contractor: 0.5, // 30 segundos
        //     orders: 1, // 1 minuto por orden (búsqueda y selección)
        //     quantities: 2, // 2 minutos base
        //     summary: 0.5, // 30 segundos para revisar
        // };

        // Calcular tiempo según el paso actual
        const currentStepIndex = [
            "contractor",
            "orders",
            "quantities",
            "summary",
        ].indexOf(state.currentStep);
        // const remainingSteps = 4 - currentStepIndex;

        // Tiempo por órdenes seleccionadas
        const orderTime = state.selectedOrders.length * 0.5; // 30 seg por orden

        // Tiempo por assignments (items con cantidades)
        const itemTime = state.assignments.length * 0.2; // 12 seg por item

        // Calcular tiempo total
        additionalTime = orderTime + itemTime;

        // Tiempo mínimo y máximo estimado
        const minTime = Math.ceil(baseTime + additionalTime);
        const maxTime = Math.ceil((baseTime + additionalTime) * 1.5); // 50% más para el máximo

        // Ajustar según el paso actual
        const timeSpent = currentStepIndex * 0.5; // Asumimos 30 seg por paso completado
        const adjustedMin = Math.max(1, minTime - timeSpent);
        const adjustedMax = Math.max(adjustedMin + 1, maxTime - timeSpent);

        // Formatear texto
        let formatted = "";

        if (adjustedMin === adjustedMax) {
            formatted = `${adjustedMin} minuto${adjustedMin > 1 ? "s" : ""}`;
        } else {
            formatted = `${adjustedMin}-${adjustedMax} minutos`;
        }

        return {
            min: adjustedMin,
            max: adjustedMax,
            formatted,
        };
    };

    return calculateEstimatedTime();
}
