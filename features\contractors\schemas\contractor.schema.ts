import { z } from "zod";

// Esquema para crear un contratista
export const createContractorSchema = z.object({
    firstName: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres")
        .transform((val) => val.trim()),

    middleName: z
        .string()
        .max(50, "El segundo nombre no puede exceder los 50 caracteres")
        .optional()
        .transform((val) => val?.trim() || undefined),

    lastName: z
        .string()
        .min(2, "El apellido debe tener al menos 2 caracteres")
        .max(50, "El apellido no puede exceder los 50 caracteres")
        .transform((val) => val.trim()),

    secondLastName: z
        .string()
        .max(50, "El segundo apellido no puede exceder los 50 caracteres")
        .optional()
        .transform((val) => val?.trim() || undefined),

    email: z
        .string()
        .email("Email inválido")
        .optional()
        .or(z.literal(""))
        .transform((val) => {
            if (!val || val.trim() === "") return undefined;

            return val.trim().toLowerCase();
        }),

    phone: z
        .string()
        .regex(/^\+?[\d\s\-()]+$/, "Formato de teléfono inválido")
        .min(10, "El teléfono debe tener al menos 10 dígitos")
        .optional()
        .or(z.literal(""))
        .transform((val) => {
            if (!val || val.trim() === "") return undefined;

            return val.trim();
        }),

    notes: z
        .string()
        .max(500, "Las notas no pueden exceder los 500 caracteres")
        .optional()
        .transform((val) => val?.trim() || undefined),
});

// Esquema para actualizar un contratista (maneja null, undefined y strings vacíos)
export const updateContractorSchema = z.object({
    firstName: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres")
        .transform((val) => val.trim())
        .optional(),

    middleName: z
        .union([z.string().max(50), z.literal(""), z.null()])
        .optional()
        .transform((val) => {
            if (val === null || val === "") return null;

            return val?.trim() || null;
        }),

    lastName: z
        .string()
        .min(2, "El apellido debe tener al menos 2 caracteres")
        .max(50, "El apellido no puede exceder los 50 caracteres")
        .transform((val) => val.trim())
        .optional(),

    secondLastName: z
        .union([z.string().max(50), z.literal(""), z.null()])
        .optional()
        .transform((val) => {
            if (val === null || val === "") return null;

            return val?.trim() || null;
        }),

    email: z
        .union([z.string().email("Email inválido"), z.literal(""), z.null()])
        .optional()
        .transform((val) => {
            if (val === null || val === "") return null;

            return val?.trim().toLowerCase() || null;
        }),

    phone: z
        .union([
            z
                .string()
                .regex(/^\+?[\d\s\-()]+$/, "Formato de teléfono inválido")
                .min(10, "El teléfono debe tener al menos 10 dígitos"),
            z.literal(""),
            z.null(),
        ])
        .optional()
        .transform((val) => {
            if (val === null || val === "") return null;

            return val?.trim() || null;
        }),

    notes: z
        .union([
            z
                .string()
                .max(500, "Las notas no pueden exceder los 500 caracteres"),
            z.literal(""),
            z.null(),
        ])
        .optional()
        .transform((val) => {
            if (val === null || val === "") return null;

            return val?.trim() || null;
        }),
});

// Tipos inferidos
export type CreateContractorInput = z.infer<typeof createContractorSchema>;
export type UpdateContractorInput = z.infer<typeof updateContractorSchema>;
