"use client";

import type { PackingSummaryProps } from "../types";

import React from "react";

import { formatDate } from "../utils/transform";
import styles from "../PackingPreview.module.css";

export const PackingSummary: React.FC<PackingSummaryProps> = ({
    summary,
    customer,
    order,
    editMode,
    onEdit,
}) => {
    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.currentTarget.textContent);
            }
        };

    return (
        <div className={styles.summary}>
            {/* Customer and Order Information */}
            <div className={styles.summarySection}>
                <h3 className={styles.sectionTitle}>Información del Cliente</h3>
                <div className={styles.summaryGrid}>
                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>Cliente:</span>
                        <span className={styles.summaryValue}>
                            {customer.name}
                        </span>
                    </div>

                    {customer.subName && (
                        <div className={styles.summaryItem}>
                            <span className={styles.summaryLabel}>
                                Sub-Cliente:
                            </span>
                            <span className={styles.summaryValue}>
                                {customer.subName}
                            </span>
                        </div>
                    )}

                    {customer.contact && (
                        <div className={styles.summaryItem}>
                            <span className={styles.summaryLabel}>
                                Contacto:
                            </span>
                            <span className={styles.summaryValue}>
                                {customer.contact}
                            </span>
                        </div>
                    )}

                    {customer.phone && (
                        <div className={styles.summaryItem}>
                            <span className={styles.summaryLabel}>
                                Teléfono:
                            </span>
                            <span className={styles.summaryValue}>
                                {customer.phone}
                            </span>
                        </div>
                    )}

                    {order && (
                        <>
                            <div className={styles.summaryItem}>
                                <span className={styles.summaryLabel}>
                                    Pedido:
                                </span>
                                <span className={styles.summaryValue}>
                                    {order.number}
                                </span>
                            </div>
                            {order.cutOrder && (
                                <div className={styles.summaryItem}>
                                    <span className={styles.summaryLabel}>
                                        Orden de Corte:
                                    </span>
                                    <span className={styles.summaryValue}>
                                        {order.cutOrder}
                                    </span>
                                </div>
                            )}
                        </>
                    )}

                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>
                            Fecha de Entrega:
                        </span>
                        <span className={styles.summaryValue}>
                            {formatDate(summary.deliveryDate, "short")}
                        </span>
                    </div>
                </div>
            </div>

            {/* Totals Summary */}
            <div className={styles.summarySection}>
                <h3 className={styles.sectionTitle}>Resumen de Empaque</h3>
                <div className={styles.summaryGrid}>
                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>
                            Total Piezas:
                        </span>
                        <span
                            className={`${styles.summaryValue} ${styles.fontBold}`}
                        >
                            {summary.totalPieces.toLocaleString("es-MX")}
                        </span>
                    </div>

                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>
                            Total Cajas:
                        </span>
                        <span
                            className={`${styles.summaryValue} ${styles.fontBold}`}
                        >
                            {summary.totalBoxes.toLocaleString("es-MX")}
                        </span>
                    </div>

                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>
                            Total Bolsas:
                        </span>
                        <span
                            className={`${styles.summaryValue} ${styles.fontBold}`}
                        >
                            {summary.totalBags.toLocaleString("es-MX")}
                        </span>
                    </div>

                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>
                            Piezas Sueltas:
                        </span>
                        <span
                            className={`${styles.summaryValue} ${styles.fontBold}`}
                        >
                            {summary.totalLoosePieces.toLocaleString("es-MX")}
                        </span>
                    </div>
                </div>
            </div>

            {/* Quality Breakdown */}
            <div className={styles.summarySection}>
                <h3 className={styles.sectionTitle}>Calidad del Producto</h3>
                <div className={styles.summaryGrid}>
                    <div className={styles.summaryItem}>
                        <span className={styles.summaryLabel}>Primera:</span>
                        <span className={styles.summaryValue}>
                            {summary.qualityBreakdown.primera.toLocaleString(
                                "es-MX",
                            )}{" "}
                            pzs
                        </span>
                    </div>

                    {summary.qualityBreakdown.segunda > 0 && (
                        <div className={styles.summaryItem}>
                            <span className={styles.summaryLabel}>
                                Segunda:
                            </span>
                            <span className={styles.summaryValue}>
                                {summary.qualityBreakdown.segunda.toLocaleString(
                                    "es-MX",
                                )}{" "}
                                pzs
                            </span>
                        </div>
                    )}

                    {summary.qualityBreakdown.manchada > 0 && (
                        <div className={styles.summaryItem}>
                            <span className={styles.summaryLabel}>
                                Manchada:
                            </span>
                            <span className={styles.summaryValue}>
                                {summary.qualityBreakdown.manchada.toLocaleString(
                                    "es-MX",
                                )}{" "}
                                pzs
                            </span>
                        </div>
                    )}

                    {summary.qualityBreakdown.incompleta > 0 && (
                        <div className={styles.summaryItem}>
                            <span className={styles.summaryLabel}>
                                Incompleta:
                            </span>
                            <span className={styles.summaryValue}>
                                {summary.qualityBreakdown.incompleta.toLocaleString(
                                    "es-MX",
                                )}{" "}
                                pzs
                            </span>
                        </div>
                    )}
                </div>
            </div>

            {/* Notes */}
            {summary.notes && (
                <div className={styles.summarySection}>
                    <h3 className={styles.sectionTitle}>Notas</h3>
                    <p
                        suppressContentEditableWarning
                        className={`${styles.summaryNotes} ${editMode ? styles.editableField : ""}`}
                        contentEditable={editMode}
                        onBlur={handleEdit("summary.notes")}
                    >
                        {summary.notes}
                    </p>
                </div>
            )}
        </div>
    );
};
