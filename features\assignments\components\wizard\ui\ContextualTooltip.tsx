"use client";

import React, { useState } from "react";
import { Tooltip } from "@heroui/react";
import {
    InformationCircleIcon,
    QuestionMarkCircleIcon,
    ExclamationTriangleIcon,
    LightBulbIcon,
    ShieldCheckIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

interface ContextualTooltipProps {
    content: string;
    variant?: "info" | "help" | "warning" | "tip" | "success";
    position?: "top" | "bottom" | "left" | "right";
    children?: React.ReactNode;
    showIcon?: boolean;
    className?: string;
    delay?: number;
    animated?: boolean;
}

const variantIcons = {
    info: InformationCircleIcon,
    help: QuestionMarkCircleIcon,
    warning: ExclamationTriangleIcon,
    tip: LightBulbIcon,
    success: ShieldCheckIcon,
};

const variantColors = {
    info: {
        icon: "text-blue-500 hover:text-blue-600",
        bg: "from-blue-500/20 to-indigo-500/20",
        border: "border-blue-500/30",
        glow: "shadow-blue-500/20",
    },
    help: {
        icon: "text-purple-500 hover:text-purple-600",
        bg: "from-purple-500/20 to-pink-500/20",
        border: "border-purple-500/30",
        glow: "shadow-purple-500/20",
    },
    warning: {
        icon: "text-amber-500 hover:text-amber-600",
        bg: "from-amber-500/20 to-orange-500/20",
        border: "border-amber-500/30",
        glow: "shadow-amber-500/20",
    },
    tip: {
        icon: "text-emerald-500 hover:text-emerald-600",
        bg: "from-emerald-500/20 to-green-500/20",
        border: "border-emerald-500/30",
        glow: "shadow-emerald-500/20",
    },
    success: {
        icon: "text-green-500 hover:text-green-600",
        bg: "from-green-500/20 to-emerald-500/20",
        border: "border-green-500/30",
        glow: "shadow-green-500/20",
    },
};

export function ContextualTooltip({
    content,
    variant = "help",
    position = "bottom",
    children,
    showIcon = true,
    className = "",
    delay = 0,
    animated = true,
}: ContextualTooltipProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const Icon = variantIcons[variant];
    const colors = variantColors[variant];

    // Detectar si es móvil para ajustar posición
    const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
    const tooltipPosition = isMobile ? "top" : position;

    const tooltipContent = (
        <motion.div
            animate={{ opacity: 1, scale: 1 }}
            className="relative"
            initial={{ opacity: 0, scale: 0.95 }}
        >
            <div className="p-3 rounded-xl bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-700/50 shadow-2xl">
                <div className="flex items-start gap-2">
                    <motion.div
                        animate={{ rotate: animated ? [0, 10, -10, 0] : 0 }}
                        className={`p-1.5 rounded-lg bg-gradient-to-br ${colors.bg} ${colors.border} border`}
                        transition={{ duration: 2, repeat: Infinity }}
                    >
                        <Icon
                            className="w-4 h-4"
                            style={{
                                color: colors.icon
                                    .split(" ")[0]
                                    .replace("text-", ""),
                            }}
                        />
                    </motion.div>
                    <div className="max-w-xs">
                        <p className="text-sm text-gray-100 leading-relaxed">
                            {content}
                        </p>
                    </div>
                </div>
                <motion.div
                    animate={{ opacity: [0.3, 0.6, 0.3] }}
                    className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 blur-xl"
                    transition={{ duration: 2, repeat: Infinity }}
                />
            </div>
        </motion.div>
    );

    const trigger =
        children ||
        (showIcon && (
            <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className={`relative inline-flex ${className}`}
                initial={{ opacity: 0, scale: 0 }}
                transition={{ delay, type: "spring" }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                <motion.button
                    aria-label="Más información"
                    className={`
                        relative p-2 rounded-lg 
                        bg-gradient-to-br ${colors.bg} 
                        ${colors.border} border
                        ${colors.glow} shadow-lg
                        hover:shadow-xl
                        transition-all duration-300
                        cursor-help
                    `}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                >
                    <Icon className={`w-4 h-4 ${colors.icon}`} />

                    {/* Animated glow effect */}
                    <AnimatePresence>
                        {isHovered && (
                            <motion.div
                                animate={{ scale: 1.5, opacity: 1 }}
                                className={`
                                    absolute inset-0 rounded-lg
                                    bg-gradient-to-br ${colors.bg}
                                    blur-md
                                `}
                                exit={{ scale: 2, opacity: 0 }}
                                initial={{ scale: 0.8, opacity: 0 }}
                                transition={{ duration: 0.3 }}
                            />
                        )}
                    </AnimatePresence>

                    {/* Pulse animation */}
                    {animated && (
                        <motion.div
                            animate={{
                                scale: [1, 1.2, 1],
                                opacity: [0.5, 0, 0.5],
                            }}
                            className={`
                                absolute inset-0 rounded-lg
                                bg-gradient-to-br ${colors.bg}
                            `}
                            transition={{ duration: 2, repeat: Infinity }}
                        />
                    )}
                </motion.button>
            </motion.div>
        ));

    return (
        <Tooltip
            classNames={{
                base: "p-0 bg-transparent",
            }}
            content={tooltipContent}
            isOpen={isOpen}
            motionProps={{
                variants: {
                    exit: {
                        opacity: 0,
                        scale: 0.9,
                        y: -10,
                        transition: {
                            duration: 0.2,
                            ease: "easeInOut",
                        },
                    },
                    enter: {
                        opacity: 1,
                        scale: 1,
                        y: 0,
                        transition: {
                            duration: 0.3,
                            ease: "easeOut",
                        },
                    },
                },
            }}
            placement={tooltipPosition}
            showArrow={false}
            onOpenChange={setIsOpen}
        >
            {trigger}
        </Tooltip>
    );
}

// Enhanced predefined contexts for the wizard
export const tooltipContexts = {
    contractor: {
        text: "Selecciona el contratista que realizará el trabajo. Puedes buscar por nombre o documento. Los contratistas con mejor desempeño aparecen primero.",
        variant: "info" as const,
    },
    orderSelection: {
        text: "Puedes seleccionar múltiples órdenes. Usa Ctrl+Click para selección múltiple o Shift+Click para rangos. Las órdenes urgentes están destacadas.",
        variant: "tip" as const,
    },
    orderSearch: {
        text: "Busca órdenes por número, cliente o referencia. Los resultados se actualizan automáticamente con sugerencias inteligentes.",
        variant: "info" as const,
    },
    quantities: {
        text: "Distribuye las cantidades según la capacidad del contratista. El sistema te sugerirá cantidades óptimas basadas en el historial.",
        variant: "warning" as const,
    },
    quantityInput: {
        text: "Ingresa la cantidad a asignar. El sistema validará la disponibilidad en tiempo real y te sugerirá cantidades óptimas.",
        variant: "help" as const,
    },
    summary: {
        text: "Revisa todos los detalles antes de confirmar. Puedes regresar para hacer cambios. La asignación se guardará automáticamente.",
        variant: "success" as const,
    },
    navigation: {
        text: "Usa Ctrl+→ para avanzar, Ctrl+← para retroceder, o Esc para cancelar. Los cambios se guardan automáticamente.",
        variant: "tip" as const,
    },
    timeEstimate: {
        text: "Tiempo estimado basado en la cantidad de órdenes y artículos seleccionados. Se actualiza en tiempo real.",
        variant: "info" as const,
    },
    draftSaved: {
        text: "Tu progreso se guarda automáticamente cada 30 segundos. Puedes cerrar y continuar más tarde.",
        variant: "success" as const,
    },
    bulkActions: {
        text: "Selecciona múltiples elementos para realizar acciones en lote. Ahorra tiempo con operaciones masivas.",
        variant: "tip" as const,
    },
};

// Enhanced hook for contextual tooltips with animations
export function useContextualTooltips() {
    const getTooltip = (
        context: keyof typeof tooltipContexts,
        options?: Partial<ContextualTooltipProps>,
    ) => {
        const config = tooltipContexts[context];

        return (
            <ContextualTooltip
                animated={true}
                content={config.text}
                variant={config.variant}
                {...options}
            />
        );
    };

    const getAnimatedTooltip = (
        content: string,
        variant: ContextualTooltipProps["variant"] = "help",
    ) => {
        return (
            <motion.div
                animate={{ opacity: 1, scale: 1 }}
                initial={{ opacity: 0, scale: 0 }}
                transition={{ type: "spring", stiffness: 300 }}
            >
                <ContextualTooltip
                    animated={true}
                    content={content}
                    variant={variant}
                />
            </motion.div>
        );
    };

    return { getTooltip, getAnimatedTooltip, ContextualTooltip };
}
