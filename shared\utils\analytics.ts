/**
 * Analytics utilities for tracking deprecated field usage and feature adoption
 */

/**
 * Get current user ID from auth context
 * TODO: Replace with actual auth implementation
 */
function getCurrentUserId(): string | null {
    if (typeof window === "undefined") return null;

    return window.localStorage.getItem("userId") || "anonymous";
}

/**
 * Track when a deprecated field is accessed
 * @param field - Name of the deprecated field
 */
export function trackDeprecatedFieldAccess(field: string): void {
    if (typeof window !== "undefined" && "gtag" in window) {
        (window as any).gtag("event", "deprecated_field_access", {
            field_name: field,
            timestamp: new Date().toISOString(),
            user_id: getCurrentUserId(),
            page_path: window.location.pathname,
        });
    }

    // Also log to console in development
    if (process.env.NODE_ENV === "development") {
        // REMOVED: console.log(`[Analytics] Deprecated field accessed: ${field}`);
    }
}

/**
 * Track when the "coming soon" card is viewed
 */
export function trackComingSoonCardView(): void {
    if (typeof window !== "undefined" && "gtag" in window) {
        (window as any).gtag("event", "coming_soon_card_view", {
            location: "order_modal",
            timestamp: new Date().toISOString(),
            user_id: getCurrentUserId(),
        });
    }
}

/**
 * Track order modal interactions
 * @param action - Action performed (open, close, tab_change, etc.)
 * @param details - Additional details about the action
 */
export function trackOrderModalAction(
    action: string,
    details?: Record<string, any>,
): void {
    if (typeof window !== "undefined" && "gtag" in window) {
        (window as any).gtag("event", "order_modal_action", {
            action,
            timestamp: new Date().toISOString(),
            user_id: getCurrentUserId(),
            ...details,
        });
    }
}

/**
 * Track feature flag evaluation
 * @param feature - Feature flag name
 * @param enabled - Whether the feature was enabled
 */
export function trackFeatureFlagEvaluation(
    feature: string,
    enabled: boolean,
): void {
    if (typeof window !== "undefined" && "gtag" in window) {
        (window as any).gtag("event", "feature_flag_evaluation", {
            feature,
            enabled,
            timestamp: new Date().toISOString(),
            user_id: getCurrentUserId(),
        });
    }
}
