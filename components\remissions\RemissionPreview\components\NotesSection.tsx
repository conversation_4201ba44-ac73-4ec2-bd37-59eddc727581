"use client";

import React from "react";
import { Card, CardBody } from "@heroui/card";
import { Textarea } from "@heroui/input";

interface NotesSectionProps {
    internalNotes?: string;
    externalNotes?: string;
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

export const NotesSection: React.FC<NotesSectionProps> = ({
    internalNotes,
    externalNotes,
    editMode,
    onEdit,
}) => {
    const handleNotesChange =
        (field: string) => (e: React.ChangeEvent<HTMLTextAreaElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.target.value);
            }
        };

    if (!internalNotes && !externalNotes && !editMode) {
        return null; // Don't show section if no notes and not in edit mode
    }

    return (
        <Card className="mb-6">
            <CardBody>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    Observaciones
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {(internalNotes || editMode) && (
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Notas Internas
                            </label>
                            {editMode ? (
                                <Textarea
                                    minRows={3}
                                    placeholder="Notas visibles solo internamente..."
                                    value={internalNotes || ""}
                                    variant="bordered"
                                    onChange={
                                        handleNotesChange(
                                            "internalNotes",
                                        ) as any
                                    }
                                />
                            ) : (
                                <p className="text-base text-gray-800 whitespace-pre-wrap">
                                    {internalNotes}
                                </p>
                            )}
                        </div>
                    )}

                    {(externalNotes || editMode) && (
                        <div>
                            <label className="text-sm text-gray-600 mb-1 block">
                                Notas Externas
                            </label>
                            {editMode ? (
                                <Textarea
                                    minRows={3}
                                    placeholder="Notas visibles en el PDF..."
                                    value={externalNotes || ""}
                                    variant="bordered"
                                    onChange={
                                        handleNotesChange(
                                            "externalNotes",
                                        ) as any
                                    }
                                />
                            ) : (
                                <p className="text-base text-gray-800 whitespace-pre-wrap">
                                    {externalNotes}
                                </p>
                            )}
                        </div>
                    )}
                </div>
            </CardBody>
        </Card>
    );
};
