import { PrismaClient } from "@prisma/client";

// Verificar si estamos en el navegador (evita errores al importar en componentes del cliente)
const isServer = typeof window === "undefined";

// Configuración de logging según el entorno
const getLogConfig = () => {
    if (process.env.NODE_ENV === "development") {
        return [...["error", "warn"]] as ("error" | "warn")[];
    }

    return [...["error"]] as "error"[];
};

// Función para crear el cliente Prisma con configuración optimizada
const prismaClientSingleton = () => {
    const url = process.env.DATABASE_URL || "";

    // En desarrollo con Turbopack, agregar pgbouncer=true si no está presente
    let connectionUrl = url;

    if (
        process.env.NODE_ENV === "development" &&
        !url.includes("pgbouncer=true")
    ) {
        connectionUrl = url.includes("?")
            ? `${url}&pgbouncer=true&statement_cache_size=0`
            : `${url}?pgbouncer=true&statement_cache_size=0`;
    }

    return new PrismaClient({
        log: getLogConfig(),
        errorFormat: "pretty",
        datasources: {
            db: {
                url: connectionUrl,
            },
        },
        transactionOptions: {
            maxWait: 15000, // 15 seconds max wait to enter transaction
            timeout: 15000, // 15 seconds max transaction duration (temporary while we optimize)
            isolationLevel: "ReadCommitted", // Balanced isolation level for better performance
        },
    });
};

// Declaración de tipos para globalThis
declare global {
    // eslint-disable-next-line no-var
    var prisma: PrismaClient | undefined;
}

// Cliente singleton - solo se crea en el servidor
let prisma: PrismaClient;

if (isServer) {
    // Usar globalThis para garantizar una instancia única en desarrollo
    prisma = globalThis.prisma ?? prismaClientSingleton();

    // En desarrollo, guardar en globalThis para evitar múltiples instancias con HMR
    if (process.env.NODE_ENV !== "production") {
        globalThis.prisma = prisma;
    }
} else {
    // En el navegador, crear un proxy que previene errores
    prisma = new Proxy({} as PrismaClient, {
        get: (target, prop) => {
            if (typeof prop === "string") {
                return () => {
                    // eslint-disable-next-line no-console
                    // Prisma Client no puede ejecutarse en el navegador - comentario removido

                    return null;
                };
            }

            return undefined;
        },
    });
}

// Métricas simples de conexión
let activeQueries = 0;
let totalQueries = 0;
const queryMetrics = {
    getActiveQueries: () => activeQueries,
    getTotalQueries: () => totalQueries,
    reset: () => {
        totalQueries = 0;
    },
};

// Middleware para monitoreo básico (solo en servidor)
if (isServer) {
    prisma.$use(async (params, next) => {
        activeQueries++;
        totalQueries++;

        const startTime = Date.now();

        try {
            const result = await next(params);

            // Log de queries lentas en desarrollo
            if (process.env.NODE_ENV === "development") {
                const duration = Date.now() - startTime;

                if (duration > 1000) {
                    // eslint-disable-next-line no-console
                    // Query lenta detectada - comentario removido
                }
            }

            return result;
        } finally {
            activeQueries--;

            // Advertencia si hay muchas queries activas
            if (activeQueries > 10) {
                // eslint-disable-next-line no-console
                // Alto número de queries activas - comentario removido
            }
        }
    });
}

// Función auxiliar para ejecutar operaciones con retry básico
export async function executeWithRetry<T>(
    operation: () => Promise<T>,
    options: { maxRetries?: number; retryDelay?: number } = {},
): Promise<T> {
    const { maxRetries = 2, retryDelay = 100 } = options;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await operation();
        } catch (error: unknown) {
            // Si es el último intento, lanzar el error
            if (attempt === maxRetries) {
                throw error;
            }

            // Solo reintentar errores de conexión específicos
            const prismaError = error as { code?: string; message?: string };
            const isRetriableError =
                prismaError.code === "P1017" || // No se puede conectar
                prismaError.code === "P1001" || // No se puede alcanzar el servidor
                prismaError.message?.includes("connection") ||
                prismaError.message?.includes("Connection terminated");

            if (!isRetriableError) {
                throw error;
            }

            // Log del reintento
            // eslint-disable-next-line no-console
            // Error de conexión, reintentando - comentario removido

            // Esperar con backoff exponencial
            const delay = retryDelay * Math.pow(2, attempt);

            await new Promise((resolve) => setTimeout(resolve, delay));
        }
    }

    throw new Error("Unreachable");
}

// Función para verificar el estado de la conexión
export async function checkConnection(): Promise<boolean> {
    if (!isServer) return false;

    try {
        await prisma.$queryRaw`SELECT 1`;

        return true;
    } catch (error) {
        // eslint-disable-next-line no-console
        // Error al verificar conexión - comentario removido

        return false;
    }
}

// Función para limpiar prepared statements (útil en desarrollo)
export async function clearPreparedStatements(): Promise<void> {
    if (!isServer || process.env.NODE_ENV !== "development") return;

    try {
        await prisma.$executeRawUnsafe("DEALLOCATE ALL");
        // eslint-disable-next-line no-console
        // Prepared statements liberados - comentario removido
    } catch {
        // Es normal que falle si no hay prepared statements
        // eslint-disable-next-line no-console
        // No hay prepared statements para liberar - comentario removido
    }
}

// Exportar el cliente y utilidades
export { prisma, queryMetrics };

// Exportar tipo para uso en el proyecto
export type { PrismaClient } from "@prisma/client";
