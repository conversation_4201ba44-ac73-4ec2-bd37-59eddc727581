"use client";

import React, { use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    CubeIcon,
    SparklesIcon,
    TruckIcon,
    CogIcon,
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    ChartBarIcon,
    PlusIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import {
    <PERSON>bs,
    Tab,
    Button,
    Card,
    CardBody,
    Pagination,
} from "@/shared/components/ui/hero-ui-client";
import {
    DashboardLayout,
    DashboardFilters,
    DashboardTable,
} from "@/shared/components/dashboard";
import { Order } from "@/features/orders/types/orders";
import MultiSelectDropdown from "@/shared/components/ui/MultiSelectDropdown";
import { useOrdersData } from "@/features/orders/hooks/useOrdersData";
import { useOrdersState } from "@/features/orders/hooks/useOrdersState";
import { useContractorsForFilter } from "@/features/contractors/hooks/useContractors";
import { useOrderPageState } from "@/features/orders/hooks/useOrderPageState";
import { useOrderStats } from "@/features/orders/hooks/useOrderStats";
import OrderDetailModal from "@/features/orders/components/OrderDetailModal";
import { EnhancedOrderCardV2 } from "@/features/orders/components/EnhancedOrderCardV2";
import CompactAlerts from "@/features/orders/components/CompactAlerts";
import MetricsPlaceholder from "@/features/orders/components/MetricsPlaceholder";
import CalendarPlaceholder from "@/features/orders/components/CalendarPlaceholder";
import { sortData } from "@/shared/utils/sortHelpers";

import { columns } from "./orderTableColumns";
import { filters, sortOptions } from "./orderFiltersConfig";

interface OptimizedOrdersPageProps {
    currentUserId?: string;
    userRole?: string;
}

const ROWS_PER_PAGE = 12;

export default function OptimizedOrdersPage({
    currentUserId,
    userRole,
}: OptimizedOrdersPageProps) {
    const router = useRouter();

    // Gestión de estado centralizada
    const { state: pageState, actions: pageActions } = useOrderPageState();
    const { state: ordersState, actions: ordersActions } = useOrdersState();

    // Datos de órdenes
    const {
        data: { orders, orderStatuses },
        status: { isLoading },
        operations: { deleteOrder, fetchOrder, mutate },
    } = useOrdersData(ordersState);

    // Hook para contratistas
    const { contractors, isLoading: loadingContractors } =
        useContractorsForFilter();

    // Estadísticas optimizadas
    const { stats } = useOrderStats(orders);

    // Estadísticas mejoradas con memoización
    const extendedStats = useMemo(
        () => [
            {
                title: "Total de Órdenes",
                value: stats.total,
                icon: <CubeIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Órdenes activas en el sistema",
            },
            {
                title: "Nuevas (7 días)",
                value: stats.new7Days,
                icon: <SparklesIcon className="w-6 h-6" />,
                color: "secondary" as const,
                change: 15,
                changeLabel: "vs. semana anterior",
                changeType: "increase" as const,
            },
            {
                title: "En Proceso",
                value: stats.inProgress,
                icon: <CogIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "Órdenes en producción",
            },
            {
                title: "En Producción",
                value: stats.inProduction,
                icon: <TruckIcon className="w-6 h-6" />,
                color: "warning" as const,
                description: "En línea de producción",
            },
            {
                title: "Entregadas",
                value: stats.delivered,
                icon: <CheckCircleIcon className="w-6 h-6" />,
                color: "success" as const,
                change: 23,
                changeLabel: "este mes",
                changeType: "increase" as const,
            },
            {
                title: "Canceladas",
                value: stats.cancelled,
                icon: <XCircleIcon className="w-6 h-6" />,
                color: "danger" as const,
                change: -2,
                changeLabel: "vs. mes anterior",
                changeType: "decrease" as const,
            },
            {
                title: "Retrasadas",
                value: stats.delayed,
                icon: <ExclamationTriangleIcon className="w-6 h-6" />,
                color: "danger" as const,
                description: "¡Requieren atención inmediata!",
            },
            {
                title: "Tasa de Cumplimiento",
                value: stats.complianceRate,
                icon: <ChartBarIcon className="w-6 h-6" />,
                color: "success" as const,
                description: "Entregas a tiempo",
                suffix: "%",
            },
        ],
        [stats],
    );

    // Acciones de la tabla
    const actions = useMemo(
        () => [
            {
                label: "Ver Detalles",
                icon: <EyeIcon className="w-4 h-4" />,
                onClick: (order: Order) => handleOpenDetail(order),
            },
            {
                label: "Editar",
                icon: <PencilIcon className="w-4 h-4" />,
                onClick: (order: Order) => {
                    router.push(`/dashboard/orders/${order.id}/edit`);
                },
                color: "primary" as const,
            },
            {
                label: "Eliminar",
                icon: <TrashIcon className="w-4 h-4" />,
                onClick: async (order: Order) => {
                    if (confirm("¿Estás seguro de eliminar esta orden?")) {
                        await deleteOrder(order.id);
                    }
                },
                color: "danger" as const,
                isDisabled: (order: Order) => userRole !== "admin",
            },
        ],
        [router, deleteOrder, userRole],
    );

    // Filtrar y ordenar datos con memoización pesada
    const filteredData = useMemo(() => {
        let filtered = [...orders];

        // Búsqueda
        if (pageState.searchValue) {
            const search = pageState.searchValue.toLowerCase();

            filtered = filtered.filter(
                (order) =>
                    order.cutOrder?.toLowerCase().includes(search) ||
                    order.customer?.name?.toLowerCase().includes(search) ||
                    order.id.toLowerCase().includes(search),
            );
        }

        // Filtro por estado
        if (
            pageState.filterValues.status &&
            pageState.filterValues.status !== "all"
        ) {
            filtered = filtered.filter(
                (o) => o.status === pageState.filterValues.status,
            );
        }

        // Filtro por contratista(s)
        if (pageState.selectedContractors.length > 0) {
            filtered = filtered.filter((order: any) =>
                order.assignments?.some((assignment: any) =>
                    pageState.selectedContractors.includes(
                        assignment.contractorId,
                    ),
                ),
            );
        }

        // Filtro por período
        if (
            pageState.filterValues.period &&
            pageState.filterValues.period !== "all"
        ) {
            const days = parseInt(pageState.filterValues.period);
            const cutoffDate = new Date();

            cutoffDate.setDate(cutoffDate.getDate() - days);
            filtered = filtered.filter(
                (o) => new Date(o.createdAt) >= cutoffDate,
            );
        }

        // Aplicar ordenamiento
        if (pageState.currentSort) {
            filtered = sortData(
                filtered,
                pageState.currentSort.field,
                pageState.currentSort.direction,
            );
        }

        return filtered;
    }, [
        orders,
        pageState.searchValue,
        pageState.filterValues,
        pageState.selectedContractors,
        pageState.currentSort,
    ]);

    // Paginación con memoización
    const paginatedData = useMemo(() => {
        const startIndex = (pageState.page - 1) * ROWS_PER_PAGE;

        return filteredData.slice(startIndex, startIndex + ROWS_PER_PAGE);
    }, [filteredData, pageState.page]);

    const totalPages = Math.ceil(filteredData.length / ROWS_PER_PAGE);

    // Handler para abrir detalles optimizado
    const handleOpenDetail = useCallback(
        async (order: Order) => {
            pageActions.openOrderDetail(order);

            try {
                const fullOrder = await fetchOrder(order.id, false);

                if (fullOrder) {
                    pageActions.setSelectedOrder(fullOrder);
                }
            } catch (error) {
                // Error manejado silenciosamente
            } finally {
                pageActions.setLoadingOrder(false);
            }
        },
        [fetchOrder, pageActions],
    );

    // Renderizar contenido según la pestaña activa
    const renderTabContent = useCallback(() => {
        switch (pageState.activeTab) {
            case "orders":
                return (
                    <div className="space-y-4">
                        {/* Filtros y búsqueda */}
                        <DashboardFilters
                            activeFiltersCount={
                                Object.keys(pageState.filterValues).filter(
                                    (key) =>
                                        pageState.filterValues[key] &&
                                        pageState.filterValues[key] !== "all",
                                ).length
                            }
                            currentSort={pageState.currentSort}
                            filterValues={pageState.filterValues}
                            filters={filters}
                            searchValue={pageState.searchValue}
                            sortOptions={sortOptions}
                            onClearFilters={pageActions.resetFilters}
                            onFilterChange={(key, value) => {
                                pageActions.setFilterValues({
                                    ...pageState.filterValues,
                                    [key]: value,
                                });

                                if (key === "status") {
                                    ordersActions.setStatus(
                                        value === "all" ? "" : value,
                                    );
                                }
                            }}
                            onSearchChange={(value) => {
                                pageActions.setSearchValue(value);
                                ordersActions.setSearch(value);
                            }}
                            onSortChange={pageActions.setCurrentSort}
                        />

                        {/* Filtro de contratistas */}
                        <div className="flex items-center gap-4">
                            <MultiSelectDropdown
                                isLoading={loadingContractors}
                                label="Contratistas"
                                options={contractors.map((c) => ({
                                    value: c.id,
                                    label: c.name,
                                }))}
                                placeholder={
                                    loadingContractors
                                        ? "Cargando..."
                                        : "Todos los contratistas"
                                }
                                value={pageState.selectedContractors}
                                onChange={pageActions.setSelectedContractors}
                            />
                        </div>

                        {/* Vista de tabla o grid */}
                        {pageState.viewMode === "list" ? (
                            <DashboardTable
                                actions={actions as any}
                                columns={columns as any}
                                data={paginatedData}
                                emptyContent="No hay órdenes registradas"
                                isLoading={isLoading}
                            />
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {paginatedData.map((order) => (
                                    <EnhancedOrderCardV2
                                        key={order.id}
                                        order={order}
                                        viewMode={pageState.viewMode}
                                        onView={handleOpenDetail}
                                    />
                                ))}
                            </div>
                        )}

                        {/* Paginación */}
                        {totalPages > 1 && (
                            <div className="flex justify-center mt-6">
                                <Pagination
                                    showControls
                                    page={pageState.page}
                                    total={totalPages}
                                    onChange={(newPage) => {
                                        pageActions.setPage(newPage);
                                        window.scrollTo({
                                            top: 0,
                                            behavior: "smooth",
                                        });
                                    }}
                                />
                            </div>
                        )}
                    </div>
                );

            case "metrics":
                return <MetricsPlaceholder orders={orders} />;

            case "calendar":
                return (
                    <CalendarPlaceholder
                        orders={orders}
                        onSelectOrder={handleOpenDetail}
                    />
                );

            default:
                return null;
        }
    }, [
        pageState,
        pageActions,
        ordersActions,
        filters,
        sortOptions,
        contractors,
        loadingContractors,
        columns,
        paginatedData,
        isLoading,
        actions,
        totalPages,
        orders,
        handleOpenDetail,
    ]);

    return (
        <>
            <DashboardLayout
                actions={
                    <div className="flex items-center gap-3">
                        <CompactAlerts
                            orders={orders}
                            onViewOrder={handleOpenDetail}
                        />
                        <Button
                            color="primary"
                            startContent={<PlusIcon className="w-5 h-5" />}
                            onPress={() => router.push("/dashboard/orders/new")}
                        >
                            Nueva Orden
                        </Button>
                    </div>
                }
                breadcrumbs={[{ label: "Órdenes" }]}
                subtitle="Gestión integral de órdenes de producción"
                title="Centro de Comando de Órdenes"
            >
                {/* Estadísticas mejoradas */}
                <div className="mb-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
                        {extendedStats.map((stat, index) => (
                            <motion.div
                                key={index}
                                animate={{ opacity: 1, y: 0 }}
                                initial={{ opacity: 0, y: 20 }}
                                transition={{ delay: index * 0.05 }}
                            >
                                <Card className="hover:shadow-lg transition-shadow">
                                    <CardBody className="p-4">
                                        <div className="flex items-center justify-between mb-2">
                                            <div
                                                className={`p-2 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}
                                            >
                                                {stat.icon}
                                            </div>
                                            {stat.change !== undefined && (
                                                <div
                                                    className={`text-xs ${stat.changeType === "increase" ? "text-success" : "text-danger"}`}
                                                >
                                                    {stat.change > 0 ? "+" : ""}
                                                    {stat.change}%
                                                </div>
                                            )}
                                        </div>
                                        <p className="text-2xl font-bold">
                                            {stat.value}
                                            {stat.suffix || ""}
                                        </p>
                                        <p className="text-xs text-gray-500 mt-1">
                                            {stat.title}
                                        </p>
                                        {stat.description && (
                                            <p className="text-xs text-gray-400 mt-0.5">
                                                {stat.description}
                                            </p>
                                        )}
                                    </CardBody>
                                </Card>
                            </motion.div>
                        ))}
                    </div>
                </div>

                {/* Sistema de tabs */}
                <Card className="shadow-sm">
                    <CardBody className="p-0">
                        <Tabs
                            classNames={{
                                tabList:
                                    "gap-6 w-full relative rounded-none p-0 border-b border-divider px-6",
                                cursor: "w-full bg-primary/10 dark:bg-primary/20",
                                tab: "max-w-fit px-4 h-12 data-[selected=true]:text-primary",
                                tabContent:
                                    "group-data-[selected=true]:text-primary group-data-[selected=true]:font-semibold",
                            }}
                            selectedKey={pageState.activeTab}
                            onSelectionChange={(key) =>
                                pageActions.setActiveTab(key as string)
                            }
                        >
                            <Tab
                                key="orders"
                                title={
                                    <div className="flex items-center space-x-2">
                                        <CubeIcon className="w-4 h-4" />
                                        <span>Órdenes</span>
                                    </div>
                                }
                            />
                            <Tab
                                key="metrics"
                                title={
                                    <div className="flex items-center space-x-2">
                                        <ChartBarIcon className="w-4 h-4" />
                                        <span>Métricas</span>
                                    </div>
                                }
                            />
                            <Tab
                                key="calendar"
                                title={
                                    <div className="flex items-center space-x-2">
                                        <CalendarIcon className="w-4 h-4" />
                                        <span>Calendario</span>
                                    </div>
                                }
                            />
                        </Tabs>

                        <div className="p-6">{renderTabContent()}</div>
                    </CardBody>
                </Card>
            </DashboardLayout>

            {/* Modal de detalles */}
            <OrderDetailModal
                currentUserId={currentUserId}
                isLoadingOrder={pageState.isLoadingOrder}
                isOpen={pageState.isDetailModalOpen}
                order={pageState.selectedOrder as any}
                userRole={userRole}
                onClose={() => {
                    pageActions.closeOrderDetail();
                    // Pequeño delay antes de limpiar selectedOrder
                    setTimeout(() => pageActions.setSelectedOrder(null), 300);
                }}
                onDelete={async () => {
                    if (pageState.selectedOrder) {
                        await deleteOrder(pageState.selectedOrder.id);
                        pageActions.closeOrderDetail();
                        setTimeout(
                            () => pageActions.setSelectedOrder(null),
                            300,
                        );
                    }
                }}
            />
        </>
    );
}
