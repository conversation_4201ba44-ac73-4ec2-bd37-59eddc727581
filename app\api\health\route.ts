import { NextResponse } from "next/server";

import { healthCheck } from "@/shared/lib/db";

/**
 * Health check endpoint para verificar el estado de la aplicación
 * GET /api/health
 */
export async function GET() {
    try {
        const dbHealth = await healthCheck();

        const isHealthy = dbHealth.status === "healthy";

        return NextResponse.json(
            {
                status: isHealthy ? "ok" : "error",
                timestamp: new Date().toISOString(),
                services: {
                    database: dbHealth,
                },
                version: process.env.npm_package_version || "1.0.0",
            },
            {
                status: isHealthy ? 200 : 503,
            },
        );
    } catch {
        return NextResponse.json(
            {
                status: "error",
                timestamp: new Date().toISOString(),
                error: "Failed to check health status",
            },
            {
                status: 503,
            },
        );
    }
}
