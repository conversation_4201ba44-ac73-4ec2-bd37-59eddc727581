"use client";

export default function EmptyNotes() {
    return (
        <div className="flex flex-col items-center justify-center py-24">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full p-8 mb-4">
                <svg
                    className="h-12 w-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                    />
                </svg>
            </div>
            <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
                No hay notas aún
            </h3>
            <p className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-6">
                Aún no se han creado notas en el sistema. Las notas te permiten
                documentar información importante sobre órdenes y otros
                elementos.
            </p>
            <a
                className="px-4 py-2 bg-primary-500 hover:bg-primary-600 rounded-md text-white font-medium transition-colors"
                href="/dashboard/notes/new"
            >
                Crear primera nota
            </a>
        </div>
    );
}
