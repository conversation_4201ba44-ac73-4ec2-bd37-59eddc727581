"use server";

import type {
    CreatePackingInput,
    PackingSummaryData,
    QualityType,
} from "@/lib/types/packing";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";
import { auth } from "@/lib/auth";

import { generatePackingFolio } from "./generate-folio";

const createPackingEnhancedSchema = z.object({
    customerId: z.string().min(1, "El cliente es requerido"),
    subCustomerId: z.string().optional().nullable(),
    orderId: z.string().optional().nullable(),
    deliveryDate: z.date(),
    notes: z.string().optional().nullable(),
    transportNotes: z.string().optional().nullable(),
    packingType: z.enum(["mixto", "cajas", "bolsas"]).optional().nullable(),
    companyInfo: z
        .object({
            name: z.string(),
            logo: z.string().optional().nullable(),
            rfc: z.string().optional().nullable(),
            address: z.string().optional().nullable(),
            city: z.string().optional().nullable(),
            state: z.string().optional().nullable(),
            postalCode: z.string().optional().nullable(),
            country: z.string().optional().nullable(),
            phone: z.string().optional().nullable(),
            email: z.string().optional().nullable(),
            website: z.string().optional().nullable(),
        })
        .optional()
        .nullable(),
    details: z
        .array(
            z.object({
                garmentSizeId: z.string(),
                quantity: z.number().int().positive(),
                modelCode: z.string().optional().nullable(),
                colorName: z.string().optional().nullable(),
                partNumber: z.string().optional().nullable(),
                qualityType: z
                    .enum(["primera", "segunda", "manchada", "incompleta"])
                    .default("primera"),
                packagingType: z.enum(["caja", "bolsa"]).optional().nullable(),
                packagingUnits: z.number().int().min(0).default(0),
                piecesPerUnit: z.number().int().min(0).default(0),
                loosePieces: z.number().int().min(0).default(0),
                comments: z.string().optional().nullable(),
            }),
        )
        .min(1, "Debe incluir al menos un artículo"),
});

export async function createPackingEnhanced(data: CreatePackingInput) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = createPackingEnhancedSchema.parse(data);

        // Verificar que el cliente existe
        const customer = await db.customer.findUnique({
            where: { id: validatedData.customerId },
        });

        if (!customer) {
            return { success: false, error: "Cliente no encontrado" };
        }

        // Verificar subcliente si se proporciona
        if (validatedData.subCustomerId) {
            const subCustomer = await db.customer.findUnique({
                where: {
                    id: validatedData.subCustomerId,
                    parentId: validatedData.customerId,
                },
            });

            if (!subCustomer) {
                return { success: false, error: "Subcliente no válido" };
            }
        }

        // Verificar orden si se proporciona
        let order = null;

        if (validatedData.orderId) {
            order = await db.order.findUnique({
                where: { id: validatedData.orderId },
                include: {
                    garments: {
                        include: {
                            sizes: true,
                        },
                    },
                },
            });

            if (!order) {
                return { success: false, error: "Orden no encontrada" };
            }
        }

        // Obtener configuración de la empresa si no se proporciona
        let companyInfo = validatedData.companyInfo;

        if (!companyInfo) {
            const companySettings = await db.companySettings.findUnique({
                where: { id: "default" },
            });

            if (companySettings) {
                companyInfo = {
                    name: companySettings.companyName,
                    logo: companySettings.companyLogo,
                    rfc: companySettings.rfc,
                    address: companySettings.address,
                    city: companySettings.city,
                    state: companySettings.state,
                    postalCode: companySettings.postalCode,
                    country: companySettings.country,
                    phone: companySettings.phone,
                    email: companySettings.email,
                    website: companySettings.website,
                };
            }
        }

        // Validar disponibilidad de inventario
        for (const detail of validatedData.details) {
            const garmentSize = await db.garmentSize.findUnique({
                where: { id: detail.garmentSizeId },
                include: {
                    garment: {
                        include: {
                            model: true,
                            color: true,
                        },
                    },
                    size: true,
                },
            });

            if (!garmentSize) {
                return { success: false, error: "Producto no encontrado" };
            }

            const availableQuantity =
                garmentSize.totalQuantity - garmentSize.usedQuantity;

            if (detail.quantity > availableQuantity) {
                return {
                    success: false,
                    error: `Cantidad insuficiente para ${garmentSize.garment.model.code} ${garmentSize.garment.color.name} talla ${garmentSize.size.code}. Disponible: ${availableQuantity}`,
                };
            }

            // Llenar información del producto si no se proporciona
            if (!detail.modelCode)
                detail.modelCode = garmentSize.garment.model.code;
            if (!detail.colorName)
                detail.colorName = garmentSize.garment.color.name;
        }

        // Generar folio único
        const folio = await generatePackingFolio();

        // Obtener el estado inicial
        const initialStatus = await db.packingStatus.findFirst({
            where: { name: "Pendiente" },
        });

        if (!initialStatus) {
            return {
                success: false,
                error: "Estado inicial no configurado en el sistema",
            };
        }

        // Calcular totales
        let totalBoxes = 0;
        let totalBags = 0;
        const totalWeight = 0;
        const summaryByQuality: Record<QualityType, number> = {
            primera: 0,
            segunda: 0,
            manchada: 0,
            incompleta: 0,
        };

        validatedData.details.forEach((detail) => {
            if (detail.packagingType === "caja") {
                totalBoxes += detail.packagingUnits;
            } else if (detail.packagingType === "bolsa") {
                totalBags += detail.packagingUnits;
            }
            summaryByQuality[detail.qualityType] += detail.quantity;
        });

        // Determinar tipo de empaque
        let packingType = validatedData.packingType;

        if (!packingType) {
            if (totalBoxes > 0 && totalBags > 0) {
                packingType = "mixto";
            } else if (totalBoxes > 0) {
                packingType = "cajas";
            } else if (totalBags > 0) {
                packingType = "bolsas";
            }
        }

        // Usar transacción para crear todo atomicamente
        const packing = await db.$transaction(async (tx) => {
            // Crear el packing
            const newPacking = await tx.packing.create({
                data: {
                    folio,
                    code: folio,
                    customerId: validatedData.customerId,
                    subCustomerId: validatedData.subCustomerId,
                    orderId: validatedData.orderId,
                    deliveryDate: validatedData.deliveryDate,
                    notes: validatedData.notes,
                    transportNotes: validatedData.transportNotes,
                    statusId: initialStatus.id,
                    companyInfo: companyInfo as any,
                    totalBoxes,
                    totalBags,
                    packingType,
                    packedById: session.user.id,
                    packedAt: new Date(),
                    details: {
                        create: validatedData.details,
                    },
                    history: {
                        create: {
                            action: "CREATED",
                            metadata: {
                                createdBy: session.user.id,
                                userName: session.user.name,
                                details: validatedData.details.length,
                                totalQuantity: validatedData.details.reduce(
                                    (sum, d) => sum + d.quantity,
                                    0,
                                ),
                            },
                        },
                    },
                },
                include: {
                    customer: true,
                    subCustomer: true,
                    order: true,
                    status: true,
                    details: {
                        include: {
                            garmentSize: {
                                include: {
                                    garment: {
                                        include: {
                                            model: true,
                                            color: true,
                                        },
                                    },
                                    size: true,
                                },
                            },
                        },
                    },
                },
            });

            // Actualizar cantidades usadas
            for (const detail of validatedData.details) {
                await tx.garmentSize.update({
                    where: { id: detail.garmentSizeId },
                    data: {
                        usedQuantity: {
                            increment: detail.quantity,
                        },
                    },
                });
            }

            // Crear resumen si hay orden asociada
            if (validatedData.orderId && order) {
                const summaryData = await createPackingSummary(
                    tx,
                    newPacking.id,
                    validatedData.orderId,
                    order.cutOrder || undefined,
                    validatedData.details,
                );

                await tx.packingSummary.create({
                    data: summaryData,
                });
            }

            return newPacking;
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(
            `/dashboard/customers/${validatedData.customerId}/details`,
        );
        if (validatedData.orderId) {
            revalidatePath(`/dashboard/orders/${validatedData.orderId}`);
        }

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error creating enhanced packing:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return { success: false, error: "Error al crear el packing" };
    }
}

// Helper function to create packing summary
async function createPackingSummary(
    tx: any,
    packingId: string,
    orderId: string,
    cutOrderNumber: string | undefined,
    details: any[],
): Promise<PackingSummaryData> {
    const boxesBySize: Record<string, number> = {};
    const bagsBySize: Record<string, number> = {};
    const piecesBySize: Record<string, number> = {};

    let totalFirstQuality = 0;
    let totalSecondQuality = 0;
    let totalDefective = 0;
    let totalIncomplete = 0;
    let totalBoxes = 0;
    let totalBags = 0;
    let totalLoosePieces = 0;

    // Obtener información de tallas
    for (const detail of details) {
        const garmentSize = await tx.garmentSize.findUnique({
            where: { id: detail.garmentSizeId },
            include: { size: true },
        });

        if (!garmentSize) continue;

        const sizeCode = garmentSize.size.code;

        // Contar por calidad
        switch (detail.qualityType) {
            case "primera":
                totalFirstQuality += detail.quantity;
                break;
            case "segunda":
                totalSecondQuality += detail.quantity;
                break;
            case "manchada":
                totalDefective += detail.quantity;
                break;
            case "incompleta":
                totalIncomplete += detail.quantity;
                break;
        }

        // Contar empaque por talla
        if (detail.packagingType === "caja") {
            boxesBySize[sizeCode] =
                (boxesBySize[sizeCode] || 0) + detail.packagingUnits;
            totalBoxes += detail.packagingUnits;
        } else if (detail.packagingType === "bolsa") {
            bagsBySize[sizeCode] =
                (bagsBySize[sizeCode] || 0) + detail.packagingUnits;
            totalBags += detail.packagingUnits;
        }

        if (detail.loosePieces > 0) {
            piecesBySize[sizeCode] =
                (piecesBySize[sizeCode] || 0) + detail.loosePieces;
            totalLoosePieces += detail.loosePieces;
        }
    }

    return {
        packingId,
        orderId,
        cutOrderNumber,
        totalFirstQuality,
        totalSecondQuality,
        totalDefective,
        totalIncomplete,
        boxesBySize,
        bagsBySize,
        piecesBySize,
        totalBoxes,
        totalBags,
        totalLoosePieces,
    };
}
