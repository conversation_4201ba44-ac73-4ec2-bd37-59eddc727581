/**
 * Example API Route with Redis Cache
 * Shows how to integrate caching in Next.js API routes
 */

import { NextRequest, NextResponse } from "next/server";

import { withRedisCache } from "@/features/performance-monitoring/cache/middleware/cache-middleware";
import {
    cacheInvalidator,
    CacheTags,
} from "@/features/performance-monitoring/cache/cache-invalidator";

/**
 * GET /api/orders - Cached endpoint example
 */
export async function GET(request: NextRequest) {
    return withRedisCache(request, async () => {
        // Simulate expensive database query
        const orders = await fetchOrdersFromDatabase();

        return NextResponse.json({
            success: true,
            data: orders,
        });
    });
}

/**
 * POST /api/orders - Invalidates cache on mutation
 */
export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // Create order in database
        const newOrder = await createOrderInDatabase(body);

        // Invalidate related caches
        await cacheInvalidator.invalidateTag(CacheTags.ORDERS);

        // If customer-specific, invalidate customer cache too
        if (body.customerId) {
            await cacheInvalidator.invalidateTag(
                CacheTags.CUSTOMER(body.customerId),
            );
        }

        return NextResponse.json({
            success: true,
            data: newOrder,
        });
    } catch (error) {
        return NextResponse.json(
            { success: false, error: "Failed to create order" },
            { status: 500 },
        );
    }
}

// Mock functions for example
async function fetchOrdersFromDatabase() {
    // Simulate database query
    return [
        { id: "1", customer: "Customer A", total: 100 },
        { id: "2", customer: "Customer B", total: 200 },
    ];
}

async function createOrderInDatabase(data: any) {
    // Simulate database insert
    return { id: "3", ...data };
}
