/**
 * Simple hash function for consistent user bucketing
 * @param str - String to hash
 * @returns Hash code
 */
export function hashCode(str: string): number {
    let hash = 0;

    if (str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);

        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
    }

    return hash;
}

/**
 * Determines if a user should be included in a feature rollout
 * @param userId - Unique user identifier
 * @param feature - Feature name
 * @param percentage - Rollout percentage (0-100)
 * @returns Whether the user is in the rollout
 */
export function isUserInRollout(
    userId: string,
    feature: string,
    percentage: number,
): boolean {
    if (percentage >= 100) return true;
    if (percentage <= 0) return false;

    // Consistent hashing ensures same user always gets same result
    const hash = hashCode(`${userId}-${feature}`);
    const bucket = Math.abs(hash) % 100;

    return bucket < percentage;
}

/**
 * Get a stable bucket for A/B testing
 * @param userId - User identifier
 * @param experiment - Experiment name
 * @param buckets - Number of buckets
 * @returns Bucket number (0 to buckets-1)
 */
export function getUserBucket(
    userId: string,
    experiment: string,
    buckets: number = 2,
): number {
    const hash = hashCode(`${userId}-${experiment}`);

    return Math.abs(hash) % buckets;
}
