"use client";

import React, { useState } from "react";
import {
    Card,
    CardBody,
    Avatar,
    Button,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
} from "@heroui/react";
import {
    EllipsisVerticalIcon,
    PencilIcon,
    TrashIcon,
    ChatBubbleLeftIcon,
    ChevronDownIcon,
    ChevronUpIcon,
} from "@heroicons/react/24/outline";

import { getUserAvatarColor } from "@/shared/utils/avatar-colors";

import { TimeAgo } from "./TimeAgo";
import { CommentForm } from "./CommentForm";

interface Comment {
    id: string;
    content: string;
    createdAt: string | Date;
    updatedAt: string | Date;
    authorId: string;
    author: {
        id: string;
        name: string;
        email: string;
        image?: string | null;
    };
    replies?: Comment[];
}

interface CommentCardProps {
    comment: Comment;
    currentUserId?: string;
    userRole?: string;
    onUpdate: (commentId: string, content: string) => Promise<void>;
    onDelete: (commentId: string) => Promise<void>;
    onReply: (parentId: string, content: string) => Promise<void>;
    isReply?: boolean;
}

export function CommentCard({
    comment,
    currentUserId,
    userRole,
    onUpdate,
    onDelete,
    onReply,
    isReply = false,
}: CommentCardProps) {
    const [isEditing, setIsEditing] = useState(false);
    const [isReplying, setIsReplying] = useState(false);
    const [showReplies, setShowReplies] = useState(true);
    const [isDeleting, setIsDeleting] = useState(false);

    const isAuthor = comment.authorId === currentUserId;
    const canEdit = isAuthor || userRole === "ADMIN";
    const canDelete = userRole === "ADMIN";

    const userColor = getUserAvatarColor(comment.authorId);

    const handleUpdate = async (content: string) => {
        await onUpdate(comment.id, content);
        setIsEditing(false);
    };

    const handleDelete = async () => {
        if (
            !confirm("¿Estás seguro de que quieres eliminar este comentario?")
        ) {
            return;
        }
        setIsDeleting(true);
        try {
            await onDelete(comment.id);
        } finally {
            setIsDeleting(false);
        }
    };

    const handleReply = async (content: string) => {
        await onReply(comment.id, content);
        setIsReplying(false);
        setShowReplies(true);
    };

    return (
        <div className={`${isReply ? "ml-12" : ""}`}>
            <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardBody className="p-4">
                    <div className="flex items-start gap-3">
                        <Avatar
                            fallback={
                                <div
                                    className="font-semibold w-full h-full flex items-center justify-center"
                                    style={{
                                        backgroundColor: userColor.bg,
                                        color: userColor.text,
                                    }}
                                >
                                    {comment.author.name
                                        .charAt(0)
                                        .toUpperCase()}
                                </div>
                            }
                            name={comment.author.name}
                            size="sm"
                            src={comment.author.image || undefined}
                        />

                        <div className="flex-1">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium">
                                        {comment.author.name}
                                        {isAuthor && (
                                            <span className="ml-2 text-xs text-gray-500">
                                                (Tú)
                                            </span>
                                        )}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        <TimeAgo date={comment.createdAt} />
                                        {comment.updatedAt !==
                                            comment.createdAt && (
                                            <span className="ml-1">
                                                (editado)
                                            </span>
                                        )}
                                    </p>
                                </div>

                                {(canEdit || canDelete) && (
                                    <Dropdown>
                                        <DropdownTrigger>
                                            <Button
                                                isIconOnly
                                                className="text-gray-500"
                                                size="sm"
                                                variant="light"
                                            >
                                                <EllipsisVerticalIcon className="w-4 h-4" />
                                            </Button>
                                        </DropdownTrigger>
                                        <DropdownMenu>
                                            {canEdit ? (
                                                <DropdownItem
                                                    key="edit"
                                                    startContent={
                                                        <PencilIcon className="w-4 h-4" />
                                                    }
                                                    onClick={() =>
                                                        setIsEditing(true)
                                                    }
                                                >
                                                    Editar
                                                </DropdownItem>
                                            ) : null}
                                            {canDelete ? (
                                                <DropdownItem
                                                    key="delete"
                                                    className="text-danger"
                                                    color="danger"
                                                    startContent={
                                                        <TrashIcon className="w-4 h-4" />
                                                    }
                                                    onClick={handleDelete}
                                                >
                                                    Eliminar
                                                </DropdownItem>
                                            ) : null}
                                        </DropdownMenu>
                                    </Dropdown>
                                )}
                            </div>

                            {isEditing ? (
                                <div className="mt-2">
                                    <CommentForm
                                        initialContent={comment.content}
                                        placeholder="Edita tu comentario..."
                                        submitLabel="Actualizar"
                                        onCancel={() => setIsEditing(false)}
                                        onSubmit={handleUpdate}
                                    />
                                </div>
                            ) : (
                                <p className="mt-2 text-sm whitespace-pre-wrap">
                                    {comment.content}
                                </p>
                            )}

                            {!isReply && (
                                <div className="mt-2 flex items-center gap-2">
                                    <Button
                                        size="sm"
                                        startContent={
                                            <ChatBubbleLeftIcon className="w-4 h-4" />
                                        }
                                        variant="light"
                                        onClick={() =>
                                            setIsReplying(!isReplying)
                                        }
                                    >
                                        Responder
                                    </Button>

                                    {comment.replies &&
                                        comment.replies.length > 0 && (
                                            <Button
                                                endContent={
                                                    showReplies ? (
                                                        <ChevronUpIcon className="w-4 h-4" />
                                                    ) : (
                                                        <ChevronDownIcon className="w-4 h-4" />
                                                    )
                                                }
                                                size="sm"
                                                variant="light"
                                                onClick={() =>
                                                    setShowReplies(!showReplies)
                                                }
                                            >
                                                {comment.replies.length}{" "}
                                                {comment.replies.length === 1
                                                    ? "respuesta"
                                                    : "respuestas"}
                                            </Button>
                                        )}
                                </div>
                            )}

                            {isReplying && (
                                <div className="mt-3">
                                    <CommentForm
                                        placeholder={`Responder a ${comment.author.name}...`}
                                        onCancel={() => setIsReplying(false)}
                                        onSubmit={handleReply}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </CardBody>
            </Card>

            {showReplies && comment.replies && comment.replies.length > 0 && (
                <div className="mt-2 space-y-2">
                    {comment.replies.map((reply) => (
                        <CommentCard
                            key={reply.id}
                            isReply
                            comment={reply}
                            currentUserId={currentUserId}
                            userRole={userRole}
                            onDelete={onDelete}
                            onReply={onReply}
                            onUpdate={onUpdate}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}
