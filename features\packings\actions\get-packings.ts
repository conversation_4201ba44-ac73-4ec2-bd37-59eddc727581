"use server";

import { db } from "@/lib/db";

interface GetPackingsParams {
    customerId?: string;
    subCustomerId?: string;
    statusId?: string;
    search?: string;
    page?: number;
    limit?: number;
}

export async function getPackings(params: GetPackingsParams = {}) {
    try {
        const {
            customerId,
            subCustomerId,
            statusId,
            search,
            page = 1,
            limit = 10,
        } = params;

        const where: any = {};

        if (customerId) {
            where.customerId = customerId;
        }

        if (subCustomerId) {
            where.subCustomerId = subCustomerId;
        }

        if (statusId) {
            where.statusId = statusId;
        }

        if (search) {
            where.OR = [
                { folio: { contains: search, mode: "insensitive" } },
                { notes: { contains: search, mode: "insensitive" } },
                {
                    customer: {
                        name: { contains: search, mode: "insensitive" },
                    },
                },
                {
                    subCustomer: {
                        name: { contains: search, mode: "insensitive" },
                    },
                },
            ];
        }

        const [packings, total] = await Promise.all([
            db.packing.findMany({
                where,
                include: {
                    customer: true,
                    subCustomer: true,
                    order: {
                        include: {
                            customer: true,
                        },
                    },
                    status: true,
                    details: {
                        include: {
                            garmentSize: {
                                include: {
                                    garment: {
                                        include: {
                                            model: true,
                                            color: true,
                                        },
                                    },
                                    size: true,
                                },
                            },
                        },
                    },
                    _count: {
                        select: {
                            details: true,
                            history: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                skip: (page - 1) * limit,
                take: limit,
            }),
            db.packing.count({ where }),
        ]);

        return {
            success: true,
            data: {
                packings,
                pagination: {
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                },
            },
        };
    } catch (error) {
        console.error("Error fetching packings:", error);

        return { success: false, error: "Error al obtener los packings" };
    }
}

export async function getPackingById(id: string) {
    try {
        const packing = await db.packing.findUnique({
            where: { id },
            include: {
                customer: true,
                subCustomer: true,
                order: {
                    include: {
                        customer: true,
                        parts: true,
                    },
                },
                status: true,
                details: {
                    include: {
                        garmentSize: {
                            include: {
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                                size: true,
                            },
                        },
                    },
                },
                history: {
                    orderBy: {
                        timestamp: "desc",
                    },
                },
            },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error fetching packing:", error);

        return { success: false, error: "Error al obtener el packing" };
    }
}

export async function getPackingsByCustomer(
    customerId: string,
    includeSubCustomers = true,
) {
    try {
        const where: any = includeSubCustomers
            ? {
                  OR: [{ customerId }, { customer: { parentId: customerId } }],
              }
            : { customerId };

        const packings = await db.packing.findMany({
            where,
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                _count: {
                    select: {
                        details: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return { success: true, data: packings };
    } catch (error) {
        console.error("Error fetching customer packings:", error);

        return {
            success: false,
            error: "Error al obtener los packings del cliente",
        };
    }
}
