/**
 * Delete Size Use Case
 *
 * Business logic for deleting sizes.
 * Supports both soft delete and hard delete operations.
 */

// import { injectable, inject } from "tsyringe"; // Commented out - tsyringe not installed

import { SizeRepositoryInterface } from "../../domain/repositories/size.repository.interface";
import { SuccessResponseDTO } from "../dto/size.dto";

export interface DeleteSizeCommand {
    id: string;
    userId: string;
    hardDelete?: boolean;
}

export interface DeleteSizeResult {
    success: boolean;
    data?: SuccessResponseDTO;
    error?: string;
}

// @injectable() // Commented out - tsyringe not installed
export class DeleteSizeUseCase {
    constructor(
        // @inject("SizeRepository") // Commented out - tsyringe not installed
        private readonly sizeRepository: SizeRepositoryInterface,
    ) {}

    async execute(command: DeleteSizeCommand): Promise<DeleteSizeResult> {
        try {
            // 1. Validate command
            if (!command.id || !command.userId) {
                return {
                    success: false,
                    error: "Invalid command: missing required data",
                };
            }

            // 2. Find existing size
            const existingSize = await this.sizeRepository.findById(command.id);

            if (!existingSize) {
                return {
                    success: false,
                    error: "Size not found",
                };
            }

            // 3. Check if already deleted (for soft delete)
            if (!command.hardDelete && existingSize.deletedAt) {
                return {
                    success: false,
                    error: "Size is already deleted",
                };
            }

            // 4. Apply business rules
            // TODO: Check if size is being used in active orders
            // This would require checking with OrderRepository
            // For now, we'll allow deletion

            // 5. Perform deletion
            let deleted: boolean;

            if (command.hardDelete) {
                // Hard delete - permanently remove from database
                deleted = await this.sizeRepository.hardDelete(command.id);
            } else {
                // Soft delete - mark as deleted
                deleted = await this.sizeRepository.delete(command.id);
            }

            if (!deleted) {
                return {
                    success: false,
                    error: "Failed to delete size",
                };
            }

            // 6. Return success result
            return {
                success: true,
                data: {
                    success: true,
                    message: command.hardDelete
                        ? "Size permanently deleted"
                        : "Size deleted successfully",
                },
            };
        } catch (error) {
            // Log error for monitoring
            console.error("DeleteSizeUseCase error:", error);

            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Restore a soft-deleted size
     */
    async restore(id: string, userId: string): Promise<DeleteSizeResult> {
        try {
            // 1. Find the size
            const size = await this.sizeRepository.findById(id);

            if (!size) {
                return {
                    success: false,
                    error: "Size not found",
                };
            }

            // 2. Check if it's actually deleted
            if (!size.deletedAt) {
                return {
                    success: false,
                    error: "Size is not deleted",
                };
            }

            // 3. Restore the size
            size.restore();

            // 4. Save changes
            await this.sizeRepository.save(size);

            return {
                success: true,
                data: {
                    success: true,
                    message: "Size restored successfully",
                },
            };
        } catch (error) {
            console.error("RestoreSizeUseCase error:", error);

            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Delete multiple sizes in bulk
     */
    async deleteBulk(
        ids: string[],
        userId: string,
        hardDelete: boolean = false,
    ): Promise<{
        success: boolean;
        deleted: string[];
        failed: Array<{ id: string; error: string }>;
    }> {
        const deleted: string[] = [];
        const failed: Array<{ id: string; error: string }> = [];

        // Begin transaction
        await this.sizeRepository.beginTransaction();

        try {
            for (const id of ids) {
                const result = await this.execute({
                    id,
                    userId,
                    hardDelete,
                });

                if (result.success) {
                    deleted.push(id);
                } else {
                    failed.push({
                        id,
                        error: result.error || "Unknown error",
                    });
                }
            }

            // Commit if all successful
            if (failed.length === 0) {
                await this.sizeRepository.commit();
            } else {
                // Rollback if any failed
                await this.sizeRepository.rollback();

                return {
                    success: false,
                    deleted: [],
                    failed,
                };
            }

            return {
                success: true,
                deleted,
                failed: [],
            };
        } catch (error) {
            await this.sizeRepository.rollback();
            throw error;
        }
    }
}
