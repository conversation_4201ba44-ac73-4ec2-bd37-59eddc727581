"use client";

import { But<PERSON> } from "@heroui/react";
import { useState } from "react";
import { signIn } from "next-auth/react";

export function WebAuthnButton() {
    const [isRegistering, setIsRegistering] = useState(false);

    const handleWebAuthn = async () => {
        try {
            setIsRegistering(true);

            // Verificar si el navegador soporta WebAuthn
            if (!window.PublicKeyCredential) {
                throw new Error("WebAuthn no está soportado en este navegador");
            }

            // Iniciar el proceso de autenticación/registro
            await signIn("credentials", {
                callbackUrl: "/dashboard",
                webauthn: true,
            });
        } catch (error) {
            // REMOVED: console.error("Error con WebAuthn:", error);
        } finally {
            setIsRegistering(false);
        }
    };

    return (
        <Button
            className="w-full"
            color="primary"
            disabled={isRegistering}
            variant="flat"
            onClick={handleWebAuthn}
        >
            {isRegistering ? "Procesando..." : "Usar autenticación biométrica"}
        </Button>
    );
}
