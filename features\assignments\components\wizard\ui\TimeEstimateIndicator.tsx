"use client";

import React from "react";
import { Chip } from "@heroui/react";
import { ClockIcon } from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { useEstimatedTime } from "@/app/dashboard/assignments/new/wizard-context";

import { ContextualTooltip, tooltipContexts } from "./ContextualTooltip";

export function TimeEstimateIndicator() {
    const { min, max, formatted } = useEstimatedTime();

    // No mostrar si no hay estimación
    if (min === 0 && max === 0) return null;

    return (
        <AnimatePresence>
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="fixed top-20 right-4 z-40"
                exit={{ opacity: 0, y: -10 }}
                initial={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
            >
                <Chip
                    className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                    endContent={
                        <ContextualTooltip
                            content={tooltipContexts.timeEstimate.text}
                            variant={tooltipContexts.timeEstimate.variant}
                        />
                    }
                    startContent={
                        <motion.div
                            animate={{ opacity: [1, 0.5, 1] }}
                            transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                            }}
                        >
                            <ClockIcon className="w-4 h-4" />
                        </motion.div>
                    }
                    variant="flat"
                >
                    {formatted}
                </Chip>
            </motion.div>
        </AnimatePresence>
    );
}
