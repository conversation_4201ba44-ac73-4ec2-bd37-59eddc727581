import { useEffect, useState, useRef, useCallback, useMemo } from "react";

import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";
import { Order } from "@/features/orders/types/orders";

import { useOrder } from "./useOrder";
import { OrdersState } from "./useOrdersState";

// ID para seguimiento de peticiones del calendario para evitar race conditions
let lastCalendarRequestId = "";

/**
 * Hook para gestionar datos de órdenes de manera centralizada
 * @param params Parámetros de búsqueda y filtrado
 */
export function useOrdersData(params: OrdersState) {
    // Estado para el caché local de datos
    const [cachedOrders, setCachedOrders] = useState<Order[]>([]);
    const [isCacheValid, setIsCacheValid] = useState(false);
    const debounceRef = useRef<NodeJS.Timeout | null>(null);
    const lastParamsRef = useRef<OrdersState | null>(null);
    const isCalendarView = useMemo(
        () => params.view === "calendar",
        [params.view],
    );

    // Reutilizar el hook existente con una estructura más clara y optimizada
    const {
        orders,
        orderStatuses,
        isLoading,
        mutate,
        deleteOrder,
        verifyAndCreateParts,
        fetchOrder,
        prefetchNextPage,
    } = useOrder(
        {
            statusId: params.status || undefined,
            contractorId: params.contractor || undefined,
            search: params.search || undefined,
            page: params.page,
            limit: params.limit,
            sortBy: params.sortBy,
            sortDirection:
                params.sortDirection === "ascending" ? "asc" : "desc",
        },
        isCalendarView,
    ); // Pasar indicador de vista calendario para optimizar comportamiento

    // Listener de revalidación para actualizaciones en tiempo real
    // Parámetros estables para evitar cambios en la ejecución de hooks
    const revalidationOptions = useMemo(
        () => ({
            throttleTime: isCalendarView ? 3000 : 500, // Mayor throttling para calendario
            revalidateAllKeys: !isCalendarView, // No revalidar todas las claves en calendario
            enabled: true, // Always enabled, but will be throttled based on view
        }),
        [isCalendarView],
    );

    const { isRevalidating } = useRevalidationListener(
        "orders",
        undefined,
        revalidationOptions,
    );

    // Determinar si hubo cambio significativo en parámetros de búsqueda
    const hasSignificantParamChange = useCallback(() => {
        if (!lastParamsRef.current) return true;

        // Considerar cambios significativos que requieren nueva consulta
        return (
            params.status !== lastParamsRef.current.status ||
            params.search !== lastParamsRef.current.search ||
            params.sortBy !== lastParamsRef.current.sortBy ||
            params.sortDirection !== lastParamsRef.current.sortDirection
        );
    }, [params]);

    // Actualizar caché cuando llegan datos nuevos
    useEffect(() => {
        // Only process if orders is defined (not null or undefined)
        if (orders !== null && orders !== undefined) {
            if (orders.length > 0) {
                setCachedOrders(orders);
                setIsCacheValid(true);
                lastParamsRef.current = { ...params };

                // Precargar siguiente página automáticamente SOLO si no estamos en vista calendario
                if (!isLoading && !isRevalidating && !isCalendarView) {
                    prefetchNextPage();
                }
            } else if (
                Array.isArray(orders) &&
                orders.length === 0 &&
                !isLoading
            ) {
                // Si recibimos un array vacío y no estamos cargando, es válido
                setCachedOrders([]);
                setIsCacheValid(true);
                lastParamsRef.current = { ...params };
            }
        }
    }, [orders, isLoading, isRevalidating, prefetchNextPage, isCalendarView]);

    // Gestionar mutate inteligentemente con debounce y caché
    useEffect(() => {
        if (typeof mutate !== "function") return;

        // En la vista de calendario, evitamos cualquier mutación automática a menos que sea
        // un cambio muy significativo para evitar revalidaciones excesivas
        if (isCalendarView && isCacheValid) {
            // Guardar los parámetros actuales para referencia pero no hacer nada más
            lastParamsRef.current = { ...params };

            return;
        }

        // Siempre actualizar referencia de últimos parámetros usados
        const currentParams = { ...params };

        // Si hay un cambio de página simple y tenemos caché válido, usamos el prefetch
        if (
            isCacheValid &&
            !hasSignificantParamChange() &&
            lastParamsRef.current &&
            params.page !== lastParamsRef.current.page
        ) {
            // El prefetch ya está gestionado, solo actualizamos la referencia
            lastParamsRef.current = currentParams;

            return;
        }

        // Limpiar debounce anterior
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
            debounceRef.current = null;
        }

        // Evitar búsquedas ineficientes
        if (params.search !== "" && params.search.length < 2) return;

        // Generar ID único para rastrear esta petición
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

        lastCalendarRequestId = requestId;

        // Aplicar debounce para cambios de búsqueda
        // Usar debounce mucho más agresivo para la vista de calendario
        const debounceTime = isCalendarView
            ? 1500 // Debounce muy alto para calendario (1.5 segundos)
            : params.search
              ? 400
              : 200; // Debounce normal para otras vistas

        // REMOVED: console.log(debounceTime);

        // Revalidar datos con debounce
        debounceRef.current = setTimeout(() => {
            // Solo proceder si esta es la petición más reciente (evitar race conditions)
            if (lastCalendarRequestId === requestId || !isCalendarView) {
                lastParamsRef.current = currentParams;
                mutate();
            } else if (process.env.NODE_ENV !== "production") {
                // Petición cancelada por una más reciente - comentario removido
            }
        }, debounceTime);

        return () => {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, [mutate, params, isCacheValid, hasSignificantParamChange]);

    // Detectar señal de revalidación forzada (después de una edición)
    useEffect(() => {
        // Verificar si venimos de una edición que requiere revalidación forzada
        if (typeof window !== "undefined") {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get("revalidated") === "true") {
                // Detectada señal de revalidación forzada después de edición - comentario removido

                // Invalidar caché local
                setIsCacheValid(false);
                setCachedOrders([]);

                // Forzar revalidación inmediata
                if (typeof mutate === "function") {
                    mutate();
                }

                // Limpiar el parámetro de URL
                const newUrl = window.location.pathname;

                window.history.replaceState({}, "", newUrl);
            }
        }
    }, [mutate]);

    // API estructurada para mejor claridad y rendimiento
    const result = {
        data: {
            // Usar datos en caché cuando están disponibles para mejor responsive UI
            orders:
                isLoading && isCacheValid && cachedOrders.length > 0
                    ? cachedOrders
                    : orders || [],
            orderStatuses: orderStatuses || [],
        },
        status: {
            isLoading,
            isRevalidating,
            isCached: isCacheValid && cachedOrders.length > 0,
        },
        operations: {
            mutate,
            deleteOrder,
            verifyAndCreateParts,
            fetchOrder,
            prefetchNextPage,
            // Operaciones de alto nivel que abstraen detalles
            handleDelete: async (orderId: string) => {
                if (window.confirm("¿Estás seguro de eliminar esta orden?")) {
                    try {
                        await deleteOrder(orderId);
                        if (typeof mutate === "function") {
                            mutate();
                        }

                        return true;
                    } catch (error) {
                        // REMOVED: console.error("Error al eliminar orden:", error);

                        return false;
                    }
                }

                return false;
            },
        },
    };

    return useMemo(
        () => result,
        [
            orders,
            orderStatuses,
            isLoading,
            isRevalidating,
            isCacheValid,
            cachedOrders,
            mutate,
            deleteOrder,
            verifyAndCreateParts,
            fetchOrder,
            prefetchNextPage,
        ],
    );
}
