"use server";

import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Valida si un nombre de color es único
 */
export async function validateColorUnique(
    data: { name?: string; hexCode?: string },
    excludeId?: string,
) {
    if (!data.name && !data.hexCode) {
        return { success: true, data: { isValid: false } };
    }

    return await handleDbError(async () => {
        const existingColor = await db.color.findFirst({
            where: {
                name: data.name
                    ? {
                          equals: data.name,
                          mode: "insensitive" as Prisma.QueryMode,
                      }
                    : undefined,
                id: excludeId ? { not: excludeId } : undefined,
            },
        });

        return {
            isValid: !existingColor,
            existingField: existingColor ? "name" : null,
        };
    }, "Error al validar color");
}
