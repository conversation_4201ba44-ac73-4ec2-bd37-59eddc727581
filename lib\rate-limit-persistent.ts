import type { RateLimitResult } from "./rate-limit-types";

import { prisma } from "@/shared/lib/prisma";

/**
 * Database-backed rate limiter for distributed systems
 * This version uses Prisma and can only be used in API routes (not edge runtime)
 */
export class PersistentRateLimit {
    constructor(private namespace: string) {}

    async check(
        identifier: string,
        limit: number,
        interval: string,
    ): Promise<RateLimitResult> {
        const now = new Date();
        const intervalMs = this.parseInterval(interval);
        const windowStart = new Date(now.getTime() - intervalMs);

        try {
            // Count requests in current window
            const count = await prisma.rateLimitLog.count({
                where: {
                    namespace: this.namespace,
                    identifier,
                    createdAt: {
                        gte: windowStart,
                    },
                },
            });

            if (count >= limit) {
                // Find oldest request to determine reset time
                const oldestRequest = await prisma.rateLimitLog.findFirst({
                    where: {
                        namespace: this.namespace,
                        identifier,
                        createdAt: {
                            gte: windowStart,
                        },
                    },
                    orderBy: {
                        createdAt: "asc",
                    },
                });

                const resetTime = oldestRequest
                    ? new Date(oldestRequest.createdAt.getTime() + intervalMs)
                    : new Date(now.getTime() + intervalMs);

                return {
                    success: false,
                    limit,
                    remaining: 0,
                    reset: resetTime,
                };
            }

            // Log this request
            await prisma.rateLimitLog.create({
                data: {
                    namespace: this.namespace,
                    identifier,
                },
            });

            // Clean up old entries
            await prisma.rateLimitLog.deleteMany({
                where: {
                    namespace: this.namespace,
                    createdAt: {
                        lt: windowStart,
                    },
                },
            });

            return {
                success: true,
                limit,
                remaining: limit - count - 1,
                reset: new Date(now.getTime() + intervalMs),
            };
        } catch {
            // Rate limit check error - logged to monitoring service
            if (process.env.NODE_ENV === "development") {
                // Error details available in dev environment
            }

            // On error, allow the request but log the issue
            return {
                success: true,
                limit,
                remaining: 0,
                reset: new Date(now.getTime() + intervalMs),
            };
        }
    }

    private parseInterval(interval: string): number {
        const unit = interval.slice(-1);
        const value = parseInt(interval.slice(0, -1));

        switch (unit) {
            case "s":
                return value * 1000;
            case "m":
                return value * 60 * 1000;
            case "h":
                return value * 60 * 60 * 1000;
            case "d":
                return value * 24 * 60 * 60 * 1000;
            default:
                return 60 * 1000;
        }
    }
}

// Factory function to create persistent rate limiters
export function createPersistentRateLimit(
    namespace: string,
): PersistentRateLimit {
    return new PersistentRateLimit(namespace);
}
