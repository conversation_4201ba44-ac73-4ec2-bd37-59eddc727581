"use client";

import React from "react";
import {
    PlusIcon,
    TrashIcon,
    ShoppingBagIcon,
    CubeIcon,
    SwatchIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import {
    Button,
    Input,
    Chip,
    Card,
    CardBody,
    Autocomplete,
    AutocompleteItem,
} from "@/shared/components/ui/hero-ui-client";
import { IconWrapper } from "@/shared/components/ui/IconWrapper";

interface Size {
    sizeId: string;
    quantity: string;
}

interface Garment {
    modelId: string;
    colorId: string;
    sizes: Size[];
}

interface OrderGarmentsStepProps {
    garments: Garment[];
    models: Array<{ id: string; code: string; description?: string }>;
    colors: Array<{ id: string; name: string; code: string }>;
    sizesOptions: Array<{ id: string; code: string }>;
    errors: Record<string, string>;
    onAddGarment: () => void;
    onRemoveGarment: (index: number) => void;
    onGarmentChange: (index: number, field: string, value: string) => void;
    onAddSize: (garmentIndex: number) => void;
    onRemoveSize: (garmentIndex: number, sizeIndex: number) => void;
    onSizeChange: (
        garmentIndex: number,
        sizeIndex: number,
        field: string,
        value: string,
    ) => void;
}

export function OrderGarmentsStep({
    garments,
    models,
    colors,
    sizesOptions,
    errors,
    onAddGarment,
    onRemoveGarment,
    onGarmentChange,
    onAddSize,
    onRemoveSize,
    onSizeChange,
}: OrderGarmentsStepProps) {
    // Calculate total pieces
    const totalPieces = garments.reduce((total, garment) => {
        const garmentTotal = garment.sizes.reduce((sum, size) => {
            const qty = parseInt(size.quantity) || 0;

            return sum + qty;
        }, 0);

        return total + garmentTotal;
    }, 0);

    return (
        <div className="space-y-4">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                    Configura las prendas de la orden. Selecciona modelo, color
                    y especifica las cantidades por talla.
                </p>
            </div>

            {/* Error Message */}
            {errors.garments && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <p className="text-sm text-red-700 dark:text-red-300">
                        {errors.garments}
                    </p>
                </div>
            )}

            {/* Garments List */}
            <div className="space-y-4">
                <AnimatePresence>
                    {garments.map((garment, gIndex) => (
                        <motion.div
                            key={gIndex}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, x: -100 }}
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ duration: 0.3 }}
                        >
                            <Card className="border border-gray-200 dark:border-gray-700">
                                <CardBody className="p-5">
                                    {/* Header */}
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center gap-2">
                                            <IconWrapper
                                                color="primary"
                                                size="sm"
                                                variant="soft"
                                            >
                                                <ShoppingBagIcon className="w-4 h-4" />
                                            </IconWrapper>
                                            <h3 className="font-semibold">
                                                Prenda {gIndex + 1}
                                            </h3>
                                        </div>
                                        <Button
                                            isIconOnly
                                            color="danger"
                                            isDisabled={garments.length === 1}
                                            size="sm"
                                            variant="light"
                                            onPress={() =>
                                                onRemoveGarment(gIndex)
                                            }
                                        >
                                            <TrashIcon className="w-4 h-4" />
                                        </Button>
                                    </div>

                                    {/* Model and Color Selection */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                        <Autocomplete
                                            defaultItems={models}
                                            errorMessage={
                                                errors[
                                                    `garment_${gIndex}_model`
                                                ]
                                            }
                                            label="Modelo"
                                            placeholder="Buscar modelo..."
                                            selectedKey={garment.modelId}
                                            startContent={
                                                <CubeIcon className="w-4 h-4 text-gray-400" />
                                            }
                                            variant="bordered"
                                            onSelectionChange={(value) =>
                                                onGarmentChange(
                                                    gIndex,
                                                    "modelId",
                                                    value as string,
                                                )
                                            }
                                        >
                                            {(model) => (
                                                <AutocompleteItem
                                                    key={model.id}
                                                    textValue={`${model.code} - ${model.description || ""}`}
                                                >
                                                    <div className="flex items-center justify-between gap-2">
                                                        <span className="font-medium">
                                                            {model.code}
                                                        </span>
                                                        {model.description && (
                                                            <span className="text-xs text-gray-500 truncate">
                                                                {
                                                                    model.description
                                                                }
                                                            </span>
                                                        )}
                                                    </div>
                                                </AutocompleteItem>
                                            )}
                                        </Autocomplete>

                                        <Autocomplete
                                            defaultItems={colors}
                                            errorMessage={
                                                errors[
                                                    `garment_${gIndex}_color`
                                                ]
                                            }
                                            label="Color"
                                            placeholder="Buscar color..."
                                            selectedKey={garment.colorId}
                                            startContent={
                                                <SwatchIcon className="w-4 h-4 text-gray-400" />
                                            }
                                            variant="bordered"
                                            onSelectionChange={(value) =>
                                                onGarmentChange(
                                                    gIndex,
                                                    "colorId",
                                                    value as string,
                                                )
                                            }
                                        >
                                            {(color) => (
                                                <AutocompleteItem
                                                    key={color.id}
                                                    textValue={color.name}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <div
                                                            className="w-4 h-4 rounded border border-gray-300"
                                                            style={{
                                                                backgroundColor:
                                                                    color.code,
                                                            }}
                                                        />
                                                        <span>
                                                            {color.name}
                                                        </span>
                                                    </div>
                                                </AutocompleteItem>
                                            )}
                                        </Autocomplete>
                                    </div>

                                    {/* Sizes */}
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Tallas y Cantidades
                                            </h4>
                                            {errors[
                                                `garment_${gIndex}_sizes`
                                            ] && (
                                                <span className="text-xs text-red-600">
                                                    {
                                                        errors[
                                                            `garment_${gIndex}_sizes`
                                                        ]
                                                    }
                                                </span>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                            {garment.sizes.map(
                                                (size, sIndex) => (
                                                    <div
                                                        key={sIndex}
                                                        className="flex items-center gap-2"
                                                    >
                                                        <div className="flex-1">
                                                            <Autocomplete
                                                                defaultItems={
                                                                    sizesOptions
                                                                }
                                                                errorMessage={
                                                                    errors[
                                                                        `garment_${gIndex}_size_${sIndex}_id`
                                                                    ]
                                                                }
                                                                placeholder="Talla"
                                                                selectedKey={
                                                                    size.sizeId
                                                                }
                                                                size="sm"
                                                                variant="bordered"
                                                                onSelectionChange={(
                                                                    value,
                                                                ) =>
                                                                    onSizeChange(
                                                                        gIndex,
                                                                        sIndex,
                                                                        "sizeId",
                                                                        value as string,
                                                                    )
                                                                }
                                                            >
                                                                {(sizeOpt) => (
                                                                    <AutocompleteItem
                                                                        key={
                                                                            sizeOpt.id
                                                                        }
                                                                    >
                                                                        {
                                                                            sizeOpt.code
                                                                        }
                                                                    </AutocompleteItem>
                                                                )}
                                                            </Autocomplete>
                                                        </div>

                                                        <Input
                                                            className="w-24"
                                                            errorMessage={
                                                                errors[
                                                                    `garment_${gIndex}_size_${sIndex}_qty`
                                                                ]
                                                            }
                                                            min="0"
                                                            placeholder="Cant."
                                                            size="sm"
                                                            type="number"
                                                            value={
                                                                size.quantity
                                                            }
                                                            variant="bordered"
                                                            onChange={(e) =>
                                                                onSizeChange(
                                                                    gIndex,
                                                                    sIndex,
                                                                    "quantity",
                                                                    e.target
                                                                        .value,
                                                                )
                                                            }
                                                        />

                                                        <Button
                                                            isIconOnly
                                                            color="danger"
                                                            isDisabled={
                                                                garment.sizes
                                                                    .length ===
                                                                1
                                                            }
                                                            size="sm"
                                                            variant="light"
                                                            onPress={() =>
                                                                onRemoveSize(
                                                                    gIndex,
                                                                    sIndex,
                                                                )
                                                            }
                                                        >
                                                            <TrashIcon className="w-3 h-3" />
                                                        </Button>
                                                    </div>
                                                ),
                                            )}
                                        </div>

                                        <Button
                                            className="mt-2"
                                            size="sm"
                                            startContent={
                                                <PlusIcon className="w-3 h-3" />
                                            }
                                            variant="flat"
                                            onPress={() => onAddSize(gIndex)}
                                        >
                                            Agregar Talla
                                        </Button>
                                    </div>

                                    {/* Garment Total */}
                                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                                Total prenda:
                                            </span>
                                            <Chip
                                                color="primary"
                                                size="sm"
                                                variant="flat"
                                            >
                                                {garment.sizes.reduce(
                                                    (sum, size) =>
                                                        sum +
                                                        (parseInt(
                                                            size.quantity,
                                                        ) || 0),
                                                    0,
                                                )}{" "}
                                                piezas
                                            </Chip>
                                        </div>
                                    </div>
                                </CardBody>
                            </Card>
                        </motion.div>
                    ))}
                </AnimatePresence>
            </div>

            {/* Add Garment Button */}
            <div className="flex justify-center pt-4">
                <Button
                    className="border-dashed"
                    startContent={<PlusIcon className="w-4 h-4" />}
                    variant="bordered"
                    onPress={onAddGarment}
                >
                    Agregar Prenda
                </Button>
            </div>

            {/* Total Summary */}
            <div className="mt-6 p-4 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-lg">
                <div className="flex items-center justify-between">
                    <span className="font-medium text-primary">
                        Total de piezas en la orden:
                    </span>
                    <Chip color="primary" size="lg" variant="solid">
                        {totalPieces} piezas
                    </Chip>
                </div>
            </div>
        </div>
    );
}
