/**
 * Constantes relacionadas con la API y endpoints
 * Centraliza las URLs y configuraciones de API
 */

// Base URLs para diferentes entornos
export const API_BASE_URL = {
    development: "/api",
    production: "/api",
    test: "/api",
};

// Endpoints de API principales
export const API_ENDPOINTS = {
    // Autenticación
    auth: {
        login: "/auth/login",
        register: "/auth/register",
        logout: "/auth/logout",
        refresh: "/auth/refresh",
    },

    // Entidades principales
    orders: "/orders",
    customers: "/customers",
    contractors: "/contractors",
    models: "/models",
    sizes: "/sizes",
    colors: "/colors",

    // Funciones de revalidación
    revalidate: "/revalidate",
};

// Status codes comunes para manejar respuestas
export const STATUS_CODES = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500,
};

// Mensajes genéricos para respuestas de API
export const API_MESSAGES = {
    success: {
        created: "Recurso creado exitosamente",
        updated: "Recurso actualizado exitosamente",
        deleted: "Recurso eliminado exitosamente",
    },
    error: {
        badRequest: "Solicitud incorrecta",
        unauthorized: "No autorizado",
        notFound: "Recurso no encontrado",
        internalError: "Error interno del servidor",
    },
};
