import { Suspense } from "react";

import { auth } from "@/lib/auth-helpers";
import { Spinner } from "@/shared/components/ui/hero-ui-client";
import { ErrorBoundary } from "@/shared/components/ui/ErrorBoundary";

import OptimizedOrdersPage from "./OptimizedOrdersPage";

/**
 * Página de órdenes con el sistema de diseño unificado
 */
export default async function OrdersPage() {
    // Obtener la sesión del usuario actual
    const session = await auth();
    const currentUserId = session?.user?.id;
    const userRole = session?.user?.role?.name || session?.user?.role?.id;

    return (
        <ErrorBoundary>
            <Suspense
                fallback={
                    <div className="flex items-center justify-center min-h-[50vh]">
                        <Spinner label="Cargando órdenes..." size="lg" />
                    </div>
                }
            >
                <OptimizedOrdersPage
                    currentUserId={currentUserId}
                    userRole={userRole}
                />
            </Suspense>
        </ErrorBoundary>
    );
}
