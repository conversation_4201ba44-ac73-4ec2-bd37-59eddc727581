"use client";

import React from "react";
import {
    InformationCircleIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    XCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

interface NoteInfoMessageProps {
    type: "info" | "warning" | "error" | "success";
    title: string;
    message: string;
    show?: boolean;
}

export function NoteInfoMessage({
    type,
    title,
    message,
    show = true,
}: NoteInfoMessageProps) {
    const styles = {
        info: {
            bg: "bg-blue-50 dark:bg-blue-900/20",
            border: "border-blue-200 dark:border-blue-800",
            text: "text-blue-800 dark:text-blue-200",
            icon: <InformationCircleIcon className="w-5 h-5" />,
        },
        warning: {
            bg: "bg-yellow-50 dark:bg-yellow-900/20",
            border: "border-yellow-200 dark:border-yellow-800",
            text: "text-yellow-800 dark:text-yellow-200",
            icon: <ExclamationTriangleIcon className="w-5 h-5" />,
        },
        error: {
            bg: "bg-red-50 dark:bg-red-900/20",
            border: "border-red-200 dark:border-red-800",
            text: "text-red-800 dark:text-red-200",
            icon: <XCircleIcon className="w-5 h-5" />,
        },
        success: {
            bg: "bg-green-50 dark:bg-green-900/20",
            border: "border-green-200 dark:border-green-800",
            text: "text-green-800 dark:text-green-200",
            icon: <CheckCircleIcon className="w-5 h-5" />,
        },
    };

    const style = styles[type];

    return (
        <AnimatePresence>
            {show && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-4 rounded-lg border ${style.bg} ${style.border} ${style.text}`}
                    exit={{ opacity: 0, y: -10 }}
                    initial={{ opacity: 0, y: -10 }}
                >
                    <div className="flex gap-3">
                        <div className="flex-shrink-0">{style.icon}</div>
                        <div className="flex-1">
                            <h4 className="font-medium mb-1">{title}</h4>
                            <p className="text-sm">{message}</p>
                        </div>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
