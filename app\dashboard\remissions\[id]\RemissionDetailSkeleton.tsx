"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Skeleton, Divider } from "@heroui/react";

export default function RemissionDetailSkeleton() {
    return (
        <div className="space-y-6">
            {/* Header Card Skeleton */}
            <Card className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-gray-900 dark:to-purple-950/20 border-none shadow-xl">
                <CardBody className="p-8">
                    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                        <div className="flex items-center gap-4">
                            <Skeleton className="w-20 h-20 rounded-2xl" />
                            <div className="space-y-2">
                                <Skeleton className="h-8 w-48 rounded-lg" />
                                <Skeleton className="h-4 w-32 rounded-lg" />
                            </div>
                        </div>
                        <div className="flex gap-2">
                            <Skeleton className="w-10 h-10 rounded-lg" />
                            <Skeleton className="w-10 h-10 rounded-lg" />
                            <Skeleton className="w-10 h-10 rounded-lg" />
                            <Skeleton className="w-24 h-10 rounded-lg" />
                        </div>
                    </div>
                </CardBody>
            </Card>

            {/* Info Cards Grid Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                    <Card key={i} className="h-full">
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <Skeleton className="w-5 h-5 rounded" />
                                <Skeleton className="h-6 w-32 rounded-lg" />
                            </div>
                        </CardHeader>
                        <Divider />
                        <CardBody className="pt-4">
                            <div className="space-y-3">
                                <Skeleton className="h-12 w-full rounded-lg" />
                                <Skeleton className="h-4 w-3/4 rounded-lg" />
                                <Skeleton className="h-4 w-1/2 rounded-lg" />
                            </div>
                        </CardBody>
                    </Card>
                ))}
            </div>

            {/* Document Preview Skeleton */}
            <Card className="overflow-hidden shadow-xl">
                <CardHeader className="bg-gray-50 dark:bg-gray-800/50">
                    <div className="flex items-center gap-2">
                        <Skeleton className="w-5 h-5 rounded" />
                        <Skeleton className="h-6 w-48 rounded-lg" />
                    </div>
                </CardHeader>
                <Divider />
                <CardBody className="p-8">
                    <div className="space-y-4">
                        <Skeleton className="h-32 w-full rounded-lg" />
                        <div className="grid grid-cols-2 gap-4">
                            <Skeleton className="h-20 w-full rounded-lg" />
                            <Skeleton className="h-20 w-full rounded-lg" />
                        </div>
                        <Skeleton className="h-64 w-full rounded-lg" />
                    </div>
                </CardBody>
            </Card>

            {/* History Timeline Skeleton */}
            <Card>
                <CardHeader>
                    <div className="flex items-center gap-2">
                        <Skeleton className="w-5 h-5 rounded" />
                        <Skeleton className="h-6 w-40 rounded-lg" />
                    </div>
                </CardHeader>
                <Divider />
                <CardBody>
                    <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                            <div key={i} className="flex gap-4">
                                <Skeleton className="w-2 h-2 mt-2 rounded-full" />
                                <div className="flex-1 space-y-2">
                                    <Skeleton className="h-4 w-1/3 rounded-lg" />
                                    <Skeleton className="h-3 w-2/3 rounded-lg" />
                                </div>
                            </div>
                        ))}
                    </div>
                </CardBody>
            </Card>
        </div>
    );
}
