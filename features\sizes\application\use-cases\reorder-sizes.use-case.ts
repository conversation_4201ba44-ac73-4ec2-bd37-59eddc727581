/**
 * Reorder Sizes Use Case
 *
 * Business logic for reordering sizes.
 * Ensures consistent ordering across the system.
 */

// import { injectable, inject } from "tsyringe"; // Commented out - tsyringe not installed

import { SizeRepositoryInterface } from "../../domain/repositories/size.repository.interface";
import { ReorderSizesDTO, SuccessResponseDTO } from "../dto/size.dto";

export interface ReorderSizesCommand {
    data: ReorderSizesDTO;
    userId: string;
}

export interface ReorderSizesResult {
    success: boolean;
    data?: SuccessResponseDTO;
    error?: string;
    validationErrors?: Array<{ field: string; message: string }>;
}

// @injectable() // Commented out - tsyringe not installed
export class ReorderSizesUseCase {
    constructor(
        // @inject("SizeRepository") // Commented out - tsyringe not installed
        private readonly sizeRepository: SizeRepositoryInterface,
    ) {}

    async execute(command: ReorderSizesCommand): Promise<ReorderSizesResult> {
        try {
            // 1. Validate command
            if (
                !command.data ||
                !command.data.sizes ||
                command.data.sizes.length === 0
            ) {
                return {
                    success: false,
                    error: "Invalid command: no sizes provided for reordering",
                };
            }

            // 2. Validate order values are unique
            const orderValues = command.data.sizes.map((s) => s.order);
            const uniqueOrderValues = new Set(orderValues);

            if (orderValues.length !== uniqueOrderValues.size) {
                return {
                    success: false,
                    error: "Order values must be unique",
                    validationErrors: [
                        {
                            field: "sizes",
                            message: "Duplicate order values found",
                        },
                    ],
                };
            }

            // 3. Verify all sizes exist
            const sizeIds = command.data.sizes.map((s) => s.id);
            const existingSizes = await Promise.all(
                sizeIds.map((id) => this.sizeRepository.findById(id)),
            );

            const missingSizes = sizeIds.filter(
                (id, index) => !existingSizes[index],
            );

            if (missingSizes.length > 0) {
                return {
                    success: false,
                    error: "Some sizes were not found",
                    validationErrors: missingSizes.map((id) => ({
                        field: "sizes",
                        message: `Size with ID ${id} not found`,
                    })),
                };
            }

            // 4. Check if any sizes are deleted
            const deletedSizes = existingSizes.filter(
                (size, index) => size && size.deletedAt !== null,
            );

            if (deletedSizes.length > 0) {
                return {
                    success: false,
                    error: "Cannot reorder deleted sizes",
                    validationErrors: deletedSizes.map((size) => ({
                        field: "sizes",
                        message: `Size ${size!.name} is deleted`,
                    })),
                };
            }

            // 5. Build order map
            const orderMap = new Map<string, number>();

            command.data.sizes.forEach(({ id, order }) => {
                orderMap.set(id, order);
            });

            // 6. Apply reordering
            const success = await this.sizeRepository.reorder(orderMap);

            if (!success) {
                return {
                    success: false,
                    error: "Failed to reorder sizes",
                };
            }

            // 7. Return success result
            return {
                success: true,
                data: {
                    success: true,
                    message: `Successfully reordered ${command.data.sizes.length} sizes`,
                },
            };
        } catch (error) {
            // Log error for monitoring
            console.error("ReorderSizesUseCase error:", error);

            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Auto-reorder sizes to fix gaps or conflicts
     */
    async autoReorder(
        category?: "numeric" | "alpha" | "custom",
    ): Promise<ReorderSizesResult> {
        try {
            // 1. Get all active sizes
            const sizes = await this.sizeRepository.findAll({
                isActive: true,
                category,
                orderBy: "order",
                orderDirection: "asc",
            });

            // 2. Create new order values with consistent spacing
            const spacing = 10;
            const reorderData = sizes.map((size, index) => ({
                id: size.id!,
                order: index * spacing,
            }));

            // 3. Execute reordering
            return this.execute({
                data: { sizes: reorderData },
                userId: "system",
            });
        } catch (error) {
            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Move a size to a specific position
     */
    async moveToPosition(
        sizeId: string,
        newPosition: number,
        userId: string,
    ): Promise<ReorderSizesResult> {
        try {
            // 1. Get all active sizes
            const sizes = await this.sizeRepository.findActiveOrdered();

            // 2. Find the size to move
            const sizeIndex = sizes.findIndex((s) => s.id === sizeId);

            if (sizeIndex === -1) {
                return {
                    success: false,
                    error: "Size not found",
                };
            }

            // 3. Remove the size from its current position
            const [sizeToMove] = sizes.splice(sizeIndex, 1);

            // 4. Insert at new position
            const insertIndex = Math.min(
                Math.max(0, newPosition),
                sizes.length,
            );

            sizes.splice(insertIndex, 0, sizeToMove);

            // 5. Create reorder data
            const reorderData = sizes.map((size, index) => ({
                id: size.id!,
                order: index * 10,
            }));

            // 6. Execute reordering
            return this.execute({
                data: { sizes: reorderData },
                userId,
            });
        } catch (error) {
            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }
}
