"use client";

import React from "react";
import { Progress } from "@heroui/react";
import { motion } from "framer-motion";

interface CustomerFormProgressProps {
    value: number;
    label?: string;
    showPercentage?: boolean;
    className?: string;
}

export function CustomerFormProgress({
    value,
    label = "Progreso del formulario",
    showPercentage = true,
    className = "",
}: CustomerFormProgressProps) {
    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={`w-full ${className}`}
            initial={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
        >
            <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                    {label}
                </span>
                {showPercentage && (
                    <motion.span
                        key={value}
                        animate={{ scale: 1 }}
                        className="text-xs font-medium bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"
                        initial={{ scale: 0.8 }}
                    >
                        {value}%
                    </motion.span>
                )}
            </div>
            <Progress
                aria-label={label}
                classNames={{
                    base: "max-w-full",
                    track: "bg-gray-200 dark:bg-gray-700",
                    indicator: "bg-gradient-to-r from-purple-500 to-blue-600",
                }}
                size="sm"
                value={value}
            />
        </motion.div>
    );
}

// Utilidades para calcular progreso
export const calculateCreateProgress = (
    name: string,
    isValid: boolean | undefined,
    isValidating: boolean,
): number => {
    let progress = 0;

    if (name && name.trim().length > 0) {
        progress += 25; // Nombre ingresado

        if (name.trim().length >= 2) {
            progress += 25; // Nombre válido en longitud

            if (isValidating) {
                progress += 25; // Validando
            } else if (isValid !== undefined) {
                progress += 50; // Validación completa
            }
        }
    }

    return Math.min(progress, 100);
};

export const calculateEditProgress = (
    originalName: string,
    currentName: string,
    isValid: boolean,
    hasErrors: boolean,
): number => {
    let progress = 0;

    if (currentName && currentName.trim().length > 0) {
        progress += 25; // Nombre presente

        if (currentName !== originalName) {
            progress += 25; // Cambios detectados

            if (!hasErrors && currentName.trim().length >= 2) {
                progress += 25; // Sin errores de validación

                if (isValid) {
                    progress += 25; // Listo para actualizar
                }
            }
        }
    }

    return Math.min(progress, 100);
};
