"use client";

import type { OrderForPacking } from "@/features/packings/actions/select-orders-for-packing";
import type {
    QualityDistribution,
    SelectedOrderProducts,
} from "@/lib/types/packing";

import React, { useState, useEffect } from "react";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Input,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Chip,
    Divider,
    Progress,
    Badge,
} from "@heroui/react";
import { Package, AlertCircle, CheckCircle } from "lucide-react";

interface QualityDistributionStepProps {
    selectedOrders: Map<string, OrderForPacking>;
    onDistributionChange: (distribution: SelectedOrderProducts[]) => void;
    onNext: () => void;
    onPrevious: () => void;
}

export function QualityDistributionStep({
    selectedOrders,
    onDistributionChange,
    onNext,
    onPrevious,
}: QualityDistributionStepProps) {
    // Inicializar distribución con todas las cantidades en primera calidad
    const initializeDistribution = () => {
        const distribution: SelectedOrderProducts[] = [];

        selectedOrders.forEach((order) => {
            const orderDistribution: SelectedOrderProducts = {
                orderId: order.id,
                productGroups: order.products.map((product) => ({
                    modelCode: product.modelCode,
                    colorName: product.colorName,
                    partNumber: product.partNumber,
                    qualityDistribution: product.sizes.map((size) => ({
                        garmentSizeId: size.garmentSizeId,
                        sizeCode: size.sizeCode,
                        primera: size.availableQuantity,
                        segunda: 0,
                        manchada: 0,
                        incompleta: 0,
                    })),
                })),
            };

            distribution.push(orderDistribution);
        });

        return distribution;
    };

    const [distribution, setDistribution] = useState<SelectedOrderProducts[]>(
        initializeDistribution(),
    );

    // Re-inicializar distribución cuando cambian las órdenes seleccionadas
    useEffect(() => {
        if (selectedOrders.size > 0) {
            const newDistribution = initializeDistribution();

            setDistribution(newDistribution);
            onDistributionChange(newDistribution);
        } else {
            // Si no hay órdenes, limpiar la distribución
            setDistribution([]);
            onDistributionChange([]);
        }
    }, [selectedOrders]); // Solo depende de selectedOrders

    // Actualizar distribución de calidad para una talla específica
    const updateQualityDistribution = (
        orderId: string,
        productIndex: number,
        sizeIndex: number,
        quality: keyof QualityDistribution,
        value: number,
    ) => {
        if (quality === "garmentSizeId" || quality === "sizeCode") return;

        const newDistribution = [...distribution];
        const orderIndex = newDistribution.findIndex(
            (d) => d.orderId === orderId,
        );

        if (orderIndex === -1) return;

        const sizeDistribution =
            newDistribution[orderIndex].productGroups[productIndex]
                .qualityDistribution[sizeIndex];
        const order = selectedOrders.get(orderId);

        if (!order) return;

        const product = order.products[productIndex];
        const maxQuantity = product.sizes[sizeIndex].availableQuantity;

        // Calcular el total actual sin el valor que estamos cambiando
        const currentTotal =
            (quality !== "primera" ? sizeDistribution.primera : 0) +
            (quality !== "segunda" ? sizeDistribution.segunda : 0) +
            (quality !== "manchada" ? sizeDistribution.manchada : 0) +
            (quality !== "incompleta" ? sizeDistribution.incompleta : 0);

        // Validar que no exceda el máximo
        const newValue = Math.min(
            Math.max(0, value),
            maxQuantity - currentTotal,
        );

        // Actualizar el valor
        sizeDistribution[quality] = newValue;

        setDistribution(newDistribution);
        onDistributionChange(newDistribution);
    };

    // Obtener estadísticas de distribución
    const getDistributionStats = () => {
        let totalPrimera = 0;
        let totalSegunda = 0;
        let totalManchada = 0;
        let totalIncompleta = 0;
        let totalAssigned = 0;
        let totalAvailable = 0;

        distribution.forEach((orderDist) => {
            const order = selectedOrders.get(orderDist.orderId);

            if (!order) return;

            orderDist.productGroups.forEach((group, productIndex) => {
                const product = order.products[productIndex];

                group.qualityDistribution.forEach((dist, sizeIndex) => {
                    totalPrimera += dist.primera;
                    totalSegunda += dist.segunda;
                    totalManchada += dist.manchada;
                    totalIncompleta += dist.incompleta;
                    totalAssigned +=
                        dist.primera +
                        dist.segunda +
                        dist.manchada +
                        dist.incompleta;
                    totalAvailable +=
                        product.sizes[sizeIndex].availableQuantity;
                });
            });
        });

        return {
            totalPrimera,
            totalSegunda,
            totalManchada,
            totalIncompleta,
            totalAssigned,
            totalAvailable,
            percentage:
                totalAvailable > 0 ? (totalAssigned / totalAvailable) * 100 : 0,
        };
    };

    const stats = getDistributionStats();

    return (
        <div className="space-y-6">
            {/* Estadísticas generales */}
            <Card>
                <CardBody>
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">
                            Distribución por Calidad
                        </h3>
                        <div className="flex gap-2">
                            <Chip color="success" variant="flat">
                                {stats.totalAssigned} / {stats.totalAvailable}{" "}
                                piezas
                            </Chip>
                            <Chip color="primary" variant="flat">
                                {stats.percentage.toFixed(0)}% asignado
                            </Chip>
                        </div>
                    </div>

                    <Progress
                        className="mb-4"
                        color="primary"
                        label="Progreso de asignación"
                        value={stats.percentage}
                    />

                    <div className="grid grid-cols-4 gap-4">
                        <div className="text-center">
                            <p className="text-sm text-default-600">Primera</p>
                            <p className="text-2xl font-bold text-green-600">
                                {stats.totalPrimera}
                            </p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-default-600">Segunda</p>
                            <p className="text-2xl font-bold text-yellow-600">
                                {stats.totalSegunda}
                            </p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-default-600">Manchada</p>
                            <p className="text-2xl font-bold text-red-600">
                                {stats.totalManchada}
                            </p>
                        </div>
                        <div className="text-center">
                            <p className="text-sm text-default-600">
                                Incompleta
                            </p>
                            <p className="text-2xl font-bold text-gray-600">
                                {stats.totalIncompleta}
                            </p>
                        </div>
                    </div>
                </CardBody>
            </Card>

            {/* Distribución por orden y producto */}
            {Array.from(selectedOrders.entries()).map(([orderId, order]) => {
                const orderDistribution = distribution.find(
                    (d) => d.orderId === orderId,
                );

                if (!orderDistribution) return null;

                return (
                    <Card key={orderId} className="border-2 border-primary">
                        <CardHeader className="bg-primary-50 dark:bg-primary-900/20">
                            <div className="flex items-center justify-between w-full">
                                <div>
                                    <h4 className="font-semibold text-lg">
                                        {order.displayName}
                                    </h4>
                                    <div className="flex gap-2 mt-1">
                                        {order.cutOrder && (
                                            <Chip size="sm" variant="flat">
                                                OC: {order.cutOrder}
                                            </Chip>
                                        )}
                                        {order.transferNumber && (
                                            <Chip size="sm" variant="flat">
                                                Trans: {order.transferNumber}
                                            </Chip>
                                        )}
                                    </div>
                                </div>
                                <Badge
                                    color="primary"
                                    content={order.products.length}
                                >
                                    <Package className="w-5 h-5" />
                                </Badge>
                            </div>
                        </CardHeader>

                        <CardBody className="space-y-6">
                            {order.products.map((product, productIndex) => {
                                const productGroup =
                                    orderDistribution.productGroups[
                                        productIndex
                                    ];

                                return (
                                    <div
                                        key={`${product.modelCode}-${product.colorName}-${product.partNumber}`}
                                    >
                                        <div className="flex items-center justify-between mb-3">
                                            <div>
                                                <p className="font-medium">
                                                    {product.modelCode} -{" "}
                                                    {product.colorName}
                                                    {product.partNumber &&
                                                        ` - Partida: ${product.partNumber}`}
                                                </p>
                                                {product.modelDescription && (
                                                    <p className="text-sm text-default-500">
                                                        {
                                                            product.modelDescription
                                                        }
                                                    </p>
                                                )}
                                            </div>
                                            <Chip
                                                color="default"
                                                variant="flat"
                                            >
                                                {product.totalAvailable} piezas
                                                disponibles
                                            </Chip>
                                        </div>

                                        <div className="overflow-x-auto">
                                            <Table
                                                removeWrapper
                                                aria-label="Distribución por talla"
                                                classNames={{
                                                    base: "min-w-[600px]",
                                                }}
                                            >
                                                <TableHeader>
                                                    <TableColumn>
                                                        TALLA
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        DISPONIBLE
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        PRIMERA
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        SEGUNDA
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        MANCHADA
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        INCOMPLETA
                                                    </TableColumn>
                                                    <TableColumn align="center">
                                                        TOTAL
                                                    </TableColumn>
                                                </TableHeader>
                                                <TableBody>
                                                    {product.sizes.map(
                                                        (size, sizeIndex) => {
                                                            const sizeDistribution =
                                                                productGroup
                                                                    .qualityDistribution[
                                                                    sizeIndex
                                                                ];
                                                            const totalAssigned =
                                                                sizeDistribution.primera +
                                                                sizeDistribution.segunda +
                                                                sizeDistribution.manchada +
                                                                sizeDistribution.incompleta;

                                                            return (
                                                                <TableRow
                                                                    key={
                                                                        size.garmentSizeId
                                                                    }
                                                                >
                                                                    <TableCell>
                                                                        <Chip
                                                                            size="sm"
                                                                            variant="flat"
                                                                        >
                                                                            {
                                                                                size.sizeCode
                                                                            }
                                                                        </Chip>
                                                                    </TableCell>
                                                                    <TableCell className="text-center font-medium">
                                                                        {
                                                                            size.availableQuantity
                                                                        }
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <Input
                                                                            classNames={{
                                                                                input: "text-center",
                                                                                base: "max-w-[80px] mx-auto",
                                                                            }}
                                                                            max={
                                                                                size.availableQuantity
                                                                            }
                                                                            min={
                                                                                0
                                                                            }
                                                                            size="sm"
                                                                            type="number"
                                                                            value={sizeDistribution.primera.toString()}
                                                                            onChange={(
                                                                                e,
                                                                            ) =>
                                                                                updateQualityDistribution(
                                                                                    orderId,
                                                                                    productIndex,
                                                                                    sizeIndex,
                                                                                    "primera",
                                                                                    parseInt(
                                                                                        e
                                                                                            .target
                                                                                            .value,
                                                                                    ) ||
                                                                                        0,
                                                                                )
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <Input
                                                                            classNames={{
                                                                                input: "text-center",
                                                                                base: "max-w-[80px] mx-auto",
                                                                            }}
                                                                            max={
                                                                                size.availableQuantity
                                                                            }
                                                                            min={
                                                                                0
                                                                            }
                                                                            size="sm"
                                                                            type="number"
                                                                            value={sizeDistribution.segunda.toString()}
                                                                            onChange={(
                                                                                e,
                                                                            ) =>
                                                                                updateQualityDistribution(
                                                                                    orderId,
                                                                                    productIndex,
                                                                                    sizeIndex,
                                                                                    "segunda",
                                                                                    parseInt(
                                                                                        e
                                                                                            .target
                                                                                            .value,
                                                                                    ) ||
                                                                                        0,
                                                                                )
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <Input
                                                                            classNames={{
                                                                                input: "text-center",
                                                                                base: "max-w-[80px] mx-auto",
                                                                            }}
                                                                            max={
                                                                                size.availableQuantity
                                                                            }
                                                                            min={
                                                                                0
                                                                            }
                                                                            size="sm"
                                                                            type="number"
                                                                            value={sizeDistribution.manchada.toString()}
                                                                            onChange={(
                                                                                e,
                                                                            ) =>
                                                                                updateQualityDistribution(
                                                                                    orderId,
                                                                                    productIndex,
                                                                                    sizeIndex,
                                                                                    "manchada",
                                                                                    parseInt(
                                                                                        e
                                                                                            .target
                                                                                            .value,
                                                                                    ) ||
                                                                                        0,
                                                                                )
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell>
                                                                        <Input
                                                                            classNames={{
                                                                                input: "text-center",
                                                                                base: "max-w-[80px] mx-auto",
                                                                            }}
                                                                            max={
                                                                                size.availableQuantity
                                                                            }
                                                                            min={
                                                                                0
                                                                            }
                                                                            size="sm"
                                                                            type="number"
                                                                            value={sizeDistribution.incompleta.toString()}
                                                                            onChange={(
                                                                                e,
                                                                            ) =>
                                                                                updateQualityDistribution(
                                                                                    orderId,
                                                                                    productIndex,
                                                                                    sizeIndex,
                                                                                    "incompleta",
                                                                                    parseInt(
                                                                                        e
                                                                                            .target
                                                                                            .value,
                                                                                    ) ||
                                                                                        0,
                                                                                )
                                                                            }
                                                                        />
                                                                    </TableCell>
                                                                    <TableCell className="text-center">
                                                                        <Chip
                                                                            color={
                                                                                totalAssigned ===
                                                                                size.availableQuantity
                                                                                    ? "success"
                                                                                    : "warning"
                                                                            }
                                                                            variant="flat"
                                                                        >
                                                                            {
                                                                                totalAssigned
                                                                            }
                                                                        </Chip>
                                                                    </TableCell>
                                                                </TableRow>
                                                            );
                                                        },
                                                    )}
                                                </TableBody>
                                            </Table>
                                        </div>

                                        {productIndex <
                                            order.products.length - 1 && (
                                            <Divider className="mt-4" />
                                        )}
                                    </div>
                                );
                            })}
                        </CardBody>
                    </Card>
                );
            })}

            {/* Botones de navegación */}
            <div className="flex justify-between items-center">
                <Button variant="light" onPress={onPrevious}>
                    Anterior
                </Button>

                <div className="flex items-center gap-4">
                    {stats.totalAssigned === 0 && (
                        <div className="flex items-center gap-2 text-warning">
                            <AlertCircle className="w-5 h-5" />
                            <span className="text-sm">
                                Asigna las cantidades por calidad
                            </span>
                        </div>
                    )}

                    <Button
                        color="primary"
                        endContent={<CheckCircle className="w-4 h-4" />}
                        isDisabled={stats.totalAssigned === 0}
                        onPress={onNext}
                    >
                        Continuar
                    </Button>
                </div>
            </div>
        </div>
    );
}
