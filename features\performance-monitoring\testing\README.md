# Performance Testing Scripts

## Installation

First, install the required dependencies:

```bash
npm install --save-dev jest @jest/globals ts-jest @types/jest
npm install --save-dev @playwright/test
npm install --save-dev k6
```

## Add to package.json

Add these scripts to your `package.json`:

```json
{
  "scripts": {
    "test:performance": "jest --config jest.config.performance.js",
    "test:performance:watch": "jest --config jest.config.performance.js --watch",
    "test:integration": "playwright test features/performance-monitoring/testing/integration",
    "test:load": "k6 run features/performance-monitoring/testing/load-tests/basic-load-test.js",
    "test:load:api": "k6 run features/performance-monitoring/testing/load-tests/api-load-test.js",
    "monitor:cache": "ts-node features/performance-monitoring/testing/monitoring/run-monitoring.ts",
    "performance:report": "npm run test:performance && npm run test:load && npm run monitor:cache"
  }
}
```

## Running Tests

### Unit Tests
```bash
npm run test:performance
```

### Integration Tests
```bash
npm run test:integration
```

### Load Tests
```bash
# Basic load test
npm run test:load

# API-specific load test
npm run test:load:api
```

### Cache Monitoring
```bash
npm run monitor:cache
```

### Complete Performance Report
```bash
npm run performance:report
```
