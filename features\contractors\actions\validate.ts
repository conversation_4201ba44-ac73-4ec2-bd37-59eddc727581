"use server";

import { db } from "@/shared/lib/db";

import { NameValidationResult } from "../schemas/schema";

/**
 * Valida si un nombre de contratista está disponible
 * @param firstName Nombre del contratista
 * @param lastName Apellido paterno del contratista
 * @param excludeId ID a excluir de la validación (útil para edición)
 */
export async function validateContractorName(
    firstName: string,
    lastName: string,
    excludeId?: string,
): Promise<{ success: boolean; data?: NameValidationResult; error?: string }> {
    try {
        // Validar que sean strings válidos
        if (
            !firstName ||
            typeof firstName !== "string" ||
            !lastName ||
            typeof lastName !== "string"
        ) {
            return {
                success: false,
                error: "Nombre o apellido inválido",
            };
        }

        // Validar longitud mínima
        if (firstName.trim().length < 2 || lastName.trim().length < 2) {
            return {
                success: true,
                data: {
                    isValid: false,
                    message:
                        "El nombre y apellido deben tener al menos 2 caracteres",
                },
            };
        }

        // Construir el nombre completo para comparar con el campo 'name'
        const fullName = `${firstName.trim()} ${lastName.trim()}`.trim();

        // Buscar por nombre completo en el campo name
        try {
            const existingContractor = await db.contractor.findFirst({
                where: {
                    name: {
                        equals: fullName,
                        mode: "insensitive",
                    },
                    id: excludeId ? { not: excludeId } : undefined,
                },
            });

            // Si existe y no es el mismo contratista que estamos editando
            if (existingContractor) {
                return {
                    success: true,
                    data: {
                        isValid: false,
                        message:
                            "Ya existe un contratista con este nombre y apellido",
                    },
                };
            }
        } catch (error) {
            console.error("Error al buscar contratista:", error);
            // Si hay un error, continuamos con la validación
            // asumiendo que no existe (para no bloquear al usuario)
        }

        // Si todo está bien, el nombre es válido
        return {
            success: true,
            data: {
                isValid: true,
            },
        };
    } catch (error) {
        console.error(
            "[ContractorValidate] Error en validateContractorName:",
            error,
        );

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al validar nombre de contratista",
        };
    }
}
