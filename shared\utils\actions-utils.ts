"use server";

import { revalidatePath } from "next/cache";

import { auth } from "@/lib/auth-helpers";

// Interfaces para las respuestas de acciones
export interface ActionResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}

/**
 * Función de utilidad para crear una respuesta de error unificada
 */
export async function createErrorResponse(
    error: unknown,
): Promise<ActionResponse<never>> {
    // REMOVED: console.error("[Server Action Error]", error);

    return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
    };
}

/**
 * Obtiene el usuario actual de la sesión
 */
export async function getCurrentUser() {
    try {
        const session = await auth();

        if (!session || !session.user) {
            return null;
        }

        return session.user;
    } catch (error) {
        // REMOVED: console.error("[getCurrentUser Error]", error);

        return null;
    }
}

/**
 * Revalidación de rutas para mantener los datos actualizados
 */
export async function revalidateData(paths: string[]) {
    try {
        for (const path of paths) {
            revalidatePath(path);
        }
    } catch (error) {
        // REMOVED: console.error("[revalidateData Error]", error);
    }
}
