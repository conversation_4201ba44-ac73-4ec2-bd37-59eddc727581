import { Card as HeroCard, CardProps } from "@heroui/react";
import { forwardRef } from "react";

import { cn } from "@/shared/lib/utils";

export interface CardV2Props extends CardProps {
    spacing?: "tight" | "normal" | "comfortable";
    hover?: boolean;
}

export const CardV2 = forwardRef<HTMLDivElement, CardV2Props>(
    (
        { spacing = "normal", hover = true, className, children, ...props },
        ref,
    ) => {
        const spacingClasses = {
            tight: "p-3",
            normal: "p-4",
            comfortable: "p-5",
        };

        return (
            <HeroCard
                ref={ref}
                className={cn(
                    spacingClasses[spacing],
                    hover && "transition-shadow hover:shadow-md",
                    "border-gray-200 dark:border-gray-700",
                    className,
                )}
                {...props}
            >
                {children}
            </HeroCard>
        );
    },
);

CardV2.displayName = "CardV2";

// Export para uso en historias de Storybook
export const cardSpacingOptions = ["tight", "normal", "comfortable"] as const;
