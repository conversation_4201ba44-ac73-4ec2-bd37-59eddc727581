/**
 * Size Entity Tests
 *
 * Unit tests for the Size domain entity to ensure
 * business logic and validation work correctly.
 */

import { Size, SizeProps } from "../../../domain/entities/size.entity";

describe("Size Entity", () => {
    // Helper function to create valid size props
    const createValidProps = (): SizeProps => ({
        id: "123",
        name: "M",
        displayName: "Medium",
        order: 20,
        isActive: true,
        category: "alpha",
        createdAt: new Date("2024-01-01"),
        updatedAt: new Date("2024-01-01"),
    });

    describe("Constructor", () => {
        it("should create a valid size entity", () => {
            const props = createValidProps();
            const size = new Size(props);

            expect(size.id).toBe("123");
            expect(size.name).toBe("M");
            expect(size.displayName).toBe("Medium");
            expect(size.order).toBe(20);
            expect(size.isActive).toBe(true);
            expect(size.category).toBe("alpha");
        });

        it("should normalize size name to uppercase", () => {
            const props = createValidProps();

            props.name = "xs";
            const size = new Size(props);

            expect(size.name).toBe("XS");
        });

        it("should infer numeric category from name", () => {
            const props = createValidProps();

            props.name = "32";
            props.category = undefined;
            const size = new Size(props);

            expect(size.category).toBe("numeric");
        });

        it("should infer alpha category from name", () => {
            const props = createValidProps();

            props.name = "XXL";
            props.category = undefined;
            const size = new Size(props);

            expect(size.category).toBe("alpha");
        });

        it("should default to custom category for non-standard names", () => {
            const props = createValidProps();

            props.name = "ONE-SIZE";
            props.category = undefined;
            const size = new Size(props);

            expect(size.category).toBe("custom");
        });
    });

    describe("Business Logic Methods", () => {
        describe("activate()", () => {
            it("should activate an inactive size", () => {
                const props = createValidProps();

                props.isActive = false;
                const size = new Size(props);

                size.activate();

                expect(size.isActive).toBe(true);
            });

            it("should throw error when activating a deleted size", () => {
                const props = createValidProps();

                props.deletedAt = new Date();
                const size = new Size(props);

                expect(() => size.activate()).toThrow(
                    "Cannot activate a deleted size",
                );
            });
        });

        describe("deactivate()", () => {
            it("should deactivate an active size", () => {
                const props = createValidProps();
                const size = new Size(props);

                size.deactivate();

                expect(size.isActive).toBe(false);
            });
        });

        describe("delete()", () => {
            it("should soft delete a size", () => {
                const props = createValidProps();
                const size = new Size(props);

                size.delete();

                expect(size.deletedAt).toBeInstanceOf(Date);
                expect(size.isActive).toBe(false);
            });
        });

        describe("restore()", () => {
            it("should restore a deleted size", () => {
                const props = createValidProps();

                props.deletedAt = new Date();
                props.isActive = false;
                const size = new Size(props);

                size.restore();

                expect(size.deletedAt).toBeNull();
            });
        });

        describe("update()", () => {
            it("should update size properties", () => {
                const size = new Size(createValidProps());
                const originalUpdatedAt = size.updatedAt;

                // Wait a bit to ensure updatedAt changes
                jest.useFakeTimers();
                jest.advanceTimersByTime(1000);

                size.update({
                    displayName: "Medium Size",
                    order: 25,
                });

                expect(size.displayName).toBe("Medium Size");
                expect(size.order).toBe(25);
                expect(size.updatedAt.getTime()).toBeGreaterThan(
                    originalUpdatedAt.getTime(),
                );

                jest.useRealTimers();
            });

            it("should throw error when updating a deleted size", () => {
                const props = createValidProps();

                props.deletedAt = new Date();
                const size = new Size(props);

                expect(() => size.update({ displayName: "New Name" })).toThrow(
                    "Cannot update a deleted size",
                );
            });
        });
    });

    describe("Comparison Methods", () => {
        describe("comesBefore()", () => {
            it("should return true when size has lower order", () => {
                const size1 = new Size({ ...createValidProps(), order: 10 });
                const size2 = new Size({ ...createValidProps(), order: 20 });

                expect(size1.comesBefore(size2)).toBe(true);
                expect(size2.comesBefore(size1)).toBe(false);
            });
        });

        describe("comesAfter()", () => {
            it("should return true when size has higher order", () => {
                const size1 = new Size({ ...createValidProps(), order: 30 });
                const size2 = new Size({ ...createValidProps(), order: 20 });

                expect(size1.comesAfter(size2)).toBe(true);
                expect(size2.comesAfter(size1)).toBe(false);
            });
        });

        describe("isEquivalentTo()", () => {
            it("should return true for sizes with same name", () => {
                const size1 = new Size({ ...createValidProps(), name: "M" });
                const size2 = new Size({ ...createValidProps(), name: "M" });

                expect(size1.isEquivalentTo(size2)).toBe(true);
            });

            it("should return false for sizes with different names", () => {
                const size1 = new Size({ ...createValidProps(), name: "M" });
                const size2 = new Size({ ...createValidProps(), name: "L" });

                expect(size1.isEquivalentTo(size2)).toBe(false);
            });
        });
    });

    describe("Validation", () => {
        it("should validate a valid size", () => {
            const size = new Size(createValidProps());
            const errors = size.validate();

            expect(errors).toEqual([]);
            expect(size.isValid()).toBe(true);
        });

        it("should fail validation with empty display name", () => {
            const props = createValidProps();

            props.displayName = "";
            const size = new Size(props);
            const errors = size.validate();

            expect(errors).toContain("Display name is required");
            expect(size.isValid()).toBe(false);
        });

        it("should fail validation with negative measurements", () => {
            const props = createValidProps();

            props.measurements = {
                chest: -10,
                waist: 30,
            };
            const size = new Size(props);
            const errors = size.validate();

            expect(errors).toContain("Chest measurement must be positive");
            expect(size.isValid()).toBe(false);
        });
    });

    describe("Factory Methods", () => {
        describe("create()", () => {
            it("should create a new size without id", () => {
                const size = Size.create({
                    name: "L",
                    displayName: "Large",
                    order: 30,
                    isActive: true,
                });

                expect(size.id).toBeUndefined();
                expect(size.name).toBe("L");
                expect(size.createdAt).toBeInstanceOf(Date);
                expect(size.updatedAt).toBeInstanceOf(Date);
            });
        });

        describe("createNumeric()", () => {
            it("should create a numeric size", () => {
                const size = Size.createNumeric(32, "Size 32", { waist: 32 });

                expect(size.name).toBe("32");
                expect(size.displayName).toBe("Size 32");
                expect(size.order).toBe(32);
                expect(size.category).toBe("numeric");
                expect(size.measurements?.waist).toBe(32);
            });
        });

        describe("createAlpha()", () => {
            it("should create an alpha size", () => {
                const size = Size.createAlpha("xl", 40, { chest: 42 });

                expect(size.name).toBe("XL");
                expect(size.displayName).toBe("XL");
                expect(size.order).toBe(40);
                expect(size.category).toBe("alpha");
                expect(size.measurements?.chest).toBe(42);
            });
        });
    });

    describe("Serialization", () => {
        describe("toObject()", () => {
            it("should convert to plain object", () => {
                const props = createValidProps();
                const size = new Size(props);
                const obj = size.toObject();

                expect(obj).toEqual(props);
                expect(obj).not.toBe(props); // Should be a new object
            });
        });

        describe("clone()", () => {
            it("should create a copy of the size", () => {
                const size = new Size(createValidProps());
                const clone = size.clone();

                expect(clone).not.toBe(size); // Different instances
                expect(clone.toObject()).toEqual(size.toObject());
            });
        });
    });

    describe("Value Objects Integration", () => {
        it("should throw error for invalid size name", () => {
            const props = createValidProps();

            props.name = "Invalid Name!"; // Contains invalid characters

            expect(() => new Size(props)).toThrow();
        });

        it("should throw error for negative order", () => {
            const props = createValidProps();

            props.order = -1;

            expect(() => new Size(props)).toThrow(
                "Size order cannot be negative",
            );
        });

        it("should throw error for order exceeding limit", () => {
            const props = createValidProps();

            props.order = 10000;

            expect(() => new Size(props)).toThrow(
                "Size order cannot exceed 9999",
            );
        });
    });
});
