"use client";

import React from "react";
import { useRouter } from "next/navigation";
import {
    <PERSON>,
    CardBody,
    CardFooter,
    Button,
    Avatar,
    Tooltip,
} from "@heroui/react";
import {
    DocumentTextIcon,
    CalendarIcon,
    UserCircleIcon,
    EyeIcon,
    PrinterIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { formatDate } from "@/shared/utils/formatters";

import RemissionStatusBadge from "./RemissionStatusBadge";

interface RemissionCardProps {
    remission: {
        id: string;
        folio: string;
        status: string;
        createdAt: string | Date;
        printedAt?: string | Date | null;
        contractor?: {
            id: string;
            name: string;
        } | null;
        remissionItems?: Array<{
            quantity: number;
        }>;
        _count?: {
            remissionItems: number;
        };
    };
    onPrint?: (id: string) => void;
    index?: number;
}

export default function RemissionCard({
    remission,
    onPrint,
    index = 0,
}: RemissionCardProps) {
    const router = useRouter();

    const handleView = () => {
        router.push(`/dashboard/remissions/${remission.id}`);
    };

    const handlePrint = (e?: any) => {
        e?.stopPropagation?.();
        if (onPrint) {
            onPrint(remission.id);
        }
    };

    const totalItems =
        remission._count?.remissionItems ||
        remission.remissionItems?.length ||
        0;
    const totalQuantity =
        remission.remissionItems?.reduce(
            (sum, item) => sum + item.quantity,
            0,
        ) || 0;

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                delay: index * 0.1,
            }}
            whileHover={{ scale: 1.02 }}
        >
            <Card
                isPressable
                className="hover:shadow-xl transition-all cursor-pointer"
                onPress={handleView}
            >
                <CardBody className="p-6">
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg">
                                <DocumentTextIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                                <h3 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                    {remission.folio}
                                </h3>
                                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                                    <CalendarIcon className="w-4 h-4" />
                                    {formatDate(remission.createdAt)}
                                </div>
                            </div>
                        </div>
                        <RemissionStatusBadge
                            size="sm"
                            status={remission.status}
                        />
                    </div>

                    {remission.contractor && (
                        <div className="flex items-center gap-3 mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                            <Avatar
                                className="ring-2 ring-white dark:ring-gray-800"
                                name={remission.contractor.name}
                                size="sm"
                            />
                            <div className="flex-1">
                                <div className="flex items-center gap-1 text-sm">
                                    <UserCircleIcon className="w-4 h-4 text-gray-500" />
                                    <span className="font-medium">
                                        {remission.contractor.name}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="grid grid-cols-2 gap-4 mt-4">
                        <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                {totalItems}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                Items
                            </p>
                        </div>
                        <div className="text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                                {totalQuantity}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                Prendas
                            </p>
                        </div>
                    </div>

                    {remission.printedAt && (
                        <div className="mt-4 flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <PrinterIcon className="w-4 h-4" />
                            <span>
                                Impreso: {formatDate(remission.printedAt)}
                            </span>
                        </div>
                    )}
                </CardBody>

                <CardFooter className="bg-gray-50 dark:bg-gray-800/50 px-6 py-3">
                    <div className="flex gap-2 w-full">
                        <Button
                            className="flex-1"
                            color="primary"
                            size="sm"
                            startContent={<EyeIcon className="w-4 h-4" />}
                            variant="flat"
                            onPress={handleView}
                        >
                            Ver Detalle
                        </Button>
                        {onPrint && (
                            <Tooltip content="Imprimir">
                                <Button
                                    isIconOnly
                                    color="default"
                                    size="sm"
                                    variant="flat"
                                    onPress={handlePrint}
                                >
                                    <PrinterIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>
                        )}
                    </div>
                </CardFooter>
            </Card>
        </motion.div>
    );
}
