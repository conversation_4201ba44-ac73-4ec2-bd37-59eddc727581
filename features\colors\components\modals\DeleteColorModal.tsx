"use client";

import { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";
import {
    TrashIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { addToast } from "@heroui/react";

import { Color } from "@/features/colors/hooks/useColor";

interface DeleteColorModalProps {
    isOpen: boolean;
    onClose: () => void;
    color: Color | null;
    onConfirm: (colorId: string) => Promise<void>;
}

export function DeleteColorModal({
    isOpen,
    onClose,
    color,
    onConfirm,
}: DeleteColorModalProps) {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
        if (!color) return;

        setIsDeleting(true);
        try {
            await onConfirm(color.id);
            addToast({
                title: "Color eliminado",
                description: `${color.name} ha sido eliminado exitosamente`,
                color: "success",
            });
            onClose();
        } catch (error) {
            addToast({
                title: "Error al eliminar",
                description: "No se pudo eliminar el color",
                color: "danger",
            });
            console.error("Error deleting color:", error);
        } finally {
            setIsDeleting(false);
        }
    };

    if (!color) return null;

    return (
        <Modal
            backdrop="blur"
            classNames={{
                base: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700",
                header: "border-b border-gray-200 dark:border-gray-700",
                body: "py-6",
                footer: "border-t border-gray-200 dark:border-gray-700",
            }}
            isOpen={isOpen}
            placement="center"
            onClose={onClose}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">
                            <motion.div
                                animate={{ scale: 1, opacity: 1 }}
                                className="flex items-center gap-3"
                                initial={{ scale: 0.8, opacity: 0 }}
                            >
                                <div className="bg-red-500/20 p-2 rounded-lg">
                                    <ExclamationTriangleIcon className="w-6 h-6 text-red-500" />
                                </div>
                                <h3 className="text-xl font-semibold">
                                    Confirmar Eliminación
                                </h3>
                            </motion.div>
                        </ModalHeader>

                        <ModalBody>
                            <AnimatePresence mode="wait">
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className="space-y-4"
                                    exit={{ opacity: 0, y: -10 }}
                                    initial={{ opacity: 0, y: 10 }}
                                >
                                    <p className="text-gray-600">
                                        ¿Estás seguro de que deseas eliminar
                                        este color?
                                    </p>

                                    <motion.div
                                        animate={{ scale: 1 }}
                                        className="bg-gradient-to-r from-primary-500/10 to-secondary-500/10 p-4 rounded-lg border border-primary-500/20"
                                        initial={{ scale: 0.95 }}
                                    >
                                        <div className="flex items-center gap-4">
                                            <div
                                                className="w-16 h-16 rounded-xl shadow-lg border-2 border-white dark:border-gray-700"
                                                style={{
                                                    backgroundColor:
                                                        color.hexCode ||
                                                        "#000000",
                                                }}
                                            />
                                            <div className="flex-1">
                                                <h4 className="font-semibold text-lg">
                                                    {color.name}
                                                </h4>
                                                <p className="text-sm text-gray-500 font-mono">
                                                    {color.hexCode}
                                                </p>
                                                {color._count?.garments &&
                                                    color._count.garments >
                                                        0 && (
                                                        <p className="text-sm text-gray-500 mt-1">
                                                            Usado en{" "}
                                                            {
                                                                color._count
                                                                    .garments
                                                            }{" "}
                                                            {color._count
                                                                .garments === 1
                                                                ? "prenda"
                                                                : "prendas"}
                                                        </p>
                                                    )}
                                            </div>
                                        </div>
                                    </motion.div>

                                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-3 rounded-lg">
                                        <div className="flex items-start gap-2">
                                            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                                            <div className="text-sm text-red-700 dark:text-red-300">
                                                <p className="font-medium">
                                                    Esta acción no se puede
                                                    deshacer
                                                </p>
                                                <p className="mt-1">
                                                    Se eliminará permanentemente
                                                    este color del catálogo.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        </ModalBody>

                        <ModalFooter>
                            <Button
                                isDisabled={isDeleting}
                                variant="light"
                                onPress={onClose}
                            >
                                Cancelar
                            </Button>
                            <Button
                                className="bg-gradient-to-r from-red-500 to-red-600"
                                color="danger"
                                isLoading={isDeleting}
                                startContent={
                                    !isDeleting && (
                                        <TrashIcon className="w-4 h-4" />
                                    )
                                }
                                variant="shadow"
                                onPress={handleDelete}
                            >
                                {isDeleting
                                    ? "Eliminando..."
                                    : "Eliminar Color"}
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
