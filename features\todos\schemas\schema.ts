import { z } from "zod";

export const todoSchema = z.object({
    title: z
        .string()
        .min(1, "El título es requerido")
        .max(200, "El título no puede exceder 200 caracteres"),
    description: z.string().optional(),
    completed: z.boolean().default(false),
    priority: z.enum(["low", "medium", "high"]).default("medium"),
    dueDate: z.date().optional(),
});

export const updateTodoSchema = todoSchema.partial();

export const todoFiltersSchema = z.object({
    search: z.string().optional(),
    completed: z.boolean().optional(),
    priority: z.enum(["low", "medium", "high"]).optional(),
    orderBy: z
        .enum(["createdAt", "updatedAt", "dueDate", "priority"])
        .optional(),
    order: z.enum(["asc", "desc"]).optional(),
    page: z.number().positive().optional(),
    perPage: z.number().positive().max(100).optional(),
});
