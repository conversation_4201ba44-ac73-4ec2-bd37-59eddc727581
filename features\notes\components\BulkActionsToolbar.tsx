"use client";

import { useState } from "react";
import {
    Button,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownI<PERSON>,
    <PERSON><PERSON>,
    <PERSON>dal<PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>,
    ModalFooter,
    useDisclosure,
} from "@heroui/react";
import {
    XMarkIcon,
    TrashIcon,
    TagIcon,
    AdjustmentsHorizontalIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

import { useNotesSelection } from "./NotesSelectionContext";

interface BulkActionsToolbarProps {
    importanceOptions: { id: string; name: string; color?: string | null }[];
    statusOptions: { id: string; name: string; color?: string | null }[];
    onBulkUpdateStatus: (noteIds: string[], statusId: string) => Promise<any>;
    onBulkUpdateImportance: (
        noteIds: string[],
        importanceId: string,
    ) => Promise<any>;
    onBulkDelete?: (noteIds: string[]) => Promise<any>;
    isAdmin?: boolean;
    className?: string;
}

export function BulkActionsToolbar({
    importanceOptions,
    statusOptions,
    onBulkUpdateStatus,
    onBulkUpdateImportance,
    onBulkDelete,
    isAdmin = false,
    className = "",
}: BulkActionsToolbarProps) {
    // Get selection context
    const { selectedNotes, isSelectionMode, endSelectionMode, clearSelection } =
        useNotesSelection();

    // Loading states
    const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
    const [isUpdatingImportance, setIsUpdatingImportance] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);

    // Modal for delete confirmation
    const { isOpen, onOpen, onClose } = useDisclosure();

    // Handler for changing status
    const handleChangeStatus = async (statusId: string) => {
        if (selectedNotes.length === 0) return;

        try {
            setIsUpdatingStatus(true);
            await onBulkUpdateStatus(selectedNotes, statusId);
            clearSelection();
        } catch (error) {
            // REMOVED: console.error("Error updating status in bulk:", error);
        } finally {
            setIsUpdatingStatus(false);
        }
    };

    // Handler for changing importance
    const handleChangeImportance = async (importanceId: string) => {
        if (selectedNotes.length === 0) return;

        try {
            setIsUpdatingImportance(true);
            await onBulkUpdateImportance(selectedNotes, importanceId);
            clearSelection();
        } catch (error) {
            // REMOVED: console.error("Error updating importance in bulk:", error);
        } finally {
            setIsUpdatingImportance(false);
        }
    };

    // Handler for confirming delete
    const handleConfirmDelete = async () => {
        if (selectedNotes.length === 0 || !onBulkDelete) return;

        try {
            setIsDeleting(true);
            await onBulkDelete(selectedNotes);
            clearSelection();
            onClose();
        } catch (error) {
            // REMOVED: console.error("Error deleting notes in bulk:", error);
        } finally {
            setIsDeleting(false);
        }
    };

    // If not in selection mode or no notes selected, don't render
    if (!isSelectionMode || selectedNotes.length === 0) {
        return null;
    }

    return (
        <>
            <div
                className={`
        flex items-center justify-between 
        bg-white dark:bg-gray-800 
        border border-gray-200 dark:border-gray-700 
        rounded-lg shadow-sm p-2
        ${className}
      `}
            >
                <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                        {selectedNotes.length} nota
                        {selectedNotes.length !== 1 ? "s" : ""} seleccionada
                        {selectedNotes.length !== 1 ? "s" : ""}
                    </span>
                    <Button
                        aria-label="Limpiar selección"
                        size="sm"
                        variant="light"
                        onClick={clearSelection}
                    >
                        Deseleccionar
                    </Button>
                </div>

                <div className="flex items-center gap-2">
                    {/* Estado */}
                    <Dropdown>
                        <DropdownTrigger>
                            <Button
                                color="primary"
                                isDisabled={isUpdatingImportance || isDeleting}
                                isLoading={isUpdatingStatus}
                                size="sm"
                                startContent={
                                    <AdjustmentsHorizontalIcon className="w-4 h-4" />
                                }
                                variant="flat"
                            >
                                Cambiar estado
                            </Button>
                        </DropdownTrigger>
                        <DropdownMenu aria-label="Estados disponibles">
                            {statusOptions.map((status) => (
                                <DropdownItem
                                    key={status.id}
                                    textValue={status.name}
                                    onClick={() =>
                                        handleChangeStatus(status.id)
                                    }
                                >
                                    <div className="flex items-center gap-2">
                                        {status.color && (
                                            <div
                                                className="w-3 h-3 rounded-full"
                                                style={{
                                                    backgroundColor:
                                                        status.color,
                                                }}
                                            />
                                        )}
                                        <span>{status.name}</span>
                                    </div>
                                </DropdownItem>
                            ))}
                        </DropdownMenu>
                    </Dropdown>

                    {/* Importancia */}
                    <Dropdown>
                        <DropdownTrigger>
                            <Button
                                color="primary"
                                isDisabled={isUpdatingStatus || isDeleting}
                                isLoading={isUpdatingImportance}
                                size="sm"
                                startContent={<TagIcon className="w-4 h-4" />}
                                variant="flat"
                            >
                                Cambiar importancia
                            </Button>
                        </DropdownTrigger>
                        <DropdownMenu aria-label="Niveles de importancia">
                            {importanceOptions.map((importance) => (
                                <DropdownItem
                                    key={importance.id}
                                    textValue={importance.name}
                                    onClick={() =>
                                        handleChangeImportance(importance.id)
                                    }
                                >
                                    <div className="flex items-center gap-2">
                                        {importance.color && (
                                            <div
                                                className="w-3 h-3 rounded-full"
                                                style={{
                                                    backgroundColor:
                                                        importance.color,
                                                }}
                                            />
                                        )}
                                        <span>{importance.name}</span>
                                    </div>
                                </DropdownItem>
                            ))}
                        </DropdownMenu>
                    </Dropdown>

                    {/* Eliminar (solo para administradores) */}
                    {isAdmin && onBulkDelete && (
                        <Button
                            color="danger"
                            isDisabled={
                                isUpdatingStatus || isUpdatingImportance
                            }
                            isLoading={isDeleting}
                            size="sm"
                            startContent={<TrashIcon className="w-4 h-4" />}
                            variant="flat"
                            onClick={onOpen}
                        >
                            Eliminar
                        </Button>
                    )}

                    {/* Cerrar modo selección */}
                    <Button
                        isIconOnly
                        aria-label="Salir del modo selección"
                        color="default"
                        size="sm"
                        variant="flat"
                        onClick={endSelectionMode}
                    >
                        <XMarkIcon className="w-4 h-4" />
                    </Button>
                </div>
            </div>

            {/* Modal de confirmación para eliminación */}
            <Modal isOpen={isOpen} onClose={onClose}>
                <ModalContent>
                    <ModalHeader className="flex items-center gap-2 text-danger">
                        <ExclamationTriangleIcon className="w-5 h-5" />
                        Confirmar eliminación
                    </ModalHeader>
                    <ModalBody>
                        <p>
                            ¿Estás seguro de que deseas eliminar{" "}
                            {selectedNotes.length} nota
                            {selectedNotes.length !== 1 ? "s" : ""}?
                        </p>
                        <p className="text-sm text-gray-500 mt-2">
                            Esta acción no se puede deshacer y eliminará
                            definitivamente las notas seleccionadas.
                        </p>
                    </ModalBody>
                    <ModalFooter>
                        <Button
                            isDisabled={isDeleting}
                            variant="light"
                            onClick={onClose}
                        >
                            Cancelar
                        </Button>
                        <Button
                            color="danger"
                            isLoading={isDeleting}
                            onClick={handleConfirmDelete}
                        >
                            Eliminar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </>
    );
}
