---
description: Integración de Clear Thought Tools con AUTO-FLOW V6
globs: clear-thought-integration.mdc
alwaysApply: false
---
# 🧠 CLEAR THOUGHT TOOLS - INTEGRACIÓN V6

> Sistema inteligente de selección y uso de las 11 herramientas Clear Thought

## 📊 MAPEO DE HERRAMIENTAS POR MODO

```mermaid
graph TD
    subgraph "Clear Thought Tools"
        ST[sequentialthinking]
        MM[mentalmodel]
        DP[designpattern]
        PP[programmingparadigm]
        DA[debuggingapproach]
        CR[collaborativereasoning]
        DF[decisionframework]
        MCM[metacognitivemonitoring]
        SM[scientificmethod]
        SA[structuredargumentation]
        VR[visualreasoning]
    end
    
    subgraph "Modes"
        VAN[VAN Mode]
        PLAN[PLAN Mode]
        CREATIVE[CREATIVE Mode]
        IMPLEMENT[IMPLEMENT Mode]
        REFLECT[REFLECT Mode]
        ARCHIVE[ARCHIVE Mode]
        QA[QA Mode]
    end
    
    VAN --> MCM & VR & MM
    PLAN --> ST & DF & SA
    CREATIVE --> DP & MM & SA & DF & CR
    IMPLEMENT --> PP & DA & SM
    REFLECT --> MCM & CR
    ARCHIVE --> VR & SA
    QA --> SM & DA & MCM
    
    style MCM fill:#4da6ff
    style VR fill:#4dbb5f
    style ST fill:#ffa64d
    style DP fill:#d971ff
```

## 🎯 SELECTOR INTELIGENTE

```typescript
export class ClearThoughtSelector {
    private toolUsageStats: Map<string, ToolStats> = new Map();
    private contextHistory: ContextItem[] = [];
    
    selectTool(
        mode: ModeName,
        task: string,
        complexity: number,
        context?: any
    ): ClearThoughtTool {
        // 1. Herramientas primarias por modo
        const primaryTools = this.getPrimaryTools(mode);
        
        // 2. Análisis de keywords en la tarea
        const keywordTool = this.analyzeKeywords(task);
        
        // 3. Histórico de efectividad
        const historicalBest = this.getHistoricalBest(mode, task);
        
        // 4. Complejidad y contexto
        const complexityTool = this.getComplexityTool(complexity);
        
        // 5. Scoring y selección final
        return this.scoringAlgorithm({
            primaryTools,
            keywordTool,
            historicalBest,
            complexityTool,
            context
        });
    }
    
    private getPrimaryTools(mode: ModeName): ClearThoughtTool[] {
        const mapping = {
            VAN: ['metacognitivemonitoring', 'visualreasoning'],
            PLAN: ['sequentialthinking', 'decisionframework'],
            CREATIVE: ['designpattern', 'mentalmodel', 'structuredargumentation'],
            IMPLEMENT: ['programmingparadigm', 'debuggingapproach'],
            REFLECT: ['metacognitivemonitoring', 'collaborativereasoning'],
            ARCHIVE: ['visualreasoning', 'structuredargumentation'],
            QA: ['scientificmethod', 'debuggingapproach']
        };
        
        return mapping[mode] || [];
    }
}
```

## 📈 USO EFECTIVO POR HERRAMIENTA

### 1. sequentialthinking
**Mejor para**: Planificación paso a paso, descomponer tareas complejas
```typescript
// Ejemplo de uso
const plan = await think.sequentialthinking({
    thought: "Necesito implementar sistema de autenticación",
    thoughtNumber: 1,
    totalThoughts: 5,
    nextThoughtNeeded: true,
    branchFromThought: null,
    needsMoreThoughts: true
});
```

### 2. mentalmodel
**Mejor para**: Análisis de principios fundamentales, simplificar problemas complejos
```typescript
const analysis = await think.mentalmodel({
    modelName: 'first_principles',
    problem: componentDescription,
    reasoning: 'Descomponer en elementos fundamentales',
    steps: ['Identificar asunciones', 'Validar necesidades reales'],
    conclusion: 'Approach simplificado'
});
```

### 3. designpattern
**Mejor para**: Arquitectura, patrones de diseño, decisiones técnicas
```typescript
const pattern = await think.designpattern({
    patternName: 'modular_architecture',
    context: 'Sistema de pagos escalable',
    implementation: ['Módulos independientes', 'APIs bien definidas'],
    benefits: ['Escalabilidad', 'Mantenibilidad'],
    tradeoffs: ['Complejidad inicial'],
    languages: ['TypeScript', 'Node.js']
});
```

### 4. programmingparadigm
**Mejor para**: Seleccionar paradigma de programación, estructurar código
```typescript
const paradigm = await think.programmingparadigm({
    paradigmName: 'functional',
    problem: 'Procesamiento de datos inmutables',
    approach: ['Pure functions', 'Composición'],
    benefits: ['Predecible', 'Testeable'],
    limitations: ['Curva de aprendizaje'],
    languages: ['TypeScript'],
    codeExample: '// Example implementation'
});
```

### 5. debuggingapproach
**Mejor para**: Resolver bugs, encontrar errores, troubleshooting
```typescript
const debug = await think.debuggingapproach({
    approachName: 'binary_search',
    issue: 'Performance degradation en producción',
    steps: ['Aislar componentes', 'Medir tiempos', 'Identificar bottleneck'],
    findings: 'Query N+1 en ORM',
    resolution: 'Implementar eager loading'
});
```

### 6. collaborativereasoning
**Mejor para**: Múltiples perspectivas, revisión de código, decisiones de equipo
```typescript
const collaboration = await think.collaborativereasoning({
    topic: 'Arquitectura de microservicios',
    personas: [
        { id: 'architect', expertise: ['System Design'], perspective: 'Escalabilidad' },
        { id: 'developer', expertise: ['Implementation'], perspective: 'Simplicidad' },
        { id: 'devops', expertise: ['Operations'], perspective: 'Mantenibilidad' }
    ],
    contributions: [],
    consensusPoints: ['Necesidad de service mesh'],
    disagreements: [{ topic: 'Granularidad de servicios', positions: [] }],
    stage: 'ideation',
    nextContributionNeeded: true
});
```

### 7. decisionframework
**Mejor para**: Decisiones con múltiples opciones, trade-offs, evaluación sistemática
```typescript
const decision = await think.decisionframework({
    decisionStatement: 'Seleccionar base de datos para analytics',
    options: [
        { name: 'PostgreSQL', description: 'RDBMS tradicional con extensiones' },
        { name: 'ClickHouse', description: 'OLAP optimizado para analytics' }
    ],
    criteria: [
        { name: 'Performance', weight: 0.4 },
        { name: 'Costo', weight: 0.3 },
        { name: 'Mantenimiento', weight: 0.3 }
    ],
    analysisType: 'weighted-criteria',
    stage: 'evaluation',
    nextStageNeeded: false
});
```

### 8. metacognitivemonitoring
**Mejor para**: Auto-evaluación, confidence tracking, identificar gaps de conocimiento
```typescript
const monitoring = await think.metacognitivemonitoring({
    task: 'Implementar sistema de caching distribuido',
    knowledgeAssessment: {
        domain: 'Distributed Systems',
        knowledgeLevel: 'proficient',
        confidenceScore: 0.7,
        knownLimitations: ['Consistencia eventual', 'Partitioning strategies']
    },
    overallConfidence: 0.75,
    uncertaintyAreas: ['Redis Cluster configuration'],
    recommendedApproach: 'Investigar más sobre sharding',
    stage: 'planning',
    nextAssessmentNeeded: true
});
```

### 9. scientificmethod
**Mejor para**: Testing sistemático, validación de hipótesis, experimentos
```typescript
const experiment = await think.scientificmethod({
    question: '¿Mejorará el performance con caching?',
    hypothesis: {
        statement: 'Caching reducirá latencia en 50%',
        variables: [
            { name: 'Cache hit rate', type: 'dependent' },
            { name: 'Cache size', type: 'independent' }
        ],
        assumptions: ['Tráfico constante'],
        confidence: 0.8,
        status: 'testing'
    },
    stage: 'experiment',
    nextStageNeeded: true
});
```

### 10. structuredargumentation
**Mejor para**: Documentar decisiones, justificar approaches, pros/cons
```typescript
const argument = await think.structuredargumentation({
    claim: 'Microservicios es la mejor arquitectura para este proyecto',
    premises: [
        'Necesitamos escalar independientemente',
        'Tenemos equipos autónomos',
        'Los dominios están bien definidos'
    ],
    conclusion: 'Microservicios permitirá escalabilidad y autonomía',
    argumentType: 'thesis',
    confidence: 0.85,
    strengths: ['Alineado con estructura organizacional'],
    weaknesses: ['Complejidad operacional'],
    nextArgumentNeeded: false
});
```

### 11. visualreasoning
**Mejor para**: Crear diagramas, mapear sistemas, visualizar arquitectura
```typescript
const diagram = await think.visualreasoning({
    operation: 'create',
    diagramType: 'flowchart',
    diagramId: 'auth-flow',
    elements: [
        { id: 'user', type: 'node', properties: { label: 'Usuario' } },
        { id: 'login', type: 'node', properties: { label: 'Login' } },
        { id: 'edge1', type: 'edge', source: 'user', target: 'login' }
    ],
    hypothesis: 'Flow de autenticación',
    iteration: 1,
    nextOperationNeeded: true
});
```

## 📊 MÉTRICAS Y OPTIMIZACIÓN

```typescript
interface ToolEffectiveness {
    tool: ClearThoughtTool;
    mode: ModeName;
    usageCount: number;
    successRate: number;
    avgTimeToInsight: number;
    bestUseCases: string[];
    recommendations: string[];
}

// Sistema de tracking automático
class ToolMetricsTracker {
    trackUsage(tool: string, context: any, outcome: any) {
        const metric = {
            timestamp: Date.now(),
            tool,
            mode: context.mode,
            complexity: context.complexity,
            timeToComplete: context.duration,
            quality: this.assessQuality(outcome),
            useful: this.wasUseful(outcome)
        };
        
        this.updateStats(tool, metric);
        this.updateRecommendations(tool, context);
    }
}
```

## 🔄 INTEGRACIÓN CON AUTO-SAVE

```typescript
// Auto-save incluye métricas de Clear Thought
interface EnhancedSnapshot {
    clearThoughtMetrics: {
        toolsUsed: Map<string, number>;
        effectiveness: Map<string, number>;
        insights: string[];
        bestCombinations: ToolCombination[];
    };
}
```

---

**Clear Thought Tools totalmente integrados con AUTO-FLOW V6**
