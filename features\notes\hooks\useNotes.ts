"use client";

import { useState } from "react";
import { addToast } from "@heroui/react";
import useSWR from "swr";

import { createNote } from "@/features/notes/actions/create";
import { updateNote } from "@/features/notes/actions/update";
import { getNoteById } from "@/features/notes/actions/list";

// Tipos para la creación y edición de notas
export interface NoteFormData {
    content: string;
    orderId: string;
    statusId: string;
    importanceId: string;
}

export interface UpdateNoteFormData extends NoteFormData {
    id: string;
}

interface CreateNoteResponse {
    success: boolean;
    data?: any;
    error?: string;
}

/**
 * Hook para gestionar la creación de notas usando Server Actions
 */
export function useCreateNote() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Función para crear una nueva nota
    const createNoteAction = async (
        data: NoteFormData,
    ): Promise<CreateNoteResponse> => {
        setIsLoading(true);
        setError(null);

        try {
            // Validaciones básicas
            if (!data.content.trim()) {
                throw new Error("El contenido de la nota es obligatorio");
            }

            if (!data.orderId) {
                throw new Error("Debes seleccionar una orden");
            }

            if (!data.statusId) {
                throw new Error("Debes seleccionar un estado");
            }

            if (!data.importanceId) {
                throw new Error("Debes seleccionar una importancia");
            }

            // Llamada a la Server Action para crear la nota
            const result = await createNote(data);

            if (!result.success) {
                throw new Error(result.error || "Error al crear la nota");
            }

            // Mostrar notificación de éxito
            addToast({
                title: "Éxito",
                description: "Nota creada con éxito",
                color: "success",
            });

            return {
                success: true,
                data: result.data,
            };
        } catch (err: any) {
            // Mostrar error y devolverlo
            const errorMessage = err.message || "Error al crear la nota";

            setError(errorMessage);
            addToast({
                title: "Error",
                description: errorMessage,
                color: "danger",
            });

            return {
                success: false,
                error: errorMessage,
            };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createNote: createNoteAction,
        isLoading,
        error,
    };
}

/**
 * Hook para gestionar la actualización de notas usando Server Actions
 */
export function useUpdateNote() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Función para actualizar una nota existente
    const updateNoteAction = async (
        data: UpdateNoteFormData,
    ): Promise<CreateNoteResponse> => {
        setIsLoading(true);
        setError(null);

        try {
            // Validaciones básicas
            if (!data.id) {
                throw new Error("ID de nota no válido");
            }

            if (!data.content.trim()) {
                throw new Error("El contenido de la nota es obligatorio");
            }

            if (!data.orderId) {
                throw new Error("Debes seleccionar una orden");
            }

            if (!data.statusId) {
                throw new Error("Debes seleccionar un estado");
            }

            if (!data.importanceId) {
                throw new Error("Debes seleccionar una importancia");
            }

            // Llamada a la Server Action para actualizar la nota
            const result = await updateNote(data);

            if (!result.success) {
                throw new Error(result.error || "Error al actualizar la nota");
            }

            // Mostrar notificación de éxito
            addToast({
                title: "Éxito",
                description: "Nota actualizada con éxito",
                color: "success",
            });

            return {
                success: true,
                data: result.data,
            };
        } catch (err: any) {
            // Mostrar error y devolverlo
            const errorMessage = err.message || "Error al actualizar la nota";

            setError(errorMessage);
            addToast({
                title: "Error",
                description: errorMessage,
                color: "danger",
            });

            return {
                success: false,
                error: errorMessage,
            };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        updateNote: updateNoteAction,
        isLoading,
        error,
    };
}

/**
 * Hook para obtener una nota por su ID
 */
export function useGetNote(id: string) {
    const fetcher = async () => {
        if (!id) return null;
        const result = await getNoteById(id);

        if (!result.success) {
            throw new Error(result.error || "Error al obtener la nota");
        }

        return result.data;
    };

    const { data, error, isLoading, mutate } = useSWR(
        id ? `note-${id}` : null,
        fetcher,
    );

    return {
        note: data,
        isLoading,
        error: error?.message,
        mutate,
    };
}
