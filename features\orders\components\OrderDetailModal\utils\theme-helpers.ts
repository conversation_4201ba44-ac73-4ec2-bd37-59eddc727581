/**
 * Theme Helper Utilities
 * Provides safe theme detection and color management for OrderDetailModal
 */

/**
 * Safe theme hook with automatic fallback
 * Never throws, always returns a valid theme
 */
export const useThemeSafe = (): string => {
    try {
        // Intentar detectar tema desde el DOM
        if (typeof window !== "undefined") {
            // Buscar en el html o body
            const htmlElement = document.documentElement;
            const bodyElement = document.body;

            // Verificar clases de tema
            if (
                htmlElement.classList.contains("dark") ||
                bodyElement.classList.contains("dark")
            ) {
                return "dark";
            }
            if (
                htmlElement.classList.contains("light") ||
                bodyElement.classList.contains("light")
            ) {
                return "light";
            }

            // Verificar atributo data-theme
            const dataTheme =
                htmlElement.getAttribute("data-theme") ||
                bodyElement.getAttribute("data-theme");

            if (dataTheme) {
                return dataTheme;
            }

            // Detectar preferencia del sistema
            const isDark = window.matchMedia(
                "(prefers-color-scheme: dark)",
            ).matches;

            return isDark ? "dark" : "light";
        }

        // Default seguro para SSR
        return "light";
    } catch (error) {
        // Fallback final
        return "light";
    }
};

/**
 * Get theme-specific colors for components
 * @param theme - Current theme ('light' or 'dark')
 * @returns Object with all color classes for the theme
 */
export const getThemeColors = (theme: string) => {
    const colors = {
        light: {
            // Header colors - Alto contraste para tema light
            headerBg: "bg-primary-50",
            headerText: "text-primary-900",
            headerBorder: "border-b border-primary-200",
            headerHover: "hover:bg-primary-100",

            // Tab colors
            tabText: "text-foreground",
            tabActive: "text-primary-700",
            tabInactive: "text-gray-600",

            // Badge colors
            badgeBg: "bg-primary-100",
            badgeText: "text-primary-700",

            // Button colors
            buttonText: "text-primary-900",
            buttonHover: "hover:bg-primary-100",

            // Status chip adjustments
            chipTextDark: "text-gray-900", // Para chips de color claro
            chipTextLight: "text-white", // Para chips de color oscuro
        },
        dark: {
            // Header colors - Mantener diseño gradiente en dark
            headerBg: "bg-gradient-to-r from-primary-500 to-primary-600",
            headerText: "text-white",
            headerBorder: "",
            headerHover: "hover:bg-white/10",

            // Tab colors
            tabText: "text-foreground",
            tabActive: "text-primary-400",
            tabInactive: "text-gray-400",

            // Badge colors
            badgeBg: "bg-primary-500/20",
            badgeText: "text-primary-300",

            // Button colors
            buttonText: "text-white",
            buttonHover: "hover:bg-white/20",

            // Status chip adjustments
            chipTextDark: "text-gray-900",
            chipTextLight: "text-white",
        },
    };

    // Retornar colores del tema o default a light si no existe
    return colors[theme as keyof typeof colors] || colors.light;
};

/**
 * Helper para detectar si un color es claro
 * Útil para determinar el color de texto en chips/badges dinámicos
 * @param color - Color en formato hex (#RRGGBB)
 * @returns true si el color es claro, false si es oscuro
 */
export const isLightColor = (color: string): boolean => {
    // Remover # si existe
    const hex = color.replace("#", "");

    // Convertir a RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Calcular luminosidad (formula estándar)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Valores > 128 se consideran claros
    return brightness > 128;
};

/**
 * Get contrasting text color for any background
 * @param backgroundColor - Background color in hex format
 * @param theme - Current theme
 * @returns Appropriate text color class
 */
export const getContrastingTextColor = (
    backgroundColor: string,
    theme: string,
): string => {
    const colors = getThemeColors(theme);

    return isLightColor(backgroundColor)
        ? colors.chipTextDark
        : colors.chipTextLight;
};
