import React from "react";
import {
    SparklesIcon,
    ClockIcon,
    CogIcon,
    TruckIcon,
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    QuestionMarkCircleIcon,
} from "@heroicons/react/24/outline";

// Mapeo de iconName a componentes de iconos reales
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
    SparklesIcon: SparklesIcon,
    ClockIcon: ClockIcon,
    CogIcon: CogIcon,
    TruckIcon: TruckIcon,
    CheckCircleIcon: CheckCircleIcon,
    XCircleIcon: XCircleIcon,
    ExclamationTriangleIcon: ExclamationTriangleIcon,
    QuestionMarkCircleIcon: QuestionMarkCircleIcon,
};

// Configuración de estados por defecto (fallback)
export const defaultStatusConfig = {
    Nuevo: {
        color: "primary" as const,
        icon: SparklesIcon,
        label: "Nuevo",
    },
    Pendiente: {
        color: "warning" as const,
        icon: ClockIcon,
        label: "Pendiente",
    },
    "En Proceso": {
        color: "primary" as const,
        icon: CogIcon,
        label: "En Proceso",
    },
    "En Producción": {
        color: "secondary" as const,
        icon: TruckIcon,
        label: "En Producción",
    },
    Completado: {
        color: "success" as const,
        icon: CheckCircleIcon,
        label: "Completado",
    },
    Entregado: {
        color: "success" as const,
        icon: CheckCircleIcon,
        label: "Entregado",
    },
    Cancelado: {
        color: "danger" as const,
        icon: XCircleIcon,
        label: "Cancelado",
    },
};

export interface StatusConfig {
    color:
        | "default"
        | "primary"
        | "secondary"
        | "success"
        | "warning"
        | "danger";
    icon: React.ComponentType<{ className?: string }>;
    label: string;
}

/**
 * Obtiene la configuración de estado basada en el objeto de estado
 */
export function getStatusConfig(
    status:
        | string
        | { id: string; name: string; iconName?: string; color?: string }
        | undefined,
): StatusConfig {
    // Si no hay estado, devolver configuración por defecto
    if (!status) {
        return {
            color: "default",
            icon: QuestionMarkCircleIcon,
            label: "Sin estado",
        };
    }

    // Extraer el nombre del estado
    const statusName = typeof status === "string" ? status : status.name;

    // Si el estado es un objeto con iconName, intentar usar ese icono
    if (
        typeof status === "object" &&
        status.iconName &&
        iconMap[status.iconName]
    ) {
        return {
            color: mapColorToHeroUI(status.color) || "default",
            icon: iconMap[status.iconName],
            label: status.name,
        };
    }

    // Buscar en la configuración por defecto
    const defaultConfig = (defaultStatusConfig as any)[statusName];

    if (defaultConfig) {
        return defaultConfig;
    }

    // Configuración de respaldo
    return {
        color: "default",
        icon: QuestionMarkCircleIcon,
        label: statusName,
    };
}

/**
 * Mapea colores de la base de datos a colores de HeroUI
 */
function mapColorToHeroUI(color?: string): StatusConfig["color"] | undefined {
    if (!color) return undefined;

    const colorMap: Record<string, StatusConfig["color"]> = {
        blue: "primary",
        green: "success",
        yellow: "warning",
        red: "danger",
        purple: "secondary",
        gray: "default",
    };

    return colorMap[color.toLowerCase()] || "default";
}

/**
 * Renderiza un icono de estado con el tamaño especificado
 */
export function renderStatusIcon(
    status:
        | string
        | { id: string; name: string; iconName?: string; color?: string }
        | undefined,
    className: string = "w-5 h-5",
) {
    const config = getStatusConfig(status);
    const Icon = config.icon;

    return <Icon className={className} />;
}
