// Helper functions para manejar CSRF tokens en NextAuth

/**
 * Obtiene el token CSRF del cliente
 * @returns El token CSRF o null si no se encuentra
 */
export function getCSRFToken(): string | null {
    if (typeof window === "undefined") return null;

    // Buscar el token CSRF en las cookies
    const cookies = document.cookie.split(";");

    for (const cookie of cookies) {
        const [name, value] = cookie.trim().split("=");

        if (name === "authjs.csrf-token" || name === "next-auth.csrf-token") {
            // El token viene en formato "token|hash", necesitamos solo el token
            const [token] = decodeURIComponent(value).split("|");

            return token;
        }
    }

    // Si no encontramos en cookies, buscar en meta tags (fallback)
    const metaTag = document.querySelector('meta[name="csrf-token"]');

    if (metaTag) {
        return metaTag.getAttribute("content");
    }

    return null;
}

/**
 * Refresca el token CSRF haciendo una petición a NextAuth
 * @returns Promise con el nuevo token CSRF
 */
export async function refreshCSRFToken(): Promise<string | null> {
    try {
        const response = await fetch("/api/auth/csrf", {
            credentials: "include",
        });

        if (!response.ok) {
            console.error("Failed to refresh CSRF token:", response.status);

            return null;
        }

        const data = await response.json();

        return data.csrfToken || null;
    } catch (error) {
        console.error("Error refreshing CSRF token:", error);

        return null;
    }
}

/**
 * Verifica si existe un token CSRF válido
 * @returns true si existe un token CSRF
 */
export function hasCSRFToken(): boolean {
    return getCSRFToken() !== null;
}

/**
 * Helper para incluir el token CSRF en las peticiones
 * @param headers Headers existentes
 * @returns Headers con el token CSRF incluido
 */
export async function withCSRFToken(
    headers: HeadersInit = {},
): Promise<HeadersInit> {
    let csrfToken = getCSRFToken();

    // Si no hay token, intentar refrescarlo
    if (!csrfToken) {
        console.log("No CSRF token found, attempting to refresh...");
        csrfToken = await refreshCSRFToken();
    }

    if (csrfToken) {
        return {
            ...headers,
            "X-CSRF-Token": csrfToken,
        };
    }

    return headers;
}
