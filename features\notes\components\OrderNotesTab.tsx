"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button, Input, Select, SelectItem, Chip } from "@heroui/react";
import {
    MagnifyingGlassIcon,
    AdjustmentsHorizontalIcon,
    PlusIcon,
    SparklesIcon,
    DocumentIcon,
} from "@heroicons/react/24/outline";
import { addToast } from "@heroui/toast";
import clsx from "clsx";

import { RippleButton } from "@/shared/components/ui/RippleButton";
import { useOrderNotes } from "@/features/orders/hooks/useOrderNotes";
import { springConfig } from "@/shared/utils/ui/animations";

import { InlineNoteEditor } from "./InlineNoteEditor";
import { EnhancedNoteCard } from "./EnhancedNoteCard";
import { NoteSkeletonList } from "./NoteSkeletons";

interface OrderNotesTabProps {
    orderId: string;
    importanceOptions: { id: string; name: string; color?: string | null }[];
    statusOptions: { id: string; name: string; color?: string | null }[];
    authorId?: string;
    userRole?: string;
}

export function OrderNotesTab({
    orderId,
    importanceOptions,
    statusOptions,
    authorId = "",
    userRole = "",
}: OrderNotesTabProps) {
    // Estados locales para filtros y UI
    const [searchText, setSearchText] = useState("");
    const [selectedImportance, setSelectedImportance] = useState<string>("");
    const [selectedStatus, setSelectedStatus] = useState<string>("");
    const [selectedAuthor, setSelectedAuthor] = useState<string>("");
    const [showEditor, setShowEditor] = useState(false);
    const [editingNote, setEditingNote] = useState<any>(null);
    const [selectedNotes, setSelectedNotes] = useState<string[]>([]);

    // Hook de notas con filtros
    const {
        notes,
        totalCount,
        isLoading,
        isCreating,
        isUpdating,
        isDeleting,
        error,
        createNote,
        updateNote,
        deleteNote,
        changeFilters,
        refreshNotes,
        bulkUpdateStatus,
        bulkUpdateImportance,
        bulkDeleteNotes,
    } = useOrderNotes({
        orderId,
        initialFilters: {
            searchText,
            importanceId: selectedImportance,
            statusId: selectedStatus,
            authorId: selectedAuthor,
        },
    });

    // Actualizar filtros cuando cambian los estados locales
    React.useEffect(() => {
        changeFilters({
            searchText,
            importanceId: selectedImportance || undefined,
            statusId: selectedStatus || undefined,
            authorId: selectedAuthor || undefined,
        });
    }, [
        searchText,
        selectedImportance,
        selectedStatus,
        selectedAuthor,
        changeFilters,
    ]);

    // Manejar creación de nota
    const handleCreateNote = async (noteData: {
        content: string;
        importanceId: string;
        statusId: string;
    }) => {
        const result = await createNote({
            ...noteData,
            title: "", // Título vacío por defecto
        });

        if (result.success) {
            setShowEditor(false);
            addToast({
                title: "Éxito",
                description: "Nota creada correctamente",
                color: "success",
            });
        } else {
            addToast({
                title: "Error",
                description: result.error || "Error al crear la nota",
                color: "danger",
            });
        }

        return result;
    };

    // Manejar edición de nota
    const handleUpdateNote = async (noteData: {
        noteId: string;
        content: string;
        importanceId: string;
        statusId: string;
    }) => {
        const result = await updateNote(noteData);

        if (result.success) {
            setEditingNote(null);
            addToast({
                title: "Éxito",
                description: "Nota actualizada correctamente",
                color: "success",
            });
        } else {
            addToast({
                title: "Error",
                description: result.error || "Error al actualizar la nota",
                color: "danger",
            });
        }

        return result;
    };

    // Manejar eliminación de nota
    const handleDeleteNote = async (noteId: string) => {
        const result = await deleteNote(noteId);

        if (result.success) {
            addToast({
                title: "Éxito",
                description: "Nota eliminada correctamente",
                color: "success",
            });
        } else {
            addToast({
                title: "Error",
                description: result.error || "Error al eliminar la nota",
                color: "danger",
            });
        }
    };

    // Limpiar filtros
    const clearFilters = () => {
        setSearchText("");
        setSelectedImportance("");
        setSelectedStatus("");
        setSelectedAuthor("");
    };

    // Toggle selección de nota
    const toggleNoteSelection = (noteId: string) => {
        setSelectedNotes((prev) =>
            prev.includes(noteId)
                ? prev.filter((id) => id !== noteId)
                : [...prev, noteId],
        );
    };

    // Seleccionar todas las notas
    const selectAllNotes = () => {
        setSelectedNotes(notes.map((note: any) => note.id));
    };

    // Deseleccionar todas las notas
    const clearSelection = () => {
        setSelectedNotes([]);
    };

    return (
        <div className="space-y-6">
            {/* Header con título y botón de nueva nota */}
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center"
                initial={{ opacity: 0, y: -20 }}
                transition={springConfig.gentle}
            >
                <div className="space-y-1">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-400 bg-clip-text text-transparent">
                        Notas de la Orden
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                        {totalCount} {totalCount === 1 ? "nota" : "notas"} total
                        {totalCount > 0 && notes.length < totalCount
                            ? ` (mostrando ${notes.length})`
                            : ""}
                    </p>
                </div>

                <RippleButton
                    className={clsx(
                        "bg-gradient-to-r from-primary-500 to-primary-600",
                        "hover:from-primary-600 hover:to-primary-700",
                        "text-white shadow-lg transition-all duration-200",
                    )}
                    startContent={<PlusIcon className="h-4 w-4" />}
                    onClick={() => setShowEditor(true)}
                >
                    Nueva Nota
                </RippleButton>
            </motion.div>

            {/* Editor de nueva nota */}
            <AnimatePresence>
                {showEditor && (
                    <motion.div
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        initial={{ opacity: 0, height: 0 }}
                        transition={springConfig.gentle}
                    >
                        <InlineNoteEditor
                            autoFocus
                            importanceOptions={importanceOptions}
                            mode="create"
                            orderId={orderId}
                            statusOptions={statusOptions}
                            onCancel={() => setShowEditor(false)}
                            onSave={handleCreateNote}
                        />
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Filtros y búsqueda */}
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className={clsx(
                    "bg-white dark:bg-gray-900",
                    "p-4 rounded-xl border border-gray-200 dark:border-gray-700",
                    "shadow-sm",
                )}
                initial={{ opacity: 0, y: -10 }}
                transition={{ ...springConfig.gentle, delay: 0.1 }}
            >
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Búsqueda */}
                    <Input
                        className="w-full"
                        placeholder="Buscar en notas..."
                        startContent={
                            <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                        }
                        value={searchText}
                        onChange={(e) => setSearchText(e.target.value)}
                    />

                    {/* Filtro por importancia */}
                    <Select
                        placeholder="Filtrar por importancia"
                        selectedKeys={
                            selectedImportance ? [selectedImportance] : []
                        }
                        onChange={(e) => setSelectedImportance(e.target.value)}
                    >
                        {importanceOptions.map((option) => (
                            <SelectItem key={option.id} textValue={option.name}>
                                <div className="flex items-center gap-2">
                                    {option.color && (
                                        <div
                                            className="w-3 h-3 rounded-full"
                                            style={{
                                                backgroundColor: option.color,
                                            }}
                                        />
                                    )}
                                    <span>{option.name}</span>
                                </div>
                            </SelectItem>
                        ))}
                    </Select>

                    {/* Filtro por estado */}
                    <Select
                        placeholder="Filtrar por estado"
                        selectedKeys={selectedStatus ? [selectedStatus] : []}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                    >
                        {statusOptions.map((option) => (
                            <SelectItem key={option.id} textValue={option.name}>
                                <div className="flex items-center gap-2">
                                    {option.color && (
                                        <div
                                            className="w-3 h-3 rounded-full"
                                            style={{
                                                backgroundColor: option.color,
                                            }}
                                        />
                                    )}
                                    <span>{option.name}</span>
                                </div>
                            </SelectItem>
                        ))}
                    </Select>

                    {/* Botón limpiar filtros */}
                    <Button
                        className="w-full"
                        startContent={
                            <AdjustmentsHorizontalIcon className="h-4 w-4" />
                        }
                        variant="flat"
                        onClick={clearFilters}
                    >
                        Limpiar Filtros
                    </Button>
                </div>

                {/* Acciones en lote */}
                <AnimatePresence>
                    {selectedNotes.length > 0 && (
                        <motion.div
                            animate={{ opacity: 1, height: "auto" }}
                            className="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-700/50"
                            exit={{ opacity: 0, height: 0 }}
                            initial={{ opacity: 0, height: 0 }}
                            transition={springConfig.gentle}
                        >
                            <div className="flex flex-wrap gap-2 items-center">
                                <Chip color="primary" variant="flat">
                                    {selectedNotes.length} seleccionada
                                    {selectedNotes.length > 1 ? "s" : ""}
                                </Chip>

                                <Button
                                    size="sm"
                                    variant="flat"
                                    onClick={clearSelection}
                                >
                                    Deseleccionar
                                </Button>

                                <Button
                                    isDisabled={
                                        selectedNotes.length === notes.length
                                    }
                                    size="sm"
                                    variant="flat"
                                    onClick={selectAllNotes}
                                >
                                    Seleccionar Todo
                                </Button>

                                <Button
                                    color="danger"
                                    size="sm"
                                    variant="flat"
                                    onClick={() => {
                                        if (
                                            window.confirm(
                                                `¿Estás seguro de eliminar ${selectedNotes.length} nota${selectedNotes.length > 1 ? "s" : ""}?`,
                                            )
                                        ) {
                                            bulkDeleteNotes(selectedNotes).then(
                                                () => {
                                                    clearSelection();
                                                    addToast({
                                                        title: "Éxito",
                                                        description:
                                                            "Notas eliminadas correctamente",
                                                        color: "success",
                                                    });
                                                },
                                            );
                                        }
                                    }}
                                >
                                    Eliminar Seleccionadas
                                </Button>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </motion.div>

            {/* Lista de notas */}
            <motion.div
                animate={{ opacity: 1 }}
                initial={{ opacity: 0 }}
                transition={{ ...springConfig.gentle, delay: 0.2 }}
            >
                {isLoading ? (
                    <NoteSkeletonList count={3} />
                ) : notes.length === 0 ? (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className={clsx(
                            "bg-gray-50 dark:bg-gray-800",
                            "p-12 text-center rounded-xl border border-gray-200 dark:border-gray-700",
                        )}
                        initial={{ opacity: 0, y: 20 }}
                        transition={springConfig.gentle}
                    >
                        <SparklesIcon className="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
                        <h4 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                            No hay notas todavía
                        </h4>
                        <p className="text-gray-500 dark:text-gray-500 mb-6">
                            {searchText || selectedImportance || selectedStatus
                                ? "No se encontraron notas que coincidan con los filtros"
                                : "Crea la primera nota para esta orden"}
                        </p>
                        {!searchText &&
                            !selectedImportance &&
                            !selectedStatus && (
                                <Button
                                    className="bg-gradient-to-r from-primary-500 to-primary-600 text-white"
                                    startContent={
                                        <PlusIcon className="h-4 w-4" />
                                    }
                                    onClick={() => setShowEditor(true)}
                                >
                                    Crear Primera Nota
                                </Button>
                            )}
                    </motion.div>
                ) : (
                    (() => {
                        // Separar notas por prioridad
                        const priorityNotes = notes.filter((note: any) => {
                            const importance = importanceOptions.find(
                                (opt) => opt.id === note.importanceId,
                            );

                            return (
                                importance?.name === "Alta" ||
                                importance?.name === "Urgente"
                            );
                        });
                        const regularNotes = notes.filter((note: any) => {
                            const importance = importanceOptions.find(
                                (opt) => opt.id === note.importanceId,
                            );

                            return (
                                importance?.name !== "Alta" &&
                                importance?.name !== "Urgente"
                            );
                        });

                        return (
                            <div className="relative max-h-[80vh] notes-scroll-container custom-scrollbar">
                                <div className="space-y-6 notes-content-wrapper">
                                    <AnimatePresence mode="popLayout">
                                        {/* Sección de notas prioritarias */}
                                        {priorityNotes.length > 0 && (
                                            <div className="bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 p-5 rounded-xl border border-red-200 dark:border-red-800 shadow-sm mx-2">
                                                <h4 className="text-sm font-bold text-red-700 dark:text-red-400 mb-4 flex items-center gap-2">
                                                    <SparklesIcon className="w-4 h-4" />
                                                    Notas Prioritarias (
                                                    {priorityNotes.length})
                                                </h4>
                                                <div className="space-y-8 px-2">
                                                    {priorityNotes.map(
                                                        (
                                                            note: any,
                                                            index: any,
                                                        ) => (
                                                            <motion.div
                                                                key={note.id}
                                                                layout
                                                                animate={{
                                                                    opacity: 1,
                                                                    y: 0,
                                                                }}
                                                                className="relative"
                                                                exit={{
                                                                    opacity: 0,
                                                                    y: -20,
                                                                }}
                                                                initial={{
                                                                    opacity: 0,
                                                                    y: 20,
                                                                }}
                                                                style={{
                                                                    zIndex:
                                                                        priorityNotes.length -
                                                                        index,
                                                                }}
                                                                transition={{
                                                                    ...springConfig.gentle,
                                                                    delay:
                                                                        index *
                                                                        0.05,
                                                                }}
                                                            >
                                                                {editingNote?.id ===
                                                                note.id ? (
                                                                    <InlineNoteEditor
                                                                        autoFocus
                                                                        importanceOptions={
                                                                            importanceOptions
                                                                        }
                                                                        initialContent={
                                                                            note.content
                                                                        }
                                                                        initialImportanceId={
                                                                            note.importanceId
                                                                        }
                                                                        initialStatusId={
                                                                            note.statusId
                                                                        }
                                                                        mode="edit"
                                                                        noteId={
                                                                            note.id
                                                                        }
                                                                        orderId={
                                                                            orderId
                                                                        }
                                                                        statusOptions={
                                                                            statusOptions
                                                                        }
                                                                        onCancel={() =>
                                                                            setEditingNote(
                                                                                null,
                                                                            )
                                                                        }
                                                                        onSave={
                                                                            handleUpdateNote as any
                                                                        }
                                                                    />
                                                                ) : (
                                                                    <EnhancedNoteCard
                                                                        canEdit={
                                                                            true
                                                                        }
                                                                        currentUserId={
                                                                            authorId
                                                                        }
                                                                        importanceOptions={
                                                                            importanceOptions
                                                                        }
                                                                        isDeletable={
                                                                            true
                                                                        }
                                                                        isEditable={
                                                                            true
                                                                        }
                                                                        isPriority={
                                                                            true
                                                                        }
                                                                        isSelected={selectedNotes.includes(
                                                                            note.id,
                                                                        )}
                                                                        note={
                                                                            note
                                                                        }
                                                                        orderId={
                                                                            orderId
                                                                        }
                                                                        statusOptions={
                                                                            statusOptions
                                                                        }
                                                                        userRole={
                                                                            userRole
                                                                        }
                                                                        onDelete={() =>
                                                                            handleDeleteNote(
                                                                                note.id,
                                                                            )
                                                                        }
                                                                        onEdit={() =>
                                                                            setEditingNote(
                                                                                note,
                                                                            )
                                                                        }
                                                                        onToggleSelect={() =>
                                                                            toggleNoteSelection(
                                                                                note.id,
                                                                            )
                                                                        }
                                                                    />
                                                                )}
                                                            </motion.div>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {/* Sección de notas regulares */}
                                        {regularNotes.length > 0 && (
                                            <div className="space-y-4 mx-2">
                                                {regularNotes.length > 0 &&
                                                    priorityNotes.length >
                                                        0 && (
                                                        <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                                                            <DocumentIcon className="w-4 h-4" />
                                                            Otras Notas (
                                                            {
                                                                regularNotes.length
                                                            }
                                                            )
                                                        </h4>
                                                    )}
                                                <div className="space-y-8 px-2">
                                                    {regularNotes.map(
                                                        (
                                                            note: any,
                                                            index: any,
                                                        ) => (
                                                            <motion.div
                                                                key={note.id}
                                                                layout
                                                                animate={{
                                                                    opacity: 1,
                                                                    y: 0,
                                                                }}
                                                                className="relative"
                                                                exit={{
                                                                    opacity: 0,
                                                                    y: -20,
                                                                }}
                                                                initial={{
                                                                    opacity: 0,
                                                                    y: 20,
                                                                }}
                                                                style={{
                                                                    zIndex:
                                                                        regularNotes.length -
                                                                        index,
                                                                }}
                                                                transition={{
                                                                    ...springConfig.gentle,
                                                                    delay:
                                                                        (index +
                                                                            priorityNotes.length) *
                                                                        0.05,
                                                                }}
                                                            >
                                                                {editingNote?.id ===
                                                                note.id ? (
                                                                    <InlineNoteEditor
                                                                        autoFocus
                                                                        importanceOptions={
                                                                            importanceOptions
                                                                        }
                                                                        initialContent={
                                                                            note.content
                                                                        }
                                                                        initialImportanceId={
                                                                            note.importanceId
                                                                        }
                                                                        initialStatusId={
                                                                            note.statusId
                                                                        }
                                                                        mode="edit"
                                                                        noteId={
                                                                            note.id
                                                                        }
                                                                        orderId={
                                                                            orderId
                                                                        }
                                                                        statusOptions={
                                                                            statusOptions
                                                                        }
                                                                        onCancel={() =>
                                                                            setEditingNote(
                                                                                null,
                                                                            )
                                                                        }
                                                                        onSave={
                                                                            handleUpdateNote as any
                                                                        }
                                                                    />
                                                                ) : (
                                                                    <EnhancedNoteCard
                                                                        canEdit={
                                                                            true
                                                                        }
                                                                        currentUserId={
                                                                            authorId
                                                                        }
                                                                        importanceOptions={
                                                                            importanceOptions
                                                                        }
                                                                        isDeletable={
                                                                            true
                                                                        }
                                                                        isEditable={
                                                                            true
                                                                        }
                                                                        isPriority={
                                                                            false
                                                                        }
                                                                        isSelected={selectedNotes.includes(
                                                                            note.id,
                                                                        )}
                                                                        note={
                                                                            note
                                                                        }
                                                                        orderId={
                                                                            orderId
                                                                        }
                                                                        statusOptions={
                                                                            statusOptions
                                                                        }
                                                                        userRole={
                                                                            userRole
                                                                        }
                                                                        onDelete={() =>
                                                                            handleDeleteNote(
                                                                                note.id,
                                                                            )
                                                                        }
                                                                        onEdit={() =>
                                                                            setEditingNote(
                                                                                note,
                                                                            )
                                                                        }
                                                                        onToggleSelect={() =>
                                                                            toggleNoteSelection(
                                                                                note.id,
                                                                            )
                                                                        }
                                                                    />
                                                                )}
                                                            </motion.div>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </AnimatePresence>
                                </div>
                            </div>
                        );
                    })()
                )}
            </motion.div>

            {/* Error display */}
            <AnimatePresence>
                {error && (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl"
                        exit={{ opacity: 0, y: -10 }}
                        initial={{ opacity: 0, y: -10 }}
                        transition={springConfig.gentle}
                    >
                        <p className="text-red-600 dark:text-red-400">
                            Error:{" "}
                            {error.message || "Ha ocurrido un error inesperado"}
                        </p>
                        <Button
                            className="mt-2"
                            color="danger"
                            size="sm"
                            variant="flat"
                            onClick={refreshNotes}
                        >
                            Reintentar
                        </Button>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
}
