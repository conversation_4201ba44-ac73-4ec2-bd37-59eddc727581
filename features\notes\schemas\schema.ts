import { z } from "zod";

export const noteSchema = z.object({
    content: z
        .string()
        .min(1, "El contenido es requerido")
        .max(5000, "El contenido no puede exceder 5000 caracteres")
        .trim(),
    orderId: z.string().min(1, "Debes seleccionar una orden"),
    statusId: z.string().min(1, "Debes seleccionar un estado"),
    importanceId: z.string().min(1, "Debes seleccionar una importancia"),
});

export type NoteFormData = z.infer<typeof noteSchema>;
