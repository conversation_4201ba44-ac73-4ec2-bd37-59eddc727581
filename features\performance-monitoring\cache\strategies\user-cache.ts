/**
 * User-Specific Cache Strategy
 * For per-user data (preferences, cart, session data)
 */

import { CacheType } from "../cache-types";

import { BaseCacheStrategy } from "./base-strategy";

export class UserCacheStrategy extends BaseCacheStrategy {
    constructor() {
        super(CacheType.USER_SPECIFIC);
    }

    /**
     * Get user-specific data
     */
    async getUserData<T>(userId: string, resource: string): Promise<T | null> {
        return this.get({
            tenant: userId,
            resource,
            identifier: "user-data",
        });
    }

    /**
     * Set user-specific data
     */
    async setUserData<T>(
        userId: string,
        resource: string,
        data: T,
        ttl?: number,
    ): Promise<boolean> {
        return this.set(
            {
                tenant: userId,
                resource,
                identifier: "user-data",
            },
            data,
            ttl,
        );
    }

    /**
     * Clear all cache for a specific user
     */
    async clearUserCache(userId: string): Promise<void> {
        const client = await import("../redis-client").then((m) =>
            m.getRedisClient(),
        );

        if (!client) return;

        // Find all keys for this user
        const pattern = `${userId}:*`;
        const keys = await client.keys(pattern);

        if (keys.length > 0) {
            await client.del(...keys);
        }
    }

    /**
     * Refresh user session cache
     */
    async refreshUserSession(
        userId: string,
        sessionData: any,
    ): Promise<boolean> {
        return this.setUserData(
            userId,
            "session",
            sessionData,
            300, // 5 minutes for session
        );
    }
}

// Singleton instance
export const userCache = new UserCacheStrategy();
