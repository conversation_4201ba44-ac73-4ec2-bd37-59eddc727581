/**
 * Optimized React Query hooks with performance tracking
 * Base implementation for migrating from SWR
 */

import {
    useQuery,
    useInfiniteQuery,
    UseQueryOptions,
    UseInfiniteQueryOptions,
} from "@tanstack/react-query";

import { performanceTracker } from "../utils/api-metrics";
import { measurePerformance } from "../utils/web-vitals";

/**
 * Wrapper for fetch with performance tracking
 */
async function trackedFetch(url: string, options?: RequestInit) {
    const startTime = performance.now();

    try {
        const response = await fetch(url, options);
        const duration = performance.now() - startTime;

        // Track API performance
        performanceTracker.trackAPI({
            endpoint: url,
            method: options?.method || "GET",
            duration: Math.round(duration),
            status: response.status,
            timestamp: Date.now(),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
    } catch (error) {
        const duration = performance.now() - startTime;

        // Track failed requests
        performanceTracker.trackAPI({
            endpoint: url,
            method: options?.method || "GET",
            duration: Math.round(duration),
            status: 0,
            timestamp: Date.now(),
        });

        throw error;
    }
}

/**
 * Optimized query hook with performance tracking
 */
export function useOptimizedQuery<TData = unknown>(
    queryKey: string[],
    fetcher: () => Promise<TData>,
    options?: UseQueryOptions<TData>,
) {
    const performanceMark = measurePerformance(`query-${queryKey.join("-")}`);

    return useQuery({
        queryKey,
        queryFn: async () => {
            performanceMark.start();
            try {
                const data = await fetcher();

                performanceMark.end();

                return data;
            } catch (error) {
                performanceMark.end();
                throw error;
            }
        },
        // Optimized defaults
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        refetchOnWindowFocus: false,
        retry: 1,
        ...options,
    });
}

/**
 * Optimized infinite query hook for pagination
 */
export function useOptimizedInfiniteQuery<TData = unknown>(
    queryKey: string[],
    fetcher: ({ pageParam }: { pageParam?: any }) => Promise<TData>,
    options?: UseInfiniteQueryOptions<TData>,
) {
    const performanceMark = measurePerformance(
        `infinite-${queryKey.join("-")}`,
    );

    return useInfiniteQuery({
        queryKey,
        queryFn: async ({ pageParam }) => {
            performanceMark.start();
            try {
                const data = await fetcher({ pageParam });

                performanceMark.end();

                return data;
            } catch (error) {
                performanceMark.end();
                throw error;
            }
        },
        initialPageParam: undefined,
        getNextPageParam: (lastPage: any) => lastPage.nextCursor,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        refetchOnWindowFocus: false,
        ...options,
    });
}

/**
 * Example usage for orders data
 */
export function useOrdersOptimized(params: any) {
    return useOptimizedInfiniteQuery(
        ["orders", params],
        async ({ pageParam }) => {
            const response = await trackedFetch(
                `/api/orders?cursor=${pageParam || ""}&${new URLSearchParams(params)}`,
            );

            return response.json();
        },
    );
}
