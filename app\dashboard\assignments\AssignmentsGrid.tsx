"use client";

import React from "react";
import {
    ClipboardDocumentListIcon,
    CubeTransparentIcon,
    CalendarIcon,
    DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

import {
    Card,
    CardBody,
    CardHeader,
    CardFooter,
    Badge,
    Avatar,
    Progress,
    Tooltip,
} from "@/shared/components/ui/hero-ui-client";

// Mapa de colores para estados
const statusColors = {
    PENDING: "warning",
    IN_PROGRESS: "primary",
    COMPLETED: "success",
    CANCELLED: "danger",
} as const;

// Mapa de textos para estados
const statusLabels = {
    PENDING: "Pendiente",
    IN_PROGRESS: "En Proceso",
    COMPLETED: "Completada",
    CANCELLED: "Cancelada",
} as const;

// Type definitions for the assignment data structure
interface Assignment {
    id: string;
    folio?: string | null;
    contractorId: string;
    garmentSizeId: string;
    quantity: number;
    defects?: number | null;
    isCompleted: boolean;
    orderId: string;
    version: number;
    status: string;
    cancelledAt?: Date | null;
    cancelReason?: string | null;
    createdAt: string;
    updatedAt: string;
    contractor: {
        id: string;
        name: string;
        email?: string | null;
        phone?: string | null;
    };
    garmentSize: {
        id: string;
        size: {
            id: string;
            code: string;
        };
        garment: {
            id: string;
            model: {
                id: string;
                code: string;
                description?: string | null;
            };
            color: {
                id: string;
                name: string;
                hexCode?: string | null;
            };
        };
    };
    order: {
        id: string;
        code: string;
        customer: {
            id: string;
            name: string;
        };
    };
    remissions?: Array<{
        remission: {
            id: string;
            folio: string;
            status?: string;
            printedAt?: Date | null;
        };
    }>;
}

interface AssignmentsGridProps {
    assignments: Assignment[];
    onViewDetails: (assignment: Assignment) => void;
    isLoading?: boolean;
}

export default function AssignmentsGrid({
    assignments,
    onViewDetails,
    isLoading,
}: AssignmentsGridProps) {
    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {[...Array(8)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                        <CardBody className="p-4">
                            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                            <div className="h-3 bg-gray-200 rounded w-1/2" />
                        </CardBody>
                    </Card>
                ))}
            </div>
        );
    }

    if (assignments.length === 0) {
        return (
            <div className="text-center py-12">
                <ClipboardDocumentListIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                    No hay asignaciones para mostrar
                </p>
            </div>
        );
    }

    // Agrupar por estado
    const groupedAssignments = assignments.reduce(
        (acc, assignment) => {
            const status = assignment.status || "PENDING";

            if (!acc[status]) acc[status] = [];
            acc[status].push(assignment);

            return acc;
        },
        {} as Record<string, Assignment[]>,
    );

    const statusOrder = ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED"];

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {statusOrder.map((status) => {
                const statusAssignments = groupedAssignments[status] || [];
                const color = statusColors[status as keyof typeof statusColors];
                const label = statusLabels[status as keyof typeof statusLabels];

                return (
                    <div key={status} className="space-y-4">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="font-semibold text-lg">{label}</h3>
                            <Badge
                                color={color}
                                content={statusAssignments.length}
                                variant="flat"
                            >
                                {statusAssignments.length}
                            </Badge>
                        </div>

                        <div className="space-y-3">
                            {statusAssignments.map((assignment, index) => {
                                const remission =
                                    assignment.remissions?.[0]?.remission;
                                const garment = assignment.garmentSize.garment;
                                const createdDate = new Date(
                                    assignment.createdAt,
                                );
                                const daysSince = Math.floor(
                                    (Date.now() - createdDate.getTime()) /
                                        (1000 * 60 * 60 * 24),
                                );

                                return (
                                    <motion.div
                                        key={assignment.id}
                                        animate={{ opacity: 1, y: 0 }}
                                        initial={{ opacity: 0, y: 20 }}
                                        transition={{ delay: index * 0.05 }}
                                    >
                                        <Card
                                            isPressable
                                            className="hover:shadow-lg transition-shadow cursor-pointer"
                                            onPress={() =>
                                                onViewDetails(assignment)
                                            }
                                        >
                                            <CardHeader className="pb-2">
                                                <div className="flex justify-between items-start w-full">
                                                    <div className="flex items-center gap-2">
                                                        <Avatar
                                                            color="secondary"
                                                            name={
                                                                assignment
                                                                    .contractor
                                                                    .name
                                                            }
                                                            size="sm"
                                                        />
                                                        <div>
                                                            <p className="font-medium text-sm">
                                                                {
                                                                    assignment
                                                                        .contractor
                                                                        .name
                                                                }
                                                            </p>
                                                            <p className="text-xs text-gray-500">
                                                                {
                                                                    assignment
                                                                        .order
                                                                        .code
                                                                }
                                                            </p>
                                                        </div>
                                                    </div>
                                                    {remission && (
                                                        <Tooltip
                                                            content={`Remisión: ${remission.folio}`}
                                                        >
                                                            <DocumentTextIcon className="w-4 h-4 text-green-500" />
                                                        </Tooltip>
                                                    )}
                                                </div>
                                            </CardHeader>

                                            <CardBody className="py-2">
                                                <div className="space-y-2">
                                                    {/* Prenda */}
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <div
                                                                className="w-3 h-3 rounded-full border border-gray-300"
                                                                style={{
                                                                    backgroundColor:
                                                                        garment
                                                                            .color
                                                                            .hexCode ||
                                                                        undefined,
                                                                }}
                                                            />
                                                            <span className="text-sm font-medium">
                                                                {
                                                                    garment
                                                                        .model
                                                                        .code
                                                                }
                                                            </span>
                                                        </div>
                                                        <Badge
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            Talla{" "}
                                                            {
                                                                assignment
                                                                    .garmentSize
                                                                    .size.code
                                                            }
                                                        </Badge>
                                                    </div>

                                                    {/* Cantidad */}
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-1">
                                                            <CubeTransparentIcon className="w-4 h-4 text-gray-400" />
                                                            <span className="text-sm">
                                                                {
                                                                    assignment.quantity
                                                                }{" "}
                                                                prendas
                                                            </span>
                                                        </div>
                                                    </div>

                                                    {/* Progreso visual (placeholder por ahora) */}
                                                    {status ===
                                                        "IN_PROGRESS" && (
                                                        <Progress
                                                            className="mt-2"
                                                            color="primary"
                                                            size="sm"
                                                            value={
                                                                Math.random() *
                                                                100
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            </CardBody>

                                            <CardFooter className="pt-2">
                                                <div className="flex items-center justify-between w-full text-xs text-gray-500">
                                                    <div className="flex items-center gap-1">
                                                        <CalendarIcon className="w-3 h-3" />
                                                        <span>
                                                            {format(
                                                                createdDate,
                                                                "dd/MM",
                                                                {
                                                                    locale: es,
                                                                },
                                                            )}
                                                        </span>
                                                    </div>
                                                    <span
                                                        className={
                                                            daysSince > 7
                                                                ? "text-warning"
                                                                : ""
                                                        }
                                                    >
                                                        {daysSince === 0
                                                            ? "Hoy"
                                                            : `${daysSince} días`}
                                                    </span>
                                                </div>
                                            </CardFooter>
                                        </Card>
                                    </motion.div>
                                );
                            })}
                        </div>

                        {statusAssignments.length === 0 && (
                            <div className="text-center py-8 text-gray-400">
                                <p className="text-sm">Sin asignaciones</p>
                            </div>
                        )}
                    </div>
                );
            })}
        </div>
    );
}
