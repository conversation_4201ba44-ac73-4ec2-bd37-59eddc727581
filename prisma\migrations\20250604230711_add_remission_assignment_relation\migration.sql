/*
  Warnings:

  - You are about to drop the column `assignmentId` on the `Remission` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Remission" DROP CONSTRAINT "Remission_assignmentId_fkey";

-- DropIndex
DROP INDEX "Remission_assignmentId_key";

-- AlterTable
ALTER TABLE "Assignment" ADD COLUMN     "cancelReason" TEXT,
ADD COLUMN     "cancelledAt" TIMESTAMP(3),
ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'ACTIVE',
ADD COLUMN     "version" INTEGER NOT NULL DEFAULT 1;

-- CreateTable
CREATE TABLE "RemissionAssignment" (
    "id" TEXT NOT NULL,
    "remissionId" TEXT NOT NULL,
    "assignmentId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RemissionAssignment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OperationLog" (
    "id" TEXT NOT NULL,
    "operationType" TEXT NOT NULL,
    "entityIds" TEXT[],
    "userId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "metadata" JSONB NOT NULL,
    "error" TEXT,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "compensatedAt" TIMESTAMP(3),

    CONSTRAINT "OperationLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "RemissionAssignment_remissionId_idx" ON "RemissionAssignment"("remissionId");

-- CreateIndex
CREATE INDEX "RemissionAssignment_assignmentId_idx" ON "RemissionAssignment"("assignmentId");

-- CreateIndex
CREATE UNIQUE INDEX "RemissionAssignment_remissionId_assignmentId_key" ON "RemissionAssignment"("remissionId", "assignmentId");

-- Migrate existing data before dropping the column
INSERT INTO "RemissionAssignment" ("id", "remissionId", "assignmentId", "createdAt")
SELECT 
    gen_random_uuid()::text,
    "id",
    "assignmentId",
    CURRENT_TIMESTAMP
FROM "Remission"
WHERE "assignmentId" IS NOT NULL;

-- AlterTable (now we can safely drop the column)
ALTER TABLE "Remission" DROP COLUMN "assignmentId";

-- CreateIndex
CREATE INDEX "OperationLog_status_startedAt_idx" ON "OperationLog"("status", "startedAt");

-- CreateIndex
CREATE INDEX "OperationLog_operationType_status_idx" ON "OperationLog"("operationType", "status");

-- CreateIndex
CREATE INDEX "OperationLog_userId_idx" ON "OperationLog"("userId");

-- CreateIndex
CREATE INDEX "OperationLog_startedAt_idx" ON "OperationLog"("startedAt");

-- CreateIndex
CREATE INDEX "Assignment_version_idx" ON "Assignment"("version");

-- CreateIndex
CREATE INDEX "Assignment_status_idx" ON "Assignment"("status");

-- AddForeignKey
ALTER TABLE "Remission" ADD CONSTRAINT "Remission_contractorId_fkey" FOREIGN KEY ("contractorId") REFERENCES "Contractor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RemissionAssignment" ADD CONSTRAINT "RemissionAssignment_remissionId_fkey" FOREIGN KEY ("remissionId") REFERENCES "Remission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RemissionAssignment" ADD CONSTRAINT "RemissionAssignment_assignmentId_fkey" FOREIGN KEY ("assignmentId") REFERENCES "Assignment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OperationLog" ADD CONSTRAINT "OperationLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
