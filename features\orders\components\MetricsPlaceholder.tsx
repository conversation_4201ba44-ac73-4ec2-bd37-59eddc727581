"use client";

import React from "react";
import { ChartBarIcon } from "@heroicons/react/24/outline";

import {
    Card,
    CardBody,
    CardHeader,
} from "@/shared/components/ui/hero-ui-client";

export default function MetricsPlaceholder({ orders }: { orders: any[] }) {
    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <div className="flex items-center gap-2">
                        <ChartBarIcon className="w-5 h-5" />
                        <h3 className="text-lg font-semibold">
                            Métricas de Contratistas
                        </h3>
                    </div>
                </CardHeader>
                <CardBody>
                    <div className="text-center py-12 text-gray-500">
                        <ChartBarIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                        <p>Las métricas de contratistas se mostrarán aquí</p>
                        <p className="text-sm mt-2">
                            Esta funcionalidad estará disponible próximamente
                        </p>
                    </div>
                </CardBody>
            </Card>
        </div>
    );
}
