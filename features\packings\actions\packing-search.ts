"use server";

import { Prisma } from "@prisma/client";
import { startOfDay, endOfDay, subDays } from "date-fns";

import { db } from "@/lib/db";

export interface PackingSearchParams {
    search?: string;
    statusId?: string;
    customerId?: string;
    transporterId?: string;
    dateFrom?: Date;
    dateTo?: Date;
    qualityCheckPassed?: boolean;
    hasTransporter?: boolean;
    orderBy?: "createdAt" | "deliveryDate" | "folio";
    orderDirection?: "asc" | "desc";
    page?: number;
    limit?: number;
}

export async function searchPackings(params: PackingSearchParams) {
    try {
        const {
            search,
            statusId,
            customerId,
            transporterId,
            dateFrom,
            dateTo,
            qualityCheckPassed,
            hasTransporter,
            orderBy = "createdAt",
            orderDirection = "desc",
            page = 1,
            limit = 20,
        } = params;

        // Construir filtros
        const where: Prisma.PackingWhereInput = {};

        // Búsqueda de texto
        if (search) {
            where.OR = [
                { folio: { contains: search, mode: "insensitive" } },
                { code: { contains: search, mode: "insensitive" } },
                { trackingNumber: { contains: search, mode: "insensitive" } },
                { notes: { contains: search, mode: "insensitive" } },
                { driverName: { contains: search, mode: "insensitive" } },
                {
                    customer: {
                        name: { contains: search, mode: "insensitive" },
                    },
                },
                {
                    subCustomer: {
                        name: { contains: search, mode: "insensitive" },
                    },
                },
            ];
        }

        // Filtros específicos
        if (statusId) where.statusId = statusId;
        if (customerId) where.customerId = customerId;
        if (transporterId) where.transporterId = transporterId;
        if (qualityCheckPassed !== undefined)
            where.qualityCheckPassed = qualityCheckPassed;
        if (hasTransporter !== undefined) {
            where.transporterId = hasTransporter ? { not: null } : null;
        }

        // Filtros de fecha
        if (dateFrom || dateTo) {
            where.deliveryDate = {};
            if (dateFrom) where.deliveryDate.gte = startOfDay(dateFrom);
            if (dateTo) where.deliveryDate.lte = endOfDay(dateTo);
        }

        // Contar total de registros
        const totalCount = await db.packing.count({ where });

        // Obtener packings con paginación
        const packings = await db.packing.findMany({
            where,
            include: {
                customer: true,
                subCustomer: true,
                order: {
                    select: {
                        id: true,
                        cutOrder: true,
                        transferNumber: true,
                    },
                },
                status: true,
                transporter: true,
                packedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                verifiedBy: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                details: {
                    include: {
                        garmentSize: {
                            include: {
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                                size: true,
                            },
                        },
                    },
                },
                _count: {
                    select: {
                        details: true,
                        history: true,
                        qualityChecks: true,
                    },
                },
            },
            orderBy: {
                [orderBy]: orderDirection,
            },
            skip: (page - 1) * limit,
            take: limit,
        });

        // Calcular métricas
        const metrics = await getPackingMetrics(where);

        return {
            success: true,
            data: {
                packings,
                pagination: {
                    page,
                    limit,
                    totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
                metrics,
            },
        };
    } catch (error) {
        console.error("Error searching packings:", error);

        return {
            success: false,
            error: "Error al buscar packings",
            data: {
                packings: [],
                pagination: {
                    page: 1,
                    limit: 20,
                    totalCount: 0,
                    totalPages: 0,
                },
            },
        };
    }
}

export async function getPackingMetrics(where?: Prisma.PackingWhereInput) {
    try {
        const today = new Date();
        const last7Days = subDays(today, 7);
        const last30Days = subDays(today, 30);

        const [
            total,
            pending,
            inProcess,
            readyForDelivery,
            delivered,
            cancelled,
            qualityPassed,
            overdue,
            deliveredLast7Days,
            deliveredLast30Days,
        ] = await Promise.all([
            db.packing.count({ where }),
            db.packing.count({
                where: { ...where, status: { name: "Pendiente" } },
            }),
            db.packing.count({
                where: { ...where, status: { name: "En Proceso" } },
            }),
            db.packing.count({
                where: { ...where, status: { name: "Listo para Entrega" } },
            }),
            db.packing.count({
                where: { ...where, status: { name: "Entregado" } },
            }),
            db.packing.count({
                where: { ...where, status: { name: "Cancelado" } },
            }),
            db.packing.count({ where: { ...where, qualityCheckPassed: true } }),
            db.packing.count({
                where: {
                    ...where,
                    deliveryDate: { lt: today },
                    status: { name: { notIn: ["Entregado", "Cancelado"] } },
                },
            }),
            db.packing.count({
                where: {
                    ...where,
                    status: { name: "Entregado" },
                    updatedAt: { gte: last7Days },
                },
            }),
            db.packing.count({
                where: {
                    ...where,
                    status: { name: "Entregado" },
                    updatedAt: { gte: last30Days },
                },
            }),
        ]);

        // Calcular totales de piezas
        const pieceMetrics = await db.packingDetail.aggregate({
            where: { packing: where },
            _sum: {
                quantity: true,
                defects: true,
            },
            _avg: {
                quantity: true,
            },
        });

        return {
            statusMetrics: {
                total,
                pending,
                inProcess,
                readyForDelivery,
                delivered,
                cancelled,
                overdue,
            },
            qualityMetrics: {
                passed: qualityPassed,
                passRate: total > 0 ? (qualityPassed / total) * 100 : 0,
            },
            deliveryMetrics: {
                last7Days: deliveredLast7Days,
                last30Days: deliveredLast30Days,
                deliveryRate: total > 0 ? (delivered / total) * 100 : 0,
                overdueRate: total > 0 ? (overdue / total) * 100 : 0,
            },
            pieceMetrics: {
                totalPieces: pieceMetrics._sum.quantity || 0,
                totalDefects: pieceMetrics._sum.defects || 0,
                avgPiecesPerPacking: pieceMetrics._avg.quantity || 0,
            },
        };
    } catch (error) {
        console.error("Error calculating packing metrics:", error);

        return null;
    }
}

export async function exportPackingReport(params: PackingSearchParams) {
    try {
        // Obtener todos los packings sin límite de paginación
        const result = await searchPackings({
            ...params,
            page: 1,
            limit: 999999,
        });

        if (!result.success || !result.data) {
            return {
                success: false,
                error: "Error al obtener datos para exportar",
            };
        }

        // Formatear datos para exportación
        const exportData = result.data.packings.map((packing) => ({
            Folio: packing.folio,
            Código: packing.code,
            "Número de Rastreo": packing.trackingNumber || "N/A",
            Cliente: packing.customer.name,
            Subcliente: packing.subCustomer?.name || "N/A",
            "Fecha de Entrega": packing.deliveryDate,
            Estado: packing.status.name,
            "Control de Calidad": packing.qualityCheckPassed
                ? "Aprobado"
                : "Pendiente",
            Transportista: packing.transporter?.name || "No asignado",
            Conductor: packing.driverName || "N/A",
            "Teléfono Conductor": packing.driverPhone || "N/A",
            "Total Piezas": packing.details.reduce(
                (sum, d) => sum + d.quantity,
                0,
            ),
            "Total Líneas": packing.details.length,
            "Peso Total": packing.totalWeight || "N/A",
            "Volumen Total": packing.totalVolume || "N/A",
            "Número de Paquetes": packing.packagesCount,
            "Empacado Por": packing.packedBy?.name || "N/A",
            "Verificado Por": packing.verifiedBy?.name || "N/A",
            Notas: packing.notes || "N/A",
        }));

        return {
            success: true,
            data: exportData,
            metrics: result.data.metrics,
        };
    } catch (error) {
        console.error("Error exporting packing report:", error);

        return { success: false, error: "Error al exportar reporte" };
    }
}
