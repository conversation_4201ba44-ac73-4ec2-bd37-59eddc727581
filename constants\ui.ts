/**
 * Constantes relacionadas con la interfaz de usuario
 * Incluye configuraciones de apariencia, temas, y otros ajustes visuales
 */

// Tamaños de pantalla para diseño responsive
export const SCREEN_SIZES = {
    xs: "480px",
    sm: "640px",
    md: "768px",
    lg: "1024px",
    xl: "1280px",
    "2xl": "1536px",
};

// Valores para espaciado consistente en la UI
export const SPACING = {
    xs: "0.25rem",
    sm: "0.5rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
    "2xl": "3rem",
};

// Colores principales de la aplicación (para referencia rápida)
export const COLORS = {
    primary: {
        light: "#60a5fa",
        DEFAULT: "#3b82f6",
        dark: "#2563eb",
    },
    secondary: {
        light: "#a5b4fc",
        DEFAULT: "#818cf8",
        dark: "#6366f1",
    },
    success: {
        light: "#4ade80",
        DEFAULT: "#22c55e",
        dark: "#16a34a",
    },
    warning: {
        light: "#fcd34d",
        DEFAULT: "#f59e0b",
        dark: "#d97706",
    },
    error: {
        light: "#f87171",
        DEFAULT: "#ef4444",
        dark: "#dc2626",
    },
    gray: {
        100: "#f3f4f6",
        200: "#e5e7eb",
        300: "#d1d5db",
        400: "#9ca3af",
        500: "#6b7280",
        600: "#4b5563",
        700: "#374151",
        800: "#1f2937",
        900: "#111827",
    },
};

// Transiciones consistentes para animaciones
export const TRANSITIONS = {
    fast: "150ms",
    DEFAULT: "300ms",
    slow: "500ms",
};
