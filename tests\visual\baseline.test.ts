import { test, expect } from '@playwright/test';

test.describe('Visual Baseline', () => {
  test('dashboard layout', async ({ page }) => {
    await page.goto('/dashboard');
    await expect(page).toHaveScreenshot('dashboard-baseline.png');
  });
  
  test('order modal', async ({ page }) => {
    await page.goto('/dashboard/orders');
    // Esperar a que la tabla cargue
    await page.waitForSelector('table');
    // Hacer clic en la primera fila
    await page.click('table tbody tr:first-child');
    await page.waitForSelector('[role="dialog"]');
    await expect(page).toHaveScreenshot('order-modal-baseline.png');
  });

  test('sizes management page', async ({ page }) => {
    await page.goto('/dashboard/sizes');
    await page.waitForSelector('table');
    await expect(page).toHaveScreenshot('sizes-management-baseline.png');
  });

  test('create order form', async ({ page }) => {
    await page.goto('/dashboard/orders/new');
    await expect(page).toHaveScreenshot('create-order-baseline.png');
  });

  test('dark mode dashboard', async ({ page }) => {
    await page.goto('/dashboard');
    // Activar dark mode
    await page.click('[data-testid="theme-toggle"]');
    await expect(page).toHaveScreenshot('dashboard-dark-baseline.png');
  });
});
