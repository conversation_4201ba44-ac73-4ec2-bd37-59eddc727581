import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "Autenticación - LOHARI",
    description: "Inicia sesión o regístrate en LOHARI",
};

export default function AuthLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-neutral-100 to-primary-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-950 flex items-center justify-center p-4">
            {/* Patrón de fondo opcional */}
            <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5 dark:opacity-[0.02]" />

            {/* Contenido */}
            <div className="relative z-10 w-full">{children}</div>
        </div>
    );
}
