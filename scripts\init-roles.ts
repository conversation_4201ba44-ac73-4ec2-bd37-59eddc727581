import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function initRoles() {
    try {
        console.log('Iniciando creación de roles...');

        // Verificar si ya existen roles
        const existingRolesCount = await prisma.role.count();
        console.log(`Roles existentes: ${existingRolesCount}`);

        if (existingRolesCount === 0) {
            // Crear todos los roles en una sola transacción
            const roles = await prisma.$transaction([
                prisma.role.create({
                    data: {
                        name: 'ADMIN',
                        iconName: 'ShieldCheckIcon',
                        color: '#EF4444'
                    }
                }),
                prisma.role.create({
                    data: {
                        name: 'EMPLOYEE',
                        iconName: 'UserIcon',
                        color: '#3B82F6'
                    }
                }),
                prisma.role.create({
                    data: {
                        name: 'CONTRACTOR',
                        iconName: 'BriefcaseIcon',
                        color: '#10B981'
                    }
                }),
                prisma.role.create({
                    data: {
                        name: 'GUEST',
                        iconName: 'UserCircleIcon',
                        color: '#9CA3AF'
                    }
                })
            ]);

            console.log('✅ Roles creados exitosamente:', roles.map(r => r.name).join(', '));
        } else {
            console.log('ℹ️ Ya existen roles en la base de datos');
            
            const allRoles = await prisma.role.findMany({
                select: { name: true }
            });
            
            console.log('Roles actuales:', allRoles.map(r => r.name).join(', '));
        }
    } catch (error) {
        console.error('Error al crear roles:', error);
    } finally {
        await prisma.$disconnect();
    }
}

initRoles();
