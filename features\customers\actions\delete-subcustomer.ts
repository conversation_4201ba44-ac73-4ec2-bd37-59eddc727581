"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";

const deleteSubcustomerSchema = z.object({
    id: z.string().min(1, "ID es requerido"),
});

export async function deleteSubcustomer(
    data: z.infer<typeof deleteSubcustomerSchema>,
): Promise<
    { success: true; data: boolean } | { success: false; error: string }
> {
    try {
        const validated = deleteSubcustomerSchema.parse(data);

        // Verificar que el subcliente existe y es realmente un subcliente
        const subcustomer = await db.customer.findUnique({
            where: { id: validated.id },
            include: {
                parent: true,
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
        });

        if (!subcustomer) {
            return { success: false, error: "Subcliente no encontrado" };
        }

        if (!subcustomer.parentId) {
            return {
                success: false,
                error: "No se puede eliminar un cliente principal desde aquí",
            };
        }

        // Verificar dependencias antes de eliminar
        if (subcustomer._count.orders > 0) {
            return {
                success: false,
                error: `No se puede eliminar: tiene ${subcustomer._count.orders} órdenes asociadas`,
            };
        }

        if (subcustomer._count.packings > 0) {
            return {
                success: false,
                error: `No se puede eliminar: tiene ${subcustomer._count.packings} empaques asociados`,
            };
        }

        if (subcustomer._count.subCustomers > 0) {
            return {
                success: false,
                error: `No se puede eliminar: tiene ${subcustomer._count.subCustomers} subclientes asociados`,
            };
        }

        // Eliminar subcliente
        await db.customer.delete({
            where: { id: validated.id },
        });

        // Revalidar paths afectados
        revalidatePath("/dashboard/customers");
        revalidatePath(`/dashboard/customers/${subcustomer.parentId}/details`);

        return { success: true, data: true };
    } catch (error) {
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos inválidos",
            };
        }
        const dbResult = await handleDbError(() => {
            throw error;
        }, "Error al eliminar subcliente");

        if (dbResult.success) {
            return { success: true, data: true };
        } else {
            return {
                success: false,
                error: dbResult.error || "Error al eliminar subcliente",
            };
        }
    }
}

// Función auxiliar para validación previa
export async function validateSubcustomerDeletion(id: string) {
    try {
        const subcustomer = await db.customer.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
        });

        if (!subcustomer) {
            return { success: false, error: "Subcliente no encontrado" };
        }

        const canDelete =
            subcustomer._count.orders === 0 &&
            subcustomer._count.packings === 0 &&
            subcustomer._count.subCustomers === 0;

        const dependencies = {
            orders: subcustomer._count.orders,
            packings: subcustomer._count.packings,
            subCustomers: subcustomer._count.subCustomers,
        };

        return {
            success: true,
            data: {
                canDelete,
                dependencies,
                reason: !canDelete ? "Tiene dependencias asociadas" : null,
            },
        };
    } catch (error) {
        const dbResult = await handleDbError(() => {
            throw error;
        }, "Error al validar eliminación");

        if (dbResult.success) {
            return {
                success: true,
                data: {
                    canDelete: false,
                    dependencies: null,
                    reason: "Error desconocido",
                },
            };
        } else {
            return {
                success: false,
                error: dbResult.error || "Error al validar eliminación",
            };
        }
    }
}
