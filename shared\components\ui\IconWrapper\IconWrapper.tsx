import React from "react";

import { cn } from "@/shared/utils/client";

export interface IconWrapperProps {
    size?: "xs" | "sm" | "md" | "lg" | "xl";
    variant?: "ghost" | "soft" | "solid" | "bordered";
    color?: "primary" | "success" | "warning" | "danger" | "default";
    interactive?: boolean;
    className?: string;
    children: React.ReactNode;
    onClick?: () => void;
    ariaLabel?: string;
}

/**
 * Sistema unificado de iconos para LOHARI
 * Proporciona espaciado y estilos consistentes para todos los iconos
 */
export const IconWrapper = React.forwardRef<HTMLDivElement, IconWrapperProps>(
    (
        {
            size = "md",
            variant = "ghost",
            color = "default",
            interactive = false,
            className,
            children,
            onClick,
            ariaLabel,
            ...props
        },
        ref,
    ) => {
        const isClickable = interactive || !!onClick;

        return (
            <div
                ref={ref}
                aria-label={ariaLabel}
                className={cn(
                    "icon-wrapper",
                    `icon-${size}`,
                    `icon-${variant}`,
                    `icon-${color}`,
                    isClickable && "icon-interactive",
                    className,
                )}
                role={isClickable ? "button" : undefined}
                tabIndex={isClickable ? 0 : undefined}
                onClick={onClick}
                {...props}
            >
                {children}
            </div>
        );
    },
);

IconWrapper.displayName = "IconWrapper";

export default IconWrapper;
