# Sistema de Iconos LOHARI

Sistema unificado de espaciado y estilos para iconos en todo el proyecto.

## Instalación

El sistema de iconos está incluido automáticamente en `globals.css`:

```css
@import '../shared/styles/icon-system.css';
```

## Uso Básico

```tsx
import { IconWrapper } from '@/shared/components/ui/IconWrapper';
import { PlusIcon } from '@heroicons/react/24/outline';

// Uso simple (default: size="md", variant="ghost", color="default")
<IconWrapper>
  <PlusIcon />
</IconWrapper>

// Con opciones personalizadas
<IconWrapper size="lg" variant="soft" color="primary">
  <PlusIcon />
</IconWrapper>
```

## API Reference

### Props

| Prop | Tipo | Default | Descripción |
|------|------|---------|-------------|
| `size` | `'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl'` | `'md'` | Tamaño del icono y su contenedor |
| `variant` | `'ghost' \| 'soft' \| 'solid' \| 'bordered'` | `'ghost'` | Estilo visual del contenedor |
| `color` | `'primary' \| 'success' \| 'warning' \| 'danger' \| 'default'` | `'default'` | Color del icono y fondo |
| `interactive` | `boolean` | `false` | Si el icono es clickeable |
| `onClick` | `() => void` | - | Manejador de click |
| `className` | `string` | - | Clases CSS adicionales |
| `ariaLabel` | `string` | - | Label de accesibilidad |

## Guía de Tamaños

| Tamaño | Icono | Padding | Total | Uso recomendado |
|--------|-------|---------|-------|-----------------|
| `xs` | 16px | 2px | 20px | Badges, chips, texto inline |
| `sm` | 20px | 4px | 28px | Inputs, botones pequeños |
| `md` | 24px | 6px | 36px | **Default** - Botones, cards |
| `lg` | 32px | 8px | 48px | Headers, CTAs principales |
| `xl` | 40px | 10px | 60px | Hero sections, estados vacíos |

## Variantes de Estilo

### Ghost (Default)
Sin background, hover sutil. Ideal para iconos en texto o navegación.

```tsx
<IconWrapper variant="ghost">
  <HomeIcon />
</IconWrapper>
```

### Soft
Background sutil con color. Perfecto para destacar sin ser intrusivo.

```tsx
<IconWrapper variant="soft" color="primary">
  <PlusIcon />
</IconWrapper>
```

### Solid
Background sólido con alto contraste. Para CTAs principales.

```tsx
<IconWrapper variant="solid" color="success">
  <CheckIcon />
</IconWrapper>
```

### Bordered
Solo borde, sin background. Para acciones secundarias.

```tsx
<IconWrapper variant="bordered" color="danger">
  <TrashIcon />
</IconWrapper>
```

## Ejemplos de Uso

### En Botones de HeroUI
```tsx
<Button
  startContent={
    <IconWrapper size="sm" variant="ghost">
      <PlusIcon />
    </IconWrapper>
  }
>
  Nueva Orden
</Button>
```

### En Headers
```tsx
<h2 className="flex items-center gap-2">
  <IconWrapper size="sm" variant="ghost" color="primary">
    <DocumentIcon />
  </IconWrapper>
  Información de la Orden
</h2>
```

### Iconos Interactivos
```tsx
<IconWrapper 
  size="md" 
  variant="soft" 
  color="danger"
  interactive
  onClick={() => handleDelete()}
  ariaLabel="Eliminar elemento"
>
  <TrashIcon />
</IconWrapper>
```

### En Modales
```tsx
<ModalHeader>
  <div className="flex items-center gap-2">
    <IconWrapper size="sm" variant="soft" color="primary">
      <CalendarIcon />
    </IconWrapper>
    <span>Seleccionar Fecha</span>
  </div>
</ModalHeader>
```

## Migración desde Código Legacy

### Antes (Problemático)
```tsx
<div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
  <PlusIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
</div>
```

### Después (Correcto)
```tsx
<IconWrapper size="md" variant="soft" color="primary">
  <PlusIcon />
</IconWrapper>
```

## Beneficios del Sistema

1. **Consistencia**: Todos los iconos tienen el mismo espaciado y comportamiento
2. **Mantenibilidad**: Cambios globales desde un solo archivo CSS
3. **Accesibilidad**: Soporte completo para ARIA y navegación por teclado
4. **Performance**: Sin JavaScript adicional, puro CSS
5. **Dark Mode**: Soporte automático para tema oscuro

## Notas de Implementación

- Los iconos dentro de `IconWrapper` no necesitan clases de tamaño o color
- El sistema detecta automáticamente el tamaño del SVG hijo
- Compatible con todos los iconos de Heroicons y lucide-react
- Los colores se adaptan automáticamente al tema (light/dark)

## Troubleshooting

### El icono no se ve del tamaño correcto
Asegúrate de NO incluir clases de tamaño en el icono hijo:
```tsx
// ❌ Incorrecto
<IconWrapper size="lg">
  <PlusIcon className="w-6 h-6" />
</IconWrapper>

// ✅ Correcto
<IconWrapper size="lg">
  <PlusIcon />
</IconWrapper>
```

### Los colores no funcionan
El color se aplica automáticamente. No agregues clases de color al icono:
```tsx
// ❌ Incorrecto
<IconWrapper color="primary">
  <PlusIcon className="text-blue-600" />
</IconWrapper>

// ✅ Correcto
<IconWrapper color="primary">
  <PlusIcon />
</IconWrapper>
```