# 👥 Customers Feature

## 📋 Descripción
Gestión completa de clientes con soporte avanzado para subclientes, jerarquías y configuraciones personalizadas de empaque.

## 🏗️ Componentes

### CustomerList
Lista de clientes con búsqueda y visualización de jerarquía.

### CustomerForm
Formulario de registro/edición con soporte para displayName y configuraciones.

### CustomerProfile
Perfil detallado del cliente con información de jerarquía.

### CustomerHierarchy
Visualización interactiva de la jerarquía de clientes y subclientes.
```tsx
<CustomerHierarchy 
  customer={customer} 
  onEditHierarchy={() => {}} 
/>
```

### CustomerSelector
Selector inteligente con soporte para jerarquías y subclientes.
```tsx
<CustomerSelector
  value={{ customerId, subCustomerId }}
  onChange={(value) => console.log(value)}
  includeSubCustomers
/>
```

### CustomerSettingsPanel
Panel de configuración de empaque personalizado por cliente.
```tsx
<CustomerSettingsPanel customerId={customerId} />
```

## 🪝 Hooks

### useCustomers
Hook principal para gestión de clientes.
```tsx
const { customers, loading, error } = useCustomers()
```

### useCustomerOrders
Historial de órdenes por cliente.
```tsx
const { orders } = useCustomerOrders(customerId)
```

### useCustomerSettings
Gestión de configuraciones personalizadas de empaque.
```tsx
const { 
  settings, 
  updateSettings, 
  resetSettings,
  isInherited,
  hasCustomSettings 
} = useCustomerSettings({ customerId })
```

## 🎨 Características Principales

### Jerarquía de Clientes
- Soporte completo para clientes principales y subclientes
- Validación de ciclos en jerarquías
- Visualización de múltiples niveles
- DisplayName personalizable para mejor identificación

### Configuraciones de Empaque
- Configuraciones personalizadas por cliente/subcliente
- Herencia de configuraciones del cliente padre
- Capacidades por defecto para cajas y bolsas
- Instrucciones especiales de empaque y envío
- Control de calidad personalizado

### Gestión Avanzada
- Creación de subclientes con herencia automática
- Actualización de jerarquías con validación
- Reseteo de configuraciones a valores heredados
- Aplicación masiva de configuraciones a subclientes

## 📦 Server Actions

### Jerarquía
- `createSubCustomer` - Crear subcliente con herencia opcional
- `updateCustomerHierarchy` - Actualizar relaciones padre-hijo
- `getCustomerHierarchy` - Obtener jerarquía completa
- `removeFromHierarchy` - Convertir subcliente en cliente principal

### Configuraciones
- `updateCustomerSettings` - Actualizar configuraciones de empaque
- `getCustomerSettings` - Obtener configuraciones efectivas
- `resetCustomerSettings` - Resetear a valores heredados

## 🎯 Uso Avanzado

### Crear Subcliente con Configuración
```tsx
import { createSubCustomer } from '@/features/customers'

await createSubCustomer({
  name: "Reebok",
  parentId: "becktel-id",
  displayName: "Becktel - Reebok",
  inheritSettings: true,
  packingSettings: {
    defaultBoxCapacity: 30,
    requiresQualityCheck: true
  }
})
```

### Gestionar Configuraciones
```tsx
import { useCustomerSettings } from '@/features/customers'

function CustomerConfig({ customerId }) {
  const { settings, updateSettings } = useCustomerSettings({ customerId })
  
  const handleUpdate = () => {
    updateSettings({
      defaultBoxCapacity: 25,
      requiresQualityCheck: true,
      packingInstructions: "Manejar con cuidado"
    }, true) // aplicar a subclientes
  }
}
```