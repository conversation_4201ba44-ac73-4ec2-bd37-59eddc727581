"use client";

import React from "react";
import { Tooltip } from "@heroui/react";

import {
    formatDate,
    formatRelative,
    isToday,
    isTomorrow,
    isYesterday,
    isPast,
} from "@/shared/utils/dateUtils";

// Tipos disponibles de visualización de fecha
export type DateDisplayType = "regular" | "estimated" | "received";

interface DateDisplayProps {
    date: string | Date | null | undefined;
    type?: DateDisplayType;
    showRelative?: boolean;
    format?: string;
    className?: string;
    tooltipText?: string;
}

export function DateDisplay({
    date,
    type = "regular",
    showRelative = true,
    format = "dd/MM/yyyy",
    className = "",
    tooltipText,
}: DateDisplayProps) {
    // Determinar el estilo y texto según el tipo y estado de la fecha
    const getStyleAndText = () => {
        // Si no hay fecha válida
        if (!date) {
            return {
                bgClass:
                    "bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700",
                textClass: "text-gray-500 dark:text-gray-400",
                relativeText: "Sin fecha",
            };
        }

        // Formatear para mostrar
        const formattedDate = formatDate(date, format);
        let relativeText = showRelative ? formatRelative(date) : formattedDate;
        let bgClass =
            "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700";
        let textClass = "text-gray-700 dark:text-gray-300";

        // Para fechas de entrega estimada
        if (type === "estimated") {
            if (isPast(date)) {
                bgClass =
                    "bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-800";
                textClass = "text-red-700 dark:text-red-300";
                relativeText = showRelative
                    ? `Atrasada ${relativeText}`
                    : formattedDate;
            } else if (isToday(date)) {
                bgClass =
                    "bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-800";
                textClass = "text-amber-700 dark:text-amber-300";
                relativeText = "Para hoy";
            } else if (isTomorrow(date)) {
                bgClass =
                    "bg-orange-50 dark:bg-orange-900/30 border-orange-200 dark:border-orange-800";
                textClass = "text-orange-700 dark:text-orange-300";
                relativeText = "Para mañana";
            } else {
                bgClass =
                    "bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800";
                textClass = "text-blue-700 dark:text-blue-300";
            }
        }

        // Para fechas de recepción
        if (type === "received") {
            if (isToday(date)) {
                bgClass =
                    "bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-800";
                textClass = "text-green-700 dark:text-green-300";
            } else if (isYesterday(date)) {
                bgClass =
                    "bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800";
                textClass = "text-blue-700 dark:text-blue-300";
            } else if (isPast(date)) {
                bgClass =
                    "bg-violet-50 dark:bg-violet-900/30 border-violet-200 dark:border-violet-800";
                textClass = "text-violet-700 dark:text-violet-300";
            }
        }

        return { bgClass, textClass, relativeText, formattedDate };
    };

    const { bgClass, textClass, relativeText, formattedDate } =
        getStyleAndText();

    // Texto para el tooltip
    const tooltipContent = tooltipText || formattedDate;

    return (
        <Tooltip content={tooltipContent}>
            <span
                className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium border ${bgClass} ${textClass} ${className}`}
                data-testid="date-display"
            >
                {relativeText}
            </span>
        </Tooltip>
    );
}
