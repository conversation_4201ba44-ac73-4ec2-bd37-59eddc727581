"use client";

import { motion } from "framer-motion";
import { But<PERSON>, Tooltip } from "@heroui/react";
import { CheckIcon } from "@heroicons/react/24/solid";

import {
    useProgressTracker,
    ProgressStep,
} from "../../../hooks/useProgressTracker";

export type WizardStep = "contractor" | "orders" | "quantities" | "summary";

interface StepIndicatorProps {
    steps?: ProgressStep[];
    currentStep?: number;
    onStepClick?: (stepId: number) => void;
    allowNavigation?: boolean;
}

export const StepIndicator = ({
    steps,
    currentStep,
    onStepClick,
    allowNavigation = false,
}: StepIndicatorProps) => {
    // Usar el hook para manejar de manera segura el progreso
    const {
        steps: safeSteps,
        currentStep: safeCurrentStep,
        progressWidth,
        isStepActive,
        isStepCompleted,
        canStepBeClicked,
    } = useProgressTracker(currentStep, steps, defaultSteps);

    return (
        <div className="w-full mb-8 px-4 py-2">
            <div className="relative">
                {/* Pasos */}
                <div className="relative flex justify-between">
                    {safeSteps.map((step) => {
                        const stepIsActive = isStepActive(step.id);
                        const stepIsCompleted = isStepCompleted(step.id);
                        const isClickable = canStepBeClicked(
                            step.id,
                            allowNavigation,
                        );

                        return (
                            <div
                                key={step.id}
                                className="flex flex-col items-center relative"
                            >
                                <Tooltip
                                    content={step.description || step.label}
                                >
                                    <Button
                                        isIconOnly
                                        aria-current={
                                            stepIsActive ? "step" : undefined
                                        }
                                        aria-label={`Paso ${step.id}: ${step.label}`}
                                        className={`w-10 h-10 rounded-full mb-2 z-10 border-2 focus:ring-2 focus:ring-offset-2 ${
                                            stepIsActive
                                                ? "bg-blue-500 border-blue-500 text-white"
                                                : stepIsCompleted
                                                  ? "bg-green-500 border-green-500 text-white"
                                                  : "bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400"
                                        }`}
                                        isDisabled={
                                            !isClickable && !stepIsActive
                                        }
                                        size="sm"
                                        onClick={() =>
                                            isClickable &&
                                            onStepClick &&
                                            onStepClick(step.id)
                                        }
                                    >
                                        {stepIsCompleted ? (
                                            <CheckIcon className="w-5 h-5 text-white" />
                                        ) : (
                                            <span className="font-medium">
                                                {step.id}
                                            </span>
                                        )}
                                    </Button>
                                </Tooltip>

                                <motion.span
                                    animate={{
                                        opacity: stepIsActive ? 1 : 0.8,
                                        y: stepIsActive ? 0 : 3,
                                    }}
                                    className={`text-xs font-medium transition-colors sm:text-sm ${
                                        stepIsActive
                                            ? "text-blue-600 dark:text-blue-400"
                                            : stepIsCompleted
                                              ? "text-green-600 dark:text-green-400"
                                              : "text-gray-500 dark:text-gray-400"
                                    }`}
                                    initial={{ opacity: 0.6, y: 5 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    {step.label}
                                </motion.span>
                            </div>
                        );
                    })}
                </div>

                {/* Barra de progreso única */}
                <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden mt-6">
                    <motion.div
                        animate={{
                            width: progressWidth,
                        }}
                        className="h-full bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg"
                        initial={{ width: 0 }}
                        transition={{
                            duration: 0.5,
                            ease: "easeInOut",
                        }}
                    />
                </div>
            </div>
        </div>
    );
};

// Pasos predeterminados del wizard con IDs numéricos
const defaultSteps = [
    { id: 1, title: "Contratista", description: "Seleccionar contratista" },
    { id: 2, title: "Órdenes", description: "Seleccionar órdenes" },
    { id: 3, title: "Cantidades", description: "Asignar cantidades" },
    { id: 4, title: "Resumen", description: "Resumen y confirmación" },
];
