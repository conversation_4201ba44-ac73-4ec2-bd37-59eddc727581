"use server";

// Importar funciones originales de validation.ts
import type { OptimisticValidationResponse } from "./types";

import {
    validateOptimistic as _validateOptimistic,
    validateAndReconcile as _validateAndReconcile,
    getQuantitySuggestions as _getQuantitySuggestions,
} from "./validation";

// Re-exportar funciones asíncronas explícitamente
export async function validateOptimistic(
    garmentSizeId: string,
    quantity: number,
): Promise<OptimisticValidationResponse> {
    return _validateOptimistic(garmentSizeId, quantity);
}

export async function validateAndReconcile(
    garmentSizeId: string,
    quantity: number,
    versionToken?: string,
): Promise<OptimisticValidationResponse> {
    return _validateAndReconcile(garmentSizeId, quantity, versionToken);
}

export async function getQuantitySuggestions(
    garmentSizeId: string,
    contractorId: string,
): Promise<{
    suggestedQuantity: number;
    maxQuantity: number;
    recentAssignments: number[];
}> {
    return _getQuantitySuggestions(garmentSizeId, contractorId);
}
