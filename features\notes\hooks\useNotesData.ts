"use client";

// Using any types for quick build fix
type Note = any;
type NoteStatus = any;
type NoteImportance = any;
import type { User } from "@prisma/client";

import useSWR from "swr";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";

import {
    listNotes,
    getStatuses,
    getImportances,
    getUsers,
} from "@/features/notes/actions";

interface UseNotesDataReturn {
    notes: Note[];
    statuses: NoteStatus[];
    importances: NoteImportance[];
    users: User[];
    isLoading: boolean;
    isError: boolean;
    error: any;
    mutate: any;
    stats: {
        total: number;
        critical: number;
        unresolved: number;
        recentActivity: number;
        myNotes: number;
    };
}

export function useNotesData(currentUserId?: string): UseNotesDataReturn {
    const searchParams = useSearchParams();

    // Parse search params
    const params = {
        search: searchParams.get("search") || "",
        statusId: searchParams.get("statusId") || undefined,
        importanceId: searchParams.get("importanceId") || undefined,
        authorId: searchParams.get("authorId") || undefined,
        orderId: searchParams.get("orderId") || undefined,
        orderBy: searchParams.get("orderBy") || "createdAt",
        order: (searchParams.get("order") || "desc") as "asc" | "desc",
        page: parseInt(searchParams.get("page") || "1"),
        perPage: parseInt(searchParams.get("perPage") || "12"),
    };

    // Fetch notes
    const {
        data: notesData,
        error: notesError,
        mutate,
    } = useSWR(["notes", params], () => listNotes(params), {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
    });

    // Fetch metadata in parallel
    const { data: statusesData } = useSWR("note-statuses", getStatuses);
    const { data: importancesData } = useSWR(
        "note-importances",
        getImportances,
    );
    const { data: usersData } = useSWR("note-users", getUsers);

    // Process data
    const notes =
        notesData?.success && notesData.data ? notesData.data.notes : [];
    const statuses = statusesData?.success ? statusesData.data : [];
    const importances = importancesData?.success ? importancesData.data : [];
    const users = usersData?.success ? usersData.data : [];

    // Calculate stats
    const stats = useMemo(() => {
        const now = new Date();
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        const critical = notes.filter(
            (n: any) => n.importance?.name === "Crítica",
        ).length;
        const unresolved = notes.filter(
            (n: any) =>
                n.status?.name === "Pendiente" ||
                n.status?.name === "En Proceso",
        ).length;
        const recentActivity = notes.filter(
            (n: any) =>
                new Date(n.createdAt) > sevenDaysAgo ||
                (n.updatedAt && new Date(n.updatedAt) > sevenDaysAgo),
        ).length;
        const myNotes = currentUserId
            ? notes.filter((n: any) => n.authorId === currentUserId).length
            : 0;

        return {
            total: notes.length,
            critical,
            unresolved,
            recentActivity,
            myNotes,
        };
    }, [notes, currentUserId]);

    return {
        notes,
        statuses,
        importances,
        users: users as any,
        isLoading: !notesData && !notesError,
        isError: !!notesError || !notesData?.success,
        error: notesError || notesData?.error,
        mutate,
        stats,
    };
}
