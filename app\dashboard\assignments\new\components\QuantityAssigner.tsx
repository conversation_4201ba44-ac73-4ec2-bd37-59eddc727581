"use client";

import type {
    Order,
    Garment,
    GarmentSize,
    GarmentModel,
    Color,
    Size,
} from "@/features/assignments/types/types";
import type { OrderPart } from "@/shared/types/common";

import React, { useState, useMemo, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Chip,
    Button,
    Card,
    CardBody,
    Badge,
    Tooltip,
    Progress,
} from "@heroui/react";
import {
    CubeIcon,
    CalculatorIcon,
    SparklesIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    ArrowPathIcon,
    PlusIcon,
    MinusIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface QuantityItem {
    id: string;
    orderId: string;
    orderCode?: string;
    model: GarmentModel;
    color: Color;
    size: Size;
    totalQuantity: number;
    usedQuantity: number;
    availableQuantity: number;
    assignedQuantity: number;
}

interface GroupedData {
    id: string;
    title: string;
    subtitle?: string;
    cutOrder?: string;
    parts?: OrderPart[];
    items: QuantityItem[];
    colors?: string;
}

interface OrderWithExtras extends Order {
    code?: string;
    parts?: OrderPart[];
}

interface QuantityAssignerProps {
    orders: OrderWithExtras[];
    quantities: Record<string, Record<string, number>>;
    onChange: (
        orderId: string,
        garmentSizeId: string,
        quantity: number,
    ) => void;
    error?: string;
}

// Componente personalizado para input de cantidad
function QuantityInput({
    value,
    onChange,
    max,
    orderId,
    garmentSizeId,
}: {
    value: number;
    onChange: (orderId: string, garmentSizeId: string, value: number) => void;
    max: number;
    orderId: string;
    garmentSizeId: string;
}) {
    const [localValue, setLocalValue] = useState(value.toString());
    const [isFocused, setIsFocused] = useState(false);

    useEffect(() => {
        if (!isFocused) {
            setLocalValue(value.toString());
        }
    }, [value, isFocused]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;

        setLocalValue(newValue);

        const numValue = parseInt(newValue) || 0;

        if (numValue >= 0 && numValue <= max) {
            onChange(orderId, garmentSizeId, numValue);
        }
    };

    const handleBlur = () => {
        setIsFocused(false);
        const numValue = parseInt(localValue) || 0;
        const finalValue = Math.min(Math.max(0, numValue), max);

        setLocalValue(finalValue.toString());
        onChange(orderId, garmentSizeId, finalValue);
    };

    const increment = () => {
        const current = parseInt(localValue) || 0;

        if (current < max) {
            const newValue = current + 1;

            setLocalValue(newValue.toString());
            onChange(orderId, garmentSizeId, newValue);
        }
    };

    const decrement = () => {
        const current = parseInt(localValue) || 0;

        if (current > 0) {
            const newValue = current - 1;

            setLocalValue(newValue.toString());
            onChange(orderId, garmentSizeId, newValue);
        }
    };

    const setMax = () => {
        setLocalValue(max.toString());
        onChange(orderId, garmentSizeId, max);
    };

    const percentage = max > 0 ? (value / max) * 100 : 0;
    const isMaxed = value === max && max > 0;

    return (
        <div className="flex items-center gap-1">
            <Button
                isIconOnly
                className="w-6 h-6 min-w-6"
                isDisabled={value === 0}
                size="sm"
                variant="light"
                onPress={decrement}
            >
                <MinusIcon className="w-3 h-3" />
            </Button>

            <div className="relative">
                <Input
                    classNames={{
                        input: "text-center",
                        inputWrapper: `w-20 ${
                            isMaxed
                                ? "bg-success-50 border-success"
                                : value > 0
                                  ? "bg-warning-50 border-warning"
                                  : ""
                        }`,
                    }}
                    endContent={
                        isMaxed && (
                            <CheckCircleIcon className="w-3 h-3 text-success" />
                        )
                    }
                    max={max}
                    min={0}
                    size="sm"
                    type="number"
                    value={localValue}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    onFocus={() => setIsFocused(true)}
                    onKeyDown={(e) => {
                        if (e.key === "ArrowUp") {
                            e.preventDefault();
                            increment();
                        } else if (e.key === "ArrowDown") {
                            e.preventDefault();
                            decrement();
                        } else if (e.key === "Enter") {
                            e.currentTarget.blur();
                        }
                    }}
                />
                {/* Barra de progreso sutil */}
                {value > 0 && (
                    <div
                        className="absolute bottom-0 left-0 h-0.5 bg-primary rounded-b-small transition-all"
                        style={{ width: `${percentage}%` }}
                    />
                )}
            </div>

            <Button
                isIconOnly
                className="w-6 h-6 min-w-6"
                isDisabled={value === max}
                size="sm"
                variant="light"
                onPress={increment}
            >
                <PlusIcon className="w-3 h-3" />
            </Button>

            <Tooltip content="Asignar máximo" placement="top">
                <Button
                    className="px-2 h-6 text-xs"
                    isDisabled={value === max}
                    size="sm"
                    variant="flat"
                    onPress={setMax}
                >
                    Max
                </Button>
            </Tooltip>
        </div>
    );
}

export function QuantityAssigner({
    orders,
    quantities,
    onChange,
    error,
}: QuantityAssignerProps) {
    // Agrupar datos por orden
    const groupedData = useMemo<GroupedData[]>(() => {
        return orders.map((order) => {
            // Obtener modelos únicos de la orden
            const models = [
                ...new Set(
                    order.garments
                        ?.map((g: Garment) => g.model?.code)
                        .filter(Boolean) || [],
                ),
            ].join(", ");

            // Obtener colores únicos de la orden
            const colors = [
                ...new Set(
                    order.garments
                        ?.map((g: Garment) => g.color?.name)
                        .filter(Boolean) || [],
                ),
            ].join(", ");

            return {
                id: order.id,
                title: order.cutOrder
                    ? `OT: ${order.cutOrder}`
                    : `Orden #${order.code}`,
                subtitle: models,
                cutOrder: order.cutOrder || undefined,
                parts: order.parts || undefined,
                colors: colors,
                items:
                    order.garments
                        ?.flatMap(
                            (garment: Garment) =>
                                garment.sizes?.map((size: GarmentSize) => ({
                                    id: size.id,
                                    orderId: order.id,
                                    model: garment.model!,
                                    color: garment.color!,
                                    size: size.size,
                                    totalQuantity: size.totalQuantity,
                                    usedQuantity: size.usedQuantity,
                                    availableQuantity:
                                        size.totalQuantity - size.usedQuantity,
                                    assignedQuantity:
                                        quantities[order.id]?.[size.id] || 0,
                                })) || [],
                        )
                        .filter(Boolean) || [],
            };
        });
    }, [orders, quantities]);

    // Calcular estadísticas totales
    const totalStats = useMemo(() => {
        let totalAvailable = 0;
        let totalAssigned = 0;

        groupedData.forEach((group) => {
            group.items.forEach((item) => {
                totalAvailable += item.availableQuantity;
                totalAssigned += item.assignedQuantity;
            });
        });

        return { totalAvailable, totalAssigned };
    }, [groupedData]);

    // Función para distribuir cantidades
    const distributeQuantities = (mode: "equal" | "proportional") => {
        const newQuantities = { ...quantities };

        if (mode === "equal") {
            // Distribuir equitativamente
            const totalItems = groupedData.reduce(
                (sum, group) => sum + group.items.length,
                0,
            );
            const quantityPerItem = Math.floor(
                totalStats.totalAvailable / totalItems,
            );

            groupedData.forEach((group) => {
                group.items.forEach((item) => {
                    const assignedQty = Math.min(
                        quantityPerItem,
                        item.availableQuantity,
                    );

                    if (!newQuantities[item.orderId]) {
                        newQuantities[item.orderId] = {};
                    }
                    newQuantities[item.orderId][item.id] = assignedQty;
                    onChange(item.orderId, item.id, assignedQty);
                });
            });
        } else {
            // Distribuir proporcionalmente
            groupedData.forEach((group) => {
                const groupTotal = group.items.reduce(
                    (sum: number, item) => sum + item.availableQuantity,
                    0,
                );

                group.items.forEach((item) => {
                    const proportion = item.availableQuantity / groupTotal;
                    const assignedQty = Math.floor(
                        totalStats.totalAvailable * proportion,
                    );
                    const finalQty = Math.min(
                        assignedQty,
                        item.availableQuantity,
                    );

                    if (!newQuantities[item.orderId]) {
                        newQuantities[item.orderId] = {};
                    }
                    newQuantities[item.orderId][item.id] = finalQty;
                    onChange(item.orderId, item.id, finalQty);
                });
            });
        }
    };

    // Función para limpiar cantidades
    const clearAllQuantities = () => {
        groupedData.forEach((group) => {
            group.items.forEach((item) => {
                onChange(item.orderId, item.id, 0);
            });
        });
    };

    return (
        <div className="space-y-3">
            {/* Barra de herramientas */}
            <div className="flex flex-wrap gap-2 justify-end">
                <Tooltip content="Distribuir cantidades equitativamente">
                    <Button
                        color="primary"
                        size="sm"
                        startContent={<CalculatorIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={() => distributeQuantities("equal")}
                    >
                        Distribuir igual
                    </Button>
                </Tooltip>

                <Tooltip content="Distribuir proporcionalmente según disponibilidad">
                    <Button
                        color="secondary"
                        size="sm"
                        startContent={<SparklesIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={() => distributeQuantities("proportional")}
                    >
                        Distribuir proporcional
                    </Button>
                </Tooltip>

                <Button
                    color="danger"
                    size="sm"
                    startContent={<ArrowPathIcon className="w-4 h-4" />}
                    variant="light"
                    onPress={clearAllQuantities}
                >
                    Limpiar
                </Button>
            </div>

            {/* Estadísticas */}
            <div className="flex items-center justify-between p-3 bg-default-100 dark:bg-default-50 rounded-medium">
                <div className="flex items-center gap-3 text-sm">
                    <span className="text-default-600">Disponible:</span>
                    <span className="font-medium">
                        {totalStats.totalAvailable}
                    </span>
                </div>
                <Progress
                    aria-label="Progreso de asignación"
                    className="max-w-[150px]"
                    color="primary"
                    size="sm"
                    value={
                        (totalStats.totalAssigned / totalStats.totalAvailable) *
                        100
                    }
                />
                <div className="flex items-center gap-3 text-sm">
                    <span className="text-default-600">Asignado:</span>
                    <span className="font-medium text-primary">
                        {totalStats.totalAssigned}
                    </span>
                </div>
            </div>

            {/* Error */}
            {error && (
                <div className="p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg flex items-center gap-2 text-danger">
                    <ExclamationTriangleIcon className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                </div>
            )}

            {/* Tablas de asignación */}
            <div className="space-y-4">
                {groupedData.map((group, groupIndex) => (
                    <motion.div
                        key={group.id}
                        animate={{ opacity: 1, y: 0 }}
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ delay: groupIndex * 0.05 }}
                    >
                        <Card>
                            <CardBody className="p-3">
                                <div className="space-y-2">
                                    {/* Encabezado simplificado */}
                                    <div className="flex items-start justify-between gap-2">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 flex-wrap">
                                                <h4 className="font-semibold">
                                                    {group.title}
                                                </h4>
                                                {/* Partidas inline */}
                                                {group.parts &&
                                                    group.parts.length > 0 && (
                                                        <>
                                                            <span className="text-default-400">
                                                                •
                                                            </span>
                                                            {group.parts.map(
                                                                (part) => (
                                                                    <Chip
                                                                        key={
                                                                            part.id
                                                                        }
                                                                        color="secondary"
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        {
                                                                            part.code
                                                                        }
                                                                    </Chip>
                                                                ),
                                                            )}
                                                        </>
                                                    )}
                                            </div>
                                            {/* Modelos */}
                                            <p className="text-sm text-default-500">
                                                Modelos: {group.subtitle}
                                            </p>
                                            {/* Colores */}
                                            {group.colors && (
                                                <p className="text-sm text-default-500">
                                                    Color
                                                    {group.colors.includes(",")
                                                        ? "es"
                                                        : ""}
                                                    : {group.colors}
                                                </p>
                                            )}
                                        </div>
                                        <Badge
                                            color="primary"
                                            content={
                                                group.items.filter(
                                                    (item) =>
                                                        item.assignedQuantity >
                                                        0,
                                                ).length
                                            }
                                            isInvisible={
                                                group.items.filter(
                                                    (item) =>
                                                        item.assignedQuantity >
                                                        0,
                                                ).length === 0
                                            }
                                            size="sm"
                                        >
                                            <CubeIcon className="w-5 h-5 text-default-400" />
                                        </Badge>
                                    </div>

                                    {/* Tabla simplificada */}
                                    <Table
                                        isCompact
                                        removeWrapper
                                        aria-label="Tabla de asignación de cantidades"
                                        classNames={{
                                            th: "bg-default-100 dark:bg-default-50 text-xs uppercase",
                                            td: "py-2",
                                        }}
                                    >
                                        <TableHeader>
                                            <TableColumn>TALLA</TableColumn>
                                            <TableColumn
                                                align="center"
                                                className="w-48"
                                            >
                                                ASIGNAR
                                            </TableColumn>
                                            <TableColumn align="center">
                                                TOTAL
                                            </TableColumn>
                                            <TableColumn align="center">
                                                DISPONIBLE
                                            </TableColumn>
                                        </TableHeader>
                                        <TableBody>
                                            {group.items.map((item) => (
                                                <TableRow key={item.id}>
                                                    <TableCell>
                                                        <Chip
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            {item.size.code}
                                                        </Chip>
                                                    </TableCell>
                                                    <TableCell>
                                                        <QuantityInput
                                                            garmentSizeId={
                                                                item.id
                                                            }
                                                            max={
                                                                item.availableQuantity
                                                            }
                                                            orderId={
                                                                item.orderId
                                                            }
                                                            value={
                                                                item.assignedQuantity
                                                            }
                                                            onChange={onChange}
                                                        />
                                                    </TableCell>
                                                    <TableCell className="text-center">
                                                        <span className="font-medium">
                                                            {item.totalQuantity}
                                                        </span>
                                                    </TableCell>
                                                    <TableCell className="text-center">
                                                        <span
                                                            className={
                                                                item.availableQuantity ===
                                                                0
                                                                    ? "text-danger"
                                                                    : "text-success"
                                                            }
                                                        >
                                                            {
                                                                item.availableQuantity
                                                            }
                                                        </span>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>

                                    {/* Resumen del grupo */}
                                    <div className="flex justify-between items-center pt-2 border-t border-default-200">
                                        <span className="text-xs text-default-500">
                                            {group.items.length} tallas
                                        </span>
                                        <span className="text-sm">
                                            Asignado:{" "}
                                            <span className="font-medium text-primary">
                                                {group.items.reduce(
                                                    (sum: number, item) =>
                                                        sum +
                                                        item.assignedQuantity,
                                                    0,
                                                )}
                                            </span>
                                            <span className="text-default-400">
                                                {" "}
                                                /{" "}
                                            </span>
                                            <span className="text-default-600">
                                                {group.items.reduce(
                                                    (sum: number, item) =>
                                                        sum +
                                                        item.availableQuantity,
                                                    0,
                                                )}
                                            </span>
                                        </span>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </motion.div>
                ))}
            </div>
        </div>
    );
}
