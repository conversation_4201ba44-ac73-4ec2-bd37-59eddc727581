"use server";

import { db } from "@/shared/lib/db";

export async function getSubCustomers(parentId: string) {
    try {
        const subCustomers = await db.customer.findMany({
            where: {
                parentId: parentId,
            },
            include: {
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
            orderBy: {
                name: "asc",
            },
        });

        return { success: true, data: subCustomers };
    } catch (error) {
        console.error("Error fetching subcustomers:", error);

        return { success: false, error: "Error al obtener los subclientes" };
    }
}

export async function getCustomerHierarchy(customerId: string) {
    try {
        const customer = await db.customer.findUnique({
            where: { id: customerId },
            include: {
                parent: true,
                subCustomers: {
                    include: {
                        _count: {
                            select: {
                                orders: true,
                                packings: true,
                                subCustomers: true,
                            },
                        },
                    },
                    orderBy: {
                        name: "asc",
                    },
                },
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
        });

        if (!customer) {
            return { success: false, error: "Cliente no encontrado" };
        }

        return { success: true, data: customer };
    } catch (error) {
        console.error("Error fetching customer hierarchy:", error);

        return {
            success: false,
            error: "Error al obtener la jerarquía del cliente",
        };
    }
}

export async function getAllCustomersWithHierarchy() {
    try {
        // Obtener solo clientes principales (sin padre)
        const mainCustomers = await db.customer.findMany({
            where: {
                parentId: null,
            },
            include: {
                subCustomers: {
                    include: {
                        _count: {
                            select: {
                                orders: true,
                                packings: true,
                                subCustomers: true,
                            },
                        },
                    },
                    orderBy: {
                        name: "asc",
                    },
                },
                _count: {
                    select: {
                        orders: true,
                        packings: true,
                        subCustomers: true,
                    },
                },
            },
            orderBy: {
                name: "asc",
            },
        });

        return { success: true, data: mainCustomers };
    } catch (error) {
        console.error("Error fetching customers with hierarchy:", error);

        return { success: false, error: "Error al obtener los clientes" };
    }
}
