/**
 * Utilidad para generar colores de avatar consistentes y de alto contraste
 * Cumple con estándares WCAG AA (ratio de contraste mínimo 4.5:1)
 */

export interface AvatarColor {
    bg: string;
    text: string;
}

/**
 * Genera un color de avatar consistente basado en el userId
 * @param userId - ID único del usuario
 * @returns Objeto con color de fondo y texto
 */
export function getUserAvatarColor(userId?: string): AvatarColor {
    // Colores de alto contraste predefinidos
    // Todos tienen contraste mínimo de 7:1 con texto blanco
    const highContrastColors: AvatarColor[] = [
        { bg: "#1e293b", text: "#ffffff" }, // slate-800
        { bg: "#991b1b", text: "#ffffff" }, // red-800
        { bg: "#166534", text: "#ffffff" }, // green-800
        { bg: "#1e40af", text: "#ffffff" }, // blue-800
        { bg: "#6b21a8", text: "#ffffff" }, // purple-800
        { bg: "#92400e", text: "#ffffff" }, // amber-800
        { bg: "#064e3b", text: "#ffffff" }, // emerald-800
        { bg: "#831843", text: "#ffffff" }, // pink-800
        { bg: "#7c2d12", text: "#ffffff" }, // orange-800
        { bg: "#0c4a6e", text: "#ffffff" }, // sky-800
        { bg: "#312e81", text: "#ffffff" }, // indigo-800
        { bg: "#86198f", text: "#ffffff" }, // fuchsia-800
    ];

    // Si no hay userId, devolver el primer color
    if (!userId) {
        return highContrastColors[0];
    }

    // Generar hash consistente del userId
    // Usar bitwise operations para mejor distribución
    let hash = 0;

    for (let i = 0; i < userId.length; i++) {
        const char = userId.charCodeAt(i);

        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convertir a 32-bit integer
    }

    // Asegurar que el índice sea positivo y dentro del rango
    const index = Math.abs(hash) % highContrastColors.length;

    return highContrastColors[index];
}

/**
 * Obtiene las iniciales de un nombre
 * @param name - Nombre completo del usuario
 * @returns Iniciales en mayúsculas (máximo 2 caracteres)
 */
export function getInitials(name?: string | null): string {
    if (!name) return "U";

    const cleanName = name.trim();

    if (!cleanName) return "U";

    const parts = cleanName.split(/\s+/);

    if (parts.length === 1) {
        // Un solo nombre: tomar primera letra
        return parts[0].charAt(0).toUpperCase();
    }

    // Múltiples nombres: primera letra del primero y último
    const firstInitial = parts[0].charAt(0).toUpperCase();
    const lastInitial = parts[parts.length - 1].charAt(0).toUpperCase();

    return firstInitial + lastInitial;
}

/**
 * Genera un color de avatar basado en el nombre (fallback si no hay userId)
 * @param name - Nombre del usuario
 * @returns Objeto con color de fondo y texto
 */
export function getAvatarColorByName(name?: string | null): AvatarColor {
    if (!name) {
        return getUserAvatarColor();
    }

    // Usar el nombre como semilla para generar un color consistente
    return getUserAvatarColor(name);
}
