// Script para verificar la disponibilidad de órdenes para packing
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function checkOrdersAvailability() {
  try {
    console.log('🔍 Verificando disponibilidad de órdenes para packing...\n')
    
    // 1. Contar total de órdenes
    const totalOrders = await prisma.order.count()
    console.log(`📊 Total de órdenes en la base de datos: ${totalOrders}`)
    
    // 2. Obtener órdenes con sus detalles
    const orders = await prisma.order.findMany({
      take: 5, // Solo las primeras 5 para no sobrecargar
      include: {
        customer: true,
        garments: {
          include: {
            model: true,
            color: true,
            sizes: {
              include: {
                size: true
              }
            }
          }
        },
        status: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    console.log('\n📦 Analizando las primeras 5 órdenes más recientes:')
    console.log('═══════════════════════════════════════════════════')
    
    for (const order of orders) {
      console.log(`\n🔸 Orden: ${order.cutOrder || order.transferNumber || order.id}`)
      console.log(`   Cliente: ${order.customer.name} (ID: ${order.customerId})`)
      console.log(`   Estado: ${order.status?.name || 'Sin estado'}`)
      console.log(`   Creada: ${order.createdAt.toLocaleDateString()}`)
      console.log(`   Total de prendas: ${order.garments.length}`)
      
      let totalQuantity = 0
      let totalUsed = 0
      let totalAvailable = 0
      
      for (const garment of order.garments) {
        console.log(`\n   👔 Prenda: ${garment.model.code} - ${garment.color.name}`)
        console.log(`      Tallas:`)
        
        for (const size of garment.sizes) {
          const available = size.totalQuantity - size.usedQuantity
          totalQuantity += size.totalQuantity
          totalUsed += size.usedQuantity
          totalAvailable += available
          
          const status = available > 0 ? '✅' : '❌'
          console.log(`      ${status} ${size.size.code}: Total=${size.totalQuantity}, Usado=${size.usedQuantity}, Disponible=${available}`)
        }
      }
      
      console.log(`\n   📊 Resumen de la orden:`)
      console.log(`      Total cantidad: ${totalQuantity}`)
      console.log(`      Total usado: ${totalUsed}`)
      console.log(`      Total disponible: ${totalAvailable}`)
      console.log(`      ${totalAvailable > 0 ? '✅ DISPONIBLE para packing' : '❌ NO DISPONIBLE (todo usado)'}`)
    }
    
    // 3. Contar órdenes con disponibilidad
    const ordersWithAvailability = await prisma.$queryRaw<Array<{count: bigint}>>`
      SELECT COUNT(DISTINCT o.id) as count
      FROM "Order" o
      INNER JOIN "Garment" g ON g."orderId" = o.id
      INNER JOIN "GarmentSize" gs ON gs."garmentId" = g.id
      WHERE gs."totalQuantity" > gs."usedQuantity"
    `
    
    console.log(`\n\n📈 Resumen general:`)
    console.log(`═══════════════════`)
    console.log(`Total órdenes: ${totalOrders}`)
    console.log(`Órdenes con cantidad disponible: ${ordersWithAvailability[0].count}`)
    
    // 4. Verificar si hay assignments que estén consumiendo las cantidades
    const totalAssignments = await prisma.assignment.count()
    console.log(`\n📋 Total de assignments creados: ${totalAssignments}`)
    
    if (totalAssignments > 0) {
      const recentAssignments = await prisma.assignment.findMany({
        take: 3,
        include: {
          garmentSize: {
            include: {
              size: true,
              garment: {
                include: {
                  model: true,
                  color: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })
      
      console.log('\n📌 Últimos assignments (que consumen cantidad):')
      for (const assignment of recentAssignments) {
        console.log(`   - ${assignment.garmentSize.garment.model.code} ${assignment.garmentSize.garment.color.name} talla ${assignment.garmentSize.size.code}: ${assignment.quantity} unidades`)
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Ejecutar el script
checkOrdersAvailability()