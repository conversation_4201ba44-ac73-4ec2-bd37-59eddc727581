"use client";

import React, { useState, useMemo, useRef, useEffect } from "react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";

import {
    Select,
    SelectItem,
    Input,
} from "@/shared/components/ui/hero-ui-client";

interface AutocompleteSelectEnhancedProps<T> {
    label: React.ReactNode;
    placeholder?: string;
    items: T[];
    selectedKey?: string;
    onSelectionChange?: (key: string) => void;
    startContent?: React.ReactNode;
    errorMessage?: string;
    variant?: "bordered" | "flat" | "faded" | "underlined";
    isRequired?: boolean;
    isDisabled?: boolean;
    size?: "sm" | "md" | "lg";
    // Custom field mapping
    labelField?: keyof T | ((item: T) => string);
    valueField?: keyof T;
    searchFields?: (keyof T)[];
    // Custom rendering
    renderItem?: (item: T) => React.ReactNode;
    renderValue?: (item: T) => React.ReactNode;
}

export function AutocompleteSelectEnhanced<T extends Record<string, any>>({
    label,
    placeholder = "Buscar...",
    items,
    selectedKey,
    onSelectionChange,
    startContent,
    errorMessage,
    variant = "bordered",
    isRequired = false,
    isDisabled = false,
    size = "md",
    labelField,
    valueField = "id" as keyof T,
    searchFields,
    renderItem,
    renderValue,
}: AutocompleteSelectEnhancedProps<T>) {
    const [searchValue, setSearchValue] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);

    // Get label for an item
    const getItemLabel = (item: T): string => {
        if (typeof labelField === "function") {
            return labelField(item);
        }
        if (labelField) {
            return String(item[labelField]);
        }

        // Default to 'name' or 'code' if they exist
        return String(item["name"] || item["code"] || item[valueField]);
    };

    // Get search text for an item
    const getSearchText = (item: T): string => {
        if (searchFields && searchFields.length > 0) {
            return searchFields
                .map((field) => String(item[field] || ""))
                .join(" ")
                .toLowerCase();
        }

        return getItemLabel(item).toLowerCase();
    };

    // Filter items based on search
    const filteredItems = useMemo(() => {
        if (!searchValue) return items;

        const searchLower = searchValue.toLowerCase();

        return items.filter((item) =>
            getSearchText(item).includes(searchLower),
        );
    }, [items, searchValue]);

    // Get selected item
    const selectedItem = items.find(
        (item) => String(item[valueField]) === selectedKey,
    );

    // Reset search when closing
    useEffect(() => {
        if (!isOpen) {
            setSearchValue("");
        }
    }, [isOpen]);

    return (
        <div className="relative">
            <Select
                errorMessage={errorMessage}
                isDisabled={isDisabled}
                isRequired={isRequired}
                label={label}
                placeholder={placeholder}
                renderValue={() => {
                    if (!selectedItem) return "";
                    if (renderValue) return renderValue(selectedItem);

                    return getItemLabel(selectedItem);
                }}
                selectedKeys={selectedKey ? [selectedKey] : []}
                size={size}
                startContent={startContent}
                variant={variant}
                onChange={(e) => onSelectionChange?.(e.target.value)}
                onOpenChange={setIsOpen}
            >
                <SelectItem
                    key="search"
                    className="sticky top-0 bg-white dark:bg-gray-900 border-b z-10 hover:bg-transparent data-[hover=true]:bg-transparent"
                    textValue=""
                >
                    <div
                        className="cursor-default pointer-events-none"
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                        }}
                        onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                        }}
                    >
                        <Input
                            ref={inputRef}
                            className="pointer-events-auto"
                            classNames={{
                                input: "text-sm cursor-text",
                                inputWrapper: "h-9 cursor-text",
                            }}
                            placeholder="Buscar..."
                            size="sm"
                            startContent={
                                <MagnifyingGlassIcon className="w-4 h-4" />
                            }
                            value={searchValue}
                            variant="flat"
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                            onKeyDown={(e) => e.stopPropagation()}
                            onMouseDown={(e) => {
                                e.stopPropagation();
                            }}
                            onValueChange={setSearchValue}
                        />
                    </div>
                </SelectItem>
                {
                    (filteredItems.length === 0 ? (
                        <SelectItem key="no-results" isDisabled textValue="">
                            <div className="text-center text-gray-500 py-2">
                                No se encontraron resultados
                            </div>
                        </SelectItem>
                    ) : (
                        filteredItems.map((item) => (
                            <SelectItem
                                key={String(item[valueField])}
                                textValue={getItemLabel(item)}
                            >
                                {renderItem
                                    ? renderItem(item)
                                    : getItemLabel(item)}
                            </SelectItem>
                        ))
                    )) as any
                }
            </Select>
        </div>
    );
}
