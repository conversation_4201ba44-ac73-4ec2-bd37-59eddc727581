/**
 * Optimized React Query hooks for Orders
 * Uses selective field loading and cursor pagination
 */

import {
    useOptimizedQuery,
    useOptimizedInfiniteQuery,
} from "../hooks/useOptimizedQuery";
import {
    getOrdersOptimized,
    getOrdersCursorPaginated,
} from "../optimizations/order-queries";

interface UseOrdersOptions {
    search?: string;
    statusId?: string;
    customerId?: string;
    orderBy?: string;
    order?: "asc" | "desc";
    page?: number;
    perPage?: number;
}

/**
 * Hook for paginated orders with optimized queries
 */
export function useOrdersOptimized(options: UseOrdersOptions = {}) {
    return useOptimizedQuery(
        ["orders", "optimized", JSON.stringify(options)],
        () => getOrdersOptimized(options),
        {
            staleTime: 5 * 60 * 1000, // 5 minutes
            gcTime: 10 * 60 * 1000, // 10 minutes
        } as any,
    );
}

/**
 * Hook for infinite scroll orders
 */
export function useOrdersInfinite(
    options: Omit<UseOrdersOptions, "page"> = {},
) {
    return useOptimizedInfiniteQuery(
        ["orders", "infinite", JSON.stringify(options)],
        ({ pageParam }) =>
            getOrdersCursorPaginated({
                ...options,
                cursor: pageParam,
            }),
        {
            getNextPageParam: (lastPage: any) => lastPage.nextCursor,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        } as any,
    );
}

/**
 * Migration helper - drop-in replacement for existing useOrders
 */
export function useOrders(options: UseOrdersOptions = {}) {
    const query = useOrdersOptimized(options);

    // Transform to match existing API
    return {
        orders: query.data?.data?.orders || [],
        pagination: query.data?.data?.pagination,
        isLoading: query.isLoading,
        isError: query.isError,
        error: query.error,
        refresh: query.refetch,
    };
}
