"use client";

interface PasswordStrengthProps {
    password: string;
}

export function PasswordStrength({ password }: PasswordStrengthProps) {
    const calculateStrength = (pass: string): number => {
        let strength = 0;

        if (pass.length >= 8) strength++;
        if (pass.length >= 12) strength++;
        if (/[a-z]/.test(pass) && /[A-Z]/.test(pass)) strength++;
        if (/\d/.test(pass)) strength++;
        if (/[^a-zA-Z\d]/.test(pass)) strength++;

        return strength;
    };

    const strength = calculateStrength(password);
    const percentage = (strength / 5) * 100;

    const getColor = () => {
        if (strength <= 1) return "bg-error";
        if (strength <= 2) return "bg-warning";
        if (strength <= 3) return "bg-yellow-500";

        return "bg-success";
    };

    const getLabel = () => {
        if (strength <= 1) return "Muy débil";
        if (strength <= 2) return "Débil";
        if (strength <= 3) return "Media";
        if (strength <= 4) return "Fuerte";

        return "Muy fuerte";
    };

    if (!password) return null;

    return (
        <div className="mt-2">
            <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-neutral-600">
                    Fortaleza de contraseña
                </span>
                <span
                    className={`text-xs font-medium ${getColor().replace("bg-", "text-")}`}
                >
                    {getLabel()}
                </span>
            </div>
            <div className="h-2 bg-neutral-200 rounded-full overflow-hidden">
                <div
                    className={`h-full transition-all duration-300 ${getColor()}`}
                    style={{ width: `${percentage}%` }}
                />
            </div>
        </div>
    );
}
