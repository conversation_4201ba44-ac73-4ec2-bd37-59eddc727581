"use client";

import { Textarea, DatePicker } from "@heroui/react";
import { CalendarDays, FileText } from "lucide-react";
import { parseDate } from "@internationalized/date";

import { CustomerSubClientSelector } from "../CustomerSubClientSelector";

interface BasicInfo {
    customerId: string;
    subCustomerId?: string;
    deliveryDate: string;
    notes?: string;
}

interface BasicInfoStepProps {
    data: BasicInfo;
    onChange: (data: BasicInfo) => void;
}

export function BasicInfoStep({ data, onChange }: BasicInfoStepProps) {
    const handleCustomerChange = (value: {
        customerId: string;
        subCustomerId?: string;
    }) => {
        onChange({
            ...data,
            customerId: value.customerId,
            subCustomerId: value.subCustomerId,
        });
    };

    const handleDateChange = (date: any) => {
        if (date) {
            onChange({
                ...data,
                deliveryDate: date.toString(),
            });
        }
    };

    const handleNotesChange = (value: string) => {
        onChange({
            ...data,
            notes: value,
        });
    };

    return (
        <div className="space-y-6">
            <div>
                <CustomerSubClientSelector
                    isRequired
                    error={
                        !data.customerId ? "Cliente es requerido" : undefined
                    }
                    value={{
                        customerId: data.customerId,
                        subCustomerId: data.subCustomerId,
                    }}
                    onChange={handleCustomerChange}
                />
            </div>

            <div>
                <DatePicker
                    isRequired
                    errorMessage={
                        !data.deliveryDate
                            ? "Fecha de entrega es requerida"
                            : undefined
                    }
                    granularity="day"
                    label="Fecha de entrega"
                    startContent={
                        <CalendarDays className="w-4 h-4 text-gray-400" />
                    }
                    value={
                        data.deliveryDate ? parseDate(data.deliveryDate) : null
                    }
                    onChange={handleDateChange}
                />
            </div>

            <div>
                <Textarea
                    label="Notas (opcional)"
                    maxRows={6}
                    minRows={3}
                    placeholder="Ingrese notas adicionales sobre el packing..."
                    startContent={
                        <FileText className="w-4 h-4 text-gray-400" />
                    }
                    value={data.notes || ""}
                    onValueChange={handleNotesChange}
                />
            </div>
        </div>
    );
}
