"use client";

import React from "react";
import { Progress } from "@heroui/react";

interface ColorFormProgressProps {
    value: number;
    className?: string;
}

export function ColorFormProgress({
    value,
    className = "",
}: ColorFormProgressProps) {
    const getColor = () => {
        if (value < 30) return "danger";
        if (value < 60) return "warning";
        if (value < 100) return "primary";

        return "success";
    };

    return (
        <div className={`${className}`}>
            <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                    Progreso del formulario
                </span>
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    {value}%
                </span>
            </div>
            <Progress
                aria-label="Progreso del formulario"
                classNames={{
                    base: "max-w-full",
                    track: "drop-shadow-sm border border-default",
                    indicator: "bg-gradient-to-r",
                    label: "tracking-wider font-medium text-default-600",
                    value: "text-foreground/60",
                }}
                color={getColor()}
                size="sm"
                value={value}
            />
        </div>
    );
}

export function calculateCreateProgress(name: string, hexCode: string): number {
    let progress = 0;

    // Nombre del color (50%)
    if (name && name.trim().length >= 2) {
        progress += 50;
    }

    // Código hexadecimal válido (50%)
    if (hexCode && /^#(?:[0-9a-fA-F]{3}){1,2}$/.test(hexCode)) {
        progress += 50;
    }

    return progress;
}

export function calculateEditProgress(
    formData: { name?: string; hexCode?: string },
    originalData: { name: string; hexCode: string },
    isDirty: boolean,
): number {
    if (!isDirty) return 0;

    let progress = 0;
    let changedFields = 0;
    let validFields = 0;

    // Verificar cambios en el nombre
    if (formData.name !== originalData.name) {
        changedFields++;
        if (formData.name && formData.name.trim().length >= 2) {
            validFields++;
        }
    }

    // Verificar cambios en el código hexadecimal
    if (formData.hexCode !== originalData.hexCode) {
        changedFields++;
        if (
            formData.hexCode &&
            /^#(?:[0-9a-fA-F]{3}){1,2}$/.test(formData.hexCode)
        ) {
            validFields++;
        }
    }

    if (changedFields === 0) return 0;

    progress = Math.round((validFields / changedFields) * 100);

    return progress;
}
