"use client";

import React, { useState } from "react";
import {
    BellIcon,
    ExclamationTriangleIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";

import {
    Button,
    Badge,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
} from "@/shared/components/ui/hero-ui-client";
import { Order } from "@/features/orders/types/orders";

import { useOrderAlerts } from "./OrderAlerts";

interface CompactAlertsProps {
    orders: Order[];
    onViewOrder?: (order: Order) => void;
}

export default function CompactAlerts({
    orders,
    onViewOrder,
}: CompactAlertsProps) {
    const alerts = useOrderAlerts(orders, onViewOrder);
    const [isOpen, setIsOpen] = useState(false);
    const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(
        new Set(),
    );

    const visibleAlerts = alerts.filter(
        (alert) => !dismissedAlerts.has(alert.id),
    );
    const criticalAlerts = visibleAlerts.filter(
        (a) => a.type === "danger" || a.type === "warning",
    );

    if (visibleAlerts.length === 0) {
        return null;
    }

    const getAlertIcon = (type: string) => {
        switch (type) {
            case "danger":
                return "🚨";
            case "warning":
                return "⚠️";
            case "success":
                return "✅";
            default:
                return "ℹ️";
        }
    };

    return (
        <div className="flex items-center gap-2">
            <Dropdown isOpen={isOpen} onOpenChange={setIsOpen}>
                <DropdownTrigger>
                    <Button
                        color={criticalAlerts.length > 0 ? "danger" : "default"}
                        endContent={
                            <Badge
                                color={
                                    criticalAlerts.length > 0
                                        ? "danger"
                                        : "primary"
                                }
                                size="sm"
                            >
                                {visibleAlerts.length}
                            </Badge>
                        }
                        size="sm"
                        startContent={
                            criticalAlerts.length > 0 ? (
                                <ExclamationTriangleIcon className="w-4 h-4" />
                            ) : (
                                <BellIcon className="w-4 h-4" />
                            )
                        }
                        variant={criticalAlerts.length > 0 ? "flat" : "light"}
                    >
                        Alertas
                    </Button>
                </DropdownTrigger>
                <DropdownMenu
                    aria-label="Alertas del sistema"
                    className="max-w-sm"
                >
                    {visibleAlerts.map((alert) => (
                        <DropdownItem
                            key={alert.id}
                            className="py-2"
                            endContent={
                                <button
                                    className="text-gray-400 hover:text-gray-600"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setDismissedAlerts((prev) =>
                                            new Set(prev).add(alert.id),
                                        );
                                    }}
                                >
                                    <XMarkIcon className="w-4 h-4" />
                                </button>
                            }
                            textValue={alert.title}
                        >
                            <div className="flex items-start gap-2">
                                <span className="text-lg">
                                    {getAlertIcon(alert.type)}
                                </span>
                                <div className="flex-1">
                                    <p className="font-medium text-sm">
                                        {alert.title}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {alert.description}
                                    </p>
                                    {alert.action && (
                                        <Button
                                            className="mt-1"
                                            color={alert.type as any}
                                            size="sm"
                                            variant="light"
                                            onPress={() => {
                                                alert.action?.onClick();
                                                setIsOpen(false);
                                            }}
                                        >
                                            {alert.action.label}
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </DropdownItem>
                    ))}
                </DropdownMenu>
            </Dropdown>

            {/* Mostrar alerta crítica inline si hay */}
            {criticalAlerts.length > 0 && (
                <div className="text-sm text-danger font-medium">
                    {criticalAlerts[0].title}
                </div>
            )}
        </div>
    );
}
