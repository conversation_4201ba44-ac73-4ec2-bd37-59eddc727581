-- Add critical indexes for performance and stability

-- Order table indexes
CREATE INDEX IF NOT EXISTS "Order_customerId_idx" ON "Order"("customerId");
CREATE INDEX IF NOT EXISTS "Order_statusId_idx" ON "Order"("statusId");
CREATE INDEX IF NOT EXISTS "Order_customerId_statusId_createdAt_idx" ON "Order"("customerId", "statusId", "createdAt" DESC);

-- Garment table indexes
CREATE INDEX IF NOT EXISTS "Garment_orderId_idx" ON "Garment"("orderId");
CREATE INDEX IF NOT EXISTS "Garment_modelId_idx" ON "Garment"("modelId");
CREATE INDEX IF NOT EXISTS "Garment_colorId_idx" ON "Garment"("colorId");

-- GarmentSize table indexes
CREATE INDEX IF NOT EXISTS "GarmentSize_garmentId_idx" ON "GarmentSize"("garmentId");
CREATE INDEX IF NOT EXISTS "GarmentSize_sizeId_idx" ON "GarmentSize"("sizeId");

-- Packing table indexes
CREATE INDEX IF NOT EXISTS "Packing_customerId_idx" ON "Packing"("customerId");
CREATE INDEX IF NOT EXISTS "Packing_subCustomerId_idx" ON "Packing"("subCustomerId");
CREATE INDEX IF NOT EXISTS "Packing_orderId_idx" ON "Packing"("orderId");
CREATE INDEX IF NOT EXISTS "Packing_statusId_idx" ON "Packing"("statusId");

-- PackingDetail table indexes
CREATE INDEX IF NOT EXISTS "PackingDetail_packingId_idx" ON "PackingDetail"("packingId");
CREATE INDEX IF NOT EXISTS "PackingDetail_garmentSizeId_idx" ON "PackingDetail"("garmentSizeId");
CREATE INDEX IF NOT EXISTS "PackingDetail_packingId_garmentSizeId_idx" ON "PackingDetail"("packingId", "garmentSizeId");

-- Assignment table indexes
CREATE INDEX IF NOT EXISTS "Assignment_contractorId_idx" ON "Assignment"("contractorId");
CREATE INDEX IF NOT EXISTS "Assignment_contractorId_status_createdAt_idx" ON "Assignment"("contractorId", "status", "createdAt" DESC);

-- Note table indexes (if not already present)
CREATE INDEX IF NOT EXISTS "Note_authorId_createdAt_idx" ON "Note"("authorId", "createdAt" DESC);

-- Customer table index for hierarchical queries
CREATE INDEX IF NOT EXISTS "Customer_parentId_idx" ON "Customer"("parentId");

-- RateLimitLog table
CREATE TABLE IF NOT EXISTS "RateLimitLog" (
    "id" TEXT NOT NULL,
    "namespace" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RateLimitLog_pkey" PRIMARY KEY ("id")
);

CREATE INDEX IF NOT EXISTS "RateLimitLog_namespace_identifier_createdAt_idx" ON "RateLimitLog"("namespace", "identifier", "createdAt");
CREATE INDEX IF NOT EXISTS "RateLimitLog_createdAt_idx" ON "RateLimitLog"("createdAt");

-- Add data integrity constraints
ALTER TABLE "GarmentSize" DROP CONSTRAINT IF EXISTS "check_used_quantity";
ALTER TABLE "GarmentSize" ADD CONSTRAINT "check_used_quantity" 
  CHECK ("usedQuantity" >= 0 AND "usedQuantity" <= "totalQuantity");

ALTER TABLE "PackingDetail" DROP CONSTRAINT IF EXISTS "check_positive_quantity";
ALTER TABLE "PackingDetail" ADD CONSTRAINT "check_positive_quantity" 
  CHECK ("quantity" > 0);

-- Add constraint to prevent negative total quantity
ALTER TABLE "GarmentSize" DROP CONSTRAINT IF EXISTS "check_positive_total";
ALTER TABLE "GarmentSize" ADD CONSTRAINT "check_positive_total" 
  CHECK ("totalQuantity" >= 0);

-- Add constraint for dates
ALTER TABLE "Packing" DROP CONSTRAINT IF EXISTS "check_delivery_date";
ALTER TABLE "Packing" ADD CONSTRAINT "check_delivery_date" 
  CHECK ("deliveryDate" >= CURRENT_DATE - INTERVAL '1 day');