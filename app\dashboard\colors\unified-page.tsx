import { Suspense } from "react";

import { Spinner } from "@/shared/components/ui/hero-ui-client";

import UnifiedColorsPage from "./UnifiedClientPage";

/**
 * Página de colores con el nuevo sistema de diseño unificado
 */
export default function ColorsUnifiedPage() {
    return (
        <Suspense
            fallback={
                <div className="flex items-center justify-center min-h-[50vh]">
                    <Spinner label="Cargando colores..." size="lg" />
                </div>
            }
        >
            <UnifiedColorsPage />
        </Suspense>
    );
}
