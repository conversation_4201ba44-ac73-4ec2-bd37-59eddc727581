"use client";

import { useEffect, useMemo, useState } from "react";
import {
    Autocomplete,
    AutocompleteItem,
    Card,
    CardBody,
    Spinner,
    Chip,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    BuildingOfficeIcon,
    CheckCircleIcon,
    PhoneIcon,
    EnvelopeIcon,
    ClockIcon,
    SparklesIcon,
    UserGroupIcon,
    CubeTransparentIcon,
    CheckBadgeIcon,
    CalendarDaysIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import useSWR, { mutate } from "swr";
import { Contractor } from "@prisma/client";

import { getContractorsForSelector } from "@/features/assignments/actions";
import { getContractorMetrics } from "@/features/contractors/actions";
import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";
import { ContractorAvatar } from "@/features/assignments/components/wizard/ui/ContractorAvatar";
import {
    ContextualTooltip,
    tooltipContexts,
} from "@/features/assignments/components/wizard/ui/ContextualTooltip";

// Componente mejorado para mostrar un contratista
const ContractorItemContent = ({ contractor }: { contractor: Contractor }) => {
    return (
        <div className="flex items-center gap-4 py-2 px-1">
            <motion.div
                transition={{ type: "spring", stiffness: 400 }}
                whileHover={{ scale: 1.05 }}
            >
                <ContractorAvatar
                    className="ring-2 ring-offset-2 ring-blue-100"
                    name={contractor.name}
                    size="md"
                />
            </motion.div>
            <div className="flex-1">
                <div className="font-semibold text-gray-800 dark:text-gray-100">
                    {contractor.name}
                </div>
                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                    {contractor.phone && (
                        <span className="flex items-center gap-1">
                            <PhoneIcon className="w-3 h-3" />
                            {contractor.phone}
                        </span>
                    )}
                    {contractor.email && (
                        <span className="flex items-center gap-1">
                            <EnvelopeIcon className="w-3 h-3" />
                            {contractor.email}
                        </span>
                    )}
                </div>
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-500">
                <ClockIcon className="w-3 h-3" />
                <span>Disponible</span>
            </div>
        </div>
    );
};

export default function ContractorStep() {
    const { state, dispatch } = useWizard();
    const [searchValue, setSearchValue] = useState("");

    const { data, error, isLoading } = useSWR<{ contractors: Contractor[] }>(
        "contractors-for-selector",
        async () => getContractorsForSelector() as any,
    );

    // Obtener métricas generales
    const { data: metricsData } = useSWR(
        "contractors-overall-metrics",
        async () => undefined,
    );

    // Obtener métricas del contratista seleccionado
    const { data: selectedMetrics } = useSWR(
        state.contractor ? `contractor-metrics-${state.contractor}` : null,
        async () =>
            state.contractor ? getContractorMetrics(state.contractor) : null,
    );

    const handleSelectionChange = (contractorId: string) => {
        const selected =
            data?.contractors.find((c) => c.id === contractorId) || null;

        dispatch({ type: "SET_CONTRACTOR", payload: contractorId });
        dispatch({ type: "SET_CONTRACTOR_DATA", payload: selected as any });

        // Invalidar métricas anteriores y cargar las nuevas
        if (contractorId) {
            mutate(`contractor-metrics-${contractorId}`);
        }
    };

    const contractors = useMemo(() => {
        return data?.contractors || [];
    }, [data]);

    // Auto-selección si hay un solo contratista
    useEffect(() => {
        if (contractors.length === 1 && !state.contractor) {
            const single = contractors[0];

            dispatch({ type: "SET_CONTRACTOR", payload: single.id });
            dispatch({ type: "SET_CONTRACTOR_DATA", payload: single as any });
        }
    }, [contractors, state.contractor, dispatch]);

    const selectedContractor = useMemo(() => {
        if (!state.contractor) return null;

        return contractors.find((c) => c.id === state.contractor) || null;
    }, [state.contractor, contractors]);

    return (
        <motion.div
            animate={{ opacity: 1 }}
            className="space-y-6 max-w-4xl mx-auto"
            exit={{ opacity: 0 }}
            initial={{ opacity: 0 }}
        >
            {/* Header Card mejorado */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
            >
                <Card className="bg-gradient-to-br from-white via-blue-50/50 to-indigo-50/30 dark:from-gray-900 dark:via-blue-950/50 dark:to-indigo-950/30 shadow-xl border-0 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-600/5" />
                    <CardBody className="relative p-8">
                        <div className="flex items-center gap-4 mb-3">
                            <motion.div
                                className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg"
                                transition={{ type: "spring", stiffness: 300 }}
                                whileHover={{ scale: 1.05, rotate: 5 }}
                            >
                                <BuildingOfficeIcon className="w-7 h-7 text-white" />
                            </motion.div>
                            <div className="flex-1">
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                                    Seleccionar Contratista
                                </h2>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-1.5">
                                    <SparklesIcon className="w-4 h-4 text-indigo-500" />
                                    Elige el contratista que realizará esta
                                    asignación
                                </p>
                            </div>
                            <ContextualTooltip
                                content={tooltipContexts.contractor.text}
                                variant={tooltipContexts.contractor.variant}
                            />
                        </div>

                        {/* Stats preview - Métricas empresariales relevantes */}
                        {contractors.length > 0 && (
                            <div className="grid grid-cols-3 gap-4 mt-6">
                                <motion.div
                                    className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl backdrop-blur-sm border border-blue-200/50 dark:border-blue-700/50 shadow-sm hover:shadow-md transition-all cursor-help"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <UserGroupIcon className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {(metricsData as any)?.data
                                            ?.totalContractors ||
                                            contractors.length}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Disponibles
                                    </p>
                                </motion.div>
                                <motion.div
                                    className="text-center p-3 bg-gradient-to-br from-emerald-50 to-emerald-100/50 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-xl backdrop-blur-sm border border-emerald-200/50 dark:border-emerald-700/50 shadow-sm hover:shadow-md transition-all cursor-help"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <CubeTransparentIcon className="w-5 h-5 text-emerald-600 mx-auto mb-1" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {(metricsData as any)?.data
                                            ?.totalActiveAssignments || 0}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Asignaciones Activas
                                    </p>
                                </motion.div>
                                <motion.div
                                    className="text-center p-3 bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl backdrop-blur-sm border border-purple-200/50 dark:border-purple-700/50 shadow-sm hover:shadow-md transition-all cursor-help"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <CheckBadgeIcon className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {(metricsData as any)?.data
                                            ?.overallCompletionRate || 0}
                                        %
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Tasa Cumplimiento
                                    </p>
                                </motion.div>
                            </div>
                        )}
                    </CardBody>
                </Card>
            </motion.div>

            {/* Main Content Card */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                initial={{ y: 20, opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.1, ease: "easeOut" }}
            >
                <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
                    <CardBody className="p-8">
                        {isLoading ? (
                            <motion.div
                                animate={{ opacity: 1 }}
                                className="flex flex-col items-center justify-center py-16"
                                initial={{ opacity: 0 }}
                            >
                                <div className="relative mb-4">
                                    <div className="w-20 h-20 border-4 border-blue-200 rounded-full animate-pulse" />
                                    <div className="absolute inset-0 w-20 h-20 border-4 border-blue-600 rounded-full animate-spin border-t-transparent" />
                                </div>
                                <Spinner color="primary" size="lg" />
                                <p className="mt-4 text-gray-600 dark:text-gray-400 animate-pulse">
                                    Cargando contratistas disponibles...
                                </p>
                            </motion.div>
                        ) : error ? (
                            <motion.div
                                animate={{ opacity: 1, scale: 1 }}
                                className="text-center py-12"
                                initial={{ opacity: 0, scale: 0.9 }}
                            >
                                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                                    <span className="text-2xl">⚠️</span>
                                </div>
                                <p className="text-red-600 dark:text-red-400 font-medium">
                                    Error al cargar los contratistas
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                    Por favor, intenta de nuevo más tarde
                                </p>
                            </motion.div>
                        ) : contractors.length === 0 ? (
                            <motion.div
                                animate={{ opacity: 1, scale: 1 }}
                                className="text-center py-12"
                                initial={{ opacity: 0, scale: 0.9 }}
                            >
                                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                                    <UserGroupIcon className="w-8 h-8 text-gray-400" />
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 font-medium">
                                    No hay contratistas disponibles
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                                    Agrega contratistas al sistema para
                                    continuar
                                </p>
                            </motion.div>
                        ) : (
                            <div className="space-y-6">
                                <div className="relative">
                                    <Autocomplete
                                        aria-label="Seleccionar contratista"
                                        className="max-w-2xl mx-auto"
                                        classNames={{
                                            base: "max-w-2xl",
                                            listboxWrapper: "max-h-[400px]",
                                            popoverContent:
                                                "shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm",
                                        }}
                                        defaultItems={contractors}
                                        isDisabled={contractors.length === 1}
                                        label="Buscar contratista"
                                        labelPlacement="outside"
                                        placeholder="Escribe para buscar..."
                                        selectedKey={
                                            state.contractor || undefined
                                        }
                                        size="lg"
                                        startContent={
                                            <motion.div
                                                animate={{
                                                    rotate: searchValue
                                                        ? 360
                                                        : 0,
                                                }}
                                                transition={{ duration: 0.5 }}
                                            >
                                                <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                                            </motion.div>
                                        }
                                        variant="bordered"
                                        onInputChange={setSearchValue}
                                        onSelectionChange={(key) =>
                                            handleSelectionChange(key as string)
                                        }
                                    >
                                        {(contractor: Contractor) => (
                                            <AutocompleteItem
                                                key={contractor.id}
                                                className="py-2"
                                                textValue={contractor.name}
                                            >
                                                <ContractorItemContent
                                                    contractor={contractor}
                                                />
                                            </AutocompleteItem>
                                        )}
                                    </Autocomplete>

                                    {contractors.length === 1 && (
                                        <motion.div
                                            animate={{ opacity: 1, y: 0 }}
                                            className="mt-4 flex justify-center"
                                            initial={{ opacity: 0, y: -10 }}
                                            transition={{ delay: 0.3 }}
                                        >
                                            <Chip
                                                classNames={{
                                                    base: "py-5 px-4",
                                                    content: "font-medium",
                                                }}
                                                color="success"
                                                startContent={
                                                    <CheckCircleIcon className="w-4 h-4" />
                                                }
                                                variant="flat"
                                            >
                                                Único contratista disponible -
                                                Seleccionado automáticamente
                                            </Chip>
                                        </motion.div>
                                    )}
                                </div>

                                <AnimatePresence mode="wait">
                                    {selectedContractor && (
                                        <motion.div
                                            animate={{
                                                opacity: 1,
                                                y: 0,
                                                scale: 1,
                                            }}
                                            className="max-w-2xl mx-auto"
                                            exit={{
                                                opacity: 0,
                                                y: -20,
                                                scale: 0.95,
                                            }}
                                            initial={{
                                                opacity: 0,
                                                y: 20,
                                                scale: 0.95,
                                            }}
                                            transition={{
                                                duration: 0.4,
                                                ease: "easeOut",
                                            }}
                                        >
                                            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-0 shadow-lg overflow-hidden">
                                                <div className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-emerald-600/10" />
                                                <CardBody className="relative p-6">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-4">
                                                            <motion.div
                                                                transition={{
                                                                    type: "spring",
                                                                    stiffness: 300,
                                                                }}
                                                                whileHover={{
                                                                    scale: 1.05,
                                                                }}
                                                            >
                                                                <ContractorAvatar
                                                                    className="ring-4 ring-green-100 dark:ring-green-900 shadow-lg"
                                                                    name={
                                                                        selectedContractor.name
                                                                    }
                                                                    size="xl"
                                                                />
                                                            </motion.div>
                                                            <div>
                                                                <h4 className="font-bold text-xl text-gray-800 dark:text-gray-100">
                                                                    {
                                                                        selectedContractor.name
                                                                    }
                                                                </h4>
                                                                <div className="flex flex-wrap items-center gap-3 mt-2">
                                                                    {selectedContractor.email && (
                                                                        <div className="flex items-center gap-1.5 text-sm text-gray-600 dark:text-gray-400">
                                                                            <EnvelopeIcon className="w-4 h-4" />
                                                                            <span>
                                                                                {
                                                                                    selectedContractor.email
                                                                                }
                                                                            </span>
                                                                        </div>
                                                                    )}
                                                                    {selectedContractor.phone && (
                                                                        <div className="flex items-center gap-1.5 text-sm text-gray-600 dark:text-gray-400">
                                                                            <PhoneIcon className="w-4 h-4" />
                                                                            <span>
                                                                                {
                                                                                    selectedContractor.phone
                                                                                }
                                                                            </span>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <Chip
                                                                    className="mt-3"
                                                                    color="success"
                                                                    size="sm"
                                                                    startContent={
                                                                        <CheckCircleIcon className="w-3 h-3" />
                                                                    }
                                                                    variant="flat"
                                                                >
                                                                    Seleccionado
                                                                </Chip>
                                                            </div>
                                                        </div>
                                                        <motion.div
                                                            animate={{
                                                                scale: 1,
                                                            }}
                                                            className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg"
                                                            initial={{
                                                                scale: 0,
                                                            }}
                                                            transition={{
                                                                delay: 0.2,
                                                                type: "spring",
                                                                stiffness: 200,
                                                            }}
                                                        >
                                                            <CheckCircleIcon className="w-8 h-8 text-white" />
                                                        </motion.div>
                                                    </div>

                                                    {/* Métricas del contratista - Información empresarial relevante */}
                                                    <div className="grid grid-cols-3 gap-3 mt-6 pt-6 border-t border-green-200 dark:border-green-800">
                                                        <motion.div
                                                            className="text-center p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors cursor-help"
                                                            whileHover={{
                                                                scale: 1.05,
                                                            }}
                                                        >
                                                            <CubeTransparentIcon className="w-5 h-5 text-emerald-600 mx-auto mb-1" />
                                                            <p className="text-lg font-bold text-gray-800 dark:text-gray-100">
                                                                {selectedMetrics
                                                                    ?.data
                                                                    ?.activeOrders ||
                                                                    0}
                                                            </p>
                                                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                                                Órdenes Activas
                                                            </p>
                                                        </motion.div>
                                                        <motion.div
                                                            className="text-center p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors cursor-help"
                                                            whileHover={{
                                                                scale: 1.05,
                                                            }}
                                                        >
                                                            <CalendarDaysIcon className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                                                            <p className="text-lg font-bold text-gray-800 dark:text-gray-100">
                                                                {selectedMetrics
                                                                    ?.data
                                                                    ?.daysSinceLastDelivery ? (
                                                                    <>
                                                                        <span className="text-sm">
                                                                            Hace
                                                                        </span>{" "}
                                                                        {
                                                                            selectedMetrics
                                                                                .data
                                                                                .daysSinceLastDelivery
                                                                        }
                                                                        d
                                                                    </>
                                                                ) : (
                                                                    <span className="text-sm">
                                                                        Sin
                                                                        entregas
                                                                    </span>
                                                                )}
                                                            </p>
                                                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                                                Última Entrega
                                                            </p>
                                                        </motion.div>
                                                        <motion.div
                                                            className="text-center p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors cursor-help"
                                                            whileHover={{
                                                                scale: 1.05,
                                                            }}
                                                        >
                                                            <CheckBadgeIcon className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                                                            <p className="text-lg font-bold text-gray-800 dark:text-gray-100">
                                                                {selectedMetrics?.data?.totalCompletedPieces?.toLocaleString() ||
                                                                    0}
                                                            </p>
                                                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                                                Piezas
                                                                Completadas
                                                            </p>
                                                        </motion.div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </div>
                        )}
                    </CardBody>
                </Card>
            </motion.div>
        </motion.div>
    );
}
