import { NextResponse } from "next/server";

/**
 * POST /api/auth/logout
 * Logs out the user by clearing NextAuth session
 */
export async function POST() {
    try {
        // Call NextAuth signOut
        // Since signOut is a server action that triggers a redirect,
        // we need to handle it differently in an API route

        // Clear the session by returning a response that clears the session cookie
        const response = NextResponse.json(
            {
                success: true,
                message: "Sesión cerrada exitosamente",
            },
            { status: 200 },
        );

        // Clear NextAuth session cookies
        // NextAuth uses these cookie names by default
        response.cookies.set("authjs.csrf-token", "", {
            httpOnly: true,
            sameSite: "lax",
            secure: process.env.NODE_ENV === "production",
            maxAge: 0,
        });

        response.cookies.set("authjs.callback-url", "", {
            httpOnly: true,
            sameSite: "lax",
            secure: process.env.NODE_ENV === "production",
            maxAge: 0,
        });

        response.cookies.set("authjs.session-token", "", {
            httpOnly: true,
            sameSite: "lax",
            secure: process.env.NODE_ENV === "production",
            maxAge: 0,
        });

        // Also try the secure cookie name used in production
        response.cookies.set("__Secure-authjs.session-token", "", {
            httpOnly: true,
            sameSite: "lax",
            secure: true,
            maxAge: 0,
        });

        return response;
    } catch {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        // Even if there's an error, we should still try to clear the session
        const response = NextResponse.json(
            {
                success: false,
                error: {
                    message: "Error al cerrar sesión",
                    code: "LOGOUT_ERROR",
                },
            },
            { status: 500 },
        );

        // Attempt to clear cookies even on error
        response.cookies.set("authjs.session-token", "", { maxAge: 0 });
        response.cookies.set("__Secure-authjs.session-token", "", {
            maxAge: 0,
        });

        return response;
    }
}
