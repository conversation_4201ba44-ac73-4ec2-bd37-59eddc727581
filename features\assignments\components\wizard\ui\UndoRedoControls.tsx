"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Kbd } from "@heroui/react";
import {
    ArrowUturnLeftIcon,
    ArrowUturnRightIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";

interface UndoRedoControlsProps {
    className?: string;
    size?: "sm" | "md" | "lg";
    showLabels?: boolean;
}

export function UndoRedoControls({
    className = "",
    size = "sm",
    showLabels = false,
}: UndoRedoControlsProps) {
    const { state, undo, redo } = useWizard();

    return (
        <div className={`flex items-center gap-2 ${className}`}>
            <AnimatePresence mode="wait">
                <Tooltip
                    content={
                        <div className="flex flex-col gap-1">
                            <span><PERSON><PERSON>cer</span>
                            <div className="flex gap-1">
                                <Kbd>Ctrl</Kbd>
                                <span>+</span>
                                <Kbd>Z</Kbd>
                            </div>
                        </div>
                    }
                    placement="bottom"
                >
                    <motion.div
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        initial={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.15 }}
                    >
                        <Button
                            className="min-w-unit-8"
                            color="default"
                            isDisabled={!state.canUndo}
                            size={size}
                            startContent={
                                <ArrowUturnLeftIcon className="w-4 h-4" />
                            }
                            variant="flat"
                            onPress={undo}
                        >
                            {showLabels && "Deshacer"}
                        </Button>
                    </motion.div>
                </Tooltip>
            </AnimatePresence>

            <AnimatePresence mode="wait">
                <Tooltip
                    content={
                        <div className="flex flex-col gap-1">
                            <span>Rehacer</span>
                            <div className="flex gap-1">
                                <Kbd>Ctrl</Kbd>
                                <span>+</span>
                                <Kbd>Y</Kbd>
                            </div>
                        </div>
                    }
                    placement="bottom"
                >
                    <motion.div
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        initial={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.15 }}
                    >
                        <Button
                            className="min-w-unit-8"
                            color="default"
                            isDisabled={!state.canRedo}
                            size={size}
                            startContent={
                                <ArrowUturnRightIcon className="w-4 h-4" />
                            }
                            variant="flat"
                            onPress={redo}
                        >
                            {showLabels && "Rehacer"}
                        </Button>
                    </motion.div>
                </Tooltip>
            </AnimatePresence>
        </div>
    );
}
