"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

import { toggleNoteLike, getNoteLikes } from "../actions/likes";

export function useNoteLikes(noteId: string, initialCount = 0) {
    const { data: session } = useSession();
    const userId = session?.user?.id;

    const [isLiked, setIsLiked] = useState(false);
    const [likesCount, setLikesCount] = useState(initialCount);
    const [isLoading, setIsLoading] = useState(false);

    // Cargar el estado inicial de likes
    useEffect(() => {
        if (noteId) {
            getNoteLikes(noteId, userId).then((result) => {
                if (result.success) {
                    setLikesCount(result.count || 0);
                    setIsLiked(result.isLiked || false);
                }
            });
        }
    }, [noteId, userId]);

    const toggleLike = async () => {
        if (!userId) {
            // Si no hay usuario, no hacer nada
            return;
        }

        setIsLoading(true);

        // Actualización optimista
        setIsLiked((prev) => !prev);
        setLikesCount((prev) => (isLiked ? prev - 1 : prev + 1));

        try {
            const result = await toggleNoteLike({ noteId });

            if (result.success) {
                // Actualizar con los datos reales del servidor
                setIsLiked(result.liked || false);
                setLikesCount(result.likesCount || 0);
            } else {
                // Revertir si hay error
                setIsLiked((prev) => !prev);
                setLikesCount((prev) => (isLiked ? prev + 1 : prev - 1));
            }
        } catch (error) {
            // Revertir en caso de error
            setIsLiked((prev) => !prev);
            setLikesCount((prev) => (isLiked ? prev + 1 : prev - 1));
        } finally {
            setIsLoading(false);
        }
    };

    return {
        isLiked,
        likesCount,
        isLoading,
        toggleLike,
        canLike: !!userId,
    };
}
