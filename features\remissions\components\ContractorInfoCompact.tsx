"use client";

import { useEffect, useState } from "react";
import { UserIcon } from "@heroicons/react/24/outline";

import { getContractor } from "@/features/contractors/actions";

interface ContractorInfoCompactProps {
    contractorId: string;
}

interface Contractor {
    id: string;
    name: string;
    firstName?: string;
    middleName?: string;
    lastName?: string;
    secondLastName?: string;
    email?: string;
    phone?: string;
    notes?: string;
}

export default function ContractorInfoCompact({
    contractorId,
}: ContractorInfoCompactProps) {
    const [contractor, setContractor] = useState<Contractor | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchContractor = async () => {
            try {
                const result = await getContractor(contractorId);

                if (result.success && result.data) {
                    setContractor(result.data as Contractor);
                }
            } catch (error) {
                console.error("Error fetching contractor:", error);
            } finally {
                setLoading(false);
            }
        };

        if (contractorId) {
            fetchContractor();
        } else {
            setLoading(false);
        }
    }, [contractorId]);

    if (loading) {
        return (
            <div className="animate-pulse">
                <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-200 rounded" />
                    <div>
                        <div className="h-3 w-32 bg-gray-200 rounded mb-1" />
                        <div className="h-2 w-24 bg-gray-100 rounded" />
                    </div>
                </div>
            </div>
        );
    }

    if (!contractor) {
        return (
            <div className="text-xs text-gray-500 italic">
                Contratista no encontrado
            </div>
        );
    }

    // Construir el nombre completo
    const fullName = [
        contractor.firstName,
        contractor.middleName,
        contractor.lastName,
        contractor.secondLastName,
    ]
        .filter(Boolean)
        .join(" ");

    // Obtener iniciales para el avatar
    const initials = contractor.name
        .split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);

    return (
        <div className="flex items-start gap-2">
            {/* Avatar compacto */}
            <div className="flex-shrink-0">
                <div className="w-7 h-7 bg-gray-800 text-white rounded flex items-center justify-center text-[10px] font-semibold leading-none">
                    {initials || <UserIcon className="w-3.5 h-3.5" />}
                </div>
            </div>

            {/* Información del contratista */}
            <div className="flex-1 min-w-0">
                <p className="text-xs font-semibold text-gray-900 break-words leading-tight mb-0.5">
                    {contractor.name}
                </p>
                {fullName && fullName !== contractor.name && (
                    <p className="text-[10px] text-gray-600 break-words leading-tight">
                        {fullName}
                    </p>
                )}
                {(contractor.email || contractor.phone) && (
                    <p className="text-[10px] text-gray-500 break-words leading-tight mt-0.5">
                        {contractor.phone && (
                            <span className="inline-block">
                                {contractor.phone}
                            </span>
                        )}
                        {contractor.email && contractor.phone && (
                            <span className="inline-block mx-1">•</span>
                        )}
                        {contractor.email && (
                            <span className="inline-block">
                                {contractor.email}
                            </span>
                        )}
                    </p>
                )}
            </div>
        </div>
    );
}
