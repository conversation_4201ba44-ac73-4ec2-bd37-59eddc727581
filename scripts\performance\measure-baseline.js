/**
 * Performance Baseline Script
 * Measures current performance metrics before optimization
 */

import { performance } from 'perf_hooks';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const BASELINE_FILE = path.join(__dirname, '../../memory-bank/performance-baseline.json');
const API_ENDPOINTS = [
  '/api/orders',
  '/api/customers',
  '/api/products',
  '/api/assignments',
  '/api/status'
];

async function measureEndpoint(url) {
  const measurements = [];
  
  // Take 5 measurements for accuracy
  for (let i = 0; i < 5; i++) {
    const start = performance.now();
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100));
      
      const duration = performance.now() - start;
      measurements.push(duration);
    } catch (error) {
      console.error(`Error measuring ${url}:`, error);
    }
    
    // Wait between measurements
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // Calculate statistics
  const sorted = measurements.sort((a, b) => a - b);
  return {
    endpoint: url,
    measurements: measurements.map(m => Math.round(m)),
    average: Math.round(measurements.reduce((a, b) => a + b, 0) / measurements.length),
    p95: Math.round(sorted[Math.floor(sorted.length * 0.95)])
  };
}

async function runBaseline() {
  console.log('📊 Measuring Performance Baseline...\n');
  
  const baseline = {
    timestamp: new Date().toISOString(),
    apiEndpoints: {},
    environment: {
      node: process.version,
      platform: process.platform,
      memory: process.memoryUsage()
    }
  };
  
  // Measure API endpoints
  for (const endpoint of API_ENDPOINTS) {
    console.log(`Measuring ${endpoint}...`);
    baseline.apiEndpoints[endpoint] = await measureEndpoint(endpoint);
  }
  
  // Save baseline
  await fs.mkdir(path.dirname(BASELINE_FILE), { recursive: true });
  await fs.writeFile(BASELINE_FILE, JSON.stringify(baseline, null, 2));
  
  // Display summary
  console.log('\n✅ Baseline Complete!\n');
  console.log('API Performance Summary:');
  console.log('========================');
  
  for (const [endpoint, metrics] of Object.entries(baseline.apiEndpoints)) {
    console.log(`${endpoint}:`);
    console.log(`  Average: ${metrics.average}ms`);
    console.log(`  P95: ${metrics.p95}ms`);
  }
  
  console.log('\nBaseline saved to:', BASELINE_FILE);
}

// Run the baseline measurement
runBaseline().catch(console.error);
