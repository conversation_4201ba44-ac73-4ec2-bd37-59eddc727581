"use client";

import { useState, useCallback, useRef } from "react";

export interface UndoRedoState<T> {
    current: T;
    canUndo: boolean;
    canRedo: boolean;
    undoDescription?: string;
    redoDescription?: string;
}

interface HistoryEntry<T> {
    state: T;
    description: string;
    timestamp: number;
}

interface UseUndoRedoOptions {
    maxHistorySize?: number;
    debounceMs?: number;
}

export function useUndoRedo<T>(
    initialState: T,
    options: UseUndoRedoOptions = {},
): {
    state: T;
    setState: (newState: T, description?: string) => void;
    undo: () => void;
    redo: () => void;
    canUndo: boolean;
    canRedo: boolean;
    undoDescription?: string;
    redoDescription?: string;
    clearHistory: () => void;
    historySize: number;
} {
    const { maxHistorySize = 10, debounceMs = 300 } = options;

    const [currentState, setCurrentState] = useState<T>(initialState);
    const [undoStack, setUndoStack] = useState<HistoryEntry<T>[]>([]);
    const [redoStack, setRedoStack] = useState<HistoryEntry<T>[]>([]);

    const debounceTimerRef = useRef<NodeJS.Timeout>();
    const lastSavedStateRef = useRef<string>("");

    // Guardar estado en el historial
    const saveToHistory = useCallback(
        (state: T, description: string = "Cambio") => {
            const stateString = JSON.stringify(state);

            // Evitar guardar el mismo estado múltiples veces
            if (stateString === lastSavedStateRef.current) {
                return;
            }
            lastSavedStateRef.current = stateString;

            const entry: HistoryEntry<T> = {
                state: { ...state },
                description,
                timestamp: Date.now(),
            };

            setUndoStack((prev) => {
                const newStack = [...prev, entry];

                // Limitar el tamaño del historial
                return newStack.slice(-maxHistorySize);
            });

            // Limpiar redo stack cuando se hace un cambio nuevo
            setRedoStack([]);
        },
        [maxHistorySize],
    );

    // Actualizar estado con debounce opcional
    const setState = useCallback(
        (newState: T, description?: string) => {
            setCurrentState(newState);

            if (debounceMs > 0 && description) {
                // Cancelar el timer anterior
                if (debounceTimerRef.current) {
                    clearTimeout(debounceTimerRef.current);
                }

                // Establecer nuevo timer
                debounceTimerRef.current = setTimeout(() => {
                    saveToHistory(currentState, description);
                }, debounceMs);
            } else if (description) {
                // Guardar inmediatamente si no hay debounce
                saveToHistory(currentState, description);
            }
        },
        [currentState, debounceMs, saveToHistory],
    );

    // Función deshacer
    const undo = useCallback(() => {
        if (undoStack.length === 0) return;

        const newUndoStack = [...undoStack];
        const previousEntry = newUndoStack.pop()!;

        // Guardar estado actual en redo stack
        const currentEntry: HistoryEntry<T> = {
            state: { ...currentState },
            description: "Estado actual",
            timestamp: Date.now(),
        };

        setRedoStack((prev) => [...prev, currentEntry]);
        setUndoStack(newUndoStack);
        setCurrentState(previousEntry.state);
        lastSavedStateRef.current = JSON.stringify(previousEntry.state);
    }, [undoStack, currentState]);

    // Función rehacer
    const redo = useCallback(() => {
        if (redoStack.length === 0) return;

        const newRedoStack = [...redoStack];
        const nextEntry = newRedoStack.pop()!;

        // Guardar estado actual en undo stack
        const currentEntry: HistoryEntry<T> = {
            state: { ...currentState },
            description: "Estado anterior",
            timestamp: Date.now(),
        };

        setUndoStack((prev) => [...prev, currentEntry]);
        setRedoStack(newRedoStack);
        setCurrentState(nextEntry.state);
        lastSavedStateRef.current = JSON.stringify(nextEntry.state);
    }, [redoStack, currentState]);

    // Limpiar historial
    const clearHistory = useCallback(() => {
        setUndoStack([]);
        setRedoStack([]);
        lastSavedStateRef.current = "";
    }, []);

    return {
        state: currentState,
        setState,
        undo,
        redo,
        canUndo: undoStack.length > 0,
        canRedo: redoStack.length > 0,
        undoDescription: undoStack[undoStack.length - 1]?.description,
        redoDescription: redoStack[redoStack.length - 1]?.description,
        clearHistory,
        historySize: undoStack.length + redoStack.length,
    };
}
