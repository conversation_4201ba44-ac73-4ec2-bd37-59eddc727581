"use client";

import type { ToastOptions } from "../utils/toast";

import React, { useState, useEffect } from "react";
import { Card, CardBody } from "@heroui/react";
import { motion, AnimatePresence } from "framer-motion";
import {
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    XMarkIcon,
} from "@heroicons/react/24/outline";

import { toastManager } from "../utils/toast";

const iconMap = {
    success: CheckCircleIcon,
    danger: XCircleIcon,
    warning: ExclamationTriangleIcon,
    primary: InformationCircleIcon,
    default: InformationCircleIcon,
};

const colorClasses = {
    success:
        "bg-success-50 text-success-700 dark:bg-success-900/20 dark:text-success-300",
    danger: "bg-danger-50 text-danger-700 dark:bg-danger-900/20 dark:text-danger-300",
    warning:
        "bg-warning-50 text-warning-700 dark:bg-warning-900/20 dark:text-warning-300",
    primary:
        "bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300",
    default: "bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300",
};

export function ToastContainer() {
    const [toasts, setToasts] = useState<Array<ToastOptions & { id: number }>>(
        [],
    );

    useEffect(() => {
        const unsubscribe = toastManager.subscribe(setToasts);

        return unsubscribe;
    }, []);

    return (
        <div className="fixed top-4 right-4 z-50 space-y-2">
            <AnimatePresence>
                {toasts.map((toast) => {
                    const Icon =
                        iconMap[
                            (toast.color as keyof typeof iconMap) || "default"
                        ];
                    const colorClass =
                        colorClasses[
                            (toast.color as keyof typeof colorClasses) ||
                                "default"
                        ];

                    return (
                        <motion.div
                            key={toast.id}
                            animate={{ opacity: 1, x: 0, scale: 1 }}
                            exit={{ opacity: 0, x: 50, scale: 0.95 }}
                            initial={{ opacity: 0, x: 50, scale: 0.95 }}
                            transition={{ duration: 0.2 }}
                        >
                            <Card
                                className={`min-w-[300px] shadow-lg ${colorClass}`}
                            >
                                <CardBody className="py-3 px-4">
                                    <div className="flex items-start gap-3">
                                        <Icon className="w-5 h-5 flex-shrink-0 mt-0.5" />
                                        <div className="flex-1">
                                            <p className="font-medium text-sm">
                                                {toast.title}
                                            </p>
                                            {toast.description && (
                                                <p className="text-xs mt-1 opacity-80">
                                                    {toast.description}
                                                </p>
                                            )}
                                        </div>
                                        <button
                                            className="flex-shrink-0 p-1 rounded hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
                                            onClick={() =>
                                                toastManager.removeToast(
                                                    toast.id,
                                                )
                                            }
                                        >
                                            <XMarkIcon className="w-4 h-4" />
                                        </button>
                                    </div>
                                </CardBody>
                            </Card>
                        </motion.div>
                    );
                })}
            </AnimatePresence>
        </div>
    );
}
