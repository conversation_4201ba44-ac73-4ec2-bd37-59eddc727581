# Subclient Order Management Feature

## Overview
This feature moves subclient selection from the packing creation process to the order management process, allowing orders to be associated with specific subclients at creation/editing time.

## Business Context
Currently, users can only select subclients when creating packings. This creates a limitation where orders cannot be pre-assigned to subclients, requiring the selection to be made later in the process. By moving subclient selection to orders, we enable:

- Better order organization and filtering by subclient
- Pre-assignment of orders to specific subclients
- Simplified packing creation process
- More logical workflow alignment

## Technical Summary
- Add `subCustomerId` field to Order model in database
- Integrate CustomerSelector component into order forms
- Remove subclient selection from packing wizard
- Update all related server actions and queries

## Status
- Created: 2025-01-04
- Status: Pending Implementation
- Estimated Effort: 6-8 hours