{"hooks": {"PostToolUse": [{"matcher": "Edit|MultiEdit|Write", "hooks": [{"type": "command", "command": "cd \"$CLAUDE_PROJECT_DIR\" && echo '🔧 Formateando código...' && npm run lint --silent >nul 2>&1 && echo '✅ Código formateado' || echo '⚠️  Advertencias de lint encontradas'", "timeout": 30}, {"type": "command", "command": "cd \"$CLAUDE_PROJECT_DIR\" && echo '📊 Verificando calidad del código...' && npm run lint:errors --silent >nul 2>&1 && echo '✅ Sin errores críticos' || echo '❌ Errores encontrados en el código'", "timeout": 20}, {"type": "command", "command": "cd \"$CLAUDE_PROJECT_DIR\" && echo '🔍 Verificando compilación TypeScript...' && npx tsc --noEmit >nul 2>&1 && echo '✅ Compilación exitosa' || echo '❌ Errores de TypeScript detectados'", "timeout": 30}]}]}}