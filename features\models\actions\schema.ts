import { z } from "zod";

// Esquema para validación de datos
export const modelSchema = z.object({
    code: z
        .string()
        .min(2, "El código debe tener al menos 2 caracteres")
        .max(20, "El código no puede exceder los 20 caracteres"),
    description: z
        .string()
        .min(2, "La descripción debe tener al menos 2 caracteres")
        .max(100, "La descripción no puede exceder los 100 caracteres"),
    basePrice: z
        .number()
        .min(0, "El precio base no puede ser negativo")
        .optional()
        .default(0),
});
