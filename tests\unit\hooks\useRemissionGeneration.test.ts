import { renderHook, act, waitFor } from '@testing-library/react'
import { useRemissionGeneration } from '@/hooks/useRemissionGeneration'
import { downloadRemissionPDF } from '@/lib/pdf/generator'
import { toast } from 'sonner'

jest.mock('@/lib/pdf/generator')
jest.mock('sonner')

describe('useRemissionGeneration Hook', () => {
  const mockRemissionData = {
    id: 'rem-123',
    folio: 'REM-2025-001',
    date: new Date(),
    contractor: {
      name: 'Test Contractor',
      rfc: 'TEST123456ABC',
      address: 'Test Address',
      phone: '555-1234',
    },
    items: [
      {
        id: '1',
        name: 'Item 1',
        quantity: 10,
        unitPrice: 100,
        subtotal: 1000,
      },
    ],
    subtotal: 1000,
    tax: 160,
    total: 1160,
    status: 'active' as const,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('debe inicializar con el estado correcto', () => {
    const { result } = renderHook(() => useRemissionGeneration())

    expect(result.current.state).toEqual({
      isGenerating: false,
      isDownloading: false,
      error: null,
      progress: 0,
      currentStep: 'idle',
      remissionData: null,
      pdfBlob: null,
    })
  })

  it('debe generar remisión exitosamente', async () => {
    const { result } = renderHook(() => useRemissionGeneration())

    await act(async () => {
      await result.current.generateRemission(mockRemissionData)
    })

    await waitFor(() => {
      expect(result.current.state.currentStep).toBe('complete')
      expect(result.current.state.progress).toBe(100)
      expect(result.current.state.remissionData).toEqual(mockRemissionData)
      expect(toast.success).toHaveBeenCalledWith(
        'Remisión generada exitosamente',
        expect.objectContaining({
          description: 'Folio: REM-2025-001',
        })
      )
    })
  })

  it('debe manejar errores durante la generación', async () => {
    const { result } = renderHook(() => useRemissionGeneration())
    const error = new Error('Error de generación')

    // Simular error modificando el mock temporalmente
    jest.spyOn(global, 'setTimeout').mockImplementationOnce(() => {
      throw error
    })

    await act(async () => {
      try {
        await result.current.generateRemission(mockRemissionData)
      } catch (e) {
        // Expected error
      }
    })

    expect(result.current.state.error).toBeTruthy()
    expect(result.current.state.isGenerating).toBe(false)
  })

  it('debe descargar PDF exitosamente', async () => {
    const { result } = renderHook(() => useRemissionGeneration())

    // Primero generar la remisión
    await act(async () => {
      await result.current.generateRemission(mockRemissionData)
    })

    // Luego descargar
    await act(async () => {
      await result.current.downloadPDF()
    })

    expect(downloadRemissionPDF).toHaveBeenCalledWith(mockRemissionData)
    expect(toast.success).toHaveBeenCalledWith(
      'PDF descargado exitosamente',
      expect.any(Object)
    )
  })

  it('debe resetear el estado correctamente', async () => {
    const { result } = renderHook(() => useRemissionGeneration())

    // Generar remisión primero
    await act(async () => {
      await result.current.generateRemission(mockRemissionData)
    })

    // Reset
    act(() => {
      result.current.reset()
    })

    expect(result.current.state).toEqual({
      isGenerating: false,
      isDownloading: false,
      error: null,
      progress: 0,
      currentStep: 'idle',
      remissionData: null,
      pdfBlob: null,
    })
  })

  it('debe actualizar el progreso correctamente', () => {
    const { result } = renderHook(() => useRemissionGeneration())

    act(() => {
      result.current.setProgress(50)
    })

    expect(result.current.state.progress).toBe(50)
  })
})
