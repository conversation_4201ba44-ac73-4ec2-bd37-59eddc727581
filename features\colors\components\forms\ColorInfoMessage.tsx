"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    InformationCircleIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
} from "@heroicons/react/24/outline";

export type MessageType = "info" | "success" | "warning" | "error";

interface ColorInfoMessageProps {
    type: MessageType;
    title: string;
    message: string;
    show: boolean;
}

export function ColorInfoMessage({
    type,
    title,
    message,
    show,
}: ColorInfoMessageProps) {
    const config = {
        info: {
            bgColor: "bg-blue-50 dark:bg-blue-900/20",
            borderColor: "border-blue-200 dark:border-blue-800",
            textColor: "text-blue-800 dark:text-blue-200",
            iconColor: "text-blue-600 dark:text-blue-400",
            Icon: InformationCircleIcon,
        },
        success: {
            bgColor: "bg-green-50 dark:bg-green-900/20",
            borderColor: "border-green-200 dark:border-green-800",
            textColor: "text-green-800 dark:text-green-200",
            iconColor: "text-green-600 dark:text-green-400",
            Icon: CheckCircleIcon,
        },
        warning: {
            bgColor: "bg-amber-50 dark:bg-amber-900/20",
            borderColor: "border-amber-200 dark:border-amber-800",
            textColor: "text-amber-800 dark:text-amber-200",
            iconColor: "text-amber-600 dark:text-amber-400",
            Icon: ExclamationTriangleIcon,
        },
        error: {
            bgColor: "bg-red-50 dark:bg-red-900/20",
            borderColor: "border-red-200 dark:border-red-800",
            textColor: "text-red-800 dark:text-red-200",
            iconColor: "text-red-600 dark:text-red-400",
            Icon: XCircleIcon,
        },
    };

    const { bgColor, borderColor, textColor, iconColor, Icon } = config[type];

    return (
        <AnimatePresence>
            {show && (
                <motion.div
                    animate={{ opacity: 1, y: 0, height: "auto" }}
                    className={`
                        ${bgColor} ${borderColor} ${textColor}
                        border rounded-lg p-4 flex gap-3
                    `}
                    exit={{ opacity: 0, y: -10, height: 0 }}
                    initial={{ opacity: 0, y: -10, height: 0 }}
                    transition={{ duration: 0.2 }}
                >
                    <Icon
                        className={`${iconColor} w-5 h-5 flex-shrink-0 mt-0.5`}
                    />
                    <div className="flex-1">
                        <h4 className="font-medium mb-0.5">{title}</h4>
                        <p className="text-sm opacity-90">{message}</p>
                    </div>
                </motion.div>
            )}
        </AnimatePresence>
    );
}
