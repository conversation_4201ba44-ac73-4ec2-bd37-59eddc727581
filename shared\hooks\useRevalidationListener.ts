"use client";

import { useState, useEffect, useCallback } from "react";
import { mutate } from "swr";

// Tipo para opciones personalizables del hook
interface RevalidationOptions {
    throttleTime?: number; // Tiempo en ms para limitar frecuencia de revalidaciones
    revalidateAllKeys?: boolean; // Si debe revalidar todas las claves o solo las específicas
    enabled?: boolean; // Si el listener está habilitado
}

/**
 * Hook para escuchar eventos de revalidación para una entidad específica
 * @param entityName Nombre de la entidad (ej: "sizes", "colors", "models")
 * @param onRevalidate Callback opcional a ejecutar cuando se recibe un evento
 * @param options Opciones adicionales para personalizar el comportamiento
 * @returns Estado de revalidación
 */

export function useRevalidationListener(
    entityName?: string,
    onRevalidate?: (id?: string) => void,
    options?: RevalidationOptions,
): { isRevalidating: boolean } {
    const [isRevalidating, setIsRevalidating] = useState(false);

    // Valores predeterminados para opciones
    const {
        throttleTime = 500,
        revalidateAllKeys = true,
        enabled = true,
    } = options || {};

    // Handler para eventos de revalidación
    const handleEntityChange = useCallback(
        (event: Event) => {
            const customEvent = event as CustomEvent<{
                tag: string;
                id?: string;
            }>;
            const { tag, id } = customEvent.detail || {};

            // Si el listener está deshabilitado, no hacer nada
            if (!enabled) return;

            // Si no se proporciona entityName, escuchar todos los eventos
            // o solo revalidar si el evento es para esta entidad
            if (!entityName || tag === entityName) {
                if (process.env.NODE_ENV !== "production") {
                    // // REMOVED: console.log(
                    //     `🔄 Evento de cambio en ${tag} detectado, ID: ${id || "todos"}`,
                    // );
                }

                // Activar estado de revalidación
                setIsRevalidating(true);

                // Revalidar con opciones
                mutate(
                    (key) =>
                        // Si revalidateAllKeys es false, solo revalidar las claves exactas
                        revalidateAllKeys
                            ? !entityName ||
                              (Array.isArray(key) && key[0] === entityName)
                            : Array.isArray(key) &&
                              key[0] === entityName &&
                              (id ? key.includes(id) : true),
                    undefined,
                    {
                        revalidate: true, // Forzar revalidación
                        populateCache: true, // Actualizar caché
                        rollbackOnError: true, // Volver a datos anteriores en caso de error
                    },
                );

                // Si hay un callback personalizado, ejecutarlo
                if (onRevalidate) {
                    onRevalidate(id);
                }

                // Desactivar estado de revalidación después del tiempo configurado
                setTimeout(() => {
                    setIsRevalidating(false);
                }, throttleTime);
            }
        },
        [entityName, onRevalidate, enabled, revalidateAllKeys, throttleTime],
    );

    // Efecto para registrar y limpiar el listener
    useEffect(() => {
        if (typeof window === "undefined") return;

        // Añadir listener
        window.addEventListener(
            "custom-revalidation",
            handleEntityChange as EventListener,
        );

        // Registrar la escucha (para depuración)
        if (process.env.NODE_ENV !== "production") {
            // // REMOVED: console.log(
            //     `📡 Escuchando eventos de revalidación para: ${entityName || "todos"}`,
            // );
        }

        // Limpiar listener al desmontar
        return () => {
            window.removeEventListener(
                "custom-revalidation",
                handleEntityChange as EventListener,
            );

            if (process.env.NODE_ENV !== "production") {
                // // REMOVED: console.log(
                //     `📴 Dejando de escuchar eventos para: ${entityName || "todos"}`,
                // );
            }
        };
    }, [entityName, handleEntityChange]);

    return { isRevalidating };
}
