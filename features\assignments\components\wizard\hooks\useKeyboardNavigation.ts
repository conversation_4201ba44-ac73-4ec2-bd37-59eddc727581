"use client";

import { useEffect, useCallback } from "react";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";

interface UseKeyboardNavigationOptions {
    onCancel?: () => void;
    enabled?: boolean;
}

export function useKeyboardNavigation(
    options: UseKeyboardNavigationOptions = {},
) {
    const { enabled = true, onCancel } = options;
    const { nextStep, prevStep, canGoNext, canGoBack, undo, redo, state } =
        useWizard();

    const handleKeyDown = useCallback(
        (event: KeyboardEvent) => {
            // No procesar si está deshabilitado
            if (!enabled) return;

            // No procesar si el foco está en un input/textarea
            const activeElement = document.activeElement;
            const isInputFocused =
                activeElement?.tagName === "INPUT" ||
                activeElement?.tagName === "TEXTAREA" ||
                activeElement?.tagName === "SELECT";

            // Ctrl/Cmd + keys para navegación y deshacer/rehacer
            if (event.ctrlKey || event.metaKey) {
                switch (event.key) {
                    case "ArrowRight":
                        if (canGoNext() && !isInputFocused) {
                            event.preventDefault();
                            nextStep();
                        }
                        break;

                    case "ArrowLeft":
                        if (canGoBack() && !isInputFocused) {
                            event.preventDefault();
                            prevStep();
                        }
                        break;

                    case "z":
                    case "Z":
                        if (state.canUndo && !isInputFocused) {
                            event.preventDefault();
                            undo();
                        }
                        break;

                    case "y":
                    case "Y":
                        if (state.canRedo && !isInputFocused) {
                            event.preventDefault();
                            redo();
                        }
                        break;
                }
            }

            // Escape para cancelar (solo si no hay input focused)
            if (event.key === "Escape" && !isInputFocused && onCancel) {
                event.preventDefault();

                // Mostrar confirmación antes de cancelar
                const confirmed = window.confirm(
                    "¿Estás seguro de que deseas cancelar? Se perderán todos los cambios no guardados.",
                );

                if (confirmed) {
                    onCancel();
                }
            }

            // Enter para confirmar en ciertos contextos
            if (event.key === "Enter" && !isInputFocused) {
                // Si hay un botón primario visible, hacer click en él
                const primaryButton = document.querySelector(
                    'button[color="primary"]:not([disabled])',
                ) as HTMLButtonElement;

                if (primaryButton && primaryButton.offsetParent !== null) {
                    event.preventDefault();
                    primaryButton.click();
                }
            }
        },
        [
            enabled,
            canGoNext,
            canGoBack,
            nextStep,
            prevStep,
            onCancel,
            undo,
            redo,
            state,
        ],
    );

    useEffect(() => {
        if (enabled) {
            window.addEventListener("keydown", handleKeyDown);

            return () => window.removeEventListener("keydown", handleKeyDown);
        }
    }, [enabled, handleKeyDown]);

    // Retornar hints de atajos para mostrar en UI
    return {
        shortcuts: [
            { keys: ["Ctrl", "→"], description: "Siguiente paso" },
            { keys: ["Ctrl", "←"], description: "Paso anterior" },
            { keys: ["Ctrl", "Z"], description: "Deshacer" },
            { keys: ["Ctrl", "Y"], description: "Rehacer" },
            { keys: ["Tab"], description: "Navegar campos" },
            { keys: ["Enter"], description: "Confirmar" },
            { keys: ["Esc"], description: "Cancelar" },
        ],
    };
}
