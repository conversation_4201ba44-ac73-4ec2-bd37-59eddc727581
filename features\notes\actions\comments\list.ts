"use server";

import type {
    CommentFilters,
    CommentWithAuthor,
    PaginatedComments,
} from "../../types/comment";

import { Prisma } from "@prisma/client";

import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/utils/prisma-helpers";

/**
 * Función auxiliar para obtener comentarios con respuestas anidadas
 */
async function getCommentsWithReplies(
    commentIds: string[],
): Promise<CommentWithAuthor[]> {
    const comments = await db.noteComment.findMany({
        where: {
            id: { in: commentIds },
        },
        include: {
            author: {
                select: {
                    id: true,
                    name: true,
                    email: true,
                    image: true,
                },
            },
            replies: {
                include: {
                    author: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                            image: true,
                        },
                    },
                    _count: {
                        select: {
                            replies: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: "asc",
                },
            },
        },
    });

    // Recursivamente obtener todas las respuestas anidadas
    const processReplies = async (
        replies: any[],
    ): Promise<CommentWithAuthor[]> => {
        if (replies.length === 0) return [];

        const replyIds = replies.map((r) => r.id);
        const deepReplies = await getCommentsWithReplies(replyIds);

        return replies.map((reply) => ({
            ...reply,
            replies: deepReplies.filter((dr) => dr.parentId === reply.id),
        }));
    };

    // Procesar cada comentario
    const processedComments = await Promise.all(
        comments.map(async (comment) => ({
            ...comment,
            replies: await processReplies(comment.replies),
        })),
    );

    return processedComments as CommentWithAuthor[];
}

/**
 * Obtiene comentarios de una nota con paginación
 */
export async function getComments(
    filters: CommentFilters,
): Promise<{ success: boolean; data?: PaginatedComments; error?: string }> {
    try {
        const {
            noteId,
            page = 1,
            pageSize = 10,
            includeReplies = true,
            sortOrder = "desc",
        } = filters;

        // Verificar que la nota existe
        const noteExists = await db.note.findUnique({
            where: { id: noteId },
            select: { id: true },
        });

        if (!noteExists) {
            return { success: false, error: "La nota no existe" };
        }
        // Construir where clause - solo comentarios de nivel superior
        const where: Prisma.NoteCommentWhereInput = {
            noteId,
            parentId: null, // Solo comentarios principales
        };

        // Obtener el conteo total
        const totalCount = await db.noteComment.count({ where });

        // Calcular paginación
        const skip = (page - 1) * pageSize;
        const totalPages = Math.ceil(totalCount / pageSize);

        // Obtener comentarios principales
        const topLevelComments = await db.noteComment.findMany({
            where,
            orderBy: {
                createdAt: sortOrder,
            },
            skip,
            take: pageSize,
            include: {
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
                _count: {
                    select: {
                        replies: true,
                    },
                },
            },
        });

        // Si se solicitan respuestas, obtenerlas recursivamente
        let commentsWithReplies = topLevelComments;

        if (includeReplies && topLevelComments.length > 0) {
            const commentIds = topLevelComments.map((c) => c.id);

            commentsWithReplies = (await getCommentsWithReplies(
                commentIds,
            )) as any;
        }

        return {
            success: true,
            data: {
                comments: commentsWithReplies as CommentWithAuthor[],
                totalCount,
                hasMore: page < totalPages,
                page,
                pageSize,
                totalPages,
            },
        };
    } catch (error) {
        return handlePrismaError(error, "Error al obtener los comentarios");
    }
}
