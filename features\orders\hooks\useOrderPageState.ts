"use client";

import { useReducer, useCallback } from "react";

import { Order } from "@/features/orders/types/orders";
import { SortOption } from "@/shared/utils/sortHelpers";

// State interface
interface OrderPageState {
    selectedOrder: Order | null;
    isDetailModalOpen: boolean;
    isLoadingOrder: boolean;
    activeTab: string;
    viewMode: "grid" | "list";
    searchValue: string;
    filterValues: Record<string, any>;
    selectedContractors: string[];
    currentSort: SortOption;
    page: number;
}

// Action types
type OrderPageAction =
    | { type: "SET_SELECTED_ORDER"; payload: Order | null }
    | { type: "SET_DETAIL_MODAL_OPEN"; payload: boolean }
    | { type: "SET_LOADING_ORDER"; payload: boolean }
    | { type: "SET_ACTIVE_TAB"; payload: string }
    | { type: "SET_VIEW_MODE"; payload: "grid" | "list" }
    | { type: "SET_SEARCH_VALUE"; payload: string }
    | { type: "SET_FILTER_VALUES"; payload: Record<string, any> }
    | { type: "SET_SELECTED_CONTRACTORS"; payload: string[] }
    | { type: "SET_CURRENT_SORT"; payload: SortOption }
    | { type: "SET_PAGE"; payload: number }
    | { type: "RESET_FILTERS" }
    | { type: "OPEN_ORDER_DETAIL"; payload: Order }
    | { type: "CLOSE_ORDER_DETAIL" };

// Initial state
const initialState: OrderPageState = {
    selectedOrder: null,
    isDetailModalOpen: false,
    isLoadingOrder: false,
    activeTab: "orders",
    viewMode: "list",
    searchValue: "",
    filterValues: {},
    selectedContractors: [],
    currentSort: {
        key: "createdAt-desc",
        label: "Más reciente",
        field: "createdAt",
        direction: "desc",
    },
    page: 1,
};

// Reducer
function orderPageReducer(
    state: OrderPageState,
    action: OrderPageAction,
): OrderPageState {
    switch (action.type) {
        case "SET_SELECTED_ORDER":
            return { ...state, selectedOrder: action.payload };

        case "SET_DETAIL_MODAL_OPEN":
            return { ...state, isDetailModalOpen: action.payload };

        case "SET_LOADING_ORDER":
            return { ...state, isLoadingOrder: action.payload };

        case "SET_ACTIVE_TAB":
            return { ...state, activeTab: action.payload };

        case "SET_VIEW_MODE":
            return { ...state, viewMode: action.payload };

        case "SET_SEARCH_VALUE":
            return { ...state, searchValue: action.payload, page: 1 };

        case "SET_FILTER_VALUES":
            return { ...state, filterValues: action.payload, page: 1 };

        case "SET_SELECTED_CONTRACTORS":
            return { ...state, selectedContractors: action.payload, page: 1 };

        case "SET_CURRENT_SORT":
            return { ...state, currentSort: action.payload, page: 1 };

        case "SET_PAGE":
            return { ...state, page: action.payload };

        case "RESET_FILTERS":
            return {
                ...state,
                searchValue: "",
                filterValues: {},
                selectedContractors: [],
                page: 1,
            };

        case "OPEN_ORDER_DETAIL":
            return {
                ...state,
                selectedOrder: action.payload,
                isDetailModalOpen: true,
                isLoadingOrder: true,
            };

        case "CLOSE_ORDER_DETAIL":
            return {
                ...state,
                isDetailModalOpen: false,
                isLoadingOrder: false,
                // selectedOrder se limpia después con un delay
            };

        default:
            return state;
    }
}

// Hook
export function useOrderPageState() {
    const [state, dispatch] = useReducer(orderPageReducer, initialState);

    // Action creators
    const actions = {
        setSelectedOrder: useCallback(
            (order: Order | null) =>
                dispatch({ type: "SET_SELECTED_ORDER", payload: order }),
            [],
        ),

        setDetailModalOpen: useCallback(
            (open: boolean) =>
                dispatch({ type: "SET_DETAIL_MODAL_OPEN", payload: open }),
            [],
        ),

        setLoadingOrder: useCallback(
            (loading: boolean) =>
                dispatch({ type: "SET_LOADING_ORDER", payload: loading }),
            [],
        ),

        setActiveTab: useCallback(
            (tab: string) => dispatch({ type: "SET_ACTIVE_TAB", payload: tab }),
            [],
        ),

        setViewMode: useCallback(
            (mode: "grid" | "list") =>
                dispatch({ type: "SET_VIEW_MODE", payload: mode }),
            [],
        ),

        setSearchValue: useCallback(
            (value: string) =>
                dispatch({ type: "SET_SEARCH_VALUE", payload: value }),
            [],
        ),

        setFilterValues: useCallback(
            (values: Record<string, any>) =>
                dispatch({ type: "SET_FILTER_VALUES", payload: values }),
            [],
        ),

        setSelectedContractors: useCallback(
            (contractors: string[]) =>
                dispatch({
                    type: "SET_SELECTED_CONTRACTORS",
                    payload: contractors,
                }),
            [],
        ),

        setCurrentSort: useCallback(
            (sort: SortOption) =>
                dispatch({ type: "SET_CURRENT_SORT", payload: sort }),
            [],
        ),

        setPage: useCallback(
            (page: number) => dispatch({ type: "SET_PAGE", payload: page }),
            [],
        ),

        resetFilters: useCallback(
            () => dispatch({ type: "RESET_FILTERS" }),
            [],
        ),

        openOrderDetail: useCallback(
            (order: Order) =>
                dispatch({ type: "OPEN_ORDER_DETAIL", payload: order }),
            [],
        ),

        closeOrderDetail: useCallback(
            () => dispatch({ type: "CLOSE_ORDER_DETAIL" }),
            [],
        ),
    };

    return { state, actions };
}
