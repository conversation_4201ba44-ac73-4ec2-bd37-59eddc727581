#!/bin/bash
# Script para crear estructura completa en todas las features

features=("orders" "customers" "contractors" "assignments" "garments" "inventory" "auth" "notes" "reports" "colors" "models")
folders=("components" "hooks" "actions" "schemas" "types" "utils")

for feature in "${features[@]}"; do
  echo "Verificando feature: $feature"
  for folder in "${folders[@]}"; do
    path="C:/Users/<USER>/Documents/git/Next Js/lohari/features/$feature/$folder"
    if [ ! -d "$path" ]; then
      echo "Creando: $path"
      mkdir -p "$path"
    fi
  done
done