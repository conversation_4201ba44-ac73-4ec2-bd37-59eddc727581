"use server";

import { prisma } from "@/shared/lib/prisma";
import {
    ActionResponse,
    createErrorResponse,
    getCurrentUser,
} from "@/shared/utils/actions-utils";

interface ListNotesOptions {
    search?: string;
    statusId?: string;
    importanceId?: string;
    authorId?: string;
    orderId?: string;
    orderBy?: string;
    order?: "asc" | "desc";
    page?: number;
    perPage?: number;
}

/**
 * Server Action para obtener todas las notas
 */
export async function getNotes(): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        // Obtener todas las notas con sus relaciones
        const notes = await prisma.note.findMany({
            include: {
                order: true,
                status: true,
                importance: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        return {
            success: true,
            data: notes,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Server Action para listar notas con filtros y paginación
 */
export async function listNotes(
    options: ListNotesOptions = {},
): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        const {
            search,
            statusId,
            importanceId,
            authorId,
            orderId,
            orderBy = "createdAt",
            order = "desc",
            page = 1,
            perPage = 12,
        } = options;

        // Construir where clause
        const where: any = {};

        if (search) {
            where.OR = [
                { content: { contains: search, mode: "insensitive" } },
                { order: { code: { contains: search, mode: "insensitive" } } },
                {
                    order: {
                        customer: {
                            name: { contains: search, mode: "insensitive" },
                        },
                    },
                },
                { author: { name: { contains: search, mode: "insensitive" } } },
            ];
        }

        if (statusId && statusId !== "all") {
            where.statusId = statusId;
        }

        if (importanceId && importanceId !== "all") {
            where.importanceId = importanceId;
        }

        if (authorId && authorId !== "all") {
            where.authorId = authorId;
        }

        if (orderId) {
            where.orderId = orderId;
        }

        // Obtener total para paginación
        const total = await prisma.note.count({ where });

        // Obtener notas con paginación
        const notes = await prisma.note.findMany({
            where,
            include: {
                order: {
                    include: {
                        customer: true,
                        parts: true,
                    },
                },
                status: true,
                importance: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
                _count: {
                    select: {
                        likes: true,
                        comments: true,
                    },
                },
                likes: {
                    where: {
                        userId: user.id,
                    },
                },
            },
            orderBy: { [orderBy]: order },
            skip: (page - 1) * perPage,
            take: perPage,
        });

        return {
            success: true,
            data: {
                notes,
                pagination: {
                    total,
                    currentPage: page,
                    lastPage: Math.ceil(total / perPage),
                    perPage,
                },
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Server Action para obtener una nota por su ID
 */
export async function getNoteById(id: string): Promise<ActionResponse<any>> {
    try {
        // Verificar autenticación
        const user = await getCurrentUser();

        if (!user) {
            return {
                success: false,
                error: "No estás autenticado",
            };
        }

        // Validar ID
        if (!id) {
            return {
                success: false,
                error: "ID de nota requerido",
            };
        }

        // Obtener la nota con sus relaciones
        const note = await prisma.note.findUnique({
            where: { id },
            include: {
                order: {
                    include: {
                        customer: true,
                        parts: true,
                    },
                },
                status: true,
                importance: true,
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
                _count: {
                    select: {
                        likes: true,
                        comments: true,
                    },
                },
                likes: {
                    where: {
                        userId: user.id,
                    },
                },
                comments: {
                    include: {
                        author: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                image: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
        });

        if (!note) {
            return {
                success: false,
                error: "Nota no encontrada",
            };
        }

        return {
            success: true,
            data: note,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
