"use client";

import React, { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    useDisclosure,
} from "@heroui/react";
import { CheckCircleIcon, XMarkIcon } from "@heroicons/react/24/outline";

import { SubcustomerForm } from "../forms/SubcustomerForm";
import { createSubCustomer, updateSubcustomer } from "../../actions";

interface SubcustomerData {
    id?: string;
    name: string;
    displayName?: string | null;
}

interface SubcustomerModalProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    parentId: string;
    editData?: SubcustomerData;
    onSuccess?: () => void;
}

export function SubcustomerModal({
    isOpen,
    onOpenChange,
    parentId,
    editData,
    onSuccess,
}: SubcustomerModalProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errors, setErrors] = useState<{
        name?: string;
        displayName?: string;
    }>({});
    const isEdit = !!editData?.id;

    const handleSubmit = async (formData: {
        name: string;
        displayName?: string;
    }) => {
        setIsSubmitting(true);
        setErrors({});

        try {
            let result;

            if (isEdit && editData?.id) {
                result = await updateSubcustomer({
                    id: editData.id,
                    name: formData.name,
                    displayName: formData.displayName,
                });
            } else {
                result = await createSubCustomer({
                    name: formData.name,
                    parentId,
                    displayName: formData.displayName,
                });
            }

            if (result.success) {
                onSuccess?.();
                onOpenChange(false);
                setErrors({});
            } else {
                // Mapear errores específicos
                const errorMessage = result.error || "Error desconocido";

                if (errorMessage.includes("nombre")) {
                    setErrors({ name: errorMessage });
                } else {
                    setErrors({ name: errorMessage });
                }
            }
        } catch (error) {
            console.error("Error al procesar subcliente:", error);
            setErrors({ name: "Error inesperado al procesar la solicitud" });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleClose = () => {
        if (!isSubmitting) {
            onOpenChange(false);
            setErrors({});
        }
    };

    return (
        <Modal
            isDismissable={!isSubmitting}
            isKeyboardDismissDisabled={isSubmitting}
            isOpen={isOpen}
            size="2xl"
            onOpenChange={onOpenChange}
        >
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">
                            <h3 className="text-lg font-semibold">
                                {isEdit ? "Editar" : "Crear"} Subcliente
                            </h3>
                            <p className="text-sm text-gray-600">
                                {isEdit
                                    ? "Modifica la información del subcliente"
                                    : "Agrega un nuevo subcliente a este cliente principal"}
                            </p>
                        </ModalHeader>
                        <ModalBody>
                            <SubcustomerForm
                                errors={errors}
                                initialData={editData}
                                isSubmitting={isSubmitting}
                                onSubmit={handleSubmit}
                            />
                        </ModalBody>
                        <ModalFooter>
                            <Button
                                color="danger"
                                isDisabled={isSubmitting}
                                startContent={<XMarkIcon className="w-4 h-4" />}
                                variant="light"
                                onPress={handleClose}
                            >
                                Cancelar
                            </Button>
                            <Button
                                color="primary"
                                isLoading={isSubmitting}
                                startContent={
                                    !isSubmitting ? (
                                        <CheckCircleIcon className="w-4 h-4" />
                                    ) : undefined
                                }
                                onPress={() => {
                                    // Trigger form submission via a custom event
                                    const form = document.querySelector("form");

                                    if (form) {
                                        form.requestSubmit();
                                    }
                                }}
                            >
                                {isEdit ? "Actualizar" : "Crear"}
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}

// Hook para facilitar el uso del modal
export function useSubcustomerModal() {
    const { isOpen, onOpen, onOpenChange } = useDisclosure();
    const [editData, setEditData] = useState<SubcustomerData | undefined>();

    const openCreate = () => {
        setEditData(undefined);
        onOpen();
    };

    const openEdit = (data: SubcustomerData) => {
        setEditData(data);
        onOpen();
    };

    return {
        isOpen,
        onOpenChange,
        editData,
        openCreate,
        openEdit,
    };
}
