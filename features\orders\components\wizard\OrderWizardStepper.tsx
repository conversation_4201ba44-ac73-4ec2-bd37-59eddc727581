"use client";

import React from "react";
import { CheckCircleIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";

export interface WizardStepInfo {
    id: string;
    title: string;
    subtitle?: string;
    icon: React.ReactNode;
}

interface OrderWizardStepperProps {
    steps: WizardStepInfo[];
    currentStep: number;
    completedSteps: Set<string>;
    onStepClick?: (index: number) => void;
    allowNavigation?: boolean;
}

export function OrderWizardStepper({
    steps,
    currentStep,
    completedSteps,
    onStepClick,
    allowNavigation = false,
}: OrderWizardStepperProps) {
    const canNavigateToStep = (index: number) => {
        if (!allowNavigation || !onStepClick) return false;
        if (index <= currentStep) return true;
        if (index === currentStep + 1)
            return completedSteps.has(steps[currentStep].id);

        return false;
    };

    return (
        <div className="w-full">
            {/* Desktop Stepper */}
            <div className="hidden lg:flex items-center justify-between">
                {steps.map((step, index) => {
                    const isActive = index === currentStep;
                    const isCompleted =
                        completedSteps.has(step.id) || index < currentStep;
                    const canNavigate = canNavigateToStep(index);

                    return (
                        <React.Fragment key={step.id}>
                            <div className="flex-1 flex items-center">
                                <button
                                    className={`
                    relative flex items-center gap-3 px-4 py-3 rounded-xl transition-all w-full
                    ${isActive ? "bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20" : ""}
                    ${canNavigate ? "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800" : "cursor-default"}
                  `}
                                    disabled={!canNavigate}
                                    type="button"
                                    onClick={() =>
                                        canNavigate && onStepClick?.(index)
                                    }
                                >
                                    {isActive && (
                                        <motion.div
                                            className="absolute inset-0 border-2 border-primary rounded-xl"
                                            layoutId="activeStepIndicator"
                                            transition={{
                                                type: "spring",
                                                stiffness: 300,
                                                damping: 30,
                                            }}
                                        />
                                    )}

                                    <div
                                        className={`
                    relative z-10 w-12 h-12 rounded-full flex items-center justify-center transition-all
                    ${isActive ? "bg-primary text-white shadow-lg scale-110" : ""}
                    ${isCompleted && !isActive ? "bg-success text-white" : ""}
                    ${!isActive && !isCompleted ? "bg-gray-200 dark:bg-gray-700 text-gray-500" : ""}
                  `}
                                    >
                                        {isCompleted && !isActive ? (
                                            <CheckCircleIcon className="w-6 h-6" />
                                        ) : (
                                            <span className="text-2xl">
                                                {step.icon}
                                            </span>
                                        )}
                                    </div>

                                    <div className="relative z-10 text-left flex-1">
                                        <div
                                            className={`font-semibold transition-colors ${
                                                isActive
                                                    ? "text-primary"
                                                    : isCompleted
                                                      ? "text-gray-900 dark:text-white"
                                                      : "text-gray-500 dark:text-gray-400"
                                            }`}
                                        >
                                            {step.title}
                                        </div>
                                        {step.subtitle && (
                                            <div
                                                className={`text-xs transition-colors ${
                                                    isActive
                                                        ? "text-primary-600 dark:text-primary-400"
                                                        : "text-gray-500 dark:text-gray-400"
                                                }`}
                                            >
                                                {step.subtitle}
                                            </div>
                                        )}
                                    </div>
                                </button>

                                {index < steps.length - 1 && (
                                    <div className="flex-shrink-0 w-8 mx-2">
                                        <div
                                            className={`h-0.5 transition-colors ${
                                                index < currentStep
                                                    ? "bg-success"
                                                    : "bg-gray-300 dark:bg-gray-700"
                                            }`}
                                        />
                                    </div>
                                )}
                            </div>
                        </React.Fragment>
                    );
                })}
            </div>

            {/* Mobile Stepper */}
            <div className="flex lg:hidden flex-col gap-3">
                {/* Current Step Indicator */}
                <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Paso {currentStep + 1} de {steps.length}
                    </span>
                    <div className="flex items-center gap-2">
                        {steps.map((_, index) => (
                            <button
                                key={index}
                                className={`
                  w-2 h-2 rounded-full transition-all
                  ${index === currentStep ? "w-8 bg-primary" : ""}
                  ${index < currentStep ? "bg-success" : ""}
                  ${index > currentStep ? "bg-gray-300 dark:bg-gray-700" : ""}
                `}
                                disabled={!canNavigateToStep(index)}
                                type="button"
                                onClick={() =>
                                    canNavigateToStep(index) &&
                                    onStepClick?.(index)
                                }
                            />
                        ))}
                    </div>
                </div>

                {/* Current Step Info */}
                <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 rounded-xl p-4">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center">
                            <span className="text-xl">
                                {steps[currentStep].icon}
                            </span>
                        </div>
                        <div>
                            <div className="font-semibold text-primary">
                                {steps[currentStep].title}
                            </div>
                            {steps[currentStep].subtitle && (
                                <div className="text-xs text-primary-600 dark:text-primary-400">
                                    {steps[currentStep].subtitle}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
