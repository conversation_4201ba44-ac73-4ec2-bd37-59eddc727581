"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardBody, Button, Avatar, Badge, Tooltip } from "@heroui/react";
import {
    UserGroupIcon,
    ShoppingBagIcon,
    DocumentTextIcon,
    ChevronRightIcon,
    UserPlusIcon,
} from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { es } from "date-fns/locale";

interface SubCustomer {
    id: string;
    name: string;
    createdAt: string | Date;
    _count?: {
        orders: number;
        packings: number;
        subCustomers: number;
    };
}

interface SubCustomerListProps {
    subCustomers: SubCustomer[];
    parentId: string;
    onAddSubCustomer?: () => void;
}

export function SubCustomerList({
    subCustomers,
    parentId,
    onAddSubCustomer,
}: SubCustomerListProps) {
    const router = useRouter();

    if (subCustomers.length === 0) {
        return (
            <Card className="bg-gray-50 dark:bg-gray-900/50">
                <CardBody className="text-center py-8">
                    <UserGroupIcon className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                        No hay subclientes registrados
                    </p>
                    {onAddSubCustomer && (
                        <Button
                            color="primary"
                            size="sm"
                            startContent={<UserPlusIcon className="w-4 h-4" />}
                            variant="flat"
                            onPress={onAddSubCustomer}
                        >
                            Agregar Subcliente
                        </Button>
                    )}
                </CardBody>
            </Card>
        );
    }

    return (
        <div className="space-y-3">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                    <UserGroupIcon className="w-5 h-5" />
                    Subclientes ({subCustomers.length})
                </h3>
                {onAddSubCustomer && (
                    <Button
                        color="primary"
                        size="sm"
                        startContent={<UserPlusIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={onAddSubCustomer}
                    >
                        Agregar
                    </Button>
                )}
            </div>

            {subCustomers.map((subCustomer, index) => (
                <motion.div
                    key={subCustomer.id}
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ delay: index * 0.05 }}
                >
                    <Card
                        isPressable
                        className="hover:shadow-md transition-all"
                        onPress={() =>
                            router.push(
                                `/dashboard/customers/${subCustomer.id}/details`,
                            )
                        }
                    >
                        <CardBody className="flex flex-row items-center gap-4 p-4">
                            <Avatar
                                className="flex-shrink-0"
                                color="secondary"
                                name={subCustomer.name}
                                size="md"
                            />

                            <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">
                                    {subCustomer.name}
                                </h4>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    Creado{" "}
                                    {format(
                                        typeof subCustomer.createdAt ===
                                            "string"
                                            ? new Date(subCustomer.createdAt)
                                            : subCustomer.createdAt,
                                        "PPP",
                                        { locale: es },
                                    )}
                                </p>
                            </div>

                            <div className="flex items-center gap-3">
                                {subCustomer._count && (
                                    <>
                                        <Tooltip content="Órdenes">
                                            <Badge
                                                color="primary"
                                                content={
                                                    subCustomer._count.orders
                                                }
                                                size="sm"
                                            >
                                                <ShoppingBagIcon className="w-5 h-5 text-gray-400" />
                                            </Badge>
                                        </Tooltip>

                                        <Tooltip content="Packings">
                                            <Badge
                                                color="secondary"
                                                content={
                                                    subCustomer._count.packings
                                                }
                                                size="sm"
                                            >
                                                <DocumentTextIcon className="w-5 h-5 text-gray-400" />
                                            </Badge>
                                        </Tooltip>

                                        {subCustomer._count.subCustomers >
                                            0 && (
                                            <Tooltip content="Sub-subclientes">
                                                <Badge
                                                    color="warning"
                                                    content={
                                                        subCustomer._count
                                                            .subCustomers
                                                    }
                                                    size="sm"
                                                >
                                                    <UserGroupIcon className="w-5 h-5 text-gray-400" />
                                                </Badge>
                                            </Tooltip>
                                        )}
                                    </>
                                )}

                                <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            ))}
        </div>
    );
}
