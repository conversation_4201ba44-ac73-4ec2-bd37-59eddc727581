"use client";

import { useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Toolt<PERSON>,
    Pop<PERSON>,
    PopoverTrigger,
    PopoverContent,
    Divider,
} from "@heroui/react";
import {
    CheckCircleIcon,
    XMarkIcon,
    ChevronUpIcon,
    QuestionMarkCircleIcon,
    SparklesIcon,
    ShoppingBagIcon,
} from "@heroicons/react/24/outline";

import { Order } from "@/features/assignments/types";

import { OrderCard } from "./OrderCard";

// Extended Order type with additional properties that might be in the data
interface ExtendedOrder extends Order {
    customerName?: string;
    modelName?: string;
}

interface SelectedOrdersPanelProps {
    selectedOrders: ExtendedOrder[];
    onRemove: (id: string) => void;
    onClearAll: () => void;
}

export const SelectedOrdersPanel = ({
    selectedOrders,
    onRemove,
    onClearAll,
}: SelectedOrdersPanelProps) => {
    const [expanded, setExpanded] = useState(true);

    // Manejar eliminación de orden con callback memorizado
    const handleRemove = useCallback(
        (id: string) => {
            onRemove(id);
        },
        [onRemove],
    );

    // No hay órdenes seleccionadas - Mostrar un estado vacío atractivo
    if (selectedOrders.length === 0) {
        return (
            <AnimatePresence>
                <motion.div
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    className="fixed bottom-6 left-1/2 transform -translate-x-1/2 px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-lg border border-gray-300 dark:border-gray-700"
                    exit={{ opacity: 0, y: 20, scale: 0.9 }}
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    transition={{ type: "spring", stiffness: 300 }}
                >
                    <div className="flex items-center gap-3">
                        <ShoppingBagIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                            Selecciona órdenes para continuar
                        </span>
                    </div>
                </motion.div>
            </AnimatePresence>
        );
    }

    const totalGarments = selectedOrders.reduce((acc, order) => {
        return acc + (order._count?.garments || 0);
    }, 0);

    const containerVariants = {
        collapsed: { height: 60 },
        expanded: { height: "auto", minHeight: 60 },
    };

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            aria-label="Panel de órdenes seleccionadas"
            aria-live="polite"
            className="fixed bottom-6 left-1/2 transform -translate-x-1/2 w-11/12 md:w-10/12 lg:w-9/12 max-w-5xl z-50"
            exit={{ opacity: 0, y: 20 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
        >
            {/* Glow effect */}
            <motion.div
                animate={{ opacity: [0.2, 0.4, 0.2] }}
                className="absolute -inset-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl blur-lg opacity-30"
                transition={{ duration: 3, repeat: Infinity }}
            />

            <motion.div
                animate={expanded ? "expanded" : "collapsed"}
                className="relative bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border border-white/50 dark:border-gray-700/50 rounded-2xl shadow-2xl overflow-hidden"
                initial="expanded"
                transition={{ duration: 0.3, type: "spring", stiffness: 400 }}
                variants={containerVariants}
            >
                {/* Cabecera siempre visible con glassmorphism */}
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 dark:from-blue-400/20 dark:via-purple-400/20 dark:to-pink-400/20 backdrop-blur-xl border-b border-white/20 dark:border-gray-700/50">
                    <div className="flex items-center gap-3">
                        <motion.div
                            className="relative"
                            transition={{ type: "spring", stiffness: 400 }}
                            whileHover={{ scale: 1.1 }}
                        >
                            <div className="bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl p-2 shadow-lg">
                                <CheckCircleIcon className="w-6 h-6 text-white" />
                            </div>
                            <motion.div
                                animate={{ opacity: [0.5, 0.8, 0.5] }}
                                className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur opacity-50"
                                transition={{ duration: 2, repeat: Infinity }}
                            />
                        </motion.div>

                        <div>
                            <motion.span
                                key={selectedOrders.length}
                                animate={{ scale: 1 }}
                                className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent"
                                initial={{ scale: 0.8 }}
                                transition={{ type: "spring", stiffness: 500 }}
                            >
                                {selectedOrders.length}
                            </motion.span>
                            <span className="ml-1 font-medium text-gray-700 dark:text-gray-300">
                                {selectedOrders.length === 1
                                    ? "orden seleccionada"
                                    : "órdenes seleccionadas"}
                            </span>
                        </div>

                        <Tooltip content="Total de prendas en las órdenes seleccionadas">
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <Chip
                                    className="ml-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30 dark:border-green-400/30"
                                    size="md"
                                    startContent={
                                        <SparklesIcon className="w-4 h-4" />
                                    }
                                    variant="flat"
                                >
                                    <span className="font-bold">
                                        {totalGarments}
                                    </span>{" "}
                                    prendas
                                </Chip>
                            </motion.div>
                        </Tooltip>
                    </div>

                    <div className="flex items-center gap-2">
                        <AnimatePresence>
                            {selectedOrders.length > 0 && (
                                <motion.div
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.8 }}
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <Tooltip content="Limpiar selección">
                                        <motion.div
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                        >
                                            <Button
                                                isIconOnly
                                                aria-label="Limpiar todas las órdenes seleccionadas"
                                                className="bg-red-500/10 hover:bg-red-500/20 text-red-500 dark:text-red-400 border-red-500/30 dark:border-red-400/30"
                                                size="sm"
                                                variant="flat"
                                                onPress={onClearAll}
                                            >
                                                <XMarkIcon className="w-4 h-4" />
                                            </Button>
                                        </motion.div>
                                    </Tooltip>
                                </motion.div>
                            )}
                        </AnimatePresence>

                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Button
                                aria-controls="selected-orders-content"
                                aria-expanded={expanded}
                                className="min-w-0 flex items-center gap-2 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-700 dark:hover:to-gray-600"
                                size="sm"
                                variant="flat"
                                onClick={() => setExpanded(!expanded)}
                            >
                                <motion.div
                                    animate={{ rotate: expanded ? 180 : 0 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <ChevronUpIcon className="w-4 h-4" />
                                </motion.div>
                                <span className="hidden sm:inline font-medium">
                                    {expanded ? "Minimizar" : "Expandir"}
                                </span>
                            </Button>
                        </motion.div>
                    </div>
                </div>

                {/* Contenido expandible */}
                <AnimatePresence>
                    {expanded && (
                        <motion.div
                            animate={{ opacity: 1 }}
                            className="p-4"
                            exit={{ opacity: 0 }}
                            id="selected-orders-content"
                            initial={{ opacity: 0 }}
                        >
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                <AnimatePresence mode="popLayout">
                                    {selectedOrders.map((order, index) => (
                                        <motion.div
                                            key={order.id}
                                            layout
                                            animate={{
                                                opacity: 1,
                                                scale: 1,
                                                y: 0,
                                            }}
                                            exit={{
                                                opacity: 0,
                                                scale: 0.8,
                                                y: -20,
                                            }}
                                            initial={{
                                                opacity: 0,
                                                scale: 0.8,
                                                y: 20,
                                            }}
                                            transition={{
                                                duration: 0.3,
                                                delay: index * 0.05,
                                                type: "spring",
                                                stiffness: 300,
                                            }}
                                        >
                                            <OrderCard
                                                order={order}
                                                onRemove={handleRemove}
                                            />
                                        </motion.div>
                                    ))}
                                </AnimatePresence>
                            </div>

                            <motion.div
                                animate={{ opacity: 1 }}
                                initial={{ opacity: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                <Divider className="my-4 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-700 to-transparent h-px" />
                            </motion.div>

                            <Popover showArrow backdrop="blur" placement="top">
                                <PopoverTrigger>
                                    <motion.div
                                        className="flex justify-center"
                                        whileHover={{ scale: 1.05 }}
                                        whileTap={{ scale: 0.95 }}
                                    >
                                        <Button
                                            className="text-gray-600 dark:text-gray-300 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-700 dark:hover:to-gray-600"
                                            size="sm"
                                            startContent={
                                                <QuestionMarkCircleIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                        >
                                            ¿Cómo funciona?
                                        </Button>
                                    </motion.div>
                                </PopoverTrigger>
                                <PopoverContent className="backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border border-white/20 dark:border-gray-700/50">
                                    <div className="p-4 max-w-xs">
                                        <div className="flex items-center gap-2 mb-3">
                                            <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
                                                <CheckCircleIcon className="w-4 h-4 text-white" />
                                            </div>
                                            <h5 className="font-bold text-base bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                                                Órdenes Seleccionadas
                                            </h5>
                                        </div>
                                        <div className="space-y-2">
                                            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                                                Estas son las órdenes que has
                                                seleccionado para asignar al
                                                contratista.
                                            </p>
                                            <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3">
                                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                                    <span className="font-semibold">
                                                        Siguiente paso:
                                                    </span>{" "}
                                                    Podrás indicar las
                                                    cantidades específicas para
                                                    cada orden.
                                                </p>
                                            </div>
                                            <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                                                Puedes eliminar órdenes
                                                individuales o limpiar toda la
                                                selección.
                                            </p>
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        </motion.div>
                    )}
                </AnimatePresence>
            </motion.div>
        </motion.div>
    );
};
