"use client";

import React, { useState, useEffect } from "react";
import {
    Button,
    Input,
    Select,
    SelectItem,
    DateRangePicker,
    Chip,
    Popover,
    PopoverTrigger,
    PopoverContent,
    Badge,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    XMarkIcon,
    CalendarIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";

import { CustomerSelector } from "@/features/customers/components";

interface PackingFiltersProps {
    onFiltersChange: (filters: any) => void;
    packingStatuses: Array<{ id: string; name: string }>;
    defaultFilters?: any;
}

export function PackingFilters({
    onFiltersChange,
    packingStatuses,
    defaultFilters = {},
}: PackingFiltersProps) {
    const [showFilters, setShowFilters] = useState(false);
    const [search, setSearch] = useState(defaultFilters.search || "");
    const [statusId, setStatusId] = useState(defaultFilters.statusId || "");
    const [customerFilter, setCustomerFilter] = useState<{
        customerId: string;
        subCustomerId?: string;
    }>({
        customerId: defaultFilters.customerId || "",
        subCustomerId: defaultFilters.subCustomerId || "",
    });
    const [dateRange, setDateRange] = useState(
        defaultFilters.dateRange || null,
    );
    const [isPrinted, setIsPrinted] = useState(
        defaultFilters.isPrinted || "all",
    );

    const activeFiltersCount = [
        statusId,
        customerFilter.customerId,
        dateRange,
        isPrinted !== "all",
    ].filter(Boolean).length;

    const handleApplyFilters = () => {
        const filters: any = {
            search,
            statusId: statusId || undefined,
            customerId: customerFilter.customerId || undefined,
            subCustomerId: customerFilter.subCustomerId || undefined,
            dateRange: dateRange || undefined,
            isPrinted: isPrinted !== "all" ? isPrinted === "yes" : undefined,
        };

        // Remove undefined values
        Object.keys(filters).forEach((key) => {
            if (filters[key] === undefined) {
                delete filters[key];
            }
        });

        onFiltersChange(filters);
    };

    const handleClearFilters = () => {
        setSearch("");
        setStatusId("");
        setCustomerFilter({ customerId: "", subCustomerId: "" });
        setDateRange(null);
        setIsPrinted("all");
        onFiltersChange({});
    };

    useEffect(() => {
        const debounce = setTimeout(() => {
            if (search !== defaultFilters.search) {
                handleApplyFilters();
            }
        }, 500);

        return () => clearTimeout(debounce);
    }, [search]);

    return (
        <div className="space-y-4">
            <div className="flex items-center gap-4">
                <Input
                    isClearable
                    className="flex-1"
                    placeholder="Buscar por folio, cliente o notas..."
                    startContent={
                        <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
                    }
                    value={search}
                    onClear={() => setSearch("")}
                    onValueChange={setSearch}
                />

                <Popover
                    isOpen={showFilters}
                    placement="bottom-end"
                    onOpenChange={setShowFilters}
                >
                    <PopoverTrigger>
                        <Button
                            color={
                                activeFiltersCount > 0 ? "primary" : "default"
                            }
                            endContent={
                                activeFiltersCount > 0 && (
                                    <Badge color="primary" size="sm">
                                        {activeFiltersCount}
                                    </Badge>
                                )
                            }
                            startContent={<FunnelIcon className="w-4 h-4" />}
                            variant={
                                activeFiltersCount > 0 ? "flat" : "bordered"
                            }
                        >
                            Filtros
                        </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-96 p-4">
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">
                                Filtros avanzados
                            </h3>

                            {/* Cliente/Subcliente */}
                            <CustomerSelector
                                label="Filtrar por cliente"
                                placeholder="Todos los clientes"
                                value={customerFilter}
                                onChange={setCustomerFilter}
                            />

                            {/* Estado */}
                            <Select
                                label="Estado"
                                placeholder="Todos los estados"
                                selectedKeys={statusId ? [statusId] : []}
                                startContent={
                                    <CheckCircleIcon className="w-4 h-4 text-gray-400" />
                                }
                                onSelectionChange={(keys) => {
                                    const key = Array.from(keys)[0] as string;

                                    setStatusId(key);
                                }}
                            >
                                {packingStatuses.map((status) => (
                                    <SelectItem key={status.id}>
                                        {status.name}
                                    </SelectItem>
                                ))}
                            </Select>

                            {/* Rango de fechas */}
                            <DateRangePicker
                                label="Rango de fechas de entrega"
                                startContent={
                                    <CalendarIcon className="w-4 h-4 text-gray-400" />
                                }
                                value={dateRange}
                                onChange={setDateRange}
                            />

                            {/* Estado de impresión */}
                            <div>
                                <label className="text-sm font-medium mb-2 block">
                                    Estado de impresión
                                </label>
                                <div className="flex gap-2">
                                    <Button
                                        size="sm"
                                        variant={
                                            isPrinted === "all"
                                                ? "solid"
                                                : "bordered"
                                        }
                                        onPress={() => setIsPrinted("all")}
                                    >
                                        Todos
                                    </Button>
                                    <Button
                                        color="success"
                                        size="sm"
                                        variant={
                                            isPrinted === "yes"
                                                ? "solid"
                                                : "bordered"
                                        }
                                        onPress={() => setIsPrinted("yes")}
                                    >
                                        Impresos
                                    </Button>
                                    <Button
                                        color="warning"
                                        size="sm"
                                        variant={
                                            isPrinted === "no"
                                                ? "solid"
                                                : "bordered"
                                        }
                                        onPress={() => setIsPrinted("no")}
                                    >
                                        Sin imprimir
                                    </Button>
                                </div>
                            </div>

                            {/* Acciones */}
                            <div className="flex gap-2 pt-2">
                                <Button
                                    startContent={
                                        <XMarkIcon className="w-4 h-4" />
                                    }
                                    variant="light"
                                    onPress={handleClearFilters}
                                >
                                    Limpiar
                                </Button>
                                <Button
                                    className="flex-1"
                                    color="primary"
                                    onPress={() => {
                                        handleApplyFilters();
                                        setShowFilters(false);
                                    }}
                                >
                                    Aplicar filtros
                                </Button>
                            </div>
                        </div>
                    </PopoverContent>
                </Popover>
            </div>

            {/* Chips de filtros activos */}
            {activeFiltersCount > 0 && (
                <div className="flex flex-wrap gap-2">
                    {customerFilter.customerId && (
                        <Chip
                            color="primary"
                            variant="flat"
                            onClose={() =>
                                setCustomerFilter({
                                    customerId: "",
                                    subCustomerId: "",
                                })
                            }
                        >
                            Cliente filtrado
                        </Chip>
                    )}
                    {statusId && (
                        <Chip
                            color="primary"
                            variant="flat"
                            onClose={() => setStatusId("")}
                        >
                            Estado:{" "}
                            {
                                packingStatuses.find((s) => s.id === statusId)
                                    ?.name
                            }
                        </Chip>
                    )}
                    {dateRange && (
                        <Chip
                            color="primary"
                            variant="flat"
                            onClose={() => setDateRange(null)}
                        >
                            Rango de fechas
                        </Chip>
                    )}
                    {isPrinted !== "all" && (
                        <Chip
                            color="primary"
                            variant="flat"
                            onClose={() => setIsPrinted("all")}
                        >
                            {isPrinted === "yes" ? "Impresos" : "Sin imprimir"}
                        </Chip>
                    )}
                </div>
            )}
        </div>
    );
}
