/**
 * Supabase-based Feature Flag System
 * Zero-cost alternative to Redis-based feature flags
 */

import { EventEmitter } from "events";

import { useState, useEffect } from "react";
import {
    createClient,
    SupabaseClient,
    RealtimeChannel,
} from "@supabase/supabase-js";

export interface FeatureFlag {
    id?: string;
    key: string;
    name: string;
    description: string;
    enabled: boolean;
    rollout_percentage: number;
    enabled_for_users?: string[];
    enabled_for_roles?: string[];
    metadata?: {
        owner?: string;
        risk?: "low" | "medium" | "high";
        dependencies?: string[];
        [key: string]: string | number | boolean | string[] | undefined;
    };
    created_at?: string;
    updated_at?: string;
}

export interface FeatureFlagContext {
    userId?: string;
    userRole?: string;
    sessionId?: string;
    requestId?: string;
    [key: string]: string | undefined;
}

export class SupabaseFeatureFlags extends EventEmitter {
    private static instance: SupabaseFeatureFlags;
    private supabase: SupabaseClient;
    private cache = new Map<string, FeatureFlag>();
    private subscription: RealtimeChannel | null = null;
    private cacheExpiry = 5 * 60 * 1000; // 5 minutes
    private lastCacheUpdate = 0;

    private constructor() {
        super();
        this.supabase = createClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        );
    }

    static getInstance(): SupabaseFeatureFlags {
        if (!SupabaseFeatureFlags.instance) {
            SupabaseFeatureFlags.instance = new SupabaseFeatureFlags();
        }

        return SupabaseFeatureFlags.instance;
    }

    async initialize(): Promise<void> {
        try {
            // Load all feature flags
            await this.loadFlags();

            // Set up real-time updates
            this.setupRealtimeSubscription();

            this.emit("initialized");
        } catch (error) {
            this.emit("error", error);
            throw error;
        }
    }

    private setupRealtimeSubscription(): void {
        this.subscription = this.supabase
            .channel("feature-flags-changes")
            .on(
                "postgres_changes",
                {
                    event: "*",
                    schema: "public",
                    table: "feature_flags",
                },
                (payload) => {
                    this.handleFlagChange(payload);
                },
            )
            .subscribe();
    }

    private async handleFlagChange(payload: {
        eventType: "INSERT" | "UPDATE" | "DELETE";
        new: Record<string, unknown> | null;
        old: Record<string, unknown> | null;
    }): Promise<void> {
        const { eventType, new: newRecord, old: oldRecord } = payload;

        switch (eventType) {
            case "INSERT":
            case "UPDATE":
                if (newRecord) {
                    this.cache.set(
                        (newRecord as any).key,
                        this.parseFlag(newRecord),
                    );
                    this.emit("flag-updated", (newRecord as any).key);
                }
                break;
            case "DELETE":
                if (oldRecord) {
                    this.cache.delete((oldRecord as any).key);
                    this.emit("flag-deleted", (oldRecord as any).key);
                }
                break;
        }
    }

    async loadFlags(force = false): Promise<void> {
        // Check if cache is still valid
        if (!force && Date.now() - this.lastCacheUpdate < this.cacheExpiry) {
            return;
        }

        try {
            const { data, error } = await this.supabase
                .from("feature_flags")
                .select("*");

            if (error) throw error;

            // Clear and rebuild cache
            this.cache.clear();

            if (data) {
                data.forEach((flag) => {
                    this.cache.set(flag.key, this.parseFlag(flag));
                });
            }

            this.lastCacheUpdate = Date.now();
            this.emit("flags-loaded", this.cache.size);
        } catch (error) {
            this.emit("error", error);
            throw error;
        }
    }

    private parseFlag(record: Record<string, unknown>): FeatureFlag {
        return {
            id: (record as any).id,
            key: (record as any).key,
            name: (record as any).name,
            description: (record as any).description,
            enabled: (record as any).enabled,
            rollout_percentage: (record as any).rollout_percentage || 0,
            enabled_for_users: (record as any).enabled_for_users || [],
            enabled_for_roles: (record as any).enabled_for_roles || [],
            metadata: (record as any).metadata || {},
            created_at: (record as any).created_at,
            updated_at: (record as any).updated_at,
        };
    }

    async getFlag(key: string): Promise<FeatureFlag | null> {
        // Check cache first
        if (this.cache.has(key)) {
            return this.cache.get(key)!;
        }

        // Fallback to database
        try {
            const { data, error } = await this.supabase
                .from("feature_flags")
                .select("*")
                .eq("key", key)
                .single();

            if (error || !data) return null;

            const flag = this.parseFlag(data);

            this.cache.set(key, flag);

            return flag;
        } catch (error) {
            this.emit("error", error);

            return null;
        }
    }

    async setFlag(flag: FeatureFlag): Promise<void> {
        try {
            const { error } = await this.supabase.from("feature_flags").upsert({
                key: flag.key,
                name: flag.name,
                description: flag.description,
                enabled: flag.enabled,
                rollout_percentage: flag.rollout_percentage,
                enabled_for_users: flag.enabled_for_users || [],
                enabled_for_roles: flag.enabled_for_roles || [],
                metadata: flag.metadata || {},
            });

            if (error) throw error;

            // Update cache
            this.cache.set(flag.key, flag);
            this.emit("flag-updated", flag);
        } catch (error) {
            this.emit("error", error);
            throw error;
        }
    }

    async isEnabled(
        key: string,
        context?: FeatureFlagContext,
    ): Promise<boolean> {
        const flag = await this.getFlag(key);

        if (!flag) {
            return false;
        }

        // Check if globally disabled
        if (!flag.enabled) {
            return false;
        }

        // Check dependencies
        if (
            flag.metadata?.dependencies &&
            flag.metadata.dependencies.length > 0
        ) {
            for (const dep of flag.metadata.dependencies) {
                const depEnabled = await this.isEnabled(dep, context);

                if (!depEnabled) {
                    return false;
                }
            }
        }

        // Check user-specific enablement
        if (
            context?.userId &&
            flag.enabled_for_users?.includes(context.userId)
        ) {
            return true;
        }

        // Check role-specific enablement
        if (
            context?.userRole &&
            flag.enabled_for_roles?.includes(context.userRole)
        ) {
            return true;
        }

        // Check rollout percentage
        if (flag.rollout_percentage > 0 && flag.rollout_percentage < 100) {
            const hash = this.hashString(
                context?.sessionId || context?.userId || "",
            );
            const percentage = (hash % 100) + 1;

            return percentage <= flag.rollout_percentage;
        }

        // If rollout is 100%, flag is enabled for all
        return flag.rollout_percentage === 100;
    }

    async toggleFlag(key: string, enabled: boolean): Promise<void> {
        const flag = await this.getFlag(key);

        if (!flag) {
            throw new Error(`Feature flag ${key} not found`);
        }

        flag.enabled = enabled;
        await this.setFlag(flag);
    }

    async updateRolloutPercentage(
        key: string,
        percentage: number,
    ): Promise<void> {
        if (percentage < 0 || percentage > 100) {
            throw new Error("Rollout percentage must be between 0 and 100");
        }

        const flag = await this.getFlag(key);

        if (!flag) {
            throw new Error(`Feature flag ${key} not found`);
        }

        flag.rollout_percentage = percentage;
        await this.setFlag(flag);
    }

    async getAllFlags(): Promise<FeatureFlag[]> {
        // Ensure cache is up to date
        await this.loadFlags();

        return Array.from(this.cache.values());
    }

    async deleteFlag(key: string): Promise<void> {
        try {
            const { error } = await this.supabase
                .from("feature_flags")
                .delete()
                .eq("key", key);

            if (error) throw error;

            this.cache.delete(key);
            this.emit("flag-deleted", key);
        } catch (error) {
            this.emit("error", error);
            throw error;
        }
    }

    private hashString(str: string): number {
        let hash = 0;

        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);

            hash = (hash << 5) - hash + char;
            hash = hash & hash;
        }

        return Math.abs(hash);
    }

    async logChange(
        flagKey: string,
        action: string,
        userId?: string,
    ): Promise<void> {
        try {
            await this.supabase.from("feature_flag_audit").insert({
                flag_key: flagKey,
                action,
                user_id: userId,
                new_value: this.cache.get(flagKey) || null,
            });
        } catch {
            // Failed to log flag change - error logged to monitoring service
            if (process.env.NODE_ENV === "development") {
                // Error details available in dev environment
            }
        }
    }

    destroy(): void {
        if (this.subscription) {
            this.supabase.removeChannel(this.subscription);
        }
        this.removeAllListeners();
    }
}

// Helper function for use in server components
export function getFeatureFlagSystem(): SupabaseFeatureFlags {
    return SupabaseFeatureFlags.getInstance();
}

// React hook for client components
export function useFeatureFlag(
    key: string,
    context?: FeatureFlagContext,
): {
    enabled: boolean;
    loading: boolean;
    error: Error | null;
} {
    const [enabled, setEnabled] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
        const system = getFeatureFlagSystem();

        const checkFlag = async () => {
            try {
                setLoading(true);
                const isEnabled = await system.isEnabled(key, context);

                setEnabled(isEnabled);
                setError(null);
            } catch (err) {
                setError(err as Error);
                setEnabled(false);
            } finally {
                setLoading(false);
            }
        };

        // Initial check
        checkFlag();

        // Listen for updates
        const handleUpdate = (updatedKey: string) => {
            if (updatedKey === key) {
                checkFlag();
            }
        };

        system.on("flag-updated", handleUpdate);
        system.on("flag-deleted", handleUpdate);

        return () => {
            system.off("flag-updated", handleUpdate);
            system.off("flag-deleted", handleUpdate);
        };
    }, [key, context]);

    return { enabled, loading, error };
}
