"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardBody, CardHeader, Progress, Button } from "@heroui/react";
import { ChevronLeft, ChevronRight, Save, X } from "lucide-react";

import { showToast } from "@/app/dashboard/assignments/new/utils/toast";

import { OrderSelectionStep } from "../OrderSelectionStep";
import { QualityDistributionStep } from "../QualityDistributionStep";
import { createPackingEnhancedV3 } from "../../actions/create-packing-enhanced-v3";
// Removed usePackingWizardStore import (Zustand not available)

import { PackagingSummaryStep } from "./PackagingSummaryStep";
import { ReviewStep } from "./ReviewStep";
import { BasicInfoStep } from "./BasicInfoStep";

const STEPS = [
    {
        id: "basic-info",
        title: "Información Básica",
        description: "Datos generales del packing",
    },
    {
        id: "orders",
        title: "Selección de Órdenes",
        description: "Seleccione las órdenes a incluir",
    },
    {
        id: "quality",
        title: "Distribución por Calidad",
        description: "Clasifique las prendas por calidad",
    },
    {
        id: "packaging",
        title: "Resumen de Empaque",
        description: "Configure el empaque por talla",
    },
    {
        id: "review",
        title: "Revisión y Confirmación",
        description: "Revise y genere el packing",
    },
];

export function EnhancedPackingWizardV3() {
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Replace Zustand store with useState hooks
    const [basicInfo, setBasicInfo] = useState<any>({});
    const [selectedOrders, setSelectedOrders] = useState<any>([]);
    const [qualityDistribution, setQualityDistribution] = useState<any>([]);
    const [packagingSummary, setPackagingSummary] = useState<any>([]);

    const reset = () => {
        setBasicInfo({});
        setSelectedOrders([]);
        setQualityDistribution([]);
        setPackagingSummary([]);
    };

    const isStepValid = () => {
        switch (currentStep) {
            case 0:
                return basicInfo.customerId && basicInfo.deliveryDate;
            case 1:
                return selectedOrders.length > 0;
            case 2:
                return qualityDistribution.length > 0;
            case 3:
                return packagingSummary.length > 0;
            case 4:
                return true;
            default:
                return false;
        }
    };

    const handleNext = () => {
        if (currentStep < STEPS.length - 1 && isStepValid()) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handlePrevious = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleCancel = () => {
        if (
            confirm(
                "¿Está seguro de cancelar? Se perderán los cambios no guardados.",
            )
        ) {
            reset();
            router.push("/packings");
        }
    };

    const handleSubmit = async () => {
        try {
            setIsSubmitting(true);

            // Prepare data for submission
            const packingData = {
                deliveryDate: basicInfo.deliveryDate,
                customerId: basicInfo.customerId,
                subCustomerId: basicInfo.subCustomerId,
                orderIds: selectedOrders.map((o: any) => o.id),
                notes: basicInfo.notes,
                packagingSummaries: packagingSummary,
                details: qualityDistribution,
            };

            const result = await createPackingEnhancedV3(packingData);

            if (result.success) {
                showToast({
                    title: "Packing creado exitosamente",
                    color: "success",
                });
                reset();
                router.push(`/dashboard/packings`);
            } else {
                showToast({
                    title: result.error || "Error al crear packing",
                    color: "danger",
                });
            }
        } catch (error) {
            console.error("Error submitting packing:", error);
            showToast({
                title: "Error al crear packing",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const renderStep = () => {
        switch (currentStep) {
            case 0:
                return (
                    <BasicInfoStep data={basicInfo} onChange={setBasicInfo} />
                );
            case 1:
                return (
                    <OrderSelectionStep
                        customerId={""} // Temporalmente sin filtro para ver todas las órdenes
                        selectedOrders={selectedOrders}
                        onNext={() => {}}
                        onOrdersChange={setSelectedOrders as any}
                    />
                );
            case 2:
                return (
                    <QualityDistributionStep
                        selectedOrders={selectedOrders as any}
                        onDistributionChange={setQualityDistribution as any}
                        onNext={() => {}}
                        onPrevious={() => {}}
                    />
                );
            case 3:
                return (
                    <PackagingSummaryStep
                        orders={selectedOrders}
                        onSummaryChange={setPackagingSummary}
                    />
                );
            case 4:
                return (
                    <ReviewStep
                        basicInfo={basicInfo}
                        packagingSummary={packagingSummary}
                        qualityDistribution={qualityDistribution}
                        selectedOrders={selectedOrders}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <div className="max-w-6xl mx-auto p-6">
            {/* Progress */}
            <Card className="mb-6">
                <CardBody>
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-2xl font-bold">Nuevo Packing</h2>
                        <Button
                            color="danger"
                            startContent={<X className="w-4 h-4" />}
                            variant="light"
                            onPress={handleCancel}
                        >
                            Cancelar
                        </Button>
                    </div>

                    <Progress
                        className="mb-4"
                        color="primary"
                        value={((currentStep + 1) / STEPS.length) * 100}
                    />

                    <div className="flex justify-between">
                        {STEPS.map((step, index) => (
                            <div
                                key={step.id}
                                className={`text-center ${
                                    index <= currentStep
                                        ? "text-primary"
                                        : "text-gray-400"
                                }`}
                            >
                                <div
                                    className={`w-8 h-8 rounded-full mx-auto mb-2 flex items-center justify-center ${
                                        index <= currentStep
                                            ? "bg-primary text-white"
                                            : "bg-gray-200"
                                    }`}
                                >
                                    {index + 1}
                                </div>
                                <p className="text-xs font-medium">
                                    {step.title}
                                </p>
                            </div>
                        ))}
                    </div>
                </CardBody>
            </Card>

            {/* Current Step */}
            <Card className="mb-6">
                <CardHeader>
                    <div>
                        <h3 className="text-lg font-semibold">
                            {STEPS[currentStep].title}
                        </h3>
                        <p className="text-sm text-gray-500">
                            {STEPS[currentStep].description}
                        </p>
                    </div>
                </CardHeader>
                <CardBody>{renderStep()}</CardBody>
            </Card>

            {/* Navigation */}
            <div className="flex justify-between">
                <Button
                    isDisabled={currentStep === 0}
                    startContent={<ChevronLeft className="w-4 h-4" />}
                    variant="bordered"
                    onPress={handlePrevious}
                >
                    Anterior
                </Button>

                {currentStep === STEPS.length - 1 ? (
                    <Button
                        color="primary"
                        endContent={<Save className="w-4 h-4" />}
                        isDisabled={!isStepValid()}
                        isLoading={isSubmitting}
                        onPress={handleSubmit}
                    >
                        Crear Packing
                    </Button>
                ) : (
                    <Button
                        color="primary"
                        endContent={<ChevronRight className="w-4 h-4" />}
                        isDisabled={!isStepValid()}
                        onPress={handleNext}
                    >
                        Siguiente
                    </Button>
                )}
            </div>
        </div>
    );
}
