"use client";

import { useState } from "react";
import { addToast } from "@heroui/react";

import { RemissionPreview } from "@/types/remission";

interface UseRemissionPDFOptions {
    onSuccess?: () => void;
    onError?: (error: Error) => void;
}

export function useRemissionPDF(options?: UseRemissionPDFOptions) {
    const [isGenerating, setIsGenerating] = useState(false);

    const generatePDF = async (
        remissionData: RemissionPreview,
        orientation: "portrait" | "landscape" = "portrait",
    ) => {
        setIsGenerating(true);

        try {
            const response = await fetch("/api/remissions/pdf", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    remissionData,
                    orientation,
                }),
            });

            if (!response.ok) {
                const error = await response.json();

                throw new Error(error.message || "Error al generar PDF");
            }

            // Get the blob from response
            const blob = await response.blob();

            // Create download link
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");

            link.href = url;
            link.download = `remision-${remissionData.folio}.pdf`;

            // Trigger download
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            addToast({
                title: "Éxito",
                description: "PDF generado exitosamente",
                color: "success",
            });
            options?.onSuccess?.();
        } catch (error) {
            console.error("Error generating PDF:", error);
            const errorMessage =
                error instanceof Error ? error.message : "Error desconocido";

            addToast({
                title: "Error",
                description: `Error al generar PDF: ${errorMessage}`,
                color: "danger",
            });
            options?.onError?.(error as Error);
        } finally {
            setIsGenerating(false);
        }
    };

    return {
        generatePDF,
        isGenerating,
    };
}
