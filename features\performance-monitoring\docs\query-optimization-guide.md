# Query Optimization Migration Guide

## Phase 2: Query Optimization Implementation

### 1. Apply Database Indexes

Run the following migration to add performance indexes:

```bash
# Apply indexes using Prisma migrate
npx prisma migrate dev --name add_performance_indexes --create-only
# Then apply the SQL file: prisma/migrations/add_performance_indexes.sql
npx prisma migrate dev
```

### 2. Replace Order Queries

#### Before (Over-fetching)
```typescript
// In features/orders/actions/query.ts
const orders = await prisma.order.findMany({
    include: {
        customer: true,      // ❌ Fetches ALL fields
        status: true,        // ❌ Fetches ALL fields
        parts: true,         // ❌ Fetches ALL fields
        _count: {
            select: {
                garments: true,
                notes: true,
                packings: true,
            },
        },
    },
});
```

#### After (Optimized)
```typescript
// Import optimized select
import { optimizedOrderSelect } from '@/features/performance-monitoring/optimizations/prisma-selects';

// Use selective loading
const orders = await prisma.order.findMany({
    select: optimizedOrderSelect,  // ✅ Only necessary fields
});
```

### 3. Update React Hooks

Replace existing order hooks with optimized versions:

```typescript
// Before
import { useOrders } from '@/features/orders/hooks/useOrders';

// After
import { useOrdersOptimized } from '@/features/performance-monitoring/hooks/useOrdersOptimized';
```

### 4. Implement Cursor Pagination

For lists with infinite scroll:

```typescript
import { useOrdersInfinite } from '@/features/performance-monitoring/hooks/useOrdersOptimized';

function OrderList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useOrdersInfinite({ statusId: 'active' });
  
  const orders = data?.pages.flatMap(page => page.items) ?? [];
  
  return (
    <div>
      {orders.map(order => <OrderItem key={order.id} order={order} />)}
      {hasNextPage && (
        <button onClick={() => fetchNextPage()}>
          {isFetchingNextPage ? 'Loading...' : 'Load More'}
        </button>
      )}
    </div>
  );
}
```

### 5. Performance Monitoring

The optimized queries automatically track performance:

```typescript
// Check performance metrics
import { performanceTracker } from '@/features/performance-monitoring/utils/api-metrics';

const report = performanceTracker.generateReport();
console.log('Query Performance:', report.database);
```

## Expected Improvements

Based on the optimization strategy:

- **Query Time**: 50-70% reduction
- **Data Transfer**: 60-80% reduction
- **Memory Usage**: 40-60% reduction

## Migration Checklist

- [ ] Apply database indexes
- [ ] Update order queries to use selective loading
- [ ] Replace order hooks with optimized versions
- [ ] Implement cursor pagination for large lists
- [ ] Run benchmark to verify improvements
- [ ] Monitor performance metrics
