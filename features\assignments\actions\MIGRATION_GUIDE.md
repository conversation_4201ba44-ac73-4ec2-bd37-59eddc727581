// Migration Guide: createAssignments Hybrid Implementation
// ========================================================

# Migration from create.ts to create-v2.ts

## Overview
The new implementation (create-v2.ts) uses a hybrid approach to solve the transaction timeout issue:
- Single transaction for ≤10 items (optimized)
- Split transactions for >10 items (assignments first, then remission)
- Integrated with IntegrityManager for audit trail and compensation

## Key Changes

### 1. Integrity Wrapper
All operations are now wrapped with IntegrityManager.executeWithIntegrity() which provides:
- Operation logging (OperationLog table)
- Automatic compensation on failure
- Metrics and monitoring

### 2. Volume-Based Strategy
```typescript
const VOLUME_THRESHOLD = 10;
if (assignments.length <= VOLUME_THRESHOLD) {
    // Use single optimized transaction
} else {
    // Use split transaction approach
}
```

### 3. Performance Optimizations
- Uses createMany() when possible
- Batch updates for garmentSize quantities
- Reduced includes in queries
- Separate timeouts for each phase

### 4. Enhanced Error Recovery
- Soft delete compensation for failed operations
- Operation status tracking
- Ability to retry or recover stuck operations

## Migration Steps

### Option 1: Direct Replacement (Recommended)
1. Replace import in all files:
   ```typescript
   // Before
   import { createAssignments } from '@/features/assignments/actions/create';
   
   // After
   import { createAssignments } from '@/features/assignments/actions/create-v2';
   ```

2. The API is identical, no code changes needed

### Option 2: Gradual Migration with Feature Flag
1. Use environment variable to control which version:
   ```typescript
   const useNewImplementation = process.env.USE_NEW_ASSIGNMENT_CREATE === 'true';
   
   export const createAssignments = useNewImplementation 
     ? createAssignmentsV2 
     : createAssignmentsV1;
   ```

## Database Migration Required

Run the following Prisma migration to add new fields:
```bash
npx prisma migrate dev --name add_assignment_integrity_fields
```

This adds:
- Assignment.status (default: "ACTIVE")
- Assignment.cancelledAt
- Assignment.cancelReason
- OperationLog table (already added)

## Testing

1. Test with small batches (< 10 items) to verify single transaction
2. Test with large batches (> 10 items) to verify split transaction
3. Test failure scenarios to verify compensation
4. Monitor OperationLog table for audit trail

## Monitoring

Check operation statistics:
```typescript
const stats = await integrityManager.getOperationStats('ASSIGNMENT_BATCH', 24);
console.log('Success rate:', stats.successRate);
console.log('Compensation rate:', stats.compensationRate);
```

Find stuck operations:
```typescript
const stuckOps = await integrityManager.findStuckOperations();
// Implement recovery logic for stuck operations
```

## Rollback Plan

If issues arise:
1. Revert to original implementation by changing imports back
2. The database changes are backward compatible
3. OperationLog entries can be ignored/cleaned up later
