"use client";

import { useState, useCallback, useRef } from "react";

export type FeedbackType = "success" | "error" | "warning" | "info" | "loading";

interface FeedbackItem {
    id: string;
    type: FeedbackType;
    message: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}

interface UseVisualFeedbackReturn {
    showFeedback: (
        type: FeedbackType,
        message: string,
        options?: {
            duration?: number;
            action?: { label: string; onClick: () => void };
        },
    ) => void;
    clearFeedback: (id?: string) => void;
    isLoading: boolean;
    setLoading: (loading: boolean) => void;
    feedbackItems: FeedbackItem[];
}

export function useVisualFeedback(): UseVisualFeedbackReturn {
    const [feedbackItems, setFeedbackItems] = useState<FeedbackItem[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());

    const showFeedback = useCallback(
        (
            type: FeedbackType,
            message: string,
            options?: {
                duration?: number;
                action?: { label: string; onClick: () => void };
            },
        ) => {
            const id = `feedback-${Date.now()}-${Math.random()}`;
            const duration =
                options?.duration ?? (type === "loading" ? 0 : 5000);

            const newItem: FeedbackItem = {
                id,
                type,
                message,
                duration,
                action: options?.action,
            };

            setFeedbackItems((prev) => [...prev, newItem]);

            // Auto-eliminar después de duration (si no es 0)
            if (duration > 0) {
                const timeout = setTimeout(() => {
                    clearFeedback(id);
                }, duration);

                timeoutRefs.current.set(id, timeout);
            }

            // Si es tipo loading, actualizar isLoading
            if (type === "loading") {
                setIsLoading(true);
            }
        },
        [],
    );

    const clearFeedback = useCallback((id?: string) => {
        if (id) {
            // Limpiar timeout específico
            const timeout = timeoutRefs.current.get(id);

            if (timeout) {
                clearTimeout(timeout);
                timeoutRefs.current.delete(id);
            }

            setFeedbackItems((prev) => {
                const filtered = prev.filter((item) => item.id !== id);

                // Si era el último loading, actualizar isLoading
                if (!filtered.some((item) => item.type === "loading")) {
                    setIsLoading(false);
                }

                return filtered;
            });
        } else {
            // Limpiar todos
            timeoutRefs.current.forEach((timeout) => clearTimeout(timeout));
            timeoutRefs.current.clear();
            setFeedbackItems([]);
            setIsLoading(false);
        }
    }, []);

    const setLoading = useCallback((loading: boolean) => {
        setIsLoading(loading);
        if (!loading) {
            // Limpiar todos los feedbacks de tipo loading
            setFeedbackItems((prev) =>
                prev.filter((item) => item.type !== "loading"),
            );
        }
    }, []);

    return {
        showFeedback,
        clearFeedback,
        isLoading,
        setLoading,
        feedbackItems,
    };
}
