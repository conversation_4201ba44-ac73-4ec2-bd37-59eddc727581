"use server";

import { revalidatePath } from "next/cache";

import { db } from "@/lib/db";

export async function deletePacking(packingId: string) {
    try {
        // Verificar que el packing existe
        const packing = await db.packing.findUnique({
            where: { id: packingId },
            include: {
                customer: true,
            },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        // Eliminar el packing (las relaciones se eliminarán en cascada)
        await db.packing.delete({
            where: { id: packingId },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/customers/${packing.customerId}/details`);

        return { success: true, message: "Packing eliminado exitosamente" };
    } catch (error) {
        console.error("Error deleting packing:", error);

        return { success: false, error: "Error al eliminar el packing" };
    }
}
