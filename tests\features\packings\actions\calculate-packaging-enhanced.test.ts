import { calculatePackagingAutomatically } from '@/features/packings/actions/calculate-packaging-enhanced';
import { PackagingInputSchema } from '@/features/packings/schemas/packing-enhanced-schema-v3';
import { z } from 'zod';

describe('calculatePackagingAutomatically', () => {
  describe('Basic Calculations', () => {
    it('should calculate packaging for a single size with exact box quantities', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-1',
        details: [
          {
            orderDetailId: 'detail-1',
            sizeName: 'M',
            quantityFirst: 100,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.orderId).toBe('order-1');
      expect(result.data!.sizeBreakdown).toHaveLength(1);
      
      const sizeBreakdown = result.data!.sizeBreakdown[0];
      expect(sizeBreakdown).toEqual({
        size: 'M',
        totalQuantity: 100,
        boxes: 2,
        bagsFirst: 0,
        bagsSecond: 0,
        loosePieces: 0
      });
    });

    it('should handle mixed quality distributions', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-2',
        details: [
          {
            orderDetailId: 'detail-2',
            sizeName: 'L',
            quantityFirst: 75,
            quantitySecond: 25,
            quantityStained: 10,
            quantityIncomplete: 5
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      
      const sizeBreakdown = result.data!.sizeBreakdown[0];
      expect(sizeBreakdown.totalQuantity).toBe(115); // 75 + 25 + 10 + 5
      expect(sizeBreakdown.boxes).toBe(1); // First 50 pieces go in box
      expect(sizeBreakdown.bagsFirst).toBe(1); // Next 25 of first quality in bag
      expect(sizeBreakdown.bagsSecond).toBe(2); // 25 + 10 + 5 = 40 pieces (2 bags)
      expect(sizeBreakdown.loosePieces).toBe(0);
    });

    it('should handle loose pieces correctly', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-3',
        details: [
          {
            orderDetailId: 'detail-3',
            sizeName: 'S',
            quantityFirst: 57,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      const sizeBreakdown = result.data!.sizeBreakdown[0];
      expect(sizeBreakdown.boxes).toBe(1);
      expect(sizeBreakdown.bagsFirst).toBe(0);
      expect(sizeBreakdown.loosePieces).toBe(7); // 57 - 50 = 7
    });
  });

  describe('Multiple Sizes', () => {
    it('should group quantities by size correctly', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-4',
        details: [
          {
            orderDetailId: 'detail-4a',
            sizeName: 'M',
            quantityFirst: 30,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          },
          {
            orderDetailId: 'detail-4b',
            sizeName: 'M',
            quantityFirst: 20,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          },
          {
            orderDetailId: 'detail-4c',
            sizeName: 'L',
            quantityFirst: 25,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      expect(result.data!.sizeBreakdown).toHaveLength(2);
      
      const mSize = result.data!.sizeBreakdown.find(s => s.size === 'M');
      const lSize = result.data!.sizeBreakdown.find(s => s.size === 'L');
      
      expect(mSize).toBeDefined();
      expect(mSize!.totalQuantity).toBe(50); // 30 + 20
      expect(mSize!.boxes).toBe(1);
      
      expect(lSize).toBeDefined();
      expect(lSize!.totalQuantity).toBe(25);
      expect(lSize!.bagsFirst).toBe(1);
    });

    it('should sort sizes in correct order', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-5',
        details: [
          { orderDetailId: 'd1', sizeName: 'XL', quantityFirst: 10, quantitySecond: 0, quantityStained: 0, quantityIncomplete: 0 },
          { orderDetailId: 'd2', sizeName: 'S', quantityFirst: 10, quantitySecond: 0, quantityStained: 0, quantityIncomplete: 0 },
          { orderDetailId: 'd3', sizeName: 'M', quantityFirst: 10, quantitySecond: 0, quantityStained: 0, quantityIncomplete: 0 },
          { orderDetailId: 'd4', sizeName: 'XS', quantityFirst: 10, quantitySecond: 0, quantityStained: 0, quantityIncomplete: 0 },
          { orderDetailId: 'd5', sizeName: 'L', quantityFirst: 10, quantitySecond: 0, quantityStained: 0, quantityIncomplete: 0 }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      const sizes = result.data!.sizeBreakdown.map(s => s.size);
      expect(sizes).toEqual(['XS', 'S', 'M', 'L', 'XL']);
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero quantities', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-6',
        details: [
          {
            orderDetailId: 'detail-6',
            sizeName: 'M',
            quantityFirst: 0,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      expect(result.data!.sizeBreakdown).toHaveLength(1);
      
      const sizeBreakdown = result.data!.sizeBreakdown[0];
      expect(sizeBreakdown.totalQuantity).toBe(0);
      expect(sizeBreakdown.boxes).toBe(0);
      expect(sizeBreakdown.bagsFirst).toBe(0);
      expect(sizeBreakdown.bagsSecond).toBe(0);
      expect(sizeBreakdown.loosePieces).toBe(0);
    });

    it('should handle very large quantities', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-7',
        details: [
          {
            orderDetailId: 'detail-7',
            sizeName: 'L',
            quantityFirst: 527,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      const sizeBreakdown = result.data!.sizeBreakdown[0];
      expect(sizeBreakdown.boxes).toBe(10); // 500 pieces
      expect(sizeBreakdown.bagsFirst).toBe(1); // 25 pieces
      expect(sizeBreakdown.loosePieces).toBe(2); // 2 pieces
    });

    it('should handle custom packaging configurations', async () => {
      const input: z.infer<typeof PackagingInputSchema> = {
        orderId: 'order-8',
        details: [
          {
            orderDetailId: 'detail-8',
            sizeName: 'M',
            quantityFirst: 100,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 100,
          piecesPerBag: 50
        }
      };

      const result = await calculatePackagingAutomatically(input);

      expect(result.success).toBe(true);
      const sizeBreakdown = result.data!.sizeBreakdown[0];
      expect(sizeBreakdown.boxes).toBe(1);
      expect(sizeBreakdown.bagsFirst).toBe(0);
      expect(sizeBreakdown.loosePieces).toBe(0);
    });
  });

  describe('Validation', () => {
    it('should reject negative quantities', async () => {
      const input = {
        orderId: 'order-9',
        details: [
          {
            orderDetailId: 'detail-9',
            sizeName: 'M',
            quantityFirst: -10,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 50,
          piecesPerBag: 25
        }
      };

      const result = await calculatePackagingAutomatically(input as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('must be greater than or equal to 0');
    });

    it('should reject invalid packaging configuration', async () => {
      const input = {
        orderId: 'order-10',
        details: [
          {
            orderDetailId: 'detail-10',
            sizeName: 'M',
            quantityFirst: 100,
            quantitySecond: 0,
            quantityStained: 0,
            quantityIncomplete: 0
          }
        ],
        packagingConfig: {
          piecesPerBox: 0,
          piecesPerBag: 0
        }
      };

      const result = await calculatePackagingAutomatically(input as any);

      expect(result.success).toBe(false);
      expect(result.error).toContain('must be greater than 0');
    });
  });
});