"use server";

import { db } from "@/shared/lib/db";

import { AssignmentResponse } from "../schemas/schema";

/**
 * Get assignments by IDs - Version 2 without deletedAt
 */
export async function getAssignmentsByIdsV2(
    ids: string[],
): Promise<AssignmentResponse> {
    try {
        console.log("getAssignmentsByIdsV2 called with:", ids);

        if (!ids || ids.length === 0) {
            return {
                success: false,
                error: "No se proporcionaron IDs de asignaciones",
            };
        }

        const assignments = await db.assignment.findMany({
            where: {
                id: {
                    in: ids,
                },
                // NO deletedAt field here
            },
            include: {
                garmentSize: {
                    include: {
                        size: true,
                        garment: {
                            include: {
                                model: true,
                                color: true,
                            },
                        },
                    },
                },
                order: {
                    select: {
                        id: true,
                        cutOrder: true,
                        createdAt: true,
                        parts: {
                            select: {
                                id: true,
                                code: true,
                            },
                        },
                    },
                },
                contractor: {
                    select: {
                        id: true,
                        name: true,
                        firstName: true,
                        lastName: true,
                        middleName: true,
                        secondLastName: true,
                        notes: true,
                    },
                },
            },
        });

        return {
            success: true,
            data: {
                assignments,
            } as any,
        };
    } catch (error) {
        console.error("Error fetching assignments by IDs V2:", error);

        return {
            success: false,
            error: "Error al obtener las asignaciones",
        };
    }
}
