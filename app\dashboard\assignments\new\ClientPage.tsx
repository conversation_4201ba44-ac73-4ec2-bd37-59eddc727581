"use client";

import React from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

import EnhancedAssignmentWizard from "@/features/assignments/components/wizard/EnhancedAssignmentWizard";
import { DashboardShell } from "@/shared/components/layout/Shell";
import { DashboardHeader } from "@/shared/components/layout/Header";

import { WizardProvider } from "./wizard-context";

export default function ClientPage() {
    return (
        <DashboardShell>
            <DashboardHeader
                heading="Nueva Asignación de Trabajo"
                text="Asigna trabajo a contratistas seleccionando órdenes, prendas, tallas y cantidades."
            >
                <Link href="/dashboard/assignments">
                    <Button
                        className="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                        color="default"
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        variant="flat"
                    >
                        Volver a Asignaciones
                    </Button>
                </Link>
            </DashboardHeader>

            <div className="grid gap-10 lg:gap-12">
                <WizardProvider>
                    <EnhancedAssignmentWizard />
                </WizardProvider>
            </div>
        </DashboardShell>
    );
}
