"use client";

import { useEffect } from "react";

import { refreshCSRFToken } from "@/lib/auth/csrf-helper";

/**
 * Hook to ensure CSRF token is available on page load
 * This helps prevent CSRF errors during authentication
 */
export function useCSRFToken() {
    useEffect(() => {
        // Check if we have a CSRF token on mount
        const initializeCSRF = async () => {
            const cookies = document.cookie.split(";");
            const hasCSRFToken = cookies.some((cookie) => {
                const [name] = cookie.trim().split("=");

                return (
                    name === "authjs.csrf-token" ||
                    name === "next-auth.csrf-token"
                );
            });

            // If no CSRF token exists, fetch one
            if (!hasCSRFToken) {
                console.log("No CSRF token found on page load, fetching...");
                await refreshCSRFToken();
            }
        };

        initializeCSRF();
    }, []);
}
