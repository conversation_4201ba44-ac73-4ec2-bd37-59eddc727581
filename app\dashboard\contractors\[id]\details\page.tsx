"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { use } from "react";
import {
    <PERSON>,
    CardHeader,
    CardBody,
    Button,
    Link,
    Spinner,
    Avatar,
    Chip,
    Badge,
} from "@heroui/react";
import {
    UserIcon,
    ArrowLeftIcon,
    HomeIcon,
    UsersIcon,
    ChevronRightIcon,
    CalendarIcon,
    ClipboardDocumentCheckIcon,
    ClipboardDocumentListIcon,
    CheckBadgeIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";

import {
    useContractor,
    useDeleteContractor,
} from "@/features/contractors/hooks/useContractor";
import EmptyState from "@/shared/components/ui/EmptyState";
import { Contractor } from "@/features/contractors/types";

// Animaciones
const pageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            staggerChildren: 0.05,
            ease: [0.22, 1, 0.36, 1],
        },
    },
    exit: {
        opacity: 0,
        y: -10,
        transition: {
            duration: 0.4,
            ease: [0.22, 1, 0.36, 1],
        },
    },
};

// Añadir la animación de spin lento a Tailwind
const spinSlowAnimation = `@keyframes spin-slow {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}`;

// Añadir el estilo al documento
if (typeof document !== "undefined") {
    const existingStyle = document.getElementById("spin-slow-style");

    if (!existingStyle) {
        const style = document.createElement("style");

        style.id = "spin-slow-style";
        style.textContent = spinSlowAnimation;
        document.head.appendChild(style);

        // Añadir la clase a Tailwind
        const animationStyle = document.createElement("style");

        animationStyle.textContent = `.animate-spin-slow { animation: spin-slow 20s linear infinite; }`;
        document.head.appendChild(animationStyle);
    }
}

const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: [0.22, 1, 0.36, 1],
        },
    },
};

const fadeInVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
};

// Definir el tipo para los parámetros de la página
type ContractorDetailsParams = {
    id: string;
};

export default function ContractorDetailsPage({
    params,
}: {
    params: ContractorDetailsParams | Promise<ContractorDetailsParams>;
}) {
    const router = useRouter();

    // Si params ya es un objeto (no una promesa), úsalo directamente
    // En caso contrario, desenvolver la promesa con use()
    const { id } =
        params instanceof Promise || Symbol.asyncIterator in Object(params)
            ? use(params as Promise<ContractorDetailsParams>)
            : (params as ContractorDetailsParams);

    // Estados
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    // Hooks
    const { contractor, isLoading, error } = useContractor(id);
    const { remove } = useDeleteContractor();
    // Variable para manejar el estado de eliminación
    const [isDeleting, setIsDeleting] = useState(false);

    // Type guard para verificar si estamos trabajando con un contratista único y no un array
    const isContractor = (value: any): value is Contractor => {
        return (
            value &&
            typeof value === "object" &&
            !Array.isArray(value) &&
            "id" in value
        );
    };

    // Verificar si contractor es un objeto de tipo Contractor válido
    const validContractor =
        contractor && isContractor(contractor) ? contractor : null;

    // Manejar eliminación
    const handleDelete = async () => {
        try {
            setIsDeleting(true);
            await remove(id);
            router.push("/dashboard/contractors");
        } catch (error) {
            // REMOVED: console.error("Error al eliminar contratista:", error);
            setIsConfirmingDelete(false);
        } finally {
            setIsDeleting(false);
        }
    };

    // Renderizar iniciales y nombre completo
    const renderAvatar = () => {
        if (!validContractor) return null;

        const firstName = validContractor.firstName || "";
        const middleName = validContractor.middleName || "";
        const lastName = validContractor.lastName || "";
        const secondLastName = validContractor.secondLastName || "";

        const initials =
            `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
        const fullName = `${firstName} ${middleName}`.trim();
        const fullLastName = `${lastName} ${secondLastName}`.trim();

        return (
            <div className="flex flex-col items-center">
                <motion.div
                    animate={{ scale: 1, opacity: 1 }}
                    className="relative"
                    initial={{ scale: 0.8, opacity: 0 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                    <div className="absolute -inset-1.5 bg-gradient-to-r from-primary/20 via-blue-500/20 to-primary/20 rounded-full blur-sm animate-pulse" />
                    <div className="absolute -inset-3 bg-gradient-to-r from-primary/10 via-transparent to-blue-500/10 rounded-full animate-spin-slow" />
                    <Avatar
                        isBordered
                        className="w-24 h-24 text-2xl font-bold bg-gradient-to-br from-primary-100 to-blue-100 dark:from-primary-900/30 dark:to-blue-800/30 border-primary/20 relative z-10"
                        name={initials}
                    />
                </motion.div>
                <div className="relative mt-4">
                    <div className="absolute -inset-x-4 -inset-y-2 bg-gradient-to-r from-primary/5 via-blue-500/5 to-primary/5 rounded-lg blur-sm" />
                    <motion.h2
                        animate={{ y: 0, opacity: 1 }}
                        className="text-2xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent relative z-10"
                        initial={{ y: 10, opacity: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        {fullName}
                    </motion.h2>
                    <motion.h3
                        animate={{ y: 0, opacity: 1 }}
                        className="text-xl text-gray-600 dark:text-gray-300 relative z-10"
                        initial={{ y: 10, opacity: 0 }}
                        transition={{ delay: 0.2 }}
                    >
                        {fullLastName}
                    </motion.h3>
                </div>
            </div>
        );
    };

    return (
        <motion.div
            animate="visible"
            className="container mx-auto px-4 py-6"
            initial="hidden"
            variants={pageVariants}
        >
            {/* Navegación */}
            <motion.div className="mb-6" variants={itemVariants}>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <Link
                        className="hover:text-primary transition-colors flex items-center"
                        href="/dashboard"
                    >
                        <HomeIcon className="h-4 w-4 mr-1" />
                        Inicio
                    </Link>
                    <ChevronRightIcon className="h-3 w-3" />
                    <Link
                        className="hover:text-primary transition-colors flex items-center"
                        href="/dashboard/contractors"
                    >
                        <UsersIcon className="h-4 w-4 mr-1" />
                        Contratistas
                    </Link>
                    <ChevronRightIcon className="h-3 w-3" />
                    <span className="text-gray-700 dark:text-gray-300 font-medium flex items-center">
                        <UserIcon className="h-4 w-4 mr-1" />
                        Detalles
                    </span>
                </div>

                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <div>
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent">
                            Detalles del Contratista
                        </h1>
                        <p className="text-gray-500 dark:text-gray-400 mt-1 max-w-xl">
                            Información detallada del contratista y sus
                            asignaciones
                        </p>
                    </div>

                    <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <Button
                            className="relative overflow-hidden bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30"
                            startContent={
                                <ArrowLeftIcon className="h-4 w-4 text-primary/90" />
                            }
                            variant="flat"
                            onPress={() =>
                                router.push("/dashboard/contractors")
                            }
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 opacity-0 hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <span className="relative z-10 font-medium">
                                Volver al listado
                            </span>
                        </Button>
                    </motion.div>
                </div>
            </motion.div>

            {/* Contenido principal */}
            <AnimatePresence mode="wait">
                {isLoading ? (
                    <motion.div
                        key="loading"
                        animate="visible"
                        className="flex flex-col items-center justify-center py-12"
                        exit="hidden"
                        initial="hidden"
                        variants={fadeInVariants}
                    >
                        <Spinner color="primary" size="lg" />
                        <p className="mt-4 text-gray-500 dark:text-gray-400">
                            Cargando información del contratista...
                        </p>
                    </motion.div>
                ) : error || !validContractor ? (
                    <motion.div
                        key="error"
                        animate="visible"
                        exit="hidden"
                        initial="hidden"
                        variants={fadeInVariants}
                    >
                        <EmptyState
                            buttonIcon={<ArrowLeftIcon className="h-5 w-5" />}
                            buttonText="Volver al listado"
                            description="No se pudo encontrar la información del contratista solicitado. Es posible que haya sido eliminado o que no exista."
                            title="Contratista no encontrado"
                            onAction={() =>
                                router.push("/dashboard/contractors")
                            }
                        />
                    </motion.div>
                ) : (
                    <motion.div
                        key="content"
                        animate="visible"
                        className="grid grid-cols-1 md:grid-cols-3 gap-6"
                        exit="hidden"
                        initial="hidden"
                        variants={fadeInVariants}
                    >
                        {/* Columna izquierda - Información básica */}
                        <div className="md:col-span-1">
                            <motion.div variants={itemVariants}>
                                <Card className="shadow-lg border-none overflow-hidden bg-gradient-to-b from-white to-blue-50/30 dark:from-gray-900 dark:to-blue-900/10 hover:shadow-xl transition-all duration-300">
                                    <CardHeader className="flex flex-col items-center p-6 bg-gradient-to-r from-blue-50/80 via-indigo-50/60 to-blue-50/80 dark:from-blue-900/30 dark:via-indigo-900/20 dark:to-blue-900/30 relative overflow-hidden">
                                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-blue-500/5 animate-pulse" />
                                        {renderAvatar()}
                                    </CardHeader>
                                    <CardBody className="p-6">
                                        <div className="space-y-4">
                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <UserIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Nombre completo
                                                </h3>
                                                <p className="text-lg font-semibold">
                                                    {validContractor
                                                        ? `${validContractor.firstName} ${validContractor.lastName}`
                                                        : ""}
                                                </p>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <CalendarIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Fecha de registro
                                                </h3>
                                                <p className="text-base">
                                                    {validContractor &&
                                                        format(
                                                            new Date(
                                                                validContractor.createdAt,
                                                            ),
                                                            "PPP",
                                                            { locale: es },
                                                        )}
                                                </p>
                                                <p className="text-sm">
                                                    {validContractor &&
                                                        formatDistanceToNow(
                                                            new Date(
                                                                validContractor.createdAt,
                                                            ),
                                                            {
                                                                addSuffix: true,
                                                                locale: es,
                                                            },
                                                        )}
                                                </p>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <ClipboardDocumentCheckIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Asignaciones
                                                </h3>
                                                <Badge
                                                    className="text-sm"
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    {validContractor?._count
                                                        ?.assignments || 0}{" "}
                                                    asignaciones
                                                </Badge>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <ClipboardDocumentCheckIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Fecha de última
                                                    actualización
                                                </h3>
                                                <p className="text-sm">
                                                    {validContractor?.updatedAt &&
                                                        formatDistanceToNow(
                                                            new Date(
                                                                validContractor.updatedAt,
                                                            ),
                                                            {
                                                                addSuffix: true,
                                                                locale: es,
                                                            },
                                                        )}
                                                </p>
                                            </div>

                                            <div>
                                                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1 flex items-center">
                                                    <ClipboardDocumentCheckIcon className="h-4 w-4 mr-1 text-primary" />
                                                    Asignaciones
                                                </h3>
                                                <Chip
                                                    className="mt-1"
                                                    color={
                                                        validContractor?._count
                                                            ?.assignments > 0
                                                            ? "success"
                                                            : "warning"
                                                    }
                                                    size="sm"
                                                    startContent={
                                                        validContractor?._count
                                                            ?.assignments >
                                                        0 ? (
                                                            <CheckBadgeIcon className="h-3.5 w-3.5" />
                                                        ) : (
                                                            <ClipboardDocumentListIcon className="h-3.5 w-3.5" />
                                                        )
                                                    }
                                                    variant="flat"
                                                >
                                                    {validContractor?._count
                                                        ?.assignments > 0
                                                        ? `${validContractor._count.assignments} asignaciones`
                                                        : "Sin asignaciones"}
                                                </Chip>
                                            </div>
                                        </div>
                                    </CardBody>
                                </Card>
                            </motion.div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
}
