"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useState } from "react";

/**
 * Optimized QueryClient configuration
 */
function makeQueryClient() {
    return new QueryClient({
        defaultOptions: {
            queries: {
                // Avoid refetching on window focus
                refetchOnWindowFocus: false,
                // Retry failed requests once
                retry: 1,
                // Consider data stale after 5 minutes
                staleTime: 5 * 60 * 1000,
                // Keep data in cache for 10 minutes
                gcTime: 10 * 60 * 1000,
                // Show previous data while fetching
                placeholderData: (previousData: any) => previousData,
            },
            mutations: {
                // Retry failed mutations once
                retry: 1,
            },
        },
    });
}

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
    if (typeof window === "undefined") {
        // Server: always make a new query client
        return makeQueryClient();
    } else {
        // Browser: make a new query client if we don't already have one
        if (!browserQueryClient) browserQueryClient = makeQueryClient();

        return browserQueryClient;
    }
}

export function OptimizedQueryProvider({
    children,
}: {
    children: React.ReactNode;
}) {
    // NOTE: useState is used to ensure we don't re-create on every render
    const [queryClient] = useState(() => getQueryClient());

    return (
        <QueryClientProvider client={queryClient}>
            {children}
            {process.env.NODE_ENV === "development" && (
                <ReactQueryDevtools initialIsOpen={false} />
            )}
        </QueryClientProvider>
    );
}
