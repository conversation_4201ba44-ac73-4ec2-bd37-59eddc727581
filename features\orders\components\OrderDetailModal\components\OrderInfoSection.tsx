"use client";

import type { ExtendedOrder } from "../types/order-modal.types";

import React from "react";
import { Card, CardBody, Badge } from "@heroui/react";
import {
    CalendarIcon,
    ClockIcon,
    UserIcon,
    InformationCircleIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { useFeatureFlag } from "@/shared/hooks/useFeatureFlag";

import {
    useThemeSafe,
    getThemeColors,
    formatDate,
    getCustomerName,
} from "../utils";

import OrderSizesBreakdown from "./OrderSizesBreakdown";

interface OrderInfoSectionProps {
    order: ExtendedOrder;
}

export default function OrderInfoSection({ order }: OrderInfoSectionProps) {
    const theme = useThemeSafe();
    const colors = getThemeColors(theme);
    const showComingSoon = useFeatureFlag("SHOW_COMING_SOON_CARD");
    const showExtendedFields = useFeatureFlag("CUSTOMER_EXTENDED_FIELDS");

    const customerName = getCustomerName(order);
    const receivedDate = formatDate(order.receivedDate);
    const estimatedDate = order.estimatedDeliveryDate
        ? formatDate(order.estimatedDeliveryDate)
        : "No definida";
    const deliveryDate = order.deliveryDate
        ? formatDate(order.deliveryDate)
        : "Pendiente";

    // Only show fields that exist in the database
    const baseInfoItems = [
        {
            icon: UserIcon,
            label: "Cliente",
            value: customerName,
            color: "text-blue-600 dark:text-blue-400",
        },
        {
            icon: CalendarIcon,
            label: "Fecha de Recibido",
            value: receivedDate,
            color: "text-blue-600 dark:text-blue-400",
        },
        {
            icon: ClockIcon,
            label: "Fecha Estimada de Entrega",
            value: estimatedDate,
            color: "text-amber-600 dark:text-amber-400",
            highlight: !order.estimatedDeliveryDate,
        },
        {
            icon: CheckCircleIcon,
            label: "Fecha de Entrega Real",
            value: deliveryDate,
            color: "text-green-600 dark:text-green-400",
            highlight: !order.deliveryDate,
        },
    ];

    // Only add extended fields if feature flag is active (for future use)
    const infoItems = showExtendedFields
        ? [
              ...baseInfoItems,
              // Future fields would be added here when available in DB
          ]
        : baseInfoItems;

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
        >
            {/* Card de información básica */}
            <Card className="border border-default-200">
                <CardBody className="p-6">
                    <h3 className="text-lg font-semibold mb-4 text-foreground">
                        Información General
                    </h3>

                    {/* Single column layout with only existing fields */}
                    <div className="grid grid-cols-1 gap-4">
                        {infoItems.map((item, index) => {
                            const Icon = item.icon;

                            return (
                                <motion.div
                                    key={item.label}
                                    animate={{ opacity: 1, x: 0 }}
                                    className="flex items-start gap-3"
                                    initial={{ opacity: 0, x: -20 }}
                                    transition={{ delay: index * 0.05 }}
                                >
                                    <div className={`mt-0.5 ${item.color}`}>
                                        <Icon className="w-5 h-5" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm text-default-500">
                                            {item.label}
                                        </p>
                                        <p
                                            className={`font-medium ${item.highlight ? "text-warning" : "text-foreground"} truncate`}
                                        >
                                            {item.value}
                                        </p>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </div>
                </CardBody>
            </Card>

            {/* Coming Soon Card - New addition from CREATIVE decision */}
            {showComingSoon && !showExtendedFields && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 10 }}
                    transition={{ delay: 0.3 }}
                >
                    <Card className="border border-dashed border-default-300 bg-default-50/50">
                        <CardBody className="p-4">
                            <div className="flex items-center gap-3 text-default-500">
                                <InformationCircleIcon className="w-5 h-5" />
                                <div>
                                    <p className="text-sm font-medium">
                                        Información adicional próximamente
                                    </p>
                                    <p className="text-xs mt-1 text-default-400">
                                        Pronto podrás ver teléfono, dirección y
                                        empresa del cliente
                                    </p>
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            )}

            {/* Sizes and Quantities Breakdown Section */}
            {order.garments && order.garments.length > 0 && (
                <OrderSizesBreakdown garments={order.garments} />
            )}

            {/* Order Parts Section - Based on CREATIVE decision */}
            {order.parts && order.parts.length > 0 && (
                <Card className="border border-default-200">
                    <CardBody className="p-6">
                        <h3 className="text-lg font-semibold mb-4 text-foreground">
                            Partidas
                        </h3>

                        <div className="space-y-2">
                            {order.parts.map((part, index) => (
                                <motion.div
                                    key={part.id}
                                    animate={{ opacity: 1, x: 0 }}
                                    className="flex items-center gap-2"
                                    initial={{ opacity: 0, x: -20 }}
                                    transition={{ delay: index * 0.05 }}
                                >
                                    <Badge
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {part.code}
                                    </Badge>
                                    <span className="text-sm text-default-700">
                                        Parte {index + 1}
                                    </span>
                                </motion.div>
                            ))}
                        </div>
                    </CardBody>
                </Card>
            )}

            {/* Summary Card - Updated with correct property names */}
            <Card className="border border-default-200">
                <CardBody className="p-6">
                    <h3 className="text-lg font-semibold mb-4 text-foreground">
                        Resumen de la Orden
                    </h3>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                            <p className="text-2xl font-bold text-foreground">
                                {order.garments?.length || 0}
                            </p>
                            <p className="text-sm text-default-500">Prendas</p>
                        </div>

                        <div className="text-center">
                            <p className="text-2xl font-bold text-primary">
                                {order.totalQuantity || 0}
                            </p>
                            <p className="text-sm text-default-500">Unidades</p>
                        </div>

                        <div className="text-center">
                            <p className="text-2xl font-bold text-success">
                                {order.completedQuantity || 0}
                            </p>
                            <p className="text-sm text-default-500">
                                Completadas
                            </p>
                        </div>

                        <div className="text-center">
                            <p className="text-2xl font-bold text-warning">
                                {order.pendingQuantity || 0}
                            </p>
                            <p className="text-sm text-default-500">
                                Pendientes
                            </p>
                        </div>
                    </div>

                    {/* Progress bar for visual reference */}
                    {order.progressPercentage !== undefined && (
                        <div className="mt-4">
                            <div className="flex justify-between text-xs text-default-500 mb-1">
                                <span>Progreso</span>
                                <span>{order.progressPercentage}%</span>
                            </div>
                            <div className="w-full bg-default-200 rounded-full h-2">
                                <div
                                    className="bg-primary rounded-full h-2 transition-all duration-300"
                                    style={{
                                        width: `${order.progressPercentage}%`,
                                    }}
                                />
                            </div>
                        </div>
                    )}
                </CardBody>
            </Card>
        </motion.div>
    );
}
