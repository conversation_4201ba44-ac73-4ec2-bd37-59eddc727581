# 📦 Inventory Feature

## 📋 Descripción
Control de inventario de materiales y productos textiles.

## 🏗️ Componentes
- **InventoryDashboard** - Panel de control
- **StockList** - Lista de inventario
- **StockMovements** - Movimientos de stock
- **LowStockAlerts** - Alertas de bajo stock

## 🪝 Hooks
- `useInventory` - Gestión de inventario
- `useStockMovements` - Historial de movimientos
- `useInventoryAlerts` - Sistema de alertas

## 📊 Características
- Control de stock en tiempo real
- Alertas automáticas
- Historial de movimientos
- Reportes de inventario
- Predicción de necesidades

## 🎯 Uso
```tsx
import { InventoryDashboard, useInventory } from '@/features/inventory'

const { stock, updateStock, lowStockItems } = useInventory()
```