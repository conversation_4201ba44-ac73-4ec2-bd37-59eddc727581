import type { NextRequest } from "next/server";

import { NextResponse } from "next/server";

import { rateLimit } from "@/lib/rate-limit-edge";

// Configuration for different endpoints
const rateLimitConfig = {
    "/api/auth/login": { limit: 5, interval: "15m" },
    "/api/auth/register": { limit: 3, interval: "1h" },
    "/api/auth/reset-password": { limit: 3, interval: "1h" },
    "/api/": { limit: 100, interval: "1m" }, // General API limit
};

// Protected routes that require authentication
const protectedRoutes = ["/dashboard"];

export async function middleware(request: NextRequest) {
    return null;
    const { pathname } = request.nextUrl;

    // Check if the route requires authentication
    const isProtectedRoute = protectedRoutes.some((route) =>
        pathname.startsWith(route),
    );

    if (isProtectedRoute) {
        // Check for session token
        const sessionToken =
            request.cookies.get("authjs.session-token") ||
            request.cookies.get("__Secure-authjs.session-token");

        if (!sessionToken) {
            // No session found, redirect to login with the original URL as callback
            const loginUrl = new URL("/login", request.url);

            loginUrl.searchParams.set("callbackUrl", pathname);

            return NextResponse.redirect(loginUrl);
        }
    }

    // Only apply rate limiting to API routes
    if (pathname.startsWith("/api/")) {
        // Skip rate limiting for NextAuth routes
        if (pathname.startsWith("/api/auth/[...nextauth]")) {
            return NextResponse.next();
        }

        // Find the most specific rate limit config
        let config = rateLimitConfig["/api/"];

        for (const [path, pathConfig] of Object.entries(rateLimitConfig)) {
            if (pathname.startsWith(path) && path !== "/api/") {
                config = pathConfig;
                break;
            }
        }

        const { success, limit, remaining, reset } = await rateLimit.check(
            request,
            config.limit,
            config.interval,
        );

        if (!success) {
            return new NextResponse(
                JSON.stringify({
                    error: "Too many requests",
                    message: `Rate limit exceeded. Try again at ${reset.toISOString()}`,
                }),
                {
                    status: 429,
                    headers: {
                        "Content-Type": "application/json",
                        "X-RateLimit-Limit": limit.toString(),
                        "X-RateLimit-Remaining": remaining.toString(),
                        "X-RateLimit-Reset": reset.toISOString(),
                        "Retry-After": Math.ceil(
                            (reset.getTime() - Date.now()) / 1000,
                        ).toString(),
                    },
                },
            );
        }

        // Add rate limit headers to successful responses
        const response = NextResponse.next();

        response.headers.set("X-RateLimit-Limit", limit.toString());
        response.headers.set("X-RateLimit-Remaining", remaining.toString());
        response.headers.set("X-RateLimit-Reset", reset.toISOString());

        return response;
    }

    // For non-API routes, just continue
    return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
    matcher: [
        // Match API routes
        "/api/:path*",
        // Match protected routes
        "/dashboard/:path*",
    ],
};
