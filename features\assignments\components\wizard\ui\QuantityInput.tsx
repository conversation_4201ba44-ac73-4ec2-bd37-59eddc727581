"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { Input, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@heroui/react";
import {
    ExclamationCircleIcon,
    QuestionMarkCircleIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    SparklesIcon,
    ArrowTrendingUpIcon,
    ArrowTrendingDownIcon,
} from "@heroicons/react/24/outline";
import {
    motion,
    AnimatePresence,
    useSpring,
    useTransform,
} from "framer-motion";

import { useOptimisticValidation } from "@/features/assignments/hooks";
import {
    ContextualTooltip,
    tooltipContexts,
} from "@/features/assignments/components/wizard/ui/ContextualTooltip";

export interface QuantityInputProps {
    garmentSizeId: string;
    initialValue?: number;
    contractorId?: string;
    onChange: (value: number) => void;
    onValidationChange?: (isValid: boolean) => void;
    max?: number;
    className?: string;
    label?: string;
    showSuggestions?: boolean;
}

export function QuantityInput({
    garmentSizeId,
    initialValue = 0,
    contractorId,
    onChange,
    onValidationChange,
    max,
    className = "",
    label = "Cantidad",
    showSuggestions = true,
}: QuantityInputProps) {
    const [value, setValue] = useState<number>(initialValue);
    const [debouncedValue, setDebouncedValue] = useState<number>(initialValue);
    const [showSuggestionsPanel, setShowSuggestionsPanel] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const prevValidState = useRef<boolean | null>(null);

    const {
        validation,
        suggestions,
        validateQuantity,
        getSuggestions,
        resetValidation,
    } = useOptimisticValidation();

    // Manejar cambios en el valor con debounce
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);

            // Solo validar si hay un valor positivo
            if (value > 0) {
                validateQuantity(garmentSizeId, value);
            } else {
                resetValidation();
            }
        }, 300);

        return () => clearTimeout(timer);
    }, [value, garmentSizeId, validateQuantity, resetValidation]);

    // Notificar cambio en la validación
    useEffect(() => {
        if (
            onValidationChange &&
            prevValidState.current !== validation.isValid
        ) {
            prevValidState.current = validation.isValid;
            onValidationChange(validation.isValid);
        }
    }, [validation.isValid, onValidationChange]);

    // Obtener sugerencias de cantidad al montar el componente
    useEffect(() => {
        if (contractorId && showSuggestions) {
            getSuggestions(garmentSizeId, contractorId);
        }
    }, [garmentSizeId, contractorId, getSuggestions, showSuggestions]);

    // Manejar cambio manual de valor
    const handleChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const newValue = parseInt(e.target.value, 10) || 0;

            setValue(newValue);
            onChange(newValue);
        },
        [onChange],
    );

    // Aplicar la sugerencia de cantidad
    const applySuggestion = useCallback(() => {
        if (suggestions.suggestedQuantity > 0) {
            setValue(suggestions.suggestedQuantity);
            onChange(suggestions.suggestedQuantity);
        }
    }, [suggestions.suggestedQuantity, onChange]);

    // Aplicar el valor máximo disponible
    const applyMaxValue = useCallback(() => {
        if (validation.availableQuantity) {
            setValue(validation.availableQuantity);
            onChange(validation.availableQuantity);
        } else if (max) {
            setValue(max);
            onChange(max);
        }
    }, [validation.availableQuantity, onChange, max]);

    // Determinar el estado visual del componente
    const getInputStatus = () => {
        if (validation.isLoading) return "default";
        if (!validation.isValid) return "danger";
        if (debouncedValue > 0 && validation.isValid) return "success";

        return "default";
    };

    // Spring animation for value changes
    const springValue = useSpring(value, { stiffness: 300, damping: 30 });
    const displayValue = useTransform(springValue, (v) => Math.round(v));

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={`relative ${className}`}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, type: "spring" }}
        >
            <div className="p-4 rounded-2xl bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-xl">
                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                        <motion.div
                            className="p-2 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm"
                            whileHover={{ scale: 1.1, rotate: 5 }}
                        >
                            <SparklesIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </motion.div>
                        <div>
                            <label className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                {label}
                            </label>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                {validation.availableQuantity
                                    ? `${validation.availableQuantity} disponibles`
                                    : "Ingresa cantidad"}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <ContextualTooltip
                            content={tooltipContexts.quantityInput.text}
                            variant={tooltipContexts.quantityInput.variant}
                        />
                        <AnimatePresence mode="wait">
                            {validation.isLoading && (
                                <motion.div
                                    animate={{
                                        opacity: 1,
                                        scale: 1,
                                        rotate: 360,
                                    }}
                                    exit={{ opacity: 0, scale: 0.8 }}
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    transition={{
                                        duration: 0.5,
                                        rotate: {
                                            duration: 1,
                                            repeat: Infinity,
                                            ease: "linear",
                                        },
                                    }}
                                >
                                    <div className="p-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500">
                                        <Spinner color="white" size="sm" />
                                    </div>
                                </motion.div>
                            )}
                            {!validation.isValid && !validation.isLoading && (
                                <motion.div
                                    animate={{ opacity: 1, scale: 1 }}
                                    exit={{ opacity: 0, scale: 0.8 }}
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    transition={{ duration: 0.15 }}
                                >
                                    <Tooltip
                                        color="danger"
                                        content={
                                            validation.error ||
                                            "Error de validación"
                                        }
                                    >
                                        <motion.div
                                            animate={{
                                                rotate: [0, -10, 10, -10, 0],
                                            }}
                                            className="p-2 rounded-full bg-red-500/20 backdrop-blur-sm"
                                            transition={{ duration: 0.5 }}
                                        >
                                            <ExclamationCircleIcon className="w-5 h-5 text-red-500" />
                                        </motion.div>
                                    </Tooltip>
                                </motion.div>
                            )}
                            {debouncedValue > 0 &&
                                validation.isValid &&
                                !validation.isLoading && (
                                    <motion.div
                                        animate={{ opacity: 1, scale: 1 }}
                                        exit={{ opacity: 0, scale: 0.8 }}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        transition={{ duration: 0.15 }}
                                    >
                                        <Tooltip
                                            color="success"
                                            content="Cantidad válida"
                                        >
                                            <motion.div
                                                animate={{ scale: [1, 1.2, 1] }}
                                                className="p-2 rounded-full bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm"
                                                transition={{ duration: 0.3 }}
                                            >
                                                <CheckCircleIcon className="w-5 h-5 text-green-500" />
                                            </motion.div>
                                        </Tooltip>
                                    </motion.div>
                                )}
                        </AnimatePresence>
                    </div>
                </div>

                <div className="relative">
                    <motion.div
                        animate={{
                            scale: isFocused ? 1.02 : 1,
                            y: isFocused ? -2 : 0,
                        }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                        }}
                    >
                        <Input
                            classNames={{
                                base: "w-full",
                                input: "text-center font-bold text-2xl transition-all duration-200",
                                inputWrapper: `
                                    bg-gradient-to-r 
                                    ${
                                        !validation.isValid
                                            ? "from-red-500/10 to-pink-500/10 border-red-500/50 animate-pulse"
                                            : validation.isValid &&
                                                debouncedValue > 0
                                              ? "from-green-500/10 to-emerald-500/10 border-green-500/50"
                                              : "from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900"
                                    }
                                    backdrop-blur-sm
                                    hover:shadow-lg
                                    transition-all
                                    duration-300
                                `,
                            }}
                            color={getInputStatus()}
                            endContent={
                                <div className="flex items-center gap-2">
                                    {validation.availableQuantity && (
                                        <motion.div
                                            animate={{ opacity: 1, scale: 1 }}
                                            className="text-xs font-medium text-gray-500"
                                            initial={{ opacity: 0, scale: 0 }}
                                        >
                                            /{validation.availableQuantity}
                                        </motion.div>
                                    )}
                                    {showSuggestions && (
                                        <Tooltip content="Ver sugerencias inteligentes">
                                            <motion.button
                                                className="p-1.5 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30 transition-all"
                                                type="button"
                                                whileHover={{ scale: 1.1 }}
                                                whileTap={{ scale: 0.95 }}
                                                onClick={() =>
                                                    setShowSuggestionsPanel(
                                                        !showSuggestionsPanel,
                                                    )
                                                }
                                            >
                                                <QuestionMarkCircleIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                            </motion.button>
                                        </Tooltip>
                                    )}
                                </div>
                            }
                            max={validation.availableQuantity || max}
                            min={0}
                            startContent={
                                <motion.div
                                    animate={{ rotate: value > 0 ? 360 : 0 }}
                                    className="pointer-events-none flex items-center"
                                    transition={{
                                        duration: 0.5,
                                        type: "spring",
                                    }}
                                >
                                    <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20">
                                        <span className="text-blue-600 dark:text-blue-400 font-bold">
                                            #
                                        </span>
                                    </div>
                                </motion.div>
                            }
                            type="number"
                            value={value.toString()}
                            onBlur={() => setIsFocused(false)}
                            onChange={handleChange}
                            onFocus={() => setIsFocused(true)}
                        />
                    </motion.div>

                    {/* Progress indicator */}
                    {validation.availableQuantity && (
                        <motion.div
                            animate={{ scaleX: 1 }}
                            className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
                            initial={{ scaleX: 0 }}
                        >
                            <motion.div
                                animate={{
                                    width: `${(value / validation.availableQuantity) * 100}%`,
                                }}
                                className={`h-full rounded-full bg-gradient-to-r ${
                                    value > validation.availableQuantity
                                        ? "from-red-500 to-pink-500"
                                        : value >=
                                            validation.availableQuantity * 0.8
                                          ? "from-amber-500 to-orange-500"
                                          : "from-blue-500 to-purple-500"
                                }`}
                                transition={{
                                    type: "spring",
                                    stiffness: 300,
                                    damping: 30,
                                }}
                            />
                        </motion.div>
                    )}
                </div>

                <AnimatePresence mode="wait">
                    {validation.error && (
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-1 text-xs text-danger"
                            exit={{ opacity: 0, y: -5 }}
                            initial={{ opacity: 0, y: -5 }}
                            transition={{ duration: 0.2 }}
                        >
                            <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{ duration: 0.4, repeat: 2 }}
                            >
                                <ExclamationCircleIcon className="w-4 h-4" />
                            </motion.div>
                            <span>{validation.error}</span>
                        </motion.div>
                    )}
                </AnimatePresence>

                <AnimatePresence>
                    {showSuggestionsPanel && (
                        <motion.div
                            animate={{ opacity: 1, y: 0, scale: 1 }}
                            className="mt-3 p-4 rounded-xl bg-gradient-to-br from-blue-50/80 to-purple-50/80 dark:from-blue-900/20 dark:to-purple-900/20 backdrop-blur-xl border border-blue-200/30 dark:border-blue-700/30 shadow-xl"
                            exit={{ opacity: 0, y: -10, scale: 0.95 }}
                            initial={{ opacity: 0, y: -10, scale: 0.95 }}
                            transition={{
                                type: "spring",
                                stiffness: 300,
                                damping: 25,
                            }}
                        >
                            <div className="flex justify-between items-center mb-3">
                                <div className="flex items-center gap-2">
                                    <motion.div
                                        animate={{ rotate: [0, 10, -10, 0] }}
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                        }}
                                    >
                                        <SparklesIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                    </motion.div>
                                    <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                        Sugerencias Inteligentes
                                    </span>
                                </div>
                                <motion.button
                                    className="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                                    whileHover={{ scale: 1.1, rotate: 90 }}
                                    whileTap={{ scale: 0.9 }}
                                    onClick={() =>
                                        setShowSuggestionsPanel(false)
                                    }
                                >
                                    <ExclamationCircleIcon className="w-4 h-4 text-gray-500" />
                                </motion.button>
                            </div>

                            <div className="flex flex-col gap-2">
                                {suggestions.isLoading ? (
                                    <div className="flex justify-center p-2">
                                        <Spinner size="sm" />
                                    </div>
                                ) : (
                                    <>
                                        {suggestions.suggestedQuantity > 0 && (
                                            <motion.div
                                                animate={{ opacity: 1, x: 0 }}
                                                className="p-3 rounded-lg bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-500/20"
                                                initial={{ opacity: 0, x: -20 }}
                                            >
                                                <div className="flex justify-between items-center">
                                                    <div className="flex items-center gap-2">
                                                        <motion.div
                                                            animate={{
                                                                scale: [
                                                                    1, 1.2, 1,
                                                                ],
                                                            }}
                                                            className="p-1.5 rounded-full bg-blue-500/20"
                                                            transition={{
                                                                duration: 2,
                                                                repeat: Infinity,
                                                            }}
                                                        >
                                                            <ArrowTrendingUpIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                                        </motion.div>
                                                        <div>
                                                            <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
                                                                Cantidad Óptima
                                                            </span>
                                                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                                                Basado en tu
                                                                historial
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <motion.button
                                                        className="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-medium text-sm shadow-lg hover:shadow-xl transition-all"
                                                        whileHover={{
                                                            scale: 1.05,
                                                        }}
                                                        whileTap={{
                                                            scale: 0.95,
                                                        }}
                                                        onClick={
                                                            applySuggestion
                                                        }
                                                    >
                                                        Usar{" "}
                                                        {
                                                            suggestions.suggestedQuantity
                                                        }
                                                    </motion.button>
                                                </div>
                                            </motion.div>
                                        )}

                                        {validation.availableQuantity && (
                                            <div className="flex justify-between items-center">
                                                <div className="flex items-center gap-1">
                                                    <InformationCircleIcon className="w-4 h-4 text-success" />
                                                    <span className="text-xs">
                                                        Cantidad máxima
                                                    </span>
                                                </div>
                                                <Button
                                                    color="success"
                                                    size="sm"
                                                    variant="flat"
                                                    onClick={applyMaxValue}
                                                >
                                                    Usar{" "}
                                                    {
                                                        validation.availableQuantity
                                                    }
                                                </Button>
                                            </div>
                                        )}

                                        {validation.suggestions
                                            ?.recommendedQuantity &&
                                            !validation.isValid && (
                                                <div className="flex justify-between items-center">
                                                    <div className="flex items-center gap-1">
                                                        <InformationCircleIcon className="w-4 h-4 text-warning" />
                                                        <span className="text-xs">
                                                            Cantidad recomendada
                                                        </span>
                                                    </div>
                                                    <Button
                                                        color="warning"
                                                        size="sm"
                                                        variant="flat"
                                                        onClick={() => {
                                                            const value =
                                                                validation
                                                                    .suggestions
                                                                    ?.recommendedQuantity ||
                                                                0;

                                                            setValue(value);
                                                            onChange(value);
                                                        }}
                                                    >
                                                        Usar{" "}
                                                        {
                                                            validation
                                                                .suggestions
                                                                .recommendedQuantity
                                                        }
                                                    </Button>
                                                </div>
                                            )}

                                        {suggestions.recentAssignments.length >
                                            0 && (
                                            <motion.div
                                                animate={{ opacity: 1 }}
                                                className="mt-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                                                initial={{ opacity: 0 }}
                                                transition={{ delay: 0.2 }}
                                            >
                                                <div className="flex items-center gap-2 mb-2">
                                                    <ArrowTrendingDownIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                                        Historial Reciente
                                                    </span>
                                                </div>
                                                <div className="flex flex-wrap gap-2">
                                                    {suggestions.recentAssignments.map(
                                                        (qty, index) => (
                                                            <motion.button
                                                                key={index}
                                                                animate={{
                                                                    opacity: 1,
                                                                    scale: 1,
                                                                }}
                                                                className="px-3 py-1.5 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 hover:shadow-md transition-all"
                                                                initial={{
                                                                    opacity: 0,
                                                                    scale: 0,
                                                                }}
                                                                transition={{
                                                                    delay:
                                                                        index *
                                                                        0.05,
                                                                }}
                                                                whileHover={{
                                                                    scale: 1.1,
                                                                    y: -2,
                                                                }}
                                                                whileTap={{
                                                                    scale: 0.95,
                                                                }}
                                                                onClick={() => {
                                                                    setValue(
                                                                        qty,
                                                                    );
                                                                    onChange(
                                                                        qty,
                                                                    );
                                                                }}
                                                            >
                                                                <span className="font-medium text-sm">
                                                                    {qty}
                                                                </span>
                                                            </motion.button>
                                                        ),
                                                    )}
                                                </div>
                                            </motion.div>
                                        )}
                                    </>
                                )}
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </motion.div>
    );
}
