import { NextResponse } from "next/server";
import { z } from "zod";

import { prisma } from "@/shared/lib/prisma";
import { hashPassword } from "@/features/auth/utils";

// Schema de validación para registro
const registerSchema = z.object({
    name: z.string().min(1, "El nombre es requerido"),
    email: z.string().email("Email inválido"),
    password: z
        .string()
        .min(6, "La contraseña debe tener al menos 6 caracteres"),
});

export async function POST(request: Request) {
    try {
        const body = await request.json();

        // Validar datos
        const validatedData = registerSchema.parse(body);

        // Verificar si el usuario ya existe
        const existingUser = await prisma.user.findUnique({
            where: { email: validatedData.email },
        });

        if (existingUser) {
            return NextResponse.json(
                { message: "El correo electrónico ya está registrado" },
                { status: 400 },
            );
        }

        // Hash de la contraseña
        const hashedPassword = await hashPassword(validatedData.password);

        // Get USER role
        const userRole = await prisma.role.findFirst({
            where: { name: "USER" },
        });

        if (!userRole) {
            return NextResponse.json(
                {
                    error: "Error de configuración: rol de usuario no encontrado",
                },
                { status: 500 },
            );
        }

        // Crear usuario
        const user = await prisma.user.create({
            data: {
                name: validatedData.name,
                email: validatedData.email,
                password: hashedPassword,
                roleId: userRole.id,
            },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
            },
        });

        return NextResponse.json({
            message: "Usuario registrado exitosamente",
            user,
        });
    } catch (error) {
        if (error instanceof z.ZodError) {
            return NextResponse.json(
                { message: "Datos inválidos", errors: error.errors },
                { status: 400 },
            );
        }

        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            { message: "Error al registrar usuario" },
            { status: 500 },
        );
    }
}
