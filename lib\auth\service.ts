import { User, LoginCredentials, RegisterData, AuthResponse } from "./types";

// Simulación de API service - Reemplazar con tu implementación real
class AuthService {
    private baseURL = process.env.NEXT_PUBLIC_API_URL || "/api";
    private tokenKey = "auth_token";
    private refreshTokenKey = "refresh_token";

    // Login
    async login(credentials: LoginCredentials): Promise<AuthResponse> {
        try {
            const response = await fetch(`${this.baseURL}/auth/login`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(credentials),
            });

            if (!response.ok) {
                const error = await response.json();

                throw new Error(error.message || "Error al iniciar sesión");
            }

            const data: AuthResponse = await response.json();

            // Guardar tokens
            this.setTokens(data.accessToken, data.refreshToken);

            return data;
        } catch (error) {
            throw error;
        }
    }

    // Register
    async register(data: RegisterData): Promise<AuthResponse> {
        try {
            const response = await fetch(`${this.baseURL}/auth/register`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                const error = await response.json();

                throw new Error(error.message || "Error al registrarse");
            }

            const result: AuthResponse = await response.json();

            // Guardar tokens
            this.setTokens(result.accessToken, result.refreshToken);

            return result;
        } catch (error) {
            throw error;
        }
    }

    // Logout
    async logout(): Promise<void> {
        try {
            const token = this.getAccessToken();

            if (token) {
                await fetch(`${this.baseURL}/auth/logout`, {
                    method: "POST",
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });
            }
        } catch (error) {
            console.error("Error during logout:", error);
        } finally {
            this.clearTokens();
        }
    }

    // Get current user
    async getCurrentUser(): Promise<User | null> {
        try {
            const token = this.getAccessToken();

            if (!token) {
                return null;
            }

            const response = await fetch(`${this.baseURL}/auth/me`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                this.clearTokens();

                return null;
            }

            const user: User = await response.json();

            return user;
        } catch (error) {
            this.clearTokens();

            return null;
        }
    }

    // Token management
    private setTokens(accessToken: string, refreshToken?: string) {
        localStorage.setItem(this.tokenKey, accessToken);
        if (refreshToken) {
            localStorage.setItem(this.refreshTokenKey, refreshToken);
        }
    }

    private getAccessToken(): string | null {
        return localStorage.getItem(this.tokenKey);
    }

    private clearTokens() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.refreshTokenKey);
    }

    // Helper para requests autenticados
    async authenticatedRequest(
        url: string,
        options: RequestInit = {},
    ): Promise<Response> {
        const token = this.getAccessToken();

        if (!token) {
            throw new Error("No authentication token");
        }

        return fetch(url, {
            ...options,
            headers: {
                ...options.headers,
                Authorization: `Bearer ${token}`,
            },
        });
    }
}

export const authService = new AuthService();
