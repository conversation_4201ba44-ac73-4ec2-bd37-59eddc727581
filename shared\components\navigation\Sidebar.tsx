"use client";

import { ReactNode, useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
    HomeIcon,
    DocumentTextIcon,
    UserGroupIcon,
    SwatchIcon,
    ShoppingBagIcon,
    ClipboardIcon,
    TagIcon,
    ChevronDoubleLeftIcon,
    ChevronDoubleRightIcon,
    ClipboardDocumentListIcon,
    DocumentDuplicateIcon,
} from "@heroicons/react/24/outline";

import { useNavigation } from "../../../core/context/navigation-context";
import { cn } from "../../lib/client-utils";
import { isNavigationItemActive } from "../../utils/navigation-utils";

interface NavItemProps {
    href: string;
    label: string;
    icon: ReactNode;
    isCollapsed: boolean;
    isActive: boolean;
}

function NavItem({ href, label, icon, isCollapsed, isActive }: NavItemProps) {
    return (
        <li>
            <Link
                className={cn(
                    "flex items-center gap-2 rounded-md transition-colors duration-200",
                    "hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-primary",
                    isActive
                        ? "bg-gray-100 dark:bg-gray-800 text-primary font-medium"
                        : "text-gray-700 dark:text-gray-300",
                    isCollapsed ? "justify-center p-3" : "p-2",
                )}
                href={href}
                title={isCollapsed ? label : undefined}
            >
                <span className="w-5 h-5 flex-shrink-0">{icon}</span>
                {!isCollapsed && <span className="truncate">{label}</span>}
            </Link>
        </li>
    );
}

export default function Sidebar() {
    const { isSidebarOpen, toggleSidebar, isDesktop } = useNavigation();
    const pathname = usePathname();
    const [mounted, setMounted] = useState(false);

    // To avoid hydration mismatch
    useEffect(() => {
        setMounted(true);
    }, []);

    const navItems = [
        { href: "/dashboard", label: "Inicio", icon: <HomeIcon /> },
        {
            href: "/dashboard/orders",
            label: "Pedidos",
            icon: <ShoppingBagIcon />,
        },
        {
            href: "/dashboard/customers",
            label: "Clientes",
            icon: <UserGroupIcon />,
        },
        {
            href: "/dashboard/contractors",
            label: "Contratistas",
            icon: <UserGroupIcon />,
        },
        {
            href: "/dashboard/assignments",
            label: "Asignaciones",
            icon: <ClipboardDocumentListIcon />,
        },
        {
            href: "/dashboard/remissions",
            label: "Remisiones",
            icon: <DocumentDuplicateIcon />,
        },
        { href: "/dashboard/models", label: "Modelos", icon: <TagIcon /> },
        { href: "/dashboard/colors", label: "Colores", icon: <SwatchIcon /> },
        { href: "/dashboard/sizes", label: "Tallas", icon: <TagIcon /> },
        {
            href: "/dashboard/notes",
            label: "Notas",
            icon: <DocumentTextIcon />,
        },
        { href: "/dashboard/todos", label: "Tareas", icon: <ClipboardIcon /> },
    ];

    if (!mounted) return null;

    return (
        <aside
            className={cn(
                "h-full bg-white dark:bg-gray-900 border-r dark:border-gray-800",
                "transition-all duration-300 ease-in-out flex flex-col shadow-lg",
                "flex-shrink-0 z-sidebar",
                isSidebarOpen ? "w-64" : "w-20",
            )}
        >
            {/* Header de la sidebar */}
            <div
                className={cn(
                    "border-b dark:border-gray-800 flex items-center bg-white dark:bg-gray-900",
                    "h-16 flex-shrink-0",
                    isSidebarOpen
                        ? "px-4 justify-between"
                        : "px-3 justify-center",
                )}
            >
                {isSidebarOpen ? (
                    <>
                        <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                            Dashboard
                        </h1>
                        {isDesktop && (
                            <button
                                aria-label="Colapsar menú"
                                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 
                                         dark:hover:text-gray-200 focus:outline-none focus:ring-2 
                                         focus:ring-primary focus:ring-offset-2 
                                         dark:focus:ring-offset-gray-900 rounded p-1"
                                onClick={toggleSidebar}
                            >
                                <ChevronDoubleLeftIcon className="w-5 h-5" />
                            </button>
                        )}
                    </>
                ) : (
                    <button
                        aria-label="Expandir menú"
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 
                                 dark:hover:text-gray-200 focus:outline-none focus:ring-2 
                                 focus:ring-primary focus:ring-offset-2 
                                 dark:focus:ring-offset-gray-900 rounded p-1"
                        onClick={toggleSidebar}
                    >
                        <ChevronDoubleRightIcon className="w-5 h-5" />
                    </button>
                )}
            </div>

            {/* Contenido de navegación con scroll interno */}
            <div className="flex-1 overflow-y-auto">
                <nav className={cn("p-4", !isSidebarOpen && "p-2")}>
                    <ul className="space-y-1">
                        {navItems.map((item) => (
                            <NavItem
                                key={item.href}
                                href={item.href}
                                icon={item.icon}
                                isActive={isNavigationItemActive(
                                    pathname,
                                    item.href,
                                )}
                                isCollapsed={!isSidebarOpen}
                                label={item.label}
                            />
                        ))}
                    </ul>
                </nav>
            </div>
        </aside>
    );
}
