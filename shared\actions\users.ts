"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { Prisma } from "@prisma/client";

import { db, handleDbError } from "@/shared/lib/db";

// import { hashPassword } from "@/shared/lib/server-utils"; // Comment out - module doesn't exist
const hashPassword = async (password: string) => password; // Simple fallback

// Esquema de validación para usuarios
const userSchema = z.object({
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(100, "El nombre no puede exceder los 100 caracteres"),
    email: z.string().email("Email inválido"),
    role: z.enum(["ADMIN", "EMPLOYEE", "CONTRACTOR", "GUEST"]),
    password: z
        .string()
        .min(6, "La contraseña debe tener al menos 6 caracteres")
        .optional(),
});

// Valores de rol válidos
const VALID_ROLES = ["ADMIN", "EMPLOYEE", "CONTRACTOR", "GUEST"];

// Obtener todos los usuarios con opciones de filtrado y paginación
export async function getUsers({
    search = "",
    role = "",
    sortBy = "createdAt",
    order = "desc" as "asc" | "desc",
    page = 1,
    limit = 10,
} = {}) {
    try {
        // Construir el where para filtrado
        const where: Prisma.UserWhereInput = {};

        if (search) {
            where.OR = [
                { name: { contains: search, mode: "insensitive" } },
                { email: { contains: search, mode: "insensitive" } },
            ];
        }

        if (role && VALID_ROLES.includes(role)) {
            where.role = { name: role };
        }

        // Obtener usuarios con paginación
        const skip = (page - 1) * limit;

        // Ejecutar consultas secuencialmente en lugar de con Promise.all
        // para evitar problemas con prepared statements
        const users = await db.user.findMany({
            where,
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                createdAt: true,
                _count: {
                    select: {
                        notes: true,
                    },
                },
            },
            orderBy: {
                [sortBy]: order,
            },
            skip,
            take: limit,
        });

        // Realizar el conteo después de obtener los usuarios
        const total = await db.user.count({ where });

        // Calcular información de paginación
        const lastPage = Math.ceil(total / limit);

        // Serializar fechas para evitar problemas de serialización
        const serializedUsers = users.map((user) => ({
            ...user,
            createdAt: user.createdAt.toISOString(),
        }));

        return {
            success: true,
            data: {
                users: serializedUsers,
                pagination: {
                    total,
                    currentPage: page,
                    lastPage,
                },
            },
        };
    } catch (error) {
        // REMOVED: console.error("Error al obtener usuarios:", error);

        return handleDbError(error, "Error al obtener usuarios");
    }
}

// Obtener un usuario específico por ID
export async function getUser(id: string) {
    try {
        // Validar ID
        if (!id) {
            return {
                success: false,
                error: "ID de usuario inválido",
            };
        }

        // Buscar usuario
        const user = await db.user.findUnique({
            where: { id },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                createdAt: true,
                _count: {
                    select: {
                        notes: true,
                    },
                },
            },
        });

        if (!user) {
            return {
                success: false,
                error: "Usuario no encontrado",
            };
        }

        // Serializar fecha para evitar problemas de serialización
        const serializedUser = {
            ...user,
            createdAt: user.createdAt.toISOString(),
        };

        return {
            success: true,
            data: serializedUser,
        };
    } catch (error) {
        // REMOVED: console.error("Error al obtener usuario:", error);

        return handleDbError(error, "Error al obtener usuario");
    }
}

// Validar email de usuario (comprobar unicidad)
export async function validateUserEmail(email: string, excludeId?: string) {
    try {
        // Validar email
        const validationResult = userSchema.shape.email.safeParse(email);

        if (!validationResult.success) {
            return {
                success: true,
                isValid: false,
                message: validationResult.error.errors[0].message,
            };
        }

        // Construir condición para verificar unicidad
        const where: Prisma.UserWhereInput = {
            email: { equals: email, mode: "insensitive" },
        };

        // Si se proporciona un ID, excluirlo de la búsqueda
        if (excludeId) {
            where.id = { not: excludeId };
        }

        // Verificar si ya existe un usuario con ese email
        const existingUser = await db.user.findFirst({ where });

        return {
            success: true,
            isValid: !existingUser,
            message: existingUser
                ? "Este email ya está registrado"
                : "Email disponible",
        };
    } catch (error) {
        // En caso de error, devolver un objeto con el formato esperado
        if (error instanceof Error) {
            return {
                success: false,
                isValid: false,
                message: error.message || "Error al validar email",
            };
        }

        return {
            success: false,
            isValid: false,
            message: "Error desconocido al validar email",
        };
    }
}

// Crear un nuevo usuario
export async function createUser(data: {
    name: string;
    email: string;
    role: string;
    password: string;
}) {
    try {
        // Validar datos
        const validation = userSchema.safeParse({
            ...data,
            role:
                data.role === "ADMIN" ||
                data.role === "EMPLOYEE" ||
                data.role === "CONTRACTOR" ||
                data.role === "GUEST"
                    ? data.role
                    : "GUEST",
        });

        if (!validation.success) {
            return {
                success: false,
                error: validation.error.errors[0].message,
            };
        }

        // Verificar que el email sea único
        const emailValidation = await validateUserEmail(data.email);

        if (!emailValidation.isValid) {
            return {
                success: false,
                error: emailValidation.message,
            };
        }

        // Obtener el Role o crear uno nuevo si no existe
        const role = await db.role.findUnique({
            where: { name: data.role },
        });

        if (!role) {
            return {
                success: false,
                error: "Rol no válido",
            };
        }

        // Crear usuario con roleId
        const user = await db.user.create({
            data: {
                name: data.name,
                email: data.email,
                password: await hashPassword(data.password),
                roleId: role.id,
            },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                createdAt: true,
            },
        });

        // Revalidar rutas
        revalidatePath("/dashboard/users");

        return {
            success: true,
            data: user,
        };
    } catch (error) {
        return handleDbError(error, "Error al crear usuario");
    }
}

// Actualizar un usuario existente
export async function updateUser(
    id: string,
    data: {
        name?: string;
        email?: string;
        role?: string;
        password?: string;
    },
) {
    try {
        // Validar ID
        if (!id) {
            return {
                success: false,
                error: "ID de usuario inválido",
            };
        }

        // Preparar datos para actualización
        const updateData: any = {};

        if (data.name !== undefined) {
            const nameValidation = userSchema.shape.name.safeParse(data.name);

            if (!nameValidation.success) {
                return {
                    success: false,
                    error: nameValidation.error.errors[0].message,
                };
            }
            updateData.name = data.name;
        }

        if (data.email !== undefined) {
            const emailValidation = await validateUserEmail(data.email, id);

            if (!emailValidation.isValid) {
                return {
                    success: false,
                    error: emailValidation.message,
                };
            }
            updateData.email = data.email;
        }

        if (data.role !== undefined) {
            const roleValue =
                data.role === "ADMIN" ||
                data.role === "EMPLOYEE" ||
                data.role === "CONTRACTOR" ||
                data.role === "GUEST"
                    ? data.role
                    : "GUEST";

            const role = await db.role.findUnique({
                where: { name: roleValue },
            });

            if (!role) {
                return {
                    success: false,
                    error: "Rol no válido",
                };
            }

            updateData.roleId = role.id;
        }

        if (data.password !== undefined && data.password !== "") {
            const passwordValidation = userSchema.shape.password.safeParse(
                data.password,
            );

            if (!passwordValidation.success) {
                return {
                    success: false,
                    error: passwordValidation.error.errors[0].message,
                };
            }
            updateData.password = await hashPassword(data.password);
        }

        // Actualizar usuario
        const user = await db.user.update({
            where: { id },
            data: updateData,
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                createdAt: true,
            },
        });

        // Revalidar rutas
        revalidatePath("/dashboard/users");

        return {
            success: true,
            data: user,
        };
    } catch (error) {
        return handleDbError(error, "Error al actualizar usuario");
    }
}

// Eliminar un usuario
export async function deleteUser(id: string) {
    try {
        // Validar ID
        if (!id) {
            return {
                success: false,
                error: "ID de usuario inválido",
            };
        }

        // Verificar que el usuario no tenga relaciones
        const userWithCount = await db.user.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        notes: true,
                    },
                },
            },
        });

        if (!userWithCount) {
            return {
                success: false,
                error: "Usuario no encontrado",
            };
        }

        if (userWithCount._count && userWithCount._count.notes > 0) {
            return {
                success: false,
                error: "No se puede eliminar un usuario con relaciones existentes",
            };
        }

        // Eliminar usuario
        await db.user.delete({
            where: { id },
        });

        // Revalidar rutas
        revalidatePath("/dashboard/users");

        return {
            success: true,
        };
    } catch (error) {
        return handleDbError(error, "Error al eliminar usuario");
    }
}
