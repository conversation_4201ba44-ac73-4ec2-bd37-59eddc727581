"use client";

import { useState } from "react";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Chip,
    Divider,
    Progress,
    Badge,
} from "@heroui/react";
import {
    ChevronDownIcon,
    CubeIcon,
    UserIcon,
    ShoppingBagIcon,
    SparklesIcon,
    ChartBarIcon,
    ClockIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import {
    motion,
    AnimatePresence,
    useSpring,
    useTransform,
} from "framer-motion";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";

interface AssignmentPreviewProps {
    className?: string;
}

export function AssignmentPreview({ className = "" }: AssignmentPreviewProps) {
    const { state } = useWizard();
    const [isExpanded, setIsExpanded] = useState(false);
    const [hoveredCard, setHoveredCard] = useState<string | null>(null);

    // Calcular totales con animaciones
    const totalQuantity = state.assignments.reduce(
        (sum, a) => sum + a.quantity,
        0,
    );
    const totalOrders = state.selectedOrders.length;
    const totalItems = state.assignments.length;

    // Spring animations para números
    const springQuantity = useSpring(totalQuantity, {
        stiffness: 300,
        damping: 30,
    });
    const displayQuantity = useTransform(springQuantity, (v) => Math.round(v));

    const springOrders = useSpring(totalOrders, {
        stiffness: 300,
        damping: 30,
    });
    const displayOrders = useTransform(springOrders, (v) => Math.round(v));

    // Calcular progreso
    const progress =
        totalItems > 0 ? (totalQuantity / (totalItems * 100)) * 100 : 0;

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={className}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, type: "spring" }}
        >
            <Card className="overflow-hidden bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-2xl">
                {/* Animated background gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-50" />

                <CardHeader className="relative z-10 pb-0 px-6 pt-6">
                    <div className="flex justify-between items-start w-full">
                        <div className="flex items-center gap-4">
                            <motion.div
                                className="p-3 rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm shadow-lg"
                                whileHover={{ scale: 1.1, rotate: 5 }}
                            >
                                <SparklesIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </motion.div>
                            <div>
                                <h3 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                    Vista Previa de Asignación
                                </h3>
                                <div className="flex items-center gap-2 mt-1">
                                    <motion.div
                                        animate={{ scale: 1 }}
                                        initial={{ scale: 0 }}
                                        transition={{
                                            delay: 0.2,
                                            type: "spring",
                                        }}
                                    >
                                        <Badge
                                            className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border-blue-500/30"
                                            color="primary"
                                            variant="flat"
                                        >
                                            {totalItems} items
                                        </Badge>
                                    </motion.div>
                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                        •
                                    </span>
                                    <motion.div
                                        animate={{ scale: 1 }}
                                        className="flex items-center gap-1"
                                        initial={{ scale: 0 }}
                                        transition={{
                                            delay: 0.3,
                                            type: "spring",
                                        }}
                                    >
                                        <ClockIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            {new Date().toLocaleTimeString()}
                                        </span>
                                    </motion.div>
                                </div>
                            </div>
                        </div>
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Button
                                className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-500/30 hover:from-blue-500/30 hover:to-purple-500/30"
                                endContent={
                                    <motion.div
                                        animate={{
                                            rotate: isExpanded ? 180 : 0,
                                        }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <ChevronDownIcon className="w-4 h-4" />
                                    </motion.div>
                                }
                                size="sm"
                                variant="flat"
                                onPress={() => setIsExpanded(!isExpanded)}
                            >
                                {isExpanded ? "Ocultar" : "Expandir"} detalles
                            </Button>
                        </motion.div>
                    </div>

                    {/* Progress bar */}
                    <motion.div
                        animate={{ scaleX: 1 }}
                        className="mt-4"
                        initial={{ scaleX: 0 }}
                        transition={{ delay: 0.5, type: "spring" }}
                    >
                        <Progress
                            classNames={{
                                track: "bg-gray-200/50 dark:bg-gray-700/50",
                                indicator:
                                    "bg-gradient-to-r from-blue-500 to-purple-500",
                            }}
                            size="sm"
                            value={progress}
                        />
                    </motion.div>
                </CardHeader>

                <CardBody className="relative z-10 p-6">
                    {/* Enhanced summary cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Contractor Card */}
                        <motion.div
                            animate={{ opacity: 1, x: 0 }}
                            className="relative group"
                            initial={{ opacity: 0, x: -20 }}
                            transition={{ delay: 0.1 }}
                            whileHover={{ y: -4, scale: 1.02 }}
                            onHoverEnd={() => setHoveredCard(null)}
                            onHoverStart={() => setHoveredCard("contractor")}
                        >
                            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-xl p-4">
                                {/* Hover gradient */}
                                <motion.div
                                    animate={{
                                        opacity:
                                            hoveredCard === "contractor"
                                                ? 1
                                                : 0,
                                    }}
                                    className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10"
                                    initial={{ opacity: 0 }}
                                />

                                <div className="relative z-10 flex items-start gap-3">
                                    <motion.div
                                        className="p-2 rounded-lg bg-gradient-to-br from-blue-500/20 to-indigo-500/20"
                                        transition={{ duration: 0.5 }}
                                        whileHover={{ rotate: 360 }}
                                    >
                                        <UserIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                    </motion.div>
                                    <div className="flex-1">
                                        <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                            Contratista
                                        </p>
                                        <p className="font-bold text-gray-800 dark:text-gray-200">
                                            {state.contractorData?.name ||
                                                "Sin seleccionar"}
                                        </p>
                                        {state.contractorData && (
                                            <motion.div
                                                animate={{ scale: 1 }}
                                                className="mt-2"
                                                initial={{ scale: 0 }}
                                            >
                                                <CheckCircleIcon className="w-4 h-4 text-green-500" />
                                            </motion.div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </motion.div>

                        {/* Orders Card */}
                        <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className="relative group"
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ delay: 0.2 }}
                            whileHover={{ y: -4, scale: 1.02 }}
                            onHoverEnd={() => setHoveredCard(null)}
                            onHoverStart={() => setHoveredCard("orders")}
                        >
                            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-xl p-4">
                                <motion.div
                                    animate={{
                                        opacity:
                                            hoveredCard === "orders" ? 1 : 0,
                                    }}
                                    className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10"
                                    initial={{ opacity: 0 }}
                                />

                                <div className="relative z-10 flex items-start gap-3">
                                    <motion.div
                                        animate={{ rotate: [0, 10, -10, 0] }}
                                        className="p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20"
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                        }}
                                    >
                                        <ShoppingBagIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                    </motion.div>
                                    <div className="flex-1">
                                        <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                            Órdenes
                                        </p>
                                        <motion.p className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                            {displayOrders}
                                        </motion.p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            {totalOrders === 1
                                                ? "orden"
                                                : "órdenes"}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </motion.div>

                        {/* Total Items Card */}
                        <motion.div
                            animate={{ opacity: 1, x: 0 }}
                            className="relative group"
                            initial={{ opacity: 0, x: 20 }}
                            transition={{ delay: 0.3 }}
                            whileHover={{ y: -4, scale: 1.02 }}
                            onHoverEnd={() => setHoveredCard(null)}
                            onHoverStart={() => setHoveredCard("items")}
                        >
                            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-xl p-4">
                                <motion.div
                                    animate={{
                                        opacity:
                                            hoveredCard === "items" ? 1 : 0,
                                    }}
                                    className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10"
                                    initial={{ opacity: 0 }}
                                />

                                <div className="relative z-10 flex items-start gap-3">
                                    <motion.div
                                        animate={{ scale: [1, 1.1, 1] }}
                                        className="p-2 rounded-lg bg-gradient-to-br from-green-500/20 to-emerald-500/20"
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                        }}
                                    >
                                        <CubeIcon className="w-5 h-5 text-green-600 dark:text-green-400" />
                                    </motion.div>
                                    <div className="flex-1">
                                        <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                            Total Prendas
                                        </p>
                                        <motion.p className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                                            {displayQuantity}
                                        </motion.p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            unidades
                                        </p>
                                    </div>
                                </div>

                                {/* Mini chart */}
                                <motion.div
                                    animate={{ scaleY: 1 }}
                                    className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-400 to-emerald-400 opacity-50"
                                    initial={{ scaleY: 0 }}
                                    style={{ transformOrigin: "bottom" }}
                                    transition={{ delay: 0.5 }}
                                />
                            </div>
                        </motion.div>
                    </div>

                    {/* Enhanced expandable details */}
                    <AnimatePresence>
                        {isExpanded && (
                            <motion.div
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                initial={{ opacity: 0, height: 0 }}
                                transition={{
                                    duration: 0.3,
                                    ease: "easeInOut",
                                }}
                            >
                                <Divider className="my-6 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-700 to-transparent" />

                                {/* Enhanced model distribution */}
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className="mb-6"
                                    initial={{ opacity: 0, y: 20 }}
                                    transition={{ delay: 0.1 }}
                                >
                                    <div className="flex items-center gap-2 mb-4">
                                        <ChartBarIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                                        <h4 className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                            Distribución por Modelo
                                        </h4>
                                    </div>
                                    <div className="grid gap-3">
                                        {Object.entries(
                                            state.assignments.reduce(
                                                (acc, assignment) => {
                                                    const key = `${assignment.modelCode} - ${assignment.colorName}`;

                                                    if (!acc[key]) {
                                                        acc[key] = {
                                                            quantity: 0,
                                                            sizes: [],
                                                            colorHex: (
                                                                assignment as any
                                                            ).colorHex,
                                                        };
                                                    }
                                                    acc[key].quantity +=
                                                        assignment.quantity;
                                                    acc[key].sizes.push(
                                                        `${assignment.sizeCode}: ${assignment.quantity}`,
                                                    );

                                                    return acc;
                                                },
                                                {} as Record<
                                                    string,
                                                    {
                                                        quantity: number;
                                                        sizes: string[];
                                                        colorHex?: string;
                                                    }
                                                >,
                                            ),
                                        ).map(([model, data], index) => (
                                            <motion.div
                                                key={model}
                                                animate={{ opacity: 1, x: 0 }}
                                                className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 shadow-lg p-4 group"
                                                initial={{ opacity: 0, x: -20 }}
                                                transition={{
                                                    delay: index * 0.1,
                                                }}
                                                whileHover={{
                                                    scale: 1.02,
                                                    y: -2,
                                                }}
                                            >
                                                {/* Hover effect */}
                                                <motion.div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity" />

                                                <div className="relative z-10">
                                                    <div className="flex justify-between items-start mb-2">
                                                        <div className="flex items-center gap-3">
                                                            {data.colorHex && (
                                                                <motion.div
                                                                    className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-700 shadow-lg"
                                                                    style={{
                                                                        backgroundColor:
                                                                            data.colorHex,
                                                                    }}
                                                                    whileHover={{
                                                                        scale: 1.2,
                                                                        rotate: 180,
                                                                    }}
                                                                />
                                                            )}
                                                            <p className="font-semibold text-gray-800 dark:text-gray-200">
                                                                {model}
                                                            </p>
                                                        </div>
                                                        <motion.div
                                                            whileHover={{
                                                                scale: 1.05,
                                                            }}
                                                        >
                                                            <Badge
                                                                className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30"
                                                                color="success"
                                                                variant="flat"
                                                            >
                                                                {data.quantity}{" "}
                                                                prendas
                                                            </Badge>
                                                        </motion.div>
                                                    </div>

                                                    {/* Size pills */}
                                                    <div className="flex flex-wrap gap-2 mt-3">
                                                        {data.sizes.map(
                                                            (
                                                                size,
                                                                sizeIndex,
                                                            ) => (
                                                                <motion.div
                                                                    key={
                                                                        sizeIndex
                                                                    }
                                                                    animate={{
                                                                        opacity: 1,
                                                                        scale: 1,
                                                                    }}
                                                                    initial={{
                                                                        opacity: 0,
                                                                        scale: 0,
                                                                    }}
                                                                    transition={{
                                                                        delay:
                                                                            0.3 +
                                                                            sizeIndex *
                                                                                0.05,
                                                                    }}
                                                                >
                                                                    <Chip
                                                                        className="bg-gray-100/50 dark:bg-gray-800/50"
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        {size}
                                                                    </Chip>
                                                                </motion.div>
                                                            ),
                                                        )}
                                                    </div>

                                                    {/* Progress bar */}
                                                    <motion.div
                                                        animate={{ scaleX: 1 }}
                                                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"
                                                        initial={{ scaleX: 0 }}
                                                        style={{
                                                            transformOrigin:
                                                                "left",
                                                        }}
                                                        transition={{
                                                            delay:
                                                                0.5 +
                                                                index * 0.1,
                                                        }}
                                                    />
                                                </div>
                                            </motion.div>
                                        ))}
                                    </div>
                                </motion.div>

                                {/* Enhanced additional info */}
                                <motion.div
                                    animate={{ opacity: 1, y: 0 }}
                                    className="relative overflow-hidden rounded-xl bg-gradient-to-br from-amber-500/10 to-orange-500/10 backdrop-blur-xl border border-amber-500/20 p-4"
                                    initial={{ opacity: 0, y: 20 }}
                                    transition={{ delay: 0.4 }}
                                >
                                    <motion.div
                                        animate={{ x: [0, 100, 0] }}
                                        className="absolute inset-0 bg-gradient-to-r from-amber-400/10 to-orange-400/10"
                                        transition={{
                                            duration: 20,
                                            repeat: Infinity,
                                            ease: "linear",
                                        }}
                                    />

                                    <div className="relative z-10 flex items-start gap-3">
                                        <motion.div
                                            animate={{
                                                rotate: [0, 10, -10, 0],
                                            }}
                                            className="p-2 rounded-lg bg-amber-500/20"
                                            transition={{
                                                duration: 3,
                                                repeat: Infinity,
                                            }}
                                        >
                                            <SparklesIcon className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                                        </motion.div>
                                        <div>
                                            <p className="text-sm font-medium text-amber-700 dark:text-amber-300 mb-1">
                                                Nota Importante
                                            </p>
                                            <p className="text-xs text-amber-600 dark:text-amber-400 leading-relaxed">
                                                Esta es una vista previa de la
                                                asignación. Revisa todos los
                                                detalles antes de confirmar. Los
                                                cambios se guardan
                                                automáticamente.
                                            </p>
                                        </div>
                                    </div>
                                </motion.div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </CardBody>
            </Card>
        </motion.div>
    );
}
