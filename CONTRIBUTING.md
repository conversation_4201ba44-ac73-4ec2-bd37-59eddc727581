# 🤝 Guía de Contribución - LOHARI

¡Gracias por tu interés en contribuir a LOHARI! Esta guía te ayudará a entender cómo puedes colaborar en el proyecto.

## 📋 Tabla de Contenidos

- [Código de Conducta](#código-de-conducta)
- [¿Cómo Contribuir?](#cómo-contribuir)
- [Configuración del Entorno](#configuración-del-entorno)
- [Estructura del Proyecto](#estructura-del-proyecto)
- [Guías de Desarrollo](#guías-de-desarrollo)
- [Proceso de Pull Request](#proceso-de-pull-request)

## 📜 Código de Conducta

- Sé respetuoso y profesional
- Acepta críticas constructivas
- Enfócate en lo mejor para la comunidad
- Muestra empatía hacia otros contribuidores

## 🚀 ¿Cómo Contribuir?

### Reportar Bugs

1. Verifica que el bug no haya sido reportado previamente
2. Crea un issue detallado incluyendo:
   - Descripción clara del problema
   - Pasos para reproducir
   - Comportamiento esperado vs actual
   - Screenshots si aplica
   - Información del entorno

### Sugerir Mejoras

1. Abre un issue para discutir la mejora
2. Describe claramente la funcionalidad propuesta
3. Explica por qué sería útil para el proyecto
4. Espera feedback antes de comenzar a desarrollar

### Contribuir con Código

1. Fork el repositorio
2. Crea un branch descriptivo: `feature/nueva-funcionalidad`
3. Realiza tus cambios siguiendo las guías de estilo
4. Añade tests si es aplicable
5. Asegúrate de que todos los tests pasen
6. Crea un Pull Request detallado

## 🛠️ Configuración del Entorno

```bash
# Clonar tu fork
git clone https://github.com/tu-usuario/lohari.git
cd lohari

# Añadir upstream
git remote add upstream https://github.com/propietario-original/lohari.git

# Instalar dependencias
npm install

# Configurar base de datos
cp .env.example .env.local
# Editar .env.local con tus credenciales

# Ejecutar migraciones
npx prisma migrate dev
npx prisma generate

# Iniciar desarrollo
npm run dev
```

## 📁 Estructura del Proyecto

### Arquitectura Feature-Based

Cada módulo debe seguir esta estructura:

```
features/[nombre-modulo]/
├── components/      # Componentes UI del módulo
├── hooks/          # Custom hooks
├── actions/        # Server actions
├── schemas/        # Validación con Zod
├── types/          # TypeScript types
├── utils/          # Utilidades
└── index.ts        # Barrel exports
```

### Convenciones de Nombres

- **Componentes**: PascalCase (`OrderList.tsx`)
- **Hooks**: useCamelCase (`useOrders.ts`)
- **Actions**: camelCase (`createOrder.ts`)
- **Types**: PascalCase con sufijo (`Order.types.ts`)
- **Utils**: camelCase (`formatters.ts`)

## 📝 Guías de Desarrollo

### Commits

Usamos [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` Nueva funcionalidad
- `fix:` Corrección de bugs
- `docs:` Cambios en documentación
- `style:` Cambios de formato (no afectan funcionalidad)
- `refactor:` Refactorización de código
- `test:` Añadir o modificar tests
- `chore:` Tareas de mantenimiento

Ejemplos:
```bash
git commit -m "feat: agregar filtro por estado en órdenes"
git commit -m "fix: corregir cálculo de totales en remisiones"
git commit -m "docs: actualizar guía de instalación"
```

### Estilo de Código

- Usamos ESLint y Prettier
- Ejecuta `npm run lint` antes de commitear
- El código debe pasar todos los checks de TypeScript
- Mantén consistencia con el código existente

### Tests

- Escribe tests para nuevas funcionalidades
- Mantén cobertura de tests existente
- Ejecuta `npm test` antes de crear PR
- Los tests deben ser descriptivos y cubrir casos edge

### Server Actions

Las Server Actions deben seguir este patrón:

```typescript
"use server";

import { z } from "zod";
import { db } from "@/shared/lib/db";
import { revalidatePath } from "next/cache";

const schema = z.object({
  // Definir schema
});

export async function createItem(data: z.infer<typeof schema>) {
  try {
    // Validar datos
    const validated = schema.parse(data);
    
    // Ejecutar operación
    const result = await db.item.create({
      data: validated
    });
    
    // Revalidar cache
    revalidatePath("/items");
    
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: "Error message" };
  }
}
```

## 🔄 Proceso de Pull Request

### Antes de Crear un PR

1. Actualiza tu branch con main:
   ```bash
   git checkout main
   git pull upstream main
   git checkout tu-branch
   git rebase main
   ```

2. Verifica que todo funcione:
   ```bash
   npm run build
   npm run lint
   npm test
   ```

### Crear el PR

1. **Título**: Descriptivo y siguiendo conventional commits
2. **Descripción**: Incluye:
   - Qué cambios realizaste y por qué
   - Cómo probar los cambios
   - Screenshots si hay cambios visuales
   - Referencias a issues relacionados

### Proceso de Review

- Responde a todos los comentarios
- Realiza los cambios solicitados
- Re-solicita review cuando esté listo
- No hagas force push después del primer review

## 🏗️ Áreas que Necesitan Ayuda

- **Testing**: Mejorar cobertura de tests
- **Documentación**: Documentar APIs y componentes
- **Performance**: Optimización de queries y componentes
- **Accesibilidad**: Mejorar a11y en toda la aplicación
- **i18n**: Preparar para internacionalización

## 📞 ¿Necesitas Ayuda?

- Abre un issue con tus preguntas
- Únete a las discusiones en GitHub
- Revisa la documentación en `/docs`

## 🙏 Agradecimientos

¡Gracias por contribuir a hacer LOHARI mejor! Cada contribución, sin importar el tamaño, es valorada y apreciada.

---

**Happy Coding!** 🚀
