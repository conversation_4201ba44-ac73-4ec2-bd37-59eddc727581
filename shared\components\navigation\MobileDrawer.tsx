"use client";

import { ReactNode, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { XMarkIcon } from "@heroicons/react/24/outline";

import { useNavigation } from "../../../core/context/navigation-context";
import { cn } from "../../lib/client-utils";
import { isNavigationItemActive } from "../../utils/navigation-utils";

interface MobileDrawerProps {
    navItems: Array<{
        href: string;
        label: string;
        icon: ReactNode;
    }>;
    isOpen?: boolean;
}

export default function MobileDrawer({ navItems, isOpen }: MobileDrawerProps) {
    const { isMobileMenuOpen, closeMobileMenu } = useNavigation();
    const pathname = usePathname();

    // Use prop if provided, otherwise fall back to context
    const isDrawerOpen = isOpen !== undefined ? isOpen : isMobileMenuOpen;

    // Prevent body scroll when drawer is open
    useEffect(() => {
        if (isDrawerOpen) {
            document.body.style.overflow = "hidden";
        } else {
            document.body.style.overflow = "";
        }

        return () => {
            document.body.style.overflow = "";
        };
    }, [isDrawerOpen]);

    return (
        <>
            {/* Drawer */}
            <div
                className={cn(
                    "fixed top-0 left-0 h-full w-64 bg-white dark:bg-gray-900 z-sidebar-mobile lg:hidden",
                    "transform transition-transform duration-300 ease-in-out shadow-xl",
                    isDrawerOpen ? "translate-x-0" : "-translate-x-full",
                )}
            >
                <div className="p-4 border-b dark:border-gray-800 flex items-center justify-between">
                    <h1 className="text-xl font-bold">Lohari</h1>
                    <button
                        aria-label="Cerrar menú"
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded focus:outline-none focus:ring-2 focus:ring-primary"
                        onClick={closeMobileMenu}
                    >
                        <XMarkIcon className="w-5 h-5" />
                    </button>
                </div>

                <nav className="p-4 overflow-y-auto h-[calc(100%-4rem)]">
                    <ul className="space-y-2">
                        {navItems.map((item) => {
                            const isActive = isNavigationItemActive(
                                pathname,
                                item.href,
                            );

                            return (
                                <li key={item.href}>
                                    <Link
                                        className={cn(
                                            "flex items-center gap-2 p-2 rounded-md transition-colors duration-200",
                                            "hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-primary",
                                            isActive
                                                ? "bg-gray-100 dark:bg-gray-800 text-primary font-medium"
                                                : "text-gray-700 dark:text-gray-300",
                                        )}
                                        href={item.href}
                                        onClick={closeMobileMenu}
                                    >
                                        <span className="w-5 h-5 flex-shrink-0">
                                            {item.icon}
                                        </span>
                                        <span className="truncate">
                                            {item.label}
                                        </span>
                                    </Link>
                                </li>
                            );
                        })}
                    </ul>
                </nav>
            </div>
        </>
    );
}
