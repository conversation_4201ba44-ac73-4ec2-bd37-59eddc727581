"use client";

import React from "react";
import { <PERSON><PERSON>, But<PERSON>, Chip, Tooltip } from "@heroui/react";
import {
    PencilIcon,
    TrashIcon,
    CalendarIcon,
    UserIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

import { OrderStatus, OrderCustomer } from "../types/order-modal.types";
// Importar helpers seguros
import {
    useThemeSafe,
    getThemeColors,
    getContrastingTextColor,
} from "../utils";
import { getCustomerName, getCustomerAvatar, formatDate } from "../utils";

interface OrderModalHeaderProps {
    orderCode?: string;
    cutOrder?: string;
    status?: OrderStatus;
    customerName?: string;
    customerAvatar?: string;
    customer?: OrderCustomer;
    createdAt?: string | Date;
    onEdit?: () => void;
    onDelete?: () => void;
}

export default function OrderModalHeader({
    orderCode,
    cutOrder,
    status,
    customerName,
    customerAvatar,
    customer,
    createdAt,
    onEdit,
    onDelete,
}: OrderModalHeaderProps) {
    // Usar helpers seguros
    const theme = useThemeSafe();
    const colors = getThemeColors(theme);

    // Datos seguros con helpers
    const displayName = getCustomerName({ customer, customerName } as any);
    const displayAvatar = getCustomerAvatar({
        customer,
        customerAvatar,
    } as any);
    const displayDate = formatDate(createdAt);

    // Determinar si el header debe usar el estilo light o dark
    const isLightTheme = theme === "light";

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={`relative overflow-hidden rounded-t-xl ${colors.headerBorder}`}
            initial={{ opacity: 0, y: -20 }}
        >
            {/* Background adaptable al tema */}
            {isLightTheme ? (
                // Tema light: Fondo sólido con alto contraste
                <div className={`absolute inset-0 ${colors.headerBg}`} />
            ) : (
                // Tema dark: Mantener gradiente original
                <>
                    <div
                        className="absolute inset-0"
                        style={{
                            background:
                                "linear-gradient(135deg, var(--heroui-colors-primary-500) 0%, var(--heroui-colors-primary-600) 100%)",
                        }}
                    />
                    <div
                        className="absolute inset-0 backdrop-blur-md"
                        style={{
                            backdropFilter: "blur(10px)",
                            WebkitBackdropFilter: "blur(10px)",
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        }}
                    />
                </>
            )}

            {/* Content */}
            <div className="relative z-10 p-6 flex items-center justify-between">
                {/* Left section - Avatar and Info */}
                <div className="flex items-center gap-4">
                    <Avatar
                        className={
                            isLightTheme
                                ? "ring-2 ring-primary-200"
                                : "ring-2 ring-white/20"
                        }
                        fallback={
                            <UserIcon
                                className={`w-6 h-6 ${colors.headerText}`}
                            />
                        }
                        name={displayName}
                        size="lg"
                        src={displayAvatar}
                    />

                    <div>
                        <h2
                            className={`text-2xl font-bold ${colors.headerText} mb-1`}
                        >
                            {cutOrder
                                ? `Orden de trabajo: ${cutOrder}`
                                : "Sin orden de trabajo"}
                        </h2>
                        <div
                            className={`flex items-center gap-2 ${colors.headerText} ${isLightTheme ? "opacity-70" : "opacity-80"}`}
                        >
                            <CalendarIcon className="w-4 h-4" />
                            <span className="text-sm">{displayDate}</span>
                        </div>
                    </div>
                </div>

                {/* Center - Status */}
                {status && (
                    <Chip
                        className="font-medium shadow-sm"
                        size="lg"
                        style={{
                            backgroundColor: status.color,
                            color: getContrastingTextColor(status.color, theme),
                        }}
                    >
                        {status.name}
                    </Chip>
                )}

                {/* Right section - Actions */}
                <div className="flex items-center gap-2">
                    {onEdit && (
                        <Tooltip content="Editar orden">
                            <Button
                                isIconOnly
                                className={`${colors.headerText} ${colors.headerHover} transition-colors`}
                                variant="light"
                                onPress={onEdit}
                            >
                                <PencilIcon className="w-5 h-5" />
                            </Button>
                        </Tooltip>
                    )}

                    {onDelete && (
                        <Tooltip content="Eliminar orden">
                            <Button
                                isIconOnly
                                className={`${colors.headerText} ${colors.headerHover} transition-colors`}
                                variant="light"
                                onPress={onDelete}
                            >
                                <TrashIcon className="w-5 h-5" />
                            </Button>
                        </Tooltip>
                    )}
                </div>
            </div>

            {/* Fallback for browsers without backdrop-filter support (solo para dark theme) */}
            {!isLightTheme && (
                <style jsx>{`
                    @supports not (backdrop-filter: blur(10px)) {
                        .backdrop-blur-md {
                            background-color: var(--heroui-colors-primary-500);
                            opacity: 0.95;
                        }
                    }
                `}</style>
            )}
        </motion.div>
    );
}
