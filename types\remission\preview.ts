// types/remission/preview.ts
/**
 * UI-specific interfaces for remission preview
 * These interfaces extend core with UI display data
 */

import type { RemissionCore, RemissionItem } from "./core";

export interface RemissionPreview extends RemissionCore {
    contractor: ContractorInfo;
    displayTotals: DisplayTotals;
    groupedItems: GroupedItems;
}

export interface ContractorInfo {
    id: string;
    name: string;
    code: string;
    phone?: string;
    email?: string;
}

export interface DisplayTotals {
    totalItems: number;
    totalQuantity: number;
    uniqueModels: number;
    bySize: Record<string, number>;
}

export type GroupedItems = Map<string, RemissionItem[]>;
