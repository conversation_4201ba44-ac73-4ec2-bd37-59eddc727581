import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testRoles() {
    try {
        console.log('🔍 Verificando roles en la base de datos...\n');

        // Contar roles
        const count = await prisma.role.count();
        console.log(`Total de roles: ${count}\n`);

        // Listar todos los roles
        const roles = await prisma.role.findMany();
        
        if (roles.length === 0) {
            console.log('❌ No hay roles en la base de datos');
        } else {
            console.log('✅ Roles encontrados:');
            roles.forEach(role => {
                console.log(`  - ${role.name} (ID: ${role.id})`);
            });
        }

        // Buscar rol GUEST específicamente
        console.log('\n🔍 Buscando rol GUEST...');
        const guestRole = await prisma.role.findUnique({
            where: { name: 'GUEST' }
        });

        if (guestRole) {
            console.log('✅ Rol GUEST encontrado:', guestRole);
        } else {
            console.log('❌ Rol GUEST NO encontrado');
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await prisma.$disconnect();
    }
}

testRoles();
