"use client";

import type { SortOption } from "@/shared/components/dashboard";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import useSWR from "swr";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    CubeIcon,
    RocketLaunchIcon,
    ChartBarIcon,
    ClockIcon,
    DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { addToast } from "@heroui/react";
import { motion } from "framer-motion";

import { CrudListTemplate } from "@/shared/templates";
import { sortData } from "@/shared/utils/sortHelpers";
import { useModels, useDeleteModel } from "@/features/models/hooks/useModel";
import { Chip, Progress } from "@/shared/components/ui/hero-ui-client";
import { formatCurrency } from "@/shared/utils/formatters";

// Fetcher para SWR
const fetcher = (url: string) => fetch(url).then((res) => res.json());

interface Model {
    id: string;
    code: string;
    description: string;
    basePrice?: number;
    createdAt: string;
    updatedAt: string;
    _count?: {
        garments?: number;
    };
}

export default function UnifiedModelsPage() {
    const router = useRouter();
    const { models = [], isLoading, mutate } = useModels();
    const { deleteModel } = useDeleteModel();

    // Obtener usuario actual directamente de la API
    const { data: currentUser } = useSWR("/api/auth/me", fetcher);

    const [searchValue, setSearchValue] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, any>>({});
    const [currentSort, setCurrentSort] = useState<SortOption>({
        key: "code-asc",
        label: "Código A-Z",
        field: "code",
        direction: "asc",
    });
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;

    // Calcular métricas de modelos
    const modelMetrics = useMemo(() => {
        const totalGarments = models.reduce(
            (sum: number, model: Model) => sum + (model._count?.garments || 0),
            0,
        );

        return models
            .map((model: Model) => ({
                ...model,
                usage: model._count?.garments || 0,
                percentage:
                    totalGarments > 0
                        ? ((model._count?.garments || 0) / totalGarments) * 100
                        : 0,
            }))
            .sort((a: any, b: any) => b.usage - a.usage);
    }, [models]);

    // Calcular estadísticas
    const stats = useMemo(() => {
        const usedModels = models.filter(
            (m: Model) => m._count?.garments && m._count.garments > 0,
        );
        const topModels = modelMetrics.slice(0, 3);

        // Modelos recientes (últimos 30 días)
        const thirtyDaysAgo = new Date();

        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentModels = models.filter(
            (m: Model) => new Date(m.createdAt) > thirtyDaysAgo,
        );

        // Promedio de uso
        const avgUsage =
            models.length > 0
                ? Math.round(
                      models.reduce(
                          (sum: number, m: Model) =>
                              sum + (m._count?.garments || 0),
                          0,
                      ) / models.length,
                  )
                : 0;

        return [
            {
                title: "Total Modelos",
                value: models.length,
                icon: <CubeIcon className="w-6 h-6" />,
                color: "primary" as const,
                description: "En catálogo",
            },
            {
                title: "Más Popular",
                value: topModels[0]?.code || "-",
                description: topModels[0]
                    ? `${topModels[0].usage} prendas`
                    : "",
                icon: <RocketLaunchIcon className="w-6 h-6" />,
                color: "success" as const,
            },
            {
                title: "Promedio de Uso",
                value: avgUsage,
                description: "prendas por modelo",
                icon: <ChartBarIcon className="w-6 h-6" />,
                color: "warning" as const,
                change: usedModels.length,
                changeLabel: "modelos activos",
            },
            {
                title: "Recientes",
                value: recentModels.length,
                icon: <ClockIcon className="w-6 h-6" />,
                color: "default" as const,
                changeLabel: "últimos 30 días",
            },
        ];
    }, [models, modelMetrics]);

    // Columnas de la tabla con visualizaciones mejoradas
    const columns = [
        {
            key: "code",
            label: "Código",
            sortable: true,
            render: (model: Model) => (
                <div className="flex items-center gap-2">
                    <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                        <DocumentTextIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                        <span className="font-bold text-gray-900 dark:text-white">
                            {model.code}
                        </span>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[200px]">
                            {model.description}
                        </p>
                    </div>
                </div>
            ),
        },
        // Mostrar precio base solo para administradores
        ...(currentUser?.role === "admin"
            ? [
                  {
                      key: "basePrice",
                      label: "Precio Base",
                      sortable: true,
                      render: (model: Model) => (
                          <div className="font-medium text-gray-900 dark:text-white">
                              {formatCurrency(model.basePrice || 0)}
                          </div>
                      ),
                  },
              ]
            : []),
        {
            key: "usage",
            label: "Uso",
            render: (model: Model) => {
                const metric = modelMetrics.find((m: any) => m.id === model.id);
                const usage = metric?.usage || 0;
                const percentage = metric?.percentage || 0;

                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-3">
                            <Progress
                                className="w-24"
                                color={
                                    percentage > 50
                                        ? "success"
                                        : percentage > 20
                                          ? "warning"
                                          : "default"
                                }
                                size="sm"
                                value={percentage}
                            />
                            <span className="text-sm font-medium">
                                {percentage.toFixed(1)}%
                            </span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            {usage} {usage === 1 ? "prenda" : "prendas"}
                        </span>
                    </div>
                );
            },
        },
        {
            key: "status",
            label: "Estado",
            render: (model: Model) => {
                const isUsed =
                    model._count?.garments && model._count.garments > 0;
                const usage = model._count?.garments || 0;

                let status = "Sin uso";
                let color: "default" | "warning" | "success" | "danger" =
                    "default";

                if (usage > 100) {
                    status = "Alta demanda";
                    color = "danger";
                } else if (usage > 50) {
                    status = "Popular";
                    color = "success";
                } else if (usage > 0) {
                    status = "En uso";
                    color = "warning";
                }

                return (
                    <Chip color={color} size="sm" variant="flat">
                        {status}
                    </Chip>
                );
            },
        },
        {
            key: "dates",
            label: "Fechas",
            render: (model: Model) => (
                <div className="text-sm space-y-1">
                    <div className="text-gray-600 dark:text-gray-400">
                        Creado:{" "}
                        {format(new Date(model.createdAt), "dd/MM/yyyy", {
                            locale: es,
                        })}
                    </div>
                    {model.updatedAt !== model.createdAt && (
                        <div className="text-gray-500 dark:text-gray-500 text-xs">
                            Actualizado:{" "}
                            {formatDistanceToNow(new Date(model.updatedAt), {
                                locale: es,
                                addSuffix: true,
                            })}
                        </div>
                    )}
                </div>
            ),
        },
    ];

    // Acciones
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: (model: Model) => {
                router.push(`/dashboard/models/${model.id}/details`);
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (model: Model) => {
                router.push(`/dashboard/models/${model.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (model: Model) => {
                if (model._count?.garments && model._count.garments > 0) {
                    addToast({
                        title: "No se puede eliminar",
                        description: "Este modelo está siendo usado en prendas",
                        color: "warning",
                    });

                    return;
                }

                if (
                    confirm(
                        `¿Estás seguro de eliminar el modelo ${model.code}?`,
                    )
                ) {
                    const result = await deleteModel(model.id);

                    if (result.success) {
                        mutate();
                        addToast({
                            title: "Modelo eliminado",
                            description:
                                "El modelo ha sido eliminado correctamente",
                            color: "success",
                        });
                    }
                }
            },
            color: "danger" as const,
            isDisabled: (model: Model) =>
                !!(model._count?.garments && model._count.garments > 0),
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "used", label: "En uso" },
                { value: "unused", label: "Sin usar" },
                { value: "popular", label: "Populares (>50 prendas)" },
                { value: "high-demand", label: "Alta demanda (>100 prendas)" },
            ],
        },
        {
            key: "recent",
            label: "Periodo",
            type: "select" as const,
            placeholder: "Todo el tiempo",
            options: [
                { value: "all", label: "Todo el tiempo" },
                { value: "7", label: "Últimos 7 días" },
                { value: "30", label: "Últimos 30 días" },
                { value: "90", label: "Últimos 90 días" },
            ],
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "code-asc",
            label: "Código A-Z",
            field: "code",
            direction: "asc" as const,
        },
        {
            key: "code-desc",
            label: "Código Z-A",
            field: "code",
            direction: "desc" as const,
        },
        {
            key: "usage-desc",
            label: "Más usado",
            field: "_count.garments",
            direction: "desc" as const,
        },
        {
            key: "usage-asc",
            label: "Menos usado",
            field: "_count.garments",
            direction: "asc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Más reciente",
            field: "createdAt",
            direction: "desc" as const,
        },
        {
            key: "createdAt-asc",
            label: "Más antiguo",
            field: "createdAt",
            direction: "asc" as const,
        },
    ];

    // Filtrar datos
    const filteredData = useMemo(() => {
        let filtered = [...models];

        // Búsqueda
        if (searchValue) {
            filtered = filtered.filter(
                (model) =>
                    model.code
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()) ||
                    model.description
                        .toLowerCase()
                        .includes(searchValue.toLowerCase()),
            );
        }

        // Filtro por estado
        if (filterValues.status && filterValues.status !== "all") {
            switch (filterValues.status) {
                case "used":
                    filtered = filtered.filter(
                        (m) => m._count?.garments && m._count.garments > 0,
                    );
                    break;
                case "unused":
                    filtered = filtered.filter(
                        (m) => !m._count?.garments || m._count.garments === 0,
                    );
                    break;
                case "popular":
                    filtered = filtered.filter(
                        (m) => m._count?.garments && m._count.garments > 50,
                    );
                    break;
                case "high-demand":
                    filtered = filtered.filter(
                        (m) => m._count?.garments && m._count.garments > 100,
                    );
                    break;
            }
        }

        // Filtro por periodo
        if (filterValues.recent && filterValues.recent !== "all") {
            const days = parseInt(filterValues.recent);
            const dateLimit = new Date();

            dateLimit.setDate(dateLimit.getDate() - days);
            filtered = filtered.filter(
                (m) => new Date(m.createdAt) > dateLimit,
            );
        }

        // Aplicar ordenamiento
        if (currentSort) {
            filtered = sortData(
                filtered,
                currentSort.field,
                currentSort.direction,
            );
        }

        return filtered;
    }, [models, searchValue, filterValues, currentSort]);

    // Componente de gráfico de top modelos
    const TopModelsChart = () => (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6"
            initial={{ opacity: 0, y: 20 }}
        >
            <h3 className="text-lg font-semibold mb-4">
                Top 5 Modelos Más Usados
            </h3>
            <div className="space-y-3">
                {modelMetrics.slice(0, 5).map((model: any, index: number) => (
                    <div key={model.id} className="flex items-center gap-3">
                        <div className="flex items-center gap-2 w-32">
                            <span
                                className={`text-sm font-bold ${index === 0 ? "text-yellow-600" : index === 1 ? "text-gray-500" : index === 2 ? "text-orange-600" : "text-gray-600"}`}
                            >
                                #{index + 1}
                            </span>
                            <span className="font-medium truncate">
                                {model.code}
                            </span>
                        </div>
                        <div className="flex-1">
                            <Progress
                                color={
                                    index === 0
                                        ? "success"
                                        : index < 3
                                          ? "warning"
                                          : "default"
                                }
                                size="md"
                                value={model.percentage}
                            />
                        </div>
                        <div className="text-sm w-20 text-right">
                            <span className="font-medium">{model.usage}</span>
                            <span className="text-gray-500 dark:text-gray-400">
                                {" "}
                                prendas
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        </motion.div>
    );

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={columns}
                currentSort={currentSort}
                filterValues={filterValues}
                isLoading={isLoading}
                page={page}
                searchValue={searchValue}
                sortOptions={sortOptions}
                subtitle="Gestión de modelos de prendas"
                title="Modelos"
                totalPages={Math.ceil(filteredData.length / rowsPerPage)}
                onClearFilters={() => {
                    setSearchValue("");
                    setFilterValues({});
                }}
                onFilterChange={(key, value) => {
                    setFilterValues((prev) => ({ ...prev, [key]: value }));
                }}
                onPageChange={setPage}
                onSearchChange={setSearchValue}
                onSortChange={setCurrentSort}
                actions={actions}
                // Create
                createRoute="/dashboard/models/new"
                activeFiltersCount={
                    Object.keys(filterValues).filter(
                        (key) =>
                            filterValues[key] && filterValues[key] !== "all",
                    ).length
                }
                // Pagination
                breadcrumbs={[{ label: "Modelos" }]}
                // Stats
                stats={stats}
                createLabel="Nuevo Modelo"
                // Filters
                filters={filters}
                data={filteredData}
                // Table
                emptyContent="No hay modelos registrados"
            />

            {/* Gráfico de top modelos */}
            {models.length > 0 && <TopModelsChart />}
        </>
    );
}
