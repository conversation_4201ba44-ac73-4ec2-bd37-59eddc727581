import { Prisma } from "@prisma/client";

export interface OrderPart {
    id: string;
    code: string;
}

export interface OrderDetails {
    id: string;
    cutOrder?: string;
    creationDate?: Date;
    parts?: OrderPart[];
}

export interface RemissionData {
    id: string;
    folio: string;
    createdAt: Date;
    updatedAt: Date;
    contractorId: string;
    notes?: string;
    orderDetails?: OrderDetails | Prisma.InputJsonValue;
    printedAt?: Date;
    status: string;
    assignments?: any[];
}

export interface RemissionFilters {
    contractorId?: string;
    startDate?: Date;
    endDate?: Date;
    status?: string;
}

export interface RemissionItemData {
    modelCode: string;
    colorName: string;
    sizeCode: string;
    quantity: number;
}

export interface CreateRemissionData {
    assignmentIds: string[];
    contractorId: string;
    notes?: string;
    orderDetails: OrderDetails;
    items: RemissionItemData[];
}

export interface RemissionHistoryEntry {
    action: string;
    timestamp: Date;
    metadata?: Prisma.InputJsonValue;
}

export interface LocalStorageFolios {
    lastDate: string;
    lastSequence: number;
    all: string[];
}
