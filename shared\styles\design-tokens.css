/* ===================================
   LOHARI Design System Tokens v1.0
   =================================== */

/* Color System - Radix UI Colors Compatible */
:root {
  /* Primary Colors - Blue Scale */
  --color-primary-1: #fbfdff;
  --color-primary-2: #f4faff;
  --color-primary-3: #e6f4fe;
  --color-primary-4: #d5efff;
  --color-primary-5: #c2e5ff;
  --color-primary-6: #acd8fc;
  --color-primary-7: #8ec8f6;
  --color-primary-8: #5eb1ef;
  --color-primary-9: #0090ff; /* Main brand color */
  --color-primary-10: #0588f0;
  --color-primary-11: #0d74ce;
  --color-primary-12: #113264;

  /* Gray Scale - Neutral */
  --color-gray-1: #fcfcfd;
  --color-gray-2: #f9f9fb;
  --color-gray-3: #f0f0f3;
  --color-gray-4: #e8e8ec;
  --color-gray-5: #e0e0e6;
  --color-gray-6: #d8d8df;
  --color-gray-7: #ceceda;
  --color-gray-8: #b9b9c6;
  --color-gray-9: #8b8b9a;
  --color-gray-10: #80808f;
  --color-gray-11: #646471;
  --color-gray-12: #1a1a1f;

  /* Semantic Colors */
  --color-success-9: #30a46c;
  --color-success-11: #18794e;
  --color-warning-9: #f5a524;
  --color-warning-11: #c87311;
  --color-error-9: #e54d4d;
  --color-error-11: #ce2c31;
  --color-info-9: #3b82f6;
  --color-info-11: #1e40af;

  /* Typography Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing Scale (4px base) */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;  /* 2px */
  --radius-md: 0.25rem;   /* 4px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Z-Index Scale */
  --z-header: 10;
  --z-sidebar: 10;  --z-dropdown: 20;
  --z-backdrop: 25;
  --z-modal-backdrop: 30;
  --z-modal: 40;
  --z-notification: 50;
  --z-tooltip: 60;

  /* Animations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 400ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* Dark Mode Theme */
[data-theme="dark"] {
  /* Inverted Gray Scale */
  --color-gray-1: #18181b;
  --color-gray-2: #1f1f23;
  --color-gray-3: #27272a;
  --color-gray-4: #2e2e32;
  --color-gray-5: #34343a;
  --color-gray-6: #3b3b42;
  --color-gray-7: #48484f;
  --color-gray-8: #62626b;
  --color-gray-9: #7c7c85;
  --color-gray-10: #91919a;
  --color-gray-11: #b1b1ba;
  --color-gray-12: #eeeeef;

  /* Dark mode shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.2);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);
}

/* Utility Classes */
.text-high-contrast {
  color: var(--color-gray-12);
}

.text-medium-contrast {
  color: var(--color-gray-11);
}

.text-low-contrast {
  color: var(--color-gray-9);
}