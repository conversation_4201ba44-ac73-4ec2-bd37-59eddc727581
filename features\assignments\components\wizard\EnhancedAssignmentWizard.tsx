"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
} from "@heroui/react";
import {
    ArrowLeftIcon,
    ArrowRightIcon,
    CheckIcon,
    PrinterIcon,
    InformationCircleIcon,
    ExclamationCircleIcon,
    UserCircleIcon,
    CubeIcon,
    ChartBarIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import {
    useWizard,
    useEstimatedTime,
    WizardStep,
} from "@/app/dashboard/assignments/new/wizard-context";
import ContractorStep from "@/features/assignments/components/wizard/steps/ContractorStep";
import OrdersStep from "@/features/assignments/components/wizard/steps/OrdersStep";
import QuantitiesStep from "@/features/assignments/components/wizard/steps/QuantitiesStep";
import SummaryStep from "@/features/assignments/components/wizard/steps/SummaryStep";
import AssignmentRemission from "@/features/remissions/components/AssignmentRemission";
import { getOrderDetailsForAssignment } from "@/features/assignments/actions";

import { EnhancedStepIndicator } from "./ui/EnhancedStepIndicator";
import { TimeEstimateIndicator } from "./ui/TimeEstimateIndicator";
import { useKeyboardNavigation } from "./hooks/useKeyboardNavigation";
import { UndoRedoControls } from "./ui/UndoRedoControls";
import { DraftControls } from "./ui/DraftControls";
import { FeedbackToast } from "./ui/FeedbackToast";

interface EnhancedAssignmentWizardProps {
    onComplete?: () => void;
}

export default function EnhancedAssignmentWizard({
    onComplete,
}: EnhancedAssignmentWizardProps) {
    const router = useRouter();
    const {
        state,
        nextStep: goToNextStep,
        prevStep: goToPreviousStep,
        canGoNext,
        canGoBack,
        validationErrors,
    } = useWizard();

    const estimatedTime = useEstimatedTime();

    const currentStep = state.currentStep;
    const isSubmitting = state.isSubmitting;

    const [isSuccess, setIsSuccess] = useState(false);
    const [showRemission, setShowRemission] = useState(false);
    const [previousStep, setPreviousStep] = useState<WizardStep | null>(null);
    const [showTooltip, setShowTooltip] = useState(false);
    const [createdAssignments, setCreatedAssignments] = useState<any[]>([]);
    const [orderData, setOrderData] = useState<any>(null);

    // Hook para navegación con teclado
    const { shortcuts } = useKeyboardNavigation({
        onCancel: () => {
            window.location.href = "/dashboard/assignments";
        },
        enabled: !isSubmitting && !isSuccess,
    });

    // Obtener detalles de la orden cuando se muestra la remisión
    useEffect(() => {
        if (showRemission && state.selectedOrders[0]) {
            getOrderDetailsForAssignment(state.selectedOrders[0])
                .then((result) => {
                    if (
                        result.success &&
                        result.data &&
                        (result.data as any).order
                    ) {
                        setOrderData({
                            id: (result.data as any).order.id,
                            cutOrder: (result.data as any).order.cutOrder,
                            creationDate: (result.data as any).order
                                .receivedDate,
                            parts: (result.data as any).order.parts || [],
                        });
                    }
                })
                .catch((error) => {
                    console.error("Error fetching order details:", error);
                });
        }
    }, [showRemission, state.selectedOrders]);

    // Al cambiar de paso, guardar el paso anterior para la animación
    const handleStepChange = (direction: "next" | "previous") => {
        setPreviousStep(currentStep);
        if (direction === "next") {
            goToNextStep();
        } else {
            goToPreviousStep();
        }
    };

    // Renderizar el paso actual
    const renderCurrentStep = () => {
        switch (currentStep) {
            case "contractor":
                return <ContractorStep />;
            case "orders":
                return <OrdersStep />;
            case "quantities":
                return <QuantitiesStep />;
            case "summary":
                return (
                    <SummaryStep
                        onSuccess={(data?: any) => {
                            if (data?.assignments) {
                                setCreatedAssignments(data.assignments);
                                // Redirigir directamente a crear remisión
                                router.push("/dashboard/remissions/create");
                            }
                            if (onComplete) onComplete();
                        }}
                    />
                );
            default:
                return null;
        }
    };

    // Textos según el paso actual
    const getStepTitle = (step: WizardStep): string => {
        switch (step) {
            case "contractor":
                return "Seleccionar Contratista";
            case "orders":
                return "Seleccionar Órdenes";
            case "quantities":
                return "Asignar Cantidades";
            case "summary":
                return "Resumen y Confirmación";
            default:
                return "";
        }
    };

    const getNextButtonText = (): string => {
        switch (currentStep) {
            case "contractor":
            case "orders":
            case "quantities":
                return "Continuar";
            case "summary":
                return "Confirmar Asignaciones";
            default:
                return "Continuar";
        }
    };

    // Información contextual del paso
    const getStepHelp = (): string => {
        switch (currentStep) {
            case "contractor":
                return "Selecciona un contratista para asignarle trabajo. El contratista recibirá una notificación cuando se complete la asignación.";
            case "orders":
                return "Selecciona una o más órdenes que deseas asignar al contratista. Puedes filtrar por varios criterios.";
            case "quantities":
                return "Especifica las cantidades para cada talla de prenda. Puedes usar las sugerencias o ingresar valores personalizados.";
            case "summary":
                return "Revisa todos los detalles de la asignación antes de confirmarla. Una vez confirmada, se generará una remisión.";
            default:
                return "";
        }
    };

    // Determinar la dirección de la transición
    const getDirectionForStep = (
        current: WizardStep,
        previous: WizardStep | null,
    ): number => {
        if (!previous) return 0;

        const stepOrder: WizardStep[] = [
            "contractor",
            "orders",
            "quantities",
            "summary",
        ];
        const currentIndex = stepOrder.indexOf(current);
        const previousIndex = stepOrder.indexOf(previous);

        return currentIndex > previousIndex ? 1 : -1;
    };

    // Variantes de animación para el contenido
    const contentVariants = {
        enter: (direction: number) => ({
            x: direction > 0 ? 100 : -100,
            opacity: 0,
        }),
        center: {
            x: 0,
            opacity: 1,
        },
        exit: (direction: number) => ({
            x: direction > 0 ? -100 : 100,
            opacity: 0,
        }),
    };

    // Renderizado condicional para errores de validación
    const renderValidationErrors = () => {
        if (Object.keys(validationErrors).length === 0) return null;

        return (
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4"
                initial={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex items-center text-red-800 dark:text-red-400 mb-2">
                    <ExclamationCircleIcon className="w-5 h-5 mr-2" />
                    <span className="font-medium">
                        Por favor corrige los siguientes errores:
                    </span>
                </div>
                <ul className="list-disc list-inside text-sm text-red-700 dark:text-red-300 pl-2">
                    {Object.values(validationErrors).map((error, index) => (
                        <li key={index}>{error}</li>
                    ))}
                </ul>
            </motion.div>
        );
    };

    if (isSuccess) {
        return (
            <Card className="w-full">
                <CardBody className="py-12 px-8 sm:px-10 lg:px-12">
                    {showRemission ? (
                        <AssignmentRemission
                            assignments={
                                createdAssignments.length > 0
                                    ? createdAssignments
                                    : state.assignments || []
                            }
                            contractorId={state.contractor || ""}
                            orderData={orderData}
                            orderId={state.selectedOrders[0] || ""}
                            onSuccess={() => setShowRemission(false)}
                        />
                    ) : (
                        <motion.div
                            animate={{ scale: 1, opacity: 1 }}
                            className="flex flex-col items-center justify-center space-y-6 text-center"
                            initial={{ scale: 0.8, opacity: 0 }}
                            transition={{ duration: 0.5, type: "spring" }}
                        >
                            <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-2">
                                <motion.div
                                    animate={{ scale: 1 }}
                                    initial={{ scale: 0 }}
                                    transition={{
                                        delay: 0.3,
                                        duration: 0.5,
                                        type: "spring",
                                    }}
                                >
                                    <CheckIcon className="w-12 h-12 text-green-500 dark:text-green-400" />
                                </motion.div>
                            </div>
                            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100">
                                ¡Asignaciones creadas con éxito!
                            </h2>
                            <p className="text-gray-600 dark:text-gray-400 max-w-md text-center">
                                Las asignaciones han sido creadas correctamente
                                y los contratistas ya pueden comenzar a trabajar
                                en ellas.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-3 mt-6">
                                <Button
                                    color="primary"
                                    startContent={
                                        <PrinterIcon className="w-4 h-4" />
                                    }
                                    onPress={() => setShowRemission(true)}
                                >
                                    Ver e Imprimir Remisión
                                </Button>
                                <Button
                                    color="primary"
                                    variant="flat"
                                    onPress={() =>
                                        (window.location.href =
                                            "/dashboard/assignments")
                                    }
                                >
                                    Ver Asignaciones
                                </Button>
                                <Button
                                    color="default"
                                    variant="flat"
                                    onPress={() => {
                                        setIsSuccess(false);
                                        window.location.href =
                                            "/dashboard/assignments/new";
                                    }}
                                >
                                    Crear Nuevas Asignaciones
                                </Button>
                            </div>
                        </motion.div>
                    )}
                </CardBody>
            </Card>
        );
    }

    const direction = getDirectionForStep(currentStep, previousStep);

    return (
        <>
            {/* Indicador de tiempo estimado */}
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, type: "spring" }}
            >
                <TimeEstimateIndicator />
            </motion.div>

            <div className="w-full">
                {/* Panel principal con pasos del wizard (ancho completo) */}
                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    initial={{ opacity: 0, scale: 0.98 }}
                    transition={{ duration: 0.5, type: "spring" }}
                >
                    <Card className="w-full backdrop-blur-xl bg-white/90 dark:bg-gray-900/90 border border-gray-200/50 dark:border-gray-700/50 shadow-2xl hover:shadow-3xl transition-all duration-300">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 rounded-xl pointer-events-none" />
                        <CardBody className="p-8 sm:p-10 lg:p-12 xl:p-14 relative z-10">
                            {/* Indicador de pasos mejorado */}
                            <EnhancedStepIndicator
                                allowNavigation={true}
                                currentStep={
                                    currentStep === "contractor"
                                        ? 1
                                        : currentStep === "orders"
                                          ? 2
                                          : currentStep === "quantities"
                                            ? 3
                                            : currentStep === "summary"
                                              ? 4
                                              : 1
                                }
                                stepsWithErrors={
                                    // Marcar pasos con errores de validación
                                    Object.keys(validationErrors).length > 0
                                        ? currentStep === "quantities"
                                            ? [3]
                                            : []
                                        : []
                                }
                                stepsWithWarnings={
                                    // Marcar pasos con advertencias (ejemplo: sin órdenes seleccionadas)
                                    currentStep === "orders" &&
                                    state.selectedOrders.length === 0
                                        ? [2]
                                        : []
                                }
                                onStepClick={(stepId) => {
                                    const stepMap: Record<number, WizardStep> =
                                        {
                                            1: "contractor",
                                            2: "orders",
                                            3: "quantities",
                                            4: "summary",
                                        };

                                    const targetStep = stepMap[stepId];

                                    if (targetStep) {
                                        setPreviousStep(currentStep);
                                        goToNextStep();
                                    }
                                }}
                            />

                            <div className="py-6 lg:py-8">
                                {/* Encabezado con título e información de ayuda */}
                                <div className="flex justify-between items-start mb-8">
                                    <motion.div
                                        key={`header-${currentStep}`}
                                        animate={{ opacity: 1, x: 0 }}
                                        className="flex items-center gap-4"
                                        initial={{ opacity: 0, x: -20 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <motion.div
                                            className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-xl"
                                            transition={{
                                                type: "spring",
                                                stiffness: 300,
                                            }}
                                            whileHover={{
                                                scale: 1.1,
                                                rotate: 5,
                                            }}
                                        >
                                            {currentStep === "contractor" ? (
                                                <UserCircleIcon className="w-8 h-8 text-white" />
                                            ) : currentStep === "orders" ? (
                                                <CubeIcon className="w-8 h-8 text-white" />
                                            ) : currentStep === "quantities" ? (
                                                <ChartBarIcon className="w-8 h-8 text-white" />
                                            ) : (
                                                <CheckCircleIcon className="w-8 h-8 text-white" />
                                            )}
                                        </motion.div>
                                        <div>
                                            <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                                {getStepTitle(currentStep)}
                                            </h2>
                                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                Paso{" "}
                                                {currentStep === "contractor"
                                                    ? 1
                                                    : currentStep === "orders"
                                                      ? 2
                                                      : currentStep ===
                                                          "quantities"
                                                        ? 3
                                                        : 4}{" "}
                                                de 4
                                            </p>
                                        </div>
                                    </motion.div>

                                    <motion.div
                                        animate={{ opacity: 1, scale: 1 }}
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <Tooltip
                                            classNames={{
                                                content:
                                                    "bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium shadow-xl",
                                            }}
                                            content={getStepHelp()}
                                            isOpen={showTooltip}
                                            placement="left"
                                            onOpenChange={setShowTooltip}
                                        >
                                            <motion.div
                                                transition={{
                                                    type: "spring",
                                                    stiffness: 300,
                                                }}
                                                whileHover={{
                                                    scale: 1.1,
                                                    rotate: 180,
                                                }}
                                            >
                                                <Button
                                                    isIconOnly
                                                    aria-label="Información de ayuda"
                                                    className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-200 dark:border-blue-700 shadow-lg"
                                                    size="lg"
                                                    onPress={() =>
                                                        setShowTooltip(
                                                            !showTooltip,
                                                        )
                                                    }
                                                >
                                                    <InformationCircleIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                                </Button>
                                            </motion.div>
                                        </Tooltip>
                                    </motion.div>
                                </div>

                                {/* Errores de validación */}
                                <AnimatePresence>
                                    {Object.keys(validationErrors).length >
                                        0 && (
                                        <motion.div
                                            animate={{
                                                opacity: 1,
                                                height: "auto",
                                            }}
                                            exit={{ opacity: 0, height: 0 }}
                                            initial={{ opacity: 0, height: 0 }}
                                            transition={{ duration: 0.3 }}
                                        >
                                            {renderValidationErrors()}
                                        </motion.div>
                                    )}
                                </AnimatePresence>

                                {/* Contenido del paso actual con animación */}
                                <AnimatePresence custom={direction} mode="wait">
                                    <motion.div
                                        key={currentStep}
                                        animate="center"
                                        custom={direction}
                                        exit="exit"
                                        initial="enter"
                                        transition={{
                                            type: "spring",
                                            stiffness: 300,
                                            damping: 30,
                                        }}
                                        variants={contentVariants}
                                    >
                                        {renderCurrentStep()}
                                    </motion.div>
                                </AnimatePresence>
                            </div>
                        </CardBody>

                        <CardFooter className="flex justify-between items-center px-8 sm:px-10 lg:px-12 xl:px-14 py-6 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                            <div className="flex items-center gap-3">
                                <motion.div
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Button
                                        className="font-semibold bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 shadow-md hover:shadow-lg transition-all"
                                        isDisabled={
                                            !canGoBack() || isSubmitting
                                        }
                                        size="lg"
                                        startContent={
                                            <ArrowLeftIcon className="w-5 h-5" />
                                        }
                                        onPress={() =>
                                            handleStepChange("previous")
                                        }
                                    >
                                        Anterior
                                    </Button>
                                </motion.div>

                                {/* Controles de deshacer/rehacer */}
                                <motion.div
                                    animate={{ opacity: 1, scale: 1 }}
                                    className="hidden sm:flex items-center gap-3"
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    <div className="h-10 w-px bg-gradient-to-b from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700" />
                                    <UndoRedoControls size="md" />
                                    <div className="h-10 w-px bg-gradient-to-b from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700" />
                                    <DraftControls size="md" />
                                </motion.div>
                            </div>

                            <motion.div
                                whileHover={{
                                    scale: currentStep === "summary" ? 1 : 1.05,
                                }}
                                whileTap={{
                                    scale: currentStep === "summary" ? 1 : 0.95,
                                }}
                            >
                                <Button
                                    className={
                                        currentStep === "summary"
                                            ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                                            : "bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold shadow-lg hover:shadow-xl transform transition-all"
                                    }
                                    endContent={
                                        isSubmitting ? (
                                            <Spinner color="white" size="sm" />
                                        ) : currentStep === "summary" ? (
                                            <CheckIcon className="w-5 h-5" />
                                        ) : (
                                            <ArrowRightIcon className="w-5 h-5" />
                                        )
                                    }
                                    isDisabled={
                                        !canGoNext() ||
                                        isSubmitting ||
                                        currentStep === "summary"
                                    }
                                    size="lg"
                                    onPress={() => {
                                        if (currentStep !== "summary") {
                                            handleStepChange("next");
                                        }
                                    }}
                                >
                                    {currentStep === "summary"
                                        ? "Usa el botón de arriba para confirmar"
                                        : getNextButtonText()}
                                </Button>
                            </motion.div>
                        </CardFooter>
                    </Card>
                </motion.div>
            </div>

            {/* Sistema de feedback visual */}
            <motion.div
                animate={{ opacity: 1 }}
                initial={{ opacity: 0 }}
                transition={{ delay: 0.5 }}
            >
                <FeedbackToast />
            </motion.div>
        </>
    );
}
