"use client";

import React, { useState, useEffect } from "react";
import {
    Input,
    Textarea,
    Button,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    Modal<PERSON>ooter,
    useDisclosure,
    addToast,
    <PERSON><PERSON><PERSON>,
    Spinner,
} from "@heroui/react";
import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import {
    ChevronLeftIcon,
    UserGroupIcon,
    InformationCircleIcon,
    ExclamationTriangleIcon,
    CheckIcon,
    ArrowPathIcon,
    ShieldCheckIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    Card,
    CardBody,
    CardHeader,
} from "@/shared/components/ui/hero-ui-client";
import {
    useCustomer,
    useValidateCustomerName,
    useUpdateCustomer,
} from "@/features/customers/hooks/useCustomer";
import { triggerRevalidation } from "@/shared/providers/swr-provider";
import {
    CustomerFormProgress,
    calculateEditProgress,
    CustomerFormHeader,
    CustomerInfoMessage,
} from "@/features/customers/components/forms";

// Esquema de validación
const customerSchema = z.object({
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres")
        .transform((val) => val.trim()),
    displayName: z
        .string()
        .max(100, "El nombre para mostrar no puede exceder los 100 caracteres")
        .transform((val) => val?.trim() || null)
        .optional()
        .nullable(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

export default function EditCustomerPage() {
    const params = useParams();
    const customerId = params.id as string;
    const router = useRouter();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const {
        isOpen: isExitModalOpen,
        onOpen: onExitModalOpen,
        onClose: onExitModalClose,
    } = useDisclosure();

    // Estados para la UI
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [formProgress, setFormProgress] = useState(0);
    const [isDirty, setIsDirty] = useState(false);

    // Obtener datos del cliente
    const { customer, isLoading } = useCustomer(customerId);
    const { updateCustomer } = useUpdateCustomer();

    // Configurar react-hook-form con validación zod
    const {
        register,
        handleSubmit,
        formState: { errors, isValid: isFormValid },
        reset,
        watch,
        setValue,
    } = useForm<CustomerFormData>({
        resolver: zodResolver(customerSchema),
        mode: "onChange",
    });

    // Obtener el valor actual del nombre para validación
    const currentName = watch("name");

    // Usar el hook de validación con el nombre actual
    const { isValid: isNameValid, isValidating } = useValidateCustomerName(
        currentName &&
            currentName.length >= 2 &&
            currentName !== (customer as any)?.name
            ? currentName
            : null,
        customerId,
    );

    // Cargar datos iniciales cuando customer cambia
    useEffect(() => {
        if (customer) {
            setValue("name", (customer as any).name);
            setValue("displayName", (customer as any).displayName || "");
        }
    }, [customer, setValue]);

    // Calcular progreso del formulario
    useEffect(() => {
        const name = watch("name");
        const progress = calculateEditProgress(
            (customer as any)?.name || "",
            name || "",
            isFormValid &&
                (currentName === (customer as any)?.name ||
                    isNameValid !== false),
            !!errors.name,
        );

        setFormProgress(progress);
    }, [watch, isFormValid, errors.name, customer, currentName, isNameValid]);

    // Detectar cambios en el formulario
    useEffect(() => {
        const subscription = watch((value, { name, type }) => {
            if (type === "change" && customer) {
                // Check if any values are different from the original
                const hasChanges =
                    value.name !== (customer as any).name ||
                    (value.displayName || "") !==
                        ((customer as any).displayName || "");

                setIsDirty(hasChanges);
            }
        });

        return () => subscription.unsubscribe();
    }, [watch, customer]);

    // Manejar envío del formulario
    const onSubmit = async (data: CustomerFormData) => {
        // Si el nombre no ha cambiado, no es necesario validar
        if (customer && (customer as any).name === data.name.trim()) {
            // Mostrar modal de confirmación directamente
            onOpen();

            return;
        }

        // Verificar si se está validando
        if (isValidating) {
            addToast({
                title: "Validando",
                description:
                    "Por favor, espere mientras se valida el nombre del cliente",
                color: "primary",
            });

            return;
        }

        // Validar que el nombre no esté duplicado (excluyendo el cliente actual)
        if (isNameValid === false) {
            addToast({
                title: "Nombre duplicado",
                description: `El cliente "${data.name}" ya existe en el sistema`,
                color: "danger",
            });

            return;
        }

        // Mostrar modal de confirmación
        onOpen();
    };

    // Manejar confirmación de actualización
    const handleConfirmedSubmit = async () => {
        try {
            setIsSubmitting(true);
            const data = watch();
            const trimmedName = data.name.trim();
            const trimmedDisplayName = data.displayName?.trim() || null;

            // Verificar si hay cambios
            const hasChanges =
                customer &&
                ((customer as any).name !== trimmedName ||
                    (customer as any).displayName !== trimmedDisplayName);

            if (!hasChanges) {
                // No hay cambios, mostrar mensaje y redirigir
                addToast({
                    title: "Sin cambios",
                    description:
                        "No se detectaron cambios en la información del cliente",
                    color: "primary",
                });

                onClose();
                router.push("/dashboard/customers");

                return;
            }

            // Actualizar cliente usando la server action
            console.log(
                `[EditCustomer] Actualizando cliente ${customerId} con nombre: ${trimmedName} y displayName: ${trimmedDisplayName}`,
            );
            const result = await updateCustomer(
                customerId,
                {
                    name: trimmedName,
                    displayName: trimmedDisplayName,
                },
                false, // forceUpdate
            );

            console.log(`[EditCustomer] Resultado de actualización:`, result);

            // Verificar que result existe
            if (!result) {
                throw new Error(
                    "No se pudo completar la operación de actualización",
                );
            }

            if (!result.success) {
                throw new Error(
                    result.error || "Error al actualizar el cliente",
                );
            }

            // Mostrar mensaje de éxito
            addToast({
                title: "Cliente actualizado",
                description: `El cliente "${trimmedName}" ha sido actualizado exitosamente`,
                color: "success",
                icon: <CheckIcon className="w-5 h-5" />,
            });

            // Revalidar datos y redirigir
            triggerRevalidation("customers", customerId);
            setTimeout(() => {
                router.push("/dashboard/customers");
            }, 1000);
        } catch (error: any) {
            console.error("[EditCustomer] Error al actualizar:", error);
            addToast({
                title: "Error",
                description: error.message || "Error al actualizar el cliente",
                color: "danger",
                icon: <ExclamationTriangleIcon className="w-5 h-5" />,
            });
        } finally {
            setIsSubmitting(false);
            onClose();
        }
    };

    // Manejar salida sin guardar
    const handleExit = () => {
        if (isDirty) {
            onExitModalOpen();
        } else {
            router.push("/dashboard/customers");
        }
    };

    // Función helper para obtener el color del input
    const getInputColor = () => {
        if (errors.name) return "danger";
        if (
            currentName !== (customer as any)?.name &&
            currentName?.length >= 2
        ) {
            if (isValidating) return "default";
            if (isNameValid === false) return "danger";
            if (isNameValid === true) return "success";
        }

        return "default";
    };

    // Mostrar spinner mientras carga
    if (isLoading) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Clientes", href: "/dashboard/customers" },
                    { label: "Editar Cliente" },
                ]}
                subtitle="Actualiza la información del cliente"
                title="Editar Cliente"
            >
                <div className="h-[50vh] flex items-center justify-center">
                    <div className="text-center">
                        <Spinner className="mb-4" size="lg" />
                        <p className="text-gray-600 dark:text-gray-400">
                            Cargando información del cliente...
                        </p>
                    </div>
                </div>
            </DashboardLayout>
        );
    }

    // Mostrar error si no se encuentra el cliente
    if (!customer) {
        return (
            <DashboardLayout
                breadcrumbs={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Clientes", href: "/dashboard/customers" },
                    { label: "Error" },
                ]}
                subtitle="Cliente no encontrado"
                title="Error"
            >
                <div className="max-w-2xl mx-auto">
                    <Card className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-center">
                        <CardBody className="p-8">
                            <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
                            <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">
                                Cliente no encontrado
                            </h2>
                            <p className="text-red-600 dark:text-red-300 mb-4">
                                No se ha encontrado el cliente con el ID
                                especificado.
                            </p>
                            <Button
                                as={Link}
                                className="bg-gradient-to-r from-primary-600 to-primary-700 text-white"
                                href="/dashboard/customers"
                            >
                                Volver a la lista de clientes
                            </Button>
                        </CardBody>
                    </Card>
                </div>
            </DashboardLayout>
        );
    }

    // Animaciones
    const containerVariants = {
        initial: { opacity: 0, y: 20 },
        animate: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.3,
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        initial: { opacity: 0, x: -20 },
        animate: { opacity: 1, x: 0 },
    };

    return (
        <DashboardLayout
            actions={
                <div className="flex items-center gap-4">
                    <CustomerFormProgress
                        className="hidden md:block w-48"
                        value={formProgress}
                    />
                    <Button
                        startContent={<ChevronLeftIcon className="w-4 h-4" />}
                        variant="light"
                        onPress={() => router.push("/dashboard/customers")}
                    >
                        Volver
                    </Button>
                </div>
            }
            breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Clientes", href: "/dashboard/customers" },
                { label: "Editar Cliente" },
            ]}
            subtitle={`Actualiza la información de ${(customer as any).name}`}
            title="Editar Cliente"
        >
            <motion.div
                animate="animate"
                className="max-w-2xl mx-auto"
                initial="initial"
                variants={containerVariants}
            >
                {/* Progress bar móvil */}
                <div className="mb-6 md:hidden">
                    <CustomerFormProgress value={formProgress} />
                </div>

                <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:translate-y-[-2px]">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 p-6">
                        <CustomerFormHeader
                            subtitle="Actualiza los datos del cliente en el sistema"
                            title="Información del Cliente"
                        />
                    </CardHeader>

                    <CardBody className="p-6 md:p-8">
                        <form
                            className="space-y-8"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            <motion.div
                                className="space-y-2"
                                variants={itemVariants}
                            >
                                <label
                                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
                                    htmlFor="customerName"
                                >
                                    <span>Nombre del Cliente</span>
                                    <Tooltip content="Nombre completo del cliente. Ejemplo: Juan Pérez, Empresa XYZ, etc.">
                                        <InformationCircleIcon className="ml-1.5 text-gray-400 h-3.5 w-3.5 hover:text-primary-500 transition-colors" />
                                    </Tooltip>
                                </label>

                                <Input
                                    {...register("name")}
                                    isRequired
                                    classNames={{
                                        base: "max-w-full",
                                        inputWrapper: `
                                            bg-transparent shadow-sm border-2 transition-all duration-200
                                            data-[hover=true]:border-primary-500/50 
                                            data-[focus=true]:border-primary-500
                                        `,
                                        input: "text-lg font-medium",
                                    }}
                                    color={getInputColor()}
                                    description="Este nombre se utilizará para identificar al cliente en el sistema"
                                    endContent={
                                        (customer as any)?.name !==
                                            currentName &&
                                        currentName?.length >= 2 ? (
                                            isValidating ? (
                                                <ArrowPathIcon className="w-4 h-4 text-primary animate-spin" />
                                            ) : isNameValid ? (
                                                <CheckIcon className="w-4 h-4 text-success" />
                                            ) : (
                                                <ExclamationTriangleIcon className="w-4 h-4 text-danger" />
                                            )
                                        ) : null
                                    }
                                    errorMessage={errors.name?.message}
                                    id="customerName"
                                    placeholder="Ej: Juan Pérez, Empresa XYZ"
                                    startContent={
                                        <UserGroupIcon className="text-primary-500 h-4 w-4" />
                                    }
                                    variant="bordered"
                                />

                                <CustomerInfoMessage
                                    message={`Nombre actual: ${(customer as any).name}`}
                                    show={
                                        (customer as any)?.name !==
                                            currentName &&
                                        currentName?.length > 0
                                    }
                                    title="Valor original"
                                    type="info"
                                />
                            </motion.div>

                            {/* Campo de nombre para mostrar */}
                            <motion.div
                                className="space-y-2"
                                variants={itemVariants}
                            >
                                <label
                                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
                                    htmlFor="displayName"
                                >
                                    <span>Nombre para Mostrar (Opcional)</span>
                                    <Tooltip content="Cómo se mostrará en la interfaz. Si no se especifica, se usará el nombre del cliente.">
                                        <InformationCircleIcon className="ml-1.5 text-gray-400 h-3.5 w-3.5 hover:text-primary-500 transition-colors" />
                                    </Tooltip>
                                </label>

                                <Textarea
                                    {...register("displayName")}
                                    classNames={{
                                        base: "max-w-full",
                                        inputWrapper: `
                                            bg-transparent shadow-sm border-2 transition-all duration-200
                                            data-[hover=true]:border-primary-500/50 
                                            data-[focus=true]:border-primary-500
                                        `,
                                        input: "text-base",
                                    }}
                                    description="Ej: Empresa ABC - División XYZ"
                                    errorMessage={errors.displayName?.message}
                                    id="displayName"
                                    maxRows={3}
                                    minRows={2}
                                    placeholder="Nombre descriptivo más largo para mostrar en la interfaz"
                                    variant="bordered"
                                />

                                <CustomerInfoMessage
                                    message={`Nombre para mostrar actual: ${(customer as any).displayName || "(no definido)"}`}
                                    show={
                                        (customer as any)?.displayName !==
                                            watch("displayName") &&
                                        watch("displayName")?.length !==
                                            (customer as any)?.displayName
                                                ?.length
                                    }
                                    title="Valor original"
                                    type="info"
                                />
                            </motion.div>

                            {/* Mensaje de información importante */}
                            <motion.div variants={itemVariants}>
                                <CustomerInfoMessage
                                    message="Recuerda que este cambio afectará a todas las órdenes asociadas a este cliente. Asegúrate de que la información sea correcta."
                                    show={
                                        isDirty &&
                                        (customer as any)?.name !== currentName
                                    }
                                    title="Antes de actualizar el cliente"
                                    type="warning"
                                />
                            </motion.div>

                            {/* Botones de acción con mejor estilo */}
                            <motion.div
                                className="flex justify-end gap-3 pt-4"
                                variants={itemVariants}
                            >
                                <Button
                                    className="font-medium text-gray-700 dark:text-gray-200"
                                    variant="flat"
                                    onPress={handleExit}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    className="bg-gradient-to-r from-primary-600 to-primary-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:translate-y-[-1px]"
                                    isDisabled={
                                        !isDirty ||
                                        !isFormValid ||
                                        isSubmitting ||
                                        isValidating ||
                                        (currentName !==
                                            (customer as any)?.name &&
                                            isNameValid === false)
                                    }
                                    isLoading={isSubmitting}
                                    type="submit"
                                >
                                    {isSubmitting
                                        ? "Procesando..."
                                        : "Actualizar Cliente"}
                                </Button>
                            </motion.div>
                        </form>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Modal de confirmación con mejor diseño */}
            <Modal
                backdrop="blur"
                classNames={{
                    backdrop: "bg-gradient-to-t from-black/20 to-black/10",
                    base: "border border-gray-200 dark:border-gray-800 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md",
                    header: "border-b border-gray-100 dark:border-gray-800",
                    footer: "border-t border-gray-100 dark:border-gray-800",
                    closeButton: "hover:bg-gray-100 dark:hover:bg-gray-800",
                }}
                isOpen={isOpen}
                motionProps={{
                    variants: {
                        enter: {
                            y: 0,
                            opacity: 1,
                            transition: {
                                duration: 0.3,
                                ease: "easeOut",
                            },
                        },
                        exit: {
                            y: -20,
                            opacity: 0,
                            transition: {
                                duration: 0.2,
                                ease: "easeIn",
                            },
                        },
                    },
                }}
                placement="center"
                onClose={onClose}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="flex items-center gap-2">
                                <ShieldCheckIcon className="w-5 h-5 text-primary-600" />
                                <span>Confirmar Actualización</span>
                            </ModalHeader>
                            <ModalBody>
                                <div className="text-center mb-4">
                                    <div className="bg-primary-50 dark:bg-primary-900/20 mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-3">
                                        <UserGroupIcon className="w-8 h-8 text-primary-600 dark:text-primary-400" />
                                    </div>
                                    <p className="text-lg font-semibold">
                                        ¿Estás seguro de actualizar este
                                        cliente?
                                    </p>
                                </div>

                                <div className="space-y-4">
                                    {(customer as any).name !==
                                        watch("name") && (
                                        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800/40">
                                            <p className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                                                Cambio de nombre:
                                            </p>
                                            <div className="space-y-1">
                                                <div className="flex items-center gap-2">
                                                    <span className="text-gray-600 dark:text-gray-400 text-sm">
                                                        De:
                                                    </span>
                                                    <span className="font-mono bg-white dark:bg-gray-800 px-2 py-1 rounded text-sm text-gray-900 dark:text-gray-100">
                                                        {(customer as any).name}
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <span className="text-gray-600 dark:text-gray-400 text-sm">
                                                        A:
                                                    </span>
                                                    <span className="font-mono bg-white dark:bg-gray-800 px-2 py-1 rounded text-sm text-primary-600 dark:text-primary-400 font-medium">
                                                        {watch("name")}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    <CustomerInfoMessage
                                        message="Todas las órdenes asociadas a este cliente serán actualizadas con el nuevo nombre."
                                        show={true}
                                        title="Impacto del cambio"
                                        type="warning"
                                    />
                                </div>
                            </ModalBody>
                            <ModalFooter>
                                <Button
                                    isDisabled={isSubmitting}
                                    variant="flat"
                                    onPress={onClose}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    className="bg-gradient-to-r from-primary-600 to-primary-700 text-white"
                                    isLoading={isSubmitting}
                                    onPress={handleConfirmedSubmit}
                                >
                                    Confirmar actualización
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>

            {/* Modal de confirmación para salir sin guardar */}
            <Modal
                backdrop="blur"
                classNames={{
                    backdrop: "bg-gradient-to-t from-black/20 to-black/10",
                    base: "border border-gray-200 dark:border-gray-800 bg-white/90 dark:bg-gray-900/90 backdrop-blur-md",
                    header: "border-b border-gray-100 dark:border-gray-800",
                    footer: "border-t border-gray-100 dark:border-gray-800",
                    closeButton: "hover:bg-gray-100 dark:hover:bg-gray-800",
                }}
                isOpen={isExitModalOpen}
                motionProps={{
                    variants: {
                        enter: {
                            y: 0,
                            opacity: 1,
                            transition: {
                                duration: 0.3,
                                ease: "easeOut",
                            },
                        },
                        exit: {
                            y: -20,
                            opacity: 0,
                            transition: {
                                duration: 0.2,
                                ease: "easeIn",
                            },
                        },
                    },
                }}
                placement="center"
                onClose={onExitModalClose}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="flex flex-col gap-1">
                                <div className="flex items-center gap-2">
                                    <ExclamationTriangleIcon className="w-5 h-5 text-amber-600" />
                                    <span>Cambios sin guardar</span>
                                </div>
                            </ModalHeader>
                            <ModalBody>
                                <div className="text-center mb-4">
                                    <div className="bg-amber-50 dark:bg-amber-900/20 mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-3">
                                        <ExclamationTriangleIcon className="w-8 h-8 text-amber-600 dark:text-amber-400" />
                                    </div>
                                </div>
                                <p className="text-gray-700 dark:text-gray-300 text-center">
                                    Tienes cambios sin guardar. ¿Estás seguro de
                                    que deseas salir? Los cambios se perderán.
                                </p>
                            </ModalBody>
                            <ModalFooter>
                                <Button
                                    color="primary"
                                    variant="light"
                                    onPress={onClose}
                                >
                                    Continuar editando
                                </Button>
                                <Button
                                    color="danger"
                                    onPress={() => {
                                        onClose();
                                        router.push("/dashboard/customers");
                                    }}
                                >
                                    Salir sin guardar
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </DashboardLayout>
    );
}
