-- Migration: Enhanced Packing System
-- Description: Adds new fields and tables for advanced packing functionality

-- 1. Update Packing table
ALTER TABLE "Packing"
ADD COLUMN IF NOT EXISTS "companyInfo" JSONB,
ADD COLUMN IF NOT EXISTS "transportNotes" TEXT,
ADD COLUMN IF NOT EXISTS "totalBoxes" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "totalBags" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "packingType" TEXT;

-- 2. Update PackingDetail table
ALTER TABLE "PackingDetail" 
ADD COLUMN IF NOT EXISTS "modelCode" TEXT,
ADD COLUMN IF NOT EXISTS "colorName" TEXT,
ADD COLUMN IF NOT EXISTS "partNumber" TEXT,
ADD COLUMN IF NOT EXISTS "qualityType" TEXT DEFAULT 'primera',
ADD COLUMN IF NOT EXISTS "packagingType" TEXT,
ADD COLUMN IF NOT EXISTS "packagingUnits" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "piecesPerUnit" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "loosePieces" INTEGER DEFAULT 0;

-- Add check constraint for qualityType
ALTER TABLE "PackingDetail"
ADD CONSTRAINT "PackingDetail_qualityType_check" 
CHECK ("qualityType" IN ('primera', 'segunda', 'manchada', 'incompleta'));

-- 3. Create PackingSummary table
CREATE TABLE IF NOT EXISTS "PackingSummary" (
    "id" TEXT NOT NULL,
    "packingId" TEXT NOT NULL,
    "orderId" TEXT,
    "cutOrderNumber" TEXT,
    "totalFirstQuality" INTEGER NOT NULL DEFAULT 0,
    "totalSecondQuality" INTEGER NOT NULL DEFAULT 0,
    "totalDefective" INTEGER NOT NULL DEFAULT 0,
    "totalIncomplete" INTEGER NOT NULL DEFAULT 0,
    "boxesBySize" JSONB,
    "bagsBySize" JSONB,
    "piecesBySize" JSONB,
    "totalBoxes" INTEGER NOT NULL DEFAULT 0,
    "totalBags" INTEGER NOT NULL DEFAULT 0,
    "totalLoosePieces" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PackingSummary_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints
ALTER TABLE "PackingSummary"
ADD CONSTRAINT "PackingSummary_packingId_fkey" 
FOREIGN KEY ("packingId") REFERENCES "Packing"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "PackingSummary"
ADD CONSTRAINT "PackingSummary_orderId_fkey" 
FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add unique constraint
ALTER TABLE "PackingSummary"
ADD CONSTRAINT "PackingSummary_packingId_orderId_key" 
UNIQUE ("packingId", "orderId");

-- Add indexes
CREATE INDEX IF NOT EXISTS "PackingSummary_packingId_idx" ON "PackingSummary"("packingId");
CREATE INDEX IF NOT EXISTS "PackingSummary_orderId_idx" ON "PackingSummary"("orderId");

-- 4. Create CompanySettings table
CREATE TABLE IF NOT EXISTS "CompanySettings" (
    "id" TEXT NOT NULL DEFAULT 'default',
    "companyName" TEXT NOT NULL,
    "companyLogo" TEXT,
    "rfc" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "country" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "website" TEXT,
    "defaultBoxCapacity" INTEGER DEFAULT 25,
    "defaultBagCapacity" INTEGER DEFAULT 20,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CompanySettings_pkey" PRIMARY KEY ("id")
);

-- 5. Insert default company settings (update with your actual data)
INSERT INTO "CompanySettings" (
    "id",
    "companyName",
    "address",
    "city",
    "state",
    "country",
    "defaultBoxCapacity",
    "defaultBagCapacity"
) VALUES (
    'default',
    'Tu Empresa S.A. de C.V.',
    'Dirección de la empresa',
    'Ciudad',
    'Estado',
    'México',
    25,
    20
) ON CONFLICT ("id") DO NOTHING;

-- 6. Update existing PackingDetails to have default qualityType
UPDATE "PackingDetail" 
SET "qualityType" = 'primera' 
WHERE "qualityType" IS NULL;