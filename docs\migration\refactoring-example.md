# 📋 Ejemplo de Refactorización: Módulo Orders

## Estado Actual vs Estado Deseado

### 🔴 Estructura Actual (Problemática)
```
lib/actions/orders/create.ts      # Mezcla validación, lógica y DB
components/orders/OrderForm.tsx   # UI con lógica de negocio
features/orders/...              # Duplicación con components
```

### 🟢 Estructura Propuesta (Clean Architecture)
```
src/
├── core/
│   ├── domain/order/           # Entidades y reglas de negocio
│   └── application/order/      # Casos de uso
├── infrastructure/
│   └── persistence/            # Implementación de repositorios
└── presentation/
    └── features/orders/        # UI específica de orders
```

## Paso a Paso: Refactorizar Creación de Order

### 1️⃣ Crear la Entidad de Dominio

```typescript
// src/core/domain/order/entities/order.entity.ts
export class Order {
  private constructor(
    private readonly props: OrderProps
  ) {
    Object.freeze(this);
  }

  static create(params: CreateOrderParams): Result<Order> {
    // Validaciones de negocio
    const errors: string[] = [];

    if (!params.customerId) {
      errors.push('Customer is required');
    }

    if (!params.items || params.items.length === 0) {
      errors.push('Order must have at least one item');
    }

    if (params.estimatedDeliveryDate < new Date()) {
      errors.push('Delivery date must be in the future');
    }

    // Validar cantidades
    const hasInvalidQuantities = params.items.some(
      item => item.quantity <= 0
    );
    if (hasInvalidQuantities) {
      errors.push('All items must have positive quantities');
    }

    if (errors.length > 0) {
      return Result.fail(errors.join(', '));
    }

    // Crear la orden con estado inicial
    const order = new Order({
      id: generateId(),
      customerId: params.customerId,
      status: OrderStatus.PENDING,
      items: params.items,
      estimatedDeliveryDate: params.estimatedDeliveryDate,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return Result.ok(order);
  }

  // Métodos de negocio
  canBeAssigned(): boolean {
    return this.props.status === OrderStatus.PENDING;
  }

  assign(): Result<void> {
    if (!this.canBeAssigned()) {
      return Result.fail('Order cannot be assigned in current status');
    }
    this.props.status = OrderStatus.IN_PROGRESS;
    this.props.updatedAt = new Date();
    return Result.ok();
  }

  getTotalQuantity(): number {
    return this.props.items.reduce((sum, item) => sum + item.quantity, 0);
  }

  // Getters
  get id() { return this.props.id; }
  get customerId() { return this.props.customerId; }
  get status() { return this.props.status; }
  get items() { return [...this.props.items]; } // Copia para inmutabilidad
}
```

### 2️⃣ Definir el Puerto del Repositorio

```typescript
// src/core/ports/repositories/order.repository.ts
export interface IOrderRepository {
  save(order: Order): Promise<void>;
  findById(id: string): Promise<Order | null>;
  findByCustomer(customerId: string): Promise<Order[]>;
  update(order: Order): Promise<void>;
  delete(id: string): Promise<void>;
}
```
### 3️⃣ Crear el Caso de Uso

```typescript
// src/core/application/order/create-order.use-case.ts
export class CreateOrderUseCase {
  constructor(
    private orderRepository: IOrderRepository,
    private customerRepository: ICustomerRepository,
    private inventoryService: IInventoryService,
    private notificationService: INotificationService
  ) {}

  async execute(input: CreateOrderDTO): Promise<Result<OrderResponseDTO>> {
    // 1. Verificar que el cliente existe
    const customer = await this.customerRepository.findById(input.customerId);
    if (!customer) {
      return Result.fail('Customer not found');
    }

    // 2. Verificar disponibilidad de inventario
    for (const item of input.items) {
      const available = await this.inventoryService.checkAvailability(
        item.modelId,
        item.colorId,
        item.sizeId,
        item.quantity
      );
      
      if (!available) {
        return Result.fail(
          `Insufficient inventory for model ${item.modelId}`
        );
      }
    }

    // 3. Crear la orden usando la entidad de dominio
    const orderResult = Order.create({
      customerId: input.customerId,
      items: input.items,
      estimatedDeliveryDate: input.estimatedDeliveryDate
    });

    if (orderResult.isFailure) {
      return Result.fail(orderResult.error);
    }

    const order = orderResult.getValue();

    // 4. Persistir la orden
    await this.orderRepository.save(order);

    // 5. Reservar inventario
    await this.inventoryService.reserve(order.id, order.items);

    // 6. Notificar al cliente
    await this.notificationService.sendOrderConfirmation(
      customer.email,
      order
    );

    // 7. Retornar DTO de respuesta
    return Result.ok(OrderMapper.toDTO(order));
  }
}
```
### 4️⃣ Implementar el Repositorio

```typescript
// src/infrastructure/persistence/prisma/repositories/order.repository.impl.ts
export class PrismaOrderRepository implements IOrderRepository {
  constructor(private prisma: PrismaClient) {}

  async save(order: Order): Promise<void> {
    const data = OrderMapper.toPersistence(order);
    
    await this.prisma.$transaction(async (tx) => {
      // Crear la orden
      const createdOrder = await tx.order.create({
        data: {
          id: data.id,
          customerId: data.customerId,
          statusId: data.statusId,
          estimatedDeliveryDate: data.estimatedDeliveryDate
        }
      });

      // Crear los items
      for (const item of data.items) {
        const garment = await tx.garment.create({
          data: {
            orderId: createdOrder.id,
            modelId: item.modelId,
            colorId: item.colorId
          }
        });

        // Crear los tamaños
        for (const size of item.sizes) {
          await tx.garmentSize.create({
            data: {
              garmentId: garment.id,
              sizeId: size.sizeId,
              totalQuantity: size.quantity,
              usedQuantity: 0
            }
          });
        }
      }
    });
  }

  async findById(id: string): Promise<Order | null> {
    const data = await this.prisma.order.findUnique({
      where: { id },
      include: {
        garments: {
          include: {
            model: true,
            color: true,
            sizes: {
              include: { size: true }
            }
          }
        },
        customer: true,
        status: true
      }
    });

    return data ? OrderMapper.toDomain(data) : null;
  }
}
```
### 5️⃣ Refactorizar el Server Action

```typescript
// src/presentation/app/dashboard/orders/actions.ts
'use server';

import { container } from '@/infrastructure/container';
import { CreateOrderUseCase } from '@/core/application/order';
import { createOrderSchema } from './schemas';

export async function createOrder(formData: FormData) {
  // 1. Autenticación
  const session = await auth();
  if (!session?.user) {
    return { error: 'Unauthorized' };
  }

  // 2. Validación de entrada
  const validation = createOrderSchema.safeParse({
    customerId: formData.get('customerId'),
    items: JSON.parse(formData.get('items') as string),
    estimatedDeliveryDate: formData.get('estimatedDeliveryDate')
  });

  if (!validation.success) {
    return { 
      error: 'Invalid input',
      fieldErrors: validation.error.flatten().fieldErrors 
    };
  }

  try {
    // 3. Ejecutar caso de uso
    const useCase = container.resolve(CreateOrderUseCase);
    const result = await useCase.execute(validation.data);

    if (result.isFailure) {
      return { error: result.error };
    }

    // 4. Revalidar caché
    revalidatePath('/dashboard/orders');
    revalidateTag('orders');

    return { 
      success: true,
      data: result.getValue() 
    };
  } catch (error) {
    console.error('Error creating order:', error);
    return { error: 'An unexpected error occurred' };
  }
}
```
### 6️⃣ Componente UI Simplificado

```typescript
// src/presentation/features/orders/components/CreateOrderForm.tsx
'use client';

export function CreateOrderForm() {
  const [isPending, startTransition] = useTransition();
  
  async function handleSubmit(formData: FormData) {
    startTransition(async () => {
      const result = await createOrder(formData);
      
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Order created successfully');
        router.push(`/dashboard/orders/${result.data.id}`);
      }
    });
  }

  return (
    <form action={handleSubmit}>
      {/* UI pura sin lógica de negocio */}
    </form>
  );
}
```

## Beneficios de esta Refactorización

1. **Testabilidad**: Puedes testear la lógica de negocio sin UI ni DB
2. **Reutilización**: El caso de uso puede ser llamado desde API, CLI, etc.
3. **Mantenibilidad**: Cambios en UI no afectan lógica de negocio
4. **Escalabilidad**: Fácil agregar nuevas validaciones o reglas
5. **Claridad**: Cada capa tiene una responsabilidad clara

## Próximos Pasos

1. Aplicar el mismo patrón a otros módulos
2. Crear tests unitarios para entidades y casos de uso
3. Configurar inyección de dependencias
4. Documentar patrones y convenciones