"use client";

import type { SWRConfiguration } from "swr";

import useSWR from "swr";
import { useCallback, useEffect, useMemo } from "react";

import {
    getOrders,
    getOrderStatuses,
    deleteOrder as remove,
    submitOrder,
    getOrder,
    updateOrderWithDetails,
} from "@/features/orders/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";
import { useRevalidationListener } from "@/shared/hooks/useRevalidationListener";

// Obtener helpers de revalidación para el cliente
const { revalidateData } = createClientRevalidation("orders");

// Importaciones desde el nuevo archivo de tipos
import {
    Order,
    OrderQueryOptions,
    OrderCreateData,
} from "@/features/orders/types/orders";

// Se usa el tipo Customer de features/customers/types

export interface Assignment {
    id: string;
    contractorId: string;
    quantity: number;
}

export interface OrdersResponse {
    orders: Order[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

export interface OrderResponse {
    success: boolean;
    data?: Order;
    error?: string;
}

export interface OrderCreateFullData extends OrderCreateData {
    // Ya están todos los campos incluidos en OrderCreateData
}

// Configuración SWR optimizada para mejorar rendimiento
const DEFAULT_SWR_CONFIG: SWRConfiguration = {
    revalidateIfStale: false, // Solo revalidar cuando sea necesario
    revalidateOnMount: true, // Mantener la validación inicial
    revalidateOnFocus: false, // Desactivar revalidación automática al enfocar la ventana
    revalidateOnReconnect: true, // Mantener revalidación al reconectar
    errorRetryCount: 2, // Limitar reintentos en caso de error
    dedupingInterval: 5000, // Evitar solicitudes duplicadas (5 segundos)
    focusThrottleInterval: 10000, // Espaciar revalidaciones por foco (10 segundos)
    keepPreviousData: true, // Mantener datos anteriores mientras se carga
};

// Configuración SWR optimizada específicamente para la vista de calendario
const CALENDAR_SWR_CONFIG: SWRConfiguration = {
    ...DEFAULT_SWR_CONFIG,
    revalidateIfStale: false, // No revalidar automáticamente en calendario
    revalidateOnReconnect: false, // No revalidar en reconexión para calendario
    dedupingInterval: 15000, // Mayor intervalo para evitar solicitudes duplicadas (15 segundos)
    errorRetryCount: 1, // Menos reintentos para calendario
};

const DEFAULT_OPTIONS: OrderQueryOptions = {
    page: 1,
    limit: 10,
    sortBy: "createdAt",
    sortDirection: "desc",
};

export type SortDirection = "asc" | "desc";

/**
 * Hook para obtener todas las órdenes
 * @param options Opciones de filtrado y paginación
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de órdenes, paginación, estado de carga y errores
 */
export function useOrders(
    options: {
        search?: string;
        statusId?: string;
        customerId?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
    config?: SWRConfiguration,
) {
    // Si el statusId es "all", lo quitamos de las opciones
    const filteredOptions = { ...options };

    if (filteredOptions.statusId === "all") {
        delete filteredOptions.statusId;
    }

    // Convertir opciones a cadena para clave de caché
    const optionsKey = JSON.stringify(filteredOptions);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        ["orders", optionsKey],
        async () => getOrders(filteredOptions),
        {
            ...DEFAULT_SWR_CONFIG,
            ...config,
        },
    );

    // Escuchar eventos de revalidación
    const { isRevalidating } = useRevalidationListener("orders");

    return {
        orders: data?.data?.orders || [],
        pagination: data?.data?.pagination,
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook personalizado para gestionar pedidos
 * @param options Opciones de filtrado y paginación
 * @param isCalendarView Indica si estamos en vista de calendario para optimizar comportamiento
 * @returns Datos de órdenes, estados y funciones para manipularlas
 */
export function useOrder(
    options: OrderQueryOptions = DEFAULT_OPTIONS,
    isCalendarView: boolean = false,
) {
    // Normalize options by merging with defaults
    const queryOptions = { ...DEFAULT_OPTIONS, ...options };

    // Create a unique key for SWR based on the query options
    const ordersKey = JSON.stringify(["orders", queryOptions]);

    // Crear clave de cache con opciones para prefetching
    const createOrdersKey = (opts: Partial<OrderQueryOptions> = {}) => {
        const mergedOptions = { ...queryOptions, ...opts };

        return JSON.stringify(["orders", mergedOptions]);
    };

    // Create stable SWR configuration to avoid hooks changing
    const swrConfig = useMemo(() => {
        return isCalendarView ? CALENDAR_SWR_CONFIG : DEFAULT_SWR_CONFIG;
    }, [isCalendarView]);

    // Fetch orders with optimized SWR configuration - usar configuración específica para calendario
    const {
        data: ordersData,
        error: ordersError,
        isLoading: isLoadingOrders,
        isValidating: isValidatingOrders,
        mutate: mutateOrders,
    } = useSWR(
        ordersKey,
        async () => {
            try {
                const result = await getOrders({
                    page: queryOptions.page,
                    perPage: queryOptions.limit,
                    statusId: queryOptions.statusId,
                    contractorId: queryOptions.contractorId,
                    search: queryOptions.search,
                    orderBy: queryOptions.sortBy,
                    order: queryOptions.sortDirection,
                });

                // Asegurar que retornamos un objeto plano serializable
                if (result && typeof result === "object") {
                    return JSON.parse(JSON.stringify(result));
                }

                return result;
            } catch (error) {
                // Retornar un objeto de error compatible con ActionResponse
                return {
                    success: false,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Error desconocido al obtener órdenes",
                };
            }
        },
        swrConfig,
    );

    // Fetch order statuses with SWR
    const {
        data: statusesData,
        error: statusesError,
        isLoading: isLoadingStatuses,
    } = useSWR("orderStatuses", getOrderStatuses, {
        ...DEFAULT_SWR_CONFIG,
        revalidateOnFocus: false,
        dedupingInterval: 60000, // Cache for longer as these rarely change
    });

    // Function to fetch a single order by ID
    const fetchOrder = async (
        id: string,
        shouldRevalidate: boolean = true,
    ): Promise<Order | null> => {
        try {
            const response = await getOrder(id);

            if (response.success && response.data) {
                // Si estamos en vista de calendario y no se desea revalidar, evitar la revalidación
                if (!shouldRevalidate && isCalendarView) {
                    // Devolver los datos sin revalidar
                    return response.data;
                }

                // En caso contrario, proceder con el comportamiento normal
                return response.data;
            }

            return null;
        } catch (error) {
            // REMOVED: console.error("Error al obtener el pedido:", error);

            return null;
        }
    };

    // Function to create a new order with optimistic update
    const addOrder = async (
        orderData: OrderCreateData,
    ): Promise<Order | null> => {
        try {
            // Crear un objeto temporal para la actualización optimista
            const optimisticOrder: Order = {
                id: `temp-${Date.now()}`,
                status: {
                    id: orderData.statusId,
                    name: "Pendiente", // Valor temporal
                    color: "#808080",
                },
                customer: {
                    id: orderData.customerId,
                    name: "Cliente", // Valor temporal
                },
                receivedDate: new Date(orderData.receivedDate),
                estimatedDeliveryDate: orderData.estimatedDeliveryDate
                    ? new Date(orderData.estimatedDeliveryDate)
                    : undefined,
                transferNumber: orderData.transferNumber || undefined,
                cutOrder: orderData.cutOrder || undefined,
                batch: orderData.batch || undefined,
                createdAt: new Date(),
                updatedAt: new Date(),
                parts: orderData.parts
                    ? orderData.parts.map((p) => ({
                          id: `temp-part-${Date.now()}-${Math.random()}`,
                          code: p.code,
                          orderId: `temp-${Date.now()}`,
                          createdAt: new Date(),
                          updatedAt: new Date(),
                      }))
                    : [],
                notes: orderData.notes
                    ? orderData.notes.map((n) => ({
                          id: `temp-note-${Date.now()}-${Math.random()}`,
                          content: n.content,
                          statusId: n.statusId,
                          importanceId: n.importanceId,
                          orderId: `temp-${Date.now()}`,
                          authorId: "system",
                          createdAt: new Date(),
                          updatedAt: new Date(),
                          status: {
                              id: n.statusId,
                              name: "Pendiente",
                              color: "#808080",
                          },
                          importance: {
                              id: n.importanceId,
                              name: "Media", // Valor temporal
                              color: "#F59E0B", // Valor temporal
                              iconName: "ExclamationTriangleIcon", // Valor temporal
                          },
                          author: {
                              id: "system",
                              name: "Sistema",
                              email: null,
                              image: null,
                          },
                      }))
                    : [],
                assignments: [],
            };

            // Actualizar caché de manera optimista
            await mutateOrders(
                (prevData: any) => {
                    if (!prevData || !prevData.data) return prevData;

                    return {
                        ...prevData,
                        data: {
                            ...prevData.data,
                            orders: [optimisticOrder, ...prevData.data.orders],
                            pagination: {
                                ...prevData.data.pagination,
                                total: prevData.data.pagination.total + 1,
                            },
                        },
                    };
                },
                false, // No revalidar inmediatamente
            );

            // Llamar a submitOrder en lugar de createOrder para manejar todos los datos relacionados
            const response = await submitOrder(orderData);

            // Revalidar datos para obtener el pedido real
            await mutateOrders();

            if (response.success && response.data) {
                return response.data;
            }

            return null;
        } catch (error) {
            // Si hubo un error, revalidar para restaurar estado original
            await mutateOrders();
            // REMOVED: console.error("Error al crear el pedido:", error);

            return null;
        }
    };

    // Procesar datos para devolverlos en formato esperado

    const processedOrders =
        ordersData?.success && ordersData.data?.orders
            ? ordersData.data.orders
            : [];
    const pagination =
        ordersData?.success && ordersData.data?.pagination
            ? ordersData.data.pagination
            : {
                  total: 0,
                  currentPage: queryOptions.page || 1,
                  lastPage: 0,
              };

    const statuses =
        statusesData?.success && statusesData.data?.statuses
            ? statusesData.data.statuses
            : [];

    // Función para precargar la siguiente página
    const prefetchNextPage = useCallback(() => {
        if (
            queryOptions.page &&
            pagination?.lastPage &&
            queryOptions.page < pagination.lastPage
        ) {
            const nextPageOptions = {
                ...queryOptions,
                page: queryOptions.page + 1,
            };

            // Precargar la siguiente página sin mostrar indicadores de carga
            getOrders({
                page: nextPageOptions.page,
                perPage: nextPageOptions.limit,
                statusId: nextPageOptions.statusId,
                search: nextPageOptions.search,
                orderBy: nextPageOptions.sortBy,
                order: nextPageOptions.sortDirection,
            })
                .then((data) => {
                    // Almacenar en caché SWR
                    mutateOrders(data, {
                        revalidate: false,
                        populateCache: true,
                    });
                })
                .catch((err) => {
                    // REMOVED: console.error("Error prefetching next page:", err);
                });
        }
    }, [queryOptions, pagination, mutateOrders]);

    // Precargar automáticamente la siguiente página SOLO en vista dashboard (no calendario)
    useEffect(() => {
        // Evitar prefetch automático en vista de calendario
        if (
            !isCalendarView &&
            processedOrders.length > 0 &&
            !isLoadingOrders &&
            !isValidatingOrders
        ) {
            prefetchNextPage();
        }
    }, [
        processedOrders,
        isLoadingOrders,
        isValidatingOrders,
        prefetchNextPage,
        isCalendarView,
    ]);

    return {
        // Data
        orders: processedOrders,
        total: pagination?.total || 0,
        page: pagination?.currentPage || queryOptions.page || 1,
        totalPages: pagination?.lastPage || 1,
        orderStatuses: statuses,

        // Loading states
        isLoading: isLoadingOrders || isLoadingStatuses,
        isValidating: isValidatingOrders,

        // Errors
        error: ordersError || statusesError,

        // Actions
        fetchOrder,
        addOrder,
        mutate: mutateOrders,
        prefetchNextPage,
        deleteOrder: async (id: string) => {
            try {
                const response = await remove(id);

                if (response.success) {
                    await mutateOrders();

                    return true;
                }

                return false;
            } catch (error) {
                // REMOVED: console.error("Error al eliminar el pedido:", error);

                return false;
            }
        },
        verifyAndCreateParts: async (id: string) => {
            try {
                // Implementar si es necesario
                await mutateOrders();

                return true;
            } catch (error) {
                // REMOVED: console.error("Error al verificar partes:", error);

                return false;
            }
        },
    };
}

/**
 * Hook para obtener todos los estados de órdenes
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de estados, estado de carga y errores
 */
export function useOrderStatuses(config?: SWRConfiguration) {
    const { data, error, isLoading } = useSWR(
        "orderStatuses",
        async () => getOrderStatuses(),
        {
            ...DEFAULT_SWR_CONFIG,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            ...config,
        },
    );

    const { isRevalidating } = useRevalidationListener("orders");

    return {
        orderStatuses: data?.data?.statuses || [],
        isLoading,
        isRevalidating,
        isError: !!error,
        error: data?.error || error,
    };
}

/**
 * Hook para crear una nueva orden
 * @returns Función para crear una orden y estado de la operación
 */
export function useCreateOrder() {
    const createOrderFn = async (data: OrderCreateData) => {
        const result = await submitOrder(data);

        if (result.success) {
            if (result.data && "id" in result.data) {
                await revalidateData(result.data.id);
            } else {
                await revalidateData();
            }
        }

        return result;
    };

    return { createOrder: createOrderFn };
}

/**
 * Hook para actualizar una orden existente
 * @returns Función para actualizar una orden y estado de la operación
 */
export function useUpdateOrder() {
    const updateOrder = async (id: string, data: OrderCreateData) => {
        // Utilizar updateOrderWithDetails en lugar de update para manejar correctamente
        // partes, prendas y notas de la orden
        const result = await updateOrderWithDetails(id, data);

        if (result.success) {
            await revalidateData(id);
        }

        return result;
    };

    return { updateOrder };
}

/**
 * Hook para eliminar una orden
 * @returns Función para eliminar una orden y estado de la operación
 */
export function useDeleteOrder() {
    const deleteOrder = async (id: string) => {
        const result = await remove(id);

        if (result.success) {
            await revalidateData();
        }

        return result;
    };

    return { deleteOrder };
}
