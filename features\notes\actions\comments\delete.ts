"use server";

import type { CommentActionResult } from "../../types/comment";

import { revalidatePath } from "next/cache";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/utils/prisma-helpers";

/**
 * Elimina un comentario (soft delete para mantener hilos)
 */
export async function deleteComment(
    commentId: string,
): Promise<CommentActionResult> {
    try {
        // Validar sesión
        const session = await auth();
        const userId = session?.user?.id;
        const userRole = session?.user?.role;

        if (!userId) {
            return { success: false, error: "No autorizado" };
        }

        // Obtener el comentario
        const comment = await db.noteComment.findUnique({
            where: { id: commentId },
            select: {
                id: true,
                authorId: true,
                content: true,
                noteId: true,
                note: {
                    select: {
                        orderId: true,
                    },
                },
                _count: {
                    select: {
                        replies: true,
                    },
                },
            },
        });

        if (!comment) {
            return { success: false, error: "El comentario no existe" };
        }

        // Verificar permisos
        const isAuthor = comment.authorId === userId;
        const isAdmin = (userRole as any)?.name === "ADMIN";

        if (!isAuthor && !isAdmin) {
            return {
                success: false,
                error: "No tienes permiso para eliminar este comentario",
            };
        }

        // Si tiene respuestas, hacer soft delete
        if (comment._count.replies > 0) {
            await db.noteComment.update({
                where: { id: commentId },
                data: {
                    content: "[Comentario eliminado]",
                    updatedAt: new Date(),
                },
            });
        } else {
            // Si no tiene respuestas, eliminar completamente
            await db.noteComment.delete({
                where: { id: commentId },
            });
        }

        // Revalidar las rutas afectadas
        revalidatePath(`/dashboard/orders/${comment.note.orderId}`);
        revalidatePath("/dashboard/notes");

        return {
            success: true,
            data: { deleted: true },
        };
    } catch (error) {
        return handlePrismaError(error, "Error al eliminar el comentario");
    }
}
