"use client";

import { useSession } from "next-auth/react";

export function useDebugSession() {
    const session = useSession();

    if (typeof window !== "undefined") {
        // Log session state
        console.log("🔍 Session Debug:", {
            status: session.status,
            data: session.data,
            update: typeof session.update,
        });

        // Check cookies
        console.log("🍪 Cookies:", document.cookie);

        // Check localStorage
        console.log(
            "💾 LocalStorage next-auth:",
            Object.keys(localStorage).filter((key) =>
                key.includes("next-auth"),
            ),
        );

        // Check sessionStorage
        console.log(
            "📦 SessionStorage:",
            Object.keys(sessionStorage).filter((key) =>
                key.includes("next-auth"),
            ),
        );
    }

    return session;
}

// Function to manually check session from server
export async function checkServerSession() {
    try {
        // Check both endpoints
        const [authResponse, debugResponse] = await Promise.all([
            fetch("/api/auth/session"),
            fetch("/api/debug/session"),
        ]);

        const authSession = await authResponse.json();
        const debugData = await debugResponse.json();

        console.log("🖥️ Auth Session:", authSession);
        console.log("🔍 Debug Data:", debugData);

        return { authSession, debugData };
    } catch (error) {
        console.error("❌ Error checking server session:", error);

        return null;
    }
}
