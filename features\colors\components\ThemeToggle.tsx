"use client";

import React, { useState } from "react";
import { Button, ButtonGroup } from "@heroui/react";
import { SunIcon, MoonIcon } from "@heroicons/react/24/outline";

interface ThemeToggleProps {
    onChange?: (theme: "light" | "dark") => void;
    defaultTheme?: "light" | "dark";
    className?: string;
    size?: "sm" | "md" | "lg";
}

export function ThemeToggle({
    onChange,
    defaultTheme = "light",
    className = "",
    size = "sm",
}: ThemeToggleProps) {
    const [theme, setTheme] = useState<"light" | "dark">(defaultTheme);

    const handleChange = (newTheme: "light" | "dark") => {
        setTheme(newTheme);
        if (onChange) {
            onChange(newTheme);
        }
    };

    return (
        <ButtonGroup
            aria-label="Selector de tema para previsualización"
            className={`${className}`}
            radius="full"
            size={size}
        >
            <Button
                className={
                    theme === "light"
                        ? "bg-white text-blue-600"
                        : "bg-gray-100 text-gray-500"
                }
                startContent={<SunIcon className="w-4 h-4" />}
                variant="flat"
                onPress={() => handleChange("light")}
            >
                {size !== "sm" && "Claro"}
            </Button>
            <Button
                className={
                    theme === "dark"
                        ? "bg-gray-800 text-blue-400"
                        : "bg-gray-600 text-gray-300"
                }
                startContent={<MoonIcon className="w-4 h-4" />}
                variant="flat"
                onPress={() => handleChange("dark")}
            >
                {size !== "sm" && "Oscuro"}
            </Button>
        </ButtonGroup>
    );
}
