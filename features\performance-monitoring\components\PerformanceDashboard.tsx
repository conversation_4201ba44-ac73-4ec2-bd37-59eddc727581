"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardHeader, CardBody } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { Divider } from "@heroui/divider";

import { getBaselineMetrics, initWebVitals } from "../utils/web-vitals";
import { performanceTracker } from "../utils/api-metrics";

interface MetricStatus {
    value: number;
    status: "good" | "warning" | "poor";
    target: number;
}

export function PerformanceDashboard() {
    const [webVitals, setWebVitals] = useState(getBaselineMetrics());
    const [apiStats, setApiStats] = useState(performanceTracker.getAPIStats());
    const [queryStats, setQueryStats] = useState(
        performanceTracker.getQueryStats(),
    );

    useEffect(() => {
        // Initialize web vitals tracking
        initWebVitals();

        // Update stats every 5 seconds
        const interval = setInterval(() => {
            setWebVitals(getBaselineMetrics());
            setApiStats(performanceTracker.getAPIStats());
            setQueryStats(performanceTracker.getQueryStats());
        }, 5000);

        return () => clearInterval(interval);
    }, []);

    const evaluateMetric = (name: string, value: number): MetricStatus => {
        const targets = {
            LCP: { good: 2500, poor: 4000 },
            INP: { good: 200, poor: 500 },
            CLS: { good: 0.1, poor: 0.25 },
            TTFB: { good: 800, poor: 1800 },
            API: { good: 200, poor: 500 },
        };

        const target = targets[name as keyof typeof targets];

        if (!target) return { value, status: "good", target: 0 };

        const status =
            value <= target.good
                ? "good"
                : value <= target.poor
                  ? "warning"
                  : "poor";

        return { value, status, target: target.good };
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case "good":
                return "success";
            case "warning":
                return "warning";
            case "poor":
                return "danger";
            default:
                return "default";
        }
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Web Vitals Card */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold">Core Web Vitals</h3>
                </CardHeader>
                <Divider />
                <CardBody className="space-y-3">
                    {webVitals.LCP && (
                        <div className="flex justify-between items-center">
                            <span className="text-sm">
                                LCP (Largest Contentful Paint)
                            </span>
                            <Chip
                                color={getStatusColor(
                                    evaluateMetric("LCP", webVitals.LCP).status,
                                )}
                                size="sm"
                            >
                                {webVitals.LCP}ms
                            </Chip>
                        </div>
                    )}
                    {webVitals.INP && (
                        <div className="flex justify-between items-center">
                            <span className="text-sm">
                                INP (Interaction to Next Paint)
                            </span>
                            <Chip
                                color={getStatusColor(
                                    evaluateMetric("INP", webVitals.INP).status,
                                )}
                                size="sm"
                            >
                                {webVitals.INP}ms
                            </Chip>
                        </div>
                    )}
                    {webVitals.CLS && (
                        <div className="flex justify-between items-center">
                            <span className="text-sm">
                                CLS (Cumulative Layout Shift)
                            </span>
                            <Chip
                                color={getStatusColor(
                                    evaluateMetric("CLS", webVitals.CLS).status,
                                )}
                                size="sm"
                            >
                                {webVitals.CLS}
                            </Chip>
                        </div>
                    )}
                </CardBody>
            </Card>

            {/* API Performance Card */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold">API Performance</h3>
                </CardHeader>
                <Divider />
                <CardBody className="space-y-2">
                    {apiStats ? (
                        <>
                            <div className="flex justify-between">
                                <span className="text-sm">Average</span>
                                <span className="font-mono text-sm">
                                    {apiStats.average}ms
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm">P95</span>
                                <span className="font-mono text-sm">
                                    {apiStats.p95}ms
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm">Total Calls</span>
                                <span className="font-mono text-sm">
                                    {apiStats.count}
                                </span>
                            </div>
                        </>
                    ) : (
                        <p className="text-sm text-gray-500">No data yet</p>
                    )}
                </CardBody>
            </Card>

            {/* Database Performance Card */}
            <Card>
                <CardHeader>
                    <h3 className="text-lg font-semibold">
                        Database Performance
                    </h3>
                </CardHeader>
                <Divider />
                <CardBody className="space-y-2">
                    {queryStats ? (
                        <>
                            <div className="flex justify-between">
                                <span className="text-sm">Average</span>
                                <span className="font-mono text-sm">
                                    {queryStats.average}ms
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm">P95</span>
                                <span className="font-mono text-sm">
                                    {queryStats.p95}ms
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm">Total Queries</span>
                                <span className="font-mono text-sm">
                                    {queryStats.count}
                                </span>
                            </div>
                        </>
                    ) : (
                        <p className="text-sm text-gray-500">No data yet</p>
                    )}
                </CardBody>
            </Card>
        </div>
    );
}
