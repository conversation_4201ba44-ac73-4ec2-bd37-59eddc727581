"use client";

import {
    createContext,
    useContext,
    useState,
    useEffect,
    useMemo,
    ReactNode,
} from "react";

type NavigationContextType = {
    isSidebarOpen: boolean;
    isMobileMenuOpen: boolean;
    toggleSidebar: () => void;
    toggleMobileMenu: () => void;
    closeMobileMenu: () => void;
    isDesktop: boolean;
    isTablet: boolean;
    isMobile: boolean;
};

const NavigationContext = createContext<NavigationContextType | undefined>(
    undefined,
);

// Key for localStorage
const SIDEBAR_STATE_KEY = "lohari-sidebar-state";

export function NavigationProvider({ children }: { children: ReactNode }) {
    // Initialize with localStorage value or default to true
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [windowWidth, setWindowWidth] = useState<number>(0);

    // Screen breakpoints
    const isDesktop = windowWidth >= 1024; // lg
    const isTablet = windowWidth >= 768 && windowWidth < 1024; // md to lg
    const isMobile = windowWidth < 768; // < md

    // Load saved sidebar state on initial mount
    useEffect(() => {
        // Initialize window width
        setWindowWidth(window.innerWidth);

        // Try to load sidebar state from localStorage
        try {
            const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);

            if (savedState !== null) {
                setIsSidebarOpen(JSON.parse(savedState));
            } else {
                // Default state based on screen size if no saved state
                setIsSidebarOpen(window.innerWidth >= 1024);
            }
        } catch (error) {
            // In case of any error with localStorage, set default
            setIsSidebarOpen(window.innerWidth >= 1024);
        }

        const handleResize = () => {
            setWindowWidth(window.innerWidth);

            // Auto-close mobile menu on resize
            if (window.innerWidth >= 768) {
                setIsMobileMenuOpen(false);
            }
        };

        window.addEventListener("resize", handleResize);

        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Save sidebar state to localStorage whenever it changes
    useEffect(() => {
        try {
            localStorage.setItem(
                SIDEBAR_STATE_KEY,
                JSON.stringify(isSidebarOpen),
            );
        } catch (error) {
            // Ignore localStorage errors
        }
    }, [isSidebarOpen]);

    const toggleSidebar = () => {
        setIsSidebarOpen((prev) => !prev);
    };

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen((prev) => !prev);
    };

    const closeMobileMenu = () => {
        setIsMobileMenuOpen(false);
    };

    // Memoize context value to prevent unnecessary re-renders
    const contextValue = useMemo(
        () => ({
            isSidebarOpen,
            isMobileMenuOpen,
            toggleSidebar,
            toggleMobileMenu,
            closeMobileMenu,
            isDesktop,
            isTablet,
            isMobile,
        }),
        [isSidebarOpen, isMobileMenuOpen, isDesktop, isTablet, isMobile],
    );

    return (
        <NavigationContext.Provider value={contextValue}>
            {children}
        </NavigationContext.Provider>
    );
}

export function useNavigation() {
    const context = useContext(NavigationContext);

    if (context === undefined) {
        throw new Error(
            "useNavigation must be used within a NavigationProvider",
        );
    }

    return context;
}
