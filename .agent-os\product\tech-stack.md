# Tech Stack

## Frontend
- **Framework**: Next.js 14.2.3 (App Router)
- **Language**: TypeScript 5.6.3 (strict mode)
- **UI Library**: HeroUI v2.7.5
- **Styling**: TailwindCSS 3.4.16
- **State Management**: Server Actions + SWR 2.3.3
- **Forms**: React Hook Form 7.57.0 + Zod 3.25.48
- **Animations**: Framer Motion 11.18.2

## Backend
- **Runtime**: Node.js 18+
- **API Pattern**: Server Actions (no traditional APIs)
- **ORM**: Prisma 6.12.0
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: NextAuth v4.24.7

## Infrastructure
- **Hosting**: Vercel (planned)
- **Database Host**: Supabase
- **File Storage**: Local (future: cloud storage)
- **Email**: Nodemailer (future: transactional service)

## Development Tools
- **Package Manager**: npm (pnpm recommended)
- **Linting**: ESLint 8.57.0
- **Formatting**: Prettier 3.3.3
- **Git Hooks**: <PERSON><PERSON> (planned)
- **Testing**: Jest (planned)

## PDF Generation
- **Primary**: @react-pdf/renderer 4.3.0
- **Alternative**: jspdf 3.0.1

## Key Libraries
- **Date Handling**: date-fns 4.1.0
- **Icons**: Lucide React 0.475.0
- **Tables**: @tanstack/react-table 8.21.2
- **Charts**: Recharts 2.15.2
- **IDs**: nanoid 5.1.5

## AI Development
- **Agent OS**: For AI agent guidance
- **Compatible with**: Claude Code, Cursor, any AI coding assistant