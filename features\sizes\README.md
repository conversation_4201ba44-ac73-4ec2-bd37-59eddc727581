# Módulo Sizes

Este módulo gestiona las tallas (sizes) de las prendas en el sistema Lohari.

## Estructura

```
sizes/
├── actions/        # Server actions para operaciones CRUD
├── components/     # Componentes React (pendiente de migrar)
├── hooks/          # Custom hooks para manejo de estado
├── schemas/        # Esquemas de validación con Zod
├── types/          # Definiciones de TypeScript
└── utils/          # Utilidades auxiliares
```

## API Pública

### Hooks
- `useSizes()` - Obtener lista de tallas con filtros
- `useSize(id)` - Obtener una talla específica
- `useCreateSize()` - Crear nueva talla
- `useUpdateSize()` - Actualizar talla existente
- `useDeleteSize()` - Eliminar talla
- `useValidateSizeCode()` - Validar código único

### Types
- `Size` - Interface principal de talla
- `SizesResponse` - Respuesta con paginación
- `CreateSizeInput` - Datos para crear talla
- `UpdateSizeInput` - Datos para actualizar talla

## Uso

```typescript
import { useSizes, useCreateSize } from '@/features/sizes';

// En un componente
const { sizes, isLoading } = useSizes();
const { createSize } = useCreateSize();
```

## Notas de Migración

Este módulo fue separado del módulo `garments` para mejor organización.
Las rutas en `/app/dashboard/sizes/` ahora importan desde este módulo.