import { differenceInDays } from "date-fns";

import { Order } from "../types/orders";

export interface RiskFactors {
    deliveryRisk: number; // 0-40 puntos
    capacityRisk: number; // 0-30 puntos
    customerRisk: number; // 0-20 puntos
    complexityRisk: number; // 0-10 puntos
}

export interface RiskAssessment {
    score: number;
    level: "low" | "medium" | "high" | "critical";
    factors: RiskFactors;
    recommendations: string[];
}

// Extender Order para campos necesarios
interface ExtendedOrder extends Order {
    deliveryDate?: string | Date;
    quantity?: number;
    status: string;
    customer?: {
        id: string;
        name: string;
    };
}

export function calculateDeliveryRisk(order: ExtendedOrder): number {
    if (
        !order.deliveryDate ||
        order.status === "delivered" ||
        order.status === "cancelled"
    ) {
        return 0;
    }

    const daysLeft = differenceInDays(new Date(order.deliveryDate), new Date());

    // Ya está retrasada
    if (daysLeft < 0) return 40;

    // Vence hoy
    if (daysLeft === 0) return 35;

    // Próximos 3 días
    if (daysLeft <= 3) return 25;

    // Próxima semana
    if (daysLeft <= 7) return 15;

    // Próximas 2 semanas
    if (daysLeft <= 14) return 5;

    // Más de 2 semanas
    return 0;
}

export function calculateCapacityRisk(order: ExtendedOrder): number {
    // Por ahora, simulamos basándonos en el estado
    if (order.status === "pending") return 20;
    if (order.status === "in_progress") return 10;
    if (order.status === "in_production") return 5;

    return 0;
}

export function calculateCustomerRisk(order: ExtendedOrder): number {
    // Simulación: algunos clientes son más riesgosos
    // En producción, esto se basaría en historial real
    if (!order.customer) return 10;

    // Simulamos que algunos clientes tienen mayor riesgo
    const customerName = order.customer.name.toLowerCase();

    if (customerName.includes("urgente") || customerName.includes("express")) {
        return 20;
    }

    return 5;
}

export function calculateComplexityRisk(order: ExtendedOrder): number {
    // Basado en la cantidad de piezas
    const quantity = order.quantity || 0;

    if (quantity > 1000) return 10;
    if (quantity > 500) return 7;
    if (quantity > 100) return 3;

    return 0;
}

export function generateRecommendations(
    factors: RiskFactors,
    order: ExtendedOrder,
): string[] {
    const recommendations: string[] = [];

    if (factors.deliveryRisk >= 30) {
        recommendations.push("Priorizar esta orden inmediatamente");
        recommendations.push("Considerar asignar recursos adicionales");
    } else if (factors.deliveryRisk >= 20) {
        recommendations.push("Monitorear el progreso diariamente");
    }

    if (factors.capacityRisk >= 20) {
        recommendations.push("Verificar disponibilidad del contratista");
        recommendations.push(
            "Considerar dividir la orden entre múltiples contratistas",
        );
    }

    if (factors.customerRisk >= 15) {
        recommendations.push("Mantener comunicación frecuente con el cliente");
        recommendations.push("Documentar todos los avances");
    }

    if (factors.complexityRisk >= 7) {
        recommendations.push("Asignar a contratista con experiencia");
        recommendations.push("Realizar controles de calidad adicionales");
    }

    if (recommendations.length === 0) {
        recommendations.push("Mantener el seguimiento regular");
    }

    return recommendations;
}

export function calculateRiskScore(order: Order): RiskAssessment {
    const extOrder = order as ExtendedOrder;

    const factors: RiskFactors = {
        deliveryRisk: calculateDeliveryRisk(extOrder),
        capacityRisk: calculateCapacityRisk(extOrder),
        customerRisk: calculateCustomerRisk(extOrder),
        complexityRisk: calculateComplexityRisk(extOrder),
    };

    const totalScore = Object.values(factors).reduce((a, b) => a + b, 0);

    let level: RiskAssessment["level"];

    if (totalScore >= 70) level = "critical";
    else if (totalScore >= 50) level = "high";
    else if (totalScore >= 30) level = "medium";
    else level = "low";

    const recommendations = generateRecommendations(factors, extOrder);

    return {
        score: totalScore,
        level,
        factors,
        recommendations,
    };
}
