/**
 * Size Mapper
 *
 * Maps between domain entities and persistence models.
 * Ensures clean separation between domain and infrastructure layers.
 */

import { Size as PrismaSize } from "@prisma/client";
// import { injectable } from "tsyringe"; // Commented out - tsyringe not installed

import { Size } from "../../domain/entities/size.entity";

export interface SizePersistenceModel {
    id?: string;
    name: string;
    displayName: string;
    order: number;
    isActive: boolean;
    category: string;
    measurements: any;
    equivalents: any;
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date | null;
}

// @injectable() // Commented out - tsyringe not installed
export class SizeMapper {
    /**
     * Map from Prisma model to domain entity
     */
    toDomain(prismaModel: PrismaSize): Size {
        const model = prismaModel as any;

        return new Size({
            id: model.id,
            name: model.name || model.code, // Fallback to code if name doesn't exist
            displayName: model.displayName || model.code,
            order: model.order || 0,
            isActive: model.isActive ?? true,
            category: this.mapCategory(model.category),
            measurements: this.parseMeasurements(model.measurements),
            equivalents: this.parseEquivalents(model.equivalents),
            createdAt: model.createdAt,
            updatedAt: model.updatedAt,
            deletedAt: model.deletedAt,
        });
    }

    /**
     * Map from domain entity to persistence model
     */
    toPersistence(domainEntity: Size): SizePersistenceModel {
        return {
            id: domainEntity.id,
            name: domainEntity.name,
            displayName: domainEntity.displayName,
            order: domainEntity.order,
            isActive: domainEntity.isActive,
            category: domainEntity.category,
            measurements: this.serializeMeasurements(domainEntity.measurements),
            equivalents: this.serializeEquivalents(domainEntity.equivalents),
            createdAt: domainEntity.createdAt,
            updatedAt: domainEntity.updatedAt,
            deletedAt: domainEntity.deletedAt,
        };
    }

    /**
     * Map array of Prisma models to domain entities
     */
    toDomainArray(prismaModels: PrismaSize[]): Size[] {
        return prismaModels.map((model) => this.toDomain(model));
    }

    /**
     * Map array of domain entities to persistence models
     */
    toPersistenceArray(domainEntities: Size[]): SizePersistenceModel[] {
        return domainEntities.map((entity) => this.toPersistence(entity));
    }

    /**
     * Map category from string to enum
     */
    private mapCategory(category: string): "numeric" | "alpha" | "custom" {
        switch (category) {
            case "numeric":
                return "numeric";
            case "alpha":
                return "alpha";
            case "custom":
                return "custom";
            default:
                // Default to custom if unknown
                return "custom";
        }
    }

    /**
     * Parse measurements from JSON
     */
    private parseMeasurements(measurements: any): Size["measurements"] {
        if (!measurements) {
            return undefined;
        }

        // Handle if it's already an object
        if (typeof measurements === "object" && !Array.isArray(measurements)) {
            return {
                chest: measurements.chest,
                waist: measurements.waist,
                hip: measurements.hip,
                length: measurements.length,
            };
        }

        // Handle if it's a JSON string
        if (typeof measurements === "string") {
            try {
                const parsed = JSON.parse(measurements);

                return {
                    chest: parsed.chest,
                    waist: parsed.waist,
                    hip: parsed.hip,
                    length: parsed.length,
                };
            } catch {
                return undefined;
            }
        }

        return undefined;
    }

    /**
     * Parse equivalents from JSON
     */
    private parseEquivalents(equivalents: any): Size["equivalents"] {
        if (!equivalents) {
            return undefined;
        }

        // Handle if it's already an object
        if (typeof equivalents === "object" && !Array.isArray(equivalents)) {
            return {
                us: equivalents.us,
                eu: equivalents.eu,
                uk: equivalents.uk,
                mx: equivalents.mx,
            };
        }

        // Handle if it's a JSON string
        if (typeof equivalents === "string") {
            try {
                const parsed = JSON.parse(equivalents);

                return {
                    us: parsed.us,
                    eu: parsed.eu,
                    uk: parsed.uk,
                    mx: parsed.mx,
                };
            } catch {
                return undefined;
            }
        }

        return undefined;
    }

    /**
     * Serialize measurements for persistence
     */
    private serializeMeasurements(measurements?: Size["measurements"]): any {
        if (!measurements) {
            return null;
        }

        // Remove undefined values
        const cleaned: any = {};

        if (measurements.chest !== undefined)
            cleaned.chest = measurements.chest;
        if (measurements.waist !== undefined)
            cleaned.waist = measurements.waist;
        if (measurements.hip !== undefined) cleaned.hip = measurements.hip;
        if (measurements.length !== undefined)
            cleaned.length = measurements.length;

        return Object.keys(cleaned).length > 0 ? cleaned : null;
    }

    /**
     * Serialize equivalents for persistence
     */
    private serializeEquivalents(equivalents?: Size["equivalents"]): any {
        if (!equivalents) {
            return null;
        }

        // Remove undefined values
        const cleaned: any = {};

        if (equivalents.us !== undefined) cleaned.us = equivalents.us;
        if (equivalents.eu !== undefined) cleaned.eu = equivalents.eu;
        if (equivalents.uk !== undefined) cleaned.uk = equivalents.uk;
        if (equivalents.mx !== undefined) cleaned.mx = equivalents.mx;

        return Object.keys(cleaned).length > 0 ? cleaned : null;
    }

    /**
     * Create a partial update object for Prisma
     */
    toPartialPersistence(
        domainEntity: Partial<Size>,
    ): Partial<SizePersistenceModel> {
        const partial: Partial<SizePersistenceModel> = {};

        if (domainEntity.name !== undefined) {
            partial.name = domainEntity.name;
        }

        if (domainEntity.displayName !== undefined) {
            partial.displayName = domainEntity.displayName;
        }

        if (domainEntity.order !== undefined) {
            partial.order = domainEntity.order;
        }

        if (domainEntity.isActive !== undefined) {
            partial.isActive = domainEntity.isActive;
        }

        if (domainEntity.category !== undefined) {
            partial.category = domainEntity.category;
        }

        if (domainEntity.measurements !== undefined) {
            partial.measurements = this.serializeMeasurements(
                domainEntity.measurements,
            );
        }

        if (domainEntity.equivalents !== undefined) {
            partial.equivalents = this.serializeEquivalents(
                domainEntity.equivalents,
            );
        }

        if (domainEntity.deletedAt !== undefined) {
            partial.deletedAt = domainEntity.deletedAt;
        }

        // Always update the updatedAt timestamp
        partial.updatedAt = new Date();

        return partial;
    }
}
