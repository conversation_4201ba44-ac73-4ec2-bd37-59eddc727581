"use client";

import { useState, useEffect, useTransition, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    Button,
    Textarea,
    Select,
    SelectItem,
    Chip,
    Tooltip,
    Progress,
} from "@heroui/react";
import {
    PencilIcon,
    PaperAirplaneIcon,
    XMarkIcon,
    EyeIcon,
    EyeSlashIcon,
    InformationCircleIcon,
    DocumentIcon,
} from "@heroicons/react/24/outline";
import clsx from "clsx";

import { springConfig } from "@/shared/utils/ui/animations";

import { useAutosize } from "../hooks/useAutosize";

import { NoteContent } from "./NoteContent";

interface InlineNoteEditorProps {
    noteId?: string;
    initialContent?: string;
    initialImportanceId?: string;
    initialStatusId?: string;
    orderId: string;
    importanceOptions: { id: string; name: string; color?: string | null }[];
    statusOptions: { id: string; name: string; color?: string | null }[];
    onSave: (data: {
        noteId?: string;
        content: string;
        importanceId: string;
        statusId: string;
    }) => Promise<any>;
    onCancel?: () => void;
    mode?: "create" | "edit";
    autoFocus?: boolean;
    className?: string;
}

const MAX_CHARACTERS = 1000;

// Draft management utilities
const getDraftKey = (orderId: string) => `note-draft-${orderId}`;

const saveDraft = (orderId: string, content: string) => {
    if (content.trim()) {
        localStorage.setItem(getDraftKey(orderId), content);
    }
};

const getDraft = (orderId: string) => {
    return localStorage.getItem(getDraftKey(orderId)) || "";
};

const clearDraft = (orderId: string) => {
    localStorage.removeItem(getDraftKey(orderId));
};

export function InlineNoteEditor({
    noteId,
    initialContent = "",
    initialImportanceId = "",
    initialStatusId = "",
    orderId,
    importanceOptions,
    statusOptions,
    onSave,
    onCancel,
    mode = "create",
    autoFocus = false,
    className = "",
}: InlineNoteEditorProps) {
    // Estados
    const [content, setContent] = useState(() =>
        mode === "create"
            ? getDraft(orderId) || initialContent
            : initialContent,
    );
    const [importanceId, setImportanceId] = useState(initialImportanceId);
    const [statusId, setStatusId] = useState(initialStatusId);
    const [error, setError] = useState<string | null>(null);
    const [showPreview, setShowPreview] = useState(false);
    const [isPending, startTransition] = useTransition();
    const [isFormFocused, setIsFormFocused] = useState(false);

    // Hook para autosize
    const textareaRef = useAutosize(content, {
        minHeight: 100,
        maxHeight: 300,
    });

    // Calcular caracteres
    const characterCount = content.length;
    const characterPercentage = (characterCount / MAX_CHARACTERS) * 100;
    const isOverLimit = characterCount > MAX_CHARACTERS;

    // Save draft on content change
    useEffect(() => {
        if (mode === "create" && content !== initialContent) {
            const timeoutId = setTimeout(() => {
                saveDraft(orderId, content);
            }, 500);

            return () => clearTimeout(timeoutId);
        }
    }, [content, mode, orderId, initialContent]);

    // Inicializar valores al cambiar props
    useEffect(() => {
        if (mode !== "create" || !getDraft(orderId)) {
            setContent(initialContent);
        }
        setImportanceId(initialImportanceId);

        if (initialStatusId) {
            setStatusId(initialStatusId);
        } else if (
            mode === "edit" &&
            statusOptions &&
            statusOptions.length > 0
        ) {
            setStatusId(statusOptions[0]?.id || "");
        } else {
            setStatusId("");
        }
    }, [
        initialContent,
        initialImportanceId,
        initialStatusId,
        mode,
        statusOptions,
        orderId,
    ]);

    // Manejador de submit
    const handleSubmit = useCallback(() => {
        // Validación básica
        if (!content.trim()) {
            setError("El contenido es obligatorio");

            return;
        }

        if (isOverLimit) {
            setError(
                `El contenido excede el límite de ${MAX_CHARACTERS} caracteres`,
            );

            return;
        }

        if (!importanceId) {
            setError("La importancia es obligatoria");

            return;
        }

        if (!statusId) {
            setError("El estado es obligatorio");

            return;
        }

        // Limpiar error
        setError(null);

        // Ejecutar acción
        const executeSubmit = async () => {
            try {
                const result = await onSave({
                    noteId,
                    content: content.trim(),
                    importanceId,
                    statusId,
                });

                // Si es modo crear y fue exitoso, limpiar el formulario
                if (mode === "create" && result?.success) {
                    setContent("");
                    setShowPreview(false);
                    // Clear draft after successful save
                    clearDraft(orderId);
                }

                return result;
            } catch (error) {
                setError("Error al guardar la nota");

                return { success: false, error: "Error al guardar la nota" };
            }
        };

        // Usar startTransition solo para mostrar el loading state
        startTransition(() => {
            executeSubmit();
        });
    }, [
        content,
        isOverLimit,
        importanceId,
        statusId,
        noteId,
        mode,
        onSave,
        orderId,
    ]);

    // Manejador de teclas
    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
            // Ctrl+Enter o Cmd+Enter para enviar
            if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
                e.preventDefault();
                handleSubmit();
            }

            // Escape para cancelar en modo edit
            if (e.key === "Escape" && mode === "edit" && onCancel) {
                e.preventDefault();
                onCancel();
            }
        },
        [mode, onCancel, handleSubmit],
    );

    // Cancelar edición
    const handleCancel = () => {
        setContent(initialContent);
        setImportanceId(initialImportanceId);
        setStatusId(initialStatusId);
        setError(null);
        setShowPreview(false);

        if (onCancel) {
            onCancel();
        }
    };

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={clsx(
                "bg-gray-50 dark:bg-gray-900/50",
                "rounded-xl shadow-sm border-2",
                isFormFocused
                    ? "border-primary-400 shadow-md shadow-primary-100/20 dark:shadow-primary-900/20"
                    : "border-gray-200 dark:border-gray-700",
                "transition-all duration-300",
                "p-5 space-y-4",
                className,
            )}
            exit={{ opacity: 0, y: -10 }}
            initial={{ opacity: 0, y: 10 }}
            transition={springConfig.gentle}
            onBlur={() => setIsFormFocused(false)}
            onFocus={() => setIsFormFocused(true)}
        >
            {/* Header con preview toggle y contador */}
            <div className="flex justify-between items-center">
                <motion.h4
                    className="text-sm font-medium text-gray-800 dark:text-gray-200"
                    layoutId={`editor-title-${noteId || "new"}`}
                >
                    {mode === "create" ? "Nueva nota" : "Editar nota"}
                </motion.h4>

                <div className="flex items-center gap-2">
                    {/* Contador de caracteres con animación */}
                    <motion.div
                        animate={{ scale: isOverLimit ? [1, 1.1, 1] : 1 }}
                        className="flex items-center gap-2"
                        transition={{ duration: 0.3 }}
                    >
                        <Progress
                            aria-label="Caracteres usados"
                            className="w-20"
                            color={
                                isOverLimit
                                    ? "danger"
                                    : characterPercentage > 80
                                      ? "warning"
                                      : "primary"
                            }
                            size="sm"
                            value={characterPercentage}
                        />
                        <span
                            className={clsx(
                                "text-xs font-medium transition-colors",
                                isOverLimit
                                    ? "text-danger animate-pulse"
                                    : "text-gray-500",
                            )}
                        >
                            {characterCount}/{MAX_CHARACTERS}
                        </span>
                    </motion.div>

                    {/* Preview toggle con animación */}
                    <Tooltip
                        content={
                            showPreview
                                ? "Ocultar vista previa"
                                : "Mostrar vista previa"
                        }
                    >
                        <Button
                            isIconOnly
                            aria-label={
                                showPreview
                                    ? "Ocultar vista previa"
                                    : "Mostrar vista previa"
                            }
                            className="hover:bg-primary-100/50 dark:hover:bg-primary-900/50 transition-colors"
                            size="sm"
                            variant="light"
                            onClick={() => setShowPreview(!showPreview)}
                        >
                            <motion.div
                                animate={{ rotate: showPreview ? 180 : 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                {showPreview ? (
                                    <EyeSlashIcon className="w-4 h-4" />
                                ) : (
                                    <EyeIcon className="w-4 h-4" />
                                )}
                            </motion.div>
                        </Button>
                    </Tooltip>
                </div>
            </div>

            {/* Contenido principal */}
            <AnimatePresence mode="wait">
                <motion.div
                    layout
                    className={showPreview ? "grid grid-cols-2 gap-4" : ""}
                >
                    {/* Editor */}
                    <motion.div
                        layout
                        className={showPreview ? "" : "space-y-4"}
                    >
                        <div className="relative">
                            <Textarea
                                ref={textareaRef}
                                autoFocus={autoFocus}
                                classNames={{
                                    base: "w-full",
                                    input: clsx(
                                        "resize-none transition-all duration-200",
                                        "placeholder:text-gray-400 dark:placeholder:text-gray-500",
                                    ),
                                    inputWrapper: clsx(
                                        "bg-white dark:bg-gray-900",
                                        "border-2",
                                        isOverLimit
                                            ? "border-danger shadow-danger-100/20 dark:shadow-danger-900/20"
                                            : "hover:border-primary-300 dark:hover:border-primary-700",
                                        "transition-all duration-200",
                                    ),
                                }}
                                errorMessage={
                                    error && !content.trim()
                                        ? "El contenido es obligatorio"
                                        : ""
                                }
                                isInvalid={!!error && !content.trim()}
                                placeholder="Escribe tu nota aquí... Usa @usuario para mencionar y #tag para etiquetar"
                                value={content}
                                onChange={(e) => setContent(e.target.value)}
                                onKeyDown={handleKeyDown as any}
                            />

                            {/* Tooltip de atajos con glassmorphism */}
                            <Tooltip
                                content={
                                    <div className="text-xs space-y-1 p-1">
                                        <div className="flex items-center gap-1">
                                            <kbd className="px-1.5 py-0.5 rounded bg-gray-200 dark:bg-gray-700 text-xs">
                                                Ctrl
                                            </kbd>
                                            <span>+</span>
                                            <kbd className="px-1.5 py-0.5 rounded bg-gray-200 dark:bg-gray-700 text-xs">
                                                Enter
                                            </kbd>
                                            <span>para enviar</span>
                                        </div>
                                        {mode === "edit" && (
                                            <div className="flex items-center gap-1">
                                                <kbd className="px-1.5 py-0.5 rounded bg-gray-200 dark:bg-gray-700 text-xs">
                                                    Esc
                                                </kbd>
                                                <span>para cancelar</span>
                                            </div>
                                        )}
                                        <div>
                                            Usa{" "}
                                            <code className="bg-primary-100 dark:bg-primary-900/50 px-1 rounded">
                                                @usuario
                                            </code>{" "}
                                            para mencionar
                                        </div>
                                        <div>
                                            Usa{" "}
                                            <code className="bg-primary-100 dark:bg-primary-900/50 px-1 rounded">
                                                #tag
                                            </code>{" "}
                                            para etiquetar
                                        </div>
                                    </div>
                                }
                                placement="bottom-end"
                            >
                                <Button
                                    isIconOnly
                                    className="absolute top-2 right-2 opacity-60 hover:opacity-100 transition-opacity"
                                    size="sm"
                                    variant="light"
                                >
                                    <InformationCircleIcon className="w-4 h-4" />
                                </Button>
                            </Tooltip>
                        </div>

                        {!showPreview && (
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                className="grid grid-cols-1 gap-4 sm:grid-cols-2"
                                initial={{ opacity: 0, y: 10 }}
                                transition={{ delay: 0.1 }}
                            >
                                {/* Selector de importancia con glassmorphism */}
                                <Select
                                    classNames={{
                                        base: "w-full",
                                        trigger: clsx(
                                            "h-10",
                                            "bg-white dark:bg-gray-900",
                                            "border-2",
                                            "hover:border-primary-300 dark:hover:border-primary-700",
                                            "transition-all duration-200",
                                        ),
                                    }}
                                    errorMessage={
                                        !importanceId
                                            ? "La importancia es obligatoria"
                                            : ""
                                    }
                                    isInvalid={!!error && !importanceId}
                                    label="Importancia"
                                    placeholder="Selecciona importancia"
                                    selectedKeys={
                                        importanceId ? [importanceId] : []
                                    }
                                    size="sm"
                                    onChange={(e) =>
                                        setImportanceId(e.target.value)
                                    }
                                >
                                    {importanceOptions.map((option) => (
                                        <SelectItem
                                            key={option.id}
                                            textValue={option.name}
                                        >
                                            <div className="flex items-center gap-2">
                                                {option.color && (
                                                    <motion.div
                                                        className="w-3 h-3 rounded-full shadow-sm"
                                                        style={{
                                                            backgroundColor:
                                                                option.color,
                                                            boxShadow: `0 0 8px ${option.color}40`,
                                                        }}
                                                        whileHover={{
                                                            scale: 1.2,
                                                        }}
                                                    />
                                                )}
                                                <span>{option.name}</span>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </Select>

                                {/* Selector de estado con glassmorphism */}
                                <Select
                                    classNames={{
                                        base: "w-full",
                                        trigger: clsx(
                                            "h-10",
                                            "bg-white dark:bg-gray-900",
                                            "border-2",
                                            "hover:border-primary-300 dark:hover:border-primary-700",
                                            "transition-all duration-200",
                                        ),
                                    }}
                                    errorMessage={
                                        !statusId
                                            ? "El estado es obligatorio"
                                            : ""
                                    }
                                    isInvalid={!!error && !statusId}
                                    label="Estado"
                                    placeholder="Selecciona estado"
                                    selectedKeys={statusId ? [statusId] : []}
                                    size="sm"
                                    onChange={(e) =>
                                        setStatusId(e.target.value)
                                    }
                                >
                                    {statusOptions.map((option) => (
                                        <SelectItem
                                            key={option.id}
                                            textValue={option.name}
                                        >
                                            <div className="flex items-center gap-2">
                                                {option.color && (
                                                    <motion.div
                                                        className="w-3 h-3 rounded-full shadow-sm"
                                                        style={{
                                                            backgroundColor:
                                                                option.color,
                                                            boxShadow: `0 0 8px ${option.color}40`,
                                                        }}
                                                        whileHover={{
                                                            scale: 1.2,
                                                        }}
                                                    />
                                                )}
                                                <span>{option.name}</span>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </Select>
                            </motion.div>
                        )}
                    </motion.div>

                    {/* Vista previa */}
                    <AnimatePresence>
                        {showPreview && content.trim() && (
                            <motion.div
                                animate={{ opacity: 1, x: 0 }}
                                className="space-y-2"
                                exit={{ opacity: 0, x: 20 }}
                                initial={{ opacity: 0, x: 20 }}
                                transition={springConfig.gentle}
                            >
                                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Vista previa
                                </h5>
                                <div
                                    className={clsx(
                                        "bg-gray-100/50 dark:bg-gray-800/50",
                                        "p-4 rounded-lg border border-gray-200 dark:border-gray-700 min-h-[100px]",
                                        "shadow-inner",
                                    )}
                                >
                                    <NoteContent
                                        className="text-sm"
                                        content={content}
                                    />
                                </div>

                                {/* Selectores en vista previa */}
                                <div className="grid grid-cols-2 gap-2">
                                    <Select
                                        classNames={{
                                            base: "w-full",
                                            trigger: clsx(
                                                "h-9",
                                                "bg-white dark:bg-gray-900",
                                                "text-xs",
                                            ),
                                        }}
                                        selectedKeys={
                                            importanceId ? [importanceId] : []
                                        }
                                        size="sm"
                                        onChange={(e) =>
                                            setImportanceId(e.target.value)
                                        }
                                    >
                                        {importanceOptions.map((option) => (
                                            <SelectItem
                                                key={option.id}
                                                textValue={option.name}
                                            >
                                                <div className="flex items-center gap-1 text-xs">
                                                    {option.color && (
                                                        <div
                                                            className="w-2 h-2 rounded-full"
                                                            style={{
                                                                backgroundColor:
                                                                    option.color,
                                                            }}
                                                        />
                                                    )}
                                                    <span>{option.name}</span>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </Select>
                                    <Select
                                        classNames={{
                                            base: "w-full",
                                            trigger: clsx(
                                                "h-9",
                                                "bg-white dark:bg-gray-900",
                                                "text-xs",
                                            ),
                                        }}
                                        selectedKeys={
                                            statusId ? [statusId] : []
                                        }
                                        size="sm"
                                        onChange={(e) =>
                                            setStatusId(e.target.value)
                                        }
                                    >
                                        {statusOptions.map((option) => (
                                            <SelectItem
                                                key={option.id}
                                                textValue={option.name}
                                            >
                                                <div className="flex items-center gap-1 text-xs">
                                                    {option.color && (
                                                        <div
                                                            className="w-2 h-2 rounded-full"
                                                            style={{
                                                                backgroundColor:
                                                                    option.color,
                                                            }}
                                                        />
                                                    )}
                                                    <span>{option.name}</span>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </Select>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </motion.div>
            </AnimatePresence>

            {/* Error general con animación */}
            <AnimatePresence>
                {error && !error.includes("obligatori") && (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        initial={{ opacity: 0, y: -10 }}
                        transition={springConfig.gentle}
                    >
                        <Chip
                            className="animate-shake"
                            color="danger"
                            size="sm"
                            variant="flat"
                        >
                            {error}
                        </Chip>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Botones de acción */}
            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2 text-xs text-gray-500">
                    <Chip
                        className="bg-gray-100 dark:bg-gray-800"
                        size="sm"
                        variant="light"
                    >
                        <kbd className="px-1 rounded bg-gray-200 dark:bg-gray-700">
                            Ctrl
                        </kbd>
                        <span className="mx-1">+</span>
                        <kbd className="px-1 rounded bg-gray-200 dark:bg-gray-700">
                            Enter
                        </kbd>
                        <span className="ml-1">para enviar</span>
                    </Chip>
                    <AnimatePresence>
                        {mode === "create" && content.trim() && (
                            <motion.div
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                                initial={{ opacity: 0, scale: 0.8 }}
                                transition={springConfig.gentle}
                            >
                                <Chip
                                    className="bg-secondary-100 dark:bg-secondary-900"
                                    color="secondary"
                                    size="sm"
                                    startContent={
                                        <DocumentIcon className="w-3 h-3" />
                                    }
                                    variant="flat"
                                >
                                    Borrador guardado
                                </Chip>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </div>

                <div className="flex gap-2">
                    {(mode === "edit" || (mode === "create" && onCancel)) && (
                        <Button
                            className={clsx(
                                "bg-gray-100 dark:bg-gray-800",
                                "hover:bg-gray-200 dark:hover:bg-gray-700",
                                "transition-all duration-200",
                            )}
                            color="default"
                            size="sm"
                            startContent={<XMarkIcon className="h-4 w-4" />}
                            variant="flat"
                            onClick={handleCancel}
                        >
                            Cancelar
                        </Button>
                    )}
                    <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                    >
                        <Button
                            className={clsx(
                                mode === "create"
                                    ? "bg-primary"
                                    : "bg-secondary",
                                "text-white shadow-lg",
                                "hover:shadow-xl",
                                "transition-all duration-200",
                            )}
                            color="primary"
                            isDisabled={
                                !content.trim() ||
                                isOverLimit ||
                                !importanceId ||
                                !statusId
                            }
                            isLoading={isPending}
                            size="sm"
                            startContent={
                                mode === "create" ? (
                                    <PaperAirplaneIcon className="h-4 w-4" />
                                ) : (
                                    <PencilIcon className="h-4 w-4" />
                                )
                            }
                            onClick={handleSubmit}
                        >
                            {mode === "create"
                                ? "Crear nota"
                                : "Guardar cambios"}
                        </Button>
                    </motion.div>
                </div>
            </div>
        </motion.div>
    );
}
