"use server";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

// Obtener helpers de revalidación para la entidad "contractor"
const { revalidateCache } = createServerRevalidation("contractor");

/**
 * Elimina un contratista por su ID
 * @param id ID del contratista a eliminar
 */
export async function deleteContractor(id: string) {
    try {
        // Validar que el ID es un string válido
        if (!id || typeof id !== "string") {
            return {
                success: false,
                error: "ID de contratista inválido",
            };
        }

        // Verificar si el contratista existe y si tiene asignaciones relacionadas
        const contractor = await db.contractor.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        assignments: true,
                    },
                },
            },
        });

        if (!contractor) {
            return {
                success: false,
                error: "Contratista no encontrado",
            };
        }

        // Verificar si el contratista tiene asignaciones
        if (contractor._count.assignments > 0) {
            return {
                success: false,
                error: "No se puede eliminar el contratista porque tiene asignaciones asociadas",
            };
        }

        // Eliminar el contratista
        await db.contractor.delete({
            where: { id },
        });

        // Revalidar caché usando el helper genérico
        revalidateCache(id);

        return { success: true };
    } catch (error) {
        // Usar el helper para manejar errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al eliminar contratista");
    }
}
