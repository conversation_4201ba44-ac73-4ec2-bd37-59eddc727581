"use client";

import React from "react";
import { Chip, Button, Tooltip } from "@heroui/react";
import {
    CheckCircleIcon,
    ArrowPathIcon,
    TrashIcon,
} from "@heroicons/react/24/outline";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

interface DraftIndicatorProps {
    lastSaved: Date | null;
    isRestored: boolean;
    onDiscard?: () => void;
}

export function DraftIndicator({
    lastSaved,
    isRestored,
    onDiscard,
}: DraftIndicatorProps) {
    if (!lastSaved) return null;

    const timeAgo = formatDistanceToNow(lastSaved, {
        addSuffix: true,
        locale: es,
    });

    return (
        <div className="flex items-center gap-2">
            {isRestored && (
                <Chip
                    color="primary"
                    size="sm"
                    startContent={<ArrowPathIcon className="w-3 h-3" />}
                    variant="flat"
                >
                    Borrador restaurado
                </Chip>
            )}

            <Tooltip content={`Último guardado: ${timeAgo}`}>
                <Chip
                    color="success"
                    size="sm"
                    startContent={<CheckCircleIcon className="w-3 h-3" />}
                    variant="flat"
                >
                    Guardado {timeAgo}
                </Chip>
            </Tooltip>

            {onDiscard && (
                <Tooltip content="Descartar borrador">
                    <Button
                        isIconOnly
                        color="danger"
                        size="sm"
                        variant="light"
                        onPress={onDiscard}
                    >
                        <TrashIcon className="w-4 h-4" />
                    </Button>
                </Tooltip>
            )}
        </div>
    );
}
