import {
    parseISO,
    isToday as isTodayFns,
    isTomorrow as isTomorrowFns,
    differenceInDays,
    isSameDay as isSameDayFns,
    isYesterday as isYesterdayFns,
    isPast as isPastFns,
    parse,
    isValid,
} from "date-fns";
import { formatInTimeZone, toZonedTime } from "date-fns-tz";

// Usar la zona horaria de México para todas las operaciones
export const TIME_ZONE = "America/Mexico_City";

/**
 * Tipos de visualización de fechas
 */
export enum DateDisplayType {
    regular = "regular",
    received = "received",
    estimated = "estimated",
}

/**
 * Normaliza diferentes formatos de entrada a un objeto Date
 * @param date Fecha en varios formatos posibles
 * @returns Date object o null si es inválido
 */
export const normalizeDate = (
    date: string | Date | undefined | null,
): Date | null => {
    if (!date) return null;

    try {
        // Si ya es un objeto Date
        if (date instanceof Date) {
            return isValid(date) ? date : null;
        }

        // Si es string
        if (typeof date === "string") {
            // Intentar interpretar como ISO
            let parsedDate = parseISO(date);

            // Si es válido, devolver
            if (isValid(parsedDate)) {
                return parsedDate;
            }

            // Intentar formato dd/mm/yyyy
            try {
                parsedDate = parse(date, "dd/MM/yyyy", new Date());
                if (isValid(parsedDate)) {
                    return parsedDate;
                }
            } catch (error) {
                // Error al parsear fecha en formato dd/MM/yyyy - comentario removido
            }

            // Intentar formato yyyy-mm-dd
            try {
                parsedDate = parse(date, "yyyy-MM-dd", new Date());
                if (isValid(parsedDate)) {
                    return parsedDate;
                }
            } catch (error) {
                // Error al parsear fecha en formato yyyy-MM-dd - comentario removido
            }
        }

        return null;
    } catch (error) {
        // REMOVED: console.error("Error al normalizar fecha:", error);

        return null;
    }
};

/**
 * Formatea una fecha considerando la zona horaria
 * @param date Fecha a formatear
 * @param formatString Formato de salida (por defecto: dd/MM/yyyy)
 * @returns String formateado o mensaje de error
 */
export function formatDate(
    date: string | Date | undefined | null,
    formatString: string = "dd/MM/yyyy",
): string {
    try {
        const normalizedDate = normalizeDate(date);

        if (!normalizedDate) return "";

        return formatInTimeZone(normalizedDate, TIME_ZONE, formatString);
    } catch (error) {
        // REMOVED: console.error("Error al formatear fecha:", error);

        return "";
    }
}

/**
 * Comprueba si dos fechas son el mismo día
 */
export const isSameDay = (
    date1: string | Date | undefined | null,
    date2: string | Date | undefined | null,
): boolean => {
    const normalizedDate1 = normalizeDate(date1);
    const normalizedDate2 = normalizeDate(date2);

    if (!normalizedDate1 || !normalizedDate2) return false;

    return isSameDayFns(
        toZonedTime(normalizedDate1, TIME_ZONE),
        toZonedTime(normalizedDate2, TIME_ZONE),
    );
};

/**
 * Comprueba si una fecha es hoy
 */
export const isToday = (date: string | Date | undefined | null): boolean => {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return false;

    return isTodayFns(toZonedTime(normalizedDate, TIME_ZONE));
};

/**
 * Comprueba si una fecha es mañana
 */
export const isTomorrow = (date: string | Date | undefined | null): boolean => {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return false;

    return isTomorrowFns(toZonedTime(normalizedDate, TIME_ZONE));
};

/**
 * Comprueba si una fecha es ayer
 */
export const isYesterday = (
    date: string | Date | undefined | null,
): boolean => {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return false;

    return isYesterdayFns(toZonedTime(normalizedDate, TIME_ZONE));
};

/**
 * Comprueba si una fecha está en el pasado
 */
export const isPast = (date: string | Date | undefined | null): boolean => {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return false;

    return isPastFns(toZonedTime(normalizedDate, TIME_ZONE));
};

/**
 * Obtiene la diferencia en días entre la fecha proporcionada y hoy
 */
export const getDaysDifference = (
    date: string | Date | undefined | null,
): number => {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return 0;

    const today = new Date();

    return differenceInDays(
        toZonedTime(normalizedDate, TIME_ZONE),
        toZonedTime(today, TIME_ZONE),
    );
};

/**
 * Formatea una fecha para mostrar de forma relativa
 */
export function formatRelative(
    date: string | Date | undefined | null,
    showFullDate = true,
): string {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return "";

    if (isToday(normalizedDate)) {
        return "Hoy";
    }

    if (isTomorrow(normalizedDate)) {
        return "Mañana";
    }

    if (isYesterday(normalizedDate)) {
        return "Ayer";
    }

    const daysDiff = getDaysDifference(normalizedDate);

    if (daysDiff > 0) {
        return `En ${daysDiff} día${daysDiff !== 1 ? "s" : ""}${showFullDate ? ` (${formatDate(normalizedDate)})` : ""}`;
    }

    if (daysDiff < 0) {
        const absDiff = Math.abs(daysDiff);

        return `Hace ${absDiff} día${absDiff !== 1 ? "s" : ""}${showFullDate ? ` (${formatDate(normalizedDate)})` : ""}`;
    }

    return formatDate(normalizedDate);
}

/**
 * Ajusta el offset de zona horaria para una fecha
 * @param date Fecha a ajustar
 * @returns Date ajustada o null si es inválida
 */
export function adjustTimezoneOffset(
    date: string | Date | null | undefined,
): Date | null {
    const normalizedDate = normalizeDate(date);

    if (!normalizedDate) return null;

    // En lugar de usar toZonedTime, vamos a ajustar la fecha manualmente
    // para evitar el problema de desfase de un día
    const dateStr = normalizedDate.toISOString().split("T")[0]; // Obtener solo YYYY-MM-DD
    const dateWithoutTime = new Date(`${dateStr}T12:00:00`); // Crear fecha a mediodía para evitar problemas de zona horaria

    return dateWithoutTime;
}

/**
 * Obtiene las clases CSS para los diferentes estados de las fechas
 */
export function getDateStatusClasses(
    statusType: string,
    displayType: DateDisplayType,
): { bgClass: string; textClass: string } {
    // Valores predeterminados
    let bgClass =
        "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700";
    let textClass = "text-gray-700 dark:text-gray-300";

    // Ajustar según el tipo y estado
    if (displayType === "received") {
        if (statusType === "past") {
            bgClass =
                "bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800";
            textClass = "text-red-700 dark:text-red-300";
        } else if (statusType === "today") {
            bgClass =
                "bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800";
            textClass = "text-green-700 dark:text-green-300";
        } else {
            bgClass =
                "bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800";
            textClass = "text-blue-700 dark:text-blue-300";
        }
    } else if (displayType === "estimated") {
        if (statusType === "past") {
            bgClass =
                "bg-amber-50 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800";
            textClass = "text-amber-700 dark:text-amber-300";
        } else if (statusType === "today" || statusType === "tomorrow") {
            bgClass =
                "bg-purple-50 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800";
            textClass = "text-purple-700 dark:text-purple-300";
        } else {
            bgClass =
                "bg-indigo-50 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800";
            textClass = "text-indigo-700 dark:text-indigo-300";
        }
    }

    return { bgClass, textClass };
}

/**
 * Formatea una fecha para su visualización en la interfaz
 */
export function formatDateForDisplay(
    date: string | Date | null | undefined,
    formatStr: string = "dd MMM yyyy",
    type: DateDisplayType = DateDisplayType.regular,
): {
    formattedDate: string;
    relativeText: string;
    statusType: string;
    isValid: boolean;
    parsedDate: Date | null;
} {
    // Normalizar la fecha
    const parsedDate = normalizeDate(date);

    // Si la fecha no es válida, devolver un objeto con información de error
    if (!parsedDate) {
        return {
            formattedDate: "",
            relativeText: "Fecha inválida",
            statusType: "invalid",
            isValid: false,
            parsedDate: null,
        };
    }

    // Formatear la fecha según el formato especificado
    const formattedDate = formatDate(parsedDate, formatStr);

    // Determinar el estado relativo de la fecha
    let statusType = "future";
    let relativeText = "";

    if (isToday(parsedDate)) {
        statusType = "today";
        relativeText = "Hoy";
    } else if (isTomorrow(parsedDate)) {
        statusType = "tomorrow";
        relativeText = "Mañana";
    } else if (isYesterday(parsedDate)) {
        statusType = "past";
        relativeText = "Ayer";
    } else if (isPast(parsedDate)) {
        statusType = "past";
        const daysDiff = Math.abs(getDaysDifference(parsedDate));

        relativeText = `Hace ${daysDiff} día${daysDiff !== 1 ? "s" : ""}`;
    } else {
        const daysDiff = getDaysDifference(parsedDate);

        if (daysDiff > 0 && daysDiff <= 7) {
            statusType = "soon";
            relativeText = `En ${daysDiff} día${daysDiff !== 1 ? "s" : ""}`;
        } else {
            relativeText = formatDate(parsedDate, "dd MMM");
        }
    }

    // Ajustar el texto según el tipo de fecha
    if (type === DateDisplayType.received && statusType === "past") {
        relativeText = `Recibido ${relativeText.toLowerCase()}`;
    } else if (type === DateDisplayType.estimated) {
        if (statusType === "past") {
            relativeText = `Vencido ${relativeText.toLowerCase()}`;
        } else if (statusType === "today") {
            relativeText = "Entrega hoy";
        } else if (statusType === "tomorrow") {
            relativeText = "Entrega mañana";
        } else if (statusType === "soon") {
            relativeText = `Entrega en ${getDaysDifference(parsedDate)} días`;
        } else {
            relativeText = `Entrega el ${formatDate(parsedDate, "dd MMM")}`;
        }
    }

    return {
        formattedDate,
        relativeText,
        statusType,
        isValid: true,
        parsedDate,
    };
}
