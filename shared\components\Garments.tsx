// components/Garments.tsx

import React from "react";
import {
    Card,
    Button,
    Input,
    Autocomplete,
    AutocompleteItem,
} from "@heroui/react";
import { CubeIcon, PlusIcon } from "@heroicons/react/24/outline";

import { GarmentEntry, SizeOption, SizeEntry } from "../../core/types/types";

interface GarmentsProps {
    garments: GarmentEntry[];
    models: any[];
    colors: any[];
    sizesOptions: SizeOption[];
    validationErrors?: { [key: string]: string };
    onAddGarment: () => void;
    onRemoveGarment: (index: number) => void;
    onGarmentChange: (
        index: number,
        field: keyof GarmentEntry,
        value: string,
    ) => void;
    onAddSize: (garmentIndex: number) => void;
    onRemoveSize: (garmentIndex: number, sizeIndex: number) => void;
    onSizeChange: (
        garmentIndex: number,
        sizeIndex: number,
        field: keyof SizeEntry,
        value: string,
    ) => void;
}

const Garments: React.FC<GarmentsProps> = ({
    garments,
    models,
    colors,
    sizesOptions,
    validationErrors = {},
    onAddGarment,
    onRemoveGarment,
    onGarmentChange,
    onAddSize,
    onRemoveSize,
    onSizeChange,
}) => {
    return (
        <Card className="shadow-md rounded-lg p-6 bg-white dark:bg-gray-800">
            <div className="flex items-center gap-2 mb-4">
                <CubeIcon className="w-6 h-6" />
                <h2 className="text-2xl font-bold">Prendas</h2>
            </div>
            {garments.map((garment, gIndex) => (
                <div
                    key={gIndex}
                    className="border p-4 rounded-lg space-y-4 bg-white/80 dark:bg-gray-900/50 mb-4"
                >
                    <div className="flex justify-between items-center">
                        <h3 className="text-md font-bold">
                            Prenda {gIndex + 1}
                        </h3>
                        <Button
                            color="danger"
                            variant="flat"
                            onPress={() => onRemoveGarment(gIndex)}
                        >
                            Eliminar Prenda
                        </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Autocomplete
                            color={
                                validationErrors[`garment_${gIndex}_model`]
                                    ? "danger"
                                    : "primary"
                            }
                            errorMessage={
                                validationErrors[`garment_${gIndex}_model`]
                            }
                            label="Modelo *"
                            placeholder="Buscar modelo..."
                            selectedKey={garment.modelId}
                            variant="bordered"
                            onSelectionChange={(key) =>
                                onGarmentChange(
                                    gIndex,
                                    "modelId",
                                    key as string,
                                )
                            }
                        >
                            {models.map((m) => (
                                <AutocompleteItem key={m.id} textValue={m.code}>
                                    {m.code}
                                </AutocompleteItem>
                            ))}
                        </Autocomplete>

                        <Autocomplete
                            color={
                                validationErrors[`garment_${gIndex}_color`]
                                    ? "danger"
                                    : "primary"
                            }
                            errorMessage={
                                validationErrors[`garment_${gIndex}_color`]
                            }
                            label="Color *"
                            placeholder="Buscar color..."
                            selectedKey={garment.colorId}
                            variant="bordered"
                            onSelectionChange={(key) =>
                                onGarmentChange(
                                    gIndex,
                                    "colorId",
                                    key as string,
                                )
                            }
                        >
                            {colors.map((c) => (
                                <AutocompleteItem key={c.id} textValue={c.name}>
                                    {c.name}
                                </AutocompleteItem>
                            ))}
                        </Autocomplete>
                    </div>

                    <div className="space-y-2">
                        <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300">
                            Tallas
                        </h4>
                        {validationErrors[`garment_${gIndex}_sizes`] && (
                            <div className="text-sm text-danger mb-2">
                                {validationErrors[`garment_${gIndex}_sizes`]}
                            </div>
                        )}
                        {validationErrors[`garment_${gIndex}_duplicate`] && (
                            <div className="text-sm text-danger mb-2">
                                {
                                    validationErrors[
                                        `garment_${gIndex}_duplicate`
                                    ]
                                }
                            </div>
                        )}
                        {garment.sizes.map((sz, sIndex) => (
                            <div
                                key={sIndex}
                                className="flex items-center gap-4 mb-2"
                            >
                                <Autocomplete
                                    color={
                                        validationErrors[
                                            `garment_${gIndex}_size_${sIndex}`
                                        ]
                                            ? "danger"
                                            : "primary"
                                    }
                                    errorMessage={
                                        validationErrors[
                                            `garment_${gIndex}_size_${sIndex}`
                                        ]
                                    }
                                    label="Talla *"
                                    placeholder="Buscar talla..."
                                    selectedKey={sz.sizeId}
                                    variant="bordered"
                                    onSelectionChange={(key) =>
                                        onSizeChange(
                                            gIndex,
                                            sIndex,
                                            "sizeId",
                                            key as string,
                                        )
                                    }
                                >
                                    {sizesOptions.map((so) => (
                                        <AutocompleteItem
                                            key={so.id}
                                            textValue={so.code}
                                        >
                                            {so.code}
                                        </AutocompleteItem>
                                    ))}
                                </Autocomplete>
                                <Input
                                    classNames={{
                                        input: "w-24 text-center focus:outline-none",
                                    }}
                                    color={
                                        validationErrors[
                                            `garment_${gIndex}_size_${sIndex}_qty`
                                        ]
                                            ? "danger"
                                            : "default"
                                    }
                                    errorMessage={
                                        validationErrors[
                                            `garment_${gIndex}_size_${sIndex}_qty`
                                        ]
                                    }
                                    min={1}
                                    placeholder="Cantidad"
                                    type="number"
                                    value={
                                        sz.quantity === "0" ? "" : sz.quantity
                                    }
                                    variant="bordered"
                                    onChange={(e) =>
                                        onSizeChange(
                                            gIndex,
                                            sIndex,
                                            "quantity",
                                            e.target.value,
                                        )
                                    }
                                />
                                <Button
                                    color="danger"
                                    variant="flat"
                                    onPress={() => onRemoveSize(gIndex, sIndex)}
                                >
                                    Eliminar
                                </Button>
                            </div>
                        ))}
                        <Button
                            className="flex items-center gap-2 text-blue-500 hover:text-blue-700 transition-all duration-150"
                            variant="flat"
                            onPress={() => onAddSize(gIndex)}
                        >
                            <PlusIcon className="w-4 h-4" />
                            Agregar Talla
                        </Button>
                    </div>
                </div>
            ))}
            <Button
                className="text-blue-500 hover:text-blue-700"
                variant="flat"
                onPress={onAddGarment}
            >
                Agregar Prenda
            </Button>
        </Card>
    );
};

export default Garments;
