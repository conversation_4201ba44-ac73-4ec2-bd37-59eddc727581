const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function checkOrderStatuses() {
    console.log("🔍 Verificando estados de OrderStatus en la BD...\n");
    
    try {
        const orderStatuses = await prisma.orderStatus.findMany({
            orderBy: { name: 'asc' }
        });
        
        console.log("📊 Estados encontrados:");
        orderStatuses.forEach(status => {
            const hasIcon = status.iconName ? "✅" : "❌";
            console.log(`${hasIcon} ${status.name}: iconName="${status.iconName || 'NULL'}", color="${status.color}"`);
        });
        
        console.log("\n🔍 Verificación específica:");
        const nuevo = orderStatuses.find(s => s.name === "Nuevo");
        const enProduccion = orderStatuses.find(s => s.name === "En producción");
        
        if (nuevo) {
            console.log(`\nNuevo: ${JSON.stringify(nuevo, null, 2)}`);
        } else {
            console.log("\n❌ Estado 'Nuevo' NO encontrado");
        }
        
        if (enProduccion) {
            console.log(`\nEn producción: ${JSON.stringify(enProduccion, null, 2)}`);
        } else {
            console.log("\n❌ Estado 'En producción' NO encontrado");
        }
        
    } catch (error) {
        console.error("❌ Error:", error);
    } finally {
        await prisma.$disconnect();
    }
}

checkOrderStatuses();
