"use server";

import { Prisma } from "@prisma/client";

import { ActionResponse } from "./utils";

/**
 * Maneja errores específicos de Prisma y retorna respuestas consistentes
 */
export function handlePrismaError(error: unknown): ActionResponse<never> {
    // REMOVED: console.error("[Prisma Error]", error);

    // Error de Prisma conocido
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
            case "P2002":
                // Violación de restricción única
                const field = error.meta?.target as string[] | undefined;

                return {
                    success: false,
                    error: `Ya existe un registro con ${field?.join(", ") || "estos datos"}`,
                };

            case "P2025":
                // Registro no encontrado
                return {
                    success: false,
                    error: "El registro solicitado no fue encontrado",
                };

            case "P2003":
                // Violación de clave foránea
                return {
                    success: false,
                    error: "No se puede completar la operación debido a referencias existentes",
                };

            case "P2014":
                // Violación de relación requerida
                return {
                    success: false,
                    error: "Faltan datos requeridos para completar la operación",
                };

            default:
                return {
                    success: false,
                    error: `Error de base de datos: ${error.code}`,
                };
        }
    }

    // Error de validación de Prisma
    if (error instanceof Prisma.PrismaClientValidationError) {
        return {
            success: false,
            error: "Los datos proporcionados no son válidos",
        };
    }

    // Error de inicialización de Prisma
    if (error instanceof Prisma.PrismaClientInitializationError) {
        return {
            success: false,
            error: "Error al conectar con la base de datos",
        };
    }

    // Error genérico
    if (error instanceof Error) {
        return {
            success: false,
            error: error.message,
        };
    }

    return {
        success: false,
        error: "Ha ocurrido un error inesperado",
    };
}

/**
 * Wrapper para ejecutar operaciones de Prisma con manejo de errores
 */
export async function withPrismaErrorHandling<T>(
    operation: () => Promise<T>,
): Promise<ActionResponse<T>> {
    try {
        const data = await operation();

        return {
            success: true,
            data,
        };
    } catch (error) {
        return handlePrismaError(error);
    }
}

/**
 * Decorator para server actions que maneja errores automáticamente
 * Uso: const myAction = withErrorHandler(async (data) => { ... })
 */
export function withErrorHandler<TArgs extends any[], TReturn>(
    action: (...args: TArgs) => Promise<ActionResponse<TReturn>>,
) {
    return async (...args: TArgs): Promise<ActionResponse<TReturn>> => {
        try {
            return await action(...args);
        } catch (error) {
            return handlePrismaError(error);
        }
    };
}
