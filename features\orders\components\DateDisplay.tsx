import React from "react";
import { Chip, Tooltip, cn } from "@heroui/react";
import {
    CalendarIcon,
    ClockIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

import {
    formatDateForDisplay,
    getDateStatusClasses,
    formatRelative,
    DateDisplayType,
    adjustTimezoneOffset,
} from "@/shared/utils";

// Re-exportamos la función formatDateForDisplay para mantener compatibilidad con el código existente
export const formatDate = formatDateForDisplay;

interface DateDisplayProps {
    date: string | Date | null | undefined;
    format?: string;
    showRelative?: boolean;
    className?: string;
    type?: DateDisplayType;
    chipSize?: "sm" | "md" | "lg";
    showIcon?: boolean;
}

/**
 * Componente reutilizable para mostrar fechas con formato uniforme
 * Proporciona visualización mejorada para fechas recibidas y estimadas
 */
export function DateDisplay({
    date,
    format: formatStr = "dd MMM yyyy",
    showRelative = true,
    className,
    type = DateDisplayType.regular,
    chipSize = "sm",
    showIcon = true,
}: DateDisplayProps) {
    // Ajustar la fecha para compensar el desfase de zona horaria y formatearla
    const adjustedDate = adjustTimezoneOffset(date);
    const dateInfo = formatDateForDisplay(
        adjustedDate ? adjustedDate : date,
        formatStr,
        type,
    );

    // Si la fecha no es válida, mostrar un mensaje de error
    if (!dateInfo.isValid || !dateInfo.parsedDate) {
        return (
            <Tooltip
                content={`La fecha "${date}" tiene un formato no reconocido`}
            >
                <div className="flex flex-col items-center gap-1">
                    <Chip
                        className="bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800 text-xs shadow-sm"
                        size={chipSize}
                        startContent={
                            showIcon ? (
                                <ExclamationCircleIcon className="w-3 h-3" />
                            ) : undefined
                        }
                        variant="flat"
                    >
                        Formato inválido
                    </Chip>
                    <span className="text-[10px] text-red-500">
                        Revisa la fecha
                    </span>
                </div>
            </Tooltip>
        );
    }

    // Obtener clases CSS según el estado de la fecha
    const { bgClass, textClass: _textClass } = getDateStatusClasses(
        dateInfo.statusType,
        type,
    );

    // Determinar el icono según el estado
    const renderIcon = (): React.ReactNode => {
        if (!showIcon) return undefined;

        if (dateInfo.statusType === "past") {
            return <ExclamationCircleIcon className="w-3 h-3" />;
        } else if (dateInfo.statusType === "today") {
            return <CheckCircleIcon className="w-3 h-3" />;
        } else if (
            dateInfo.statusType === "tomorrow" ||
            dateInfo.statusType === "soon"
        ) {
            return <ClockIcon className="w-3 h-3" />;
        } else {
            return <CalendarIcon className="w-3 h-3" />;
        }
    };

    const iconComponent = renderIcon();

    // Para fechas de tipo recibida o estimada, mostrar un chip con información contextual
    if (
        type === DateDisplayType.received ||
        type === DateDisplayType.estimated
    ) {
        return (
            <Tooltip
                content={`${dateInfo.formattedDate} - ${dateInfo.relativeText}`}
            >
                <Chip
                    className={cn(bgClass, className)}
                    size={chipSize}
                    startContent={iconComponent}
                    variant="flat"
                >
                    {showRelative
                        ? dateInfo.relativeText
                        : dateInfo.formattedDate}
                </Chip>
            </Tooltip>
        );
    }

    // Para fechas regulares, mostrar solo el formato estándar
    return (
        <span
            className={cn(
                "text-sm text-gray-700 dark:text-gray-300",
                className,
            )}
        >
            {dateInfo.formattedDate}
        </span>
    );
}

const getDisplayText = (date: Date, showFullDate: boolean = true): string => {
    // Intentar formatear la fecha
    try {
        const dateInfo = formatDateForDisplay(date);
        const relativeText = formatRelative(date, showFullDate);

        if (relativeText) {
            return `${relativeText} · ${dateInfo.formattedDate}`;
        }

        return dateInfo.formattedDate;
    } catch (error) {
        // REMOVED: console.error("Error al formatear fecha:", error);

        return "Fecha inválida";
    }
};

// Re-exportamos como default para mantener compatibilidad
export default DateDisplay;
