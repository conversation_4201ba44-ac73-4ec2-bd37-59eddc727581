/**
 * SizeOrder Value Object
 *
 * Represents the ordering/position of a size in a sequence.
 * Ensures ordering values are valid and provides comparison methods.
 */

export class SizeOrder {
    private readonly _value: number;

    constructor(value: number) {
        this.validate(value);
        this._value = value;
    }

    get value(): number {
        return this._value;
    }

    /**
     * Validate the order value
     */
    private validate(value: number): void {
        if (value === undefined || value === null) {
            throw new Error("Size order cannot be null or undefined");
        }

        if (!Number.isInteger(value)) {
            throw new Error("Size order must be an integer");
        }

        if (value < 0) {
            throw new Error("Size order cannot be negative");
        }

        if (value > 9999) {
            throw new Error("Size order cannot exceed 9999");
        }
    }

    /**
     * Check if this order comes before another
     */
    isLessThan(other: SizeOrder): boolean {
        return this._value < other._value;
    }

    /**
     * Check if this order comes after another
     */
    isGreaterThan(other: SizeOrder): boolean {
        return this._value > other._value;
    }

    /**
     * Check if this order is the same as another
     */
    equals(other: SizeOrder): boolean {
        return this._value === other._value;
    }

    /**
     * Check if this order is less than or equal to another
     */
    isLessThanOrEqual(other: SizeOrder): boolean {
        return this._value <= other._value;
    }

    /**
     * Check if this order is greater than or equal to another
     */
    isGreaterThanOrEqual(other: SizeOrder): boolean {
        return this._value >= other._value;
    }

    /**
     * Get the distance between two orders
     */
    distanceFrom(other: SizeOrder): number {
        return Math.abs(this._value - other._value);
    }

    /**
     * Create a new order that comes after this one
     */
    next(increment: number = 10): SizeOrder {
        return new SizeOrder(this._value + increment);
    }

    /**
     * Create a new order that comes before this one
     */
    previous(decrement: number = 10): SizeOrder {
        const newValue = this._value - decrement;

        return new SizeOrder(Math.max(0, newValue));
    }

    /**
     * Create an order between two existing orders
     */
    static between(first: SizeOrder, second: SizeOrder): SizeOrder {
        const min = Math.min(first._value, second._value);
        const max = Math.max(first._value, second._value);

        if (max - min <= 1) {
            throw new Error("Cannot create order between consecutive values");
        }

        const middle = Math.floor((min + max) / 2);

        return new SizeOrder(middle);
    }

    /**
     * Convert to string
     */
    toString(): string {
        return this._value.toString();
    }

    /**
     * Convert to JSON
     */
    toJSON(): number {
        return this._value;
    }

    /**
     * Create from value with validation
     */
    static from(value: number): SizeOrder {
        return new SizeOrder(value);
    }

    /**
     * Create the first order in a sequence
     */
    static first(): SizeOrder {
        return new SizeOrder(0);
    }

    /**
     * Create an order at a specific position with standard spacing
     */
    static at(position: number, spacing: number = 10): SizeOrder {
        return new SizeOrder(position * spacing);
    }

    /**
     * Check if a value is valid for SizeOrder
     */
    static isValid(value: number): boolean {
        try {
            new SizeOrder(value);

            return true;
        } catch {
            return false;
        }
    }

    /**
     * Create an array of orders with standard spacing
     */
    static sequence(
        count: number,
        startAt: number = 0,
        spacing: number = 10,
    ): SizeOrder[] {
        const orders: SizeOrder[] = [];

        for (let i = 0; i < count; i++) {
            orders.push(new SizeOrder(startAt + i * spacing));
        }

        return orders;
    }

    /**
     * Reorder a list of items based on their current order
     */
    static reorder<T extends { order: number }>(items: T[]): T[] {
        return [...items].sort((a, b) => a.order - b.order);
    }
}
