"use client";

import type { SWRConfiguration } from "swr";
import type { TodoFilters } from "../types";

import { useState } from "react";
import useSWR, { mutate } from "swr";

import {
    getTodos,
    getTodo,
    createTodo as create,
    updateTodo as update,
    deleteTodo as remove,
    toggleTodoComplete,
} from "../actions";

/**
 * Hook para obtener todos los todos
 */
export function useTodos(filters: TodoFilters = {}, config?: SWRConfiguration) {
    const filtersKey = JSON.stringify(filters);

    const { data, error, isLoading, mutate } = useSWR(
        ["todos", filtersKey],
        async () => getTodos(filters),
        {
            revalidateOnFocus: true,
            revalidateIfStale: true,
            ...config,
        },
    );

    return {
        todos: (data as any)?.data?.todos || [],
        pagination: (data as any)?.data?.pagination,
        isLoading,
        isError: !!error,
        error: (data as any)?.error || error,
        mutate,
    };
}

/**
 * Hook para obtener un todo por ID
 */
export function useTodo(
    id: string | null | undefined,
    config?: SWRConfiguration,
) {
    const { data, error, isLoading, mutate } = useSWR(
        id ? ["todo", id] : null,
        async () => (id ? getTodo(id) : null),
        {
            revalidateOnFocus: true,
            ...config,
        },
    );

    return {
        todo: (data as any)?.data,
        isLoading,
        isError: !!error,
        error: (data as any)?.error || error,
        mutate,
    };
}

/**
 * Hook para crear un todo
 */
export function useCreateTodo() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createTodo = async (data: Parameters<typeof create>[0]) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await create(data);

            if (result?.success) {
                // Revalidar todos los todos
                await mutate((key) => Array.isArray(key) && key[0] === "todos");
            } else {
                setError(result?.error || "Error al crear el todo");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        createTodo,
        isCreating: isLoading,
        createError: error,
    };
}

/**
 * Hook para actualizar un todo
 */
export function useUpdateTodo() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateTodo = async (
        id: string,
        data: Parameters<typeof update>[1],
    ) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await update(id, data);

            if (result?.success) {
                // Revalidar el todo específico y la lista
                await Promise.all([
                    mutate(["todo", id]),
                    mutate((key) => Array.isArray(key) && key[0] === "todos"),
                ]);
            } else {
                setError(result?.error || "Error al actualizar el todo");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        updateTodo,
        isUpdating: isLoading,
        updateError: error,
    };
}

/**
 * Hook para eliminar un todo
 */
export function useDeleteTodo() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteTodo = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await remove(id);

            if (result?.success) {
                // Revalidar la lista
                await mutate((key) => Array.isArray(key) && key[0] === "todos");
            } else {
                setError(result?.error || "Error al eliminar el todo");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        deleteTodo,
        isDeleting: isLoading,
        deleteError: error,
    };
}

/**
 * Hook para cambiar el estado de completado
 */
export function useToggleTodo() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const toggle = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await toggleTodoComplete(id, true); // Simple fallback

            if (result?.success) {
                // Revalidar el todo específico y la lista
                await Promise.all([
                    mutate(["todo", id]),
                    mutate((key) => Array.isArray(key) && key[0] === "todos"),
                ]);
            } else {
                setError(result?.error || "Error al cambiar estado");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return {
        toggle,
        isToggling: isLoading,
        toggleError: error,
    };
}
