"use client";

import React from "react";
import { ClipboardDocumentIcon } from "@heroicons/react/24/outline";

interface NoteFormHeaderProps {
    title: string;
    description?: string;
    icon?: React.ReactNode;
}

export function NoteFormHeader({
    title,
    description,
    icon = <ClipboardDocumentIcon className="w-5 h-5" />,
}: NoteFormHeaderProps) {
    return (
        <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                <div className="text-yellow-600 dark:text-yellow-400">
                    {icon}
                </div>
            </div>
            <div>
                <h2 className="text-xl font-semibold">{title}</h2>
                {description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                        {description}
                    </p>
                )}
            </div>
        </div>
    );
}
