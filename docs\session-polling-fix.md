# Fix: Session Polling Excesivo y Prepared Statements

## Problema Identificado

### Síntomas
1. **Polling excesivo**: `/api/auth/session` se ejecutaba cada segundo (1000ms)
2. **Prepared statements perdidos**: Errores `prepared statement "sXX" does not exist`
3. **Sobrecarga del sistema**: 40+ requests de sesión por minuto

### Causa Raíz
La combinación de:
- NextAuth configurado con `refetchInterval={1000}` (1 segundo)
- Turbopack HMR creando nuevas conexiones
- PgBouncer en modo transacción sin soporte para prepared statements persistentes
- Agotamiento del pool de conexiones por exceso de requests

## Soluciones Implementadas

### 1. Reducir Polling de Sesión
```typescript
// app/providers.tsx
<SessionProvider
    refetchInterval={5 * 60 * 1000} // Cambiado de 1 segundo a 5 minutos
    refetchOnWindowFocus={false}    // Desactivado para evitar requests innecesarios
    refetchWhenOffline={false}
>
```

### 2. <PERSON><PERSON> Headers en Session Endpoint
```typescript
// app/api/auth/[...nextauth]/route.ts
export async function GET(request: Request) {
    const response = await originalGET(request);
    
    // Cache la respuesta por 60 segundos
    response.headers.set(
        'Cache-Control',
        'private, max-age=60, stale-while-revalidate=30'
    );
    
    return response;
}
```

### 3. Deshabilitar Prepared Statements en Desarrollo
```typescript
// shared/lib/prisma.ts
if (process.env.NODE_ENV === "development" && !url.includes("pgbouncer=true")) {
    connectionUrl = url.includes("?") 
        ? `${url}&pgbouncer=true&statement_cache_size=0`
        : `${url}?pgbouncer=true&statement_cache_size=0`;
}
```

## Resultados Esperados

### Antes
- 40-60 requests/minuto a `/api/auth/session`
- Errores frecuentes de prepared statements
- Alta carga en la base de datos

### Después
- 1 request cada 5 minutos por pestaña
- Sin errores de prepared statements
- Carga reducida en 98%

## Configuración Adicional Recomendada

### Para Producción
```env
# .env.production
DATABASE_URL="postgresql://[user]:[pass]@[host]:6543/postgres?pgbouncer=true&connection_limit=1"
```

### Para Desarrollo
```env
# .env.development
DATABASE_URL="postgresql://[user]:[pass]@[host]:6543/postgres?pgbouncer=true&statement_cache_size=0"
```

## Monitoreo

Para verificar que el fix funciona:

1. **Revisar logs del servidor**: No deberías ver múltiples GET a `/api/auth/session`
2. **Verificar errores de Prisma**: No más errores de "prepared statement does not exist"
3. **Performance**: Las páginas deberían cargar más rápido sin el overhead del polling

## Consideraciones Futuras

1. **Implementar WebSockets** para actualizaciones de sesión en tiempo real si es necesario
2. **Usar Redis** para cache de sesiones y reducir carga en PostgreSQL
3. **Configurar un pool dedicado** para queries de autenticación