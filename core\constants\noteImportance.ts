// Enumeración de importancias para notas
export enum NoteImportance {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    // CRITICAL ya no existe en la base de datos
}

// Mapeo de importancia a IDs (para usar con Prisma)
export const noteImportanceIds = {
    [NoteImportance.LOW]: "cm9d2wh7i000cfcac81ycnihq", // Bajo (azul)
    [NoteImportance.MEDIUM]: "cm9d2wh7i000dfcacku66sz1o", // Medio (ámbar)
    [NoteImportance.HIGH]: "cm9d2wh7i000efcac5qped3fl", // Alto (rojo)
};

// Mapeo de importancia a colores (para UI)
export const noteImportanceColors = {
    [NoteImportance.LOW]: "#3B82F6", // Azul
    [NoteImportance.MEDIUM]: "#F59E0B", // Ámbar
    [NoteImportance.HIGH]: "#EF4444", // Rojo
};

// Mapeo de importancia a descripciones
export const noteImportanceLabels = {
    [NoteImportance.LOW]: "Bajo",
    [NoteImportance.MEDIUM]: "Medio",
    [NoteImportance.HIGH]: "Alto",
};
