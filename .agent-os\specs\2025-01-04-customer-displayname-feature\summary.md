# Resumen: Campo displayName para clientes principales

## Problema
Los clientes principales solo tienen el campo `name`, mientras que los subclientes ya tienen implementado `displayName`. El campo existe en la base de datos pero no está expuesto en la UI para clientes principales.

## Solución
Agregar el campo `displayName` (opcional) a los formularios de creación y edición de clientes principales, siguiendo el mismo patrón ya implementado para subclientes.

## Cambios principales
1. Actualizar tipos TypeScript para incluir displayName
2. Modificar acciones del servidor (create/update) para manejar displayName
3. Agregar campo Textarea en formularios de creación y edición
4. Mantener consistencia con implementación de subclientes

## Beneficios
- Permite nombres descriptivos más largos (ej: "Empresa ABC - División Norte")
- Consistencia entre clientes y subclientes
- Mejor organización visual en listas y jerarquías
- No requiere migraciones de base de datos