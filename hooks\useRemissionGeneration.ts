"use client";

import { useState, useCallback } from "react";
import { addToast } from "@heroui/react";

import { RemissionPreview } from "@/types/remission";
import { downloadRemissionPDF } from "@/lib/pdf/generator";

export interface RemissionGenerationState {
    isGenerating: boolean;
    isDownloading: boolean;
    error: string | null;
    progress: number;
    currentStep:
        | "idle"
        | "creating"
        | "generating"
        | "downloading"
        | "complete";
    remissionData: RemissionPreview | null;
    pdfBlob: Blob | null;
}

export interface UseRemissionGenerationReturn {
    state: RemissionGenerationState;
    generateRemission: (data: RemissionPreview) => Promise<void>;
    downloadPDF: () => Promise<void>;
    reset: () => void;
    setProgress: (progress: number) => void;
}

export function useRemissionGeneration(): UseRemissionGenerationReturn {
    const [state, setState] = useState<RemissionGenerationState>({
        isGenerating: false,
        isDownloading: false,
        error: null,
        progress: 0,
        currentStep: "idle",
        remissionData: null,
        pdfBlob: null,
    });

    const setProgress = useCallback((progress: number) => {
        setState((prev) => ({ ...prev, progress }));
    }, []);

    const generateRemission = useCallback(async (data: RemissionPreview) => {
        setState((prev) => ({
            ...prev,
            isGenerating: true,
            error: null,
            progress: 0,
            currentStep: "creating",
            remissionData: data,
        }));

        try {
            // Simular proceso de creación (esto sería la llamada real a la API)
            await new Promise((resolve) => setTimeout(resolve, 500));
            setState((prev) => ({
                ...prev,
                progress: 33,
                currentStep: "generating",
            }));

            // En un caso real, aquí iría la llamada a la API para generar el PDF
            // Por ahora simulamos el proceso
            await new Promise((resolve) => setTimeout(resolve, 1000));
            setState((prev) => ({ ...prev, progress: 66 }));

            // Simulación de obtención del blob PDF
            // En producción esto vendría de la respuesta de la API
            const mockBlob = new Blob(["mock pdf content"], {
                type: "application/pdf",
            });

            setState((prev) => ({
                ...prev,
                progress: 100,
                currentStep: "complete",
                isGenerating: false,
                pdfBlob: mockBlob,
            }));

            // Mostrar notificación de éxito
            addToast({
                title: "Éxito",
                description: `Remisión generada exitosamente - Folio: ${data.folio}`,
                color: "success",
            });
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "Error al generar la remisión";

            setState((prev) => ({
                ...prev,
                isGenerating: false,
                error: errorMessage,
                currentStep: "idle",
            }));

            addToast({
                title: "Error",
                description: `Error al generar la remisión: ${errorMessage}`,
                color: "danger",
            });
        }
    }, []);

    const downloadPDF = useCallback(async () => {
        if (!state.remissionData || !state.pdfBlob) {
            addToast({
                title: "Error",
                description: "No hay remisión disponible para descargar",
                color: "danger",
            });

            return;
        }

        setState((prev) => ({
            ...prev,
            isDownloading: true,
            currentStep: "downloading",
        }));

        try {
            // Usar la función del generador para descargar
            await downloadRemissionPDF(state.remissionData as any);

            addToast({
                title: "Éxito",
                description: `PDF descargado exitosamente: ${state.remissionData.folio}.pdf`,
                color: "success",
            });

            setState((prev) => ({ ...prev, isDownloading: false }));
        } catch (error) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "Error al descargar el PDF";

            setState((prev) => ({
                ...prev,
                isDownloading: false,
                error: errorMessage,
            }));

            addToast({
                title: "Error",
                description: `Error al descargar el PDF: ${errorMessage}`,
                color: "danger",
            });
        }
    }, [state.remissionData, state.pdfBlob]);

    const reset = useCallback(() => {
        setState({
            isGenerating: false,
            isDownloading: false,
            error: null,
            progress: 0,
            currentStep: "idle",
            remissionData: null,
            pdfBlob: null,
        });
    }, []);

    return {
        state,
        generateRemission,
        downloadPDF,
        reset,
        setProgress,
    };
}
