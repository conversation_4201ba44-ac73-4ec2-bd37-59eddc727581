"use client";

import React, { useMemo } from "react";
import { <PERSON><PERSON>, Tooltip, Chip } from "@heroui/react";
import {
    CubeIcon,
    SparklesIcon,
    ChartBarIcon,
    FireIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface Size {
    id: string;
    code: string;
    totalQuantity: number;
    size: {
        code: string;
    };
}

interface Garment {
    id: string;
    model?: {
        code: string;
        description?: string;
    };
    color?: {
        name: string;
        hexCode?: string;
    };
    sizes?: Size[];
}

interface OrderSizesPreviewProps {
    garments: Garment[];
    compact?: boolean;
}

export default function OrderSizesPreview({
    garments,
    compact = false,
}: OrderSizesPreviewProps) {
    // Calcular métricas para cada prenda
    const garmentMetrics = useMemo(() => {
        return garments.map((garment) => {
            const sizes = garment.sizes || [];
            const totalQuantity = sizes.reduce(
                (sum, size) => sum + size.totalQuantity,
                0,
            );

            // Encontrar la talla con mayor cantidad
            const popularSize = sizes.reduce(
                (prev, current) =>
                    current.totalQuantity > (prev?.totalQuantity || 0)
                        ? current
                        : prev,
                sizes[0],
            );

            // Calcular distribución para heat map
            const maxQuantity = Math.max(...sizes.map((s) => s.totalQuantity));

            return {
                ...garment,
                totalQuantity,
                popularSizeCode: popularSize?.size?.code || popularSize?.code,
                maxQuantity,
                sizesData: sizes.map((size) => ({
                    ...size,
                    intensity:
                        maxQuantity > 0 ? size.totalQuantity / maxQuantity : 0,
                })),
            };
        });
    }, [garments]);

    const getIntensityClass = (intensity: number) => {
        if (intensity >= 0.8) return "bg-primary/20 border-primary/60";
        if (intensity >= 0.6) return "bg-primary/15 border-primary/40";
        if (intensity >= 0.4) return "bg-primary/10 border-primary/30";
        if (intensity >= 0.2) return "bg-primary/5 border-primary/20";

        return "bg-default-100";
    };

    // Función para determinar el contraste del texto
    const getContrastColor = (hexColor: string): string => {
        const hex = hexColor.replace("#", "");
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        return luminance > 0.5 ? "text-gray-900" : "text-white";
    };

    if (!garments || garments.length === 0) {
        return (
            <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className="flex flex-col items-center justify-center p-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-900/50 rounded-2xl border border-dashed border-gray-300 dark:border-gray-700"
                initial={{ opacity: 0, scale: 0.95 }}
            >
                <CubeIcon className="w-12 h-12 text-gray-400 dark:text-gray-600 mb-4" />
                <p className="text-gray-600 dark:text-gray-400 text-center">
                    No hay prendas disponibles en esta orden
                </p>
            </motion.div>
        );
    }

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
        >
            <motion.div
                className="flex items-center gap-3 mb-4 p-4 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 dark:from-blue-400/20 dark:via-purple-400/20 dark:to-pink-400/20 backdrop-blur-sm rounded-xl"
                transition={{ type: "spring", stiffness: 400, damping: 30 }}
                whileHover={{ scale: 1.02 }}
            >
                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                    <CubeIcon className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                    <h4 className="text-base font-bold bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                        Detalle de Prendas y Tallas
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                        Visualización interactiva con métricas en tiempo real
                    </p>
                </div>
                <Badge
                    className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border-blue-500/30 dark:border-blue-400/30"
                    size="md"
                    variant="flat"
                >
                    <span className="font-bold">{garments.length}</span> items
                </Badge>
                <Tooltip content="Análisis avanzado de distribución">
                    <motion.div
                        animate={{ rotate: [0, 10, -10, 0] }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            repeatDelay: 3,
                        }}
                    >
                        <SparklesIcon className="w-5 h-5 text-yellow-500 dark:text-yellow-400" />
                    </motion.div>
                </Tooltip>
            </motion.div>

            {garmentMetrics.map((garment, index) => (
                <motion.div
                    key={garment.id}
                    animate={{ opacity: 1, y: 0 }}
                    className="relative overflow-hidden border border-white/50 dark:border-gray-700/50 rounded-2xl p-5 hover:shadow-2xl transition-all cursor-default bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
                    initial={{ opacity: 0, y: 10 }}
                    transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 30,
                        delay: index * 0.05,
                    }}
                    whileHover={{
                        scale: 1.02,
                        boxShadow: "0 20px 40px -15px rgba(0,0,0,0.3)",
                    }}
                >
                    {/* Gradient background decoration */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 dark:from-blue-400/10 dark:via-purple-400/10 dark:to-pink-400/10 pointer-events-none" />

                    {/* Header con modelo y color */}
                    <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                            <motion.div
                                className="relative"
                                transition={{ type: "spring", stiffness: 300 }}
                                whileHover={{ scale: 1.1, rotate: 360 }}
                            >
                                <div
                                    className="w-10 h-10 rounded-xl flex items-center justify-center text-sm font-bold shadow-lg border-2 border-white/50 dark:border-gray-700/50"
                                    style={{
                                        backgroundColor:
                                            garment.color?.hexCode || "#94a3b8",
                                    }}
                                >
                                    <span
                                        className={
                                            garment.color?.hexCode
                                                ? getContrastColor(
                                                      garment.color.hexCode,
                                                  )
                                                : "text-white"
                                        }
                                    >
                                        {garment.color?.name
                                            ?.charAt(0)
                                            .toUpperCase() || "?"}
                                    </span>
                                </div>
                                <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl blur opacity-25 group-hover:opacity-50 transition-opacity" />
                            </motion.div>
                            <div>
                                <div className="flex items-center gap-2">
                                    <Badge
                                        className="bg-gradient-to-r from-blue-500/20 to-purple-500/20"
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {garment.model?.code || "Sin código"}
                                    </Badge>
                                    <span className="text-xs text-gray-700 dark:text-gray-300 font-medium">
                                        {garment.model?.description || ""}
                                    </span>
                                </div>
                                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
                                    {garment.color?.name || "Sin color"}
                                </p>
                            </div>
                        </div>

                        <motion.div
                            className="text-right"
                            whileHover={{ scale: 1.05 }}
                        >
                            <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 dark:from-blue-400/20 dark:to-purple-400/20 rounded-xl p-3">
                                <p className="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                                    {garment.totalQuantity.toLocaleString()}
                                </p>
                                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                    unidades
                                </p>
                            </div>
                        </motion.div>
                    </div>

                    {/* Grid de tallas con heat map */}
                    {garment.sizesData && garment.sizesData.length > 0 && (
                        <div>
                            <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2">
                                    <ChartBarIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Distribución por tallas
                                    </p>
                                </div>
                                {garment.popularSizeCode && (
                                    <motion.div
                                        animate={{ scale: 1, opacity: 1 }}
                                        initial={{ scale: 0.8, opacity: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <Chip
                                            className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30 dark:border-yellow-400/30"
                                            size="sm"
                                            startContent={
                                                <FireIcon className="w-3 h-3" />
                                            }
                                            variant="flat"
                                        >
                                            <span className="font-medium">
                                                Talla {garment.popularSizeCode}
                                            </span>
                                        </Chip>
                                    </motion.div>
                                )}
                            </div>

                            <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 gap-2">
                                {garment.sizesData.map((sizeData) => (
                                    <Tooltip
                                        key={sizeData.id}
                                        content={
                                            <div className="text-xs">
                                                <p className="font-medium">
                                                    Talla{" "}
                                                    {sizeData.size?.code ||
                                                        sizeData.code}
                                                </p>
                                                <p>
                                                    {sizeData.totalQuantity}{" "}
                                                    unidades
                                                </p>
                                                <p>
                                                    {Math.round(
                                                        sizeData.intensity *
                                                            100,
                                                    )}
                                                    % del máximo
                                                </p>
                                            </div>
                                        }
                                    >
                                        <motion.div
                                            className={`
                                                relative p-3 rounded-xl border-2 text-center transition-all cursor-pointer backdrop-blur-sm
                                                ${getIntensityClass(sizeData.intensity)}
                                                hover:shadow-lg hover:border-primary/40
                                            `}
                                            transition={{
                                                type: "spring",
                                                stiffness: 400,
                                                damping: 25,
                                            }}
                                            whileHover={{ scale: 1.1, y: -4 }}
                                            whileTap={{ scale: 0.95 }}
                                        >
                                            {/* Badge para talla más popular */}
                                            {(sizeData.size?.code ||
                                                sizeData.code) ===
                                                garment.popularSizeCode && (
                                                <motion.div
                                                    animate={{ scale: 1 }}
                                                    className="absolute -top-2 -right-2 z-10"
                                                    initial={{ scale: 0 }}
                                                    transition={{
                                                        delay: 0.3,
                                                        type: "spring",
                                                    }}
                                                >
                                                    <div className="w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full shadow-lg flex items-center justify-center">
                                                        <FireIcon className="w-2.5 h-2.5 text-white" />
                                                    </div>
                                                </motion.div>
                                            )}

                                            <div className="text-xs font-bold text-gray-700 dark:text-gray-300">
                                                {sizeData.size?.code ||
                                                    sizeData.code}
                                            </div>
                                            <div className="font-black text-base mt-1 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                                                {sizeData.totalQuantity}
                                            </div>

                                            {/* Mini barra de intensidad con gradiente */}
                                            <div className="mt-2 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                                <motion.div
                                                    animate={{
                                                        width: `${sizeData.intensity * 100}%`,
                                                    }}
                                                    className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                                                    initial={{ width: 0 }}
                                                    transition={{
                                                        duration: 0.5,
                                                        delay: index * 0.05,
                                                    }}
                                                />
                                            </div>
                                        </motion.div>
                                    </Tooltip>
                                ))}
                            </div>

                            {/* Visualización de distribución mejorada */}
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                className="mt-4 p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-900/50 rounded-xl"
                                initial={{ opacity: 0, y: 10 }}
                                transition={{ delay: 0.3 }}
                            >
                                <div className="flex items-center gap-2 mb-2">
                                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                        Distribución visual:
                                    </span>
                                </div>
                                <div className="flex gap-0.5 h-6 rounded-lg overflow-hidden">
                                    {garment.sizesData.map((size, idx) => (
                                        <Tooltip
                                            key={idx}
                                            content={
                                                <div className="p-2">
                                                    <p className="font-bold">
                                                        {size.size?.code ||
                                                            size.code}
                                                    </p>
                                                    <p className="text-sm">
                                                        {size.totalQuantity}{" "}
                                                        unidades
                                                    </p>
                                                    <p className="text-xs text-gray-400">
                                                        {Math.round(
                                                            (size.totalQuantity /
                                                                garment.totalQuantity) *
                                                                100,
                                                        )}
                                                        % del total
                                                    </p>
                                                </div>
                                            }
                                        >
                                            <motion.div
                                                animate={{ scaleY: 1 }}
                                                className="bg-gradient-to-t from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 transition-all cursor-pointer"
                                                initial={{ scaleY: 0 }}
                                                style={{
                                                    width: `${(size.totalQuantity / garment.totalQuantity) * 100}%`,
                                                    opacity:
                                                        0.4 +
                                                        size.intensity * 0.6,
                                                }}
                                                transition={{
                                                    duration: 0.5,
                                                    delay: idx * 0.05,
                                                }}
                                                whileHover={{ opacity: 1 }}
                                            />
                                        </Tooltip>
                                    ))}
                                </div>
                            </motion.div>
                        </div>
                    )}
                </motion.div>
            ))}

            {/* Resumen total mejorado */}
            {garments.length > 1 && (
                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    className="mt-6 p-5 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 dark:from-blue-400/20 dark:via-purple-400/20 dark:to-pink-400/20 rounded-2xl border border-white/50 dark:border-gray-700/50 backdrop-blur-sm"
                    initial={{ opacity: 0, scale: 0.95 }}
                    transition={{ delay: 0.3, type: "spring", stiffness: 300 }}
                >
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                                <ChartBarIcon className="w-5 h-5 text-white" />
                            </div>
                            <div>
                                <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                    Resumen Total
                                </span>
                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                    {garments.length} prendas diferentes
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-4">
                            <motion.div
                                className="text-right"
                                whileHover={{ scale: 1.05 }}
                            >
                                <p className="text-3xl font-black bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                                    {garmentMetrics
                                        .reduce(
                                            (sum, g) => sum + g.totalQuantity,
                                            0,
                                        )
                                        .toLocaleString()}
                                </p>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                    unidades totales
                                </p>
                            </motion.div>
                        </div>
                    </div>
                </motion.div>
            )}
        </motion.div>
    );
}
