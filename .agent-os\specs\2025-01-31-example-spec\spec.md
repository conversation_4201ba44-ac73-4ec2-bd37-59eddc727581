# Spec: WebAuthn Authentication Enhancement

## Overview
Add WebAuthn (passwordless) authentication support to the existing NextAuth implementation, allowing users to login with biometrics or security keys.

## Requirements

### Functional Requirements
1. Users can register a WebAuthn credential during signup
2. Users can add WebAuthn credentials to existing accounts
3. Users can login using WebAuthn instead of password
4. Multiple credentials per user supported
5. Fallback to password authentication available

### Technical Requirements
1. Integrate with existing NextAuth setup
2. Store credentials in PostgreSQL via Prisma
3. Support both platform authenticators (Touch ID, Face ID) and roaming authenticators (YubiKey)
4. Maintain existing session management
5. Progressive enhancement - works without JavaScript

## Implementation Details

### Database Schema
```prisma
model Credencial {
  id                String   @id @default(cuid())
  user_id          String
  credential_id    String   @unique
  public_key       String
  counter          Int      @default(0)
  device_type      String
  backed_up        Boolean  @default(false)
  transports       String[]
  created_at       DateTime @default(now())
  last_used_at     DateTime?
  
  user             User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  
  @@map("credenciales")
}
```

### API Endpoints (Server Actions)
- `registerCredential()` - Start WebAuthn registration
- `verifyCredential()` - Complete registration
- `authenticateWithCredential()` - WebAuthn login
- `listUserCredentials()` - Show user's credentials
- `removeCredential()` - Delete a credential

### UI Components
- `WebAuthnButton` - Triggers WebAuthn flow
- `CredentialManager` - List and manage credentials
- `WebAuthnProvider` - Context for WebAuthn state

### Security Considerations
- Validate origin and RP ID
- Implement replay attack prevention
- Rate limit authentication attempts
- Secure credential storage
- HTTPS required

## Tasks

### Backend Tasks
1. [ ] Update Prisma schema with Credencial model
2. [ ] Create WebAuthn server actions
3. [ ] Implement credential validation logic
4. [ ] Update NextAuth configuration
5. [ ] Add WebAuthn to auth flow

### Frontend Tasks
1. [ ] Create WebAuthnButton component
2. [ ] Implement registration flow UI
3. [ ] Build credential management page
4. [ ] Add WebAuthn to login form
5. [ ] Handle browser compatibility

### Testing Tasks
1. [ ] Unit tests for credential validation
2. [ ] Integration tests for auth flow
3. [ ] E2E tests with Playwright
4. [ ] Test on multiple browsers/devices
5. [ ] Security audit

## Success Criteria
- Users can successfully register and login with WebAuthn
- No disruption to existing password authentication
- Works on 90% of modern browsers
- Credentials properly secured
- Clear error messages for unsupported browsers

## Timeline
- Estimated: 3-4 days
- Backend implementation: 1.5 days
- Frontend implementation: 1.5 days
- Testing and refinement: 1 day