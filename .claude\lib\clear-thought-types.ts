// Clear Thought Types for AUTO-FLOW V5.1
export type ClearThoughtTool = 
  | 'sequentialthinking'
  | 'mentalmodel'
  | 'designpattern'
  | 'programmingparadigm'
  | 'debuggingapproach'
  | 'collaborativereasoning'
  | 'decisionframework'
  | 'metacognitivemonitoring'
  | 'scientificmethod'
  | 'structuredargumentation'
  | 'visualreasoning';

export interface ToolConfig {
  triggers: string[];
  weight: number;
  contexts: string[];
  defaultModel?: string;
}

export interface ClearThoughtConfig {
  enabled: boolean;
  autoSelect: boolean;
  fallbackTool: ClearThoughtTool;
  confidence: {
    threshold: number;
    multiToolThreshold: number;
  };
  tools: Record<ClearThoughtTool, ToolConfig>;
}

export interface ToolMatch {
  tool: ClearThoughtTool;
  confidence: number;
  matchedTriggers: string[];
  context: string;
}

export interface ToolSelection {
  selectedTool: ClearThoughtTool;
  confidence: number;
  reason: string;
  alternatives: ToolMatch[];
}

export interface ToolMetric {
  tool: ClearThoughtTool;
  context: string;
  success: boolean;
  timeSpent: number;
  confidence: number;
  timestamp: number;
}