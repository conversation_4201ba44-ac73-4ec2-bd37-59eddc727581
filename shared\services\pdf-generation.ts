import html2canvas from "html2canvas";
import jsPDF from "jspdf";

/**
 * Genera un PDF a partir de un elemento HTML con alta calidad y fidelidad
 * @param elementId ID del elemento HTML a convertir en PDF
 * @param filename Nombre del archivo PDF a generar
 * @param options Opciones adicionales para personalizar la generación del PDF
 * @returns Promise<boolean> Indica si la generación fue exitosa
 */
export async function generatePDF(
    elementId: string,
    filename: string = "remision-asignacion.pdf",
    options: {
        openPdfAfterGeneration?: boolean;
        quality?: number; // 0.7 - 1.0
        scale?: number; // 1 - 4
        orientation?: "portrait" | "landscape";
        twoCopies?: boolean; // Generar dos copias en una página
    } = {},
) {
    // Opciones por defecto
    // Opciones por defecto optimizadas para impresión
    const {
        openPdfAfterGeneration = false,
        quality = 0.98, // Mayor calidad para impresión
        scale = 4, // Resolución muy alta para impresión nítida
        orientation = "portrait",
        twoCopies = false,
    } = options;

    try {
        // Obtener el elemento
        const element = document.getElementById(elementId);

        if (!element) {
            // REMOVED: console.error(`Elemento con ID ${elementId} no encontrado`);

            return false;
        }

        // Mostrar indicador de carga
        const loadingIndicator = document.createElement("div");

        loadingIndicator.style.position = "fixed";
        loadingIndicator.style.top = "0";
        loadingIndicator.style.left = "0";
        loadingIndicator.style.width = "100%";
        loadingIndicator.style.height = "100%";
        loadingIndicator.style.backgroundColor = "rgba(255, 255, 255, 0.8)";
        loadingIndicator.style.display = "flex";
        loadingIndicator.style.alignItems = "center";
        loadingIndicator.style.justifyContent = "center";
        loadingIndicator.style.zIndex = "9999";
        loadingIndicator.innerHTML = `
      <div style="background-color: #4338ca; color: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="display: flex; align-items: center; gap: 12px;">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="animation: spin 1s linear infinite;">
            <circle cx="12" cy="12" r="10" stroke="white" stroke-opacity="0.3" stroke-width="4"/>
            <path d="M12 2C6.47715 2 2 6.47715 2 12C2 12.7267 2.08428 13.4346 2.24571 14.1149" stroke="white" stroke-width="4" stroke-linecap="round"/>
          </svg>
          <span>Generando PDF de alta calidad...</span>
        </div>
      </div>
      <style>
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      </style>
    `;
        document.body.appendChild(loadingIndicator);

        // Preparar el elemento para optimizar el PDF
        element.classList.add("pdf-generating");

        // Pequeña pausa para asegurar que los estilos se apliquen
        await new Promise((resolve) => setTimeout(resolve, 300));

        try {
            // Crear canvas optimizado con configuración mejorada
            const canvas = await html2canvas(element, {
                scale: scale, // Mayor escala para mejor calidad
                useCORS: true, // Permitir imágenes de otros dominios
                logging: false, // Desactivar logs para producción
                backgroundColor: "#ffffff", // Fondo blanco
                windowWidth: element.scrollWidth,
                windowHeight: element.scrollHeight,
                allowTaint: true,
                imageTimeout: 15000, // Tiempo de espera para cargar imágenes
                onclone: (clonedDoc) => {
                    // Ajustar estilos del clon para optimizar para PDF
                    const clonedElement = clonedDoc.getElementById(elementId);

                    if (clonedElement) {
                        // Aplicar estilos específicos para PDF
                        clonedElement.style.padding = "10mm";

                        // Si estamos en modo horizontal, ajustar el ancho al de una hoja A4 landscape
                        if (orientation === "landscape") {
                            if (twoCopies) {
                                // Para dos copias, usar el ancho completo sin escalar aquí
                                clonedElement.style.width = "260mm";
                                clonedElement.style.maxWidth = "260mm";
                            } else {
                                clonedElement.style.width = "277mm"; // Ancho A4 landscape menos márgenes
                                clonedElement.style.maxWidth = "277mm";
                            }
                        } else {
                            clonedElement.style.width = "190mm"; // Ancho A4 portrait menos márgenes
                            clonedElement.style.maxWidth = "190mm";
                        }

                        clonedElement.style.margin = "0 auto";
                        clonedElement.style.borderRadius = "0";
                        clonedElement.style.boxShadow = "none";
                        clonedElement.style.fontSize = "10pt";
                        clonedElement.style.lineHeight = "1.3";

                        // Mejorar legibilidad del texto
                        const textElements = clonedElement.querySelectorAll(
                            "p, h1, h2, h3, h4, h5, h6, span, td, th",
                        );

                        textElements.forEach((el) => {
                            (el as HTMLElement).style.color = "#000000";
                            (el as HTMLElement).style.textRendering =
                                "optimizeLegibility";
                        });

                        // Asegurar que las tablas se vean correctamente
                        const tables = clonedElement.querySelectorAll("table");

                        tables.forEach((table) => {
                            table.style.width = "100%";
                            table.style.borderCollapse = "collapse";
                            table.style.pageBreakInside = "auto";

                            // Mejorar borders y espaciado de celdas
                            const cells = table.querySelectorAll("td, th");

                            cells.forEach((cell) => {
                                (cell as HTMLElement).style.padding = "8px";
                                (cell as HTMLElement).style.borderColor =
                                    "#ddd";
                            });
                        });
                    }
                },
            });

            // Configurar el PDF con orientación según parámetro
            const pdf = new jsPDF({
                orientation: orientation,
                unit: "mm",
                format: "a4",
                compress: true, // Comprimir el PDF
                hotfixes: ["px_scaling"], // Correcciones para problemas conocidos
            });

            // Calcular dimensiones para ajustar la imagen al PDF
            const imgData = canvas.toDataURL("image/jpeg", quality);
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();

            // Calcular relación de aspecto
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            const ratio = canvasWidth / canvasHeight;

            // Si necesitamos dos copias en una página
            if (twoCopies && orientation === "landscape") {
                // Configuramos para imprimir dos copias en una sola página horizontal
                const aspectRatio = canvasWidth / canvasHeight;

                // Márgenes más pequeños para aprovechar mejor el espacio
                const marginX = 7; // 7mm margen horizontal
                const marginY = 5; // 5mm margen vertical
                const middleGap = 5; // 5mm espacio entre copias

                // Área disponible para cada copia
                const availableWidth = pdfWidth - 2 * marginX;
                const availableHeight =
                    (pdfHeight - 2 * marginY - middleGap) / 2;

                // Calcular dimensiones óptimas manteniendo proporción
                let copyWidth = availableWidth;
                let copyHeight = copyWidth / aspectRatio;

                // Si es muy alto, ajustar por altura
                if (copyHeight > availableHeight) {
                    copyHeight = availableHeight;
                    copyWidth = copyHeight * aspectRatio;
                }

                // Centrar horizontalmente
                const xOffset = (pdfWidth - copyWidth) / 2;

                // Primera copia - Parte superior
                pdf.addImage(
                    imgData,
                    "JPEG",
                    xOffset,
                    marginY,
                    copyWidth,
                    copyHeight,
                    "copy1",
                    "MEDIUM", // Mejor calidad
                    0,
                );

                // Segunda copia - Parte inferior
                const secondCopyY = marginY + copyHeight + middleGap;

                pdf.addImage(
                    imgData,
                    "JPEG",
                    xOffset,
                    secondCopyY,
                    copyWidth,
                    copyHeight,
                    "copy2",
                    "MEDIUM", // Mejor calidad
                    0,
                );

                // Línea divisoria punteada más sutil
                pdf.setDrawColor(200, 200, 200);
                pdf.setLineWidth(0.2);
                pdf.setLineDashPattern([3, 3], 0);
                const lineY = marginY + copyHeight + middleGap / 2;

                pdf.line(marginX, lineY, pdfWidth - marginX, lineY);

                // Indicadores de corte opcionales en las esquinas
                pdf.setDrawColor(150, 150, 150);
                pdf.setLineWidth(0.1);
                pdf.setLineDashPattern([], 0); // Línea sólida
                const cutMarkLength = 5;

                // Marcas de corte para la línea divisoria
                pdf.line(0, lineY, cutMarkLength, lineY); // Izquierda
                pdf.line(pdfWidth - cutMarkLength, lineY, pdfWidth, lineY); // Derecha
            } else {
                // Para una sola copia, determinar las dimensiones optimizadas para el PDF
                const imgWidth = pdfWidth - 20; // Margen de 10mm en cada lado
                const imgHeight = imgWidth / ratio;

                // Si la altura es mayor que la página, ajustar a múltiples páginas
                if (imgHeight > pdfHeight - 20) {
                    // Margen de 10mm en cada lado (superior e inferior)
                    // Para documentos largos, mejor dividir en múltiples páginas
                    // que comprimir todo en una página
                    const contentHeight = canvasHeight / scale;
                    const pageHeight =
                        ((pdfHeight - 20) * (canvasWidth / scale)) / imgWidth;
                    const totalPages = Math.ceil(contentHeight / pageHeight);

                    for (let i = 0; i < totalPages; i++) {
                        if (i > 0) {
                            pdf.addPage();
                        }

                        // Posición de inicio para esta página
                        const sourceY = i * pageHeight * scale;
                        const sourceHeight = Math.min(
                            pageHeight * scale,
                            canvasHeight - sourceY,
                        );

                        if (sourceHeight <= 0) continue;

                        // Ajustar las dimensiones proporcionalmente
                        const pageImgHeight =
                            (sourceHeight / scale) *
                            (imgWidth / (canvasWidth / scale));

                        // Añadir imagen del canvas al PDF con recorte para múltiples páginas
                        // Usamos el enfoque tradicional para compatibilidad
                        pdf.addImage(
                            imgData, // datos de la imagen
                            "JPEG", // formato
                            10, // x - margen izquierdo
                            10, // y - margen superior
                            imgWidth, // ancho
                            pageImgHeight, // alto
                            `page_${i}`, // alias único para cada página
                            "FAST", // compresión
                            0, // rotación
                        );
                    }
                } else {
                    // Para documentos cortos, centrar en la página
                    const yOffset = (pdfHeight - imgHeight) / 2;

                    // Añadir imagen del canvas al PDF (documento completo)
                    pdf.addImage(
                        imgData, // datos de la imagen
                        "JPEG", // formato
                        10, // x - margen izquierdo
                        yOffset, // y - posición vertical
                        imgWidth, // ancho
                        imgHeight, // alto
                        undefined, // alias
                        "FAST", // compresión
                    );
                }
            }

            // Añadir metadatos al PDF
            pdf.setProperties({
                title: filename.replace(".pdf", ""),
                subject: "Remisión de asignación",
                author: "Lohari Textiles",
                keywords: "remision, asignacion, lohari, textiles",
                creator: "Lohari App",
            });

            // Guardar el PDF
            pdf.save(filename);

            // Opcionalmente abrir el PDF después de guardar
            if (openPdfAfterGeneration) {
                const blob = pdf.output("blob");
                const url = URL.createObjectURL(blob);

                window.open(url, "_blank");
            }

            // REMOVED: console.log("PDF generado con éxito");

            return true;
        } finally {
            // Restaurar el elemento a su estado original
            element.classList.remove("pdf-generating");

            // Eliminar el indicador de carga
            if (document.body.contains(loadingIndicator)) {
                document.body.removeChild(loadingIndicator);
            }
        }
    } catch (error) {
        // REMOVED: console.error("Error al generar PDF:", error);

        // Asegurarse de quitar la clase temporal en caso de error
        const element = document.getElementById(elementId);

        if (element) {
            element.classList.remove("pdf-generating");
        }

        // Mostrar mensaje de error al usuario
        alert(
            "Ocurrió un error al generar el PDF. Por favor, inténtelo de nuevo.",
        );

        return false;
    }
}

/**
 * Genera un número de remisión único con formato LOH-YYYYMMDD-XXXX-Z
 * @returns string Número de folio formateado
 */
export function generateRemissionNumber(): string {
    const prefix = "LOH";
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const dateStr = `${year}${month}${day}`;

    // Secuencia numérica (debe ser actualizada desde base de datos en producción)
    const sequence = Math.floor(Math.random() * 9999)
        .toString()
        .padStart(4, "0");

    // Checksum simple (implementación básica, reemplazar por algoritmo real)
    const baseString = `${prefix}-${dateStr}-${sequence}`;
    const checksum = calculateChecksum(baseString);

    return `${baseString}-${checksum}`;
}

/**
 * Calcula un dígito de verificación simple para un string
 * @param input String para calcular el checksum
 * @returns string Dígito de checksum (0-9)
 */
function calculateChecksum(input: string): string {
    // Implementación simple de checksum - sumar códigos ASCII y obtener módulo 10
    const sum = input
        .split("")
        .reduce((acc, char) => acc + char.charCodeAt(0), 0);

    return (sum % 10).toString();
}
