"use client";

import type { SWRConfiguration } from "swr";
import type { Contractor } from "../types";

import useSWR from "swr";
import { useState } from "react";

import {
    getContractors,
    getContractor,
    getContractorsForFilter,
    createContractor as create,
    updateContractor as update,
    deleteContractor as remove,
} from "@/features/contractors/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

export interface ContractorsResponse {
    contractors: Contractor[];
    pagination?: {
        total: number;
        currentPage: number;
        lastPage: number;
    };
}

// Obtener helpers de revalidación para el cliente
const { revalidateData, useRevalidationListener } =
    createClientRevalidation("contractor");

/**
 * Hook para obtener todos los contratistas
 * @param options Opciones de filtrado y paginación
 * @param config Configuración opcional para SWR
 * @returns Objeto con la lista de contratistas, paginación, estado de carga y errores
 */
export function useContractors(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
    config?: SWRConfiguration,
) {
    // Convertir opciones a cadena para clave de caché
    const optionsKey = JSON.stringify(options);

    // Escuchar eventos de revalidación
    const isRevalidating = useRevalidationListener();

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        ["contractors", optionsKey],
        async () => getContractors(options),
        config,
    );

    return {
        contractors: data?.data || [],
        pagination: data?.pagination,
        isLoading,
        isRevalidating,
        isError: !!error || data?.success === false,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para obtener contratistas para filtros (sin paginación)
 * @param config Configuración opcional para SWR
 * @returns Lista de contratistas para usar en dropdowns/filtros
 */
export function useContractorsForFilter(config?: SWRConfiguration) {
    // Configuración optimizada para filtros
    const options: SWRConfiguration = {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        refreshInterval: 0,
        dedupingInterval: 300000, // 5 minutos
        ...config,
    };

    // Utilizar Server Action como fetcher
    const { data, error, isLoading } = useSWR(
        "contractors-filter",
        async () => getContractorsForFilter(),
        options,
    );

    return {
        contractors: data || [],
        isLoading,
        isError: !!error,
        error,
    };
}

/**
 * Hook para obtener la información de un contratista por su ID
 * @param id ID del contratista a cargar
 * @param config Configuración opcional para SWR
 * @returns Objeto con los datos del contratista, estado de carga y errores
 */
export function useContractor(
    id: string | null | undefined,
    config?: SWRConfiguration,
) {
    // Escuchar eventos de revalidación
    const isRevalidating = useRevalidationListener(id || undefined);

    // Utilizar Server Action como fetcher
    const { data, error, isLoading, mutate } = useSWR(
        id ? ["contractor", id] : null,
        async () => (id ? getContractor(id) : null),
        config,
    );

    // Verificar explícitamente si data tiene la propiedad 'data'
    const contractorData = data && "data" in data ? data.data : null;

    return {
        contractor: contractorData,
        isLoading,
        isRevalidating,
        isError: !!error || data?.success === false,
        error: data?.error || error,
        mutate,
    };
}

/**
 * Hook para crear un nuevo contratista
 * @returns Función para crear un contratista y estado de la operación
 */
export function useCreateContractor() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const createContractor = async (data: {
        name: string;
        firstName?: string;
        lastName?: string;
        phone?: string;
        email?: string;
    }) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await create(data as any);

            if (result.success) {
                // Revalidar datos mediante la función genérica
                await revalidateData((result.data as any)?.id);
            } else {
                setError(result.error || "Error al crear contratista");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return { createContractor, isLoading, error };
}

/**
 * Hook para actualizar un contratista
 * @returns Función para actualizar un contratista y estado de la operación
 */
export function useUpdateContractor() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const updateContractor = async (
        id: string,
        data: {
            name?: string;
            firstName?: string;
            lastName?: string;
            phone?: string;
            email?: string;
        },
    ) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await update(id, data);

            if (result.success) {
                // Revalidar datos mediante la función genérica
                await revalidateData(id);
            } else {
                setError(result.error || "Error al actualizar contratista");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return { updateContractor, isLoading, error };
}

/**
 * Hook para eliminar un contratista
 * @returns Función para eliminar un contratista y estado de la operación
 */
export function useDeleteContractor() {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const deleteContractor = async (id: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await remove(id);

            if (result.success) {
                // Revalidar datos mediante la función genérica
                await revalidateData();
            } else {
                setError(result.error || "Error al eliminar contratista");
            }

            return result;
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : "Error desconocido";

            setError(errorMessage);

            return { success: false, error: errorMessage };
        } finally {
            setIsLoading(false);
        }
    };

    return { deleteContractor, isLoading, error };
}
