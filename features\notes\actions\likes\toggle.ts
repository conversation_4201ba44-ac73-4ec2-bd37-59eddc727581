"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/utils/prisma-helpers";

const toggleLikeSchema = z.object({
    noteId: z.string().min(1, "El ID de la nota es requerido"),
});

type ToggleLikeInput = z.infer<typeof toggleLikeSchema>;

interface ToggleLikeResult {
    success: boolean;
    liked?: boolean;
    likesCount?: number;
    error?: string;
}

/**
 * Alterna el estado de like en una nota
 */
export async function toggleNoteLike(
    input: ToggleLikeInput,
): Promise<ToggleLikeResult> {
    try {
        // Validar sesión
        const session = await auth();
        const userId = session?.user?.id;

        if (!userId) {
            return { success: false, error: "No autorizado" };
        }

        // Validar input
        const validatedData = toggleLikeSchema.parse(input);

        // Verificar que la nota existe
        const noteExists = await db.note.findUnique({
            where: { id: validatedData.noteId },
            select: { id: true, orderId: true },
        });

        if (!noteExists) {
            return { success: false, error: "La nota no existe" };
        }

        // Buscar si ya existe un like del usuario para esta nota
        const existingLike = await db.noteLike.findUnique({
            where: {
                noteId_userId: {
                    noteId: validatedData.noteId,
                    userId: userId,
                },
            },
        });

        let liked = false;

        if (existingLike) {
            // Si existe, eliminar el like
            await db.noteLike.delete({
                where: { id: existingLike.id },
            });
            liked = false;
        } else {
            // Si no existe, crear el like
            await db.noteLike.create({
                data: {
                    noteId: validatedData.noteId,
                    userId: userId,
                },
            });
            liked = true;
        }

        // Contar el total de likes
        const likesCount = await db.noteLike.count({
            where: { noteId: validatedData.noteId },
        });

        // Revalidar las rutas afectadas
        revalidatePath(`/dashboard/orders/${noteExists.orderId}`);
        revalidatePath("/dashboard/notes");

        return {
            success: true,
            liked,
            likesCount,
        };
    } catch (error) {
        return handlePrismaError(error, "Error al procesar el like");
    }
}
