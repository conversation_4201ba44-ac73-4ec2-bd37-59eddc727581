"use server";

import { db } from "@/lib/db";
import { auth } from "@/lib/auth-helpers";

// Size order mapping for proper sorting
const SIZE_ORDER_MAP: Record<string, number> = {
    XS: 1,
    S: 2,
    M: 3,
    L: 4,
    XL: 5,
    "2XL": 6,
    XXL: 6,
    "3XL": 7,
    XXXL: 7,
    // Add more sizes as needed
};

function getSizeOrder(sizeCode: string): number {
    return SIZE_ORDER_MAP[sizeCode.toUpperCase()] || 99; // Default to 99 for unknown sizes
}

export interface OrderProduct {
    orderId: string;
    orderNumber: string;
    cutOrder: string | null;
    modelCode: string;
    modelDescription: string;
    colorName: string;
    partNumber: string | null;
    sizes: {
        sizeCode: string;
        sizeOrder: number;
        garmentSizeId: string;
        totalQuantity: number;
        availableQuantity: number;
        usedQuantity: number; // Ahora representa cantidad ya empacada
    }[];
    totalAvailable: number;
}

export interface OrderForPacking {
    id: string;
    displayName: string;
    transferNumber: string | null;
    cutOrder: string | null;
    batch: string | null;
    customer: {
        id: string;
        name: string;
    };
    products: OrderProduct[];
    totalProducts: number;
    totalAvailableQuantity: number;
}

export async function getOrdersForPacking(customerId?: string) {
    try {
        // Verificación de autenticación simplificada
        try {
            const session = await auth();

            console.log(
                "🔐 Verificación de sesión:",
                session?.user?.email || "Sin sesión",
            );
            // Por ahora, solo verificar que hay una sesión, no necesariamente un ID
            if (!session) {
                console.log("❌ No hay sesión activa");

                return {
                    success: false,
                    error: "No autorizado - Por favor inicie sesión",
                    data: [],
                    debug: null,
                };
            }
        } catch (authError) {
            console.error("❌ Error al verificar autenticación:", authError);
            // Continuar sin autenticación por ahora debido al error de JWT
            console.log(
                "⚠️ Continuando sin verificación de autenticación debido a error de JWT",
            );
        }

        // Primero contar total de órdenes en la BD
        const totalOrdersCount = await db.order.count();

        console.log(
            "📊 Total de órdenes en la base de datos:",
            totalOrdersCount,
        );

        // Construir filtros
        const where: any = {
            // We'll filter orders with available products after fetching
        };

        if (customerId) {
            where.customerId = customerId;
            console.log("🔍 Filtrando por customerId:", customerId);
        } else {
            console.log(
                "🌍 Sin filtro de cliente - obteniendo TODAS las órdenes",
            );
        }

        // Primero intentar una consulta simple
        const simpleOrders = await db.order.findMany({
            where,
            select: {
                id: true,
                cutOrder: true,
                transferNumber: true,
                customerId: true,
            },
        });

        console.log("🔍 Órdenes simples encontradas:", simpleOrders.length);

        // Obtener órdenes con todos los detalles necesarios
        const orders = await db.order.findMany({
            where,
            include: {
                customer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                                packingDetails: {
                                    where: {
                                        packing: {
                                            status: {
                                                name: {
                                                    notIn: [
                                                        "CANCELLED",
                                                        "CANCELADO",
                                                    ],
                                                },
                                            },
                                        },
                                    },
                                    select: {
                                        quantity: true,
                                        packing: {
                                            select: {
                                                folio: true,
                                                status: {
                                                    select: {
                                                        name: true,
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                            orderBy: {
                                size: {
                                    code: "asc",
                                },
                            },
                        },
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        console.log("📊 Total órdenes obtenidas:", orders.length);
        console.log("🔍 CustomerId filtro:", customerId || "Sin filtro");

        if (orders.length > 0) {
            console.log("📦 Primera orden:", {
                id: orders[0].id,
                customerId: orders[0].customerId,
                customerName: orders[0].customer.name,
                garmentsCount: orders[0].garments.length,
                cutOrder: orders[0].cutOrder,
                transferNumber: orders[0].transferNumber,
            });

            // Mostrar todos los customerIds únicos encontrados
            const uniqueCustomerIds = [
                ...new Set(orders.map((o) => o.customerId)),
            ];

            console.log(
                "🏢 CustomerIds únicos encontrados:",
                uniqueCustomerIds,
            );

            // Mostrar nombres de clientes únicos
            const uniqueCustomers = [
                ...new Set(
                    orders.map((o) => `${o.customer.name} (${o.customerId})`),
                ),
            ];

            console.log("👥 Clientes únicos:", uniqueCustomers);

            // Contar órdenes por cliente
            const ordersByCustomer = orders.reduce(
                (acc, order) => {
                    acc[order.customer.name] =
                        (acc[order.customer.name] || 0) + 1;

                    return acc;
                },
                {} as Record<string, number>,
            );

            console.log("📊 Órdenes por cliente:", ordersByCustomer);

            if (customerId && !uniqueCustomerIds.includes(customerId)) {
                console.log(
                    "⚠️ El customerId filtro NO coincide con ninguna orden!",
                );
            }
        } else {
            console.log("❌ No se encontraron órdenes en la base de datos");
            console.log("🔍 Verificando si hay datos en la tabla Order...");
        }

        // Transformar a formato agrupado
        const ordersForPacking: OrderForPacking[] = orders.map((order) => {
            const products: OrderProduct[] = [];

            // Agrupar por modelo + color + partida
            const productGroups = new Map<string, OrderProduct>();

            for (const garment of order.garments) {
                // NO filtrar por disponibilidad - mostrar todas las tallas
                const allSizes = garment.sizes;

                // Log para debug
                if (order === orders[0] && allSizes.length > 0) {
                    console.log(
                        `📦 Orden ${order.cutOrder || order.id} - ${order.customer.name}:`,
                    );
                    console.log(
                        `   Partidas: ${order.parts.map((p) => p.code).join(", ") || "Sin partidas"}`,
                    );
                    allSizes.forEach((size) => {
                        // Calcular cantidad ya empacada
                        const packedQuantity =
                            size.packingDetails?.reduce(
                                (sum, pd) => sum + pd.quantity,
                                0,
                            ) || 0;
                        const availableForPacking =
                            size.totalQuantity - packedQuantity;
                        const availableForAssignment =
                            size.totalQuantity - size.usedQuantity;

                        console.log(`   Talla ${size.size.code}:`);
                        console.log(`     - Total: ${size.totalQuantity}`);
                        console.log(
                            `     - Asignado a contratistas: ${size.usedQuantity}`,
                        );
                        console.log(`     - Ya empacado: ${packedQuantity}`);
                        console.log(
                            `     - Disponible para packing: ${availableForPacking} ${availableForPacking > 0 ? "✅" : "❌"}`,
                        );
                        console.log(
                            `     - Disponible para asignar: ${availableForAssignment}`,
                        );
                    });
                }

                if (allSizes.length === 0) continue; // Skip si no hay tallas

                // Si hay partidas, crear un grupo por cada partida
                if (order.parts.length > 0) {
                    // Dividir las cantidades entre las partidas de manera equitativa
                    const partsCount = order.parts.length;

                    for (const part of order.parts) {
                        const groupKey = `${garment.model.code}-${garment.color.name}-${part.code}`;

                        if (!productGroups.has(groupKey)) {
                            productGroups.set(groupKey, {
                                orderId: order.id,
                                orderNumber:
                                    order.cutOrder ||
                                    order.transferNumber ||
                                    order.id,
                                cutOrder: order.cutOrder,
                                modelCode: garment.model.code,
                                modelDescription:
                                    garment.model.description || "",
                                colorName: garment.color.name,
                                partNumber: part.code,
                                sizes: [],
                                totalAvailable: 0,
                            });
                        }

                        const product = productGroups.get(groupKey)!;

                        // Agregar las tallas con cantidades divididas entre partidas
                        for (const size of allSizes) {
                            // Verificar si ya agregamos esta talla para esta partida
                            const existingSize = product.sizes.find(
                                (s) => s.garmentSizeId === size.id,
                            );

                            if (!existingSize) {
                                // Calcular cantidad ya empacada
                                const packedQuantity =
                                    size.packingDetails?.reduce(
                                        (sum, pd) => sum + pd.quantity,
                                        0,
                                    ) || 0;
                                const availableQuantity =
                                    size.totalQuantity - packedQuantity;

                                // Por ahora, mostrar la cantidad total para cada partida
                                // En el futuro, podrías implementar lógica para dividir entre partidas
                                product.sizes.push({
                                    sizeCode: size.size.code,
                                    sizeOrder: getSizeOrder(size.size.code),
                                    garmentSizeId: size.id,
                                    totalQuantity: size.totalQuantity,
                                    availableQuantity,
                                    usedQuantity: packedQuantity,
                                });

                                // Solo sumar una vez al total disponible por partida
                                product.totalAvailable += Math.floor(
                                    availableQuantity / partsCount,
                                );
                            }
                        }
                    }
                } else {
                    // Si no hay partidas, crear un solo grupo sin partida
                    const groupKey = `${garment.model.code}-${garment.color.name}-sin-partida`;

                    if (!productGroups.has(groupKey)) {
                        productGroups.set(groupKey, {
                            orderId: order.id,
                            orderNumber:
                                order.cutOrder ||
                                order.transferNumber ||
                                order.id,
                            cutOrder: order.cutOrder,
                            modelCode: garment.model.code,
                            modelDescription: garment.model.description || "",
                            colorName: garment.color.name,
                            partNumber: null,
                            sizes: [],
                            totalAvailable: 0,
                        });
                    }

                    const product = productGroups.get(groupKey)!;

                    // Agregar TODAS las tallas al grupo (disponibles o no)
                    for (const size of allSizes) {
                        // Calcular cantidad ya empacada (NO usar usedQuantity que es para asignaciones)
                        const packedQuantity =
                            size.packingDetails?.reduce(
                                (sum, pd) => sum + pd.quantity,
                                0,
                            ) || 0;
                        const availableQuantity =
                            size.totalQuantity - packedQuantity;

                        product.sizes.push({
                            sizeCode: size.size.code,
                            sizeOrder: getSizeOrder(size.size.code),
                            garmentSizeId: size.id,
                            totalQuantity: size.totalQuantity,
                            availableQuantity,
                            usedQuantity: packedQuantity, // Cambiar esto para mostrar cantidad empacada
                        });

                        product.totalAvailable += availableQuantity;
                    }
                }
            }

            // Convertir map a array y ordenar
            products.push(
                ...Array.from(productGroups.values())
                    .map((product) => {
                        // Sort sizes within each product
                        product.sizes.sort((a, b) => a.sizeOrder - b.sizeOrder);

                        return product;
                    })
                    .sort((a, b) => {
                        // Ordenar por modelo, luego color, luego partida
                        if (a.modelCode !== b.modelCode)
                            return a.modelCode.localeCompare(b.modelCode);
                        if (a.colorName !== b.colorName)
                            return a.colorName.localeCompare(b.colorName);

                        return (a.partNumber || "").localeCompare(
                            b.partNumber || "",
                        );
                    }),
            );

            const totalAvailableQuantity = products.reduce(
                (sum, p) => sum + p.totalAvailable,
                0,
            );

            return {
                id: order.id,
                displayName: `${order.cutOrder || order.transferNumber || order.id} - ${order.customer.name}`,
                transferNumber: order.transferNumber,
                cutOrder: order.cutOrder,
                batch: order.batch,
                customer: {
                    id: order.customer.id,
                    name: order.customer.name,
                },
                products,
                totalProducts: products.length,
                totalAvailableQuantity,
            };
        }); // REMOVIDO: .filter(order => order.totalAvailableQuantity > 0) - Mostrar TODAS las órdenes

        console.log("📊 Total órdenes procesadas:", ordersForPacking.length);
        console.log(
            "✅ Órdenes con cantidad disponible:",
            ordersForPacking.filter((o) => o.totalAvailableQuantity > 0).length,
        );
        console.log(
            "❌ Órdenes sin cantidad disponible:",
            ordersForPacking.filter((o) => o.totalAvailableQuantity === 0)
                .length,
        );

        // Mostrar detalles de todas las órdenes
        console.log("🔍 Detalle de todas las órdenes:");
        ordersForPacking.forEach((order) => {
            console.log(`   📦 ${order.displayName}:`);
            console.log(`      - ID: ${order.id}`);
            console.log(`      - Cliente: ${order.customer.name}`);
            console.log(`      - Productos: ${order.totalProducts}`);
            console.log(
                `      - Cantidad disponible total: ${order.totalAvailableQuantity}`,
            );
            if (order.products.length > 0) {
                console.log(`      - Productos desglosados:`);
                order.products.forEach((product, idx) => {
                    console.log(
                        `        ${idx + 1}. ${product.modelCode} - ${product.colorName} - Partida: ${product.partNumber || "Sin partida"}`,
                    );
                    console.log(
                        `           Tallas: ${product.sizes.map((s) => `${s.sizeCode}(${s.availableQuantity})`).join(", ")}`,
                    );
                });
            }
        });

        let debugInfo = null;

        if (ordersForPacking.length === 0) {
            console.log("⚠️ No se encontraron órdenes");

            debugInfo = {
                reason: "no_orders",
                message: customerId
                    ? "No se encontraron órdenes para este cliente"
                    : "No hay órdenes en el sistema",
                customerId,
            };
        }

        return {
            success: true,
            data: ordersForPacking,
            debug: debugInfo,
        };
    } catch (error) {
        console.error("Error getting orders for packing:", error);

        return {
            success: false,
            error: "Error al obtener las órdenes disponibles",
            data: [],
            debug: null,
        };
    }
}

export interface CreatePackingFromOrdersInput {
    customerId: string;
    subCustomerId?: string;
    deliveryDate: Date;
    notes?: string;
    transportNotes?: string;
    selectedProducts: {
        orderId: string;
        productGroups: {
            modelCode: string;
            colorName: string;
            partNumber: string | null;
            qualityDistribution: {
                garmentSizeId: string;
                sizeCode: string;
                primera: number;
                segunda: number;
                manchada: number;
                incompleta: number;
            }[];
        }[];
    }[];
}

export async function validateOrderSelections(
    input: CreatePackingFromOrdersInput,
) {
    try {
        const errors: string[] = [];

        // Validar disponibilidad de cada selección
        for (const orderSelection of input.selectedProducts) {
            for (const productGroup of orderSelection.productGroups) {
                for (const sizeDistribution of productGroup.qualityDistribution) {
                    const garmentSize = await db.garmentSize.findUnique({
                        where: { id: sizeDistribution.garmentSizeId },
                        include: {
                            garment: {
                                include: {
                                    model: true,
                                    color: true,
                                },
                            },
                            size: true,
                            packingDetails: {
                                where: {
                                    packing: {
                                        statusId: {
                                            notIn: ["CANCELLED"],
                                        },
                                    },
                                },
                                select: {
                                    quantity: true,
                                },
                            },
                        },
                    });

                    if (!garmentSize) {
                        errors.push(
                            `Producto no encontrado: ${sizeDistribution.garmentSizeId}`,
                        );
                        continue;
                    }

                    const totalRequested =
                        sizeDistribution.primera +
                        sizeDistribution.segunda +
                        sizeDistribution.manchada +
                        sizeDistribution.incompleta;

                    // Calcular cantidad ya empacada
                    const packedQuantity =
                        garmentSize.packingDetails?.reduce(
                            (sum, pd) => sum + pd.quantity,
                            0,
                        ) || 0;
                    const availableQuantity =
                        garmentSize.totalQuantity - packedQuantity;

                    if (totalRequested > availableQuantity) {
                        errors.push(
                            `Cantidad insuficiente para ${garmentSize.garment.model.code} ${garmentSize.garment.color.name} ` +
                                `talla ${garmentSize.size.code}. Solicitado: ${totalRequested}, Disponible: ${availableQuantity} ` +
                                `(Total: ${garmentSize.totalQuantity}, Ya empacado: ${packedQuantity})`,
                        );
                    }
                }
            }
        }

        return {
            success: errors.length === 0,
            errors,
        };
    } catch (error) {
        console.error("Error validating order selections:", error);

        return {
            success: false,
            errors: ["Error al validar las selecciones"],
        };
    }
}
