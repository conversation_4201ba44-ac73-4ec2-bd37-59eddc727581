// auth.ts

import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";

// Importación del verifyPassword desde server-utils
import { prisma } from "@/shared/lib/prisma";
import { verifyPassword } from "@/shared/utils/server";

// Comentado module declaration conflictivo
// declare module "next-auth" {
//     interface Session {
//         user: any;
//     }
//     interface User {
//         role: string;
//         remember?: boolean;
//     }
// }

// Tipo personalizado para nuestro usuario autenticado
interface CustomUser {
    id: string;
    email: string;
    name: string;
    role: string;
    image?: string;
    remember: boolean;
}

// Constantes para tiempos de sesión
const REGULAR_SESSION_TIME = 24 * 60 * 60; // 24 horas en segundos
const REMEMBER_ME_SESSION_TIME = 30 * 24 * 60 * 60; // 30 días en segundos

// Configuración simplificada de NextAuth
export const authOptions = {
    // Usamos una aserción de tipo para resolver problemas de compatibilidad
    adapter: PrismaAdapter(prisma) as any,
    providers: [
        CredentialsProvider({
            name: "credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                remember: { label: "Remember me", type: "checkbox" },
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }

                try {
                    const user = await prisma.user.findUnique({
                        where: { email: credentials.email as string },
                    });

                    if (!user?.password) return null;

                    const isValid = await verifyPassword(
                        credentials.password as string,
                        user.password,
                    );

                    if (!isValid) return null;

                    // Añadimos el remember a los datos del usuario para usarlo en el callback jwt
                    return {
                        id: user.id,
                        email: user.email,
                        name: user.name || "",
                        role: user.roleId,
                        image: user.image || "",
                        remember: credentials.remember === "true",
                    } as any;
                } catch (error) {
                    // REMOVED: console.error("Auth error:", error);

                    return null;
                }
            },
        }),
    ],
    callbacks: {
        async jwt({ token, user }: { token: any; user: any }) {
            if (user) {
                token.role = user.role;
                token.id = user.id;
                // Si el usuario marcó "recuérdame", extendemos la duración del token
                if (user.remember) {
                    token.exp =
                        Math.floor(Date.now() / 1000) +
                        REMEMBER_ME_SESSION_TIME;
                }
            }

            return token;
        },
        async session({ session, token }: { session: any; token: any }) {
            if (session?.user) {
                session.user.id = token.id as string;
                session.user.role = token.role as string;
            }

            return session;
        },
    },
    pages: {
        signIn: "/login",
        error: "/error",
    },
    session: {
        strategy: "jwt" as const,
        maxAge: REGULAR_SESSION_TIME,
        updateAge: 60 * 60,
    },
    debug: process.env.NODE_ENV === "development",
};

// Aplicamos una aserción de tipo a toda la configuración para evitar errores de TypeScript
export const { handlers, auth, signIn, signOut } = NextAuth(authOptions as any);
