# Spec Requirements Document

> Spec: Completar Gestión de Subclientes 
> Created: 2025-08-02
> Status: Planning

## Overview

Completar la implementación parcial de gestión de subclientes en el módulo de clientes. El backend está 70% implementado (crear, listar, mover en jerarquía) pero falta la integración UI completa en las páginas del dashboard y las operaciones de actualización/eliminación con validaciones de dependencias. Esta funcionalidad permitirá organizar clientes como "Becktel" con sus marcas "Reebok" y "Cotton" como subclientes.

## Current Implementation Status

### ✅ YA IMPLEMENTADO
- **Backend CRUD parcial**: create-subcustomer.ts, get-subcustomers.ts, update-hierarchy.ts
- **Componentes UI**: CustomerHierarchy.tsx, SubCustomerList.tsx, CustomerSelector.tsx
- **Base de datos**: Schema Prisma con parentId, subCustomers[], displayName
- **Validaciones**: Prevención de referencias circulares, verificación de padres existentes

### ❌ FALTA IMPLEMENTAR
- **Integración UI**: Tab "Subclientes" en página de detalles del cliente
- **Operaciones faltantes**: Actualizar y eliminar subclientes
- **Validaciones avanzadas**: Verificar dependencias (orders/packings) antes de eliminar
- **Indicadores visuales**: Mostrar jerarquía en lista principal de clientes
- **Modales CRUD**: Formularios para crear/editar subclientes

## User Stories

### Gestión Completa de Subclientes

Como administrador del sistema, quiero poder gestionar completamente los subclientes desde la página de detalles del cliente principal, para tener control total sobre la jerarquía "Becktel → Reebok" y "Becktel → Cotton".

El sistema debe mostrar un tab "Subclientes" en `/dashboard/customers/[id]/details` que liste todos los subclientes, permita crear nuevos, editar existentes, y eliminar solo si no tienen órdenes o empaques asociados.

### Indicadores Visuales de Jerarquía

Como usuario navegando la lista de clientes, quiero ver claramente cuáles son clientes principales y cuáles son subclientes, para entender rápidamente la estructura organizacional.

La lista principal debe mostrar "→ Reebok" y "→ Cotton" bajo "Becktel" con indentación visual, y un contador de subclientes en cada cliente principal.

## Spec Scope

1. **Integración UI de Subclientes** - Agregar tab "Subclientes" en `/dashboard/customers/[id]/details` usando SubCustomerList existente
2. **Operaciones CRUD Faltantes** - Implementar update-subcustomer.ts y delete-subcustomer.ts con validaciones
3. **Indicadores de Jerarquía** - Mostrar estructura visual en `/dashboard/customers` principal
4. **Modales CRUD** - Formularios modales para crear/editar subclientes con HeroUI
5. **Validaciones de Dependencias** - Verificar orders/packings antes de permitir eliminación

## Out of Scope

- Jerarquía multinivel (subclientes de subclientes)
- Conversión de subcliente a cliente principal (ya existe en update-hierarchy.ts)
- Modificación de módulos orders/packings (solo preparar CustomerSelector)
- Reportes específicos por subcliente
- Permisos diferenciados por subcliente

## Expected Deliverable

1. Tab "Subclientes" funcional en página de detalles con CRUD completo (crear/editar/eliminar)
2. Lista principal de clientes mostrando jerarquía visual con indicadores "→"
3. Validaciones robustas que previenen eliminación de subclientes con dependencias
4. CustomerSelector mejorado para selección jerárquica (cliente → subcliente)