"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Avatar,
    Divider,
    Tooltip,
    <PERSON>,
    Pop<PERSON>,
    <PERSON>over<PERSON>rigger,
    <PERSON>overContent,
    User,
} from "@heroui/react";
import {
    CalendarIcon,
    PrinterIcon,
    EyeIcon,
    BuildingOfficeIcon,
    UserGroupIcon,
    CubeIcon,
    TruckIcon,
    ChevronDownIcon,
    ChevronUpIcon,
    ClockIcon,
    ShieldCheckIcon,
    CheckCircleIcon,
    XCircleIcon,
    ScaleIcon,
    ArchiveBoxIcon,
    IdentificationIcon,
    PhoneIcon,
    MapPinIcon,
} from "@heroicons/react/24/outline";
import { format, isBefore } from "date-fns";
import { es } from "date-fns/locale";
import { useRouter } from "next/navigation";

import { PackingStatusBadge } from "./PackingStatusBadge";

interface EnhancedPackingCardProps {
    packing: {
        id: string;
        folio: string;
        trackingNumber?: string | null;
        deliveryDate: string;
        createdAt: string;
        printedAt?: string | null;
        notes?: string | null;
        qualityCheckPassed: boolean;
        qualityCheckAt?: string | null;
        packedAt?: string | null;
        verifiedAt?: string | null;
        totalWeight?: number | null;
        totalVolume?: number | null;
        packagesCount: number;
        customer: {
            id: string;
            name: string;
        };
        subCustomer?: {
            id: string;
            name: string;
        } | null;
        status: {
            id: string;
            name: string;
            color?: string;
            iconName?: string;
        };
        transporter?: {
            id: string;
            name: string;
            phone?: string | null;
        } | null;
        driverName?: string | null;
        driverPhone?: string | null;
        vehicleInfo?: string | null;
        packedBy?: {
            id: string;
            name?: string | null;
            email: string;
        } | null;
        verifiedBy?: {
            id: string;
            name?: string | null;
            email: string;
        } | null;
        details: Array<{
            id: string;
            quantity: number;
            qualityPassed: boolean;
            defects: number;
            boxNumber?: number | null;
            weight?: number | null;
            garmentSize: {
                size: { code: string };
                garment: {
                    model: { code: string; description?: string };
                    color: { name: string };
                };
            };
        }>;
        _count?: {
            details: number;
            history: number;
            qualityChecks: number;
        };
    };
    onViewDetails?: () => void;
    onPrint?: () => void;
    onQualityCheck?: () => void;
    onAssignTransport?: () => void;
}

export function EnhancedPackingCard({
    packing,
    onViewDetails,
    onPrint,
    onQualityCheck,
    onAssignTransport,
}: EnhancedPackingCardProps) {
    const router = useRouter();
    const [isExpanded, setIsExpanded] = useState(false);

    // Calcular urgencia basada en fecha de entrega
    const deliveryDate = new Date(packing.deliveryDate);
    const today = new Date();
    const daysUntilDelivery = Math.ceil(
        (deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
    );

    const urgencyLevel = isBefore(deliveryDate, today)
        ? "overdue"
        : daysUntilDelivery <= 2
          ? "urgent"
          : daysUntilDelivery <= 7
            ? "soon"
            : "normal";

    const urgencyConfig = {
        overdue: { color: "danger", label: "Vencido", icon: ClockIcon },
        urgent: { color: "warning", label: "Urgente", icon: ClockIcon },
        soon: { color: "primary", label: "Próximo", icon: CalendarIcon },
        normal: { color: "default", label: "Normal", icon: CalendarIcon },
    };

    const urgency = urgencyConfig[urgencyLevel as keyof typeof urgencyConfig];

    // Calcular métricas
    const totalPieces = packing.details.reduce(
        (sum, detail) => sum + detail.quantity,
        0,
    );
    const totalDefects = packing.details.reduce(
        (sum, detail) => sum + detail.defects,
        0,
    );
    const qualityRate =
        totalPieces > 0
            ? ((totalPieces - totalDefects) / totalPieces) * 100
            : 100;

    // Agrupar detalles por modelo y color
    const groupedDetails = packing.details.reduce(
        (acc, detail) => {
            const key = `${detail.garmentSize.garment.model.code}-${detail.garmentSize.garment.color.name}`;

            if (!acc[key]) {
                acc[key] = {
                    model: detail.garmentSize.garment.model,
                    color: detail.garmentSize.garment.color.name,
                    sizes: [],
                    totalQuantity: 0,
                    totalDefects: 0,
                };
            }
            acc[key].sizes.push({
                size: detail.garmentSize.size.code,
                quantity: detail.quantity,
                defects: detail.defects,
                qualityPassed: detail.qualityPassed,
            });
            acc[key].totalQuantity += detail.quantity;
            acc[key].totalDefects += detail.defects;

            return acc;
        },
        {} as Record<string, any>,
    );

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.2 }}
            whileHover={{ y: -2 }}
        >
            <Card
                isPressable
                className="hover:shadow-lg transition-all duration-200"
                onPress={() => router.push(`/dashboard/packings/${packing.id}`)}
            >
                <CardHeader className="pb-3">
                    <div className="flex items-start justify-between w-full gap-4">
                        <div className="flex items-start gap-3">
                            <Avatar
                                className="flex-shrink-0"
                                color="primary"
                                name={packing.customer.name}
                                size="md"
                            />

                            <div className="space-y-2">
                                <div className="flex items-center gap-2 flex-wrap">
                                    <h3 className="text-lg font-semibold">
                                        {packing.folio}
                                    </h3>
                                    <PackingStatusBadge
                                        size="sm"
                                        status={packing.status}
                                    />
                                    {packing.qualityCheckPassed && (
                                        <Chip
                                            color="success"
                                            size="sm"
                                            startContent={
                                                <ShieldCheckIcon className="w-3 h-3" />
                                            }
                                            variant="flat"
                                        >
                                            QC Aprobado
                                        </Chip>
                                    )}
                                    {packing.transporter && (
                                        <Chip
                                            color="secondary"
                                            size="sm"
                                            startContent={
                                                <TruckIcon className="w-3 h-3" />
                                            }
                                            variant="flat"
                                        >
                                            Transporte Asignado
                                        </Chip>
                                    )}
                                </div>

                                <div className="flex items-center gap-3 text-sm text-gray-500 flex-wrap">
                                    <span className="flex items-center gap-1">
                                        <BuildingOfficeIcon className="w-4 h-4" />
                                        {packing.customer.name}
                                    </span>
                                    {packing.subCustomer && (
                                        <>
                                            <span>•</span>
                                            <span className="flex items-center gap-1">
                                                <UserGroupIcon className="w-4 h-4" />
                                                {packing.subCustomer.name}
                                            </span>
                                        </>
                                    )}
                                    {packing.trackingNumber && (
                                        <>
                                            <span>•</span>
                                            <span className="flex items-center gap-1">
                                                <MapPinIcon className="w-4 h-4" />
                                                {packing.trackingNumber}
                                            </span>
                                        </>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-2">
                            <Tooltip
                                content={`${urgency.label} - ${format(deliveryDate, "PPP", { locale: es })}`}
                            >
                                <Chip
                                    color={urgency.color as any}
                                    size="sm"
                                    startContent={
                                        <urgency.icon className="w-3 h-3" />
                                    }
                                    variant="flat"
                                >
                                    {urgencyLevel === "overdue"
                                        ? `${Math.abs(daysUntilDelivery)} días vencido`
                                        : `${daysUntilDelivery} días`}
                                </Chip>
                            </Tooltip>

                            <div className="flex gap-1">
                                <Tooltip content="Ver detalles">
                                    <Button
                                        isIconOnly
                                        size="sm"
                                        variant="light"
                                        onPress={() => {
                                            onViewDetails?.();
                                        }}
                                    >
                                        <EyeIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>

                                <Tooltip content="Imprimir">
                                    <Button
                                        isIconOnly
                                        size="sm"
                                        variant="light"
                                        onPress={() => {
                                            onPrint?.();
                                        }}
                                    >
                                        <PrinterIcon className="w-4 h-4" />
                                    </Button>
                                </Tooltip>
                            </div>
                        </div>
                    </div>
                </CardHeader>

                <Divider />

                <CardBody className="pt-3">
                    <div className="space-y-3">
                        {/* Estadísticas mejoradas */}
                        <div className="grid grid-cols-4 gap-2">
                            <Tooltip content="Total de piezas empacadas">
                                <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                    <CubeIcon className="w-5 h-5 mx-auto mb-1 text-gray-400" />
                                    <p className="text-xl font-bold">
                                        {totalPieces}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        Piezas
                                    </p>
                                </div>
                            </Tooltip>

                            <Tooltip
                                content={`${packing.packagesCount} paquete(s)`}
                            >
                                <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                    <ArchiveBoxIcon className="w-5 h-5 mx-auto mb-1 text-gray-400" />
                                    <p className="text-xl font-bold">
                                        {packing.packagesCount}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        Paquetes
                                    </p>
                                </div>
                            </Tooltip>

                            <Tooltip
                                content={
                                    packing.totalWeight
                                        ? `${packing.totalWeight} kg`
                                        : "Peso no registrado"
                                }
                            >
                                <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                    <ScaleIcon className="w-5 h-5 mx-auto mb-1 text-gray-400" />
                                    <p className="text-xl font-bold">
                                        {packing.totalWeight || "-"}
                                    </p>
                                    <p className="text-xs text-gray-500">Kg</p>
                                </div>
                            </Tooltip>

                            <Tooltip
                                content={`Tasa de calidad: ${qualityRate.toFixed(1)}%`}
                            >
                                <div className="text-center p-2 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                                    {packing.qualityCheckPassed ? (
                                        <CheckCircleIcon className="w-5 h-5 mx-auto mb-1 text-success" />
                                    ) : (
                                        <XCircleIcon className="w-5 h-5 mx-auto mb-1 text-warning" />
                                    )}
                                    <p className="text-sm font-semibold">
                                        {totalDefects > 0
                                            ? `${totalDefects}`
                                            : "OK"}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {totalDefects > 0
                                            ? "Defectos"
                                            : "Calidad"}
                                    </p>
                                </div>
                            </Tooltip>
                        </div>

                        {/* Información de transporte */}
                        {packing.transporter && (
                            <Card className="bg-secondary-50 dark:bg-secondary-900/20">
                                <CardBody className="p-3">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <TruckIcon className="w-5 h-5 text-secondary" />
                                            <div>
                                                <p className="text-sm font-medium">
                                                    {packing.transporter.name}
                                                </p>
                                                {packing.driverName && (
                                                    <p className="text-xs text-gray-500">
                                                        Conductor:{" "}
                                                        {packing.driverName}
                                                        {packing.driverPhone && (
                                                            <span className="ml-2">
                                                                <PhoneIcon className="w-3 h-3 inline mr-1" />
                                                                {
                                                                    packing.driverPhone
                                                                }
                                                            </span>
                                                        )}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                        {packing.vehicleInfo && (
                                            <Chip size="sm" variant="flat">
                                                {packing.vehicleInfo}
                                            </Chip>
                                        )}
                                    </div>
                                </CardBody>
                            </Card>
                        )}

                        {/* Trazabilidad */}
                        <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-4">
                                {packing.packedBy && (
                                    <Popover showArrow placement="top">
                                        <PopoverTrigger>
                                            <button className="flex items-center gap-1 text-gray-500 hover:text-gray-700">
                                                <IdentificationIcon className="w-4 h-4" />
                                                <span>Empacado</span>
                                            </button>
                                        </PopoverTrigger>
                                        <PopoverContent>
                                            <div className="p-2">
                                                <User
                                                    avatarProps={{
                                                        name:
                                                            packing.packedBy
                                                                .name ||
                                                            packing.packedBy
                                                                .email,
                                                        size: "sm",
                                                    }}
                                                    description={
                                                        packing.packedAt
                                                            ? format(
                                                                  new Date(
                                                                      packing.packedAt,
                                                                  ),
                                                                  "PPp",
                                                                  {
                                                                      locale: es,
                                                                  },
                                                              )
                                                            : ""
                                                    }
                                                    name={
                                                        packing.packedBy.name ||
                                                        packing.packedBy.email
                                                    }
                                                />
                                            </div>
                                        </PopoverContent>
                                    </Popover>
                                )}

                                {packing.verifiedBy && (
                                    <Popover showArrow placement="top">
                                        <PopoverTrigger>
                                            <button className="flex items-center gap-1 text-gray-500 hover:text-gray-700">
                                                <ShieldCheckIcon className="w-4 h-4" />
                                                <span>Verificado</span>
                                            </button>
                                        </PopoverTrigger>
                                        <PopoverContent>
                                            <div className="p-2">
                                                <User
                                                    avatarProps={{
                                                        name:
                                                            packing.verifiedBy
                                                                .name ||
                                                            packing.verifiedBy
                                                                .email,
                                                        size: "sm",
                                                    }}
                                                    description={
                                                        packing.verifiedAt
                                                            ? format(
                                                                  new Date(
                                                                      packing.verifiedAt,
                                                                  ),
                                                                  "PPp",
                                                                  {
                                                                      locale: es,
                                                                  },
                                                              )
                                                            : ""
                                                    }
                                                    name={
                                                        packing.verifiedBy
                                                            .name ||
                                                        packing.verifiedBy.email
                                                    }
                                                />
                                            </div>
                                        </PopoverContent>
                                    </Popover>
                                )}
                            </div>

                            {packing.printedAt && (
                                <Chip color="success" size="sm" variant="flat">
                                    <PrinterIcon className="w-3 h-3 mr-1" />
                                    Impreso
                                </Chip>
                            )}
                        </div>

                        {/* Notas */}
                        {packing.notes && (
                            <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                <p className="text-xs text-yellow-800 dark:text-yellow-200">
                                    {packing.notes}
                                </p>
                            </div>
                        )}

                        {/* Botones de acción rápida */}
                        <div className="flex gap-2">
                            {!packing.qualityCheckPassed && (
                                <Button
                                    color="primary"
                                    size="sm"
                                    startContent={
                                        <ShieldCheckIcon className="w-4 h-4" />
                                    }
                                    variant="flat"
                                    onPress={() => {
                                        onQualityCheck?.();
                                    }}
                                >
                                    Control de Calidad
                                </Button>
                            )}

                            {!packing.transporter &&
                                packing.qualityCheckPassed && (
                                    <Button
                                        color="secondary"
                                        size="sm"
                                        startContent={
                                            <TruckIcon className="w-4 h-4" />
                                        }
                                        variant="flat"
                                        onPress={() => {
                                            onAssignTransport?.();
                                        }}
                                    >
                                        Asignar Transporte
                                    </Button>
                                )}
                        </div>

                        {/* Detalles expandibles */}
                        <div>
                            <Button
                                fullWidth
                                endContent={
                                    isExpanded ? (
                                        <ChevronUpIcon className="w-4 h-4" />
                                    ) : (
                                        <ChevronDownIcon className="w-4 h-4" />
                                    )
                                }
                                size="sm"
                                variant="light"
                                onPress={() => {
                                    setIsExpanded(!isExpanded);
                                }}
                            >
                                {isExpanded ? "Ocultar" : "Mostrar"} detalles (
                                {Object.keys(groupedDetails).length} modelos)
                            </Button>

                            {isExpanded && (
                                <motion.div
                                    animate={{ opacity: 1, height: "auto" }}
                                    className="mt-2 space-y-2"
                                    exit={{ opacity: 0, height: 0 }}
                                    initial={{ opacity: 0, height: 0 }}
                                >
                                    {Object.values(groupedDetails).map(
                                        (group: any, index) => (
                                            <div
                                                key={index}
                                                className="p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg"
                                            >
                                                <div className="flex items-center justify-between mb-2">
                                                    <div>
                                                        <p className="font-medium">
                                                            {group.model.code}
                                                            {group.model
                                                                .description && (
                                                                <span className="text-sm text-gray-500 ml-1">
                                                                    -{" "}
                                                                    {
                                                                        group
                                                                            .model
                                                                            .description
                                                                    }
                                                                </span>
                                                            )}
                                                        </p>
                                                        <p className="text-sm text-gray-500">
                                                            {group.color}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-semibold">
                                                            {
                                                                group.totalQuantity
                                                            }{" "}
                                                            pzs
                                                        </p>
                                                        {group.totalDefects >
                                                            0 && (
                                                            <p className="text-xs text-danger">
                                                                {
                                                                    group.totalDefects
                                                                }{" "}
                                                                defectos
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex flex-wrap gap-1">
                                                    {group.sizes.map(
                                                        (
                                                            size: any,
                                                            sizeIndex: number,
                                                        ) => (
                                                            <Chip
                                                                key={sizeIndex}
                                                                color={
                                                                    size.qualityPassed
                                                                        ? "default"
                                                                        : "warning"
                                                                }
                                                                size="sm"
                                                                variant="flat"
                                                            >
                                                                {size.size}:{" "}
                                                                {size.quantity}
                                                                {size.defects >
                                                                    0 && (
                                                                    <span className="ml-1 text-danger">
                                                                        (-
                                                                        {
                                                                            size.defects
                                                                        }
                                                                        )
                                                                    </span>
                                                                )}
                                                            </Chip>
                                                        ),
                                                    )}
                                                </div>
                                            </div>
                                        ),
                                    )}
                                </motion.div>
                            )}
                        </div>
                    </div>
                </CardBody>
            </Card>
        </motion.div>
    );
}
