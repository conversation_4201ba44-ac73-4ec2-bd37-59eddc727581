"use server";

import { createServerRevalidation } from "@/shared/lib/revalidation";
import { auth } from "@/lib/auth-helpers";

// Obtener helpers de revalidación para la entidad "orders"
export const { revalidateCache } = createServerRevalidation("orders");

// Interfaces para las respuestas de acciones
export interface ActionResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}

/**
 * Función de utilidad para crear una respuesta de error unificada
 */
export async function createErrorResponse(
    error: unknown,
): Promise<ActionResponse<never>> {
    // REMOVED: console.error("[Server Action Error]", error);

    return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
    };
}

/**
 * Obtiene el usuario actual de la sesión
 */
export async function getCurrentUser() {
    try {
        const session = await auth();

        if (!session || !session.user) {
            return null;
        }

        return session.user;
    } catch (error) {
        // REMOVED: console.error("[getCurrentUser Error]", error);

        return null;
    }
}

/**
 * Convierte string a objeto Date o devuelve null
 */
export async function parseDate(
    dateString?: string | null,
): Promise<Date | null> {
    if (!dateString) return null;
    try {
        return new Date(dateString);
    } catch (error) {
        // REMOVED: console.error("[parseDate Error]", error);

        return null;
    }
}
