import React from "react";
import { Info, AlertTriangle, CheckCircle, XCircle } from "lucide-react";

interface SizeInfoMessageProps {
    type: "info" | "warning" | "error" | "success";
    title: string;
    message: string;
}

export function SizeInfoMessage({
    type,
    title,
    message,
}: SizeInfoMessageProps) {
    const styles = {
        info: {
            container:
                "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",
            icon: <Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />,
            text: "text-blue-800 dark:text-blue-300",
        },
        warning: {
            container:
                "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800",
            icon: (
                <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400" />
            ),
            text: "text-amber-800 dark:text-amber-300",
        },
        error: {
            container:
                "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800",
            icon: (
                <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            ),
            text: "text-red-800 dark:text-red-300",
        },
        success: {
            container:
                "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800",
            icon: (
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            ),
            text: "text-green-800 dark:text-green-300",
        },
    };

    const style = styles[type];

    return (
        <div className={`p-4 rounded-lg border ${style.container}`}>
            <div className="flex gap-3">
                <div className="flex-shrink-0">{style.icon}</div>
                <div className="flex-1">
                    <h4 className={`text-sm font-medium ${style.text}`}>
                        {title}
                    </h4>
                    <p className={`text-sm mt-1 ${style.text} opacity-90`}>
                        {message}
                    </p>
                </div>
            </div>
        </div>
    );
}
