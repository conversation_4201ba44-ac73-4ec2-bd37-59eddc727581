{"date": "2025-05-22T20:29:15.029Z", "totalConflicts": 90, "safeToDelete": 0, "needsReview": 90, "files": {"toDelete": [], "toReview": [{"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\app\\dashboard\\orders\\renderFunctions.sync-conflict-********-120325-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\remission\\AssignmentRemission.sync-conflict-********-120326-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\remission\\RemissionDocument.sync-conflict-********-120325-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\AssignmentTable.sync-conflict-********-120332-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\AssignmentWizard.sync-conflict-********-120329-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ContractorInfo.sync-conflict-********-120324-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ContractorIno.tsx.sync-conflict-********-120329-KAS6RBD.bak", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ContractorStep.sync-conflict-********-120330-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\EnhancedAssignmentWizard.sync-conflict-********-120331-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\OrdersStep.sync-conflict-********-120328-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\QuantitiesStep.sync-conflict-********-120323-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\SummaryStep.sync-conflict-********-120330-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\EmptyAndLoadingStates.sync-conflict-********-120328-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\EnhancedStatCard.sync-conflict-********-120331-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\OrderCard.sync-conflict-********-120326-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\QuantityInput.sync-conflict-********-120333-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\SearchAndFilterPanel.sync-conflict-********-120329-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\SelectedOrdersPanel.sync-conflict-********-120326-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\assignments\\wizard\\ui\\StepIndicator.sync-conflict-********-120329-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\colors\\ColorContexts.sync-conflict-********-120330-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\layout\\ThemeSwitch.sync-conflict-********-120329-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\notes\\notes-ui.sync-conflict-********-120323-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\notes\\OrderNotesTab.sync-conflict-********-120328-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\notes\\OrderNotesTabWithSelection.sync-conflict-********-120326-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\notes\\SelectableNoteCard.sync-conflict-********-120330-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\orders\\FullCalendarView.sync-conflict-********-120324-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\orders\\OrderDashboard.sync-conflict-********-120331-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\orders\\OrderDetailModal.sync-conflict-********-120333-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\components\\ui\\BlobLoader.sync-conflict-********-120330-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\assignments\\create.sync-conflict-********-120329-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\assignments\\index.sync-conflict-********-120323-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\assignments\\query.sync-conflict-********-120327-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\assignments\\types.sync-conflict-********-120332-KAS6RBD.ts", "originalExists": true}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\contractors\\schema.sync-conflict-********-120333-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\contractors\\update.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\notes\\list.sync-conflict-********-120326-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\notes\\modal-notes.sync-conflict-********-120326-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\create.sync-conflict-********-120328-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\index.sync-conflict-********-120327-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\notes.sync-conflict-********-120331-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\parts.sync-conflict-********-120328-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\schema.sync-conflict-********-120326-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\update.sync-conflict-********-120323-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\orders\\utils.sync-conflict-********-120325-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\README.sync-conflict-********-120325-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\remissions\\index.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\remissions\\migrate.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\remissions\\types.sync-conflict-********-120326-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\remissions\\utils.sync-conflict-********-120328-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\actions\\users.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\auth\\auth-actions.sync-conflict-********-120327-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\auth\\index.sync-conflict-********-120330-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\auth\\nextauth-config.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\auth\\users.sync-conflict-********-120331-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\client-revalidation.sync-conflict-********-120325-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\db.sync-conflict-********-120326-KAS6RBD.ts", "originalExists": true}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\index.sync-conflict-********-120329-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useContractor.sync-conflict-********-120325-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useCustomer.sync-conflict-********-120325-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useModel.sync-conflict-********-120326-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrder.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrderModalAdapter.sync-conflict-********-120333-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrderNotes.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrdersData.sync-conflict-********-120330-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrdersParams.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrdersState.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useOrderStatusFilter.sync-conflict-********-120328-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useSize.sync-conflict-********-120331-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\hooks\\useUser.sync-conflict-********-120323-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\prisma-utils.sync-conflict-********-120324-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\prisma.sync-conflict-********-120333-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\services\\db-connection-service.sync-conflict-********-120330-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\services\\pdf-generation.sync-conflict-********-120333-KAS6RBD.ts", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\swr-provider.sync-conflict-********-120324-KAS6RBD.tsx", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\lib\\utils.sync-conflict-********-120330-KAS6RBD.ts", "originalExists": true}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\activeContext.sync-conflict-********-120329-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\assignment-ui-enhancement-plan.sync-conflict-********-120332-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\implementation-summary.sync-conflict-********-120330-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\observation-instrument-creative-data.sync-conflict-********-120330-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\observation-instrument-creative-uiux.sync-conflict-********-120329-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\observation-instrument-plan.sync-conflict-********-120325-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\progress.sync-conflict-********-120323-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\reflection.sync-conflict-********-120330-KAS6RBD.md", "originalExists": true}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\remission-enhancement-plan.sync-conflict-********-120331-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\memory-bank\\tasks.sync-conflict-********-120328-KAS6RBD.md", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\package-lock.sync-conflict-********-120434-KAS6RBD.json", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\prisma\\seed.sync-conflict-********-120434-KAS6RBD.cjs", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\scripts\\sync-conflict\\output\\backups\\package.sync-conflict-********-120434-KAS6RBD.json.backup-2025-05-21T20-15-32.530Z", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\scripts\\sync-conflict\\output\\backups\\route.sync-conflict-********-120328-KAS6RBD.ts.backup-2025-05-21T20-15-27.434Z", "originalExists": false}, {"file": "C:\\Users\\<USER>\\Documents\\git\\Next Js\\lohari\\scripts\\sync-conflict\\output\\backups\\settings.sync-conflict-********-120328-KAS6RBD.json.backup-2025-05-21T20-15-30.588Z", "originalExists": false}]}}