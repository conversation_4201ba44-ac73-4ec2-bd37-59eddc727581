"use client";

import { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
    ExclamationTriangleIcon,
    CheckCircleIcon,
    XCircleIcon,
    SparklesIcon,
} from "@heroicons/react/24/outline";

interface AvailabilityCounterProps {
    totalQuantity: number;
    usedQuantity: number;
    requestedQuantity: number;
    className?: string;
    showDetails?: boolean;
}

/**
 * Componente que muestra un contador visual de disponibilidad
 * con códigos de color según el estado de asignación
 */
export default function AvailabilityCounter({
    totalQuantity,
    usedQuantity,
    requestedQuantity,
    className = "",
    showDetails = true,
}: AvailabilityCounterProps) {
    const [availableText, setAvailableText] = useState("");
    const [barColor, setBarColor] = useState("from-green-400 to-emerald-500");
    const [textColor, setTextColor] = useState("text-green-600");
    const [statusIcon, setStatusIcon] = useState<React.ReactNode>(null);
    const [isOverLimit, setIsOverLimit] = useState(false);
    const availableQuantity = totalQuantity - usedQuantity;
    const remainingAfterRequest = availableQuantity - requestedQuantity;

    // Calcular porcentaje para la barra visual
    const usedPercent = Math.min(
        100,
        Math.round((usedQuantity / totalQuantity) * 100),
    );
    const requestedPercent = Math.min(
        100 - usedPercent,
        Math.round((requestedQuantity / totalQuantity) * 100),
    );

    // Ref para animar cambios en las cantidades
    const prevRequestedRef = useRef(requestedQuantity);

    useEffect(() => {
        // Determinar texto, color e icono según disponibilidad
        if (remainingAfterRequest < 0) {
            setIsOverLimit(true);
            setAvailableText(
                `¡Excedido por ${Math.abs(remainingAfterRequest)}!`,
            );
            setBarColor("from-red-400 to-red-600");
            setTextColor("text-red-600 dark:text-red-400");
            setStatusIcon(<XCircleIcon className="w-5 h-5" />);
        } else if (remainingAfterRequest === 0) {
            setIsOverLimit(false);
            setAvailableText("Al límite exacto");
            setBarColor("from-amber-400 to-orange-500");
            setTextColor("text-amber-600 dark:text-amber-400");
            setStatusIcon(<ExclamationTriangleIcon className="w-5 h-5" />);
        } else if (remainingAfterRequest <= availableQuantity * 0.2) {
            setIsOverLimit(false);
            setAvailableText(`${remainingAfterRequest} disponible`);
            setBarColor("from-amber-400 to-yellow-500");
            setTextColor("text-amber-600 dark:text-amber-400");
            setStatusIcon(<ExclamationTriangleIcon className="w-5 h-5" />);
        } else {
            setIsOverLimit(false);
            setAvailableText(`${remainingAfterRequest} disponible`);
            setBarColor("from-green-400 to-emerald-500");
            setTextColor("text-green-600 dark:text-green-400");
            setStatusIcon(<CheckCircleIcon className="w-5 h-5" />);
        }

        prevRequestedRef.current = requestedQuantity;
    }, [
        totalQuantity,
        usedQuantity,
        requestedQuantity,
        availableQuantity,
        remainingAfterRequest,
    ]);

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            className={`flex flex-col ${className}`}
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3 }}
        >
            {showDetails && (
                <div className="flex justify-between items-center mb-3">
                    <motion.div
                        animate={{ x: 0, opacity: 1 }}
                        className="flex items-center gap-2"
                        initial={{ x: -10, opacity: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        {requestedQuantity > 0 && (
                            <>
                                <SparklesIcon className="w-4 h-4 text-blue-500" />
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    {requestedQuantity} seleccionado
                                </span>
                            </>
                        )}
                    </motion.div>
                    <motion.div
                        animate={{ x: 0, opacity: 1 }}
                        className={`flex items-center gap-2 ${textColor}`}
                        initial={{ x: 10, opacity: 0 }}
                        transition={{ delay: 0.1 }}
                    >
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={availableText}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0.8, opacity: 0 }}
                                initial={{ scale: 0.8, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                            >
                                {statusIcon}
                            </motion.div>
                        </AnimatePresence>
                        <span
                            className={`font-semibold ${isOverLimit ? "animate-pulse" : ""}`}
                        >
                            {availableText}
                        </span>
                    </motion.div>
                </div>
            )}

            <div className="relative">
                {/* Barra de progreso con glassmorphism */}
                <div className="h-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden backdrop-blur-sm shadow-inner">
                    {/* Barra de cantidad usada */}
                    {usedPercent > 0 && (
                        <motion.div
                            animate={{ width: `${usedPercent}%` }}
                            className="h-full bg-gradient-to-r from-gray-400 to-gray-500 float-left relative"
                            initial={{ width: 0 }}
                            transition={{ duration: 0.5, ease: "easeOut" }}
                        >
                            <div className="absolute inset-0 bg-white/20 animate-pulse" />
                        </motion.div>
                    )}

                    {/* Barra de cantidad solicitada */}
                    {requestedPercent > 0 && (
                        <motion.div
                            animate={{ width: `${requestedPercent}%` }}
                            className={`h-full bg-gradient-to-r ${barColor} float-left relative overflow-hidden`}
                            initial={{ width: 0 }}
                            transition={{
                                duration: 0.5,
                                delay: 0.2,
                                ease: "easeOut",
                            }}
                        >
                            {/* Efecto de brillo animado */}
                            <motion.div
                                animate={{
                                    x: ["-100%", "200%"],
                                }}
                                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                                transition={{
                                    duration: 2,
                                    repeat: Infinity,
                                    repeatDelay: 1,
                                    ease: "easeInOut",
                                }}
                            />
                        </motion.div>
                    )}
                </div>

                {/* Indicadores de porcentaje */}
                {showDetails && (
                    <div className="absolute -top-1 w-full">
                        {usedPercent > 5 && (
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                className="absolute text-xs font-medium text-gray-600 dark:text-gray-400"
                                initial={{ opacity: 0, y: 5 }}
                                style={{
                                    left: `${usedPercent / 2}%`,
                                    transform: "translateX(-50%)",
                                }}
                                transition={{ delay: 0.6 }}
                            >
                                {usedPercent}%
                            </motion.div>
                        )}
                        {requestedPercent > 5 && (
                            <motion.div
                                animate={{ opacity: 1, y: 0 }}
                                className={`absolute text-xs font-bold ${textColor}`}
                                initial={{ opacity: 0, y: 5 }}
                                style={{
                                    left: `${usedPercent + requestedPercent / 2}%`,
                                    transform: "translateX(-50%)",
                                }}
                                transition={{ delay: 0.8 }}
                            >
                                {requestedPercent}%
                            </motion.div>
                        )}
                    </div>
                )}
            </div>

            {showDetails && (
                <div className="flex justify-between items-center mt-3">
                    <motion.div
                        animate={{ opacity: 1 }}
                        className="flex flex-col"
                        initial={{ opacity: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            Mínimo
                        </span>
                        <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                            0
                        </span>
                    </motion.div>

                    <motion.div
                        animate={{ opacity: 1 }}
                        className="flex items-center gap-4"
                        initial={{ opacity: 0 }}
                        transition={{ delay: 0.4 }}
                    >
                        <div className="flex flex-col items-center">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                Usado
                            </span>
                            <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                {usedQuantity}
                            </span>
                        </div>
                        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600" />
                        <div className="flex flex-col items-center">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                Disponible
                            </span>
                            <span className="text-sm font-bold text-green-600 dark:text-green-400">
                                {availableQuantity}
                            </span>
                        </div>
                    </motion.div>

                    <motion.div
                        animate={{ opacity: 1 }}
                        className="flex flex-col items-end"
                        initial={{ opacity: 0 }}
                        transition={{ delay: 0.3 }}
                    >
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                            Total
                        </span>
                        <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                            {totalQuantity}
                        </span>
                    </motion.div>
                </div>
            )}
        </motion.div>
    );
}
