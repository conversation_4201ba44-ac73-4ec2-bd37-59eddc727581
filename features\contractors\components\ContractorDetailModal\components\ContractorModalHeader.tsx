import React from "react";
import { motion } from "framer-motion";
import {
    PencilIcon,
    TrashIcon,
    UserIcon,
    EnvelopeIcon,
    PhoneIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { Avatar, Button, Chip } from "@/shared/components/ui/hero-ui-client";

import { ContractorWithDetails } from "../types/contractor-modal.types";

interface ContractorModalHeaderProps {
    contractor: ContractorWithDetails;
    onEdit?: (contractorId: string) => void;
    onDelete?: () => void;
    isDeleting?: boolean;
}

export default function ContractorModalHeader({
    contractor,
    onEdit,
    onDelete,
    isDeleting = false,
}: ContractorModalHeaderProps) {
    const getFullName = () => {
        const parts = [
            contractor.firstName,
            contractor.middleName,
            contractor.lastName,
            contractor.secondLastName,
        ].filter(Boolean);

        return parts.length > 0 ? parts.join(" ") : contractor.name;
    };

    const getInitials = () => {
        const fullName = getFullName();
        const parts = fullName.split(" ");

        if (parts.length >= 2) {
            return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
        }

        return fullName.slice(0, 2).toUpperCase();
    };

    const hasAssignments = (contractor._count?.assignments || 0) > 0;

    return (
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/30 dark:via-indigo-950/30 dark:to-purple-950/30 p-6 border-b border-gray-200 dark:border-gray-700">
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-10">
                <div className="absolute -top-4 -right-4 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl animate-blob" />
                <div className="absolute -bottom-8 -left-4 w-72 h-72 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000" />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000" />
            </div>

            <div className="relative z-10">
                <div className="flex items-start justify-between">
                    <div className="flex items-center gap-4">
                        <motion.div
                            animate={{ scale: 1, opacity: 1 }}
                            initial={{ scale: 0.8, opacity: 0 }}
                            transition={{
                                type: "spring",
                                stiffness: 300,
                                damping: 20,
                            }}
                        >
                            <Avatar
                                isBordered
                                className="w-20 h-20 text-xl font-bold"
                                color="primary"
                                name={getInitials()}
                                size="lg"
                            />
                        </motion.div>

                        <div>
                            <motion.h2
                                animate={{ y: 0, opacity: 1 }}
                                className="text-2xl font-bold text-gray-900 dark:text-white"
                                initial={{ y: 10, opacity: 0 }}
                                transition={{ delay: 0.1 }}
                            >
                                {contractor.name}
                            </motion.h2>

                            <motion.p
                                animate={{ y: 0, opacity: 1 }}
                                className="text-gray-600 dark:text-gray-300 flex items-center gap-1 mt-1"
                                initial={{ y: 10, opacity: 0 }}
                                transition={{ delay: 0.2 }}
                            >
                                <UserIcon className="w-4 h-4" />
                                {getFullName()}
                            </motion.p>

                            <motion.div
                                animate={{ y: 0, opacity: 1 }}
                                className="flex items-center gap-4 mt-2 text-sm text-gray-500 dark:text-gray-400"
                                initial={{ y: 10, opacity: 0 }}
                                transition={{ delay: 0.3 }}
                            >
                                {contractor.email && (
                                    <a
                                        className="flex items-center gap-1 hover:text-primary transition-colors"
                                        href={`mailto:${contractor.email}`}
                                    >
                                        <EnvelopeIcon className="w-4 h-4" />
                                        {contractor.email}
                                    </a>
                                )}
                                {contractor.phone && (
                                    <a
                                        className="flex items-center gap-1 hover:text-primary transition-colors"
                                        href={`tel:${contractor.phone}`}
                                    >
                                        <PhoneIcon className="w-4 h-4" />
                                        {contractor.phone}
                                    </a>
                                )}
                                <span className="flex items-center gap-1">
                                    <CalendarIcon className="w-4 h-4" />
                                    Desde{" "}
                                    {format(
                                        new Date(contractor.createdAt),
                                        "MMM yyyy",
                                        { locale: es },
                                    )}
                                </span>
                            </motion.div>

                            <motion.div
                                animate={{ y: 0, opacity: 1 }}
                                className="flex items-center gap-2 mt-3"
                                initial={{ y: 10, opacity: 0 }}
                                transition={{ delay: 0.4 }}
                            >
                                <Chip
                                    color={
                                        hasAssignments ? "success" : "warning"
                                    }
                                    size="sm"
                                    variant="flat"
                                >
                                    {hasAssignments
                                        ? "Activo"
                                        : "Sin asignaciones"}
                                </Chip>
                                {(contractor._count?.assignments || 0) > 0 && (
                                    <Chip
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {contractor._count?.assignments || 0}{" "}
                                        asignaciones
                                    </Chip>
                                )}
                                {(contractor._count?.remissions || 0) > 0 && (
                                    <Chip
                                        color="secondary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {contractor._count?.remissions || 0}{" "}
                                        remisiones
                                    </Chip>
                                )}
                            </motion.div>
                        </div>
                    </div>

                    <div className="flex items-center gap-2">
                        {onEdit && (
                            <Button
                                isIconOnly
                                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                                color="primary"
                                variant="flat"
                                onPress={() => onEdit(contractor.id)}
                            >
                                <PencilIcon className="w-4 h-4" />
                            </Button>
                        )}
                        {onDelete && (
                            <Button
                                isIconOnly
                                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                                color="danger"
                                isDisabled={hasAssignments || isDeleting}
                                isLoading={isDeleting}
                                variant="flat"
                                onPress={onDelete}
                            >
                                <TrashIcon className="w-4 h-4" />
                            </Button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
