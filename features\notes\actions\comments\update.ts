"use server";

import { revalidatePath } from "next/cache";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/utils/prisma-helpers";

import {
    updateCommentSchema,
    type UpdateCommentInput,
    type CommentActionResult,
} from "../../types/comment";

/**
 * Actualiza un comentario existente
 */
export async function updateComment(
    input: UpdateCommentInput,
): Promise<CommentActionResult> {
    try {
        // Validar sesión
        const session = await auth();
        const userId = session?.user?.id;
        const userRole = session?.user?.role;

        if (!userId) {
            return { success: false, error: "No autorizado" };
        }

        // Validar input
        const validatedData = updateCommentSchema.parse(input);

        // Obtener el comentario existente
        const existingComment = await db.noteComment.findUnique({
            where: { id: validatedData.commentId },
            select: {
                id: true,
                authorId: true,
                noteId: true,
                note: {
                    select: {
                        orderId: true,
                    },
                },
            },
        });

        if (!existingComment) {
            return { success: false, error: "El comentario no existe" };
        }

        // Verificar permisos (autor o admin)
        const isAuthor = existingComment.authorId === userId;
        const isAdmin = (userRole as any)?.name === "ADMIN";

        if (!isAuthor && !isAdmin) {
            return {
                success: false,
                error: "No tienes permiso para editar este comentario",
            };
        }

        // Actualizar el comentario
        const updatedComment = await db.noteComment.update({
            where: { id: validatedData.commentId },
            data: {
                content: validatedData.content,
                updatedAt: new Date(),
            },
            include: {
                author: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true,
                    },
                },
                _count: {
                    select: {
                        replies: true,
                    },
                },
            },
        });

        // Revalidar las rutas afectadas
        revalidatePath(`/dashboard/orders/${existingComment.note.orderId}`);
        revalidatePath("/dashboard/notes");

        return {
            success: true,
            data: updatedComment as any,
        };
    } catch (error) {
        return handlePrismaError(error, "Error al actualizar el comentario");
    }
}
