import React from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
    CalendarIcon,
    ClipboardDocumentListIcon,
    ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

import { Chip, Progress, Tooltip } from "@/shared/components/ui/hero-ui-client";
import { Order } from "@/features/orders/types/orders";
import {
    adjustTimezoneOffset,
    getDaysDifference,
} from "@/shared/utils/dateUtils";
import { calculateRiskScore } from "@/features/orders/utils/riskCalculator";
import RiskIndicator from "@/features/orders/components/RiskIndicator";
import { getStatusConfig } from "@/features/orders/utils/statusIcons";

// Helper functions
const calculateTotalAssigned = (order: Order): number => {
    if (!order.assignments || order.assignments.length === 0) return 0;

    return order.assignments.reduce(
        (sum, assignment) => sum + (assignment.quantity || 0),
        0,
    );
};

const calculateTotalPieces = (order: Order): number => {
    if ((order as any).totalQuantity) return (order as any).totalQuantity;

    if (order.garments && order.garments.length > 0) {
        const total = order.garments.reduce((sum, garment) => {
            const garmentTotal =
                garment.sizes?.reduce((sizeSum, size) => {
                    return sizeSum + (size.totalQuantity || 0);
                }, 0) || 0;

            return sum + garmentTotal;
        }, 0);

        if (total > 0) return total;
    }

    if (order.assignments && order.assignments.length > 0) {
        return order.assignments.reduce(
            (sum, assignment) => sum + (assignment.quantity || 0),
            0,
        );
    }

    return 0;
};

const calculateAssignmentPercentage = (order: Order): number => {
    const total = calculateTotalPieces(order);

    if (total === 0) return 0;
    const assigned = calculateTotalAssigned(order);

    return Math.round((assigned / total) * 100);
};

const getProgressColor = (
    percentage: number,
): "success" | "warning" | "danger" => {
    if (percentage === 100) return "success";
    if (percentage >= 50) return "warning";

    return "danger";
};

interface ExtendedOrder extends Order {
    deliveryDate?: string | Date;
}

// Columnas de la tabla
export const columns = [
    {
        key: "order",
        label: "Orden",
        sortable: true,
        render: (order: Order) => {
            const deliveryDate =
                order.estimatedDeliveryDate ||
                (order as ExtendedOrder).deliveryDate;
            const date = deliveryDate
                ? adjustTimezoneOffset(deliveryDate)
                : null;
            const daysLeft = date ? getDaysDifference(date) : null;
            const isOverdue =
                daysLeft !== null &&
                daysLeft < 0 &&
                order.status !== "delivered";
            const isUrgent =
                daysLeft !== null &&
                daysLeft <= 3 &&
                order.status !== "delivered";

            return (
                <div className="flex items-center gap-3">
                    <div className="flex-1">
                        <div className="flex items-center gap-2">
                            <ClipboardDocumentListIcon className="w-4 h-4 text-primary" />
                            <span className="font-bold text-sm">
                                {order.cutOrder ||
                                    `ORD-${order.id.substring(0, 6)}`}
                            </span>
                            {(isOverdue || isUrgent) && (
                                <Tooltip
                                    content={
                                        isOverdue
                                            ? "Orden retrasada"
                                            : "Urgente"
                                    }
                                >
                                    <ExclamationTriangleIcon
                                        className={`w-4 h-4 ${isOverdue ? "text-danger" : "text-warning"}`}
                                    />
                                </Tooltip>
                            )}
                        </div>
                        <div className="flex flex-col">
                            <span className="text-xs text-gray-500">
                                {order.customer?.name || "Sin cliente"}
                            </span>
                            {order.subCustomer && (
                                <span className="text-xs text-gray-400">
                                    → {order.subCustomer.name}
                                </span>
                            )}
                        </div>
                    </div>
                </div>
            );
        },
    },
    {
        key: "contractors",
        label: "Contratistas",
        render: (order: Order) => {
            if (!order.assignments || order.assignments.length === 0) {
                return (
                    <span className="text-gray-400 text-sm">Sin asignar</span>
                );
            }

            const contractorsWithQuantity = order.assignments.map((a) => ({
                name: a.contractor?.name || "Sin nombre",
                quantity: a.quantity || 0,
            }));

            if (contractorsWithQuantity.length <= 2) {
                return (
                    <div className="flex flex-wrap gap-1">
                        {contractorsWithQuantity.map((contractor, i) => (
                            <Chip
                                key={i}
                                className="text-xs"
                                size="sm"
                                variant="flat"
                            >
                                {contractor.name} ({contractor.quantity})
                            </Chip>
                        ))}
                    </div>
                );
            }

            const totalQuantity = contractorsWithQuantity.reduce(
                (sum, c) => sum + c.quantity,
                0,
            );

            return (
                <Tooltip
                    content={
                        <div className="space-y-1">
                            <p className="font-medium mb-2">
                                Todos los contratistas:
                            </p>
                            {contractorsWithQuantity.map((contractor, i) => (
                                <div
                                    key={i}
                                    className="flex justify-between gap-4"
                                >
                                    <span>{contractor.name}</span>
                                    <span className="font-mono text-sm">
                                        {contractor.quantity} pzs
                                    </span>
                                </div>
                            ))}
                            <div className="pt-2 mt-2 border-t border-gray-200">
                                <div className="flex justify-between gap-4 font-medium">
                                    <span>Total:</span>
                                    <span className="font-mono">
                                        {totalQuantity} pzs
                                    </span>
                                </div>
                            </div>
                        </div>
                    }
                >
                    <div className="flex flex-wrap gap-1 cursor-help">
                        {contractorsWithQuantity
                            .slice(0, 2)
                            .map((contractor, i) => (
                                <Chip
                                    key={i}
                                    className="text-xs"
                                    size="sm"
                                    variant="flat"
                                >
                                    {contractor.name} ({contractor.quantity})
                                </Chip>
                            ))}
                        <Chip
                            className="text-xs"
                            color="default"
                            size="sm"
                            variant="flat"
                        >
                            +{contractorsWithQuantity.length - 2}
                        </Chip>
                    </div>
                </Tooltip>
            );
        },
    },
    {
        key: "assignment",
        label: "Asignación",
        render: (order: Order) => {
            const totalPieces = calculateTotalPieces(order);
            const totalAssigned = calculateTotalAssigned(order);
            const percentage = calculateAssignmentPercentage(order);
            const color = getProgressColor(percentage);

            return (
                <div className="space-y-1 min-w-[100px]">
                    <Progress
                        className="max-w-md"
                        color={color}
                        size="sm"
                        value={percentage}
                    />
                    <div className="flex justify-between text-xs">
                        <span className="text-gray-600">
                            {totalAssigned}/{totalPieces} pzs
                        </span>
                        <span
                            className={`font-medium ${
                                color === "success"
                                    ? "text-success"
                                    : color === "warning"
                                      ? "text-warning"
                                      : "text-danger"
                            }`}
                        >
                            {percentage}%
                        </span>
                    </div>
                </div>
            );
        },
    },
    {
        key: "product",
        label: "Producto",
        render: (order: Order) => {
            if (!order.garments || order.garments.length === 0) {
                return (
                    <div className="flex flex-col">
                        <span className="text-gray-400 text-sm">
                            Sin modelo
                        </span>
                    </div>
                );
            }

            const firstGarment = order.garments[0];
            const modelCode = firstGarment?.model?.code || "Sin código";
            const colorName =
                firstGarment?.color?.name || "Color no disponible";

            return (
                <div className="flex flex-col">
                    <span className="font-medium text-sm">{modelCode}</span>
                    <span className="text-xs text-gray-500">{colorName}</span>
                </div>
            );
        },
    },
    {
        key: "parts",
        label: "Partidas",
        render: (order: Order) => {
            const partCodes =
                order.parts?.map((p) => p.code).filter(Boolean) || [];

            if (partCodes.length === 0)
                return <span className="text-gray-400">-</span>;

            if (partCodes.length > 3) {
                return (
                    <Tooltip
                        content={
                            <div className="max-w-xs">
                                <p className="font-medium mb-1">
                                    Todas las partidas:
                                </p>
                                <div className="flex flex-wrap gap-1">
                                    {partCodes.map((code, i) => (
                                        <span
                                            key={i}
                                            className="bg-gray-700 px-1.5 py-0.5 rounded text-xs"
                                        >
                                            {code}
                                        </span>
                                    ))}
                                </div>
                            </div>
                        }
                    >
                        <div className="cursor-help">
                            <div className="flex flex-wrap gap-1">
                                {partCodes.slice(0, 3).map((code, i) => (
                                    <span
                                        key={i}
                                        className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono"
                                    >
                                        {code}
                                    </span>
                                ))}
                                <span className="text-xs text-gray-500">
                                    +{partCodes.length - 3}
                                </span>
                            </div>
                        </div>
                    </Tooltip>
                );
            }

            return (
                <div className="flex flex-wrap gap-1">
                    {partCodes.map((code, i) => (
                        <span
                            key={i}
                            className="bg-gray-100 px-1.5 py-0.5 rounded text-xs font-mono"
                        >
                            {code}
                        </span>
                    ))}
                </div>
            );
        },
    },
    {
        key: "deliveryDate",
        label: "Entrega",
        sortable: true,
        render: (order: Order) => {
            const deliveryDate =
                order.estimatedDeliveryDate ||
                (order as ExtendedOrder).deliveryDate;

            if (!deliveryDate)
                return <span className="text-gray-400 text-sm">Sin fecha</span>;

            const date = adjustTimezoneOffset(deliveryDate);

            if (!date)
                return (
                    <span className="text-gray-400 text-sm">
                        Fecha inválida
                    </span>
                );

            const daysLeft = getDaysDifference(date);
            const isOverdue = daysLeft < 0 && order.status !== "delivered";
            const isUrgent = daysLeft <= 3 && order.status !== "delivered";

            return (
                <div className="flex items-center gap-2">
                    <CalendarIcon
                        className={`w-4 h-4 ${isOverdue ? "text-danger" : isUrgent ? "text-warning" : "text-gray-500"}`}
                    />
                    <div>
                        <span
                            className={`text-sm font-medium ${isOverdue ? "text-danger" : ""}`}
                        >
                            {format(date, "dd MMM", { locale: es })}
                        </span>
                        {order.status !== "delivered" &&
                            order.status !== "cancelled" && (
                                <div
                                    className={`text-xs ${isOverdue ? "text-danger font-bold" : isUrgent ? "text-warning" : "text-gray-500"}`}
                                >
                                    {isOverdue
                                        ? `${Math.abs(daysLeft)}d retraso`
                                        : daysLeft === 0
                                          ? "Hoy"
                                          : daysLeft === 1
                                            ? "Mañana"
                                            : `${daysLeft}d`}
                                </div>
                            )}
                    </div>
                </div>
            );
        },
    },
    {
        key: "risk",
        label: "Riesgo",
        sortable: true,
        render: (order: Order) => {
            const riskAssessment = calculateRiskScore(order);

            return (
                <RiskIndicator
                    assessment={riskAssessment}
                    showDetails={false}
                    size="sm"
                />
            );
        },
    },
    {
        key: "status",
        label: "Estado",
        render: (order: Order) => {
            const config = getStatusConfig(order.status);
            const statusColors: Record<string, any> = {
                pending: "warning",
                in_progress: "primary",
                in_production: "secondary",
                delivered: "success",
                cancelled: "danger",
            };

            const statusKey =
                typeof order.status === "string"
                    ? order.status
                    : order.status?.name || order.status?.id || "";

            return (
                <Chip
                    color={statusColors[statusKey] || "default"}
                    size="sm"
                    startContent={
                        config.icon ? (
                            <config.icon className="w-4 h-4" />
                        ) : undefined
                    }
                    variant="flat"
                >
                    {config.label}
                </Chip>
            );
        },
    },
];
