"use client";

import React from "react";
import {
    ExclamationTriangleIcon,
    InformationCircleIcon,
} from "@heroicons/react/24/outline";

import {
    Card,
    CardBody,
    Progress,
    Chip,
    Tooltip,
} from "@/shared/components/ui/hero-ui-client";

import { RiskAssessment } from "../utils/riskCalculator";

interface RiskIndicatorProps {
    assessment: RiskAssessment;
    size?: "sm" | "md" | "lg";
    showDetails?: boolean;
}

export default function RiskIndicator({
    assessment,
    size = "md",
    showDetails = true,
}: RiskIndicatorProps) {
    const { score, level, factors, recommendations } = assessment;

    const levelConfig = {
        low: { color: "success" as const, label: "Bajo", icon: "✓" },
        medium: { color: "warning" as const, label: "Medio", icon: "!" },
        high: { color: "danger" as const, label: "Alto", icon: "!!" },
        critical: { color: "danger" as const, label: "<PERSON><PERSON><PERSON><PERSON><PERSON>", icon: "!!!" },
    };

    const config = levelConfig[level];

    const progressColor =
        score >= 70
            ? "danger"
            : score >= 50
              ? "warning"
              : score >= 30
                ? "primary"
                : "success";

    if (size === "sm") {
        return (
            <div className="flex items-center gap-2">
                <Chip color={config.color} size="sm" variant="flat">
                    {config.icon} {score}
                </Chip>
                {showDetails && recommendations.length > 0 && (
                    <Tooltip
                        content={
                            <div className="max-w-xs">
                                <p className="font-medium mb-1">
                                    Nivel de Riesgo: {config.label}
                                </p>
                                <p className="text-xs">{recommendations[0]}</p>
                            </div>
                        }
                        placement="top"
                    >
                        <button className="text-gray-400 hover:text-gray-600 transition-colors">
                            <InformationCircleIcon className="w-4 h-4" />
                        </button>
                    </Tooltip>
                )}
            </div>
        );
    }

    return (
        <Card className={`${size === "lg" ? "w-full" : "max-w-sm"}`}>
            <CardBody className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                        <ExclamationTriangleIcon className="w-5 h-5" />
                        Score de Riesgo
                    </h3>
                    <Chip
                        color={config.color}
                        size={size === "lg" ? "md" : "sm"}
                        variant="flat"
                    >
                        {config.label}
                    </Chip>
                </div>

                <div className="space-y-2">
                    <div className="flex items-center justify-between">
                        <span className="text-3xl font-bold">{score}</span>
                        <span className="text-sm text-gray-500">/ 100</span>
                    </div>
                    <Progress
                        className="h-2"
                        color={progressColor}
                        value={score}
                    />
                </div>

                {showDetails && (
                    <>
                        <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-600">
                                Factores de Riesgo
                            </h4>
                            <div className="space-y-1">
                                <RiskFactorBar
                                    label="Entrega"
                                    max={40}
                                    value={factors.deliveryRisk}
                                />
                                <RiskFactorBar
                                    label="Capacidad"
                                    max={30}
                                    value={factors.capacityRisk}
                                />
                                <RiskFactorBar
                                    label="Cliente"
                                    max={20}
                                    value={factors.customerRisk}
                                />
                                <RiskFactorBar
                                    label="Complejidad"
                                    max={10}
                                    value={factors.complexityRisk}
                                />
                            </div>
                        </div>

                        {recommendations.length > 0 && (
                            <div className="space-y-2">
                                <h4 className="text-sm font-medium text-gray-600">
                                    Recomendaciones
                                </h4>
                                <ul className="space-y-1">
                                    {recommendations
                                        .slice(0, 3)
                                        .map((rec, index) => (
                                            <li
                                                key={index}
                                                className="text-xs text-gray-500 flex items-start gap-1"
                                            >
                                                <span className="text-primary">
                                                    •
                                                </span>
                                                <span>{rec}</span>
                                            </li>
                                        ))}
                                </ul>
                            </div>
                        )}
                    </>
                )}
            </CardBody>
        </Card>
    );
}

function RiskFactorBar({
    label,
    value,
    max,
}: {
    label: string;
    value: number;
    max: number;
}) {
    const percentage = (value / max) * 100;
    const color =
        percentage >= 75
            ? "danger"
            : percentage >= 50
              ? "warning"
              : percentage >= 25
                ? "primary"
                : "success";

    return (
        <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500 w-20">{label}</span>
            <div className="flex-1 relative">
                <Progress
                    className="h-1"
                    color={color}
                    size="sm"
                    value={percentage}
                />
            </div>
            <span className="text-xs text-gray-500 w-8 text-right">
                {value}
            </span>
        </div>
    );
}
