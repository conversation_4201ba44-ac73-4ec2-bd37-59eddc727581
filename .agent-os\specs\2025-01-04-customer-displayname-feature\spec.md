# Especificación: Agregar campo displayName a la gestión de clientes principales

## Fecha
2025-01-04

## Estado
Pendiente

## Resumen
Implementar la funcionalidad de `displayName` para clientes principales (no solo subclientes) en las interfaces de creación y edición de clientes. Actualmente, el campo `displayName` ya existe en el esquema de Prisma pero solo está implementado en la gestión de subclientes.

## Contexto
- El campo `displayName` ya existe en el modelo `Customer` del esquema Prisma como campo opcional
- Los subclientes ya tienen implementada la funcionalidad de `displayName`
- Los clientes principales solo pueden gestionar el campo `name`
- La UI ya sabe cómo mostrar `displayName` (CustomerHierarchy muestra `displayName || name`)

## Objetivos
1. Agregar el campo `displayName` a los formularios de creación y edición de clientes principales
2. Actualizar las acciones del servidor para manejar `displayName`
3. Actualizar los tipos TypeScript para incluir `displayName`
4. Mantener consistencia con la implementación existente de subclientes

## Implementación Técnica

### 1. Actualizar tipos y esquemas

#### Actualizar Customer interface
```typescript
// features/customers/types/index.ts
export interface Customer {
    id: string;
    name: string;
    displayName?: string | null;  // AGREGAR
    email?: string | null;
    phone?: string | null;
    createdAt: string;
    updatedAt: string;
    _count?: {
        orders?: number;
        packings?: number;
    };
}
```

#### Actualizar schema de validación
```typescript
// features/customers/actions/schema.ts
export const customerSchema = z.object({
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres"),
    displayName: z  // AGREGAR
        .string()
        .max(100, "El nombre para mostrar no puede exceder los 100 caracteres")
        .optional()
        .nullable(),
});
```

### 2. Actualizar acciones del servidor

#### Modificar createCustomer
```typescript
// features/customers/actions/create.ts
export async function createCustomer(
    data: { name: string; displayName?: string | null },  // ACTUALIZAR tipo
    forceCreate = false,
) {
    // ... validación existente ...
    
    const customer = await db.customer.create({
        data: { 
            name: validatedData.name,
            displayName: validatedData.displayName || null,  // AGREGAR
        },
    });
    
    // ... resto del código ...
}
```

#### Modificar updateCustomer
```typescript
// features/customers/actions/update.ts
export async function updateCustomer(
    id: string,
    data: { name: string; displayName?: string | null },  // ACTUALIZAR tipo
    forceUpdate = false,
) {
    // ... validación existente ...
    
    const customer = await db.customer.update({
        where: { id },
        data: { 
            name: validatedData.name,
            displayName: validatedData.displayName || null,  // AGREGAR
        },
    });
    
    // ... resto del código ...
}
```

### 3. Actualizar formulario de creación

#### Agregar campo displayName al formulario
```typescript
// app/dashboard/customers/new/page.tsx

// Actualizar schema
const customerSchema = z.object({
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres")
        .transform((val) => val.trim()),
    displayName: z  // AGREGAR
        .string()
        .max(100, "El nombre para mostrar no puede exceder los 100 caracteres")
        .transform((val) => val?.trim() || null)
        .optional()
        .nullable(),
});

// Actualizar defaultValues en useForm
defaultValues: {
    name: "",
    displayName: "",  // AGREGAR
},

// Agregar campo en el formulario después del campo name
<Textarea
    {...register("displayName")}
    label="Nombre para Mostrar (Opcional)"
    placeholder="Ej: Empresa ABC - División XYZ"
    description="Cómo se mostrará en la interfaz. Si no se especifica, se usará el nombre."
    classNames={{
        base: "max-w-full",
        inputWrapper: `
            bg-transparent shadow-sm border-2 transition-all duration-200
            data-[hover=true]:border-primary-500/50 
            data-[focus=true]:border-primary-500
        `,
        input: "text-base",
    }}
    minRows={2}
    maxRows={3}
    variant="bordered"
/>

// Actualizar la llamada a createCustomer
const result = await createCustomer({
    name: data.name,
    displayName: data.displayName || null,
}, bypassValidation);
```

### 4. Actualizar formulario de edición

#### Agregar campo displayName al formulario
```typescript
// app/dashboard/customers/[id]/edit/page.tsx

// Actualizar schema (mismo que en creación)
// Actualizar setValue cuando se cargan los datos
useEffect(() => {
    if (customer) {
        setValue("name", (customer as any).name);
        setValue("displayName", (customer as any).displayName || "");  // AGREGAR
    }
}, [customer, setValue]);

// Agregar campo en el formulario (mismo componente que en creación)
// Actualizar la llamada a updateCustomer
const result = await updateCustomer(
    customerId,
    { 
        name: trimmedName,
        displayName: data.displayName || null,  // AGREGAR
    },
    false,
);
```

### 5. Actualizar consultas para incluir displayName

#### Verificar que las queries incluyan displayName
```typescript
// features/customers/actions/query.ts
// Asegurar que todas las consultas de clientes incluyan displayName
const customers = await db.customer.findMany({
    select: {
        id: true,
        name: true,
        displayName: true,  // VERIFICAR que esté incluido
        // ... otros campos
    },
});
```

## Consideraciones de UI/UX

1. **Consistencia**: Usar el mismo componente Textarea que en subcustomers
2. **Ubicación**: Colocar el campo displayName justo después del campo name
3. **Etiquetas**: Usar las mismas etiquetas y descripciones que en subcustomers
4. **Opcional**: Mantener el campo como opcional, no requerido

## Plan de pruebas

1. Crear un cliente con solo name (sin displayName)
2. Crear un cliente con name y displayName
3. Editar un cliente existente agregando displayName
4. Editar un cliente existente modificando displayName
5. Verificar que CustomerHierarchy muestre correctamente displayName
6. Verificar que las listas de clientes muestren displayName cuando esté disponible

## Migraciones de base de datos

No se requieren migraciones ya que el campo `displayName` ya existe en el esquema Prisma.

## Tareas de implementación

1. [ ] Actualizar Customer interface en types/index.ts
2. [ ] Actualizar customerSchema en actions/schema.ts
3. [ ] Modificar createCustomer action para aceptar displayName
4. [ ] Modificar updateCustomer action para aceptar displayName
5. [ ] Agregar campo displayName al formulario de creación
6. [ ] Agregar campo displayName al formulario de edición
7. [ ] Verificar que las queries incluyan displayName
8. [ ] Probar todos los flujos de creación y edición
9. [ ] Verificar que la UI muestre displayName correctamente

## Notas adicionales

- El comportamiento debe ser idéntico al de los subclientes
- Si no se proporciona displayName, la UI debe mostrar el name
- El campo es opcional y puede dejarse vacío
- Limitar displayName a 100 caracteres para permitir nombres descriptivos más largos