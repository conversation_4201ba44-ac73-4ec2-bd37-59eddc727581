"use client";

import { useState } from "react";
import useS<PERSON> from "swr";

import {
    getContractorsForSelector,
    getAvailableOrders,
    getOrderDetailsForAssignment,
    checkGarmentSizeAvailability,
    createAssignments,
    validateAssignmentAvailability,
    QuantityAssignment,
} from "@/features/assignments/actions";

/**
 * Hook para gestionar asignaciones de trabajo a contratistas
 */
export function useAssignments() {
    // Estado para el wizards
    const [contractorId, setContractorId] = useState<string>("");
    const [selectedOrderIds, setSelectedOrderIds] = useState<string[]>([]);
    const [assignments, setAssignments] = useState<QuantityAssignment[]>([]);
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [page, setPage] = useState<number>(1);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [validationErrors, setValidationErrors] = useState<
        Record<string, string>
    >({});

    // Carga de contratistas
    const {
        data: contractorsData,
        error: contractorsError,
        isLoading: isLoadingContractors,
    } = useSWR("contractors-selector", () => getContractorsForSelector());

    // Carga de órdenes disponibles
    const {
        data: ordersData,
        error: ordersError,
        isLoading: isLoadingOrders,
        mutate: reloadOrders,
    } = useSWR(
        ["available-orders", searchTerm, page],
        () =>
            getAvailableOrders({
                search: searchTerm,
                page,
                perPage: 10,
                onlyWithAvailability: true,
            }),
        {
            revalidateOnFocus: false,
            revalidateIfStale: false,
        },
    );

    // Carga de detalles de órdenes seleccionadas
    const { data: orderDetailsMapData, isLoading: isLoadingOrderDetails } =
        useSWR(
            selectedOrderIds.length > 0
                ? ["order-details", selectedOrderIds.join(",")]
                : null,
            async () => {
                const orderDetailsMap: Record<string, any> = {};

                await Promise.all(
                    selectedOrderIds.map(async (orderId) => {
                        const details =
                            await getOrderDetailsForAssignment(orderId);

                        if (details.success) {
                            orderDetailsMap[orderId] = details.data;
                        }
                    }),
                );

                return orderDetailsMap;
            },
            {
                revalidateOnFocus: false,
            },
        );

    // Selección de contratista
    const selectContractor = (id: string) => {
        setContractorId(id);
    };

    // Selección de órdenes
    const toggleOrderSelection = (orderId: string) => {
        setSelectedOrderIds((prev) => {
            if (prev.includes(orderId)) {
                return prev.filter((id) => id !== orderId);
            } else {
                return [...prev, orderId];
            }
        });
    };

    // Búsqueda de órdenes
    const searchOrders = (term: string) => {
        setSearchTerm(term);
        setPage(1);
    };

    // Cambio de página
    const changePage = (newPage: number) => {
        setPage(newPage);
    };

    // Actualizar cantidad asignada
    const updateAssignmentQuantity = async (assignment: QuantityAssignment) => {
        // Limpiar error previo para esta asignación
        setValidationErrors((prev) => {
            const newErrors = { ...prev };

            delete newErrors[assignment.garmentSizeId];

            return newErrors;
        });

        // Verificar si ya existe la asignación
        const existingIndex = assignments.findIndex(
            (a) => a.garmentSizeId === assignment.garmentSizeId,
        );

        if (existingIndex >= 0) {
            // Actualizar existente
            const updatedAssignments = [...assignments];

            updatedAssignments[existingIndex] = assignment;
            setAssignments(updatedAssignments);
        } else {
            // Agregar nueva
            setAssignments((prev) => [...prev, assignment]);
        }

        // Validar disponibilidad
        if (assignment.quantity > 0) {
            const validation = await validateAssignmentAvailability(
                assignment.garmentSizeId,
                assignment.quantity,
            );

            if (!validation.success) {
                setValidationErrors((prev) => ({
                    ...prev,
                    [assignment.garmentSizeId]:
                        validation.error || "Error de validación",
                }));
            }
        }
    };

    // Remover asignación
    const removeAssignment = (garmentSizeId: string) => {
        setAssignments((prev) =>
            prev.filter((a) => a.garmentSizeId !== garmentSizeId),
        );

        // Limpiar error si existe
        setValidationErrors((prev) => {
            const newErrors = { ...prev };

            delete newErrors[garmentSizeId];

            return newErrors;
        });
    };

    // Verificar disponibilidad en tiempo real
    const checkAvailability = async (garmentSizeId: string) => {
        const result = await checkGarmentSizeAvailability(garmentSizeId);

        return result.success ? result.data : null;
    };

    // Enviar formulario
    const submitAssignments = async () => {
        if (!contractorId || assignments.length === 0) {
            return {
                success: false,
                error: "Faltan datos requeridos",
            };
        }

        setIsSubmitting(true);

        try {
            // Preparar datos para enviar
            const requestData = {
                contractorId,
                assignments: assignments.map((a) => ({
                    orderId: a.orderId,
                    garmentSizeId: a.garmentSizeId,
                    quantity: a.quantity,
                })),
            };

            // Enviar al servidor
            const result = await createAssignments(requestData);

            if (result.success) {
                // Limpiar el formulario
                setSelectedOrderIds([]);
                setAssignments([]);
                setValidationErrors({});

                // Recargar órdenes para actualizar disponibilidad
                reloadOrders();
            } else if (result.data?.errors) {
                // Marcar errores específicos
                const newErrors: Record<string, string> = {};

                result.data.errors.forEach((error: any) => {
                    if (error.details?.garmentSizeId) {
                        newErrors[error.details.garmentSizeId] = error.error;
                    }
                });

                setValidationErrors(newErrors);
            }

            return result;
        } catch (error) {
            return {
                success: false,
                error: "Error al enviar las asignaciones",
            };
        } finally {
            setIsSubmitting(false);
        }
    };

    // Validar asignaciones antes de enviar
    const validateAssignments = (): boolean => {
        // Verificar que haya un contratista seleccionado
        if (!contractorId) {
            return false;
        }

        // Verificar que haya al menos una asignación
        if (assignments.length === 0) {
            return false;
        }

        // Verificar que no haya errores de validación
        if (Object.keys(validationErrors).length > 0) {
            return false;
        }

        return true;
    };

    return {
        // Estados
        contractorId,
        selectedOrderIds,
        assignments,
        searchTerm,
        page,
        isSubmitting,
        validationErrors,

        // Datos
        contractors: (contractorsData as any)?.success
            ? (contractorsData as any).data
            : [],
        orders: (ordersData as any)?.success
            ? (ordersData as any).data.orders
            : [],
        pagination: (ordersData as any)?.success
            ? (ordersData as any).data.pagination
            : { total: 0, page, perPage: 10, lastPage: 1 },
        orderDetailsMap: orderDetailsMapData || {},

        // Loading states
        isLoadingContractors,
        isLoadingOrders,
        isLoadingOrderDetails,

        // Errores
        contractorsError,
        ordersError,

        // Acciones
        selectContractor,
        toggleOrderSelection,
        searchOrders,
        changePage,
        updateAssignmentQuantity,
        removeAssignment,
        checkAvailability,
        submitAssignments,
        validateAssignments,
    };
}
