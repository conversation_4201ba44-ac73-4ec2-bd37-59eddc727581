import React from "react";
import {
    UserIcon,
    EnvelopeIcon,
    PhoneIcon,
    CalendarIcon,
    ClipboardDocumentCheckIcon,
    TruckIcon,
    CurrencyDollarIcon,
    ChartBarIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

import {
    Card,
    CardBody,
    Progress,
} from "@/shared/components/ui/hero-ui-client";

import { ContractorWithDetails } from "../types/contractor-modal.types";

interface ContractorInfoSectionProps {
    contractor: ContractorWithDetails;
}

export default function ContractorInfoSection({
    contractor,
}: ContractorInfoSectionProps) {
    const getFullName = () => {
        const parts = [
            contractor.firstName,
            contractor.middleName,
            contractor.lastName,
            contractor.secondLastName,
        ].filter(Boolean);

        return parts.length > 0 ? parts.join(" ") : contractor.name;
    };

    // Calcular métricas
    const totalAssignments = contractor._count?.assignments || 0;
    const totalRemissions = contractor._count?.remissions || 0;
    const completionRate =
        totalAssignments > 0 ? (totalRemissions / totalAssignments) * 100 : 0;

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.3,
            },
        },
    };

    return (
        <motion.div
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 gap-6"
            initial="hidden"
            variants={containerVariants}
        >
            {/* Información Personal */}
            <motion.div variants={itemVariants}>
                <Card className="h-full">
                    <CardBody className="space-y-4">
                        <h3 className="text-lg font-semibold flex items-center gap-2">
                            <UserIcon className="w-5 h-5 text-primary" />
                            Información Personal
                        </h3>

                        <div className="space-y-3">
                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    Nombre completo
                                </p>
                                <p className="font-medium">{getFullName()}</p>
                            </div>

                            {contractor.email && (
                                <div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Correo electrónico
                                    </p>
                                    <a
                                        className="font-medium text-primary hover:underline flex items-center gap-1"
                                        href={`mailto:${contractor.email}`}
                                    >
                                        <EnvelopeIcon className="w-4 h-4" />
                                        {contractor.email}
                                    </a>
                                </div>
                            )}

                            {contractor.phone && (
                                <div>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Teléfono
                                    </p>
                                    <a
                                        className="font-medium text-primary hover:underline flex items-center gap-1"
                                        href={`tel:${contractor.phone}`}
                                    >
                                        <PhoneIcon className="w-4 h-4" />
                                        {contractor.phone}
                                    </a>
                                </div>
                            )}

                            <div>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                    Fecha de registro
                                </p>
                                <p className="font-medium flex items-center gap-1">
                                    <CalendarIcon className="w-4 h-4" />
                                    {format(
                                        new Date(contractor.createdAt),
                                        "PPP",
                                        { locale: es },
                                    )}
                                </p>
                                <p className="text-sm text-gray-500">
                                    {formatDistanceToNow(
                                        new Date(contractor.createdAt),
                                        { addSuffix: true, locale: es },
                                    )}
                                </p>
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Estadísticas */}
            <motion.div variants={itemVariants}>
                <Card className="h-full">
                    <CardBody className="space-y-4">
                        <h3 className="text-lg font-semibold flex items-center gap-2">
                            <ChartBarIcon className="w-5 h-5 text-primary" />
                            Estadísticas
                        </h3>

                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <ClipboardDocumentCheckIcon className="w-5 h-5 text-blue-600" />
                                    <span className="text-sm">
                                        Asignaciones totales
                                    </span>
                                </div>
                                <span className="text-2xl font-bold text-blue-600">
                                    {totalAssignments}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <TruckIcon className="w-5 h-5 text-green-600" />
                                    <span className="text-sm">
                                        Remisiones completadas
                                    </span>
                                </div>
                                <span className="text-2xl font-bold text-green-600">
                                    {totalRemissions}
                                </span>
                            </div>

                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm">
                                        Tasa de completado
                                    </span>
                                    <span className="text-sm font-medium">
                                        {completionRate.toFixed(1)}%
                                    </span>
                                </div>
                                <Progress
                                    className="h-2"
                                    color={
                                        completionRate >= 80
                                            ? "success"
                                            : completionRate >= 50
                                              ? "warning"
                                              : "danger"
                                    }
                                    value={completionRate}
                                />
                            </div>
                        </div>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Resumen de Actividad */}
            <motion.div className="md:col-span-2" variants={itemVariants}>
                <Card>
                    <CardBody>
                        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
                            <CurrencyDollarIcon className="w-5 h-5 text-primary" />
                            Resumen de Actividad
                        </h3>

                        {contractor.assignments &&
                        contractor.assignments.length > 0 ? (
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <p className="text-3xl font-bold text-primary">
                                        {
                                            contractor.assignments.filter(
                                                (a) => a.status === "PENDING",
                                            ).length
                                        }
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        Asignaciones Pendientes
                                    </p>
                                </div>

                                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <p className="text-3xl font-bold text-warning">
                                        {
                                            contractor.assignments.filter(
                                                (a) =>
                                                    a.status === "IN_PROGRESS",
                                            ).length
                                        }
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        En Proceso
                                    </p>
                                </div>

                                <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                    <p className="text-3xl font-bold text-success">
                                        {
                                            contractor.assignments.filter(
                                                (a) => a.status === "COMPLETED",
                                            ).length
                                        }
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        Completadas
                                    </p>
                                </div>
                            </div>
                        ) : (
                            <p className="text-center text-gray-500 dark:text-gray-400 py-8">
                                No hay asignaciones registradas
                            </p>
                        )}
                    </CardBody>
                </Card>
            </motion.div>
        </motion.div>
    );
}
