import React from "react";
import { cn } from "@heroui/react";
import {
    CheckCircleIcon,
    ClockIcon,
    CalendarIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

import {
    formatDateForDisplay,
    getDateStatusClasses,
    adjustTimezoneOffset,
    DateDisplayType,
} from "@/shared/utils";

interface Order {
    id: string;
    estimatedDeliveryDate: string | Date | null;
    parts?: Array<{ code: string; [key: string]: any }>;
}

interface UpcomingDeliveryCardProps {
    order: Order;
    className?: string;
    onClick?: (order: Order) => void;
}

/**
 * Componente para mostrar una tarjeta de entrega próxima
 * Muestra información visual sobre la proximidad de la entrega
 */
export default function UpcomingDeliveryCard({
    order,
    className,
    onClick,
}: UpcomingDeliveryCardProps) {
    // Ajustar la fecha para compensar el desfase de zona horaria
    const adjustedDate = adjustTimezoneOffset(order.estimatedDeliveryDate);

    // Obtener información formateada de la fecha
    const dateInfo = formatDateForDisplay(
        adjustedDate ? adjustedDate : order.estimatedDeliveryDate,
        "dd MMM",
        DateDisplayType.estimated,
    );

    // Manejar el clic en la tarjeta
    const handleClick = () => {
        if (onClick) onClick(order);
    };

    // Manejar eventos de teclado para accesibilidad
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            if (onClick) onClick(order);
        }
    };

    // Si la fecha no es válida, mostrar un mensaje de error
    if (!dateInfo.isValid) {
        return (
            <div
                aria-label={`Orden con fecha inválida: ${order.id}`}
                className={cn(
                    "flex items-center gap-2 p-1.5 rounded-md bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 my-1 shadow-sm cursor-pointer hover:shadow transition-all",
                    className,
                )}
                role="button"
                tabIndex={0}
                onClick={handleClick}
                onKeyDown={handleKeyDown}
            >
                <ExclamationCircleIcon className="w-4 h-4 text-red-500 flex-shrink-0" />
                <div className="flex flex-col">
                    <span className="text-red-600 dark:text-red-400 text-xs font-medium">
                        Formato inválido
                    </span>
                    <span className="text-[10px] text-red-500 truncate">
                        {order.parts && order.parts.length > 0
                            ? `Partida: ${order.parts[0].code}`
                            : `Orden ${order.id.substring(0, 6)}...`}
                    </span>
                </div>
            </div>
        );
    }

    // Obtener las clases CSS según el estado
    const { bgClass, textClass } = getDateStatusClasses(
        dateInfo.statusType,
        DateDisplayType.estimated,
    );

    // Determinar el icono según el estado
    let iconComponent;

    switch (dateInfo.statusType) {
        case "past":
            iconComponent = (
                <ExclamationCircleIcon className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" />
            );
            break;
        case "today":
            iconComponent = (
                <CheckCircleIcon className="w-4 h-4 text-amber-600 dark:text-amber-400 flex-shrink-0" />
            );
            break;
        case "tomorrow":
        case "soon":
            iconComponent = (
                <ClockIcon className="w-4 h-4 text-orange-600 dark:text-orange-400 flex-shrink-0" />
            );
            break;
        default:
            iconComponent = (
                <CalendarIcon className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
            );
    }

    // Obtener el código de la partida si existe
    const partCode =
        order.parts && order.parts.length > 0
            ? order.parts[0].code
            : order.id.substring(0, 6) + "...";

    return (
        <div
            aria-label={`Entrega programada para ${dateInfo.formattedDate}: ${partCode}`}
            className={cn(
                `flex items-center gap-2 p-1.5 rounded-md ${bgClass} my-1 shadow-sm hover:shadow-md transition-all cursor-pointer`,
                className,
            )}
            role="button"
            tabIndex={0}
            onClick={handleClick}
            onKeyDown={handleKeyDown}
        >
            {iconComponent}
            <div className="flex flex-col">
                <span className={`${textClass} text-xs font-medium`}>
                    {dateInfo.formattedDate} - {dateInfo.relativeText}
                </span>
                <span className={`text-[10px] ${textClass} font-mono truncate`}>
                    {partCode}
                </span>
            </div>
        </div>
    );
}
