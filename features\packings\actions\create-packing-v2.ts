"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { customAlphabet } from "nanoid";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/lib/db";

import { generatePackingFolio } from "./generate-folio";

const generateTrackingNumber = customAlphabet(
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
    10,
);

const createPackingSchemaV2 = z.object({
    customerId: z.string().min(1, "El cliente es requerido"),
    subCustomerId: z.string().optional(),
    orderId: z.string().optional(),
    deliveryDate: z.date(),
    notes: z.string().optional(),

    // Información de transporte
    transporterId: z.string().optional(),
    vehicleInfo: z.string().optional(),
    driverName: z.string().optional(),
    driverPhone: z.string().optional(),

    // Información adicional
    totalWeight: z.number().optional(),
    totalVolume: z.number().optional(),
    packagesCount: z.number().int().positive().default(1),

    details: z
        .array(
            z.object({
                garmentSizeId: z.string(),
                quantity: z.number().int().positive(),
                comments: z.string().optional(),
                boxNumber: z.number().int().optional(),
                weight: z.number().optional(),
            }),
        )
        .min(1, "Debe incluir al menos un artículo"),
});

export async function createPackingV2(
    data: z.infer<typeof createPackingSchemaV2>,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = createPackingSchemaV2.parse(data);

        // Verificar que el cliente existe
        const customer = await db.customer.findUnique({
            where: { id: validatedData.customerId },
        });

        if (!customer) {
            return { success: false, error: "Cliente no encontrado" };
        }

        // Verificar subcliente si se proporciona
        if (validatedData.subCustomerId) {
            const subCustomer = await db.customer.findUnique({
                where: {
                    id: validatedData.subCustomerId,
                    parentId: validatedData.customerId,
                },
            });

            if (!subCustomer) {
                return { success: false, error: "Subcliente no válido" };
            }
        }

        // Verificar disponibilidad de inventario
        const inventoryChecks = await Promise.all(
            validatedData.details.map(async (detail) => {
                const garmentSize = await db.garmentSize.findUnique({
                    where: { id: detail.garmentSizeId },
                    include: {
                        garment: {
                            include: {
                                model: true,
                                color: true,
                                order: true,
                            },
                        },
                        size: true,
                    },
                });

                if (!garmentSize) {
                    return {
                        valid: false,
                        error: `Talla no encontrada: ${detail.garmentSizeId}`,
                    };
                }

                const availableQuantity =
                    garmentSize.totalQuantity - garmentSize.usedQuantity;

                if (detail.quantity > availableQuantity) {
                    return {
                        valid: false,
                        error: `Cantidad insuficiente para ${garmentSize.garment.model.code} ${garmentSize.garment.color.name} ${garmentSize.size.code}. Disponible: ${availableQuantity}`,
                    };
                }

                return { valid: true, garmentSize };
            }),
        );

        // Verificar si hay errores de inventario
        const inventoryError = inventoryChecks.find((check) => !check.valid);

        if (inventoryError) {
            return { success: false, error: inventoryError.error };
        }

        // Generar folio único y tracking number
        const folio = await generatePackingFolio();
        const trackingNumber = `LH-${generateTrackingNumber()}`;

        // Obtener el estado inicial
        const initialStatus = await db.packingStatus.findFirst({
            where: { name: "Pendiente" },
        });

        if (!initialStatus) {
            return {
                success: false,
                error: "Estado inicial no configurado en el sistema",
            };
        }

        // Crear el packing con transacción para asegurar consistencia
        const packing = await db.$transaction(async (prisma) => {
            // Crear el packing
            const newPacking = await prisma.packing.create({
                data: {
                    folio,
                    code: folio,
                    customerId: validatedData.customerId,
                    subCustomerId: validatedData.subCustomerId,
                    orderId: validatedData.orderId,
                    deliveryDate: validatedData.deliveryDate,
                    notes: validatedData.notes,
                    statusId: initialStatus.id,
                    trackingNumber,

                    // Información de transporte
                    transporterId: validatedData.transporterId,
                    vehicleInfo: validatedData.vehicleInfo,
                    driverName: validatedData.driverName,
                    driverPhone: validatedData.driverPhone,

                    // Información adicional
                    totalWeight: validatedData.totalWeight,
                    totalVolume: validatedData.totalVolume,
                    packagesCount: validatedData.packagesCount,

                    // Trazabilidad
                    packedById: session.user.id,
                    packedAt: new Date(),

                    details: {
                        create: validatedData.details.map((detail) => ({
                            ...detail,
                            qualityPassed: true, // Por defecto pasa control de calidad inicial
                        })),
                    },

                    history: {
                        create: {
                            action: "CREATED",
                            metadata: {
                                createdBy: session.user.email,
                                details: validatedData.details.length,
                                trackingNumber,
                            },
                        },
                    },
                },
                include: {
                    customer: true,
                    subCustomer: true,
                    order: true,
                    status: true,
                    transporter: true,
                    packedBy: true,
                    details: {
                        include: {
                            garmentSize: {
                                include: {
                                    garment: {
                                        include: {
                                            model: true,
                                            color: true,
                                        },
                                    },
                                    size: true,
                                },
                            },
                        },
                    },
                },
            });

            // Actualizar las cantidades usadas en el inventario
            await Promise.all(
                validatedData.details.map(async (detail) => {
                    await prisma.garmentSize.update({
                        where: { id: detail.garmentSizeId },
                        data: {
                            usedQuantity: {
                                increment: detail.quantity,
                            },
                        },
                    });
                }),
            );

            // Crear registro en el log de operaciones
            await prisma.operationLog.create({
                data: {
                    operationType: "PACKING_CREATE",
                    entityIds: [newPacking.id],
                    userId: session.user.id,
                    status: "COMPLETED",
                    metadata: {
                        folio: newPacking.folio,
                        trackingNumber,
                        totalItems: validatedData.details.length,
                        totalQuantity: validatedData.details.reduce(
                            (sum, d) => sum + d.quantity,
                            0,
                        ),
                    },
                    completedAt: new Date(),
                },
            });

            return newPacking;
        });

        // Revalidar las rutas afectadas
        revalidatePath("/dashboard/packings");
        revalidatePath(
            `/dashboard/customers/${validatedData.customerId}/details`,
        );
        if (validatedData.orderId) {
            revalidatePath(`/dashboard/orders/${validatedData.orderId}`);
        }

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error creating packing V2:", error);

        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        if (error instanceof Error) {
            return { success: false, error: error.message };
        }

        return { success: false, error: "Error al crear el packing" };
    }
}
