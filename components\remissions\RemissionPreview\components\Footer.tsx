"use client";

import React from "react";

import { RemissionPreview } from "@/types/remission";

interface FooterProps {
    data: RemissionPreview;
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

export const Footer: React.FC<FooterProps> = ({ data, editMode, onEdit }) => {
    const handleEdit =
        (field: string) => (e: React.FocusEvent<HTMLElement>) => {
            if (editMode && onEdit) {
                onEdit(field, e.currentTarget.textContent);
            }
        };

    return (
        <footer className="mt-8 pt-8 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Signatures Section */}
                <div className="text-center">
                    <div className="border-b border-gray-400 mb-2 h-16" />
                    <p className="text-sm text-gray-600">Entrega</p>
                    <p
                        suppressContentEditableWarning
                        className={`text-sm text-gray-800 ${editMode ? "editable-field" : ""}`}
                        contentEditable={editMode}
                        onBlur={handleEdit("deliveredBy")}
                    >
                        {(data as any).deliveredBy || "Nombre del entregador"}
                    </p>
                </div>

                <div className="text-center">
                    <div className="border-b border-gray-400 mb-2 h-16" />
                    <p className="text-sm text-gray-600">Recibe</p>
                    <p
                        suppressContentEditableWarning
                        className={`text-sm text-gray-800 ${editMode ? "editable-field" : ""}`}
                        contentEditable={editMode}
                        onBlur={handleEdit("receivedBy")}
                    >
                        {(data as any).receivedBy || "Nombre del receptor"}
                    </p>
                </div>

                <div className="text-center">
                    <div className="border-b border-gray-400 mb-2 h-16" />
                    <p className="text-sm text-gray-600">Autoriza</p>
                    <p
                        suppressContentEditableWarning
                        className={`text-sm text-gray-800 ${editMode ? "editable-field" : ""}`}
                        contentEditable={editMode}
                        onBlur={handleEdit("authorizedBy")}
                    >
                        {(data as any).authorizedBy || "Nombre del autorizador"}
                    </p>
                </div>
            </div>

            {/* Footer Info */}
            <div className="mt-8 text-center text-xs text-gray-500">
                <p>LOHARI - Sistema de Gestión de Inventario</p>
                <p>
                    Este documento es un comprobante oficial de entrega de
                    mercancía
                </p>
            </div>
        </footer>
    );
};
