// Script para limpiar sesiones de NextAuth
// Ejecutar con: node scripts/clear-sessions.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function clearSessions() {
  try {
    console.log('🧹 Limpiando sesiones antiguas...');
    
    // Eliminar todas las sesiones
    const deletedSessions = await prisma.session.deleteMany({});
    console.log(`✅ ${deletedSessions.count} sesiones eliminadas`);
    
    // También podemos limpiar tokens de verificación expirados si existen
    const deletedTokens = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: new Date()
        }
      }
    });
    console.log(`✅ ${deletedTokens.count} tokens expirados eliminados`);
    
    console.log('🎉 Limpieza completada exitosamente');
  } catch (error) {
    console.error('❌ Error durante la limpieza:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearSessions();