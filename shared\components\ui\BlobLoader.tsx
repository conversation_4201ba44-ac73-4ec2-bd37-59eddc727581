"use client";

import React from "react";

interface BlobLoaderProps {
    size?: "sm" | "md" | "lg";
    text?: string;
    className?: string;
}

const BlobLoader = ({ size = "md", text, className = "" }: BlobLoaderProps) => {
    // Size configuration
    const sizeConfig = {
        sm: {
            blobSize: "w-[80px] h-[80px]",
            padding: "8px",
            dotSize: "24px",
            boxShadowSize: "-24px 0, 24px 0, 0 24px, 0 -24px",
            animSize: "-8px 0, 8px 0, 0 8px, 0 -8px",
            fontSize: "text-xs",
        },
        md: {
            blobSize: "w-[112px] h-[112px]",
            padding: "11.2px",
            dotSize: "33.6px",
            boxShadowSize: "-33.6px 0, 33.6px 0, 0 33.6px, 0 -33.6px",
            animSize: "-11.2px 0, 11.2px 0, 0 11.2px, 0 -11.2px",
            fontSize: "text-sm",
        },
        lg: {
            blobSize: "w-[144px] h-[144px]",
            padding: "14.4px",
            dotSize: "43.2px",
            boxShadowSize: "-43.2px 0, 43.2px 0, 0 43.2px, 0 -43.2px",
            animSize: "-14.4px 0, 14.4px 0, 0 14.4px, 0 -14.4px",
            fontSize: "text-base",
        },
    };

    return (
        <div
            className={`flex flex-col items-center justify-center gap-4 ${className}`}
        >
            <div
                className={`relative grid ${sizeConfig[size].blobSize} bg-white dark:bg-gray-900`}
                style={{
                    filter: "blur(4.5px) contrast(10)",
                    padding: sizeConfig[size].padding,
                    mixBlendMode: "darken",
                }}
            >
                <div className="blob" />
            </div>
            {text && (
                <p
                    className={`font-medium text-gray-600 dark:text-gray-300 ${sizeConfig[size].fontSize} mt-2`}
                >
                    {text}
                </p>
            )}

            <style>{`
                .blob {
                    margin: auto;
                    width: ${sizeConfig[size].dotSize};
                    height: ${sizeConfig[size].dotSize};
                    border-radius: 50%;
                    color: #ff00f5;
                    background: currentColor;
                    box-shadow: ${sizeConfig[size].boxShadowSize};
                    animation: blob-animation 1s infinite alternate;
                }

                @keyframes blob-animation {
                    90%,
                    100% {
                        box-shadow: ${sizeConfig[size].animSize};
                        transform: rotate(180deg);
                    }
                }
            `}</style>
        </div>
    );
};

export default BlobLoader;
