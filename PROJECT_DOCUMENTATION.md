# 📚 Documentación Completa - Sistema Lohari

## 🏭 Visión General

**Lohari** es un sistema integral de gestión para empresas de manufactura de prendas de vestir. Diseñado específicamente para el sector textil, cubre todo el flujo de trabajo desde la recepción de órdenes hasta la entrega final, con un enfoque especial en la gestión de contratistas externos (maquilas).

### 🎯 Propósito del Sistema
- **Gestión de Órdenes**: Control completo del ciclo de vida de las órdenes de producción
- **Administración de Contratistas**: Seguimiento y evaluación de maquilas externas  
- **Control de Producción**: Asignaciones, seguimiento y control de calidad
- **Documentación**: Generación automatizada de remisiones y reportes
- **Comunicación**: Sistema robusto de notas y colaboración

### 🔑 Características Principales
- Sistema multi-usuario con roles y permisos
- Dashboard analítico con métricas en tiempo real
- Generación de PDF para documentos oficiales
- Sistema de alertas y gestión de riesgos
- Interfaz responsiva y moderna
- Soporte para operaciones masivas (batch)

## 🏗️ Arquitectura del Sistema

### Stack Tecnológico

#### Frontend
- **Framework**: Next.js 14.2.3 con App Router
- **UI Library**: HeroUI v2.7.5 (sistema de componentes)
- **Styling**: TailwindCSS 3.4.16 + CSS Modules
- **State Management**: SWR 2.3.3 + React Query 5.79
- **Forms**: React Hook Form 7.57
- **Animaciones**: Framer Motion 11.18
- **Iconos**: Heroicons + React Icons

#### Backend
- **Runtime**: Node.js con Next.js API Routes
- **ORM**: Prisma 6.8.2
- **Database**: PostgreSQL (via Supabase)
- **Auth**: NextAuth v4.24.7
- **Validación**: Zod 3.25
- **PDF**: React PDF Renderer 4.3.0

#### Infraestructura
- **Hosting**: Optimizado para Vercel/Railway
- **CDN**: Assets estáticos
- **Storage**: Supabase para archivos
- **Cache**: Redis (opcional)

### Arquitectura de Carpetas

```
lohari/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Grupo de autenticación
│   ├── api/               # API Routes
│   └── dashboard/         # Aplicación principal
├── features/              # Módulos de negocio
│   ├── assignments/       # Asignaciones
│   ├── auth/             # Autenticación
│   ├── colors/           # Colores
│   ├── contractors/      # Contratistas
│   ├── customers/        # Clientes
│   ├── models/           # Modelos de prendas
│   ├── notes/            # Sistema de notas
│   ├── orders/           # Órdenes
│   ├── packings/         # Empaquetado (nuevo)
│   ├── remissions/       # Remisiones
│   └── sizes/            # Tallas
├── shared/               # Código compartido
│   ├── components/       # Componentes UI
│   ├── hooks/           # Custom hooks
│   ├── lib/             # Utilidades
│   ├── services/        # Servicios
│   └── templates/       # Plantillas CRUD
├── prisma/              # Base de datos
│   ├── schema.prisma    # Esquema
│   └── migrations/      # Migraciones
└── public/              # Assets estáticos
```

### Patrón de Arquitectura

El proyecto sigue una arquitectura **modular por características** (feature-based) con separación clara entre:

1. **Presentación** (UI Components)
2. **Lógica de Negocio** (Server Actions)
3. **Acceso a Datos** (Prisma ORM)
4. **Estado** (SWR/React Query)

## 🗄️ Modelo de Datos

### Entidades Principales

#### 📦 Order (Orden)
Centro del sistema, representa una orden de producción.

```typescript
interface Order {
  id: string
  transferNumber?: string      // Número de transferencia
  cutOrder?: string           // Orden de corte
  batch?: string              // Lote
  receivedDate: Date          // Fecha de recepción
  estimatedDeliveryDate?: Date // Fecha estimada
  deliveryDate?: Date         // Fecha real de entrega
  customerId: string          // Cliente
  statusId: string            // Estado actual
  
  // Relaciones
  customer: Customer
  status: OrderStatus
  garments: Garment[]         // Prendas
  parts: OrderPart[]          // Partidas
  assignments: Assignment[]    // Asignaciones
  notes: Note[]               // Notas
  packings: Packing[]         // Empaquetados
}
```

#### 👤 User (Usuario)
Usuarios del sistema con diferentes roles.

```typescript
interface User {
  id: string
  email: string               // Email único
  name?: string              // Nombre completo
  password?: string          // Hash bcrypt
  roleId: string             // Rol asignado
  emailVerified?: Date       // Verificación
  image?: string             // Avatar
  
  // Relaciones
  role: Role                 // ADMIN, EMPLOYEE, CONTRACTOR, GUEST
  notes: Note[]
  sessions: Session[]
}
```

#### 👷 Contractor (Contratista)
Maquilas externas que realizan el trabajo.

```typescript
interface Contractor {
  id: string
  name: string               // Nombre único
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  notes?: string
  
  // Relaciones
  assignments: Assignment[]   // Trabajos asignados
  remissions: Remission[]    // Entregas
  packings: Packing[]        // Como transportista
}
```

#### 📋 Assignment (Asignación)
Distribución de trabajo a contratistas.

```typescript
interface Assignment {
  id: string
  folio?: string             // Folio único
  contractorId: string       // Contratista
  garmentSizeId: string      // Prenda/talla
  quantity: number           // Cantidad
  defects?: number          // Defectos
  isCompleted: boolean       // Estado
  orderId: string           // Orden
  version: number           // Control versiones
  status: string            // ACTIVE, CANCELLED
}
```

#### 📄 Remission (Remisión)
Documento de entrega de trabajos completados.

```typescript
interface Remission {
  id: string
  folio: string              // Folio secuencial
  contractorId: string       // Contratista
  notes?: string
  orderDetails?: Json        // Detalles
  printedAt?: Date          // Impresión
  status: string            // Estado
  
  // Relaciones
  assignments: RemissionAssignment[]
  items: RemissionItem[]
  history: RemissionHistory[]
}
```

### Estados del Sistema

#### OrderStatus (Estados de Orden)
- `RECEIVED` - Recibida
- `IN_PRODUCTION` - En producción
- `QUALITY_CHECK` - Control de calidad
- `COMPLETED` - Completada
- `DELIVERED` - Entregada
- `CANCELLED` - Cancelada

#### Roles de Usuario
- `ADMIN` - Administrador (control total)
- `EMPLOYEE` - Empleado (usuario interno)
- `CONTRACTOR` - Contratista (acceso limitado)
- `GUEST` - Invitado (solo lectura)

## 🎨 Sistema de Diseño

### Filosofía de Diseño

El sistema sigue los principios de **Material Design 3** adaptados con la biblioteca **HeroUI**, enfocándose en:

1. **Claridad**: Interfaces limpias y comprensibles
2. **Eficiencia**: Flujos de trabajo optimizados
3. **Consistencia**: Patrones reutilizables
4. **Accesibilidad**: WCAG 2.1 AA compliance
5. **Responsividad**: Mobile-first approach

### Componentes Principales

#### Dashboard Layout
```tsx
<DashboardLayout
  title="Título de la Página"
  subtitle="Descripción opcional"
  breadcrumbs={[...]}
  stats={<DashboardStats />}
  actions={<Button>Acción</Button>}
>
  {/* Contenido */}
</DashboardLayout>
```

#### Sistema de Tablas
```tsx
<DashboardTable
  columns={columns}
  data={data}
  actions={rowActions}
  page={page}
  totalPages={totalPages}
  onPageChange={setPage}
  selectable={true}
  onSelectionChange={handleSelection}
/>
```

#### Sistema de Filtros
```tsx
<DashboardFilters
  filters={filterConfig}
  searchValue={search}
  onSearchChange={setSearch}
  sortOptions={sortOptions}
  currentSort={currentSort}
  onSortChange={handleSort}
/>
```

### Tokens de Diseño

#### Colores
```css
:root {
  /* Primarios */
  --primary: #006FEE;
  --secondary: #7828C8;
  --success: #17C964;
  --warning: #F5A524;
  --danger: #F31260;
  
  /* Neutros */
  --gray-50: #FAFAFA;
  --gray-100: #F4F4F5;
  --gray-200: #E4E4E7;
  --gray-300: #D4D4D8;
  --gray-400: #A1A1AA;
  --gray-500: #71717A;
  --gray-600: #52525B;
  --gray-700: #3F3F46;
  --gray-800: #27272A;
  --gray-900: #18181B;
}
```

#### Espaciado
```css
:root {
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
}
```

#### Tipografía
```css
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --text-xs: 0.75rem;   /* 12px */
  --text-sm: 0.875rem;  /* 14px */
  --text-base: 1rem;    /* 16px */
  --text-lg: 1.125rem;  /* 18px */
  --text-xl: 1.25rem;   /* 20px */
  --text-2xl: 1.5rem;   /* 24px */
  --text-3xl: 1.875rem; /* 30px */
}
```

## 🔧 Módulos del Sistema

### 📦 Módulo de Órdenes

**Propósito**: Gestión integral del ciclo de vida de las órdenes de producción.

**Características**:
- Dashboard con 3 tabs (Órdenes, Métricas, Calendario)
- 8 tarjetas de estadísticas en tiempo real
- Sistema de alertas y gestión de riesgos
- Vista dual (Grid/Lista)
- Operaciones batch
- Wizard de creación multi-paso

**Flujo de Trabajo**:
1. **Creación**: Info básica → Prendas → Partidas → Revisión
2. **Asignación**: Distribuir a contratistas
3. **Seguimiento**: Monitorear progreso
4. **Cierre**: Completar y generar documentos

### 👥 Módulo de Clientes

**Propósito**: Gestión de la base de clientes.

**Características**:
- CRUD completo con plantilla unificada
- Historial de órdenes por cliente
- Métricas de negocio
- Búsqueda y filtrado avanzado
- Jerarquía de clientes (principal/sub-clientes)

### 👷 Módulo de Contratistas

**Propósito**: Administración de maquilas externas.

**Características**:
- Información completa de contacto
- Métricas de rendimiento
- Historial de asignaciones
- Evaluación de calidad
- Reportes de productividad

### 📋 Módulo de Asignaciones

**Propósito**: Distribución inteligente del trabajo.

**Características**:
- Wizard avanzado multi-paso
- Folios únicos secuenciales
- Progress tracking
- Validación optimista
- Control de versiones
- Estados (ACTIVE, COMPLETED, CANCELLED)

### 📄 Módulo de Remisiones

**Propósito**: Gestión de entregas y documentación.

**Características**:
- Generación de PDF profesional
- Folios secuenciales únicos
- Estados de impresión
- Vista previa interactiva
- Historial completo
- Firmas digitales

### 📝 Módulo de Notas

**Propósito**: Sistema de comunicación y seguimiento.

**Características**:
- Comentarios anidados ilimitados
- Sistema de likes
- Niveles de importancia
- Menciones y etiquetas
- Operaciones bulk
- Búsqueda full-text

### 🎨 Módulos de Catálogos

#### Colores
- Gestión de paleta con código hexadecimal
- Preview y variaciones
- Análisis de accesibilidad

#### Tallas
- Códigos únicos de tallas
- Contador de uso
- Ordenamiento personalizado

#### Modelos
- Catálogo de diseños/modelos
- Códigos únicos
- Precios base
- Popularidad

## 🔐 Sistema de Autenticación

### Stack de Seguridad
- **Framework**: NextAuth v4.24.7
- **Estrategia**: JWT con refresh tokens
- **Hashing**: bcryptjs
- **Sesiones**: 24h (normal) / 30d (remember me)

### Flujo de Autenticación

```mermaid
graph LR
    A[Login] --> B[Validar Credenciales]
    B --> C[Generar JWT]
    C --> D[Crear Sesión]
    D --> E[Redirect Dashboard]
    E --> F[Middleware Verifica]
    F --> G[Acceso Permitido]
```

### Roles y Permisos

| Rol | Dashboard | Órdenes | Contratistas | Remisiones | Admin |
|-----|-----------|---------|--------------|------------|-------|
| ADMIN | ✅ | ✅ | ✅ | ✅ | ✅ |
| EMPLOYEE | ✅ | ✅ | ✅ | ✅ | ❌ |
| CONTRACTOR | ✅ | Vista limitada | Solo propio | Solo propias | ❌ |
| GUEST | ✅ | Solo lectura | Solo lectura | Solo lectura | ❌ |

## 🚀 Guía de Instalación

### Requisitos Previos
- Node.js 18+ 
- PostgreSQL 14+ (o cuenta Supabase)
- Git
- npm/yarn/pnpm

### Instalación Paso a Paso

1. **Clonar el repositorio**
```bash
git clone https://github.com/tu-usuario/lohari.git
cd lohari
```

2. **Instalar dependencias**
```bash
npm install
# o
yarn install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env
```

Editar `.env` con tus valores:
```env
# Base de datos
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="genera-un-secret-seguro"

# Supabase (opcional)
NEXT_PUBLIC_SUPABASE_URL="..."
NEXT_PUBLIC_SUPABASE_ANON_KEY="..."
```

4. **Configurar la base de datos**
```bash
# Generar cliente Prisma
npx prisma generate

# Ejecutar migraciones
npx prisma migrate deploy

# Sembrar datos iniciales
npm run db:seed
```

5. **Iniciar el servidor de desarrollo**
```bash
npm run dev
```

Abrir [http://localhost:3000](http://localhost:3000)

### Usuarios de Prueba

| Email | Contraseña | Rol |
|-------|------------|-----|
| <EMAIL> | admin123 | ADMIN |
| <EMAIL> | empleado123 | EMPLOYEE |
| <EMAIL> | contratista123 | CONTRACTOR |

## 📱 Guía de Uso

### Dashboard Principal

Al iniciar sesión, verás el dashboard con:
- **Métricas Generales**: KPIs del negocio
- **Alertas**: Órdenes en riesgo o próximas a vencer
- **Accesos Rápidos**: Links a módulos principales
- **Actividad Reciente**: Últimas operaciones

### Flujo de Trabajo Típico

1. **Crear Cliente** (si es nuevo)
   - Dashboard → Clientes → Nuevo Cliente
   - Llenar información básica
   - Guardar

2. **Crear Orden**
   - Dashboard → Órdenes → Nueva Orden
   - Wizard de 4 pasos:
     - Info básica (cliente, fechas)
     - Agregar prendas (modelo, color, tallas)
     - Definir partidas
     - Revisar y confirmar

3. **Asignar Trabajo**
   - Órdenes → Seleccionar orden → Asignar
   - Elegir items a asignar
   - Seleccionar contratistas
   - Distribuir cantidades
   - Generar folios

4. **Recibir Trabajo**
   - Asignaciones → Marcar como completadas
   - Registrar defectos (si hay)
   - Generar remisión
   - Imprimir PDF

5. **Completar Orden**
   - Verificar todas las asignaciones
   - Cambiar estado a "Completada"
   - Generar reportes finales

### Tips de Productividad

- **Usar filtros**: Guarda filtros frecuentes
- **Operaciones batch**: Selecciona múltiples items
- **Atajos de teclado**: 
  - `Ctrl+K`: Búsqueda rápida
  - `Ctrl+N`: Nueva orden
  - `Esc`: Cerrar modales

## 🛠️ Desarrollo

### Estructura de un Feature

```
features/[nombre]/
├── actions/          # Server actions
│   ├── create.ts
│   ├── update.ts
│   ├── delete.ts
│   └── query.ts
├── components/       # Componentes React
│   ├── forms/
│   └── modals/
├── hooks/           # Custom hooks
│   └── use[Feature].ts
├── schemas/         # Validación Zod
│   └── schema.ts
├── types/           # TypeScript types
│   └── index.ts
├── utils/           # Utilidades
├── index.ts         # Exports públicos
└── README.md        # Documentación
```

### Convenciones de Código

#### Nomenclatura
- **Componentes**: PascalCase (`OrderCard.tsx`)
- **Hooks**: camelCase con prefijo `use` (`useOrders.ts`)
- **Actions**: kebab-case (`create-order.ts`)
- **Tipos**: PascalCase con sufijo (`OrderType.ts`)

#### Patrones
- Server Components por defecto
- Client Components solo cuando necesario
- Server Actions para mutaciones
- SWR/React Query para estado cliente

### Comandos Útiles

```bash
# Desarrollo
npm run dev              # Iniciar servidor
npm run build           # Build producción
npm run lint            # Ejecutar linter
npm run lint:fix        # Fix automático

# Base de datos
npm run db:generate     # Generar tipos Prisma
npm run db:push        # Push cambios a BD
npm run db:seed        # Sembrar datos
npm run db:reset       # Reset completo

# Testing (limitado)
npm test               # Ejecutar tests
```

## 🚢 Deployment

### Opción 1: Vercel (Recomendado)

1. Conectar repositorio en Vercel
2. Configurar variables de entorno
3. Deploy automático en cada push

### Opción 2: Railway

1. Crear proyecto en Railway
2. Conectar GitHub
3. Agregar PostgreSQL
4. Configurar variables
5. Deploy

### Opción 3: Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY . .
RUN npm ci --only=production
RUN npx prisma generate
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Variables de Producción

```env
NODE_ENV=production
DATABASE_URL=postgresql://...
NEXTAUTH_URL=https://tu-dominio.com
NEXTAUTH_SECRET=secret-seguro-generado
```

## 🐛 Solución de Problemas

### Errores Comunes

#### "PrismaClientKnownRequestError"
- **Causa**: Violación de constraint en BD
- **Solución**: Verificar datos únicos y relaciones

#### "NEXTAUTH_URL is not set"
- **Causa**: Variable de entorno faltante
- **Solución**: Configurar en `.env`

#### "Module not found"
- **Causa**: Dependencia faltante
- **Solución**: `npm install` o limpiar cache

#### "Too many renders"
- **Causa**: Loop infinito en React
- **Solución**: Revisar dependencias de useEffect

### Performance

Para mejorar el rendimiento:
1. Habilitar cache de Prisma
2. Implementar paginación en listas grandes
3. Usar `loading.tsx` para Suspense
4. Optimizar imágenes con `next/image`

## 📊 Métricas y Monitoreo

### KPIs del Sistema
- Órdenes activas vs completadas
- Tiempo promedio de producción
- Tasa de defectos por contratista
- Cumplimiento de fechas de entrega

### Herramientas Recomendadas
- **Analytics**: Google Analytics 4
- **Monitoreo**: Sentry
- **Performance**: Vercel Analytics
- **Uptime**: UptimeRobot

## 🤝 Contribución

### Flujo de Trabajo Git

1. Crear branch desde `main`
```bash
git checkout -b feature/nombre-feature
```

2. Hacer cambios y commits
```bash
git add .
git commit -m "feat: descripción del cambio"
```

3. Push y crear PR
```bash
git push origin feature/nombre-feature
```

### Convención de Commits
- `feat:` Nueva funcionalidad
- `fix:` Corrección de bugs
- `docs:` Documentación
- `style:` Cambios de formato
- `refactor:` Refactorización
- `test:` Agregar tests
- `chore:` Tareas de mantenimiento

## 📞 Soporte

### Recursos
- **Documentación**: `/docs`
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

### FAQ

**¿Cómo resetear contraseña?**
- Actualmente manual via BD, feature pendiente

**¿Se puede exportar a Excel?**
- En desarrollo, próximamente disponible

**¿Soporta múltiples idiomas?**
- Actualmente solo español, i18n planeado

**¿Hay app móvil?**
- La web es responsive, app nativa en roadmap

## 🗺️ Roadmap

### v1.1 (Q1 2025)
- [ ] Sistema de notificaciones push
- [ ] Exportación a Excel/CSV
- [ ] Recuperación de contraseña
- [ ] Dashboard personalizable

### v1.2 (Q2 2025)
- [ ] API REST pública
- [ ] Integración con contabilidad
- [ ] Reportes avanzados
- [ ] Multi-idioma (i18n)

### v2.0 (Q3 2025)
- [ ] App móvil nativa
- [ ] Inteligencia artificial para predicciones
- [ ] Marketplace de contratistas
- [ ] Sistema de pagos integrado

---

**Última actualización**: Enero 2025  
**Versión**: 0.0.1  
**Licencia**: Propietaria