// types/types.ts

// Respuesta común de acciones del servidor
export interface ActionResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    revalidated?: boolean;
}

// Base para opciones de búsqueda comunes
export interface BaseQueryOptions {
    search?: string;
    page?: number;
    perPage?: number;
    orderBy?: string;
    order?: "asc" | "desc";
}

// Interfaces para Órdenes
export interface OrderPart {
    id?: string;
    code: string;
    orderId?: string;
}

export interface OrderGarmentSize {
    sizeId: string;
    quantity: number | string; // String para formularios, number para API
}

export interface OrderGarment {
    id?: string;
    orderId?: string;
    modelId: string;
    colorId: string;
    sizes: OrderGarmentSize[];
}

export interface OrderNote {
    id?: string;
    content: string;
    statusId: string;
    importanceId: string;
    authorId?: string;
    orderId?: string;
}

// Datos para crear/actualizar una orden
export interface OrderCreateData {
    customerId: string;
    subCustomerId?: string | null;
    statusId: string;
    receivedDate: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
    batch?: string | null;
    estimatedDeliveryDate?: string | null;
    // Relaciones
    parts?: OrderPart[];
    garments?: OrderGarment[];
    notes?: OrderNote[];
}

// Interfaces de datos para formularios
export interface SizeEntry {
    sizeId: string;
    quantity: string;
}

export interface GarmentEntry {
    modelId: string;
    colorId: string;
    sizes: SizeEntry[];
}

export interface NoteEntry {
    content: string;
    statusId: string;
    importanceId: string;
}

export interface OrderFormData {
    customerId: string;
    subCustomerId?: string;
    statusId: string;
    transferNumber: string;
    cutOrder: string;
    batch: string;
    parts: OrderPartEntry[];
    garments: GarmentEntry[];
    notes: NoteEntry[];
}

// Definición para OrderPartEntry (usada en OrderParts.tsx)
export interface OrderPartEntry {
    code: string;
}

// Interfaces para opciones de selección
export interface CustomerOption {
    id: string;
    name: string;
}

export interface StatusOption {
    id: string;
    name: string;
    color?: string;
    iconName?: string;
}

export interface ModelOption {
    id: string;
    code: string;
    name: string;
}

export interface ColorOption {
    id: string;
    name: string;
    hexCode?: string;
}

export interface SizeOption {
    id: string;
    code: string;
    name: string;
}

export interface NoteStatusOption {
    id: string;
    name: string;
    color?: string;
    iconName?: string;
}

export interface NoteImportanceOption {
    id: string;
    name: string;
    color?: string;
    iconName?: string;
}

// Interfaces para otras entidades (Models, Colors, Sizes, etc.)

// Tipos base para crear/actualizar modelos
export interface ModelCreateData {
    code: string;
    name: string;
    description?: string | null;
}

// Tipos base para crear/actualizar colores
export interface ColorCreateData {
    name: string;
    hexCode?: string | null;
    description?: string | null;
}

// Tipos base para crear/actualizar tallas
export interface SizeCreateData {
    code: string;
    name: string;
    description?: string | null;
}

// Tipos base para crear/actualizar contratistas
export interface ContractorCreateData {
    name: string;
    email?: string | null;
    phone?: string | null;
    address?: string | null;
}

// Tipos base para crear/actualizar clientes
export interface CustomerCreateData {
    name: string;
    email?: string | null;
    phone?: string | null;
    address?: string | null;
    contactPerson?: string | null;
}
