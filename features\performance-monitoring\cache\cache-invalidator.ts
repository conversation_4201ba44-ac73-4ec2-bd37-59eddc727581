/**
 * Tag-Based Cache Invalidation
 * Implements cache invalidation using tags for related data
 */

import { getRedisClient } from "./redis-client";

export class CacheInvalidator {
    private static instance: CacheInvalidator;

    private constructor() {}

    static getInstance(): CacheInvalidator {
        if (!CacheInvalidator.instance) {
            CacheInvalidator.instance = new CacheInvalidator();
        }

        return CacheInvalidator.instance;
    }

    /**
     * Tag a cache key
     */
    async tagKey(key: string, tags: string[]): Promise<void> {
        const client = getRedisClient();

        if (!client) return;

        // Add key to each tag set
        const promises = tags.map((tag) => client.sadd(`tag:${tag}`, key));

        await Promise.all(promises);

        // Store tags for the key (for cleanup)
        await client.sadd(`key:tags:${key}`, ...tags);
    }

    /**
     * Invalidate all keys with a specific tag
     */
    async invalidateTag(tag: string): Promise<number> {
        const client = getRedisClient();

        if (!client) return 0;

        const tagKey = `tag:${tag}`;
        const keys = await client.smembers(tagKey);

        if (keys.length === 0) return 0;

        // Delete all keys with this tag
        await client.del(...keys);

        // Clean up tag references
        const cleanupPromises = keys.map((key) => this.removeKeyTags(key));

        await Promise.all(cleanupPromises);

        // Delete the tag set
        await client.del(tagKey);

        return keys.length;
    }

    /**
     * Invalidate multiple tags at once
     */
    async invalidateTags(tags: string[]): Promise<number> {
        const results = await Promise.all(
            tags.map((tag) => this.invalidateTag(tag)),
        );

        return results.reduce((total, count) => total + count, 0);
    }

    /**
     * Remove all tag associations for a key
     */
    private async removeKeyTags(key: string): Promise<void> {
        const client = getRedisClient();

        if (!client) return;

        const keyTagsKey = `key:tags:${key}`;
        const tags = await client.smembers(keyTagsKey);

        // Remove key from each tag set
        const promises = tags.map((tag) => client.srem(`tag:${tag}`, key));

        await Promise.all(promises);

        // Delete the key's tag set
        await client.del(keyTagsKey);
    }

    /**
     * Get all tags for a key
     */
    async getKeyTags(key: string): Promise<string[]> {
        const client = getRedisClient();

        if (!client) return [];

        return client.smembers(`key:tags:${key}`);
    }

    /**
     * Get all keys for a tag
     */
    async getTagKeys(tag: string): Promise<string[]> {
        const client = getRedisClient();

        if (!client) return [];

        return client.smembers(`tag:${tag}`);
    }
}

// Export singleton instance
export const cacheInvalidator = CacheInvalidator.getInstance();

/**
 * Common cache tags
 */
export const CacheTags = {
    ORDERS: "orders",
    CUSTOMERS: "customers",
    PRODUCTS: "products",
    ASSIGNMENTS: "assignments",
    USER: (userId: string) => `user:${userId}`,
    ORDER: (orderId: string) => `order:${orderId}`,
    CUSTOMER: (customerId: string) => `customer:${customerId}`,
} as const;
