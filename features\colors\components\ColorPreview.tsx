"use client";

import { useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Footer,
    addToast,
    Divider,
} from "@heroui/react";
import { motion } from "framer-motion";

// Importamos los componentes y utilidades nuevas
import { getColorName, isColorDark } from "../utils/colorUtils";

import { ThemeToggle } from "./ThemeToggle";
import { ColorFormatTabs } from "./ColorFormatTabs";
import { ColorVariations } from "./ColorVariations";
import { ContrastIndicator } from "./ContrastIndicator";

interface ColorPreviewProps {
    hexCode: string;
    colorName: string;
    className?: string;
}

export function ColorPreview({
    hexCode,
    colorName,
    className = "",
}: ColorPreviewProps) {
    const [isCopied, setIsCopied] = useState(false);
    const [previewTheme, setPreviewTheme] = useState<"light" | "dark">("light");

    const approximateColorName = getColorName(hexCode);
    const isDark = isColorDark(hexCode);

    const handleCopyHex = async () => {
        try {
            await navigator.clipboard.writeText(hexCode);
            setIsCopied(true);

            addToast({
                title: "Código copiado",
                description: `${hexCode} copiado al portapapeles`,
                color: "success",
            });

            // Reset copy state after 2 seconds
            setTimeout(() => setIsCopied(false), 2000);
        } catch (err) {
            addToast({
                title: "Error al copiar",
                description: "No se pudo copiar al portapapeles",
                color: "danger",
            });
        }
    };

    return (
        <Card className={`overflow-hidden shadow-lg ${className}`}>
            <CardHeader className="px-4 py-3 flex justify-between items-center bg-gray-50 dark:bg-gray-800/50">
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300">
                    Vista previa
                </h3>
                <ThemeToggle
                    defaultTheme="light"
                    onChange={(theme) => setPreviewTheme(theme)}
                />
            </CardHeader>

            {/* Pestañas de formatos de color */}
            <div className="px-4 pt-3">
                <ColorFormatTabs hexCode={hexCode} />
            </div>

            <CardBody className="p-4">
                <motion.div
                    className={`aspect-square w-full rounded-xl overflow-hidden transition-all duration-500 shadow-lg 
            ${previewTheme === "dark" ? "bg-gray-900" : "bg-white"}`}
                    style={{ transform: "perspective(800px) rotateX(0deg)" }}
                    whileHover={{
                        rotateX: "5deg",
                        rotateY: "5deg",
                        scale: 1.02,
                        transition: { duration: 0.3 },
                    }}
                >
                    <motion.div
                        animate={{ scale: 1, opacity: 1 }}
                        className="w-full h-full transition-all duration-500"
                        initial={{ scale: 0.95, opacity: 0.8 }}
                        style={{ backgroundColor: hexCode }}
                        transition={{ duration: 0.5 }}
                    />
                </motion.div>

                {/* Indicador de contraste */}
                <div className="mt-4">
                    <ContrastIndicator
                        background={
                            previewTheme === "dark" ? "#111827" : "#FFFFFF"
                        }
                        color={hexCode}
                    />
                </div>
            </CardBody>

            <Divider className="opacity-50" />

            <CardFooter className="flex flex-col items-start gap-3 py-4 px-4 bg-gray-50/50 dark:bg-gray-800/30">
                <div className="w-full">
                    <div className="flex flex-col">
                        <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                            {colorName || "Sin nombre"}
                        </span>
                        {approximateColorName && (
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                                {approximateColorName}
                            </span>
                        )}
                    </div>
                </div>

                {/* Variaciones de color */}
                <div className="w-full">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                        Variaciones:
                    </p>
                    <ColorVariations hexCode={hexCode} />
                </div>
            </CardFooter>
        </Card>
    );
}
