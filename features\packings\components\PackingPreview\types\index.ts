// TypeScript interfaces for PackingPreview component

// Main preview data structure
export interface PackingPreviewData {
    // Company information
    company: {
        name: string;
        slogan?: string;
        rfc?: string;
        address?: string;
        phone?: string;
        email?: string;
    };

    // Document information
    document: {
        folio: string;
        date: Date;
        type: "PACKING LIST" | "LISTA DE EMPAQUE";
    };

    // Customer information
    customer: {
        name: string;
        subName?: string;
        contact?: string;
        phone?: string;
        address?: string;
    };

    // Order information
    order?: {
        number: string;
        cutOrder?: string;
    };

    // Product items
    items: PackingProductItem[];

    // Summary information
    summary: PackingSummaryInfo;

    // Transport information
    transport: {
        notes?: string;
        signature?: string;
        signedAt?: Date;
    };

    // Receiver information
    receiver: {
        name?: string;
        signature?: string;
        signedAt?: Date;
    };
}

// Product item structure
export interface PackingProductItem {
    model: string;
    color: string;
    part?: string;
    sizes: PackingSizeDetail[];
    totals: {
        pieces: number;
        boxes: number;
        bags: number;
        loosePieces: number;
    };
}

// Size detail structure
export interface PackingSizeDetail {
    size: string;
    quantity: number;
    boxes: number;
    bags: number;
    loosePieces: number;
    quality: "primera" | "segunda" | "manchada" | "incompleta";
}

// Summary information
export interface PackingSummaryInfo {
    totalPieces: number;
    totalBoxes: number;
    totalBags: number;
    totalLoosePieces: number;
    qualityBreakdown: {
        primera: number;
        segunda: number;
        manchada: number;
        incompleta: number;
    };
    deliveryDate: Date;
    notes?: string;
}

// Component props
export interface PackingPreviewProps {
    data: PackingPreviewData;
    packingId?: string; // For PDF generation
    orientation?: "portrait" | "landscape";
    editMode?: boolean;
    zoom?: number;
    onEdit?: (field: string, value: any) => void;
    className?: string;
}

// Header component props
export interface PackingHeaderProps {
    company: PackingPreviewData["company"];
    document: PackingPreviewData["document"];
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

// Products table props
export interface PackingProductsTableProps {
    items: PackingProductItem[];
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

// Summary component props
export interface PackingSummaryProps {
    summary: PackingSummaryInfo;
    customer: PackingPreviewData["customer"];
    order?: PackingPreviewData["order"];
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

// Signatures component props
export interface PackingSignaturesProps {
    transport: PackingPreviewData["transport"];
    receiver: PackingPreviewData["receiver"];
    editMode?: boolean;
    onEdit?: (field: string, value: any) => void;
}

// Print configuration
export interface PrintConfig {
    orientation: "portrait" | "landscape";
    margins: {
        top: string;
        right: string;
        bottom: string;
        left: string;
    };
    paperSize: "a4" | "letter";
    scale: number;
}
