"use client";

import { useRouter, useParams } from "next/navigation";
import { addToast } from "@heroui/react";
import useSWR from "swr";
import { useEffect } from "react";

import {
    createContractor,
    deleteContractor,
    getContractor,
    getContractors,
    updateContractor,
    validateContractorName,
} from "@/features/contractors/actions";
import {
    ContractorsListResponse,
    ContractorResponse,
    Contractor,
} from "@/features/contractors/schemas/schema";

// Define un tipo para las respuestas de las acciones de servidor
type ServerActionResponse = {
    success: boolean;
    error?: string;
    data?: any;
};

// Fetcher genérico para SWR
const fetcher = async (url: string, ...args: any[]) => {
    // El primer argumento es la URL, pero en nuestro caso es la clave
    // Los demás argumentos son los parámetros para las funciones
    if (url === "getContractor") {
        return getContractor(args[0]);
    }
    if (url === "getContractors") {
        return getContractors(args[0] || {});
    }

    return null;
};

/**
 * Hook para obtener un contratista por su ID
 */
export const useContractor = (
    id?: string,
): {
    contractor: Contractor | null | undefined;
    isLoading: boolean;
    refetch: any;
    error?: string;
} => {
    const params = useParams();
    const contractorId = id || (params?.id as string);

    const { data, error, mutate } = useSWR(
        contractorId ? ["getContractor", contractorId] : null,
        ([_, id]) => fetcher("getContractor", id),
        {
            revalidateOnFocus: false,
        },
    );

    const isLoading = !data && !error;

    // Mover la notificación de error a un efecto para evitar actualizaciones durante el renderizado
    useEffect(() => {
        if (error || (data && !data.success)) {
            addToast({
                title: "Error",
                description:
                    data?.error ||
                    error?.message ||
                    "Error al cargar contratista",
                color: "danger",
            });
        }
    }, [data, error]);

    // Tipamos explícitamente la respuesta para mejorar el tipado
    const response = data as ContractorResponse | undefined;

    return {
        contractor: response?.success ? response.data : null,
        isLoading,
        refetch: mutate,
        error: response?.error || error?.message || undefined,
    };
};

/**
 * Hook para obtener una lista de contratistas con filtros
 */
export const useContractors = (
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
) => {
    const {
        search,
        orderBy = "createdAt",
        order = "desc",
        page = 1,
        perPage = 10,
    } = options;

    const { data, error, mutate } = useSWR(
        ["getContractors", { search, orderBy, order, page, perPage }],
        ([_, opts]) => fetcher("getContractors", opts),
        {
            revalidateOnFocus: false,
            dedupingInterval: 1000 * 60 * 5, // 5 minutos
        },
    );

    const isLoading = !data && !error;

    // Mover la notificación de error a un efecto para evitar actualizaciones durante el renderizado
    useEffect(() => {
        if (error || (data && !data.success)) {
            addToast({
                title: "Error",
                description:
                    data?.error ||
                    error?.message ||
                    "Error al cargar contratistas",
                color: "danger",
            });
        }
    }, [data, error]);

    // El resultado de getContractors es ContractorsListResponse
    const response = data as ContractorsListResponse | undefined;

    return {
        contractors: response?.success && response.data ? response.data : [],
        pagination:
            response?.success && response.pagination
                ? response.pagination
                : {
                      page,
                      perPage,
                      total: 0,
                      totalPages: 0,
                      hasMore: false,
                  },
        isLoading,
        refetch: mutate,
    };
};

/**
 * Hook para crear un nuevo contratista
 */
export const useCreateContractor = () => {
    const router = useRouter();

    const create = async (data: {
        firstName: string;
        middleName?: string | null;
        lastName: string;
        secondLastName?: string | null;
        email?: string | null;
        phone?: string | null;
        notes?: string | null;
    }) => {
        try {
            // Mostrar mensaje de carga
            addToast({
                title: "Creando contratista...",
                color: "primary",
            });

            const response = (await createContractor(
                data,
            )) as ServerActionResponse;

            if (!response?.success) {
                throw new Error(
                    response?.error || "Error al crear contratista",
                );
            }

            // Mostrar mensaje de éxito
            addToast({
                title: "Contratista creado",
                description: "El contratista ha sido creado exitosamente",
                color: "success",
            });

            router.push("/dashboard/contractors");
            router.refresh();

            return response;
        } catch (err) {
            // Mostrar mensaje de error
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error al crear el contratista";

            addToast({
                title: "Error",
                description: errorMessage,
                color: "danger",
            });

            throw err;
        }
    };

    return { create };
};

/**
 * Hook para actualizar un contratista
 */
export const useUpdateContractor = () => {
    const router = useRouter();

    const update = async (
        id: string,
        data: {
            firstName?: string;
            middleName?: string | null;
            lastName?: string;
            secondLastName?: string | null;
            email?: string | null;
            phone?: string | null;
            notes?: string | null;
        },
    ) => {
        try {
            // Mostrar mensaje de carga
            addToast({
                title: "Actualizando contratista...",
                color: "primary",
            });

            const response = (await updateContractor(
                id,
                data,
            )) as ServerActionResponse;

            if (!response?.success) {
                throw new Error(
                    response?.error || "Error al actualizar contratista",
                );
            }

            // Mostrar mensaje de éxito
            addToast({
                title: "Contratista actualizado",
                description: "El contratista ha sido actualizado exitosamente",
                color: "success",
            });

            router.refresh();

            return response;
        } catch (err) {
            // Mostrar mensaje de error
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error al actualizar el contratista";

            addToast({
                title: "Error",
                description: errorMessage,
                color: "danger",
            });

            throw err;
        }
    };

    return { update };
};

/**
 * Hook para eliminar un contratista
 */
export const useDeleteContractor = () => {
    const router = useRouter();

    const remove = async (id: string) => {
        try {
            // Mostrar mensaje de carga
            addToast({
                title: "Eliminando contratista...",
                color: "primary",
            });

            const response = (await deleteContractor(
                id,
            )) as ServerActionResponse;

            if (!response?.success) {
                throw new Error(
                    response?.error || "Error al eliminar contratista",
                );
            }

            // Mostrar mensaje de éxito
            addToast({
                title: "Contratista eliminado",
                description: "El contratista ha sido eliminado exitosamente",
                color: "success",
            });

            router.push("/dashboard/contractors");
            router.refresh();

            return response;
        } catch (err) {
            // Mostrar mensaje de error
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : "Error al eliminar el contratista";

            addToast({
                title: "Error",
                description: errorMessage,
                color: "danger",
            });

            throw err;
        }
    };

    return { remove };
};

/**
 * Hook para validar nombres de contratistas
 */
export const useValidateContractorName = () => {
    const validate = async (
        firstName: string,
        lastName: string,
        excludeId?: string,
    ) => {
        try {
            const response = (await validateContractorName(
                firstName,
                lastName,
                excludeId,
            )) as ServerActionResponse;

            if (!response?.success) {
                addToast({
                    title: "Error",
                    description: response?.error || "Error al validar nombre",
                    color: "danger",
                });

                return false;
            }

            return response.data?.isValid || false;
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error al validar el nombre del contratista",
                color: "danger",
            });

            return false;
        }
    };

    return { validate };
};
