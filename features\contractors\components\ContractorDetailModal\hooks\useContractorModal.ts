import { useState, useEffect } from "react";
import useS<PERSON> from "swr";

import { getContractorWithDetails } from "@/features/contractors/actions/query";

import {
    ContractorWithDetails,
    TabType,
} from "../types/contractor-modal.types";

interface UseContractorModalProps {
    contractorId: string;
}

export function useContractorModal({ contractorId }: UseContractorModalProps) {
    const [activeTab, setActiveTab] = useState<TabType>("general");
    const [isDeleting, setIsDeleting] = useState(false);

    // Fetch contractor data with assignments
    const { data, error, isLoading, mutate } = useSWR(
        contractorId ? `contractor-details-${contractorId}` : null,
        () => getContractorWithDetails(contractorId),
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        },
    );

    // Reset tab when contractor changes
    useEffect(() => {
        setActiveTab("general");
    }, [contractorId]);

    return {
        contractor: data?.data as ContractorWithDetails | undefined,
        isLoading,
        error,
        activeTab,
        setActiveTab,
        isDeleting,
        setIsDeleting,
        mutate,
    };
}
