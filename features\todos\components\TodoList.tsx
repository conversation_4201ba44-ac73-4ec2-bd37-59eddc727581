// Temporary TodoList component
// TODO: Implement proper TodoList component

import React from "react";

// import { Todo } from "@/types/todo"; // Comment out - type doesn't exist
type Todo = any; // Simple fallback

interface TodoListProps {
    todos?: Todo[];
}

export default function TodoList({ todos = [] }: TodoListProps) {
    return (
        <div className="p-4">
            <h2 className="text-xl font-bold mb-4">Todo List</h2>
            <div className="space-y-2">
                {todos.length === 0 ? (
                    <p className="text-gray-500">
                        No todos yet. This is a placeholder component.
                    </p>
                ) : (
                    todos.map((todo) => (
                        <div key={todo.id} className="p-3 border rounded">
                            <h3 className="font-semibold">{todo.title}</h3>
                            {todo.description && (
                                <p className="text-sm text-gray-600">
                                    {todo.description}
                                </p>
                            )}
                        </div>
                    ))
                )}
            </div>
        </div>
    );
}
