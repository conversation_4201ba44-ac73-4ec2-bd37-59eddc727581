"use client";

import type { Contractor } from "@/features/contractors/types";
import type {
    Order,
    Garment,
    GarmentSize,
} from "@/features/assignments/types/types";
import type { OrderPart } from "@/shared/types/common";

import React from "react";
import {
    <PERSON>ton,
    Card,
    CardBody,
    Divider,
    Avatar,
    Progress,
} from "@heroui/react";
import {
    DocumentPlusIcon,
    CheckCircleIcon,
    UserGroupIcon,
    CubeIcon,
    ClipboardDocumentListIcon,
    ArrowRightIcon,
    CalendarIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

interface OrderWithDeliveryDate extends Order {
    code: string;
    deliveryDate: string;
    parts?: OrderPart[];
}

interface AssignmentSummaryProps {
    summary: {
        contractor: Contractor | null;
        ordersCount: number;
        totalPieces: number;
        orders: OrderWithDeliveryDate[];
    };
    onSubmit: () => void;
    isLoading: boolean;
    canSubmit: boolean;
}

export function AssignmentSummary({
    summary,
    onSubmit,
    isLoading,
    canSubmit,
}: AssignmentSummaryProps) {
    // Calcular fecha de entrega más próxima
    const earliestDelivery = summary.orders.reduce(
        (earliest, order) => {
            const deliveryDate = new Date(
                order.estimatedDeliveryDate || order.deliveryDate,
            );

            return !earliest || deliveryDate < earliest
                ? deliveryDate
                : earliest;
        },
        null as Date | null,
    );

    return (
        <div className="space-y-6">
            {/* Contratista seleccionado */}
            {summary.contractor && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                >
                    <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <Avatar
                            color="primary"
                            name={summary.contractor.name}
                            size="lg"
                        />
                        <div className="flex-1">
                            <h4 className="font-semibold text-lg">
                                {summary.contractor.name}
                            </h4>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                                {summary.contractor.phone && (
                                    <span>{summary.contractor.phone}</span>
                                )}
                                {summary.contractor.email && (
                                    <span>{summary.contractor.email}</span>
                                )}
                            </div>
                        </div>
                        <UserGroupIcon className="w-8 h-8 text-primary opacity-20" />
                    </div>
                </motion.div>
            )}

            {/* Estadísticas generales */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    initial={{ opacity: 0, scale: 0.9 }}
                    transition={{ delay: 0.1 }}
                >
                    <Card className="shadow-sm">
                        <CardBody className="text-center py-6">
                            <ClipboardDocumentListIcon className="w-8 h-8 mx-auto mb-2 text-primary" />
                            <p className="text-2xl font-bold">
                                {summary.ordersCount}
                            </p>
                            <p className="text-sm text-gray-500">
                                {summary.ordersCount === 1
                                    ? "Orden"
                                    : "Órdenes"}
                            </p>
                        </CardBody>
                    </Card>
                </motion.div>

                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    initial={{ opacity: 0, scale: 0.9 }}
                    transition={{ delay: 0.2 }}
                >
                    <Card className="shadow-sm">
                        <CardBody className="text-center py-6">
                            <CubeIcon className="w-8 h-8 mx-auto mb-2 text-secondary" />
                            <p className="text-2xl font-bold">
                                {summary.totalPieces}
                            </p>
                            <p className="text-sm text-gray-500">
                                Prendas totales
                            </p>
                        </CardBody>
                    </Card>
                </motion.div>

                <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    initial={{ opacity: 0, scale: 0.9 }}
                    transition={{ delay: 0.3 }}
                >
                    <Card className="shadow-sm">
                        <CardBody className="text-center py-6">
                            <CalendarIcon className="w-8 h-8 mx-auto mb-2 text-warning" />
                            <p className="text-lg font-bold">
                                {earliestDelivery
                                    ? format(earliestDelivery, "dd MMM", {
                                          locale: es,
                                      })
                                    : "N/A"}
                            </p>
                            <p className="text-sm text-gray-500">
                                Entrega más próxima
                            </p>
                        </CardBody>
                    </Card>
                </motion.div>
            </div>

            {/* Vista combinada: Cronograma + Tallas */}
            {summary.ordersCount > 0 && (
                <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0.4 }}
                >
                    <Card>
                        <CardBody className="space-y-6">
                            {/* Cronograma de Producción */}
                            <div>
                                <div className="flex items-center gap-2 mb-4">
                                    <CalendarIcon className="w-5 h-5 text-primary" />
                                    <h4 className="font-semibold text-lg">
                                        Cronograma de Producción
                                    </h4>
                                </div>
                                <Divider className="mb-4" />

                                <div className="space-y-4">
                                    {(() => {
                                        // Agrupar órdenes por urgencia
                                        const today = new Date();

                                        today.setHours(0, 0, 0, 0);

                                        const groupedByUrgency =
                                            summary.orders.reduce(
                                                (
                                                    acc,
                                                    order: OrderWithDeliveryDate,
                                                ) => {
                                                    const deliveryDate =
                                                        new Date(
                                                            order.estimatedDeliveryDate ||
                                                                order.deliveryDate,
                                                        );
                                                    const diffDays = Math.ceil(
                                                        (deliveryDate.getTime() -
                                                            today.getTime()) /
                                                            (1000 *
                                                                60 *
                                                                60 *
                                                                24),
                                                    );

                                                    let category = "normal";

                                                    if (
                                                        diffDays < 0 ||
                                                        diffDays <= 3
                                                    )
                                                        category = "urgent";
                                                    else if (diffDays <= 7)
                                                        category = "soon";

                                                    if (!acc[category])
                                                        acc[category] = [];
                                                    acc[category].push({
                                                        ...order,
                                                        diffDays,
                                                        deliveryDate:
                                                            deliveryDate as any,
                                                    });

                                                    return acc;
                                                },
                                                {} as Record<
                                                    string,
                                                    (OrderWithDeliveryDate & {
                                                        diffDays: number;
                                                        deliveryDate: Date;
                                                    })[]
                                                >,
                                            );

                                        // Calcular totales por categoría
                                        const calculateCategoryTotal = (
                                            orders: (OrderWithDeliveryDate & {
                                                diffDays: number;
                                                deliveryDate: Date;
                                            })[],
                                        ) =>
                                            orders.reduce(
                                                (sum, order) =>
                                                    sum +
                                                    (order.garments?.reduce(
                                                        (
                                                            gSum: number,
                                                            g: Garment,
                                                        ) =>
                                                            gSum +
                                                            (
                                                                g.sizes || []
                                                            ).reduce(
                                                                (
                                                                    sSum: number,
                                                                    s: GarmentSize,
                                                                ) =>
                                                                    sSum +
                                                                    s.totalQuantity,
                                                                0,
                                                            ),
                                                        0,
                                                    ) || 0),
                                                0,
                                            );

                                        return (
                                            <>
                                                {/* Urgentes */}
                                                {groupedByUrgency.urgent && (
                                                    <div className="space-y-2">
                                                        <div className="flex items-center gap-2">
                                                            <div className="w-3 h-3 rounded-full bg-danger" />
                                                            <span className="font-medium text-danger">
                                                                URGENTE (
                                                                {
                                                                    groupedByUrgency
                                                                        .urgent
                                                                        .length
                                                                }{" "}
                                                                {groupedByUrgency
                                                                    .urgent
                                                                    .length ===
                                                                1
                                                                    ? "orden"
                                                                    : "órdenes"}{" "}
                                                                •{" "}
                                                                {calculateCategoryTotal(
                                                                    groupedByUrgency.urgent,
                                                                )}{" "}
                                                                prendas)
                                                            </span>
                                                        </div>
                                                        <div className="ml-5 space-y-2">
                                                            {groupedByUrgency.urgent.map(
                                                                (
                                                                    order: OrderWithDeliveryDate & {
                                                                        diffDays: number;
                                                                        deliveryDate: Date;
                                                                    },
                                                                ) => (
                                                                    <div
                                                                        key={
                                                                            order.id
                                                                        }
                                                                        className="p-3 bg-danger-50 dark:bg-danger-900/20 rounded-medium"
                                                                    >
                                                                        <div className="flex items-start justify-between">
                                                                            <div className="space-y-1">
                                                                                <div className="flex items-center gap-2">
                                                                                    <span className="font-medium">
                                                                                        {order.cutOrder
                                                                                            ? `OT: ${order.cutOrder}`
                                                                                            : `Orden #${order.code}`}
                                                                                    </span>
                                                                                    <ArrowRightIcon className="w-3 h-3" />
                                                                                    <span className="font-medium">
                                                                                        {order.garments?.reduce(
                                                                                            (
                                                                                                sum: number,
                                                                                                g: Garment,
                                                                                            ) =>
                                                                                                sum +
                                                                                                (
                                                                                                    g.sizes ||
                                                                                                    []
                                                                                                ).reduce(
                                                                                                    (
                                                                                                        sSum: number,
                                                                                                        s: GarmentSize,
                                                                                                    ) =>
                                                                                                        sSum +
                                                                                                        s.totalQuantity,
                                                                                                    0,
                                                                                                ),
                                                                                            0,
                                                                                        ) ||
                                                                                            0}{" "}
                                                                                        prendas
                                                                                    </span>
                                                                                </div>
                                                                                {order.parts &&
                                                                                    order
                                                                                        .parts
                                                                                        .length >
                                                                                        0 && (
                                                                                        <div className="flex gap-1 flex-wrap">
                                                                                            <span className="text-xs text-default-500">
                                                                                                Partidas:
                                                                                            </span>
                                                                                            {order.parts.map(
                                                                                                (
                                                                                                    part: OrderPart,
                                                                                                ) => (
                                                                                                    <span
                                                                                                        key={
                                                                                                            part.id
                                                                                                        }
                                                                                                        className="text-xs text-default-600"
                                                                                                    >
                                                                                                        {
                                                                                                            part.code
                                                                                                        }
                                                                                                    </span>
                                                                                                ),
                                                                                            )}
                                                                                        </div>
                                                                                    )}
                                                                                {order.garments && (
                                                                                    <div className="text-xs text-default-500">
                                                                                        Modelos:{" "}
                                                                                        {[
                                                                                            ...new Set(
                                                                                                order.garments.map(
                                                                                                    (
                                                                                                        g: Garment,
                                                                                                    ) =>
                                                                                                        g
                                                                                                            .model
                                                                                                            ?.code,
                                                                                                ),
                                                                                            ),
                                                                                        ]
                                                                                            .filter(
                                                                                                Boolean,
                                                                                            )
                                                                                            .join(
                                                                                                ", ",
                                                                                            )}
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <div className="text-sm font-medium text-danger">
                                                                                    {format(
                                                                                        order.deliveryDate,
                                                                                        "dd MMM",
                                                                                        {
                                                                                            locale: es,
                                                                                        },
                                                                                    )}
                                                                                </div>
                                                                                <div className="text-xs text-danger">
                                                                                    {order.diffDays <
                                                                                    0
                                                                                        ? `Vencida hace ${Math.abs(order.diffDays)} días`
                                                                                        : order.diffDays ===
                                                                                            0
                                                                                          ? "Hoy"
                                                                                          : `En ${order.diffDays} días`}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                ),
                                                            )}
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Próximas */}
                                                {groupedByUrgency.soon && (
                                                    <div className="space-y-2">
                                                        <div className="flex items-center gap-2">
                                                            <div className="w-3 h-3 rounded-full bg-warning" />
                                                            <span className="font-medium text-warning">
                                                                PRÓXIMAS (
                                                                {
                                                                    groupedByUrgency
                                                                        .soon
                                                                        .length
                                                                }{" "}
                                                                {groupedByUrgency
                                                                    .soon
                                                                    .length ===
                                                                1
                                                                    ? "orden"
                                                                    : "órdenes"}{" "}
                                                                •{" "}
                                                                {calculateCategoryTotal(
                                                                    groupedByUrgency.soon,
                                                                )}{" "}
                                                                prendas)
                                                            </span>
                                                        </div>
                                                        <div className="ml-5 space-y-2">
                                                            {groupedByUrgency.soon.map(
                                                                (
                                                                    order: OrderWithDeliveryDate & {
                                                                        diffDays: number;
                                                                        deliveryDate: Date;
                                                                    },
                                                                ) => (
                                                                    <div
                                                                        key={
                                                                            order.id
                                                                        }
                                                                        className="p-3 bg-warning-50 dark:bg-warning-900/20 rounded-medium"
                                                                    >
                                                                        <div className="flex items-start justify-between">
                                                                            <div className="space-y-1">
                                                                                <div className="flex items-center gap-2">
                                                                                    <span className="font-medium">
                                                                                        {order.cutOrder
                                                                                            ? `OT: ${order.cutOrder}`
                                                                                            : `Orden #${order.code}`}
                                                                                    </span>
                                                                                    <ArrowRightIcon className="w-3 h-3" />
                                                                                    <span className="font-medium">
                                                                                        {order.garments?.reduce(
                                                                                            (
                                                                                                sum: number,
                                                                                                g: Garment,
                                                                                            ) =>
                                                                                                sum +
                                                                                                (
                                                                                                    g.sizes ||
                                                                                                    []
                                                                                                ).reduce(
                                                                                                    (
                                                                                                        sSum: number,
                                                                                                        s: GarmentSize,
                                                                                                    ) =>
                                                                                                        sSum +
                                                                                                        s.totalQuantity,
                                                                                                    0,
                                                                                                ),
                                                                                            0,
                                                                                        ) ||
                                                                                            0}{" "}
                                                                                        prendas
                                                                                    </span>
                                                                                </div>
                                                                                {order.parts &&
                                                                                    order
                                                                                        .parts
                                                                                        .length >
                                                                                        0 && (
                                                                                        <div className="flex gap-1 flex-wrap">
                                                                                            <span className="text-xs text-default-500">
                                                                                                Partidas:
                                                                                            </span>
                                                                                            {order.parts.map(
                                                                                                (
                                                                                                    part: OrderPart,
                                                                                                ) => (
                                                                                                    <span
                                                                                                        key={
                                                                                                            part.id
                                                                                                        }
                                                                                                        className="text-xs text-default-600"
                                                                                                    >
                                                                                                        {
                                                                                                            part.code
                                                                                                        }
                                                                                                    </span>
                                                                                                ),
                                                                                            )}
                                                                                        </div>
                                                                                    )}
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <div className="text-sm font-medium">
                                                                                    {format(
                                                                                        order.deliveryDate,
                                                                                        "dd MMM",
                                                                                        {
                                                                                            locale: es,
                                                                                        },
                                                                                    )}
                                                                                </div>
                                                                                <div className="text-xs text-default-500">
                                                                                    En{" "}
                                                                                    {
                                                                                        order.diffDays
                                                                                    }{" "}
                                                                                    días
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                ),
                                                            )}
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Normales */}
                                                {groupedByUrgency.normal && (
                                                    <div className="space-y-2">
                                                        <div className="flex items-center gap-2">
                                                            <div className="w-3 h-3 rounded-full bg-success" />
                                                            <span className="font-medium text-success">
                                                                NORMAL (
                                                                {
                                                                    groupedByUrgency
                                                                        .normal
                                                                        .length
                                                                }{" "}
                                                                {groupedByUrgency
                                                                    .normal
                                                                    .length ===
                                                                1
                                                                    ? "orden"
                                                                    : "órdenes"}{" "}
                                                                •{" "}
                                                                {calculateCategoryTotal(
                                                                    groupedByUrgency.normal,
                                                                )}{" "}
                                                                prendas)
                                                            </span>
                                                        </div>
                                                        <div className="ml-5 space-y-2">
                                                            {groupedByUrgency.normal.map(
                                                                (
                                                                    order: OrderWithDeliveryDate & {
                                                                        diffDays: number;
                                                                        deliveryDate: Date;
                                                                    },
                                                                ) => (
                                                                    <div
                                                                        key={
                                                                            order.id
                                                                        }
                                                                        className="p-3 bg-success-50 dark:bg-success-900/20 rounded-medium"
                                                                    >
                                                                        <div className="flex items-start justify-between">
                                                                            <div className="space-y-1">
                                                                                <div className="flex items-center gap-2">
                                                                                    <span className="font-medium">
                                                                                        {order.cutOrder
                                                                                            ? `OT: ${order.cutOrder}`
                                                                                            : `Orden #${order.code}`}
                                                                                    </span>
                                                                                    <ArrowRightIcon className="w-3 h-3" />
                                                                                    <span className="font-medium">
                                                                                        {order.garments?.reduce(
                                                                                            (
                                                                                                sum: number,
                                                                                                g: Garment,
                                                                                            ) =>
                                                                                                sum +
                                                                                                (
                                                                                                    g.sizes ||
                                                                                                    []
                                                                                                ).reduce(
                                                                                                    (
                                                                                                        sSum: number,
                                                                                                        s: GarmentSize,
                                                                                                    ) =>
                                                                                                        sSum +
                                                                                                        s.totalQuantity,
                                                                                                    0,
                                                                                                ),
                                                                                            0,
                                                                                        ) ||
                                                                                            0}{" "}
                                                                                        prendas
                                                                                    </span>
                                                                                </div>
                                                                                {order.parts &&
                                                                                    order
                                                                                        .parts
                                                                                        .length >
                                                                                        0 && (
                                                                                        <div className="flex gap-1 flex-wrap">
                                                                                            <span className="text-xs text-default-500">
                                                                                                Partidas:
                                                                                            </span>
                                                                                            {order.parts.map(
                                                                                                (
                                                                                                    part: OrderPart,
                                                                                                ) => (
                                                                                                    <span
                                                                                                        key={
                                                                                                            part.id
                                                                                                        }
                                                                                                        className="text-xs text-default-600"
                                                                                                    >
                                                                                                        {
                                                                                                            part.code
                                                                                                        }
                                                                                                    </span>
                                                                                                ),
                                                                                            )}
                                                                                        </div>
                                                                                    )}
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <div className="text-sm font-medium">
                                                                                    {format(
                                                                                        order.deliveryDate,
                                                                                        "dd MMM",
                                                                                        {
                                                                                            locale: es,
                                                                                        },
                                                                                    )}
                                                                                </div>
                                                                                <div className="text-xs text-default-500">
                                                                                    En{" "}
                                                                                    {
                                                                                        order.diffDays
                                                                                    }{" "}
                                                                                    días
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                ),
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </>
                                        );
                                    })()}
                                </div>
                            </div>

                            <Divider />

                            {/* Distribución de Tallas */}
                            <div>
                                <div className="flex items-center gap-2 mb-4">
                                    <CubeIcon className="w-5 h-5 text-secondary" />
                                    <h4 className="font-semibold text-lg">
                                        Distribución de Tallas
                                    </h4>
                                    <span className="text-sm text-default-500">
                                        ({summary.totalPieces} prendas total)
                                    </span>
                                </div>

                                <div className="space-y-3">
                                    {(() => {
                                        // Consolidar todas las tallas
                                        const sizeMap = new Map<
                                            string,
                                            number
                                        >();

                                        summary.orders.forEach((order) => {
                                            order.garments?.forEach(
                                                (garment: Garment) => {
                                                    garment.sizes?.forEach(
                                                        (size: GarmentSize) => {
                                                            const sizeCode =
                                                                size.size
                                                                    ?.code ||
                                                                "Sin talla";
                                                            const current =
                                                                sizeMap.get(
                                                                    sizeCode,
                                                                ) || 0;

                                                            sizeMap.set(
                                                                sizeCode,
                                                                current +
                                                                    size.totalQuantity,
                                                            );
                                                        },
                                                    );
                                                },
                                            );
                                        });

                                        // Convertir a array y ordenar
                                        const sizes = Array.from(
                                            sizeMap.entries(),
                                        ).sort((a, b) => {
                                            // Orden personalizado de tallas
                                            const sizeOrder = [
                                                "XS",
                                                "S",
                                                "M",
                                                "L",
                                                "XL",
                                                "XXL",
                                                "XXXL",
                                            ];
                                            const aIndex = sizeOrder.indexOf(
                                                a[0],
                                            );
                                            const bIndex = sizeOrder.indexOf(
                                                b[0],
                                            );

                                            if (aIndex !== -1 && bIndex !== -1)
                                                return aIndex - bIndex;
                                            if (aIndex !== -1) return -1;
                                            if (bIndex !== -1) return 1;

                                            return a[0].localeCompare(b[0]);
                                        });

                                        return (
                                            <>
                                                {/* Números de tallas */}
                                                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3">
                                                    {sizes.map(
                                                        ([size, quantity]) => {
                                                            const percentage =
                                                                (quantity /
                                                                    summary.totalPieces) *
                                                                100;

                                                            return (
                                                                <div
                                                                    key={size}
                                                                    className="text-center"
                                                                >
                                                                    <div className="text-2xl font-bold text-foreground">
                                                                        {size}
                                                                    </div>
                                                                    <div className="text-xl font-semibold text-primary">
                                                                        {
                                                                            quantity
                                                                        }
                                                                    </div>
                                                                    <div className="text-xs text-default-500">
                                                                        {percentage.toFixed(
                                                                            0,
                                                                        )}
                                                                        %
                                                                    </div>
                                                                </div>
                                                            );
                                                        },
                                                    )}
                                                </div>

                                                {/* Barras de progreso */}
                                                <div className="space-y-2">
                                                    {sizes.map(
                                                        ([size, quantity]) => {
                                                            const percentage =
                                                                (quantity /
                                                                    summary.totalPieces) *
                                                                100;

                                                            return (
                                                                <div
                                                                    key={`${size}-bar`}
                                                                    className="flex items-center gap-3"
                                                                >
                                                                    <span className="text-sm font-medium w-12">
                                                                        {size}
                                                                    </span>
                                                                    <Progress
                                                                        className="flex-1"
                                                                        color="secondary"
                                                                        size="sm"
                                                                        value={
                                                                            percentage
                                                                        }
                                                                    />
                                                                    <span className="text-sm text-default-500 w-12 text-right">
                                                                        {
                                                                            quantity
                                                                        }
                                                                    </span>
                                                                </div>
                                                            );
                                                        },
                                                    )}
                                                </div>
                                            </>
                                        );
                                    })()}
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            )}

            {/* Validación y botón de acción */}
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.5 }}
            >
                {/* Lista de verificación */}
                <div className="space-y-2">
                    <div
                        className={`flex items-center gap-2 ${summary.contractor ? "text-success" : "text-gray-400"}`}
                    >
                        <CheckCircleIcon className="w-5 h-5" />
                        <span className="text-sm">
                            Contratista seleccionado
                        </span>
                    </div>
                    <div
                        className={`flex items-center gap-2 ${summary.ordersCount > 0 ? "text-success" : "text-gray-400"}`}
                    >
                        <CheckCircleIcon className="w-5 h-5" />
                        <span className="text-sm">
                            Órdenes seleccionadas ({summary.ordersCount})
                        </span>
                    </div>
                    <div
                        className={`flex items-center gap-2 ${summary.totalPieces > 0 ? "text-success" : "text-gray-400"}`}
                    >
                        <CheckCircleIcon className="w-5 h-5" />
                        <span className="text-sm">
                            Cantidades asignadas ({summary.totalPieces} prendas)
                        </span>
                    </div>
                </div>

                <Divider />

                {/* Botón de acción */}
                <Button
                    fullWidth
                    color="primary"
                    endContent={
                        !isLoading && <ArrowRightIcon className="w-5 h-5" />
                    }
                    isDisabled={!canSubmit}
                    isLoading={isLoading}
                    size="lg"
                    startContent={
                        !isLoading && <DocumentPlusIcon className="w-5 h-5" />
                    }
                    onPress={onSubmit}
                >
                    {isLoading
                        ? "Creando asignaciones..."
                        : "Crear asignaciones y continuar a remisión"}
                </Button>

                {!canSubmit && (
                    <p className="text-sm text-gray-500 text-center">
                        Completa todos los pasos para continuar
                    </p>
                )}
            </motion.div>
        </div>
    );
}
