"use client";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
    <PERSON>ton,
    Card,
    CardBody,
    useDisclosure,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    Input,
    Select,
    SelectItem,
    Textarea,
    addToast,
} from "@heroui/react";
import {
    DocumentPlusIcon,
    TruckIcon,
    CubeIcon,
    ClockIcon,
    ViewColumnsIcon,
    Squares2X2Icon,
    ShieldCheckIcon,
    MagnifyingGlassIcon,
    ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";
import { startOfMonth } from "date-fns";

import {
    EnhancedPackingCard,
    PackingFilters,
    PackingDetailModal,
    EnhancedPackingDocument,
    QualityCheckModal,
} from "@/features/packings/components";
import { usePackings, usePackingPDF } from "@/features/packings/hooks";
import { DashboardHeader } from "@/shared/components/dashboard";
import { assignTransporter } from "@/features/packings/actions";
import { useContractors } from "@/features/contractors/hooks/useContractors";

// Simulación de estados de packing
const packingStatuses = [
    { id: "1", name: "Pendiente" },
    { id: "2", name: "En Proceso" },
    { id: "3", name: "Listo para Entrega" },
    { id: "4", name: "Entregado" },
    { id: "5", name: "Cancelado" },
];

export default function EnhancedUnifiedPackingsPage() {
    const router = useRouter();
    const [filters, setFilters] = useState({});
    const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
    const [selectedPacking, setSelectedPacking] = useState<any>(null);
    const [searchTerm, setSearchTerm] = useState("");

    const { isOpen, onOpen, onClose } = useDisclosure();
    const {
        isOpen: isQualityOpen,
        onOpen: onQualityOpen,
        onClose: onQualityClose,
    } = useDisclosure();
    const {
        isOpen: isTransportOpen,
        onOpen: onTransportOpen,
        onClose: onTransportClose,
    } = useDisclosure();

    const { printPacking, isGenerating } = usePackingPDF();
    const { contractors } = useContractors();

    // Estados para el modal de transporte
    const [transportData, setTransportData] = useState({
        transporterId: "",
        vehicleInfo: "",
        driverName: "",
        driverPhone: "",
    });

    const { packings: rawPackings, isLoading, mutate } = usePackings(filters);

    // Aplicar búsqueda local
    const packings = useMemo(() => {
        if (!rawPackings || !searchTerm) return rawPackings;

        return rawPackings.filter((packing) => {
            const searchLower = searchTerm.toLowerCase();

            return (
                packing.folio.toLowerCase().includes(searchLower) ||
                packing.customer.name.toLowerCase().includes(searchLower) ||
                packing.subCustomer?.name.toLowerCase().includes(searchLower) ||
                packing.trackingNumber?.toLowerCase().includes(searchLower) ||
                packing.driverName?.toLowerCase().includes(searchLower)
            );
        });
    }, [rawPackings, searchTerm]);

    // Calcular métricas mejoradas
    const metrics = useMemo(() => {
        if (!packings)
            return {
                total: 0,
                pending: 0,
                delivered: 0,
                thisMonth: 0,
                qualityPassed: 0,
                withTransport: 0,
                overdue: 0,
                totalPieces: 0,
            };

        const firstDayOfMonth = startOfMonth(new Date());
        const today = new Date();

        return {
            total: packings.length,
            pending: packings.filter((p) => p.status.name === "Pendiente")
                .length,
            delivered: packings.filter((p) => p.status.name === "Entregado")
                .length,
            thisMonth: packings.filter(
                (p) => new Date(p.createdAt) >= firstDayOfMonth,
            ).length,
            qualityPassed: packings.filter((p) => p.qualityCheckPassed).length,
            withTransport: packings.filter((p) => p.transporterId).length,
            overdue: packings.filter(
                (p) =>
                    new Date(p.deliveryDate) < today &&
                    !["Entregado", "Cancelado"].includes(p.status.name),
            ).length,
            totalPieces: packings.reduce(
                (sum, p) => sum + p.details.reduce((s, d) => s + d.quantity, 0),
                0,
            ),
        };
    }, [packings]);

    const handlePrint = async (packing: any) => {
        setSelectedPacking(packing);
        setTimeout(async () => {
            await printPacking(packing.id);
            mutate();
        }, 500); // Increased timeout to allow component to render
    };

    const handleAssignTransport = async () => {
        if (
            !selectedPacking ||
            !transportData.transporterId ||
            !transportData.driverName ||
            !transportData.driverPhone
        ) {
            addToast({
                title: "Error",
                description: "Por favor complete todos los campos requeridos",
                color: "danger",
            });

            return;
        }

        try {
            const result = await assignTransporter({
                packingId: selectedPacking.id,
                ...transportData,
            });

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description:
                        result.message || "Transporte asignado exitosamente",
                    color: "success",
                });
                onTransportClose();
                setTransportData({
                    transporterId: "",
                    vehicleInfo: "",
                    driverName: "",
                    driverPhone: "",
                });
                mutate();
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al asignar transporte",
                    color: "danger",
                });
            }
        } catch (error) {
            addToast({
                title: "Error",
                description: "Error inesperado",
                color: "danger",
            });
        }
    };

    const renderMetrics = () => (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[
                {
                    label: "Total Packings",
                    value: metrics.total,
                    subValue: `${metrics.totalPieces.toLocaleString()} piezas`,
                    icon: CubeIcon,
                    color: "primary",
                    trend: "+12%",
                },
                {
                    label: "Control de Calidad",
                    value: metrics.qualityPassed,
                    subValue: `${metrics.total > 0 ? Math.round((metrics.qualityPassed / metrics.total) * 100) : 0}% aprobados`,
                    icon: ShieldCheckIcon,
                    color: "success",
                    trend:
                        metrics.qualityPassed > 0
                            ? `${metrics.qualityPassed} aprobados`
                            : "Sin verificar",
                },
                {
                    label: "Con Transporte",
                    value: metrics.withTransport,
                    subValue: `${metrics.pending} pendientes`,
                    icon: TruckIcon,
                    color: "secondary",
                    trend: `${metrics.total - metrics.withTransport} sin asignar`,
                },
                {
                    label: "Vencidos",
                    value: metrics.overdue,
                    subValue:
                        metrics.overdue > 0
                            ? "Requieren atención"
                            : "Todo al día",
                    icon: ClockIcon,
                    color: metrics.overdue > 0 ? "danger" : "default",
                    trend:
                        metrics.overdue > 0
                            ? `${metrics.overdue} urgentes`
                            : "Sin retrasos",
                },
            ].map((metric, index) => (
                <motion.div
                    key={metric.label}
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ delay: index * 0.1 }}
                >
                    <Card>
                        <CardBody className="flex flex-row items-center justify-between p-4">
                            <div className="space-y-1">
                                <p className="text-sm text-gray-500">
                                    {metric.label}
                                </p>
                                <p className="text-2xl font-bold">
                                    {metric.value}
                                </p>
                                <p className="text-xs text-gray-400">
                                    {metric.subValue}
                                </p>
                            </div>
                            <div
                                className={`p-3 rounded-full bg-${metric.color}/10`}
                            >
                                <metric.icon
                                    className={`w-6 h-6 text-${metric.color}`}
                                />
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            ))}
        </div>
    );

    return (
        <div className="space-y-6">
            <DashboardHeader
                actions={
                    <div className="flex items-center gap-2">
                        <Input
                            className="w-64"
                            placeholder="Buscar por folio, cliente, tracking..."
                            size="sm"
                            startContent={
                                <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
                            }
                            value={searchTerm}
                            onValueChange={setSearchTerm}
                        />

                        <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={() => {
                                // Implementar exportación
                                addToast({
                                    title: "Información",
                                    description:
                                        "Función de exportación en desarrollo",
                                    color: "primary",
                                });
                            }}
                        >
                            <ArrowDownTrayIcon className="w-4 h-4" />
                        </Button>

                        <div className="flex gap-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
                            <Button
                                isIconOnly
                                size="sm"
                                variant={
                                    viewMode === "grid" ? "solid" : "light"
                                }
                                onPress={() => setViewMode("grid")}
                            >
                                <Squares2X2Icon className="w-4 h-4" />
                            </Button>
                            <Button
                                isIconOnly
                                size="sm"
                                variant={
                                    viewMode === "list" ? "solid" : "light"
                                }
                                onPress={() => setViewMode("list")}
                            >
                                <ViewColumnsIcon className="w-4 h-4" />
                            </Button>
                        </div>

                        <Button
                            color="primary"
                            startContent={
                                <DocumentPlusIcon className="w-5 h-5" />
                            }
                            onPress={() =>
                                router.push("/dashboard/packings/new")
                            }
                        >
                            Nuevo Packing
                        </Button>
                    </div>
                }
                subtitle="Administra los packing lists con control de calidad y trazabilidad completa"
                title="Gestión de Packings"
            />

            {renderMetrics()}

            <PackingFilters
                defaultFilters={filters}
                packingStatuses={packingStatuses}
                onFiltersChange={setFilters}
            />

            <div
                className={`grid gap-4 ${
                    viewMode === "grid"
                        ? "grid-cols-1 md:grid-cols-2 xl:grid-cols-3"
                        : "grid-cols-1"
                }`}
            >
                {packings?.map((packing) => (
                    <EnhancedPackingCard
                        key={packing.id}
                        packing={packing as any}
                        onAssignTransport={() => {
                            setSelectedPacking(packing);
                            onTransportOpen();
                        }}
                        onPrint={() => handlePrint(packing)}
                        onQualityCheck={() => {
                            setSelectedPacking(packing);
                            onQualityOpen();
                        }}
                        onViewDetails={() => {
                            setSelectedPacking(packing);
                            onOpen();
                        }}
                    />
                ))}
            </div>

            {/* Modal de detalles */}
            {selectedPacking && (
                <>
                    <PackingDetailModal
                        isOpen={isOpen}
                        packing={selectedPacking}
                        onClose={onClose}
                        onPrint={() => handlePrint(selectedPacking)}
                        onUpdateStatus={() => {
                            router.push(
                                `/dashboard/packings/${selectedPacking.id}/edit`,
                            );
                        }}
                    />

                    {/* Modal de control de calidad */}
                    <QualityCheckModal
                        isOpen={isQualityOpen}
                        packing={selectedPacking}
                        onClose={onQualityClose}
                        onComplete={() => {
                            mutate();
                            setSelectedPacking(null);
                        }}
                    />

                    {/* Modal de asignación de transporte */}
                    <Modal isOpen={isTransportOpen} onClose={onTransportClose}>
                        <ModalContent>
                            <ModalHeader>
                                <div className="flex items-center gap-2">
                                    <TruckIcon className="w-6 h-6 text-secondary" />
                                    <h2 className="text-xl font-semibold">
                                        Asignar Transporte
                                    </h2>
                                </div>
                            </ModalHeader>
                            <ModalBody>
                                <div className="space-y-4">
                                    <Select
                                        isRequired
                                        label="Transportista"
                                        placeholder="Seleccione un transportista"
                                        value={transportData.transporterId}
                                        onChange={(e) =>
                                            setTransportData({
                                                ...transportData,
                                                transporterId: e.target.value,
                                            })
                                        }
                                    >
                                        {contractors?.map((contractor) => (
                                            <SelectItem key={contractor.id}>
                                                {contractor.name}
                                            </SelectItem>
                                        ))}
                                    </Select>

                                    <Input
                                        isRequired
                                        label="Nombre del Conductor"
                                        placeholder="Ingrese el nombre del conductor"
                                        value={transportData.driverName}
                                        onValueChange={(value) =>
                                            setTransportData({
                                                ...transportData,
                                                driverName: value,
                                            })
                                        }
                                    />

                                    <Input
                                        isRequired
                                        label="Teléfono del Conductor"
                                        placeholder="Ingrese el teléfono del conductor"
                                        value={transportData.driverPhone}
                                        onValueChange={(value) =>
                                            setTransportData({
                                                ...transportData,
                                                driverPhone: value,
                                            })
                                        }
                                    />

                                    <Textarea
                                        label="Información del Vehículo"
                                        minRows={2}
                                        placeholder="Marca, modelo, placas, etc."
                                        value={transportData.vehicleInfo}
                                        onValueChange={(value) =>
                                            setTransportData({
                                                ...transportData,
                                                vehicleInfo: value,
                                            })
                                        }
                                    />
                                </div>
                            </ModalBody>
                            <ModalFooter>
                                <Button
                                    variant="light"
                                    onPress={onTransportClose}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    color="secondary"
                                    startContent={
                                        <TruckIcon className="w-5 h-5" />
                                    }
                                    onPress={handleAssignTransport}
                                >
                                    Asignar Transporte
                                </Button>
                            </ModalFooter>
                        </ModalContent>
                    </Modal>

                    {/* Documento oculto para impresión */}
                    <div
                        style={{
                            position: "absolute",
                            left: "-9999px",
                            top: "-9999px",
                            width: "210mm",
                            backgroundColor: "white",
                        }}
                    >
                        <EnhancedPackingDocument packing={selectedPacking} />
                    </div>
                </>
            )}
        </div>
    );
}
