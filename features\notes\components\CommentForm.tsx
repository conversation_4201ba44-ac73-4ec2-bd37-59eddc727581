"use client";

import React, { useState } from "react";
import { Button, Textarea } from "@heroui/react";

interface CommentFormProps {
    initialContent?: string;
    onSubmit: (content: string) => Promise<void>;
    onCancel?: () => void;
    submitLabel?: string;
    placeholder?: string;
}

export function CommentForm({
    initialContent = "",
    onSubmit,
    onCancel,
    submitLabel = "Comentar",
    placeholder = "Escribe un comentario...",
}: CommentFormProps) {
    const [content, setContent] = useState(initialContent);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!content.trim()) return;

        setIsSubmitting(true);
        try {
            await onSubmit(content.trim());
            setContent("");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <form className="space-y-2" onSubmit={handleSubmit}>
            <Textarea
                className="w-full"
                maxRows={4}
                minRows={2}
                placeholder={placeholder}
                size="sm"
                value={content}
                variant="bordered"
                onChange={(e) => setContent(e.target.value)}
            />

            <div className="flex gap-2 justify-end">
                {onCancel && (
                    <Button
                        isDisabled={isSubmitting}
                        size="sm"
                        variant="light"
                        onClick={onCancel}
                    >
                        Cancelar
                    </Button>
                )}
                <Button
                    color="primary"
                    isDisabled={!content.trim()}
                    isLoading={isSubmitting}
                    size="sm"
                    type="submit"
                >
                    {submitLabel}
                </Button>
            </div>
        </form>
    );
}
