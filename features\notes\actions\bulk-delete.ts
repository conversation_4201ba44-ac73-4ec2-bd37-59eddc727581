"use server";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { revalidateData } from "@/shared/utils/actions-utils";

type BulkDeleteNotesParams = {
    noteIds: string[];
};

export async function bulkDeleteNotes({ noteIds }: BulkDeleteNotesParams) {
    // Verificar sesión de usuario
    const session = await auth();

    if (!session || !session.user) {
        // REMOVED: console.error("bulkDeleteNotes: Usuario no autenticado");

        return {
            success: false,
            error: "No estás autorizado para realizar esta acción",
        };
    }

    // REMOVED: console.log(`bulkDeleteNotes: Eliminando ${noteIds.length} notas`);

    try {
        // Solo Admin puede eliminar notas que no le pertenecen
        const userRole = session.user.role;
        const userId = session.user.id;

        if ((userRole as any)?.name !== "ADMIN") {
            return {
                success: false,
                error: "Solo administradores pueden eliminar notas en grupo",
            };
        }

        // Obtener las órdenes afectadas antes de eliminar
        const affectedOrders = await db.note.findMany({
            where: {
                id: { in: noteIds },
            },
            select: {
                orderId: true,
            },
            distinct: ["orderId"],
        });

        // Eliminar las notas
        const result = await db.note.deleteMany({
            where: {
                id: { in: noteIds },
            },
        });

        // REMOVED: console.log(`bulkDeleteNotes: Eliminadas ${result.count} notas`);

        // Revalidar rutas
        revalidateData(["/dashboard/notes"]);

        // Revalidar rutas para cada orden afectada
        for (const { orderId } of affectedOrders) {
            if (orderId) {
                revalidateData([`/dashboard/orders/${orderId}`]);
            }
        }

        return {
            success: true,
            data: {
                deletedCount: result.count,
            },
        };
    } catch (error) {
        // REMOVED: console.error("bulkDeleteNotes: Error al eliminar notas:", error);

        return {
            success: false,
            error: "Ocurrió un error al eliminar las notas",
        };
    }
}
