/* Estilos para el documento de remisión */
.remissionDocument {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    color: #000;
    background-color: #fff;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

/* Estilos para impresión de alta calidad */
@media print, (min-resolution: 300dpi) {
    .remissionDocument {
        font-size: 10pt !important;
        line-height: 1.3 !important;
        color: #000 !important;
    }
    
    .remissionDocument table {
        font-size: 9pt !important;
        border-color: #d1d5db !important;
    }
    
    .remissionDocument h1 {
        font-size: 16pt !important;
        color: #111827 !important;
    }
    
    .remissionDocument h2 {
        font-size: 12pt !important;
        color: #1d4ed8 !important;
    }
    
    /* Asegurar que los bordes se impriman */
    .remissionDocument td,
    .remissionDocument th {
        border: 1px solid #d1d5db !important;
    }
}

/* Estilos para modo landscape */
.landscapeMode {
    width: 260mm !important;
    max-width: 260mm !important;
    margin: 0 auto;
}

/* Ajustes de fuente para landscape */
.landscape-document {
    font-size: 10pt !important;
    line-height: 1.3 !important;
}

.landscape-document table {
    font-size: 9pt !important;
}

.landscape-document th,
.landscape-document td {
    padding: 6px 8px !important;
}

.landscape-document h1 {
    font-size: 18pt !important;
    margin-bottom: 8px !important;
}

.landscape-document h2 {
    font-size: 13pt !important;
    margin-bottom: 6px !important;
}

/* Tabla optimizada para impresión */
.remissionTable {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.remissionTable th {
    background-color: #f3f4f6 !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
}

.remissionTable td {
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
}

/* Optimización para PDF */
.pdf-generating {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    background-color: white !important;
}

.pdf-generating * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Clase para el documento durante generación de PDF */
.pdf-generating .remission-document {
    box-shadow: none !important;
    border: none !important;
    page-break-inside: avoid !important;
}

/* Mejorar visibilidad de tablas en PDF */
.pdf-generating table {
    border-collapse: collapse !important;
}

.pdf-generating th,
.pdf-generating td {
    border: 1px solid #cbd5e1 !important;
    padding: 8px !important;
}

.pdf-generating th {
    background-color: #f1f5f9 !important;
    font-weight: 600 !important;
}

.pdf-generating tr:nth-child(even) {
    background-color: #f8fafc !important;
}

/* Estilos específicos para el modo portrait */
.portrait-mode {
    width: 190mm !important;
    max-width: 190mm !important;
    margin: 0 auto;
}

/* Ajustes para asegurar buena impresión */
@page {
    size: A4;
    margin: 10mm;
}

@media print {
    .landscape-mode {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    .portrait-mode {
        width: 100% !important;
        max-width: 100% !important;
    }
}