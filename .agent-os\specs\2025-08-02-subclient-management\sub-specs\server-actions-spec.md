# Server Actions Specification

This is the Server Actions specification for the spec detailed in @.agent-os/specs/2025-08-02-subclient-management/spec.md

## Implementation Status

### ✅ Acciones YA EXISTENTES (No Crear - Solo Usar)

Estas acciones ya están implementadas y funcionando:

#### `/features/customers/actions/create-subcustomer.ts`
```typescript
export async function createSubcustomer(data: {
  name: string;
  parentId: string;
}): Promise<ActionResult<Customer>>
```
- ✅ Validación con Zod
- ✅ Verificación de padre existente  
- ✅ Validación de nombres únicos
- ✅ Revalidación de paths

#### `/features/customers/actions/get-subcustomers.ts`
```typescript
export async function getSubCustomers(parentId: string): Promise<ActionResult<Customer[]>>
export async function getCustomerHierarchy(customerId: string): Promise<ActionResult<Customer>>
export async function getAllCustomersWithHierarchy(): Promise<ActionResult<Customer[]>>
```
- ✅ Incluye conteos (_count) de orders y packings
- ✅ Ordenamiento por nombre
- ✅ Relaciones parent/subCustomers cargadas

#### `/features/customers/actions/update-hierarchy.ts`
```typescript
export async function updateCustomerHierarchy(data: {
  customerId: string;
  parentId: string | null;
}): Promise<ActionResult<Customer>>

export async function removeFromHierarchy(customerId: string): Promise<ActionResult<Customer>>
```
- ✅ Validación de referencias circulares con función recursiva
- ✅ Verificación de padres existentes
- ✅ Revalidación múltiple de paths afectados

## 🆕 Acciones NUEVAS A IMPLEMENTAR

### updateSubcustomer.ts (NUEVA)

**Ubicación**: `/features/customers/actions/update-subcustomer.ts`

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { db, handleDbError } from "@/shared/lib/db";

const updateSubcustomerSchema = z.object({
  id: z.string().min(1, "ID es requerido"),
  name: z.string().min(1, "El nombre es requerido"),
  displayName: z.string().optional(),
});

export async function updateSubcustomer(
  data: z.infer<typeof updateSubcustomerSchema>
): Promise<{ success: true; data: Customer } | { success: false; error: string }> {
  try {
    const validated = updateSubcustomerSchema.parse(data);

    // Verificar que el subcliente existe y es realmente un subcliente
    const subcustomer = await db.customer.findUnique({
      where: { id: validated.id },
      include: { parent: true }
    });

    if (!subcustomer) {
      return { success: false, error: "Subcliente no encontrado" };
    }

    if (!subcustomer.parentId) {
      return { success: false, error: "No se puede actualizar un cliente principal desde aquí" };
    }

    // Verificar nombres únicos (excluyendo el actual)
    const existing = await db.customer.findFirst({
      where: { 
        name: validated.name, 
        id: { not: validated.id } 
      }
    });

    if (existing) {
      return { success: false, error: "Ya existe un cliente con ese nombre" };
    }

    // Actualizar subcliente
    const updated = await db.customer.update({
      where: { id: validated.id },
      data: {
        name: validated.name,
        displayName: validated.displayName || null,
      },
      include: {
        parent: true,
        subCustomers: true,
        _count: {
          select: {
            orders: true,
            packings: true,
            subCustomers: true,
          }
        }
      }
    });

    // Revalidar paths afectados
    revalidatePath("/dashboard/customers");
    revalidatePath(`/dashboard/customers/${validated.id}/details`);
    if (subcustomer.parentId) {
      revalidatePath(`/dashboard/customers/${subcustomer.parentId}/details`);
    }

    return { success: true, data: updated };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0]?.message || "Datos inválidos" };
    }
    return handleDbError(() => { throw error; }, "Error al actualizar subcliente");
  }
}
```

### deleteSubcustomer.ts (NUEVA)

**Ubicación**: `/features/customers/actions/delete-subcustomer.ts`

```typescript
"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { db, handleDbError } from "@/shared/lib/db";

const deleteSubcustomerSchema = z.object({
  id: z.string().min(1, "ID es requerido"),
});

export async function deleteSubcustomer(
  data: z.infer<typeof deleteSubcustomerSchema>
): Promise<{ success: true; data: boolean } | { success: false; error: string }> {
  try {
    const validated = deleteSubcustomerSchema.parse(data);

    // Verificar que el subcliente existe y es realmente un subcliente
    const subcustomer = await db.customer.findUnique({
      where: { id: validated.id },
      include: {
        parent: true,
        _count: {
          select: {
            orders: true,
            packings: true,
            subCustomers: true,
          }
        }
      }
    });

    if (!subcustomer) {
      return { success: false, error: "Subcliente no encontrado" };
    }

    if (!subcustomer.parentId) {
      return { success: false, error: "No se puede eliminar un cliente principal desde aquí" };
    }

    // Verificar dependencias antes de eliminar
    if (subcustomer._count.orders > 0) {
      return { 
        success: false, 
        error: `No se puede eliminar: tiene ${subcustomer._count.orders} órdenes asociadas` 
      };
    }

    if (subcustomer._count.packings > 0) {
      return { 
        success: false, 
        error: `No se puede eliminar: tiene ${subcustomer._count.packings} empaques asociados` 
      };
    }

    if (subcustomer._count.subCustomers > 0) {
      return { 
        success: false, 
        error: `No se puede eliminar: tiene ${subcustomer._count.subCustomers} subclientes asociados` 
      };
    }

    // Eliminar subcliente
    await db.customer.delete({
      where: { id: validated.id }
    });

    // Revalidar paths afectados
    revalidatePath("/dashboard/customers");
    revalidatePath(`/dashboard/customers/${subcustomer.parentId}/details`);

    return { success: true, data: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors[0]?.message || "Datos inválidos" };
    }
    return handleDbError(() => { throw error; }, "Error al eliminar subcliente");
  }
}

// Función auxiliar para validación previa
export async function validateSubcustomerDeletion(id: string) {
  try {
    const subcustomer = await db.customer.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            orders: true,
            packings: true,
            subCustomers: true,
          }
        }
      }
    });

    if (!subcustomer) {
      return { success: false, error: "Subcliente no encontrado" };
    }

    const canDelete = 
      subcustomer._count.orders === 0 && 
      subcustomer._count.packings === 0 && 
      subcustomer._count.subCustomers === 0;

    const dependencies = {
      orders: subcustomer._count.orders,
      packings: subcustomer._count.packings,
      subCustomers: subcustomer._count.subCustomers,
    };

    return {
      success: true,
      data: {
        canDelete,
        dependencies,
        reason: !canDelete ? "Tiene dependencias asociadas" : null
      }
    };
  } catch (error) {
    return handleDbError(() => { throw error; }, "Error al validar eliminación");
  }
}
```

## Integración con index.ts

**Actualizar**: `/features/customers/actions/index.ts`

```typescript
// Agregar exports de las nuevas acciones
export { updateSubcustomer } from './update-subcustomer';
export { deleteSubcustomer, validateSubcustomerDeletion } from './delete-subcustomer';

// Las existentes ya están exportadas:
// export { createSubCustomer } from './create-subcustomer';
// export { getSubCustomers, getCustomerHierarchy, getAllCustomersWithHierarchy } from './get-subcustomers';
// export { updateCustomerHierarchy, removeFromHierarchy } from './update-hierarchy';
```

## Patrones de Error y Revalidación

**Error Messages Estandarizados**:
- "Subcliente no encontrado"
- "El nombre es requerido"
- "Ya existe un cliente con ese nombre"
- "No se puede eliminar: tiene X órdenes asociadas"
- "No se puede eliminar: tiene X empaques asociados"
- "No se puede actualizar un cliente principal desde aquí"

**Revalidación de Paths**:
```typescript
// Siempre revalidar estos paths después de cambios
revalidatePath("/dashboard/customers");
revalidatePath(`/dashboard/customers/${customerId}/details`);
// Para cambios de jerarquía, también revalidar el padre
revalidatePath(`/dashboard/customers/${parentId}/details`);
```