# 👕 Garments Feature

## 📋 Descripción
Módulo para gestión del catálogo de prendas, tallas, colores y especificaciones técnicas.

## 🏗️ Componentes

### Garments
Componente principal para gestión de prendas con tallas y colores.
```tsx
import { Garments } from '@/features/garments'
```

### GarmentCatalog
Catálogo completo de prendas disponibles.

### SizeManager
Gestión de tallas y medidas.

## 🪝 Hooks

### useSize
Hook para gestión de tallas.
```tsx
const { sizes, addSize, removeSize } = useSize()
```

### useGarments
Hook principal para prendas.
```tsx
const { garments, loading } = useGarments()
```

## 🎯 Uso

```tsx
import { Garments, useGarments } from '@/features/garments'

export default function GarmentsPage() {
  const { garments } = useGarments()
  return <Garments items={garments} />
}
```