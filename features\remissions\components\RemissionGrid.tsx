"use client";

import React from "react";
import { AnimatePresence } from "framer-motion";
import { Spinner } from "@heroui/react";

import RemissionCard from "./RemissionCard";

interface RemissionGridProps {
    remissions: Array<{
        id: string;
        folio: string;
        status: string;
        createdAt: string | Date;
        printedAt?: string | Date | null;
        contractor?: {
            id: string;
            name: string;
        } | null;
        remissionItems?: Array<{
            quantity: number;
        }>;
        _count?: {
            remissionItems: number;
        };
    }>;
    onPrint?: (id: string) => void;
    loading?: boolean;
    emptyMessage?: string;
}

export default function RemissionGrid({
    remissions,
    onPrint,
    loading = false,
    emptyMessage = "No se encontraron remisiones",
}: RemissionGridProps) {
    if (loading) {
        return (
            <div className="flex items-center justify-center py-12">
                <Spinner size="lg" />
            </div>
        );
    }

    if (remissions.length === 0) {
        return (
            <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">
                    {emptyMessage}
                </p>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
                {remissions.map((remission, index) => (
                    <RemissionCard
                        key={remission.id}
                        index={index}
                        remission={remission}
                        onPrint={onPrint}
                    />
                ))}
            </AnimatePresence>
        </div>
    );
}
