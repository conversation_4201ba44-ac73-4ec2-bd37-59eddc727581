# Fonts Directory

This directory is for storing font files used in PDF generation.

## How to add custom fonts:

1. Download the font files (e.g., Inter-Regular.ttf, Inter-Bold.ttf)
2. Place them in this directory
3. Update the RemissionTemplate.tsx file to register the fonts:

```typescript
import { Font } from '@react-pdf/renderer';

Font.register({
  family: 'Inter',
  fonts: [
    { src: '/fonts/Inter-Regular.ttf' },
    { src: '/fonts/Inter-Bold.ttf', fontWeight: 'bold' },
  ],
});
```

4. Update the styles in remission.ts to use 'Inter' instead of 'Helvetica'

## Recommended fonts:
- Inter (modern, clean)
- Roboto (Google's font)
- Open Sans (readable)
- Source Sans Pro (Adobe's font)
