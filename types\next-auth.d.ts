// types/next-auth.d.ts (archivo nuevo)
import "next-auth";
import type { DefaultSession } from "next-auth";
import type { Role } from "@prisma/client";

declare module "next-auth" {
    interface User {
        id: string;
        role: Role;
        remember?: boolean;
    }

    interface Session {
        user: {
            id: string;
            role: Role;
        } & DefaultSession["user"];
    }
}

declare module "@auth/core/adapters" {
    interface AdapterUser {
        id?: string;
        role: string;
    }
}

declare module "@auth/core/jwt" {
    interface JWT {
        id?: string;
        role: string;
    }
}

declare module "next-auth/jwt" {
    interface JWT {
        id: string;
        role: Role;
        maxAge?: number;
    }
}
