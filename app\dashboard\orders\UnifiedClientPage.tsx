"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
    PencilIcon,
    TrashIcon,
    EyeIcon,
    CubeIcon,
    ClockIcon,
    CheckCircleIcon,
    XCircleIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { CrudListTemplate } from "@/shared/templates";
import { Order } from "@/features/orders/types/orders";
import { useOrdersData } from "@/features/orders/hooks/useOrdersData";
import { useOrdersState } from "@/features/orders/hooks/useOrdersState";
import OrderDetailModal from "@/features/orders/components/OrderDetailModal";
import { Chip } from "@/shared/components/ui/hero-ui-client";

interface UnifiedOrdersClientPageProps {
    currentUserId?: string;
    userRole?: string;
}

export default function UnifiedOrdersClientPage({
    currentUserId,
    userRole,
}: UnifiedOrdersClientPageProps) {
    const router = useRouter();
    const { state, actions: stateActions } = useOrdersState();
    const {
        data: { orders, orderStatuses },
        status: { isLoading },
        operations: { deleteOrder, fetchOrder },
    } = useOrdersData(state);

    const [selectedOrder, setSelectedOrder] = useState<any>(null);
    const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

    // Estadísticas
    const stats = [
        {
            title: "Total de Órdenes",
            value: orders.length,
            icon: <CubeIcon className="w-6 h-6" />,
            color: "primary" as const,
            description: "Órdenes activas",
        },
        {
            title: "Pendientes",
            value: orders.filter((o: Order) => o.status === "pending").length,
            icon: <ClockIcon className="w-6 h-6" />,
            color: "warning" as const,
            change: 5,
            changeLabel: "vs. semana pasada",
        },
        {
            title: "Completadas",
            value: orders.filter((o: Order) => o.status === "completed").length,
            icon: <CheckCircleIcon className="w-6 h-6" />,
            color: "success" as const,
            change: 12,
            changeLabel: "este mes",
        },
        {
            title: "Canceladas",
            value: orders.filter((o: Order) => o.status === "cancelled").length,
            icon: <XCircleIcon className="w-6 h-6" />,
            color: "danger" as const,
            change: -2,
            changeLabel: "vs. mes anterior",
        },
    ];

    // Columnas de la tabla
    const columns = [
        {
            key: "cutOrder",
            label: "Número de Corte",
            sortable: true,
            render: (order: Order) => (
                <span className="font-medium text-gray-900 dark:text-white">
                    {order.cutOrder || "-"}
                </span>
            ),
        },
        {
            key: "customer",
            label: "Cliente",
            sortable: true,
            render: (order: Order) => (
                <span>{order.customer?.name || "-"}</span>
            ),
        },
        {
            key: "model",
            label: "Modelo",
            render: (order: Order) => {
                const modelCode = order.garments?.[0]?.model?.code || "-";

                return <span>{modelCode}</span>;
            },
        },
        {
            key: "deliveryDate",
            label: "Fecha de Entrega",
            sortable: true,
            render: (order: Order) => (
                <span>
                    {(order as any).deliveryDate
                        ? format(
                              new Date((order as any).deliveryDate),
                              "dd MMM yyyy",
                              { locale: es },
                          )
                        : "-"}
                </span>
            ),
        },
        {
            key: "status",
            label: "Estado",
            render: (order: Order) => {
                const statusConfig = {
                    pending: { color: "warning" as const, label: "Pendiente" },
                    in_progress: {
                        color: "primary" as const,
                        label: "En Proceso",
                    },
                    completed: {
                        color: "success" as const,
                        label: "Completada",
                    },
                    cancelled: { color: "danger" as const, label: "Cancelada" },
                };
                const config = statusConfig[
                    order.status as keyof typeof statusConfig
                ] || { color: "default" as const, label: String(order.status) };

                return (
                    <Chip color={config.color} size="sm" variant="flat">
                        {config.label}
                    </Chip>
                );
            },
        },
    ];

    // Acciones de la tabla
    const actions = [
        {
            label: "Ver Detalles",
            icon: <EyeIcon className="w-4 h-4" />,
            onClick: async (order: Order) => {
                const fullOrder = await fetchOrder(order.id, false);

                if (fullOrder) {
                    setSelectedOrder(fullOrder);
                    setIsDetailModalOpen(true);
                }
            },
        },
        {
            label: "Editar",
            icon: <PencilIcon className="w-4 h-4" />,
            onClick: (order: Order) => {
                router.push(`/dashboard/orders/${order.id}/edit`);
            },
            color: "primary" as const,
        },
        {
            label: "Eliminar",
            icon: <TrashIcon className="w-4 h-4" />,
            onClick: async (order: Order) => {
                if (confirm("¿Estás seguro de eliminar esta orden?")) {
                    await deleteOrder(order.id);
                }
            },
            color: "danger" as const,
            isDisabled: (order: Order) => userRole !== "admin",
        },
    ];

    // Filtros
    const filters = [
        {
            key: "status",
            label: "Estado",
            type: "select" as const,
            placeholder: "Todos los estados",
            options: [
                { value: "all", label: "Todos" },
                { value: "pending", label: "Pendiente" },
                { value: "in_progress", label: "En Proceso" },
                { value: "completed", label: "Completada" },
                { value: "cancelled", label: "Cancelada" },
            ],
        },
        {
            key: "deliveryDate",
            label: "Fecha de Entrega",
            type: "date" as const,
            placeholder: "Seleccionar fecha",
        },
    ];

    // Opciones de ordenamiento
    const sortOptions = [
        {
            key: "cutOrder-asc",
            label: "Número de Corte",
            field: "cutOrder",
            direction: "asc" as const,
        },
        {
            key: "cutOrder-desc",
            label: "Número de Corte",
            field: "cutOrder",
            direction: "desc" as const,
        },
        {
            key: "deliveryDate-asc",
            label: "Fecha de Entrega",
            field: "deliveryDate",
            direction: "asc" as const,
        },
        {
            key: "deliveryDate-desc",
            label: "Fecha de Entrega",
            field: "deliveryDate",
            direction: "desc" as const,
        },
        {
            key: "createdAt-desc",
            label: "Fecha de Creación",
            field: "createdAt",
            direction: "desc" as const,
        },
    ];

    return (
        <>
            <CrudListTemplate
                // Layout
                columns={columns}
                data={orders}
                isLoading={isLoading}
                searchValue={state.search}
                sortOptions={sortOptions}
                subtitle="Gestiona las órdenes de producción"
                title="Órdenes"
                totalPages={Math.ceil(orders.length / state.limit)}
                onClearFilters={() => {
                    stateActions.setSearch("");
                    stateActions.setStatus("");
                }}
                onFilterChange={(key, value) => {
                    if (key === "status") {
                        stateActions.setStatus(value === "all" ? "" : value);
                    }
                }}
                onPageChange={stateActions.setPage}
                onSearchChange={stateActions.setSearch}
                actions={actions}
                // Create
                createRoute="/dashboard/orders/new"
                activeFiltersCount={state.status ? 1 : 0}
                // Pagination
                page={state.page}
                breadcrumbs={[{ label: "Órdenes" }]}
                // Stats
                stats={stats}
                createLabel="Nueva Orden"
                // Filters
                filters={filters}
                emptyContent="No hay órdenes registradas"
                // Table
                filterValues={{ status: state.status }}
            />

            {/* Modal de detalles */}
            {selectedOrder && (
                <OrderDetailModal
                    currentUserId={currentUserId}
                    isOpen={isDetailModalOpen}
                    order={selectedOrder}
                    userRole={userRole}
                    onClose={() => setIsDetailModalOpen(false)}
                    onDelete={async (orderId) => {
                        await deleteOrder(orderId);
                        setIsDetailModalOpen(false);
                    }}
                />
            )}
        </>
    );
}
