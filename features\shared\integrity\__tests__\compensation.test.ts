import { IntegrityManager } from "../IntegrityManager";

import {
    createMockOperationLog,
    setupIntegrityManager,
    mockTransactionBehavior,
    assertOperationLogCreated,
    assertOperationLogCompleted,
    assertOperationLogFailed,
} from "./test-utils";

describe("IntegrityManager - Compensation Strategies", () => {
    let mockPrisma: any;
    let integrityManager: IntegrityManager;

    beforeEach(() => {
        const setup = setupIntegrityManager();

        mockPrisma = setup.prisma;
        integrityManager = setup.integrityManager;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe("compensateAssignmentBatch", () => {
        it("should compensate assignment batch by cancelling assignments", async () => {
            // Arrange
            const operationLog = createMockOperationLog({
                id: "op-123",
                metadata: {
                    assignmentIds: ["assign-1", "assign-2", "assign-3"],
                    garmentSizeUpdates: [
                        { id: "size-1", quantityDelta: 10 },
                        { id: "size-2", quantityDelta: 20 },
                    ],
                },
            });

            mockPrisma.operationLog.update.mockResolvedValue(operationLog);
            mockPrisma.assignment.updateMany.mockResolvedValue({ count: 3 });
            mockPrisma.garmentSize.update.mockResolvedValue({});
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            await integrityManager.compensationStrategies.compensateAssignmentBatch(
                operationLog,
                mockPrisma,
            );

            // Assert
            expect(mockPrisma.assignment.updateMany).toHaveBeenCalledWith({
                where: {
                    id: { in: ["assign-1", "assign-2", "assign-3"] },
                },
                data: {
                    status: "CANCELLED",
                    cancelledAt: expect.any(Date),
                    cancelReason: "Compensation for failed operation op-123",
                },
            });

            // Verify garment size quantities were restored
            expect(mockPrisma.garmentSize.update).toHaveBeenCalledTimes(2);
            expect(mockPrisma.garmentSize.update).toHaveBeenCalledWith({
                where: { id: "size-1" },
                data: {
                    availableQuantity: { decrement: 10 },
                    assignedQuantity: { increment: 10 },
                },
            });

            // Verify operation log was marked as compensated
            expect(mockPrisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "op-123" },
                data: {
                    compensatedAt: expect.any(Date),
                    status: "COMPENSATED",
                },
            });
        });

        it("should handle partial compensation when some assignments already cancelled", async () => {
            // Arrange
            const operationLog = createMockOperationLog({
                id: "op-123",
                metadata: {
                    assignmentIds: ["assign-1", "assign-2"],
                    garmentSizeUpdates: [{ id: "size-1", quantityDelta: 10 }],
                },
            });

            mockPrisma.operationLog.update.mockResolvedValue(operationLog);
            mockPrisma.assignment.updateMany.mockResolvedValue({ count: 1 }); // Only 1 updated
            mockPrisma.garmentSize.update.mockResolvedValue({});
            mockTransactionBehavior(mockPrisma, "success");

            // Act
            await integrityManager.compensationStrategies.compensateAssignmentBatch(
                operationLog,
                mockPrisma,
            );

            // Assert
            expect(mockPrisma.assignment.updateMany).toHaveBeenCalled();
            expect(mockPrisma.garmentSize.update).toHaveBeenCalled();
            expect(mockPrisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "op-123" },
                data: {
                    compensatedAt: expect.any(Date),
                    status: "COMPENSATED",
                },
            });
        });

        it("should skip compensation if no metadata available", async () => {
            // Arrange
            const operationLog = createMockOperationLog({
                id: "op-123",
                metadata: {}, // Empty metadata
            });

            // Act
            await integrityManager.compensationStrategies.compensateAssignmentBatch(
                operationLog,
                mockPrisma,
            );

            // Assert
            expect(mockPrisma.assignment.updateMany).not.toHaveBeenCalled();
            expect(mockPrisma.garmentSize.update).not.toHaveBeenCalled();
            expect(mockPrisma.operationLog.update).toHaveBeenCalledWith({
                where: { id: "op-123" },
                data: {
                    compensatedAt: expect.any(Date),
                    status: "COMPENSATED",
                },
            });
        });

        it("should handle compensation failure gracefully", async () => {
            // Arrange
            const operationLog = createMockOperationLog({
                id: "op-123",
                metadata: {
                    assignmentIds: ["assign-1"],
                },
            });

            mockPrisma.assignment.updateMany.mockRejectedValue(
                new Error("Database error"),
            );

            // Act & Assert
            await expect(
                integrityManager.compensationStrategies.compensateAssignmentBatch(
                    operationLog,
                    mockPrisma,
                ),
            ).rejects.toThrow("Database error");

            // Operation log should not be marked as compensated
            expect(mockPrisma.operationLog.update).not.toHaveBeenCalled();
        });
    });

    describe("executeWithIntegrity", () => {
        it("should execute operation successfully and update log", async () => {
            // Arrange
            const operationLog = createMockOperationLog({ id: "op-123" });

            mockPrisma.operationLog.create.mockResolvedValue(operationLog);
            mockPrisma.operationLog.update.mockResolvedValue(operationLog);

            const mockOperation = jest
                .fn()
                .mockResolvedValue({ success: true });
            const mockCompensation = jest.fn();

            // Act
            const result = await integrityManager.executeWithIntegrity(
                {
                    type: "TEST_OPERATION",
                    userId: "user-123",
                    metadata: { test: true },
                },
                mockOperation,
                mockCompensation,
            );

            // Assert
            expect(result).toEqual({ success: true });
            expect(mockOperation).toHaveBeenCalled();
            expect(mockCompensation).not.toHaveBeenCalled();
            assertOperationLogCreated(mockPrisma, "TEST_OPERATION");
            assertOperationLogCompleted(mockPrisma, "op-123");
        });

        it("should execute compensation on operation failure", async () => {
            // Arrange
            const operationLog = createMockOperationLog({ id: "op-123" });

            mockPrisma.operationLog.create.mockResolvedValue(operationLog);
            mockPrisma.operationLog.update.mockResolvedValue(operationLog);

            const mockOperation = jest
                .fn()
                .mockRejectedValue(new Error("Operation failed"));
            const mockCompensation = jest.fn().mockResolvedValue(undefined);

            // Act & Assert
            await expect(
                integrityManager.executeWithIntegrity(
                    {
                        type: "TEST_OPERATION",
                        userId: "user-123",
                        metadata: {},
                    },
                    mockOperation,
                    mockCompensation,
                ),
            ).rejects.toThrow("Operation failed");

            expect(mockOperation).toHaveBeenCalled();
            expect(mockCompensation).toHaveBeenCalledWith(
                operationLog,
                mockPrisma,
            );
            assertOperationLogFailed(mockPrisma, "op-123");
        });

        it("should handle compensation failure", async () => {
            // Arrange
            const operationLog = createMockOperationLog({ id: "op-123" });

            mockPrisma.operationLog.create.mockResolvedValue(operationLog);
            mockPrisma.operationLog.update.mockResolvedValue(operationLog);

            const mockOperation = jest
                .fn()
                .mockRejectedValue(new Error("Operation failed"));
            const mockCompensation = jest
                .fn()
                .mockRejectedValue(new Error("Compensation failed"));

            // Act & Assert
            await expect(
                integrityManager.executeWithIntegrity(
                    {
                        type: "TEST_OPERATION",
                        userId: "user-123",
                        metadata: {},
                    },
                    mockOperation,
                    mockCompensation,
                ),
            ).rejects.toThrow("Operation failed");

            expect(mockCompensation).toHaveBeenCalled();
            // Should still mark as failed even if compensation fails
            assertOperationLogFailed(mockPrisma, "op-123");
        });
    });
});
