// Re-export SortOption interface from DashboardFilters
export type { SortOption } from "@/shared/components/dashboard/DashboardFilters";

/**
 * Helper function to sort an array of objects by a field
 * Supports nested fields using dot notation (e.g., 'user.name')
 */
export function sortData<T>(
    data: T[],
    field: string,
    direction: "asc" | "desc" = "asc",
): T[] {
    return [...data].sort((a, b) => {
        // Get values using dot notation support
        const aValue = getNestedValue(a, field);
        const bValue = getNestedValue(b, field);

        // Handle null/undefined values
        if (aValue === null || aValue === undefined)
            return direction === "asc" ? 1 : -1;
        if (bValue === null || bValue === undefined)
            return direction === "asc" ? -1 : 1;

        // Compare based on type
        let comparison = 0;

        if (typeof aValue === "string" && typeof bValue === "string") {
            // Case-insensitive string comparison
            comparison = aValue
                .toLowerCase()
                .localeCompare(bValue.toLowerCase());
        } else if (aValue instanceof Date && bValue instanceof Date) {
            // Date comparison
            comparison = aValue.getTime() - bValue.getTime();
        } else if (typeof aValue === "number" && typeof bValue === "number") {
            // Number comparison
            comparison = aValue - bValue;
        } else {
            // Fallback to string comparison
            comparison = String(aValue).localeCompare(String(bValue));
        }

        // Apply direction
        return direction === "asc" ? comparison : -comparison;
    });
}

/**
 * Get nested object value using dot notation
 * Example: getNestedValue(obj, 'user.profile.name')
 */
function getNestedValue(obj: any, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj);
}
