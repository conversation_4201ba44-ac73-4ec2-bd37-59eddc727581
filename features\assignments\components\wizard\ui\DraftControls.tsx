"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Spin<PERSON> } from "@heroui/react";
import {
    CloudArrowDownIcon,
    CloudArrowUpIcon,
    TrashIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";

import { useDraftPersistence } from "@/features/assignments/hooks";

interface DraftControlsProps {
    className?: string;
    size?: "sm" | "md" | "lg";
}

export function DraftControls(props: DraftControlsProps) {
    const { className = "", size = "md" } = props;

    const {
        hasDraft,
        draftAge,
        saveDraft,
        loadDraft,
        clearDraft,
        isAutoSaving,
        lastSavedAt,
    } = useDraftPersistence();

    // Mostrar notificación de borrador disponible al inicio
    React.useEffect(() => {
        if (hasDraft) {
            // Pequeño delay para que la UI esté lista
            setTimeout(() => {
                const shouldLoad = window.confirm(
                    `Hay un borrador guardado ${draftAge}. ¿Deseas recuperarlo?`,
                );

                if (shouldLoad) {
                    loadDraft();
                }
            }, 500);
        }
    }, []); // Solo ejecutar al montar

    const containerClass = `flex items-center gap-2 ${className}`;

    return (
        <div className={containerClass}>
            {/* Indicador de auto-guardado */}
            <AnimatePresence>
                {lastSavedAt && (
                    <motion.div
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        initial={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                    >
                        <Chip
                            color="success"
                            size="sm"
                            startContent={
                                <CheckCircleIcon className="w-3 h-3" />
                            }
                            variant="flat"
                        >
                            Guardado {lastSavedAt.toLocaleTimeString()}
                        </Chip>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Botón guardar borrador */}
            <Tooltip content="Guardar progreso actual">
                <Button
                    color="default"
                    isDisabled={isAutoSaving}
                    size={size}
                    startContent={
                        isAutoSaving ? (
                            <Spinner size="sm" />
                        ) : (
                            <CloudArrowUpIcon className="w-4 h-4" />
                        )
                    }
                    variant="flat"
                    onPress={saveDraft}
                >
                    Guardar
                </Button>
            </Tooltip>

            {/* Botón cargar borrador (solo si hay uno) */}
            {hasDraft && (
                <Tooltip content={`Cargar borrador guardado ${draftAge}`}>
                    <Button
                        color="primary"
                        size={size}
                        startContent={
                            <CloudArrowDownIcon className="w-4 h-4" />
                        }
                        variant="flat"
                        onPress={loadDraft}
                    >
                        Cargar
                    </Button>
                </Tooltip>
            )}

            {/* Botón limpiar borrador (solo si hay uno) */}
            {hasDraft && (
                <Tooltip content="Eliminar borrador guardado">
                    <Button
                        isIconOnly
                        color="danger"
                        size={size}
                        variant="flat"
                        onPress={() => {
                            if (
                                window.confirm(
                                    "¿Estás seguro de eliminar el borrador?",
                                )
                            ) {
                                clearDraft();
                            }
                        }}
                    >
                        <motion.div whileHover={{ rotate: 10 }}>
                            <TrashIcon className="w-4 h-4 text-red-600 dark:text-red-400" />
                        </motion.div>
                    </Button>
                </Tooltip>
            )}

            {/* Status indicators */}
            <div className="flex items-center gap-2 ml-2">
                <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    className="flex items-center gap-1"
                    transition={{ duration: 2, repeat: Infinity }}
                >
                    <div className="w-2 h-2 rounded-full bg-green-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                        Auto-guardado activo
                    </span>
                </motion.div>
            </div>
        </div>
    );
}
