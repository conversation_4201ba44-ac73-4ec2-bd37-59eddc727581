"use client";

import { useState, useCallback, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Card, CardBody, Chip } from "@heroui/react";
import {
    ShoppingBagIcon,
    SparklesIcon,
    CubeIcon,
    CalendarIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    DocumentTextIcon,
    ChartBarIcon,
    ArrowTrendingUpIcon,
} from "@heroicons/react/24/outline";
import useSWR from "swr";
import { Order } from "@prisma/client";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";
import { getOrdersForSelector } from "@/features/assignments/actions";

import SplitPanelOrderSelector from "../ui/SplitPanelOrderSelector";
import { ContextualTooltip } from "../ui/ContextualTooltip";

export default function OrdersStep() {
    const { state, dispatch } = useWizard();
    const [searchQuery, setSearchQuery] = useState("");
    const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);

    // Fetch órdenes con SWR
    const { data, isLoading, error } = useSWR<{ orders: Order[] }>(
        "orders-for-selector",
        () => getOrdersForSelector() as any,
    );

    // Gestión de selección
    const toggleOrderSelection = useCallback(
        (orderId: string) => {
            const isSelected = state.selectedOrders.includes(orderId);

            if (isSelected) {
                dispatch({
                    type: "SET_SELECTED_ORDERS",
                    payload: state.selectedOrders.filter(
                        (id) => id !== orderId,
                    ),
                });
            } else {
                dispatch({
                    type: "SET_SELECTED_ORDERS",
                    payload: [...state.selectedOrders, orderId],
                });
            }
        },
        [state.selectedOrders, dispatch],
    );

    // Búsqueda
    const handleSearch = useCallback((query: string) => {
        setSearchQuery(query);
    }, []);

    // Filtrar órdenes
    useEffect(() => {
        if (!data) return;

        const orders = data.orders || [];

        if (searchQuery) {
            const filtered = orders.filter((order) => {
                const cutOrderMatch =
                    order.cutOrder &&
                    order.cutOrder
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase());

                const orderPartMatch =
                    (order as any).orderPart &&
                    (order as any).orderPart
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase());

                const transferMatch =
                    order.transferNumber &&
                    order.transferNumber
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase());
                const batchMatch =
                    order.batch &&
                    order.batch
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase());
                const customerMatch =
                    (order as any).customer?.name
                        ?.toLowerCase()
                        ?.includes(searchQuery.toLowerCase()) || false;

                return (
                    cutOrderMatch ||
                    orderPartMatch ||
                    transferMatch ||
                    batchMatch ||
                    customerMatch
                );
            });

            setFilteredOrders(filtered);
        } else {
            setFilteredOrders(orders);
        }
    }, [data, searchQuery]);

    // Calcular estadísticas
    const stats = {
        totalOrders: data?.orders?.length || 0,
        selectedOrders: state.selectedOrders.length,
        totalItems: filteredOrders.reduce(
            (sum, order) => sum + ((order as any).totalQuantity || 0),
            0,
        ),
        urgentOrders: filteredOrders.filter(
            (order) =>
                (order as any).status === "PENDIENTE" &&
                order.deliveryDate &&
                new Date(order.deliveryDate) <=
                    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        ).length,
    };

    if (error) {
        return (
            <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className="max-w-4xl mx-auto"
                initial={{ opacity: 0, scale: 0.9 }}
            >
                <Card className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 border-0 shadow-xl">
                    <CardBody className="p-12 text-center">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-6">
                            <ExclamationTriangleIcon className="w-10 h-10 text-red-600" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 mb-2">
                            No se pudieron cargar las órdenes
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                            Ha ocurrido un error al cargar las órdenes. Por
                            favor, intenta de nuevo más tarde.
                        </p>
                    </CardBody>
                </Card>
            </motion.div>
        );
    }

    return (
        <AnimatePresence mode="wait">
            <motion.div
                animate={{ opacity: 1 }}
                className="space-y-6 max-w-7xl mx-auto"
                exit={{ opacity: 0 }}
                initial={{ opacity: 0 }}
            >
                {/* Header Card mejorado */}
                <motion.div
                    animate={{ y: 0, opacity: 1 }}
                    className="max-w-4xl mx-auto"
                    initial={{ y: 20, opacity: 0 }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                >
                    <Card className="bg-gradient-to-br from-white via-purple-50/50 to-pink-50/30 dark:from-gray-900 dark:via-purple-950/50 dark:to-pink-950/30 shadow-xl border-0 overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-600/5" />
                        <CardBody className="relative p-8">
                            <div className="flex items-center gap-4 mb-6">
                                <motion.div
                                    className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl shadow-lg"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                    }}
                                    whileHover={{ scale: 1.05, rotate: 5 }}
                                >
                                    <ShoppingBagIcon className="w-7 h-7 text-white" />
                                </motion.div>
                                <div className="flex-1">
                                    <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent">
                                        Seleccionar Órdenes
                                    </h2>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center gap-1.5">
                                        <SparklesIcon className="w-4 h-4 text-purple-500" />
                                        Elige las órdenes que deseas asignar a{" "}
                                        {state.contractorData?.name ||
                                            "el contratista"}
                                    </p>
                                </div>
                                <ContextualTooltip
                                    content="Puedes seleccionar múltiples órdenes. Usa la búsqueda para filtrar por orden de corte, transferencia o cliente."
                                    variant="help"
                                />
                            </div>

                            {/* Statistics Grid */}
                            <div className="grid grid-cols-4 gap-4">
                                <motion.div
                                    className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                    transition={{
                                        type: "spring",
                                        stiffness: 400,
                                    }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <CubeIcon className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {stats.totalOrders}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Órdenes disponibles
                                    </p>
                                </motion.div>
                                <motion.div
                                    className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                    transition={{
                                        type: "spring",
                                        stiffness: 400,
                                    }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <CheckCircleIcon className="w-6 h-6 text-green-600 mx-auto mb-2" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {stats.selectedOrders}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Seleccionadas
                                    </p>
                                </motion.div>
                                <motion.div
                                    className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                    transition={{
                                        type: "spring",
                                        stiffness: 400,
                                    }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <ChartBarIcon className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {stats.totalItems.toLocaleString()}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Total prendas
                                    </p>
                                </motion.div>
                                <motion.div
                                    className="text-center p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl backdrop-blur-sm"
                                    transition={{
                                        type: "spring",
                                        stiffness: 400,
                                    }}
                                    whileHover={{ scale: 1.02 }}
                                >
                                    <CalendarIcon className="w-6 h-6 text-orange-600 mx-auto mb-2" />
                                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                                        {stats.urgentOrders}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Urgentes
                                    </p>
                                </motion.div>
                            </div>

                            {/* Selection Progress */}
                            {state.selectedOrders.length > 0 && (
                                <motion.div
                                    animate={{ opacity: 1, height: "auto" }}
                                    className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"
                                    exit={{ opacity: 0, height: 0 }}
                                    initial={{ opacity: 0, height: 0 }}
                                >
                                    <div className="flex items-center gap-2">
                                        <CheckCircleIcon className="w-5 h-5 text-green-600" />
                                        <span className="text-sm font-medium text-green-800 dark:text-green-200">
                                            {state.selectedOrders.length}{" "}
                                            {state.selectedOrders.length === 1
                                                ? "orden seleccionada"
                                                : "órdenes seleccionadas"}
                                        </span>
                                    </div>
                                </motion.div>
                            )}
                        </CardBody>
                    </Card>
                </motion.div>

                {/* Main Order Selector con diseño mejorado */}
                <motion.div
                    animate={{ y: 0, opacity: 1 }}
                    initial={{ y: 20, opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.1, ease: "easeOut" }}
                >
                    <div className="bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
                        <SplitPanelOrderSelector
                            isLoading={isLoading}
                            orders={filteredOrders as any}
                            searchQuery={searchQuery}
                            selectedOrders={state.selectedOrders}
                            onSearchChange={handleSearch}
                            onToggleOrder={toggleOrderSelection}
                        />
                    </div>
                </motion.div>

                {/* Help Section mejorada */}
                <motion.div
                    animate={{ opacity: 1 }}
                    className="max-w-4xl mx-auto"
                    initial={{ opacity: 0 }}
                    transition={{ delay: 0.3 }}
                >
                    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-0">
                        <CardBody className="p-6">
                            <div className="flex items-start gap-3">
                                <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                                    <DocumentTextIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div className="flex-1">
                                    <h4 className="font-semibold text-gray-800 dark:text-gray-100 mb-1">
                                        Instrucciones
                                    </h4>
                                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                        <li className="flex items-start gap-2">
                                            <span className="text-blue-500 mt-1">
                                                •
                                            </span>
                                            <span>
                                                Selecciona las órdenes que
                                                deseas asignar al contratista
                                            </span>
                                        </li>
                                        <li className="flex items-start gap-2">
                                            <span className="text-blue-500 mt-1">
                                                •
                                            </span>
                                            <span>
                                                Usa la búsqueda para filtrar por
                                                orden de corte, transferencia o
                                                cliente
                                            </span>
                                        </li>
                                        <li className="flex items-start gap-2">
                                            <span className="text-blue-500 mt-1">
                                                •
                                            </span>
                                            <span>
                                                En el siguiente paso podrás
                                                indicar las cantidades
                                                específicas por talla
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            {/* Quick Tips */}
                            <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-800">
                                <div className="flex items-center gap-2 mb-2">
                                    <ArrowTrendingUpIcon className="w-4 h-4 text-indigo-600" />
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Consejos rápidos
                                    </span>
                                </div>
                                <div className="grid grid-cols-2 gap-3">
                                    <Chip
                                        color="warning"
                                        size="sm"
                                        startContent={
                                            <CalendarIcon className="w-3 h-3" />
                                        }
                                        variant="flat"
                                    >
                                        Prioriza órdenes urgentes
                                    </Chip>
                                    <Chip
                                        color="success"
                                        size="sm"
                                        startContent={
                                            <CheckCircleIcon className="w-3 h-3" />
                                        }
                                        variant="flat"
                                    >
                                        Agrupa órdenes similares
                                    </Chip>
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
}
