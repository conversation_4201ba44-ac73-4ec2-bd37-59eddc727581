"use client";

import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Chip,
    Card,
    CardBody,
    Divider,
} from "@heroui/react";
import {
    ClockIcon,
    UserGroupIcon,
    CubeIcon,
    DocumentDuplicateIcon,
    TrashIcon,
    ArrowPathIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

interface DraftData {
    contractorId: string | null;
    selectedOrders: string[];
    quantities: Record<string, Record<string, number>>;
    contractorName?: string;
    ordersInfo?: Array<{
        id: string;
        cutOrder?: string;
        code?: string;
        customerName?: string;
    }>;
}

interface DraftRestoreModalProps {
    isOpen: boolean;
    onClose: () => void;
    onRestore: () => void;
    onDiscard: () => void;
    draftData: DraftData | null;
    draftTimestamp: number;
}

export function DraftRestoreModal({
    isOpen,
    onClose,
    onRestore,
    onDiscard,
    draftData,
    draftTimestamp,
}: DraftRestoreModalProps) {
    if (!draftData) return null;

    const draftAge = formatDistanceToNow(new Date(draftTimestamp), {
        addSuffix: true,
        locale: es,
    });

    const formattedDate = format(
        new Date(draftTimestamp),
        "d 'de' MMMM 'a las' HH:mm",
        {
            locale: es,
        },
    );

    const handleRestore = () => {
        onRestore();
        onClose();
    };

    const handleDiscard = () => {
        onDiscard();
        onClose();
    };

    return (
        <Modal
            backdrop="blur"
            isOpen={isOpen}
            motionProps={{
                variants: {
                    enter: {
                        y: 0,
                        opacity: 1,
                        transition: {
                            duration: 0.3,
                            ease: "easeOut",
                        },
                    },
                    exit: {
                        y: -20,
                        opacity: 0,
                        transition: {
                            duration: 0.2,
                            ease: "easeIn",
                        },
                    },
                },
            }}
            placement="center"
            size="lg"
            onClose={onClose}
        >
            <ModalContent>
                {() => (
                    <>
                        <ModalHeader className="flex flex-col gap-1">
                            <div className="flex items-center gap-2">
                                <DocumentDuplicateIcon className="w-5 h-5 text-primary" />
                                <span>Borrador encontrado</span>
                            </div>
                            <p className="text-sm font-normal text-default-500">
                                Se encontró un borrador guardado {draftAge}
                            </p>
                        </ModalHeader>

                        <ModalBody>
                            <Card className="bg-default-50 dark:bg-default-100">
                                <CardBody className="gap-3">
                                    {/* Fecha de guardado */}
                                    <div className="flex items-center gap-2 text-sm">
                                        <ClockIcon className="w-4 h-4 text-default-400" />
                                        <span className="text-default-600">
                                            Guardado el {formattedDate}
                                        </span>
                                    </div>

                                    <Divider className="my-2" />

                                    {/* Contratista */}
                                    {draftData.contractorName && (
                                        <div className="flex items-center gap-2">
                                            <UserGroupIcon className="w-4 h-4 text-primary" />
                                            <span className="text-sm font-medium">
                                                Contratista:
                                            </span>
                                            <Chip
                                                color="primary"
                                                size="sm"
                                                variant="flat"
                                            >
                                                {draftData.contractorName}
                                            </Chip>
                                        </div>
                                    )}

                                    {/* Órdenes seleccionadas */}
                                    {draftData.selectedOrders.length > 0 && (
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <CubeIcon className="w-4 h-4 text-secondary" />
                                                <span className="text-sm font-medium">
                                                    Órdenes seleccionadas:{" "}
                                                    {
                                                        draftData.selectedOrders
                                                            .length
                                                    }
                                                </span>
                                            </div>

                                            {draftData.ordersInfo &&
                                                draftData.ordersInfo.length >
                                                    0 && (
                                                    <div className="ml-6 space-y-1">
                                                        {draftData.ordersInfo
                                                            .slice(0, 3)
                                                            .map((order) => (
                                                                <motion.div
                                                                    key={
                                                                        order.id
                                                                    }
                                                                    animate={{
                                                                        opacity: 1,
                                                                        x: 0,
                                                                    }}
                                                                    className="text-xs text-default-600"
                                                                    initial={{
                                                                        opacity: 0,
                                                                        x: -10,
                                                                    }}
                                                                >
                                                                    •{" "}
                                                                    {order.cutOrder
                                                                        ? `OT: ${order.cutOrder}`
                                                                        : `Orden #${order.code}`}
                                                                    {order.customerName &&
                                                                        ` - ${order.customerName}`}
                                                                </motion.div>
                                                            ))}
                                                        {draftData.ordersInfo
                                                            .length > 3 && (
                                                            <div className="text-xs text-default-400 ml-2">
                                                                ... y{" "}
                                                                {draftData
                                                                    .ordersInfo
                                                                    .length -
                                                                    3}{" "}
                                                                más
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                        </div>
                                    )}

                                    {/* Cantidades asignadas */}
                                    {Object.keys(draftData.quantities).length >
                                        0 && (
                                        <div className="flex items-center gap-2 text-sm text-default-600">
                                            <span>✓</span>
                                            <span>
                                                Cantidades asignadas en{" "}
                                                {
                                                    Object.keys(
                                                        draftData.quantities,
                                                    ).length
                                                }{" "}
                                                órdenes
                                            </span>
                                        </div>
                                    )}
                                </CardBody>
                            </Card>

                            <p className="text-sm text-default-500 mt-3">
                                ¿Deseas recuperar este trabajo o empezar desde
                                cero?
                            </p>
                        </ModalBody>

                        <ModalFooter>
                            <Button
                                color="danger"
                                startContent={<TrashIcon className="w-4 h-4" />}
                                variant="light"
                                onPress={handleDiscard}
                            >
                                Descartar
                            </Button>
                            <Button
                                color="primary"
                                startContent={
                                    <ArrowPathIcon className="w-4 h-4" />
                                }
                                onPress={handleRestore}
                            >
                                Recuperar borrador
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
