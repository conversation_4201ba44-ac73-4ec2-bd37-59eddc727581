/**
 * Base Repository Abstract Class
 *
 * Provides common functionality for all repositories including
 * caching, transaction management, and error handling.
 */

import { PrismaClient, Prisma } from "@prisma/client";
import { Redis } from "ioredis";

export interface BaseEntity {
    id?: string;
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date | null;
}

export interface QueryOptions {
    includeDeleted?: boolean;
    orderBy?: string;
    orderDirection?: "asc" | "desc";
    limit?: number;
    offset?: number;
}

export abstract class BaseRepository<T extends BaseEntity> {
    protected readonly CACHE_PREFIX: string;
    protected readonly CACHE_TTL: number = 3600; // 1 hour default
    protected transaction: Prisma.TransactionClient | null = null;

    constructor(
        protected readonly prisma: PrismaClient,
        protected readonly redis: Redis,
        cachePrefix: string,
    ) {
        this.CACHE_PREFIX = cachePrefix;
    }

    /**
     * Get the active Prisma client (transaction or regular)
     */
    protected get db() {
        return this.transaction || this.prisma;
    }

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract getModel(): any;
    protected abstract toDomain(model: any): T;
    protected abstract toPersistence(entity: T): any;

    /**
     * Cache operations
     */
    protected async getFromCache<R>(key: string): Promise<R | null> {
        try {
            const cached = await this.redis.get(key);

            if (cached) {
                return JSON.parse(cached);
            }

            return null;
        } catch (error) {
            console.error("Cache get error:", error);

            return null;
        }
    }

    protected async setCache(
        key: string,
        value: any,
        ttl?: number,
    ): Promise<void> {
        try {
            const serialized = JSON.stringify(value);

            if (ttl || this.CACHE_TTL) {
                await this.redis.setex(key, ttl || this.CACHE_TTL, serialized);
            } else {
                await this.redis.set(key, serialized);
            }
        } catch (error) {
            console.error("Cache set error:", error);
        }
    }

    protected async deleteFromCache(key: string): Promise<void> {
        try {
            await this.redis.del(key);
        } catch (error) {
            console.error("Cache delete error:", error);
        }
    }

    protected async deleteCachePattern(pattern: string): Promise<void> {
        try {
            const keys = await this.redis.keys(pattern);

            if (keys.length > 0) {
                await this.redis.del(...keys);
            }
        } catch (error) {
            console.error("Cache pattern delete error:", error);
        }
    }

    /**
     * Transaction management
     */
    async beginTransaction(): Promise<void> {
        if (!this.transaction) {
            // Note: This is a simplified version. In a real implementation,
            // you would need to properly handle Prisma's transaction API
            this.transaction = (await this.prisma.$transaction) as any;
        }
    }

    async commit(): Promise<void> {
        if (this.transaction) {
            // In a real implementation, this would commit the transaction
            this.transaction = null;
        }
    }

    async rollback(): Promise<void> {
        if (this.transaction) {
            // In a real implementation, this would rollback the transaction
            this.transaction = null;
        }
    }

    /**
     * Common query builder
     */
    protected buildWhereClause(options: QueryOptions): any {
        const where: any = {};

        if (!options.includeDeleted) {
            where.deletedAt = null;
        }

        return where;
    }

    protected buildOrderByClause(options: QueryOptions): any {
        if (!options.orderBy) {
            return undefined;
        }

        return {
            [options.orderBy]: options.orderDirection || "asc",
        };
    }

    /**
     * Error handling
     */
    protected handlePrismaError(error: any): Error {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            switch (error.code) {
                case "P2002":
                    return new Error("Unique constraint violation");
                case "P2025":
                    return new Error("Record not found");
                case "P2003":
                    return new Error("Foreign key constraint violation");
                default:
                    return new Error(`Database error: ${error.message}`);
            }
        }

        if (error instanceof Prisma.PrismaClientValidationError) {
            return new Error("Invalid data provided");
        }

        return error;
    }

    /**
     * Utility methods
     */
    protected generateCacheKey(...parts: (string | number)[]): string {
        return parts.join(":");
    }

    protected async invalidateEntityCache(id: string): Promise<void> {
        await this.deleteFromCache(`${this.CACHE_PREFIX}:${id}`);
    }

    protected async invalidateListCache(): Promise<void> {
        await this.deleteCachePattern(`${this.CACHE_PREFIX}:list:*`);
    }

    protected async invalidateAllCache(): Promise<void> {
        await this.deleteCachePattern(`${this.CACHE_PREFIX}:*`);
    }

    /**
     * Batch operations with chunking
     */
    protected async batchOperation<R>(
        items: any[],
        operation: (batch: any[]) => Promise<R[]>,
        batchSize: number = 100,
    ): Promise<R[]> {
        const results: R[] = [];

        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchResults = await operation(batch);

            results.push(...batchResults);
        }

        return results;
    }

    /**
     * Performance monitoring
     */
    protected async measurePerformance<R>(
        operation: string,
        fn: () => Promise<R>,
    ): Promise<R> {
        const startTime = Date.now();

        try {
            const result = await fn();
            const duration = Date.now() - startTime;

            // Log slow operations
            if (duration > 1000) {
                console.warn(`Slow operation: ${operation} took ${duration}ms`);
            }

            return result;
        } catch (error) {
            const duration = Date.now() - startTime;

            console.error(
                `Operation failed: ${operation} after ${duration}ms`,
                error,
            );
            throw error;
        }
    }

    /**
     * Pagination helpers
     */
    protected async paginate<R>(
        query: () => any,
        options: QueryOptions,
        transform: (items: any[]) => R[],
    ): Promise<{ data: R[]; total: number; hasMore: boolean }> {
        const limit = options.limit || 50;
        const offset = options.offset || 0;

        // Fetch one extra to determine if there are more results
        const items = await query()
            .take(limit + 1)
            .skip(offset);
        const hasMore = items.length > limit;
        const data = transform(hasMore ? items.slice(0, limit) : items);

        // Get total count (this could be optimized with caching)
        const total = await query().count();

        return { data, total, hasMore };
    }
}
