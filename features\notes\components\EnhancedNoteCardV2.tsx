"use client";

import React, { useState } from "react";
import {
    <PERSON>,
    Card<PERSON><PERSON>,
    Card<PERSON>eader,
    Card<PERSON>ooter,
    <PERSON>,
    Avatar,
    <PERSON><PERSON>,
    <PERSON>lt<PERSON>,
    <PERSON><PERSON>,
    Di<PERSON>r,
} from "@heroui/react";
import {
    HeartIcon,
    ChatBubbleLeftRightIcon,
    EllipsisVerticalIcon,
    CalendarIcon,
    ClipboardDocumentListIcon,
    UserIcon,
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

interface NoteCardProps {
    note: any;
    currentUserId?: string;
    onLike: (noteId: string) => void;
    onEdit: (note: any) => void;
    onDelete: (note: any) => void;
    onView: (note: any) => void;
}

export function EnhancedNoteCardV2({
    note,
    currentUserId,
    onLike,
    onEdit,
    onDelete,
    onView,
}: NoteCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const isLiked = note.likes?.some(
        (like: any) => like.userId === currentUserId,
    );
    const isAuthor = note.authorId === currentUserId;

    // Colores según importancia
    const importanceColors: Record<string, any> = {
        Crítica: "danger",
        Alta: "warning",
        Media: "primary",
        Baja: "default",
    };

    // Colores según estado
    const statusColors: Record<string, any> = {
        Pendiente: "warning",
        "En Proceso": "primary",
        Resuelta: "success",
        Archivada: "default",
    };

    return (
        <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
        >
            <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="pb-3">
                    <div className="flex justify-between items-start w-full">
                        <div className="flex-1">
                            {/* Información de la orden */}
                            {note.order && (
                                <div className="mb-2">
                                    <div className="flex items-center gap-2 mb-1">
                                        <ClipboardDocumentListIcon className="w-4 h-4 text-gray-500" />
                                        <span className="font-semibold text-sm">
                                            Orden:{" "}
                                            {note.order.cutOrder ||
                                                note.order.code ||
                                                "Sin código"}
                                        </span>
                                        {note.order.customer && (
                                            <Chip size="sm" variant="flat">
                                                {note.order.customer.name}
                                            </Chip>
                                        )}
                                    </div>
                                    {/* Mostrar partidas */}
                                    {note.order.parts &&
                                        note.order.parts.length > 0 && (
                                            <div className="flex flex-wrap gap-1 ml-6">
                                                {note.order.parts.map(
                                                    (part: any) => (
                                                        <Badge
                                                            key={part.id}
                                                            color="primary"
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            {part.code}
                                                        </Badge>
                                                    ),
                                                )}
                                            </div>
                                        )}
                                </div>
                            )}

                            {/* Chips de importancia y estado */}
                            <div className="flex gap-2">
                                {note.importance && (
                                    <Chip
                                        color={
                                            importanceColors[
                                                note.importance.name
                                            ] || "default"
                                        }
                                        size="sm"
                                        variant="flat"
                                    >
                                        {note.importance.name}
                                    </Chip>
                                )}
                                {note.status && (
                                    <Chip
                                        color={
                                            statusColors[note.status.name] ||
                                            "default"
                                        }
                                        size="sm"
                                        variant="flat"
                                    >
                                        {note.status.name}
                                    </Chip>
                                )}
                            </div>
                        </div>

                        {/* Menú de acciones */}
                        <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            onPress={onView}
                        >
                            <EllipsisVerticalIcon className="w-5 h-5" />
                        </Button>
                    </div>
                </CardHeader>

                <Divider />

                <CardBody className="py-4">
                    {/* Contenido de la nota */}
                    <div className="prose prose-sm max-w-none">
                        <p
                            className={`text-gray-700 dark:text-gray-300 ${!isExpanded ? "line-clamp-3" : ""}`}
                        >
                            {note.content}
                        </p>
                        {note.content.length > 150 && (
                            <Button
                                className="mt-2"
                                size="sm"
                                variant="light"
                                onPress={() => setIsExpanded(!isExpanded)}
                            >
                                {isExpanded ? "Ver menos" : "Ver más"}
                            </Button>
                        )}
                    </div>

                    {/* Información del autor y fecha */}
                    <div className="flex items-center gap-3 mt-4">
                        <Avatar
                            color="secondary"
                            icon={<UserIcon className="w-4 h-4" />}
                            name={note.author?.name || "Unknown"}
                            size="sm"
                        />
                        <div className="flex-1">
                            <p className="text-sm font-medium">
                                {note.author?.name || "Usuario desconocido"}
                            </p>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                                <CalendarIcon className="w-3 h-3" />
                                <span>
                                    {formatDistanceToNow(
                                        new Date(note.createdAt),
                                        {
                                            locale: es,
                                            addSuffix: true,
                                        },
                                    )}
                                </span>
                            </div>
                        </div>
                    </div>
                </CardBody>

                <Divider />

                <CardFooter className="justify-between">
                    {/* Interacciones */}
                    <div className="flex items-center gap-4">
                        <Tooltip
                            content={`${note._count?.likes || 0} me gusta`}
                        >
                            <Button
                                className="min-w-unit-16"
                                size="sm"
                                startContent={
                                    isLiked ? (
                                        <HeartIconSolid className="w-4 h-4 text-red-500" />
                                    ) : (
                                        <HeartIcon className="w-4 h-4" />
                                    )
                                }
                                variant="light"
                                onPress={() => onLike(note.id)}
                            >
                                {note._count?.likes || 0}
                            </Button>
                        </Tooltip>

                        <Tooltip
                            content={`${note._count?.comments || 0} comentarios`}
                        >
                            <Button
                                className="min-w-unit-16"
                                size="sm"
                                startContent={
                                    <ChatBubbleLeftRightIcon className="w-4 h-4" />
                                }
                                variant="light"
                                onPress={onView}
                            >
                                {note._count?.comments || 0}
                            </Button>
                        </Tooltip>
                    </div>

                    {/* Acciones */}
                    {isAuthor && (
                        <div className="flex gap-2">
                            <Button
                                color="primary"
                                size="sm"
                                variant="flat"
                                onPress={onEdit}
                            >
                                Editar
                            </Button>
                            <Button
                                color="danger"
                                size="sm"
                                variant="flat"
                                onPress={onDelete}
                            >
                                Eliminar
                            </Button>
                        </div>
                    )}
                </CardFooter>
            </Card>
        </motion.div>
    );
}
