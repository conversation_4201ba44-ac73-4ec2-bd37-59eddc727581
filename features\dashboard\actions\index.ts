"use server";

import { revalidatePath } from "next/cache";

import { db, handleDbError } from "@/shared/lib/db";

/**
 * Obtiene estadísticas generales del sistema para el dashboard
 */
export async function getDashboardStats() {
    return handleDbError(
        async () => {
            // Realizar consultas en paralelo para obtener estadísticas
            const [
                usersCount,
                customersCount,
                modelsCount,
                colorsCount,
                contractorsCount,
                sizesCount,
                ordersCount,
            ] = await Promise.all([
                db.user.count(),
                db.customer.count(),
                db.garmentModel.count(),
                db.color.count(),
                db.contractor.count(),
                db.size.count(),
                db.order.count(),
            ]);

            // Obtener estadísticas adicionales para modelos y colores utilizados
            const modelsWithUsage = await db.garmentModel.findMany({
                select: {
                    _count: {
                        select: {
                            garments: true,
                        },
                    },
                },
            });

            const colorsWithUsage = await db.color.findMany({
                select: {
                    _count: {
                        select: {
                            garments: true,
                        },
                    },
                },
            });

            // Calcular modelos y colores en uso
            const modelsInUse = modelsWithUsage.filter(
                (model) => model._count.garments > 0,
            ).length;
            const colorsInUse = colorsWithUsage.filter(
                (color) => color._count.garments > 0,
            ).length;

            // Retornar estadísticas estructuradas
            return {
                counts: {
                    users: usersCount,
                    customers: customersCount,
                    models: modelsCount,
                    colors: colorsCount,
                    contractors: contractorsCount,
                    sizes: sizesCount,
                    orders: ordersCount,
                },
                usage: {
                    modelsInUse,
                    modelsUnused: modelsCount - modelsInUse,
                    colorsInUse,
                    colorsUnused: colorsCount - colorsInUse,
                },
                updatedAt: new Date().toISOString(),
            };
        },
        "Error al obtener estadísticas del sistema",
        3, // Aumentar los reintentos para mejor manejo con Turbopack
    );
}

/**
 * Revalida la ruta del dashboard
 * @returns Resultado de la operación de revalidación
 */
export async function revalidateDashboard() {
    try {
        // Revalidar diferentes rutas relacionadas con el dashboard
        revalidatePath("/");
        revalidatePath("/dashboard");

        if (process.env.NODE_ENV !== "production") {
            // REMOVED: console.log("🔄 Rutas del dashboard revalidadas con éxito");
        }

        // Retornar con tag para que el cliente pueda emitir evento de revalidación apropiado
        return {
            success: true,
            tag: "dashboard",
        };
    } catch (error) {
        // REMOVED: console.error("Error revalidando dashboard:", error);

        return {
            success: false,
            error: "Error al revalidar el dashboard",
        };
    }
}
