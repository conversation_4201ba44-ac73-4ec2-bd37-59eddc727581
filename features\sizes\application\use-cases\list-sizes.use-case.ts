/**
 * List Sizes Use Case
 *
 * Business logic for retrieving and filtering sizes.
 * Handles queries, pagination, and data transformation.
 */

// import { injectable, inject } from "tsyringe"; // Commented out - tsyringe not installed

import { SizeRepositoryInterface } from "../../domain/repositories/size.repository.interface";
import {
    ListSizesQueryDTO,
    SizeResponseDTO,
    PaginatedSizesResponseDTO,
    toSizeResponse,
} from "../dto/size.dto";

export interface ListSizesQuery {
    filters: ListSizesQueryDTO;
    userId?: string;
}

export interface ListSizesResult {
    success: boolean;
    data?: PaginatedSizesResponseDTO;
    error?: string;
}

// @injectable() // Commented out - tsyringe not installed
export class ListSizesUseCase {
    constructor(
        // @inject("SizeRepository") // Commented out - tsyringe not installed
        private readonly sizeRepository: SizeRepositoryInterface,
    ) {}

    async execute(query: ListSizesQuery): Promise<ListSizesResult> {
        try {
            // 1. Extract filters with defaults
            const {
                isActive,
                category,
                includeDeleted = false,
                orderBy = "order",
                orderDirection = "asc",
                limit = 50,
                offset = 0,
            } = query.filters;

            // 2. Build repository options
            const options = {
                isActive,
                category,
                includeDeleted,
                orderBy,
                orderDirection,
                limit: limit + 1, // Fetch one extra to determine hasMore
                offset,
            };

            // 3. Fetch sizes from repository
            const sizes = await this.sizeRepository.findAll(options);

            // 4. Determine if there are more results
            const hasMore = sizes.length > limit;
            const resultsToReturn = hasMore ? sizes.slice(0, limit) : sizes;

            // 5. Get total count for pagination
            const total = await this.sizeRepository.count({
                isActive,
                category,
                includeDeleted,
            });

            // 6. Transform to DTOs
            const sizeDTOs: SizeResponseDTO[] = resultsToReturn.map((size) =>
                toSizeResponse(size.toObject() as any),
            );

            // 7. Build paginated response
            const response: PaginatedSizesResponseDTO = {
                data: sizeDTOs,
                total,
                limit,
                offset,
                hasMore,
            };

            return {
                success: true,
                data: response,
            };
        } catch (error) {
            // Log error for monitoring
            console.error("ListSizesUseCase error:", error);

            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }

    /**
     * Get all active sizes ordered by their order value
     */
    async getActiveOrdered(): Promise<ListSizesResult> {
        return this.execute({
            filters: {
                isActive: true,
                orderBy: "order",
                orderDirection: "asc",
                limit: 1000, // Get all active sizes
            },
        });
    }

    /**
     * Get sizes by category
     */
    async getByCategory(
        category: "numeric" | "alpha" | "custom",
    ): Promise<ListSizesResult> {
        return this.execute({
            filters: {
                category,
                isActive: true,
                orderBy: "order",
                orderDirection: "asc",
            },
        });
    }

    /**
     * Search sizes by name
     */
    async searchByName(searchTerm: string): Promise<ListSizesResult> {
        try {
            // For now, fetch all and filter in memory
            // In a real implementation, this would be a database query
            const allSizesResult = await this.execute({
                filters: {
                    orderBy: "order",
                    orderDirection: "asc",
                    isActive: true,
                    limit: 1000,
                } as any,
            });

            if (!allSizesResult.success || !allSizesResult.data) {
                return allSizesResult;
            }

            // Filter by search term
            const filteredSizes = allSizesResult.data.data.filter(
                (size) =>
                    size.name
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase()) ||
                    size.displayName
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase()),
            );

            return {
                success: true,
                data: {
                    ...allSizesResult.data,
                    data: filteredSizes,
                    total: filteredSizes.length,
                },
            };
        } catch (error) {
            return {
                success: false,
                error:
                    error instanceof Error
                        ? error.message
                        : "An unexpected error occurred",
            };
        }
    }
}
