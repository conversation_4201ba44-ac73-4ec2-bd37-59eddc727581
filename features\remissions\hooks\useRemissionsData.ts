"use client";

import useS<PERSON> from "swr";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";

import { getRemissions } from "@/features/remissions/actions";
import { createClientRevalidation } from "@/shared/lib/client-revalidation";

// Types
interface Remission {
    id: string;
    folio: string;
    contractorId: string;
    contractor?: {
        id: string;
        name: string;
    };
    status: string;
    notes?: string | null;
    orderDetails?: any;
    printedAt?: string | null;
    createdAt: string;
    updatedAt: string;
    assignments?: {
        assignment: {
            id: string;
            order: {
                code: string;
                customer: {
                    name: string;
                };
            };
        };
    }[];
    remissionItems?: {
        id: string;
        modelId: string;
        colorId: string;
        sizeId: string;
        quantity: number;
        model?: { code: string };
        color?: { name: string };
        size?: { code: string };
    }[];
    _count?: {
        remissionItems: number;
    };
}

interface UseRemissionsDataReturn {
    remissions: Remission[];
    isLoading: boolean;
    isError: boolean;
    error: any;
    mutate: any;
    stats: {
        total: number;
        pendingPrint: number;
        deliveredThisMonth: number;
        totalItems: number;
        todayRemissions: number;
        dailyAverage: number;
        urgentAlerts: number;
        upcomingDeliveries: number;
        topContractor: { name: string; count: number };
        onTimeDeliveryRate: number;
        avgProcessingTime: number;
    };
}

const { revalidateData, useRevalidationListener } =
    createClientRevalidation("remission");

export function useRemissionsData(): UseRemissionsDataReturn {
    const searchParams = useSearchParams();

    // Parse search params
    const params = {
        search: searchParams.get("search") || "",
        status: searchParams.get("status") || undefined,
        contractorId: searchParams.get("contractorId") || undefined,
        dateFrom: searchParams.get("dateFrom") || undefined,
        dateTo: searchParams.get("dateTo") || undefined,
        orderBy: searchParams.get("orderBy") || "createdAt",
        order: (searchParams.get("order") || "desc") as "asc" | "desc",
        page: parseInt(searchParams.get("page") || "1"),
        perPage: parseInt(searchParams.get("perPage") || "12"),
    };

    // Listen for revalidation events
    const isRevalidating = useRevalidationListener();

    // Fetch remissions
    const { data, error, mutate } = useSWR(
        ["remissions", params],
        () => getRemissions(params),
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
        },
    );

    // Process data
    const remissions = data?.success && data.data ? data.data : [];

    // Calculate stats
    const stats = useMemo(() => {
        const now = new Date();
        const today = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate(),
        );
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        // Estadísticas básicas
        const total = remissions.length;
        const pendingPrint = remissions.filter((r) => !r.printedAt).length;

        // Remisiones de hoy
        const todayRemissions = remissions.filter((r) => {
            const createdDate = new Date(r.createdAt);

            return createdDate >= today;
        }).length;

        // Promedio diario (últimos 7 días)
        const lastWeekRemissions = remissions.filter((r) => {
            const createdDate = new Date(r.createdAt);

            return createdDate >= lastWeek;
        }).length;
        const dailyAverage = Math.round(lastWeekRemissions / 7);

        // Remisiones entregadas este mes
        const deliveredThisMonth = remissions.filter((r) => {
            const createdDate = new Date(r.createdAt);

            return (
                createdDate >= firstDayOfMonth &&
                (r.status === "DELIVERED" || r.status === "ENTREGADA")
            );
        }).length;

        // Total de prendas
        const totalItems = remissions.reduce((sum, r) => {
            const items =
                r.remissionItems?.reduce(
                    (itemSum, item) => itemSum + item.quantity,
                    0,
                ) || 0;

            return sum + items;
        }, 0);

        // Alertas (sin imprimir > 48h)
        const urgentAlerts = remissions.filter((r) => {
            if (r.printedAt) return false;
            const hoursOld =
                (now.getTime() - new Date(r.createdAt).getTime()) /
                (1000 * 60 * 60);

            return hoursOld > 48;
        }).length;

        // Próximas entregas (próximos 3 días)
        const threeDaysFromNow = new Date(
            now.getTime() + 3 * 24 * 60 * 60 * 1000,
        );
        const upcomingDeliveries = remissions.filter((r) => {
            if (r.status === "DELIVERED" || r.status === "ENTREGADA")
                return false;
            // Aquí asumo que hay una fecha estimada, si no, uso la fecha de creación + 5 días
            const estimatedDate = new Date(r.createdAt);

            estimatedDate.setDate(estimatedDate.getDate() + 5);

            return estimatedDate <= threeDaysFromNow && estimatedDate >= now;
        }).length;

        // Top contratista del mes
        const contractorStats = new Map<string, number>();

        remissions
            .filter((r) => new Date(r.createdAt) >= firstDayOfMonth)
            .forEach((r) => {
                if (r.contractor?.name) {
                    const current = contractorStats.get(r.contractor.name) || 0;

                    contractorStats.set(r.contractor.name, current + 1);
                }
            });

        let topContractor = { name: "Sin datos", count: 0 };

        contractorStats.forEach((count, name) => {
            if (count > topContractor.count) {
                topContractor = { name, count };
            }
        });

        // Tasa de cumplimiento (entregadas a tiempo)
        const completedRemissions = remissions.filter(
            (r) => r.status === "DELIVERED" || r.status === "ENTREGADA",
        );
        const onTimeDeliveryRate =
            completedRemissions.length > 0
                ? Math.round((completedRemissions.length / total) * 100)
                : 0;

        // Tiempo promedio de procesamiento (en días)
        const processedRemissions = remissions.filter((r) => r.printedAt);
        const avgProcessingTime =
            processedRemissions.length > 0
                ? Math.round(
                      processedRemissions.reduce((sum, r) => {
                          const created = new Date(r.createdAt).getTime();
                          const printed = new Date(r.printedAt!).getTime();

                          return (
                              sum + (printed - created) / (1000 * 60 * 60 * 24)
                          );
                      }, 0) / processedRemissions.length,
                  )
                : 0;

        return {
            total,
            pendingPrint,
            deliveredThisMonth,
            totalItems,
            todayRemissions,
            dailyAverage,
            urgentAlerts,
            upcomingDeliveries,
            topContractor,
            onTimeDeliveryRate,
            avgProcessingTime,
        };
    }, [remissions]);

    return {
        remissions: remissions as any,
        isLoading: !data && !error,
        isError: !!error || !data?.success,
        error: error || data?.error,
        mutate,
        stats,
    };
}
