"use client";

import type { PackingWithRelations } from "@/lib/types/packing";

import React from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import Image from "next/image";
import { QRCodeSVG } from "qrcode.react";

interface EnhancedPackingDocumentProps {
    packing: PackingWithRelations;
    showSignatures?: boolean;
}

export function EnhancedPackingDocument({
    packing,
    showSignatures = true,
}: EnhancedPackingDocumentProps) {
    // Agrupar detalles por orden y modelo
    const groupedByOrder = packing.details.reduce(
        (acc, detail) => {
            const orderId = packing.orderId || "sin-orden";
            const orderKey =
                packing.order?.cutOrder ||
                packing.order?.transferNumber ||
                orderId;

            if (!acc[orderKey]) {
                acc[orderKey] = {
                    orderId,
                    orderNumber: orderKey,
                    cutOrder: packing.order?.cutOrder,
                    items: [],
                };
            }

            acc[orderKey].items.push(detail);

            return acc;
        },
        {} as Record<string, any>,
    );

    // Agrupar por modelo dentro de cada orden
    const processOrderItems = (items: any[]) => {
        return items.reduce(
            (acc, detail) => {
                const modelKey = `${detail.modelCode || detail.garmentSize.garment.model.code}-${detail.colorName || detail.garmentSize.garment.color.name}-${detail.partNumber || ""}`;

                if (!acc[modelKey]) {
                    acc[modelKey] = {
                        model:
                            detail.modelCode ||
                            detail.garmentSize.garment.model.code,
                        modelDesc: detail.garmentSize.garment.model.description,
                        color:
                            detail.colorName ||
                            detail.garmentSize.garment.color.name,
                        partNumber: detail.partNumber,
                        qualityGroups: {
                            primera: { sizes: {}, total: 0 },
                            segunda: { sizes: {}, total: 0 },
                            manchada: { sizes: {}, total: 0 },
                            incompleta: { sizes: {}, total: 0 },
                        },
                        packageSummary: {
                            boxes: 0,
                            bags: 0,
                            boxesBySize: {},
                            bagsBySize: {},
                            piecesBySize: {},
                        },
                    };
                }

                const sizeCode = detail.garmentSize.size.code;
                const quality = detail.qualityType || "primera";

                // Sumar cantidades por calidad
                if (!acc[modelKey].qualityGroups[quality].sizes[sizeCode]) {
                    acc[modelKey].qualityGroups[quality].sizes[sizeCode] = 0;
                }
                acc[modelKey].qualityGroups[quality].sizes[sizeCode] +=
                    detail.quantity;
                acc[modelKey].qualityGroups[quality].total += detail.quantity;

                // Sumar empaque
                if (detail.packagingType === "caja") {
                    acc[modelKey].packageSummary.boxes +=
                        detail.packagingUnits || 0;
                    acc[modelKey].packageSummary.boxesBySize[sizeCode] =
                        (acc[modelKey].packageSummary.boxesBySize[sizeCode] ||
                            0) + (detail.packagingUnits || 0);
                } else if (detail.packagingType === "bolsa") {
                    acc[modelKey].packageSummary.bags +=
                        detail.packagingUnits || 0;
                    acc[modelKey].packageSummary.bagsBySize[sizeCode] =
                        (acc[modelKey].packageSummary.bagsBySize[sizeCode] ||
                            0) + (detail.packagingUnits || 0);
                }

                if (detail.loosePieces > 0) {
                    acc[modelKey].packageSummary.piecesBySize[sizeCode] =
                        (acc[modelKey].packageSummary.piecesBySize[sizeCode] ||
                            0) + detail.loosePieces;
                }

                return acc;
            },
            {} as Record<string, any>,
        );
    };

    // Obtener todas las tallas únicas
    const allSizes = new Set<string>();

    packing.details.forEach((detail) => {
        allSizes.add(detail.garmentSize.size.code);
    });
    const sortedSizes = Array.from(allSizes).sort();

    // Calcular totales generales
    const totalsByQuality = {
        primera: 0,
        segunda: 0,
        manchada: 0,
        incompleta: 0,
    };

    packing.details.forEach((detail) => {
        const quality = detail.qualityType || "primera";

        (totalsByQuality as any)[quality] += detail.quantity;
    });

    const totalPieces = Object.values(totalsByQuality).reduce(
        (sum, val) => sum + val,
        0,
    );
    const totalBoxes = packing.totalBoxes || 0;
    const totalBags = packing.totalBags || 0;

    // Obtener información del remitente
    const companyInfo = (packing.companyInfo as any) || {
        name: "Mi Empresa S.A. de C.V.",
        rfc: "RFC123456789",
        address: "Dirección de la empresa",
        city: "Ciudad",
        state: "Estado",
        phone: "************",
    };

    return (
        <div
            className="bg-white p-8 max-w-6xl mx-auto"
            id={`packing-document-${packing.id}`}
        >
            {/* Header con logo y folio */}
            <div className="border-b-2 border-gray-300 pb-4 mb-6">
                <div className="flex items-start justify-between">
                    <div className="flex items-center gap-4">
                        {companyInfo.logo ? (
                            <Image
                                alt={companyInfo.name}
                                className="object-contain"
                                height={80}
                                src={companyInfo.logo}
                                width={150}
                            />
                        ) : (
                            <div className="w-[150px] h-[80px] bg-gray-200 flex items-center justify-center rounded">
                                <span className="text-gray-500 text-sm">
                                    LOGO
                                </span>
                            </div>
                        )}
                    </div>

                    <div className="text-center flex-1">
                        <h1 className="text-3xl font-bold mb-1">
                            PACKING LIST
                        </h1>
                        <p className="text-sm text-gray-600">
                            DOCUMENTO DE EMPAQUE
                        </p>
                    </div>

                    <div className="text-right">
                        <div className="bg-gray-100 p-3 rounded">
                            <p className="text-xs text-gray-600">FOLIO</p>
                            <p className="text-lg font-bold text-red-600">
                                {packing.folio}
                            </p>
                        </div>
                        <p className="text-sm mt-2">
                            <span className="font-semibold">Fecha:</span>{" "}
                            {format(new Date(packing.createdAt), "dd/MM/yyyy", {
                                locale: es,
                            })}
                        </p>
                    </div>
                </div>
            </div>

            {/* Información de remitente y destinatario */}
            <div className="grid grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-50 p-4 rounded">
                    <h3 className="font-bold text-sm mb-2 text-gray-700">
                        REMITENTE
                    </h3>
                    <div className="space-y-1">
                        <p className="font-semibold">{companyInfo.name}</p>
                        {companyInfo.rfc && (
                            <p className="text-sm">RFC: {companyInfo.rfc}</p>
                        )}
                        {companyInfo.address && (
                            <p className="text-sm">
                                {companyInfo.address}
                                {companyInfo.city && `, ${companyInfo.city}`}
                                {companyInfo.state && `, ${companyInfo.state}`}
                            </p>
                        )}
                        {companyInfo.phone && (
                            <p className="text-sm">Tel: {companyInfo.phone}</p>
                        )}
                        {companyInfo.email && (
                            <p className="text-sm">
                                Email: {companyInfo.email}
                            </p>
                        )}
                    </div>
                </div>

                <div className="bg-blue-50 p-4 rounded">
                    <h3 className="font-bold text-sm mb-2 text-blue-900">
                        DESTINATARIO
                    </h3>
                    <div className="space-y-1">
                        <p className="font-semibold text-lg">
                            {packing.customer.name}
                        </p>
                        {packing.subCustomer && (
                            <div className="mt-1 pt-1 border-t border-blue-200">
                                <p className="text-sm text-blue-700">
                                    Subcliente:
                                </p>
                                <p className="font-semibold">
                                    {packing.subCustomer.name}
                                </p>
                            </div>
                        )}
                        <p className="text-sm">
                            <span className="font-semibold">
                                Fecha Entrega:
                            </span>{" "}
                            {format(
                                new Date(packing.deliveryDate),
                                "dd/MM/yyyy",
                                { locale: es },
                            )}
                        </p>
                    </div>
                </div>
            </div>

            {/* Detalle de productos por orden */}
            {Object.entries(groupedByOrder).map(
                ([orderKey, orderData]: [string, any]) => {
                    const orderItems = processOrderItems(orderData.items);

                    return (
                        <div key={orderKey} className="mb-8">
                            <div className="bg-gray-700 text-white p-2 rounded-t">
                                <h3 className="font-bold">
                                    ORDEN: {orderData.cutOrder || orderKey}
                                    {orderData.cutOrder &&
                                        ` (OC: ${orderData.cutOrder})`}
                                </h3>
                            </div>

                            {Object.entries(orderItems).map(
                                ([modelKey, modelData]: [string, any]) => (
                                    <div
                                        key={modelKey}
                                        className="border border-gray-300 border-t-0 mb-4"
                                    >
                                        {/* Información del modelo */}
                                        <div className="bg-gray-100 p-2 border-b border-gray-300">
                                            <div className="flex justify-between items-center">
                                                <div>
                                                    <span className="font-semibold">
                                                        Modelo:
                                                    </span>{" "}
                                                    {modelData.model}
                                                    {modelData.modelDesc &&
                                                        ` - ${modelData.modelDesc}`}
                                                </div>
                                                <div>
                                                    <span className="font-semibold">
                                                        Color:
                                                    </span>{" "}
                                                    {modelData.color}
                                                </div>
                                                {modelData.partNumber && (
                                                    <div>
                                                        <span className="font-semibold">
                                                            Partida:
                                                        </span>{" "}
                                                        {modelData.partNumber}
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Tabla de cantidades por talla y calidad */}
                                        <div className="overflow-x-auto">
                                            <table className="w-full text-sm">
                                                <thead>
                                                    <tr className="bg-gray-50">
                                                        <th className="border border-gray-300 p-2 text-left">
                                                            Talla
                                                        </th>
                                                        {sortedSizes.map(
                                                            (size) => (
                                                                <th
                                                                    key={size}
                                                                    className="border border-gray-300 p-2 text-center w-16"
                                                                >
                                                                    {size}
                                                                </th>
                                                            ),
                                                        )}
                                                        <th className="border border-gray-300 p-2 text-center w-20 bg-gray-100">
                                                            Total
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {Object.entries(
                                                        modelData.qualityGroups,
                                                    ).map(
                                                        ([quality, data]: [
                                                            string,
                                                            any,
                                                        ]) => {
                                                            if (
                                                                data.total === 0
                                                            )
                                                                return null;

                                                            const qualityLabels: Record<
                                                                string,
                                                                string
                                                            > = {
                                                                primera:
                                                                    "Primera",
                                                                segunda:
                                                                    "Segunda",
                                                                manchada:
                                                                    "Manchadas",
                                                                incompleta:
                                                                    "Incompletas",
                                                            };

                                                            const qualityColors: Record<
                                                                string,
                                                                string
                                                            > = {
                                                                primera:
                                                                    "bg-green-50",
                                                                segunda:
                                                                    "bg-yellow-50",
                                                                manchada:
                                                                    "bg-red-50",
                                                                incompleta:
                                                                    "bg-gray-50",
                                                            };

                                                            return (
                                                                <tr
                                                                    key={
                                                                        quality
                                                                    }
                                                                    className={
                                                                        qualityColors[
                                                                            quality
                                                                        ]
                                                                    }
                                                                >
                                                                    <td className="border border-gray-300 p-2 font-medium">
                                                                        {
                                                                            qualityLabels[
                                                                                quality
                                                                            ]
                                                                        }
                                                                    </td>
                                                                    {sortedSizes.map(
                                                                        (
                                                                            size,
                                                                        ) => (
                                                                            <td
                                                                                key={
                                                                                    size
                                                                                }
                                                                                className="border border-gray-300 p-2 text-center"
                                                                            >
                                                                                {data
                                                                                    .sizes[
                                                                                    size
                                                                                ] ||
                                                                                    "-"}
                                                                            </td>
                                                                        ),
                                                                    )}
                                                                    <td className="border border-gray-300 p-2 text-center font-bold bg-gray-100">
                                                                        {
                                                                            data.total
                                                                        }
                                                                    </td>
                                                                </tr>
                                                            );
                                                        },
                                                    )}
                                                    <tr className="bg-gray-200 font-bold">
                                                        <td className="border border-gray-300 p-2">
                                                            TOTAL
                                                        </td>
                                                        {sortedSizes.map(
                                                            (size) => {
                                                                const sizeTotal =
                                                                    Object.values(
                                                                        modelData.qualityGroups,
                                                                    ).reduce(
                                                                        (
                                                                            sum: number,
                                                                            group: any,
                                                                        ) =>
                                                                            sum +
                                                                            (group
                                                                                .sizes[
                                                                                size
                                                                            ] ||
                                                                                0),
                                                                        0,
                                                                    );

                                                                return (
                                                                    <td
                                                                        key={
                                                                            size
                                                                        }
                                                                        className="border border-gray-300 p-2 text-center"
                                                                    >
                                                                        {sizeTotal ||
                                                                            "-"}
                                                                    </td>
                                                                );
                                                            },
                                                        )}
                                                        <td className="border border-gray-300 p-2 text-center bg-gray-300">
                                                            {Object.values(
                                                                modelData.qualityGroups,
                                                            ).reduce(
                                                                (
                                                                    sum: number,
                                                                    group: any,
                                                                ) =>
                                                                    sum +
                                                                    group.total,
                                                                0,
                                                            )}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                ),
                            )}

                            {/* Resumen de empaque por orden */}
                            {packing.summaries &&
                                packing.summaries.length > 0 && (
                                    <div className="mt-4 p-4 bg-blue-50 rounded">
                                        <h4 className="font-bold text-sm mb-3">
                                            RESUMEN DE EMPAQUE - {orderKey}
                                        </h4>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <table className="w-full text-sm">
                                                    <thead>
                                                        <tr className="bg-blue-100">
                                                            <th className="border border-blue-300 p-1">
                                                                Tipo
                                                            </th>
                                                            <th className="border border-blue-300 p-1">
                                                                Cantidad
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td className="border border-blue-300 p-1">
                                                                Cajas
                                                            </td>
                                                            <td className="border border-blue-300 p-1 text-center">
                                                                {Object.values(
                                                                    orderItems,
                                                                ).reduce(
                                                                    (
                                                                        sum: number,
                                                                        item: any,
                                                                    ) =>
                                                                        sum +
                                                                        item
                                                                            .packageSummary
                                                                            .boxes,
                                                                    0,
                                                                )}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td className="border border-blue-300 p-1">
                                                                Bolsas
                                                            </td>
                                                            <td className="border border-blue-300 p-1 text-center">
                                                                {Object.values(
                                                                    orderItems,
                                                                ).reduce(
                                                                    (
                                                                        sum: number,
                                                                        item: any,
                                                                    ) =>
                                                                        sum +
                                                                        item
                                                                            .packageSummary
                                                                            .bags,
                                                                    0,
                                                                )}
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div>
                                                <table className="w-full text-sm">
                                                    <thead>
                                                        <tr className="bg-blue-100">
                                                            <th className="border border-blue-300 p-1">
                                                                Talla
                                                            </th>
                                                            <th className="border border-blue-300 p-1">
                                                                Cajas
                                                            </th>
                                                            <th className="border border-blue-300 p-1">
                                                                Pzs
                                                            </th>
                                                            <th className="border border-blue-300 p-1">
                                                                B.1ra
                                                            </th>
                                                            <th className="border border-blue-300 p-1">
                                                                B.2da
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {sortedSizes.map(
                                                            (size) => {
                                                                const sizeBoxes =
                                                                    Object.values(
                                                                        orderItems,
                                                                    ).reduce(
                                                                        (
                                                                            sum: number,
                                                                            item: any,
                                                                        ) =>
                                                                            sum +
                                                                            (item
                                                                                .packageSummary
                                                                                .boxesBySize[
                                                                                size
                                                                            ] ||
                                                                                0),
                                                                        0,
                                                                    );
                                                                const sizePieces =
                                                                    Object.values(
                                                                        orderItems,
                                                                    ).reduce(
                                                                        (
                                                                            sum: number,
                                                                            item: any,
                                                                        ) =>
                                                                            sum +
                                                                            (item
                                                                                .packageSummary
                                                                                .piecesBySize[
                                                                                size
                                                                            ] ||
                                                                                0),
                                                                        0,
                                                                    );
                                                                const sizeBags =
                                                                    Object.values(
                                                                        orderItems,
                                                                    ).reduce(
                                                                        (
                                                                            sum: number,
                                                                            item: any,
                                                                        ) =>
                                                                            sum +
                                                                            (item
                                                                                .packageSummary
                                                                                .bagsBySize[
                                                                                size
                                                                            ] ||
                                                                                0),
                                                                        0,
                                                                    );

                                                                if (
                                                                    sizeBoxes ===
                                                                        0 &&
                                                                    sizePieces ===
                                                                        0 &&
                                                                    sizeBags ===
                                                                        0
                                                                )
                                                                    return null;

                                                                return (
                                                                    <tr
                                                                        key={
                                                                            size
                                                                        }
                                                                    >
                                                                        <td className="border border-blue-300 p-1 text-center">
                                                                            {
                                                                                size
                                                                            }
                                                                        </td>
                                                                        <td className="border border-blue-300 p-1 text-center">
                                                                            {sizeBoxes ||
                                                                                "-"}
                                                                        </td>
                                                                        <td className="border border-blue-300 p-1 text-center">
                                                                            {sizePieces ||
                                                                                "-"}
                                                                        </td>
                                                                        <td className="border border-blue-300 p-1 text-center">
                                                                            {/* TODO: Separar bolsas por calidad */}
                                                                            {sizeBags ||
                                                                                "-"}
                                                                        </td>
                                                                        <td className="border border-blue-300 p-1 text-center">
                                                                            -
                                                                        </td>
                                                                    </tr>
                                                                );
                                                            },
                                                        )}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                )}
                        </div>
                    );
                },
            )}

            {/* Resumen general */}
            <div className="bg-gray-100 p-4 rounded mb-6">
                <h3 className="font-bold mb-3">RESUMEN GENERAL</h3>
                <div className="grid grid-cols-4 gap-4 text-center">
                    <div>
                        <p className="text-sm text-gray-600">Total Piezas</p>
                        <p className="text-2xl font-bold">{totalPieces}</p>
                    </div>
                    <div>
                        <p className="text-sm text-gray-600">Total Cajas</p>
                        <p className="text-2xl font-bold">{totalBoxes}</p>
                    </div>
                    <div>
                        <p className="text-sm text-gray-600">Total Bolsas</p>
                        <p className="text-2xl font-bold">{totalBags}</p>
                    </div>
                    <div>
                        <p className="text-sm text-gray-600">Tipo Empaque</p>
                        <p className="text-lg font-semibold capitalize">
                            {packing.packingType || "Mixto"}
                        </p>
                    </div>
                </div>

                <div className="grid grid-cols-4 gap-4 mt-4 pt-4 border-t border-gray-300">
                    <div
                        className={
                            totalsByQuality.primera > 0
                                ? "text-green-600"
                                : "text-gray-400"
                        }
                    >
                        <p className="text-sm">Primera</p>
                        <p className="text-xl font-bold">
                            {totalsByQuality.primera}
                        </p>
                    </div>
                    <div
                        className={
                            totalsByQuality.segunda > 0
                                ? "text-yellow-600"
                                : "text-gray-400"
                        }
                    >
                        <p className="text-sm">Segunda</p>
                        <p className="text-xl font-bold">
                            {totalsByQuality.segunda}
                        </p>
                    </div>
                    <div
                        className={
                            totalsByQuality.manchada > 0
                                ? "text-red-600"
                                : "text-gray-400"
                        }
                    >
                        <p className="text-sm">Manchadas</p>
                        <p className="text-xl font-bold">
                            {totalsByQuality.manchada}
                        </p>
                    </div>
                    <div
                        className={
                            totalsByQuality.incompleta > 0
                                ? "text-gray-600"
                                : "text-gray-400"
                        }
                    >
                        <p className="text-sm">Incompletas</p>
                        <p className="text-xl font-bold">
                            {totalsByQuality.incompleta}
                        </p>
                    </div>
                </div>
            </div>

            {/* Notas y observaciones */}
            {(packing.notes || packing.transportNotes) && (
                <div className="mb-6 p-4 bg-yellow-50 rounded">
                    <h3 className="font-bold text-sm mb-2">
                        NOTAS Y OBSERVACIONES
                    </h3>
                    {packing.notes && (
                        <div className="mb-2">
                            <p className="text-sm font-semibold">
                                Notas Generales:
                            </p>
                            <p className="text-sm">{packing.notes}</p>
                        </div>
                    )}
                    {packing.transportNotes && (
                        <div>
                            <p className="text-sm font-semibold">
                                Notas de Transporte:
                            </p>
                            <p className="text-sm">{packing.transportNotes}</p>
                        </div>
                    )}
                </div>
            )}

            {/* Código QR y firmas */}
            {showSignatures && (
                <div className="mt-8 pt-6 border-t-2 border-gray-300">
                    <div className="grid grid-cols-3 gap-8">
                        <div className="text-center">
                            <QRCodeSVG
                                className="mx-auto mb-2"
                                size={80}
                                value={`PACKING:${packing.folio}:${packing.id}`}
                            />
                            <p className="text-xs text-gray-600">
                                Código de Rastreo
                            </p>
                        </div>

                        <div className="col-span-2">
                            <div className="grid grid-cols-2 gap-6">
                                <div>
                                    <div className="border-b-2 border-gray-400 h-16 mb-2" />
                                    <p className="text-sm font-semibold">
                                        Empacó
                                    </p>
                                    <p className="text-xs text-gray-600">
                                        Nombre y Firma
                                    </p>
                                </div>
                                <div>
                                    <div className="border-b-2 border-gray-400 h-16 mb-2" />
                                    <p className="text-sm font-semibold">
                                        Verificó
                                    </p>
                                    <p className="text-xs text-gray-600">
                                        Nombre y Firma
                                    </p>
                                </div>
                                <div>
                                    <div className="border-b-2 border-gray-400 h-16 mb-2" />
                                    <p className="text-sm font-semibold">
                                        Transportista
                                    </p>
                                    <p className="text-xs text-gray-600">
                                        Nombre y Firma
                                    </p>
                                </div>
                                <div>
                                    <div className="border-b-2 border-gray-400 h-16 mb-2" />
                                    <p className="text-sm font-semibold">
                                        Recibió
                                    </p>
                                    <p className="text-xs text-gray-600">
                                        Nombre y Firma
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
