"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Input,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>dal<PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dalBody,
    ModalFooter,
    useDisclosure,
    addToast,
} from "@heroui/react";
import {
    ChevronLeftIcon,
    SwatchIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    Card,
    CardBody,
    CardHeader,
} from "@/shared/components/ui/hero-ui-client";
import { useCreateColor } from "@/features/colors/hooks/useColor";
import { createColorSchema } from "@/features/colors/schemas";

type ColorFormValues = z.infer<typeof createColorSchema>;

// Colores predefinidos comunes
const PRESET_COLORS = [
    { name: "Negro", hex: "#000000" },
    { name: "<PERSON>", hex: "#FFFFFF" },
    { name: "<PERSON><PERSON>", hex: "#6B7280" },
    { name: "Rojo", hex: "#DC2626" },
    { name: "Rosa", hex: "#EC4899" },
    { name: "Naranja", hex: "#EA580C" },
    { name: "Amarillo", hex: "#EAB308" },
    { name: "Verde", hex: "#16A34A" },
    { name: "Azul", hex: "#2563EB" },
    { name: "Índigo", hex: "#4F46E5" },
    { name: "Púrpura", hex: "#9333EA" },
    { name: "Marrón", hex: "#92400E" },
];

export default function NewColorPage() {
    const router = useRouter();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { createColor } = useCreateColor();

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isDirty, isValid },
        reset,
    } = useForm<ColorFormValues>({
        resolver: zodResolver(createColorSchema),
        mode: "onChange",
        defaultValues: {
            name: "",
            hexCode: "#000000",
        },
    });

    const currentHexCode = watch("hexCode");

    const onSubmit = async (data: ColorFormValues) => {
        setIsSubmitting(true);

        try {
            const result = await createColor(data);

            if (!result) {
                throw new Error("No se pudo completar la operación");
            }

            if (!result.success) {
                throw new Error(result.error || "Error al crear el color");
            }

            addToast({
                title: "¡Color creado!",
                description: `${data.name} se ha añadido al catálogo.`,
                color: "success",
            });

            reset();
            router.push("/dashboard/colors");
            router.refresh();
        } catch (err) {
            addToast({
                title: "Error",
                description:
                    err instanceof Error
                        ? err.message
                        : "Error al crear el color",
                color: "danger",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        if (isDirty) {
            onOpen();
        } else {
            router.push("/dashboard/colors");
        }
    };

    const selectPresetColor = (preset: { name: string; hex: string }) => {
        setValue("hexCode", preset.hex, { shouldValidate: true });
        setValue("name", preset.name, { shouldValidate: true });
    };

    return (
        <DashboardLayout
            actions={
                <Button
                    startContent={<ChevronLeftIcon className="w-4 h-4" />}
                    variant="light"
                    onPress={() => router.push("/dashboard/colors")}
                >
                    Volver
                </Button>
            }
            breadcrumbs={[
                { label: "Dashboard", href: "/dashboard" },
                { label: "Colores", href: "/dashboard/colors" },
                { label: "Nuevo" },
            ]}
            subtitle="Añade un color para tus prendas"
            title="Nuevo Color"
        >
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
            >
                <Card className="shadow-xl">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-primary-50 dark:bg-primary-900/20">
                                <SwatchIcon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold">
                                    Crear Color
                                </h2>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Define un nuevo color para tu catálogo
                                </p>
                            </div>
                        </div>
                    </CardHeader>

                    <CardBody className="p-6">
                        <form
                            className="space-y-6"
                            onSubmit={handleSubmit(onSubmit)}
                        >
                            {/* Vista previa del color */}
                            <div className="flex justify-center">
                                <div className="text-center">
                                    <div
                                        className="w-32 h-32 rounded-2xl shadow-lg border-4 border-white dark:border-gray-700 mb-3"
                                        style={{
                                            backgroundColor: currentHexCode,
                                        }}
                                    />
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        {currentHexCode.toUpperCase()}
                                    </p>
                                </div>
                            </div>

                            {/* Campos del formulario */}
                            <div className="space-y-4">
                                <Input
                                    {...register("name")}
                                    isRequired
                                    classNames={{
                                        inputWrapper: "shadow-sm",
                                    }}
                                    errorMessage={errors.name?.message}
                                    isInvalid={!!errors.name}
                                    label="Nombre del Color"
                                    placeholder="Ej: Azul Cielo"
                                    size="lg"
                                    variant="bordered"
                                />

                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Código de Color
                                    </label>
                                    <div className="flex gap-3 items-center">
                                        <Input
                                            {...register("hexCode")}
                                            isRequired
                                            classNames={{
                                                inputWrapper: "shadow-sm",
                                                input: "font-mono",
                                            }}
                                            errorMessage={
                                                errors.hexCode?.message
                                            }
                                            isInvalid={!!errors.hexCode}
                                            placeholder="#000000"
                                            size="lg"
                                            variant="bordered"
                                        />
                                        <label className="relative cursor-pointer">
                                            <input
                                                className="sr-only"
                                                type="color"
                                                value={currentHexCode}
                                                onChange={(e) =>
                                                    setValue(
                                                        "hexCode",
                                                        e.target.value,
                                                        {
                                                            shouldValidate:
                                                                true,
                                                        },
                                                    )
                                                }
                                            />
                                            <div
                                                className="w-14 h-14 rounded-lg shadow-md border-2 border-gray-300 dark:border-gray-600 hover:scale-105 transition-transform"
                                                style={{
                                                    backgroundColor:
                                                        currentHexCode,
                                                }}
                                            />
                                        </label>
                                    </div>
                                </div>
                            </div>

                            {/* Paleta de colores predefinidos */}
                            <div className="space-y-2">
                                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Colores rápidos
                                </p>
                                <div className="grid grid-cols-6 gap-2">
                                    {PRESET_COLORS.map((preset) => (
                                        <button
                                            key={preset.hex}
                                            className="group relative"
                                            type="button"
                                            onClick={() =>
                                                selectPresetColor(preset)
                                            }
                                        >
                                            <div
                                                className="w-full aspect-square rounded-lg shadow-sm border-2 border-gray-200 dark:border-gray-700 hover:scale-105 transition-transform"
                                                style={{
                                                    backgroundColor: preset.hex,
                                                }}
                                            />
                                            <span className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                                <span className="bg-black/75 text-white text-xs px-2 py-1 rounded">
                                                    {preset.name}
                                                </span>
                                            </span>
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Botones de acción */}
                            <div className="flex justify-end gap-3 pt-4">
                                <Button variant="flat" onPress={handleCancel}>
                                    Cancelar
                                </Button>

                                <Button
                                    color="primary"
                                    isDisabled={!isValid || isSubmitting}
                                    isLoading={isSubmitting}
                                    startContent={
                                        !isSubmitting && (
                                            <CheckCircleIcon className="w-5 h-5" />
                                        )
                                    }
                                    type="submit"
                                >
                                    {isSubmitting
                                        ? "Creando..."
                                        : "Crear Color"}
                                </Button>
                            </div>
                        </form>
                    </CardBody>
                </Card>
            </motion.div>

            {/* Modal de confirmación */}
            <Modal isOpen={isOpen} onClose={onClose}>
                <ModalContent>
                    <ModalHeader>Cambios sin guardar</ModalHeader>
                    <ModalBody>
                        ¿Estás seguro de que deseas salir? Los cambios se
                        perderán.
                    </ModalBody>
                    <ModalFooter>
                        <Button variant="flat" onPress={onClose}>
                            Continuar editando
                        </Button>
                        <Button
                            color="danger"
                            onPress={() => {
                                onClose();
                                router.push("/dashboard/colors");
                            }}
                        >
                            Salir sin guardar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </DashboardLayout>
    );
}
