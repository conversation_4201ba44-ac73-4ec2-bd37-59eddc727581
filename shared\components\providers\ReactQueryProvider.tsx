"use client";

import React from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

import {
    getQueryClient,
    performanceFlags,
} from "@/shared/lib/performance/query-client";

interface ReactQueryProviderProps {
    children: React.ReactNode;
}

/**
 * React Query Provider with DevTools
 * Only enabled when feature flag is active
 */
export function ReactQueryProvider({ children }: ReactQueryProviderProps) {
    // Only provide React Query if feature flag is enabled
    if (!performanceFlags.useReactQuery) {
        return <>{children}</>;
    }

    // Get or create query client
    const queryClient = getQueryClient();

    return (
        <QueryClientProvider client={queryClient}>
            {children}
            {process.env.NODE_ENV === "development" && (
                <ReactQueryDevtools
                    buttonPosition="bottom-right"
                    initialIsOpen={false}
                    position={"bottom-right" as any}
                />
            )}
        </QueryClientProvider>
    );
}
