# Testing del Sistema de Remisión Automática

## Descripción

Este directorio contiene los tests de integración y unitarios para el flujo completo de remisión automática.

## Estructura de Tests

```
tests/
├── integration/
│   └── remission-flow.test.ts      # Test E2E del flujo completo
└── unit/
    └── hooks/
        └── useRemissionGeneration.test.ts  # Test unitario del hook
```

## Tests de Integración

### remission-flow.test.ts

Test completo end-to-end que cubre:

1. **Flujo de Éxito Completo**
   - Creación de asignaciones
   - Generación automática de remisión
   - Visualización del preview modal
   - Edición del preview
   - Generación y descarga del PDF

2. **Casos de Error**
   - Error al crear asignaciones
   - Error al generar PDF
   - Recuperación después de errores (retry)

3. **Integración con UI**
   - Verificación de estados de procesamiento
   - Notificaciones toast
   - Panel de progreso

## Tests Unitarios

### useRemissionGeneration.test.ts

Test del hook principal que maneja:

- Estado inicial del hook
- Proceso de generación de remisión
- Manejo de errores
- Descarga de PDF
- Reset del estado
- Actualización de progreso

## Ejecución de Tests

### Instalar dependencias de testing (si no están instaladas)

```bash
npm install --save-dev jest @testing-library/react @testing-library/react-hooks @testing-library/user-event @testing-library/jest-dom
npm install --save-dev ts-jest @types/jest
```

### Ejecutar todos los tests

```bash
npm test
```

### Ejecutar solo tests de integración

```bash
npm test -- tests/integration
```

### Ejecutar solo tests unitarios

```bash
npm test -- tests/unit
```

### Ejecutar con coverage

```bash
npm test -- --coverage
```

## Configuración Jest

Si no existe, crear `jest.config.js` en la raíz del proyecto:

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  roots: ['<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/__tests__/**/*.tsx',
    '**/?(*.)+(spec|test).ts',
    '**/?(*.)+(spec|test).tsx'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
}
```

## Cobertura Esperada

- **Flujo E2E**: 100% de casos principales
- **Hook**: >90% de cobertura de código
- **Manejo de errores**: Todos los casos cubiertos

## Casos de Prueba Cubiertos

✅ Creación exitosa de asignaciones  
✅ Generación automática de remisión  
✅ Preview modal se muestra automáticamente  
✅ Edición del preview funciona  
✅ Generación de PDF exitosa  
✅ Descarga de PDF funciona  
✅ Manejo de errores de red  
✅ Reintentos después de fallos  
✅ Estados de procesamiento correctos  
✅ Notificaciones al usuario  

## Próximos Pasos

Para ejecutar los tests en CI/CD:

1. Agregar script en `package.json`:
   ```json
   "test:ci": "jest --ci --coverage --maxWorkers=2"
   ```

2. Configurar GitHub Actions o similar
3. Establecer umbrales de cobertura mínima
