// lib/utils.ts - Adaptado para evitar errores de Prisma en el cliente
import { capitalized, cn } from "./client";

// Re-exportar funciones seguras para el cliente para mantener compatibilidad
export { capitalized, cn };

// Las funciones que dependen de Prisma han sido movidas a server-utils.ts
// y deben ser importadas de allí para código que se ejecuta en el servidor.

// Re-exportar todas las funciones de dateUtils
export * from "./dateUtils";

// Re-exportar logger centralizado
export * from "./logger";
