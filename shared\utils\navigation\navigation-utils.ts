/**
 * Determina si un ítem de navegación debe mostrarse como activo basado en la ruta actual
 * @param pathname - La ruta actual de la aplicación
 * @param itemHref - La ruta del ítem de navegación
 * @returns boolean - true si el ítem debe mostrarse como activo
 */
export function isNavigationItemActive(
    pathname: string,
    itemHref: string,
): boolean {
    // Extraer segmentos de ruta
    const pathSegments = pathname.split("/").filter(Boolean);

    // Caso especial: Dashboard principal
    if (
        itemHref === "/dashboard" &&
        pathSegments.length === 1 &&
        pathSegments[0] === "dashboard"
    ) {
        return true;
    }

    // Extraer segmentos del item
    const itemSegments = itemHref.split("/").filter(Boolean);

    // Para otros items, comparar el segundo segmento (la sección)
    if (pathSegments.length > 1 && itemSegments.length > 1) {
        return pathSegments[1] === itemSegments[1];
    }

    return false;
}
