// Enhanced Importance Badge with modern animations
"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Chip } from "@heroui/react";
import {
    ExclamationTriangleIcon,
    FireIcon,
    InformationCircleIcon,
    CheckCircleIcon,
} from "@heroicons/react/24/outline";
import clsx from "clsx";

import { pulseVariants } from "@/shared/utils/ui/animations";
import { neonColors } from "@/shared/utils/ui/color-palette";

interface ImportanceBadgeProps {
    importance: {
        id: string;
        name: string;
        color?: string | null;
        iconName?: string | null;
    };
    animated?: boolean;
    size?: "sm" | "md" | "lg";
}

export function ImportanceBadge({
    importance,
    animated = false,
    size = "sm",
}: ImportanceBadgeProps) {
    // Enhanced icon mapping with more options
    const getIcon = () => {
        const iconClass = clsx(
            size === "sm" && "w-3 h-3",
            size === "md" && "w-4 h-4",
            size === "lg" && "w-5 h-5",
        );

        if (importance.iconName === "fire" || importance.name === "Urgente") {
            return <FireIcon className={iconClass} />;
        }
        if (importance.name === "Alta") {
            return <ExclamationTriangleIcon className={iconClass} />;
        }
        if (importance.name === "Media") {
            return <InformationCircleIcon className={iconClass} />;
        }
        if (importance.name === "Baja") {
            return <CheckCircleIcon className={iconClass} />;
        }

        return null;
    };

    const icon = getIcon();
    const isHighPriority =
        importance.name === "Alta" || importance.name === "Urgente";
    const shouldAnimate = animated && isHighPriority;

    // Dynamic glow effect
    const glowAnimation = shouldAnimate
        ? {
              animate: {
                  boxShadow: [
                      `0 0 10px ${importance.color}40`,
                      `0 0 30px ${importance.color}60`,
                      `0 0 10px ${importance.color}40`,
                  ],
              },
              transition: {
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
              },
          }
        : {};

    return (
        <AnimatePresence>
            <motion.div
                animate={{ scale: 1, opacity: 1 }}
                className="relative"
                exit={{ scale: 0.8, opacity: 0 }}
                initial={{ scale: 0.8, opacity: 0 }}
                transition={{ type: "spring", damping: 15 }}
            >
                {/* Animated background pulse for urgent items */}
                {shouldAnimate && (
                    <motion.div
                        animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.1, 0.3],
                        }}
                        className="absolute inset-0 rounded-full"
                        style={{
                            backgroundColor:
                                importance.color || neonColors.pink,
                            filter: "blur(8px)",
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut",
                        }}
                    />
                )}

                <motion.div
                    animate={shouldAnimate ? "animate" : "initial"}
                    initial="initial"
                    variants={shouldAnimate ? pulseVariants : undefined}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    {...glowAnimation}
                >
                    <Chip
                        className={clsx(
                            "relative font-medium transition-all duration-300",
                            "backdrop-blur-sm border",
                            shouldAnimate && "animate-pulse-glow",
                        )}
                        size={size}
                        startContent={
                            icon && (
                                <motion.span
                                    animate={
                                        shouldAnimate
                                            ? {
                                                  rotate: [0, 10, -10, 0],
                                              }
                                            : {}
                                    }
                                    transition={{
                                        duration: 0.5,
                                        repeat: shouldAnimate ? Infinity : 0,
                                        repeatDelay: 1.5,
                                    }}
                                >
                                    {icon}
                                </motion.span>
                            )
                        }
                        style={{
                            backgroundColor:
                                `${importance.color}CC` || "#9CA3AFCC",
                            color: "white",
                            borderColor: `${importance.color}` || "#9CA3AF",
                            textShadow: shouldAnimate
                                ? `0 0 10px ${importance.color}`
                                : undefined,
                        }}
                        variant="solid"
                    >
                        <motion.span
                            animate={
                                shouldAnimate
                                    ? {
                                          textShadow: [
                                              "0 0 0px currentColor",
                                              "0 0 5px currentColor",
                                              "0 0 0px currentColor",
                                          ],
                                      }
                                    : {}
                            }
                            className={clsx(
                                "font-semibold",
                                size === "sm" && "text-xs",
                                size === "md" && "text-sm",
                                size === "lg" && "text-base",
                            )}
                            transition={{
                                duration: 1.5,
                                repeat: shouldAnimate ? Infinity : 0,
                                ease: "easeInOut",
                            }}
                        >
                            {importance.name}
                        </motion.span>
                    </Chip>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
}
