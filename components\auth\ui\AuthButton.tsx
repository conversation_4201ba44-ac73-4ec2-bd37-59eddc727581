"use client";

import { forwardRef, ButtonHTMLAttributes, ReactNode } from "react";

import { cn } from "@/lib/utils";

interface AuthButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: "primary" | "secondary" | "social";
    isLoading?: boolean;
    icon?: ReactNode;
}

export const AuthButton = forwardRef<HTMLButtonElement, AuthButtonProps>(
    (
        {
            children,
            className,
            variant = "primary",
            isLoading = false,
            icon,
            disabled,
            ...props
        },
        ref,
    ) => {
        const baseStyles =
            "relative w-full py-3 px-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2";

        const variants = {
            primary: cn(
                "bg-gradient-to-r from-primary-500 to-primary-600",
                "dark:from-blue-600 dark:to-blue-700",
                "text-white",
                "shadow-[0_4px_15px_rgba(59,130,246,0.4),0_2px_4px_rgba(0,0,0,0.1)]",
                "dark:shadow-[0_4px_15px_rgba(59,130,246,0.3)]",
                "hover:shadow-[0_6px_20px_rgba(59,130,246,0.5),0_3px_6px_rgba(0,0,0,0.15)]",
                "dark:hover:shadow-[0_6px_20px_rgba(59,130,246,0.4)]",
                "hover:transform hover:-translate-y-0.5",
                "active:transform active:translate-y-0",
                "active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.2),0_1px_2px_rgba(59,130,246,0.3)]",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none",
            ),
            secondary: cn(
                "bg-white dark:bg-slate-800 border-2 border-neutral-300 dark:border-slate-700",
                "text-neutral-700 dark:text-slate-300",
                "shadow-[0_2px_5px_rgba(0,0,0,0.05)] dark:shadow-none",
                "hover:shadow-[0_4px_10px_rgba(0,0,0,0.1)] dark:hover:border-slate-600",
                "hover:border-neutral-400",
                "active:shadow-[inset_0_2px_4px_rgba(0,0,0,0.1)]",
            ),
            social: cn(
                "bg-neutral-100 dark:bg-slate-800 border border-neutral-300 dark:border-slate-700",
                "text-neutral-700 dark:text-slate-300",
                "shadow-[2px_2px_5px_rgba(0,0,0,0.05),-2px_-2px_5px_rgba(255,255,255,0.5)]",
                "dark:shadow-none",
                "hover:shadow-[3px_3px_7px_rgba(0,0,0,0.08),-3px_-3px_7px_rgba(255,255,255,0.6)]",
                "dark:hover:border-slate-600",
                "active:shadow-[inset_1px_1px_3px_rgba(0,0,0,0.1)]",
            ),
        };

        return (
            <button
                ref={ref}
                className={cn(baseStyles, variants[variant], className)}
                disabled={disabled || isLoading}
                {...props}
            >
                {isLoading ? (
                    <>
                        <svg
                            className="animate-spin h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                            />
                            <path
                                className="opacity-75"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                fill="currentColor"
                            />
                        </svg>
                        <span>Cargando...</span>
                    </>
                ) : (
                    <>
                        {icon && <span className="w-5 h-5">{icon}</span>}
                        {children}
                    </>
                )}
            </button>
        );
    },
);

AuthButton.displayName = "AuthButton";
