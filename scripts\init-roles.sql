-- Script para crear roles en la base de datos LOHARI
-- Ejecutar con: npx prisma db execute --file scripts/init-roles.sql

-- Crear rol ADMIN si no existe
INSERT INTO "Role" (id, name, "iconName", color, "createdAt", "updatedAt")
SELECT 
    gen_random_uuid(),
    'ADMIN',
    'ShieldCheckIcon',
    '#EF4444',
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "Role" WHERE name = 'ADMIN'
);

-- Crear rol EMPLOYEE si no existe
INSERT INTO "Role" (id, name, "iconName", color, "createdAt", "updatedAt")
SELECT 
    gen_random_uuid(),
    'EMPLOYEE',
    'UserIcon',
    '#3B82F6',
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "Role" WHERE name = 'EMPLOYEE'
);

-- Crear rol CONTRACTOR si no existe
INSERT INTO "Role" (id, name, "iconName", color, "createdAt", "updatedAt")
SELECT 
    gen_random_uuid(),
    'CONTRACTOR',
    'BriefcaseIcon',
    '#10B981',
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "Role" WHERE name = 'CONTRACTOR'
);

-- Crear rol GUEST si no existe
INSERT INTO "Role" (id, name, "iconName", color, "createdAt", "updatedAt")
SELECT 
    gen_random_uuid(),
    'GUEST',
    'UserCircleIcon',
    '#9CA3AF',
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM "Role" WHERE name = 'GUEST'
);

-- Mostrar los roles creados
SELECT * FROM "Role";
