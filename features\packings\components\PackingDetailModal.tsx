"use client";

import React from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Di<PERSON>r,
    Chip,
    Card,
    CardBody,
} from "@heroui/react";
import {
    DocumentTextIcon,
    CalendarIcon,
    BuildingOfficeIcon,
    UserGroupIcon,
    ClockIcon,
    PrinterIcon,
    TruckIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import { PackingStatusBadge } from "./PackingStatusBadge";

interface PackingDetailModalProps {
    isOpen: boolean;
    onClose: () => void;
    packing: any;
    onPrint?: () => void;
    onUpdateStatus?: () => void;
}

export function PackingDetailModal({
    isOpen,
    onClose,
    packing,
    onPrint,
    onUpdateStatus,
}: PackingDetailModalProps) {
    if (!packing) return null;

    // Agrupar detalles por modelo y color
    const groupedDetails = packing.details.reduce((acc: any, detail: any) => {
        const key = `${detail.garmentSize.garment.model.code}-${detail.garmentSize.garment.color.name}`;

        if (!acc[key]) {
            acc[key] = {
                model: detail.garmentSize.garment.model,
                color: detail.garmentSize.garment.color.name,
                items: [],
            };
        }
        acc[key].items.push({
            size: detail.garmentSize.size.code,
            quantity: detail.quantity,
            id: detail.id,
        });

        return acc;
    }, {});

    const totalPieces = packing.details.reduce(
        (sum: number, detail: any) => sum + detail.quantity,
        0,
    );

    return (
        <Modal
            isOpen={isOpen}
            scrollBehavior="inside"
            size="3xl"
            onClose={onClose}
        >
            <ModalContent>
                <ModalHeader className="flex flex-col gap-1">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <DocumentTextIcon className="w-6 h-6" />
                            <span>Detalles del Packing {packing.folio}</span>
                        </div>
                        <PackingStatusBadge status={packing.status} />
                    </div>
                </ModalHeader>

                <Divider />

                <ModalBody className="gap-4">
                    {/* Información general */}
                    <Card>
                        <CardBody className="gap-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <BuildingOfficeIcon className="w-5 h-5 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Cliente
                                            </p>
                                            <p className="font-medium">
                                                {packing.customer.name}
                                            </p>
                                        </div>
                                    </div>

                                    {packing.subCustomer && (
                                        <div className="flex items-center gap-2">
                                            <UserGroupIcon className="w-5 h-5 text-gray-400" />
                                            <div>
                                                <p className="text-sm text-gray-500">
                                                    Subcliente
                                                </p>
                                                <p className="font-medium">
                                                    {packing.subCustomer.name}
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    <div className="flex items-center gap-2">
                                        <CalendarIcon className="w-5 h-5 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Fecha de entrega
                                            </p>
                                            <p className="font-medium">
                                                {format(
                                                    new Date(
                                                        packing.deliveryDate,
                                                    ),
                                                    "PPP",
                                                    { locale: es },
                                                )}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                        <ClockIcon className="w-5 h-5 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Creado
                                            </p>
                                            <p className="font-medium">
                                                {formatDistanceToNow(
                                                    new Date(packing.createdAt),
                                                    {
                                                        addSuffix: true,
                                                        locale: es,
                                                    },
                                                )}
                                            </p>
                                        </div>
                                    </div>

                                    {packing.printedAt && (
                                        <div className="flex items-center gap-2">
                                            <PrinterIcon className="w-5 h-5 text-gray-400" />
                                            <div>
                                                <p className="text-sm text-gray-500">
                                                    Impreso
                                                </p>
                                                <p className="font-medium">
                                                    {format(
                                                        new Date(
                                                            packing.printedAt,
                                                        ),
                                                        "PPp",
                                                        { locale: es },
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    {packing.order && (
                                        <div className="flex items-center gap-2">
                                            <TruckIcon className="w-5 h-5 text-gray-400" />
                                            <div>
                                                <p className="text-sm text-gray-500">
                                                    Orden vinculada
                                                </p>
                                                <p className="font-medium">
                                                    {packing.order.cutOrder}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {packing.notes && (
                                <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                                        Notas:
                                    </p>
                                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                                        {packing.notes}
                                    </p>
                                </div>
                            )}
                        </CardBody>
                    </Card>

                    {/* Detalles de artículos */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3 flex items-center justify-between">
                            <span>Artículos del Packing</span>
                            <Chip size="sm" variant="flat">
                                {totalPieces} piezas totales
                            </Chip>
                        </h3>

                        <div className="space-y-3">
                            {Object.entries(groupedDetails).map(
                                ([key, group]: [string, any]) => (
                                    <Card key={key}>
                                        <CardBody>
                                            <div className="flex items-center justify-between mb-3">
                                                <div>
                                                    <p className="font-medium">
                                                        {group.model.code}
                                                    </p>
                                                    <p className="text-sm text-gray-500">
                                                        {group.color}
                                                    </p>
                                                </div>
                                                <Chip
                                                    color="primary"
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    {group.items.reduce(
                                                        (
                                                            sum: number,
                                                            item: any,
                                                        ) =>
                                                            sum + item.quantity,
                                                        0,
                                                    )}{" "}
                                                    pzs
                                                </Chip>
                                            </div>

                                            <div className="flex flex-wrap gap-2">
                                                {group.items.map(
                                                    (item: any) => (
                                                        <Chip
                                                            key={item.id}
                                                            size="sm"
                                                            variant="bordered"
                                                        >
                                                            Talla {item.size}:{" "}
                                                            {item.quantity}
                                                        </Chip>
                                                    ),
                                                )}
                                            </div>
                                        </CardBody>
                                    </Card>
                                ),
                            )}
                        </div>
                    </div>

                    {/* Historial */}
                    {packing.history && packing.history.length > 0 && (
                        <div>
                            <h3 className="text-lg font-semibold mb-3">
                                Historial
                            </h3>
                            <div className="space-y-2">
                                {packing.history.map((event: any) => (
                                    <div
                                        key={event.id}
                                        className="flex items-center gap-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-900/50"
                                    >
                                        <ClockIcon className="w-4 h-4 text-gray-400" />
                                        <div className="flex-1">
                                            <p className="text-sm">
                                                <span className="font-medium">
                                                    {event.action}
                                                </span>
                                                {event.metadata?.notes && (
                                                    <span className="text-gray-500">
                                                        {" "}
                                                        - {event.metadata.notes}
                                                    </span>
                                                )}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {format(
                                                    new Date(event.timestamp),
                                                    "PPp",
                                                    { locale: es },
                                                )}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </ModalBody>

                <Divider />

                <ModalFooter>
                    <Button variant="light" onPress={onClose}>
                        Cerrar
                    </Button>
                    {onUpdateStatus && (
                        <Button
                            color="primary"
                            variant="flat"
                            onPress={onUpdateStatus}
                        >
                            Actualizar Estado
                        </Button>
                    )}
                    {onPrint && (
                        <Button
                            color="primary"
                            startContent={<PrinterIcon className="w-4 h-4" />}
                            onPress={onPrint}
                        >
                            Imprimir
                        </Button>
                    )}
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
}
