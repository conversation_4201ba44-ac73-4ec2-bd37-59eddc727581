import React from "react";
import {
    ClockIcon,
    CheckCircleIcon,
    TruckIcon,
    DocumentTextIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { motion } from "framer-motion";

import { Card, CardBody, Spinner } from "@/shared/components/ui/hero-ui-client";

interface ContractorHistorySectionProps {
    contractorId: string;
}

// Mock de datos de historial (en producción vendría de la API)
const mockHistory = [
    {
        id: "1",
        type: "assignment_created",
        title: "Nueva asignación",
        description: "Se asignó la orden #2024-001",
        date: new Date(2024, 0, 15),
        icon: DocumentTextIcon,
        color: "text-blue-600",
    },
    {
        id: "2",
        type: "remission_created",
        title: "Remisión creada",
        description: "Remisión R-001 con 50 piezas",
        date: new Date(2024, 0, 20),
        icon: TruckIcon,
        color: "text-green-600",
    },
    {
        id: "3",
        type: "assignment_completed",
        title: "Asignación completada",
        description: "Completó la orden #2024-001",
        date: new Date(2024, 0, 25),
        icon: CheckCircleIcon,
        color: "text-purple-600",
    },
];

export default function ContractorHistorySection({
    contractorId,
}: ContractorHistorySectionProps) {
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, x: -20 },
        visible: {
            opacity: 1,
            x: 0,
            transition: {
                duration: 0.3,
            },
        },
    };

    // En producción, aquí se haría una llamada a la API
    const isLoading = false;
    const history = mockHistory;

    if (isLoading) {
        return (
            <div className="flex flex-col items-center justify-center py-20">
                <Spinner color="primary" size="lg" />
                <p className="mt-4 text-gray-500 dark:text-gray-400">
                    Cargando historial...
                </p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <Card>
                <CardBody>
                    <h3 className="text-lg font-semibold flex items-center gap-2 mb-6">
                        <ClockIcon className="w-5 h-5 text-primary" />
                        Historial de Actividad
                    </h3>

                    <motion.div
                        animate="visible"
                        className="relative"
                        initial="hidden"
                        variants={containerVariants}
                    >
                        {/* Línea de tiempo */}
                        <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700" />

                        {/* Eventos */}
                        <div className="space-y-6">
                            {history.map((event, index) => {
                                const Icon = event.icon;

                                return (
                                    <motion.div
                                        key={event.id}
                                        className="relative flex items-start gap-4"
                                        variants={itemVariants}
                                    >
                                        {/* Icono del evento */}
                                        <div
                                            className={`relative z-10 flex items-center justify-center w-10 h-10 bg-white dark:bg-gray-800 rounded-full border-2 border-gray-200 dark:border-gray-700 ${event.color}`}
                                        >
                                            <Icon className="w-5 h-5" />
                                        </div>

                                        {/* Contenido del evento */}
                                        <div className="flex-1 pb-6">
                                            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                                                <div className="flex items-start justify-between">
                                                    <div>
                                                        <h4 className="font-medium text-gray-900 dark:text-white">
                                                            {event.title}
                                                        </h4>
                                                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                            {event.description}
                                                        </p>
                                                    </div>
                                                    <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-4">
                                                        {format(
                                                            event.date,
                                                            "dd MMM yyyy",
                                                            { locale: es },
                                                        )}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </motion.div>
                                );
                            })}
                        </div>
                    </motion.div>

                    {history.length === 0 && (
                        <div className="text-center py-10">
                            <ClockIcon className="w-12 h-12 mx-auto text-gray-300 dark:text-gray-600 mb-3" />
                            <p className="text-gray-500 dark:text-gray-400">
                                No hay historial disponible
                            </p>
                        </div>
                    )}
                </CardBody>
            </Card>

            {/* Nota informativa */}
            <Card className="bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800">
                <CardBody className="text-sm text-blue-700 dark:text-blue-300">
                    <p className="flex items-start gap-2">
                        <span className="font-medium">Nota:</span>
                        El historial muestra las actividades más recientes del
                        contratista, incluyendo asignaciones, remisiones y
                        cambios de estado.
                    </p>
                </CardBody>
            </Card>
        </div>
    );
}
