// Este archivo se usa para entornos Edge Runtime (middleware)
// NO inicializa PrismaClient para evitar errores en el Edge Runtime

// Exportar funciones de ayuda que sean seguras para Edge
export function isPrismaError(error: unknown): boolean {
    return (
        error instanceof Error &&
        (error.name === "PrismaClientKnownRequestError" ||
            error.name === "PrismaClientUnknownRequestError" ||
            error.name === "PrismaClientRustPanicError" ||
            error.name === "PrismaClientInitializationError" ||
            error.name === "PrismaClientValidationError")
    );
}

// Helper simple para verificar manejo de credenciales
export function isAuthError(error: unknown): boolean {
    if (error instanceof Error) {
        return (
            error.message.includes("authentication") ||
            error.message.includes("credentials") ||
            error.message.includes("unauthorized")
        );
    }

    return false;
}

// Exportar un objeto vacío como reemplazo de PrismaClient para Edge
export const prisma = {};

export default prisma;
