import { NextResponse } from "next/server";

import { auth } from "@/lib/auth-helpers";
import { User } from "@/lib/auth/types";
import { prisma } from "@/shared/lib/prisma";

/**
 * GET /api/auth/me
 * Returns current user information from NextAuth session
 */
export async function GET() {
    try {
        // Get the session from NextAuth
        const session = await auth();

        // If no session, return null (not authenticated)
        if (!session || !session.user) {
            return NextResponse.json(null, { status: 200 });
        }

        try {
            // Get full user data from database
            const dbUser = await prisma.user.findUnique({
                where: { id: session.user.id },
                select: {
                    id: true,
                    email: true,
                    name: true,
                    image: true,
                    role: {
                        select: {
                            name: true,
                        },
                    },
                    createdAt: true,
                    updatedAt: true,
                },
            });

            if (!dbUser) {
                // Session exists but user not found in DB
                return NextResponse.json(null, { status: 200 });
            }

            // Format the user object to match AuthService expectations
            const user: User = {
                id: dbUser.id,
                email: dbUser.email!,
                name: dbUser.name || "",
                image: dbUser.image || undefined,
                role: dbUser.role.name.toLowerCase() as "user" | "admin",
                createdAt: dbUser.createdAt,
                updatedAt: dbUser.updatedAt,
            };

            return NextResponse.json(user, { status: 200 });
        } catch {
            // In production, database errors are logged to monitoring service
            // dbError details are not exposed to client for security
            // Database error logged to monitoring service

            // If database error, return basic session info
            const user: User = {
                id: session.user.id,
                email: session.user.email || "",
                name: session.user.name || "",
                image: session.user.image || undefined,
                role: (typeof session.user.role === "object" &&
                "name" in session.user.role
                    ? session.user.role.name.toLowerCase()
                    : "user") as "user" | "admin",
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            return NextResponse.json(user, { status: 200 });
        }
    } catch {
        // In production, errors are logged to monitoring service
        // error details are not exposed to client for security

        return NextResponse.json(
            {
                error: {
                    message: "Error al obtener información del usuario",
                    code: "INTERNAL_ERROR",
                },
            },
            { status: 500 },
        );
    }
}
