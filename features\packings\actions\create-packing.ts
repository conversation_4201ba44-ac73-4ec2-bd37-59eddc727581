"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";

import { generatePackingFolio } from "./generate-folio";

const createPackingSchema = z.object({
    customerId: z.string().min(1, "El cliente es requerido"),
    subCustomerId: z.string().optional(),
    orderId: z.string().optional(),
    deliveryDate: z.date(),
    notes: z.string().optional(),
    details: z
        .array(
            z.object({
                garmentSizeId: z.string(),
                quantity: z.number().int().positive(),
                comments: z.string().optional(),
            }),
        )
        .min(1, "Debe incluir al menos un artículo"),
});

export async function createPacking(data: z.infer<typeof createPackingSchema>) {
    try {
        const validatedData = createPackingSchema.parse(data);

        // Verificar que el cliente existe
        const customer = await db.customer.findUnique({
            where: { id: validatedData.customerId },
        });

        if (!customer) {
            return { success: false, error: "Cliente no encontrado" };
        }

        // Verificar subcliente si se proporciona
        if (validatedData.subCustomerId) {
            const subCustomer = await db.customer.findUnique({
                where: {
                    id: validatedData.subCustomerId,
                    parentId: validatedData.customerId,
                },
            });

            if (!subCustomer) {
                return { success: false, error: "Subcliente no válido" };
            }
        }

        // Generar folio único
        const folio = await generatePackingFolio();

        // Obtener el estado inicial (por ejemplo "Pendiente")
        const initialStatus = await db.packingStatus.findFirst({
            where: { name: "Pendiente" },
        });

        if (!initialStatus) {
            return {
                success: false,
                error: "Estado inicial no configurado en el sistema",
            };
        }

        // Crear el packing con sus detalles
        const packing = await db.packing.create({
            data: {
                folio,
                code: folio, // Por ahora usamos el mismo folio como código
                customerId: validatedData.customerId,
                subCustomerId: validatedData.subCustomerId,
                orderId: validatedData.orderId,
                deliveryDate: validatedData.deliveryDate,
                notes: validatedData.notes,
                statusId: initialStatus.id,
                details: {
                    create: validatedData.details,
                },
                history: {
                    create: {
                        action: "CREATED",
                        metadata: {
                            createdBy: "system", // TODO: Obtener usuario actual
                            details: validatedData.details.length,
                        },
                    },
                },
            },
            include: {
                customer: true,
                subCustomer: true,
                order: true,
                status: true,
                details: {
                    include: {
                        garmentSize: {
                            include: {
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                                size: true,
                            },
                        },
                    },
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(
            `/dashboard/customers/${validatedData.customerId}/details`,
        );

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error creating packing:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return { success: false, error: "Error al crear el packing" };
    }
}
