"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import {
    Card,
    CardHeader,
    CardBody,
    CardFooter,
    Button,
    Textarea,
    Divider,
    Spinner,
    Chip,
    Autocomplete,
    AutocompleteItem,
    addToast,
    Tooltip,
    Progress,
    RadioGroup,
    Radio,
} from "@heroui/react";
import {
    ArrowLeftIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    PencilSquareIcon,
    ClipboardDocumentIcon,
    TagIcon,
    ClipboardDocumentListIcon,
    FlagIcon,
    LightBulbIcon,
    UserCircleIcon,
    CalendarIcon,
    ClockIcon,
    PaperAirplaneIcon,
    XMarkIcon,
    KeyIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { useUpdateNote, useGetNote } from "@/features/notes/hooks/useNotes";
import { getOrders } from "@/features/orders/actions";
import { getNoteStatuses, getNoteImportances } from "@/features/notes/actions";

// Interfaces para las opciones de selección
interface OrderOption {
    id: string;
    label: string;
    transferNumber?: string | null;
    cutOrder?: string | null;
}

interface NoteStatus {
    id: string;
    name: string;
    iconName?: string | null;
    color?: string | null;
}

interface NoteImportanceOption {
    id: string;
    name: string;
    iconName?: string | null;
    color?: string | null;
}

export default function EditNotePage() {
    const router = useRouter();
    const params = useParams();
    const noteId = params.id as string;

    const {
        updateNote,
        isLoading: isUpdating,
        error: updateError,
    } = useUpdateNote();
    const {
        note,
        isLoading: isLoadingNote,
        error: noteError,
    } = useGetNote(noteId);

    // Estados para las opciones de selección
    const [orders, setOrders] = useState<OrderOption[]>([]);
    const [noteStatuses, setNoteStatuses] = useState<NoteStatus[]>([]);
    const [noteImportances, setNoteImportances] = useState<
        NoteImportanceOption[]
    >([]);
    const [isLoadingOptions, setIsLoadingOptions] = useState(true);
    const [showTip, setShowTip] = useState(true);
    const [formProgress, setFormProgress] = useState(0);
    const [showPreview, setShowPreview] = useState(true);
    const [currentTime, setCurrentTime] = useState(new Date());

    // Estado del formulario
    const [formData, setFormData] = useState({
        id: noteId,
        content: "",
        orderId: "",
        statusId: "",
        importanceId: "",
    });

    // Estado para errores del formulario
    const [formErrors, setFormErrors] = useState({
        orderId: "",
        statusId: "",
        importanceId: "",
        content: "",
    });

    // Actualizar la fecha actual cada minuto para la vista previa
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 60000);

        return () => clearInterval(timer);
    }, []);

    // Cargar datos de la nota cuando se carga la página
    useEffect(() => {
        if (note) {
            setFormData({
                id: note.id,
                content: note.content,
                orderId: note.orderId,
                statusId: note.statusId,
                importanceId: note.importanceId,
            });
        }
    }, [note]);

    // Cargar datos iniciales usando Server Actions
    useEffect(() => {
        const loadInitialData = async () => {
            try {
                setIsLoadingOptions(true);

                // Obtener órdenes usando Server Action
                const ordersResult = await getOrders();

                if (ordersResult.success && ordersResult.data) {
                    setOrders(
                        ordersResult.data.orders.map((order: any) => ({
                            id: order.id,
                            label: `${order.cutOrder || "Sin código"} - ${order.transferNumber || "Sin transferencia"}`,
                            transferNumber: order.transferNumber,
                            cutOrder: order.cutOrder,
                        })),
                    );
                }

                // Obtener estados de notas usando Server Action
                const statusesResult = await getNoteStatuses();

                if (statusesResult.success && statusesResult.data) {
                    setNoteStatuses(statusesResult.data);
                }

                // Obtener importancias de notas usando Server Action
                const importancesResult = await getNoteImportances();

                if (importancesResult.success && importancesResult.data) {
                    setNoteImportances(importancesResult.data);
                }
            } catch (error) {
                // REMOVED: console.error("Error cargando datos iniciales:", error);
                addToast({
                    title: "Error",
                    description:
                        "Error cargando datos. Por favor, intenta de nuevo.",
                    color: "danger",
                    icon: <ExclamationTriangleIcon className="w-5 h-5" />,
                });
            } finally {
                setIsLoadingOptions(false);
            }
        };

        loadInitialData();
    }, []);

    // Calcular el progreso del formulario
    useEffect(() => {
        let progress = 0;
        const totalFields = 4; // Total de campos en el formulario

        if (formData.orderId) progress++;
        if (formData.statusId) progress++;
        if (formData.importanceId) progress++;
        if (formData.content.trim().length > 0) progress++;

        setFormProgress(Math.round((progress / totalFields) * 100));
    }, [formData]);

    // Buscar la información de importancia seleccionada
    const selectedImportance = noteImportances.find(
        (imp) => imp.id === formData.importanceId,
    );

    // Buscar la información de estado seleccionado
    const selectedStatus = noteStatuses.find(
        (status) => status.id === formData.statusId,
    );

    // Buscar la orden seleccionada
    const selectedOrder = orders.find((order) => order.id === formData.orderId);

    // Manejar cambios en el formulario
    const handleChange = (field: string, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        // Limpiar error cuando se actualiza el campo
        setFormErrors((prev) => ({ ...prev, [field]: "" }));
    };

    // Manejar atajo de teclado Ctrl+Enter para enviar
    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent<HTMLTextAreaElement | HTMLElement>) => {
            if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
                e.preventDefault();
                const form = e.currentTarget.closest("form");

                if (form) {
                    form.requestSubmit();
                }
            }
        },
        [],
    );

    // Manejar envío del formulario
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Resetear errores
        const errors = {
            orderId: "",
            statusId: "",
            importanceId: "",
            content: "",
        };

        let hasErrors = false;

        // Validaciones básicas
        if (!formData.content.trim()) {
            errors.content = "El contenido de la nota es obligatorio";
            hasErrors = true;
        }

        if (!formData.orderId) {
            errors.orderId = "Debes seleccionar una orden";
            hasErrors = true;
        }

        if (!formData.statusId) {
            errors.statusId = "Debes seleccionar un estado";
            hasErrors = true;
        }

        if (!formData.importanceId) {
            errors.importanceId = "Debes seleccionar una importancia";
            hasErrors = true;
        }

        if (hasErrors) {
            setFormErrors(errors);
            addToast({
                title: "Campos incompletos",
                description: "Por favor, completa todos los campos requeridos",
                color: "danger",
                icon: <ExclamationTriangleIcon className="w-5 h-5" />,
            });

            return;
        }

        // Enviar datos con el hook que ya utiliza la Server Action
        const result = await updateNote({
            id: formData.id,
            content: formData.content,
            orderId: formData.orderId,
            statusId: formData.statusId,
            importanceId: formData.importanceId,
        });

        if (result.success) {
            addToast({
                title: "¡Nota actualizada!",
                description: "La nota ha sido actualizada con éxito",
                color: "success",
                icon: <CheckCircleIcon className="w-5 h-5" />,
            });

            // Mostrar animación de éxito antes de redirigir
            setTimeout(() => {
                router.push("/dashboard/notes");
            }, 1500);
        }
    };

    // Componente de sugerencia de contenido
    const ContentSuggestion = () => {
        return (
            <AnimatePresence>
                {showTip && (
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg text-blue-700 dark:text-blue-300 text-sm"
                        exit={{ opacity: 0, y: -10 }}
                        initial={{ opacity: 0, y: 10 }}
                    >
                        <div className="flex justify-between items-start">
                            <div className="flex gap-2">
                                <LightBulbIcon className="w-5 h-5 flex-shrink-0 text-blue-500" />
                                <div>
                                    <p className="font-medium">
                                        Consejo para notas efectivas:
                                    </p>
                                    <p className="mt-1">
                                        Incluye información específica, acciones
                                        requeridas y plazos de tiempo cuando sea
                                        relevante.
                                    </p>
                                </div>
                            </div>
                            <button
                                className="text-blue-500 hover:text-blue-700 dark:hover:text-blue-300"
                                onClick={() => setShowTip(false)}
                            >
                                <XMarkIcon className="w-5 h-5" />
                            </button>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        );
    };

    // Componente de vista previa de la nota
    const NotePreview = () => {
        const importanceColor = selectedImportance?.color || "#888888";
        const statusColor = selectedStatus?.color || "#888888";
        const importanceName = selectedImportance?.name || "Sin importancia";
        const statusName = selectedStatus?.name || "Sin estado";
        const orderCode = selectedOrder?.cutOrder || "Sin código";

        return (
            <div className="h-full">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
                        <ClipboardDocumentListIcon className="w-5 h-5 text-primary" />
                        Vista previa
                    </h3>
                    <Button
                        isIconOnly
                        className="text-gray-500"
                        size="sm"
                        variant="light"
                        onPress={() => setShowPreview(!showPreview)}
                    >
                        {showPreview ? (
                            <XMarkIcon className="w-4 h-4" />
                        ) : (
                            <ClipboardDocumentIcon className="w-4 h-4" />
                        )}
                    </Button>
                </div>

                <AnimatePresence>
                    {showPreview && (
                        <motion.div
                            animate={{ opacity: 1, scale: 1 }}
                            className="relative p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg overflow-hidden"
                            exit={{ opacity: 0, scale: 0.95 }}
                            initial={{ opacity: 0, scale: 0.95 }}
                            style={{
                                boxShadow: `0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 2px ${importanceColor}20`,
                                borderLeft: `4px solid ${importanceColor}`,
                            }}
                            transition={{ duration: 0.2 }}
                        >
                            {/* Banda superior con información de metadata */}
                            <div className="mb-3 flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Chip
                                        className="font-medium"
                                        color="default"
                                        size="sm"
                                        startContent={
                                            <CalendarIcon className="w-3 h-3" />
                                        }
                                    >
                                        {format(currentTime, "dd MMM yyyy", {
                                            locale: es,
                                        })}
                                    </Chip>
                                    <Chip
                                        className="font-medium"
                                        color="default"
                                        size="sm"
                                        startContent={
                                            <ClockIcon className="w-3 h-3" />
                                        }
                                    >
                                        {format(currentTime, "HH:mm", {
                                            locale: es,
                                        })}
                                    </Chip>
                                </div>
                                <Tooltip content={`Orden: ${orderCode}`}>
                                    <Chip
                                        className="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 font-medium"
                                        size="sm"
                                        variant="flat"
                                    >
                                        {orderCode}
                                    </Chip>
                                </Tooltip>
                            </div>

                            {/* Contenido de la nota */}
                            <div className="min-h-[200px] mb-4">
                                <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                                    {formData.content || (
                                        <span className="text-gray-400 italic">
                                            El contenido de la nota aparecerá
                                            aquí...
                                        </span>
                                    )}
                                </p>
                            </div>

                            {/* Pie de nota con estado e importancia */}
                            <div className="mt-4 flex flex-wrap items-center justify-between gap-2 border-t border-gray-100 dark:border-gray-700 pt-3">
                                <div className="flex items-center gap-2">
                                    <Tooltip content={"Estado de la nota"}>
                                        <Chip
                                            className="font-medium"
                                            size="sm"
                                            startContent={
                                                <div
                                                    className="w-2 h-2 rounded-full"
                                                    style={{
                                                        backgroundColor:
                                                            statusColor,
                                                    }}
                                                />
                                            }
                                            variant="flat"
                                        >
                                            {statusName}
                                        </Chip>
                                    </Tooltip>

                                    <Tooltip content={"Nivel de importancia"}>
                                        <Chip
                                            className="font-medium"
                                            size="sm"
                                            startContent={
                                                <div
                                                    className="w-2 h-2 rounded-full"
                                                    style={{
                                                        backgroundColor:
                                                            importanceColor,
                                                    }}
                                                />
                                            }
                                            style={{
                                                backgroundColor: `${importanceColor}20`,
                                                color: importanceColor,
                                            }}
                                            variant="flat"
                                        >
                                            {importanceName}
                                        </Chip>
                                    </Tooltip>
                                </div>

                                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                                    <UserCircleIcon className="w-4 h-4" />
                                    <span>Tú</span>
                                </div>
                            </div>

                            {/* Marca de agua diagonal cuando está vacío */}
                            {!formData.content && (
                                <div className="absolute inset-0 flex items-center justify-center opacity-10 pointer-events-none">
                                    <div className="rotate-[-35deg] border-8 border-dashed border-gray-400 rounded-lg p-4">
                                        <span className="text-3xl font-bold text-gray-400">
                                            VISTA PREVIA
                                        </span>
                                    </div>
                                </div>
                            )}
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        );
    };

    // Renderizar vista de carga mientras se obtienen los datos de la nota o las opciones
    if (isLoadingNote || isLoadingOptions) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[70vh]">
                <motion.div
                    animate={{ opacity: 1 }}
                    className="flex flex-col items-center"
                    initial={{ opacity: 0 }}
                >
                    <Spinner className="mb-4" color="primary" size="lg" />
                    <h2 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {isLoadingNote
                            ? "Cargando nota..."
                            : "Cargando formulario"}
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-4 text-center max-w-md">
                        Preparando todo para que puedas editar esta nota...
                    </p>
                    <div className="w-64 mt-2">
                        <Progress
                            isIndeterminate
                            aria-label="Cargando..."
                            className="max-w-md"
                            color="primary"
                            size="sm"
                        />
                    </div>
                </motion.div>
            </div>
        );
    }

    // Renderizar error si no se puede cargar la nota
    if (noteError) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[70vh]">
                <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 p-6 rounded-lg max-w-lg text-center">
                    <ExclamationTriangleIcon className="w-12 h-12 mx-auto mb-4 text-red-500" />
                    <h2 className="text-xl font-medium mb-2">
                        Error al cargar la nota
                    </h2>
                    <p className="mb-4">{noteError}</p>
                    <Button
                        color="primary"
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        onPress={() => router.push("/dashboard/notes")}
                    >
                        Volver a Notas
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-6 max-w-7xl">
            <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5 }}
            >
                {/* Encabezado con navegación y progreso */}
                <div className="flex flex-col gap-4">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                        <motion.div
                            animate={{ opacity: 1, x: 0 }}
                            initial={{ opacity: 0, x: -20 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                        >
                            <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center gap-2">
                                <PencilSquareIcon className="w-8 h-8 text-primary" />
                                Editar Nota
                            </h1>
                            <p className="text-gray-600 dark:text-gray-300 mt-1">
                                Actualiza la información de esta nota
                            </p>
                        </motion.div>

                        <motion.div
                            animate={{ opacity: 1, x: 0 }}
                            className="flex items-center gap-3"
                            initial={{ opacity: 0, x: 20 }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                        >
                            <Tooltip
                                color="primary"
                                content="Ctrl+Enter para guardar"
                            >
                                <Button
                                    className="text-gray-700 dark:text-gray-300 hidden sm:flex items-center gap-1.5 text-sm"
                                    startContent={
                                        <KeyIcon className="w-3.5 h-3.5" />
                                    }
                                    variant="light"
                                >
                                    <span className="text-xs">Ctrl+Enter</span>
                                </Button>
                            </Tooltip>

                            <Button
                                className="font-medium"
                                startContent={
                                    <ArrowLeftIcon className="w-4 h-4" />
                                }
                                variant="light"
                                onPress={() => router.push("/dashboard/notes")}
                            >
                                Volver a Notas
                            </Button>
                        </motion.div>
                    </div>

                    {/* Barra de progreso */}
                    <div className="w-full flex flex-col gap-1">
                        <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 px-1">
                            <span>Avance del formulario</span>
                            <span>{formProgress}%</span>
                        </div>
                        <Progress
                            className="max-w-full"
                            color={
                                formProgress < 25
                                    ? "danger"
                                    : formProgress < 75
                                      ? "warning"
                                      : "success"
                            }
                            showValueLabel={false}
                            size="sm"
                            value={formProgress}
                        />
                    </div>
                </div>

                {/* Contenido principal - Layout de dos columnas */}
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                    {/* Columna del formulario */}
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className="lg:col-span-7"
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                    >
                        <form onSubmit={handleSubmit}>
                            <Card
                                className="bg-white dark:bg-gray-800/50 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 dark:border-gray-700"
                                radius="lg"
                            >
                                <CardHeader className="flex flex-col gap-2 pb-6 bg-gray-50 dark:bg-gray-800/50 rounded-t-xl">
                                    <div className="flex items-center gap-2 text-xl font-semibold text-gray-800 dark:text-white">
                                        <ClipboardDocumentIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                        <span>Información de la Nota</span>
                                    </div>
                                    <p className="text-sm text-gray-600 dark:text-gray-300">
                                        Completa los campos a continuación para
                                        crear una nueva nota
                                    </p>
                                </CardHeader>

                                <CardBody className="gap-8 py-6">
                                    {/* Selector de orden */}
                                    <div className="space-y-5">
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-2">
                                                <ClipboardDocumentIcon className="w-5 h-5 text-gray-500" />
                                                <h3 className="text-base font-medium text-gray-700 dark:text-gray-300">
                                                    1. Selecciona una orden
                                                </h3>
                                            </div>

                                            <Autocomplete
                                                isRequired
                                                classNames={{
                                                    base: "w-full",
                                                    popoverContent:
                                                        "bg-white dark:bg-gray-900/40 border-gray-200 dark:border-gray-700",
                                                }}
                                                defaultItems={orders}
                                                inputProps={{
                                                    classNames: {
                                                        input: "text-sm",
                                                    },
                                                }}
                                                label="Orden asociada"
                                                labelPlacement="outside"
                                                placeholder="Buscar por código o número de transferencia..."
                                                selectedKey={formData.orderId}
                                                startContent={
                                                    <ClipboardDocumentIcon className="w-5 h-5 text-gray-500" />
                                                }
                                                variant="bordered"
                                                onSelectionChange={(key) =>
                                                    handleChange(
                                                        "orderId",
                                                        key as string,
                                                    )
                                                }
                                            >
                                                {(order) => (
                                                    <AutocompleteItem
                                                        key={order.id}
                                                        textValue={order.label}
                                                    >
                                                        <div className="flex flex-col">
                                                            <span className="text-base font-medium">
                                                                {order.cutOrder ||
                                                                    "Sin código"}
                                                            </span>
                                                            <span className="text-xs text-gray-500">
                                                                Transferencia:{" "}
                                                                {order.transferNumber ||
                                                                    "No disponible"}
                                                            </span>
                                                        </div>
                                                    </AutocompleteItem>
                                                )}
                                            </Autocomplete>
                                            {formErrors.orderId && (
                                                <p className="text-red-500 text-xs mt-1">
                                                    {formErrors.orderId}
                                                </p>
                                            )}
                                        </div>

                                        {/* Selectores de estado e importancia mejorados */}
                                        <div className="grid grid-cols-1 gap-8 mt-6">
                                            {/* Estado de la nota */}
                                            <div className="space-y-4">
                                                <div className="flex items-center gap-2">
                                                    <TagIcon className="w-5 h-5 text-gray-500" />
                                                    <h3 className="text-base font-medium text-gray-700 dark:text-gray-300">
                                                        2. Estado de la nota
                                                    </h3>
                                                </div>

                                                <div>
                                                    <RadioGroup
                                                        className="flex flex-wrap gap-3"
                                                        orientation="horizontal"
                                                        value={
                                                            formData.statusId
                                                        }
                                                        onValueChange={(
                                                            value,
                                                        ) =>
                                                            handleChange(
                                                                "statusId",
                                                                value,
                                                            )
                                                        }
                                                    >
                                                        {noteStatuses.map(
                                                            (status) => (
                                                                <Radio
                                                                    key={
                                                                        status.id
                                                                    }
                                                                    className="m-0"
                                                                    classNames={{
                                                                        base: `group hover:opacity-90 transition-opacity ${
                                                                            formData.statusId ===
                                                                            status.id
                                                                                ? "border-2 border-primary shadow-md"
                                                                                : "border border-gray-200 dark:border-gray-700"
                                                                        } bg-white dark:bg-gray-800 px-4 py-3 rounded-xl cursor-pointer min-w-[130px] w-auto`,
                                                                        wrapper:
                                                                            "mr-0",
                                                                    }}
                                                                    style={{
                                                                        borderColor:
                                                                            formData.statusId ===
                                                                            status.id
                                                                                ? status.color ||
                                                                                  "#888"
                                                                                : undefined,
                                                                        boxShadow:
                                                                            formData.statusId ===
                                                                            status.id
                                                                                ? `0 0 0 3px ${status.color || "#888"}33`
                                                                                : undefined,
                                                                    }}
                                                                    value={
                                                                        status.id
                                                                    }
                                                                >
                                                                    <div className="flex items-center gap-3">
                                                                        <div
                                                                            className="w-5 h-5 rounded-full"
                                                                            style={{
                                                                                backgroundColor:
                                                                                    status.color ||
                                                                                    "#888",
                                                                            }}
                                                                        />
                                                                        <span className="font-medium">
                                                                            {
                                                                                status.name
                                                                            }
                                                                        </span>
                                                                    </div>
                                                                </Radio>
                                                            ),
                                                        )}
                                                    </RadioGroup>
                                                    {formErrors.statusId && (
                                                        <p className="text-red-500 text-xs mt-2">
                                                            {
                                                                formErrors.statusId
                                                            }
                                                        </p>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Nivel de importancia */}
                                            <div className="space-y-4">
                                                <div className="flex items-center gap-2">
                                                    <FlagIcon className="w-5 h-5 text-gray-500" />
                                                    <h3 className="text-base font-medium text-gray-700 dark:text-gray-300">
                                                        3. Nivel de importancia
                                                    </h3>
                                                </div>

                                                <div>
                                                    <RadioGroup
                                                        className="flex flex-wrap gap-3"
                                                        orientation="horizontal"
                                                        value={
                                                            formData.importanceId
                                                        }
                                                        onValueChange={(
                                                            value,
                                                        ) =>
                                                            handleChange(
                                                                "importanceId",
                                                                value,
                                                            )
                                                        }
                                                    >
                                                        {noteImportances.map(
                                                            (importance) => (
                                                                <Radio
                                                                    key={
                                                                        importance.id
                                                                    }
                                                                    className="m-0"
                                                                    classNames={{
                                                                        base: `group hover:opacity-90 transition-opacity ${
                                                                            formData.importanceId ===
                                                                            importance.id
                                                                                ? "border-2 border-primary shadow-md"
                                                                                : "border border-gray-200 dark:border-gray-700"
                                                                        } bg-white dark:bg-gray-800 px-4 py-3 rounded-xl cursor-pointer min-w-[130px] w-auto`,
                                                                        wrapper:
                                                                            "mr-0",
                                                                    }}
                                                                    style={{
                                                                        borderColor:
                                                                            formData.importanceId ===
                                                                            importance.id
                                                                                ? importance.color ||
                                                                                  "#888"
                                                                                : undefined,
                                                                        boxShadow:
                                                                            formData.importanceId ===
                                                                            importance.id
                                                                                ? `0 0 0 3px ${importance.color || "#888"}33`
                                                                                : undefined,
                                                                    }}
                                                                    value={
                                                                        importance.id
                                                                    }
                                                                >
                                                                    <div className="flex items-center gap-3">
                                                                        <div
                                                                            className="w-5 h-5 rounded-full"
                                                                            style={{
                                                                                backgroundColor:
                                                                                    importance.color ||
                                                                                    "#888",
                                                                            }}
                                                                        />
                                                                        <span className="font-medium">
                                                                            {
                                                                                importance.name
                                                                            }
                                                                        </span>
                                                                    </div>
                                                                </Radio>
                                                            ),
                                                        )}
                                                    </RadioGroup>
                                                    {formErrors.importanceId && (
                                                        <p className="text-red-500 text-xs mt-2">
                                                            {
                                                                formErrors.importanceId
                                                            }
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Contenido de la Nota */}
                                    <div className="mt-6 space-y-3">
                                        <div className="flex items-center gap-2">
                                            <PencilSquareIcon className="w-5 h-5 text-gray-500" />
                                            <h3 className="text-base font-medium text-gray-700 dark:text-gray-300">
                                                4. Contenido de la nota
                                            </h3>
                                        </div>

                                        <Textarea
                                            isRequired
                                            classNames={{
                                                base: "w-full",
                                                input: "resize-y min-h-[200px] bg-white dark:bg-gray-900/40 border-gray-200 dark:border-gray-700",
                                                label: "font-medium text-gray-700 dark:text-gray-300",
                                            }}
                                            description="Puedes usar Ctrl+Enter para guardar rápidamente"
                                            label="Contenido detallado"
                                            labelPlacement="outside"
                                            maxRows={12}
                                            minRows={7}
                                            placeholder="Escribe aquí el contenido de la nota..."
                                            value={formData.content}
                                            variant="bordered"
                                            onChange={(e) =>
                                                handleChange(
                                                    "content",
                                                    e.target.value,
                                                )
                                            }
                                            onKeyDown={handleKeyDown}
                                        />
                                        {formErrors.content && (
                                            <p className="text-red-500 text-xs mt-1">
                                                {formErrors.content}
                                            </p>
                                        )}

                                        <ContentSuggestion />

                                        <div className="flex justify-end text-xs text-gray-500">
                                            <span>
                                                {formData.content.length}{" "}
                                                caracteres
                                            </span>
                                        </div>
                                    </div>

                                    {/* Mensaje de error */}
                                    {updateError && (
                                        <motion.div
                                            animate={{ opacity: 1, y: 0 }}
                                            className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 flex items-center gap-2"
                                            initial={{ opacity: 0, y: 10 }}
                                        >
                                            <ExclamationTriangleIcon className="w-5 h-5 flex-shrink-0" />
                                            <p>{updateError}</p>
                                        </motion.div>
                                    )}
                                </CardBody>

                                <Divider />

                                <CardFooter className="flex justify-between items-center px-6 py-4 bg-gray-50 dark:bg-gray-800/30 rounded-b-xl">
                                    <Button
                                        className="font-medium"
                                        color="danger"
                                        variant="flat"
                                        onPress={() =>
                                            router.push("/dashboard/notes")
                                        }
                                    >
                                        Cancelar
                                    </Button>

                                    <Button
                                        className="bg-primary font-medium shadow-md hover:shadow-lg transition-shadow"
                                        color="primary"
                                        isDisabled={formProgress < 100}
                                        isLoading={isUpdating}
                                        size="lg"
                                        spinner={
                                            <div className="flex items-center gap-1">
                                                <Spinner
                                                    color="white"
                                                    size="sm"
                                                />
                                                <span>Guardando...</span>
                                            </div>
                                        }
                                        startContent={
                                            !isUpdating && (
                                                <PaperAirplaneIcon className="w-4 h-4" />
                                            )
                                        }
                                        type="submit"
                                    >
                                        {isUpdating
                                            ? "Guardando..."
                                            : "Guardar Nota"}
                                    </Button>
                                </CardFooter>
                            </Card>
                        </form>
                    </motion.div>

                    {/* Columna de vista previa */}
                    <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className="lg:col-span-5"
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                    >
                        <Card
                            className="bg-white dark:bg-gray-800/50 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 dark:border-gray-700 h-full"
                            radius="lg"
                        >
                            <CardBody className="p-6">
                                <NotePreview />
                            </CardBody>
                        </Card>
                    </motion.div>
                </div>
            </motion.div>
        </div>
    );
}
