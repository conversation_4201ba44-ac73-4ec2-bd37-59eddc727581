# 📊 Datos Iniciales de LOHARI

Este documento explica cómo insertar y gestionar los datos iniciales necesarios para el sistema LOHARI.

## 🎯 Tablas que requieren datos iniciales

1. **Role** - Roles de usuario (ADMIN, EMPLOYEE, CONTRACTOR, GUEST)
2. **OrderStatus** - Estados de las órdenes (RECEIVED, IN_PRODUCTION, etc.)
3. **NoteStatus** - Estados de las notas (PENDING, IN_PROGRESS, COMPLETED, CANCELLED)
4. **NoteImportance** - Niveles de importancia (Bajo, Medio, Alto)
5. **PackingStatus** - Estados del empaque (IN_PROGRESS, DELIVERED, REJECTED, CANCELLED)
6. **RejectionReason** - Razones de rechazo (QUALITY, DEFECT, DELAY, OTHER)

## 🚀 Métodos para insertar datos iniciales

### Método 1: Usando Prisma Seed (Recomendado)

```bash
# En Windows
npm run db:seed

# O directamente con npx
npx prisma db seed
```

### Método 2: Usando los scripts

```bash
# En Windows
.\scripts\run-seeds.bat

# En Linux/Mac
./scripts/run-seeds.sh
```

### Método 3: SQL Directo

Si prefieres ejecutar SQL directamente, usa el archivo:
```
prisma/seed-initial-data.sql
```

Puedes ejecutarlo en tu cliente PostgreSQL favorito o via CLI:
```bash
psql -U tu_usuario -d tu_base_de_datos -f prisma/seed-initial-data.sql
```

## 🔄 Resetear la base de datos con datos iniciales

Si necesitas resetear completamente tu base de datos:

```bash
# Esto borrará todos los datos y volverá a ejecutar las migraciones + seeds
npm run db:reset
```

## 📝 Estructura de datos

### Role
- **ADMIN**: Administrador del sistema (morado)
- **EMPLOYEE**: Empleado regular (cyan)
- **CONTRACTOR**: Contratista externo (verde lima)
- **GUEST**: Invitado con acceso limitado (gris)

### OrderStatus
- **RECEIVED**: Orden recibida (azul)
- **IN_PRODUCTION**: En producción (ámbar)
- **QUALITY_CHECK**: Control de calidad (violeta)
- **PACKING**: Empaquetando (teal)
- **READY_TO_DELIVER**: Listo para entregar (esmeralda)
- **DELIVERED**: Entregado (verde)
- **REJECTED**: Rechazado (rojo)
- **CANCELLED**: Cancelado (gris)

### NoteStatus
- **PENDING**: Pendiente (ámbar)
- **IN_PROGRESS**: En progreso (azul)
- **COMPLETED**: Completado (verde)
- **CANCELLED**: Cancelado (gris)

### NoteImportance
- **Bajo**: Baja importancia (azul)
- **Medio**: Importancia media (ámbar)
- **Alto**: Alta importancia (rojo)

### PackingStatus
- **IN_PROGRESS**: En progreso (ámbar)
- **DELIVERED**: Entregado (verde)
- **REJECTED**: Rechazado (rojo)
- **CANCELLED**: Cancelado (gris)

### RejectionReason
- **QUALITY**: Problemas de calidad (rojo)
- **DEFECT**: Defectos encontrados (naranja)
- **DELAY**: Retrasos en entrega (ámbar)
- **OTHER**: Otras razones (gris)

## 🛡️ Consideraciones de seguridad

- Los scripts verifican si los datos ya existen antes de insertarlos (ON CONFLICT DO NOTHING)
- Los IDs se generan automáticamente usando `gen_random_uuid()`
- Los timestamps se establecen automáticamente con `NOW()`

## 🔧 Personalización

Si necesitas agregar más datos iniciales:

1. Edita el archivo `prisma/seed.ts`
2. Agrega tus nuevos datos siguiendo el patrón existente
3. Ejecuta `npm run db:seed` para aplicar los cambios

## 📌 Notas importantes

- El archivo `constants/noteImportance.ts` contiene mapeos hardcodeados de IDs. Si reseteas la BD, estos IDs cambiarán y deberás actualizar el archivo.
- Para evitar este problema, considera usar los nombres en lugar de IDs en tu código.
