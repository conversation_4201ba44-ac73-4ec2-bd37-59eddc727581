"use server";

import type {
    CreatePackingFromOrdersInput,
    QualityType,
} from "@/lib/types/packing";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";
import { auth } from "@/lib/auth";

import { generatePackingFolio } from "./generate-folio";
import { validateOrderSelections } from "./select-orders-for-packing";

const createPackingFromOrdersSchema = z.object({
    customerId: z.string().min(1, "El cliente es requerido"),
    subCustomerId: z.string().optional(),
    deliveryDate: z.date(),
    notes: z.string().optional(),
    transportNotes: z.string().optional(),
    selectedProducts: z
        .array(
            z.object({
                orderId: z.string(),
                productGroups: z.array(
                    z.object({
                        modelCode: z.string(),
                        colorName: z.string(),
                        partNumber: z.string().nullable(),
                        qualityDistribution: z.array(
                            z.object({
                                garmentSizeId: z.string(),
                                sizeCode: z.string(),
                                primera: z.number().int().min(0),
                                segunda: z.number().int().min(0),
                                manchada: z.number().int().min(0),
                                incompleta: z.number().int().min(0),
                            }),
                        ),
                    }),
                ),
            }),
        )
        .min(1, "Debe seleccionar al menos una orden"),
    companyInfo: z
        .object({
            name: z.string(),
            logo: z.string().optional().nullable(),
            rfc: z.string().optional().nullable(),
            address: z.string().optional().nullable(),
            city: z.string().optional().nullable(),
            state: z.string().optional().nullable(),
            postalCode: z.string().optional().nullable(),
            country: z.string().optional().nullable(),
            phone: z.string().optional().nullable(),
            email: z.string().optional().nullable(),
            website: z.string().optional().nullable(),
        })
        .optional()
        .nullable(),
});

export async function createPackingFromOrders(
    input: CreatePackingFromOrdersInput,
) {
    try {
        const session = await auth();

        if (!session?.user?.id) {
            return { success: false, error: "No autorizado" };
        }

        const validatedData = createPackingFromOrdersSchema.parse(input);

        // Validar disponibilidad
        const validation = await validateOrderSelections(validatedData);

        if (!validation.success) {
            return {
                success: false,
                error: validation.errors.join(", "),
            };
        }

        // Verificar cliente
        const customer = await db.customer.findUnique({
            where: { id: validatedData.customerId },
        });

        if (!customer) {
            return { success: false, error: "Cliente no encontrado" };
        }

        // Verificar subcliente si se proporciona
        if (validatedData.subCustomerId) {
            const subCustomer = await db.customer.findUnique({
                where: {
                    id: validatedData.subCustomerId,
                    parentId: validatedData.customerId,
                },
            });

            if (!subCustomer) {
                return { success: false, error: "Subcliente no válido" };
            }
        }

        // Obtener configuración de la empresa si no se proporciona
        let companyInfo = validatedData.companyInfo;

        if (!companyInfo) {
            const companySettings = await db.companySettings.findUnique({
                where: { id: "default" },
            });

            if (companySettings) {
                companyInfo = {
                    name: companySettings.companyName,
                    logo: companySettings.companyLogo,
                    rfc: companySettings.rfc,
                    address: companySettings.address,
                    city: companySettings.city,
                    state: companySettings.state,
                    postalCode: companySettings.postalCode,
                    country: companySettings.country,
                    phone: companySettings.phone,
                    email: companySettings.email,
                    website: companySettings.website,
                };
            }
        }

        // Generar folio único
        const folio = await generatePackingFolio();

        // Obtener el estado inicial
        const initialStatus = await db.packingStatus.findFirst({
            where: { name: "Pendiente" },
        });

        if (!initialStatus) {
            return {
                success: false,
                error: "Estado inicial no configurado en el sistema",
            };
        }

        // Preparar detalles del packing
        const packingDetails: any[] = [];
        const totalBoxes = 0;
        const totalBags = 0;
        const orderIds = new Set<string>();

        // Procesar cada orden y sus productos
        for (const orderSelection of validatedData.selectedProducts) {
            orderIds.add(orderSelection.orderId);

            for (const productGroup of orderSelection.productGroups) {
                for (const sizeDistribution of productGroup.qualityDistribution) {
                    // Crear detalles para cada calidad que tenga cantidad > 0
                    const qualities: { type: QualityType; quantity: number }[] =
                        [
                            {
                                type: "primera",
                                quantity: sizeDistribution.primera,
                            },
                            {
                                type: "segunda",
                                quantity: sizeDistribution.segunda,
                            },
                            {
                                type: "manchada",
                                quantity: sizeDistribution.manchada,
                            },
                            {
                                type: "incompleta",
                                quantity: sizeDistribution.incompleta,
                            },
                        ];

                    for (const quality of qualities) {
                        if (quality.quantity > 0) {
                            packingDetails.push({
                                garmentSizeId: sizeDistribution.garmentSizeId,
                                quantity: quality.quantity,
                                modelCode: productGroup.modelCode,
                                colorName: productGroup.colorName,
                                partNumber: productGroup.partNumber,
                                qualityType: quality.type,
                                // Los valores de empaque se calcularán después
                                packagingType: null,
                                packagingUnits: 0,
                                piecesPerUnit: 0,
                                loosePieces: 0,
                            });
                        }
                    }
                }
            }
        }

        // Determinar tipo de empaque principal (esto podría mejorarse con lógica más compleja)
        const packingType = orderIds.size > 1 ? "mixto" : "cajas";

        // Usar transacción para crear todo atomicamente
        const packing = await db.$transaction(async (tx) => {
            // Crear el packing
            const newPacking = await tx.packing.create({
                data: {
                    folio,
                    code: folio,
                    customerId: validatedData.customerId,
                    subCustomerId: validatedData.subCustomerId,
                    // No asociamos a una orden específica cuando hay múltiples
                    orderId:
                        orderIds.size === 1 ? Array.from(orderIds)[0] : null,
                    deliveryDate: validatedData.deliveryDate,
                    notes: validatedData.notes,
                    transportNotes: validatedData.transportNotes,
                    statusId: initialStatus.id,
                    companyInfo: companyInfo as any,
                    totalBoxes,
                    totalBags,
                    packingType,
                    packedById: session.user.id,
                    packedAt: new Date(),
                    details: {
                        create: packingDetails,
                    },
                    history: {
                        create: {
                            action: "CREATED",
                            metadata: {
                                createdBy: session.user.id,
                                userName: session.user.name,
                                details: packingDetails.length,
                                totalQuantity: packingDetails.reduce(
                                    (sum, d) => sum + d.quantity,
                                    0,
                                ),
                                fromOrders: Array.from(orderIds),
                            },
                        },
                    },
                },
                include: {
                    customer: true,
                    subCustomer: true,
                    status: true,
                    details: {
                        include: {
                            garmentSize: {
                                include: {
                                    garment: {
                                        include: {
                                            model: true,
                                            color: true,
                                        },
                                    },
                                    size: true,
                                },
                            },
                        },
                    },
                },
            });

            // Actualizar cantidades usadas
            for (const detail of packingDetails) {
                await tx.garmentSize.update({
                    where: { id: detail.garmentSizeId },
                    data: {
                        usedQuantity: {
                            increment: detail.quantity,
                        },
                    },
                });
            }

            // Crear resúmenes para cada orden incluida
            for (const orderId of orderIds) {
                const order = await tx.order.findUnique({
                    where: { id: orderId },
                });

                if (order) {
                    // Calcular totales para esta orden
                    const orderDetails = packingDetails.filter((d) => {
                        // Necesitamos verificar qué detalles pertenecen a esta orden
                        return validatedData.selectedProducts.some(
                            (sp) =>
                                sp.orderId === orderId &&
                                sp.productGroups.some((pg) =>
                                    pg.qualityDistribution.some(
                                        (qd) =>
                                            qd.garmentSizeId ===
                                            d.garmentSizeId,
                                    ),
                                ),
                        );
                    });

                    const summaryData = {
                        packingId: newPacking.id,
                        orderId: orderId,
                        cutOrderNumber: order.cutOrder,
                        totalFirstQuality: orderDetails
                            .filter((d) => d.qualityType === "primera")
                            .reduce((sum, d) => sum + d.quantity, 0),
                        totalSecondQuality: orderDetails
                            .filter((d) => d.qualityType === "segunda")
                            .reduce((sum, d) => sum + d.quantity, 0),
                        totalDefective: orderDetails
                            .filter((d) => d.qualityType === "manchada")
                            .reduce((sum, d) => sum + d.quantity, 0),
                        totalIncomplete: orderDetails
                            .filter((d) => d.qualityType === "incompleta")
                            .reduce((sum, d) => sum + d.quantity, 0),
                        boxesBySize: {},
                        bagsBySize: {},
                        piecesBySize: {},
                        totalBoxes: 0,
                        totalBags: 0,
                        totalLoosePieces: 0,
                    };

                    await tx.packingSummary.create({
                        data: summaryData as any,
                    });
                }
            }

            return newPacking;
        });

        // Revalidar paths
        revalidatePath("/dashboard/packings");
        revalidatePath(
            `/dashboard/customers/${validatedData.customerId}/details`,
        );
        orderIds.forEach((orderId) => {
            revalidatePath(`/dashboard/orders/${orderId}`);
        });

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error creating packing from orders:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return { success: false, error: "Error al crear el packing" };
    }
}
