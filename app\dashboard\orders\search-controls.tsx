"use client";

import React from "react";
import {
    Input,
    Button,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    Toolt<PERSON>,
    Kbd,
    Select,
    SelectItem,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    ArrowPathIcon,
    ChevronDownIcon,
    DocumentTextIcon,
    FunnelIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface OrderStatus {
    id: string;
    name: string;
    color?: string;
    iconName?: string;
}

interface SearchControlsProps {
    searchTerm: string;
    setSearchTerm: (value: string) => void;
    statusFilter: string;
    setStatusFilter: (value: string) => void;
    statusOptions: OrderStatus[];
    rowsPerPage: number;
    setRowsPerPage: (value: number) => void;
    setCurrentPage: (value: number) => void;
    isRefreshing: boolean;
    handleRefresh: () => void;
    handleSearch: (value: string) => void;
}

export default function SearchControls({
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    statusOptions,
    rowsPerPage,
    setRowsPerPage,
    setCurrentPage,
    isRefreshing,
    handleRefresh,
    handleSearch,
}: SearchControlsProps) {
    return (
        <div className="p-6 border-t border-b border-gray-100 dark:border-gray-800 bg-gradient-to-r from-gray-50 to-blue-50/30 dark:from-gray-800/50 dark:to-blue-900/10">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                {/* Búsqueda */}
                <div className="relative w-full md:max-w-md lg:max-w-lg">
                    <Input
                        isClearable
                        aria-label="Buscar órdenes"
                        classNames={{
                            inputWrapper:
                                "bg-transparent dark:bg-gray-800/50 shadow-sm dark:shadow-gray-900/50 border border-blue-200/50 dark:border-blue-800/30",
                            input: "text-gray-900 dark:text-gray-100",
                        }}
                        id="searchInput"
                        placeholder="Buscar órdenes... (Ctrl+K)"
                        startContent={
                            <MagnifyingGlassIcon className="w-4 h-4 text-primary/70" />
                        }
                        value={searchTerm}
                        onClear={() => setSearchTerm("")}
                        onValueChange={handleSearch}
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 hidden md:flex items-center pointer-events-none">
                        <Kbd className="text-xs">Ctrl</Kbd>
                        <span className="mx-1 text-gray-400">+</span>
                        <Kbd className="text-xs">K</Kbd>
                    </div>
                </div>

                {/* Filtro de estado */}
                <div className="w-full md:w-auto">
                    <Select
                        aria-label="Filtrar por estado"
                        className="min-w-[200px]"
                        classNames={{
                            trigger:
                                "bg-transparent dark:bg-gray-800/50 shadow-sm dark:shadow-gray-900/50 border border-blue-200/50 dark:border-blue-800/30",
                        }}
                        placeholder="Filtrar por estado"
                        selectedKeys={statusFilter ? [statusFilter] : []}
                        startContent={
                            <FunnelIcon className="w-4 h-4 text-primary/70" />
                        }
                        onSelectionChange={(keys) => {
                            const selectedKey =
                                Array.from(keys)[0]?.toString() || "";

                            setStatusFilter(selectedKey);
                            setCurrentPage(1);
                        }}
                    >
                        <React.Fragment>
                            <SelectItem key="all" textValue="Todos los estados">
                                Todos los estados
                            </SelectItem>
                            {statusOptions.map((status) => (
                                <SelectItem
                                    key={status.id}
                                    textValue={status.name}
                                >
                                    <div className="flex items-center gap-2">
                                        <div
                                            className="w-3 h-3 rounded-full"
                                            style={{
                                                backgroundColor:
                                                    status.color || "#9CA3AF",
                                            }}
                                        />
                                        <span>{status.name}</span>
                                    </div>
                                </SelectItem>
                            ))}
                        </React.Fragment>
                    </Select>
                </div>

                {/* Controles de tabla */}
                <div className="flex items-center gap-3 self-end">
                    {/* Selector de filas por página */}
                    <Dropdown className="z-10 relative">
                        <DropdownTrigger>
                            <Button
                                className="relative overflow-hidden min-w-[120px] bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30 group-hover/select:border-primary/50"
                                endContent={
                                    <motion.div
                                        animate={{ rotate: [0, 0] }}
                                        transition={{ duration: 0.2 }}
                                        whileHover={{ rotate: 180 }}
                                    >
                                        <ChevronDownIcon className="h-4 w-4 text-primary/70 group-hover/select:text-primary transition-colors duration-200" />
                                    </motion.div>
                                }
                                size="sm"
                                startContent={
                                    <motion.div
                                        animate={{ rotate: [0, 0] }}
                                        className="bg-blue-100/50 dark:bg-blue-900/30 p-1 rounded-full"
                                        transition={{ duration: 0.2 }}
                                        whileHover={{ rotate: 15 }}
                                    >
                                        <DocumentTextIcon className="h-3.5 w-3.5 text-primary/90 group-hover/select:text-primary transition-colors duration-200" />
                                    </motion.div>
                                }
                                variant="flat"
                            >
                                <span className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 opacity-0 group-hover/select:opacity-100 animate-shimmer transition-opacity duration-300" />
                                <span className="text-sm font-medium relative z-10">
                                    {rowsPerPage} filas
                                </span>
                            </Button>
                        </DropdownTrigger>
                        <DropdownMenu
                            disallowEmptySelection
                            aria-label="Seleccionar registros por página"
                            className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-blue-100/50 dark:border-blue-900/50 shadow-lg"
                            selectedKeys={new Set([rowsPerPage.toString()])}
                            selectionMode="single"
                            variant="flat"
                            onAction={(key) => {
                                const value = Number(key);

                                if (!isNaN(value)) {
                                    setRowsPerPage(value);
                                    setCurrentPage(1);
                                }
                            }}
                        >
                            {[5, 10, 20, 30, 40, 50].map((option) => (
                                <DropdownItem
                                    key={option.toString()}
                                    className="group/item data-[hover=true]:bg-gradient-to-r data-[hover=true]:from-blue-50/80 data-[hover=true]:to-indigo-50/80 dark:data-[hover=true]:from-blue-900/30 dark:data-[hover=true]:to-indigo-900/30 data-[hover=true]:scale-[1.02] transition-transform duration-200"
                                    textValue={option.toString()}
                                >
                                    <div className="flex items-center gap-2">
                                        <div className="w-7 h-7 rounded-full bg-gradient-to-br from-blue-100/80 to-indigo-100/80 dark:from-blue-900/30 dark:to-indigo-900/30 flex items-center justify-center text-xs font-medium text-primary/80 group-data-[hover=true]/item:scale-110 transition-transform duration-200">
                                            {option}
                                        </div>
                                        <span className="text-gray-700 dark:text-gray-300">
                                            filas
                                        </span>
                                    </div>
                                </DropdownItem>
                            ))}
                        </DropdownMenu>
                    </Dropdown>

                    {/* Botón de actualizar */}
                    <Tooltip
                        content={
                            isRefreshing ? "Actualizando..." : "Recargar datos"
                        }
                    >
                        <Button
                            isIconOnly
                            aria-label="Recargar datos"
                            className="relative overflow-hidden bg-gradient-to-r from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/20 dark:to-gray-900 shadow-sm hover:shadow-md hover:scale-105 transition-all duration-300 border border-blue-200/50 dark:border-blue-800/30"
                            isLoading={isRefreshing}
                            variant="flat"
                            onPress={handleRefresh}
                        >
                            <span className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 opacity-0 hover:opacity-100 animate-shimmer transition-opacity duration-300" />
                            <motion.div
                                animate={{
                                    rotate: isRefreshing ? 360 : 0,
                                }}
                                className="relative z-10"
                                transition={{
                                    duration: 1,
                                    repeat: isRefreshing ? Infinity : 0,
                                    ease: "linear",
                                }}
                            >
                                <ArrowPathIcon className="w-4 h-4 text-primary/80" />
                            </motion.div>
                        </Button>
                    </Tooltip>
                </div>
            </div>
        </div>
    );
}
