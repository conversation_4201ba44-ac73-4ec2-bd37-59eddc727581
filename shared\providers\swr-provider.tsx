"use client";

import { useEffect } from "react";
import { SWRConfig, mutate } from "swr";

// Tipo de la función manejadora de eventos de revalidación
type RevalidationHandler = (id?: string) => void;

// Almacén de manejadores de eventos por entidad
const revalidationHandlers: Record<string, Set<RevalidationHandler>> = {};

/**
 * Suscribe una función manejadora a los eventos de revalidación de una entidad
 * @param entity Nombre de la entidad (normalmente plural, ej: "sizes")
 * @param handler Función a ejecutar cuando ocurra revalidación
 */
export const subscribeToRevalidation = (
    entity: string,
    handler: RevalidationHandler,
) => {
    if (!revalidationHandlers[entity]) {
        revalidationHandlers[entity] = new Set();
    }
    revalidationHandlers[entity].add(handler);
};

/**
 * Cancela la suscripción de una función manejadora
 * @param entity Nombre de la entidad (normalmente plural, ej: "sizes")
 * @param handler Función previamente suscrita
 */
export const unsubscribeFromRevalidation = (
    entity: string,
    handler: RevalidationHandler,
) => {
    if (revalidationHandlers[entity]) {
        revalidationHandlers[entity].delete(handler);
    }
};

// Función para escuchar eventos de revalidación
const setupRevalidationListener = () => {
    if (typeof window === "undefined") return;

    // Escuchar eventos de transición de página
    const handleRouteChange = () => {
        // Cambio de ruta detectado, revalidando datos - comentario removido
        // Revalidar datos importantes en cambios de ruta
        mutate((key) => typeof key === "string" && key.startsWith("/api"));
    };

    // Escuchar eventos personalizados para revalidación
    const handleCustomRevalidation = (event: CustomEvent) => {
        const { tag, id } = event.detail || {};

        // Evento de revalidación recibido - comentario removido

        // Notificar a los manejadores registrados para esta entidad
        if (tag && revalidationHandlers[tag]) {
            revalidationHandlers[tag].forEach((handler) => {
                handler(id);
            });
        }

        if (tag === "sizes") {
            mutate((key) => Array.isArray(key) && key[0] === "sizes");
            if (id) {
                mutate(["size", id]);
            }
        } else if (tag === "contractors") {
            // Revalidando datos de contratistas - comentario removido
            // Revalidación específica para contratistas
            mutate((key) => Array.isArray(key) && key[0] === "contractors");
            mutate((key) => Array.isArray(key) && key[0] === "getContractors");
            if (id) {
                mutate(["contractor", id]);
                mutate(["getContractor", id]);
            }
        } else if (tag) {
            // Revalidar basado en el tag específico
            mutate(
                (key) =>
                    (Array.isArray(key) && key[0] === tag) ||
                    (typeof key === "string" && key.includes(tag)),
            );
        } else {
            // Revalidación global si no hay tag específico
            mutate(undefined, undefined, { revalidate: true });
        }
    };

    // Registrar el listener
    window.addEventListener(
        "custom-revalidation",
        handleCustomRevalidation as EventListener,
    );

    // Revalidar en focus/visibilidad
    const handleVisibilityChange = () => {
        if (document.visibilityState === "visible") {
            // Pestaña visible, revalidando datos - comentario removido
            // Revalidar datos importantes
            mutate(
                (key) =>
                    Array.isArray(key) &&
                    (key[0] === "sizes" ||
                        key[0] === "garments" ||
                        key[0] === "products" ||
                        key[0] === "contractors" ||
                        key[0] === "getContractors"),
            );
        }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Limpiar listeners
    return () => {
        window.removeEventListener(
            "custom-revalidation",
            handleCustomRevalidation as EventListener,
        );
        document.removeEventListener(
            "visibilitychange",
            handleVisibilityChange,
        );
    };
};

// Crear una función para disparar revalidación desde cualquier parte
export const triggerRevalidation = (tag: string, id?: string) => {
    if (typeof window === "undefined") return;

    // Crear y disparar un evento personalizado
    const event = new CustomEvent("custom-revalidation", {
        detail: { tag, id },
    });

    window.dispatchEvent(event);
    // Disparando revalidación - comentario removido
};

export const SWRProvider = ({ children }: { children: React.ReactNode }) => {
    // Configurar listener para revalidación
    useEffect(() => {
        const cleanup = setupRevalidationListener();

        return cleanup;
    }, []);

    return (
        <SWRConfig
            value={{
                fetcher: (url: string) =>
                    fetch(url).then((res) => {
                        if (!res.ok) {
                            throw new Error(
                                `Error al realizar la petición: ${res.status}`,
                            );
                        }

                        return res.json();
                    }),
                revalidateOnFocus: true,
                revalidateOnReconnect: true,
                revalidateIfStale: true,
                dedupingInterval: 10000, // Evita peticiones duplicadas en un intervalo de 10 segundos
                shouldRetryOnError: true, // Reintentar en caso de error
                errorRetryInterval: 5000, // Esperar 5 segundos antes de reintentar
                errorRetryCount: 3, // Máximo 3 reintentos
                keepPreviousData: true, // Mantiene los datos anteriores mientras se revalida
                onSuccess: (data, key) => {
                    // Podemos hacer algo cuando los datos se actualizan exitosamente
                    if (process.env.NODE_ENV !== "production") {
                        // Datos actualizados correctamente - comentario removido
                    }
                },
                onError: (error, key) => {
                    // Log de errores para debug
                    if (process.env.NODE_ENV !== "production") {
                        // Error SWR - comentario removido
                    }
                },
                // Estrategia mejorada para reintento en caso de error
                onErrorRetry: (
                    error,
                    key,
                    config,
                    revalidate,
                    { retryCount },
                ) => {
                    // No reintentar para errores específicos
                    if (error.status === 404) return;
                    if (error.status === 403) return;
                    if (error.status === 401) return;

                    // Solo reintentar un número limitado de veces
                    if (retryCount >= 3) return;

                    // Incrementar el intervalo de reintento
                    setTimeout(() => revalidate({ retryCount }), 5000);
                },
            }}
        >
            {children}
        </SWRConfig>
    );
};
