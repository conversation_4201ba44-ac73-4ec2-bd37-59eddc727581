---
description: Visual Process Map para PLAN Mode con Clear Thought integration
globs: plan-mode-map.mdc
alwaysApply: false
---
# 📋 PLAN MODE - VISUAL PROCESS MAP

> Modo de planificación detallada con integración Clear Thought

## 📊 MAPA DE PROCESO COMPLETO

```mermaid
graph TD
    Start["📋 AUTO-FLOW PLAN"] --> Load["📂 Cargar tasks.md<br>+ activeContext.md"]
    
    Load --> Level{"🎯 Nivel de<br>Complejidad"}
    
    Level -->|Level 2| L2Flow["📝 Flujo Básico"]
    Level -->|Level 3| L3Flow["🎨 Flujo Completo"]
    Level -->|Level 4| L4Flow["🏗️ Flujo Enterprise"]
    
    %% Level 2 Flow
    L2Flow --> L2Tasks["📋 Listar Tareas<br>🧠 sequentialthinking"]
    L2Tasks --> L2Deps["🔗 Identificar<br>Dependencias"]
    L2Deps --> L2Plan["📝 Crear Plan<br>Implementación"]
    
    %% Level 3-4 Flow
    L3Flow --> L3Analyze["🔍 Análisis Profundo<br>🧠 sequentialthinking"]
    L4Flow --> L3Analyze
    
    L3Analyze --> L3Components["🧩 Componentes<br>🧠 decisionframework"]
    L3Components --> L3Creative{"🎨 ¿Requiere<br>Creative?"}
    
    L3Creative -->|Sí| L3Flag["🚩 Marcar para<br>CREATIVE"]
    L3Creative -->|No| L3Direct["➡️ Directo a<br>IMPLEMENT"]
    
    L3Flag --> L3Deps["🔗 Mapear<br>Dependencias"]
    L3Direct --> L3Deps
    L3Deps --> L3Plan["📋 Plan Detallado<br>🧠 structuredargumentation"]
    
    %% Convergencia
    L2Plan & L3Plan --> Update["💾 Actualizar<br>tasks.md"]
    Update --> Progress["📊 Crear<br>progress.md"]
    Progress --> Visual["🗺️ Visual State<br>Update"]
    Visual --> Ready["✅ PLAN Complete"]
    
    %% Clear Thought Tools
    L2Tasks -.-> CT1["🧠 sequentialthinking<br>Desglose paso a paso"]
    L3Components -.-> CT2["🧠 decisionframework<br>Evaluar opciones"]
    L3Plan -.-> CT3["🧠 structuredargumentation<br>Justificar decisiones"]
    
    style Start fill:#ffa64d
    style Level fill:#d94dbb
    style L3Creative fill:#d971ff
    style Ready fill:#5fd94d
```

## 🔄 TEMPLATES POR NIVEL

### Level 2: Plan Básico
```markdown
## Plan de Implementación - [Nombre Tarea]

### 📋 Tareas Identificadas
1. [Tarea 1] - Estimado: X min
2. [Tarea 2] - Estimado: X min
3. [Tarea 3] - Estimado: X min

### 🔗 Dependencias
- Tarea 2 depende de Tarea 1
- Tarea 3 puede hacerse en paralelo

### ⚡ Ruta Crítica
Tarea 1 → Tarea 2 → Completado

### ⚠️ Riesgos Identificados
- [Riesgo 1]: [Mitigación]
```

### Level 3-4: Plan Comprehensivo
```markdown
## Plan de Implementación Detallado - [Nombre Feature]

### 🎯 Objetivos
- Objetivo principal: [descripción]
- Métricas de éxito: [KPIs]

### 🧩 Componentes Afectados
| Componente | Cambios Requeridos | Creative? | Prioridad |
|------------|-------------------|-----------|-----------|
| [Comp 1] | [Descripción] | ✅ | Alta |
| [Comp 2] | [Descripción] | ❌ | Media |

### 📋 Fases de Implementación
#### Fase 1: [Nombre] (X horas)
- [ ] Subtarea 1.1
- [ ] Subtarea 1.2

#### Fase 2: [Nombre] (X horas)
- [ ] Subtarea 2.1
- [ ] Subtarea 2.2

### 🎨 Componentes para Creative Phase
1. **[Componente X]**
   - Requiere: Decisión de arquitectura
   - Opciones a explorar: [A, B, C]
   
### 🔗 Matriz de Dependencias
| De\A | Comp1 | Comp2 | Comp3 |
|------|-------|-------|-------|
| Comp1 | - | ✓ | ✗ |
| Comp2 | ✗ | - | ✓ |
| Comp3 | ✗ | ✗ | - |
```

## 💾 ACTUALIZACIONES MEMORY BANK

```typescript
// Actualizar tasks.md con plan estructurado
updateTasks({
    tasks: structuredTaskList,
    dependencies: dependencyMatrix,
    creativeFlags: componentsRequiringCreative,
    timeline: phasedImplementationPlan
});

// Crear/actualizar progress.md
createProgressTracking({
    totalTasks: taskCount,
    phases: implementationPhases,
    currentPhase: 1,
    estimatedCompletion: calculateTimeline()
});
```

## 🎯 DECISIONES DE TRANSICIÓN

```mermaid
graph LR
    PLAN -->|Con Creative Flags| CREATIVE
    PLAN -->|Sin Creative Flags| IMPLEMENT
    
    style PLAN fill:#ffa64d
    style CREATIVE fill:#d971ff  
    style IMPLEMENT fill:#4dbb5f
```

## ✅ CHECKLIST DE VERIFICACIÓN

- [ ] Tasks.md cargado y analizado
- [ ] Tareas desglosadas con sequentialthinking
- [ ] Dependencias mapeadas claramente
- [ ] Componentes evaluados con decisionframework
- [ ] Creative flags identificados (Level 3-4)
- [ ] Plan documentado con structuredargumentation
- [ ] Progress.md creado/actualizado
- [ ] Visual state actualizado
- [ ] Siguiente modo determinado

---

**PLAN Mode completo - Plan estructurado listo**
