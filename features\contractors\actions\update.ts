"use server";

import { z } from "zod";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

import { updateContractorSchema } from "../schemas/contractor.schema";

import { validateContractorName } from "./validate";

// Obtener helpers de revalidación para la entidad "contractor"
const { revalidateCache } = createServerRevalidation("contractor");

/**
 * Actualiza un contratista existente
 * @param id ID del contratista a actualizar
 * @param data Datos a actualizar
 * @param forceUpdate Si es true, ignorará la validación de nombre único
 */
export async function updateContractor(
    id: string,
    data: {
        firstName?: string;
        middleName?: string | null;
        lastName?: string;
        secondLastName?: string | null;
        email?: string | null;
        phone?: string | null;
        notes?: string | null;
    },
    forceUpdate = false,
) {
    try {
        // Validar que el ID es un string válido
        if (!id || typeof id !== "string") {
            return {
                success: false,
                error: "ID de contratista inválido",
            };
        }

        // Verificar si el contratista existe
        const existingContractor = await db.contractor.findUnique({
            where: { id },
        });

        if (!existingContractor) {
            return {
                success: false,
                error: "Contratista no encontrado",
            };
        }

        // Combinar datos existentes con datos nuevos para la validación
        const mergedData = {
            firstName: data.firstName ?? existingContractor.firstName,
            middleName:
                data.middleName !== undefined
                    ? data.middleName
                    : existingContractor.middleName,
            lastName: data.lastName ?? existingContractor.lastName,
            secondLastName:
                data.secondLastName !== undefined
                    ? data.secondLastName
                    : existingContractor.secondLastName,
            email:
                data.email !== undefined
                    ? data.email
                    : existingContractor.email,
            phone:
                data.phone !== undefined
                    ? data.phone
                    : existingContractor.phone,
            notes:
                data.notes !== undefined
                    ? data.notes
                    : existingContractor.notes,
        };

        // Validar datos con Zod schema
        const validatedData = updateContractorSchema.parse(mergedData);

        // Si el nombre del contratista ha cambiado y no es una actualización forzada,
        // validar que el nuevo nombre no exista ya
        const newFirstName = data.firstName ?? existingContractor.firstName;
        const newLastName = data.lastName ?? existingContractor.lastName;

        if (
            (newFirstName !== existingContractor.firstName ||
                newLastName !== existingContractor.lastName) &&
            !forceUpdate
        ) {
            const nameValidation = await validateContractorName(
                newFirstName as string,
                newLastName as string,
                id, // Excluir el ID actual de la validación
            );

            // Si la validación del nombre falla o el nombre no es válido
            if (
                !nameValidation ||
                !nameValidation.success ||
                (nameValidation.data && !nameValidation.data.isValid)
            ) {
                return {
                    success: false,
                    error: "El nombre del contratista ya existe en el sistema",
                };
            }
        }

        // Construir el nombre completo actualizado
        const fullName =
            `${validatedData.firstName || ""} ${validatedData.middleName || ""} ${validatedData.lastName || ""} ${validatedData.secondLastName || ""}`
                .trim()
                .replace(/\s+/g, " ");

        // Actualizar el contratista en la base de datos
        const updatedContractor = await db.contractor.update({
            where: { id },
            data: {
                name: fullName,
                firstName: validatedData.firstName,
                middleName: validatedData.middleName,
                lastName: validatedData.lastName,
                secondLastName: validatedData.secondLastName,
                email: validatedData.email,
                phone: validatedData.phone,
                notes: validatedData.notes,
            },
        });

        // Transformar fechas para evitar problemas de serialización
        const serializedContractor = {
            ...updatedContractor,
            createdAt: updatedContractor.createdAt.toISOString(),
            updatedAt: updatedContractor.updatedAt.toISOString(),
        };

        // Revalidar caché usando el helper genérico
        revalidateCache(id);

        return { success: true, data: serializedContractor };
    } catch (error) {
        // Manejar errores de validación de Zod
        if (error instanceof z.ZodError) {
            return {
                success: false,
                error: error.errors[0]?.message || "Datos inválidos",
            };
        }

        // Usar el helper para manejar errores de DB
        return handleDbError(() => {
            throw error;
        }, "Error al actualizar contratista");
    }
}
