import React from "react";
import {
    <PERSON><PERSON> as HeroButton,
    ButtonProps as HeroButtonProps,
} from "@heroui/react";

// import { cn } from "@/shared/utils/cn"; // Comment out - module doesn't exist
const cn = (...classes: any[]) => classes.filter(Boolean).join(" "); // Simple fallback

export interface ImprovedButtonProps
    extends Omit<HeroButtonProps, "color" | "variant" | "size"> {
    variant?:
        | "primary"
        | "secondary"
        | "ghost"
        | "danger"
        | "success"
        | "warning";
    size?: "xs" | "sm" | "md" | "lg" | "xl";
}

/**
 * Sistema de botones mejorado con jerarquía visual clara
 * Basado en HeroUI pero con mejor contraste y estados
 */
export const ImprovedButton = React.forwardRef<
    HTMLButtonElement,
    ImprovedButtonProps
>(
    (
        { variant = "secondary", size = "md", className, children, ...props },
        ref,
    ) => {
        // Mapeo de variantes a configuraciones de HeroUI
        const variantMapping = {
            primary: {
                color: "primary" as const,
                variant: "solid" as const,
                className:
                    "bg-[var(--color-primary-9)] text-white hover:bg-[var(--color-primary-10)] font-medium shadow-md hover:shadow-lg transition-all",
            },
            secondary: {
                color: "default" as const,
                variant: "bordered" as const,
                className:
                    "border-2 border-[var(--color-gray-6)] text-[var(--color-gray-11)] hover:bg-[var(--color-gray-2)] font-medium",
            },
            ghost: {
                color: "default" as const,
                variant: "light" as const,
                className:
                    "text-[var(--color-gray-11)] hover:bg-[var(--color-gray-3)] data-[hover=true]:bg-[var(--color-gray-3)]",
            },
            danger: {
                color: "danger" as const,
                variant: "solid" as const,
                className:
                    "bg-[var(--color-error-9)] text-white hover:bg-[var(--color-error-11)] font-medium shadow-md",
            },
            success: {
                color: "success" as const,
                variant: "solid" as const,
                className:
                    "bg-[var(--color-success-9)] text-white hover:bg-[var(--color-success-11)] font-medium shadow-md",
            },
            warning: {
                color: "warning" as const,
                variant: "solid" as const,
                className:
                    "bg-[var(--color-warning-9)] text-white hover:bg-[var(--color-warning-11)] font-medium shadow-md",
            },
        };

        const sizeMapping = {
            xs: "min-h-unit-6 text-xs px-2",
            sm: "min-h-unit-8 text-sm px-3",
            md: "min-h-unit-10 text-base px-4",
            lg: "min-h-unit-12 text-lg px-6",
            xl: "min-h-unit-14 text-xl px-8",
        };

        const config = variantMapping[variant];

        return (
            <HeroButton
                ref={ref}
                className={cn(
                    config.className,
                    sizeMapping[size],
                    "transition-all duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-[var(--color-primary-9)] focus:ring-offset-2",
                    className,
                )}
                color={config.color}
                size={size as any}
                variant={config.variant}
                {...props}
            >
                {children}
            </HeroButton>
        );
    },
);

ImprovedButton.displayName = "ImprovedButton";

export default ImprovedButton;
