import { Suspense } from "react";
import { Card, CardBody } from "@heroui/card";

import { auth } from "@/lib/auth-helpers";
import { OrderMigrationButton } from "@/features/orders/components/OrderMigrationButton";

export default async function DashboardPage() {
    const session = await auth();
    const isAdmin = session?.user?.role?.name === "ADMIN";

    return (
        <div className="container mx-auto py-6 space-y-6">
            <h1 className="text-2xl font-bold mb-4">
                Bienvenido al Panel de Control
            </h1>

            {isAdmin && (
                <Card>
                    <CardBody>
                        <div className="mb-4">
                            <h2 className="text-xl font-semibold">
                                Herramientas de Administración
                            </h2>
                            <p className="text-sm text-gray-500">
                                Funciones especiales para administradores del
                                sistema
                            </p>
                        </div>

                        <h3 className="text-lg font-semibold mb-2">
                            Migración de Pedidos
                        </h3>
                        <p className="text-sm text-gray-500 mb-4">
                            Esta herramienta creará partidas automáticamente
                            para todos los pedidos existentes que no las tengan.
                        </p>
                        <Suspense fallback={<div>Cargando...</div>}>
                            <OrderMigrationButton />
                        </Suspense>
                    </CardBody>
                </Card>
            )}
        </div>
    );
}
