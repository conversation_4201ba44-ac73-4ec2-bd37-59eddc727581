// Types for Enhanced Packing System

import type {
    Packing,
    PackingDetail,
    PackingSummary,
    PackingQualityCheck,
    Customer,
    User,
    PackingStatus,
    Order,
} from "@prisma/client";

// Extended Packing types with relations
export interface PackingWithRelations extends Packing {
    customer: Customer;
    subCustomer?: Customer | null;
    status: PackingStatus;
    order?: Order | null;
    details: PackingDetailWithRelations[];
    summaries?: PackingSummaryWithRelations[];
    qualityChecks?: PackingQualityCheck[];
    packedBy?: User | null;
    verifiedBy?: User | null;
    qualityCheckBy?: User | null;
}

export interface PackingDetailWithRelations extends PackingDetail {
    garmentSize: {
        id: string;
        totalQuantity: number;
        size: {
            id: string;
            code: string;
        };
        garment: {
            id: string;
            model: {
                id: string;
                code: string;
                description: string;
            };
            color: {
                id: string;
                name: string;
                hexCode?: string | null;
            };
        };
    };
}

export interface PackingSummaryWithRelations extends PackingSummary {
    order?: Order | null;
}

// Form data types
export interface PackingFormData {
    customerId: string;
    subCustomerId?: string | null;
    orderId?: string | null;
    deliveryDate: Date;
    notes?: string | null;
    transportNotes?: string | null;
    packingType?: string | null;
    details: PackingDetailFormData[];
}

export interface PackingDetailFormData {
    garmentSizeId: string;
    modelCode: string;
    colorName: string;
    partNumber?: string | null;
    quantity: number;
    qualityType: QualityType;
    packagingType?: PackagingType | null;
    packagingUnits?: number;
    piecesPerUnit?: number;
    loosePieces?: number;
}

// Enums
export type QualityType = "primera" | "segunda" | "manchada" | "incompleta";
export type PackagingType = "caja" | "bolsa";
export type PackingType = "mixto" | "cajas" | "bolsas";

// Company info structure
export interface CompanyInfo {
    name: string;
    logo?: string | null;
    rfc?: string | null;
    address?: string | null;
    city?: string | null;
    state?: string | null;
    postalCode?: string | null;
    country?: string | null;
    phone?: string | null;
    email?: string | null;
    website?: string | null;
}

// Summary by size structure
export interface SizeSummary {
    boxes: number;
    bags: number;
    loosePieces: number;
}

// Packing summary data
export interface PackingSummaryData {
    packingId: string;
    orderId?: string;
    cutOrderNumber?: string;
    totalFirstQuality: number;
    totalSecondQuality: number;
    totalDefective: number;
    totalIncomplete: number;
    boxesBySize: Record<string, number>;
    bagsBySize: Record<string, number>;
    piecesBySize: Record<string, number>;
    totalBoxes: number;
    totalBags: number;
    totalLoosePieces: number;
}

// Create packing input
export interface CreatePackingInput {
    customerId: string;
    subCustomerId?: string | null;
    orderId?: string | null;
    deliveryDate: Date;
    notes?: string | null;
    transportNotes?: string | null;
    packingType?: PackingType | null;
    companyInfo?: CompanyInfo | null;
    details: CreatePackingDetailInput[];
}

export interface CreatePackingDetailInput {
    garmentSizeId: string;
    quantity: number;
    modelCode?: string | null;
    colorName?: string | null;
    partNumber?: string | null;
    qualityType?: QualityType;
    packagingType?: PackagingType | null;
    packagingUnits?: number;
    piecesPerUnit?: number;
    loosePieces?: number;
    comments?: string | null;
}

// Filters
export interface PackingFilters {
    search?: string;
    customerId?: string;
    subCustomerId?: string;
    statusId?: string;
    qualityType?: QualityType;
    packingType?: PackingType;
    dateFrom?: Date | null;
    dateTo?: Date | null;
    hasQualityCheck?: boolean;
}

// Stats
export interface PackingStats {
    total: number;
    byStatus: Record<string, number>;
    byQuality: {
        primera: number;
        segunda: number;
        manchada: number;
        incompleta: number;
    };
    totalBoxes: number;
    totalBags: number;
    totalPieces: number;
}

// New types for order-based packing creation
export interface OrderProductGroup {
    orderId: string;
    orderNumber: string;
    cutOrder: string | null;
    modelCode: string;
    modelDescription: string;
    colorName: string;
    partNumber: string | null;
    sizes: OrderProductSize[];
    totalAvailable: number;
}

export interface OrderProductSize {
    sizeCode: string;
    sizeOrder: number;
    garmentSizeId: string;
    totalQuantity: number;
    availableQuantity: number;
    usedQuantity: number;
}

export interface QualityDistribution {
    garmentSizeId: string;
    sizeCode: string;
    primera: number;
    segunda: number;
    manchada: number;
    incompleta: number;
}

export interface SelectedProductGroup {
    modelCode: string;
    colorName: string;
    partNumber: string | null;
    qualityDistribution: QualityDistribution[];
}

export interface SelectedOrderProducts {
    orderId: string;
    productGroups: SelectedProductGroup[];
}

export interface CreatePackingFromOrdersInput {
    customerId: string;
    subCustomerId?: string;
    deliveryDate: Date;
    notes?: string;
    transportNotes?: string;
    selectedProducts: SelectedOrderProducts[];
    companyInfo?: CompanyInfo | null;
}

// Enhanced types for Phase 1 implementation

// Customer with sub-clients
export interface CustomerWithChildren extends Customer {
    subCustomers: Customer[];
    parent?: Customer | null;
}

// Enhanced packaging summary data
export interface PackagingSummaryData {
    orderId: string;
    orderNumber: string;
    piecesPerBox: number;
    piecesPerBag: number;
    sizeBreakdown: SizeBreakdown[];
    totalBoxes: number;
    totalBagsFirst: number;
    totalBagsSecond: number;
    totalLoosePieces: number;
}

export interface SizeBreakdown {
    size: string;
    totalQuantity: number;
    firstQuality: number;
    secondQuality: number;
    boxes: number;
    loosePieces: number;
    bagsFirst: number;
    bagsSecond: number;
}

// Enhanced packing form data
export interface EnhancedPackingFormData extends PackingFormData {
    transportSignature?: string | null;
    transportSignedAt?: Date | null;
    receiverSignature?: string | null;
    receiverSignedAt?: Date | null;
    receiverName?: string | null;
    packingSummaryBySize?: Record<
        string,
        Record<
            string,
            {
                boxes: number;
                loosePieces: number;
                bagsFirst: number;
                bagsSecond: number;
            }
        >
    > | null;
    packagingSummaries?: PackagingSummaryData[];
}

// Enhanced packing detail with packaging info
export interface EnhancedPackingDetailFormData extends PackingDetailFormData {
    boxesCount?: number | null;
    bagsFirstCount?: number | null;
    bagsSecondCount?: number | null;
    loosePiecesInfo?: Record<
        string,
        {
            quantity: number;
            reason?: string;
        }
    > | null;
}

// Packaging settings
export interface PackagingSettings {
    defaultPiecesPerBox: number;
    defaultPiecesPerBag: number;
    allowMixedPackaging: boolean;
}

// Customer packing settings
export interface CustomerPackingSettings {
    defaultPiecesPerBox?: number;
    defaultPiecesPerBag?: number;
    preferredPackagingType?: PackagingType;
    customInstructions?: string;
}

// Professional packing document props
export interface ProfessionalPackingDocumentProps {
    packing: PackingWithFullRelations;
}

// Full packing relations for PDF generation
export interface PackingWithFullRelations extends PackingWithRelations {
    createdByUser: User;
    packedByUser?: User | null;
    verifiedByUser?: User | null;
    qualityCheckByUser?: User | null;
}
