"use client";

import React, { useEffect, useState } from "react";
import { formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

interface TimeAgoProps {
    date: Date | string;
    className?: string;
}

export function TimeAgo({ date, className = "" }: TimeAgoProps) {
    const [timeAgo, setTimeAgo] = useState<string>("");

    useEffect(() => {
        const updateTimeAgo = () => {
            const dateObj = typeof date === "string" ? new Date(date) : date;

            setTimeAgo(
                formatDistanceToNow(dateObj, {
                    addSuffix: true,
                    locale: es,
                }),
            );
        };

        // Update immediately
        updateTimeAgo();

        // Update every minute
        const interval = setInterval(updateTimeAgo, 60000);

        return () => clearInterval(interval);
    }, [date]);

    if (!timeAgo) return null;

    return (
        <span
            className={`text-xs text-gray-500 dark:text-gray-400 ${className}`}
        >
            {timeAgo}
        </span>
    );
}
