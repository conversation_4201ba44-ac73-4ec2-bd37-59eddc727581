/**
 * Script para agregar datos de prueba para las métricas
 * Ejecutar con: npx ts-node scripts/seed-metrics-data.ts
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function seedMetricsData() {
    try {
        console.log("🌱 Iniciando seed de datos para métricas...");

        // Obtener algunos contratistas
        const contractors = await prisma.contractor.findMany({
            take: 3
        });

        if (contractors.length === 0) {
            console.log("❌ No hay contratistas en la base de datos");
            return;
        }

        // Obtener algunas órdenes
        const orders = await prisma.order.findMany({
            take: 5,
            include: {
                garments: {
                    include: {
                        sizes: true
                    }
                }
            }
        });

        if (orders.length === 0) {
            console.log("❌ No hay órdenes en la base de datos");
            return;
        }

        console.log(`✅ Encontrados ${contractors.length} contratistas y ${orders.length} órdenes`);

        // Crear asignaciones para generar métricas
        for (const contractor of contractors) {
            console.log(`\n📋 Procesando contratista: ${contractor.name}`);
            
            // Crear 3-5 asignaciones por contratista
            const numAssignments = Math.floor(Math.random() * 3) + 3;
            
            for (let i = 0; i < numAssignments; i++) {
                const order = orders[Math.floor(Math.random() * orders.length)];
                const garment = order.garments[0];
                
                if (!garment || !garment.sizes[0]) continue;
                
                const garmentSize = garment.sizes[0];
                const quantity = Math.floor(Math.random() * 50) + 10;
                const isCompleted = Math.random() > 0.3; // 70% completadas
                
                // Crear asignación
                const assignment = await prisma.assignment.create({
                    data: {
                        contractorId: contractor.id,
                        orderId: order.id,
                        garmentSizeId: garmentSize.id,
                        quantity: quantity,
                        isCompleted: isCompleted,
                        status: isCompleted ? "COMPLETED" : "ACTIVE",
                        folio: `TEST-${Date.now()}-${i}`
                    }
                });
                
                console.log(`  ✅ Asignación creada: ${quantity} piezas - ${isCompleted ? 'COMPLETADA' : 'ACTIVA'}`);
                
                // Si está completada, crear una remisión
                if (isCompleted && Math.random() > 0.5) {
                    const remission = await prisma.remission.create({
                        data: {
                            folio: `REM-TEST-${Date.now()}-${i}`,
                            contractorId: contractor.id,
                            createdAt: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)), // Últimos 7 días
                            assignments: {
                                create: {
                                    assignmentId: assignment.id
                                }
                            }
                        }
                    });
                    
                    console.log(`  📄 Remisión creada: ${remission.folio}`);
                }
            }
        }

        // Mostrar resumen de métricas
        console.log("\n📊 Resumen de métricas creadas:");
        
        const totalAssignments = await prisma.assignment.count();
        const activeAssignments = await prisma.assignment.count({
            where: { status: "ACTIVE" }
        });
        const completedAssignments = await prisma.assignment.count({
            where: { isCompleted: true }
        });
        const totalRemissions = await prisma.remission.count();
        
        console.log(`  📦 Total asignaciones: ${totalAssignments}`);
        console.log(`  🔄 Asignaciones activas: ${activeAssignments}`);
        console.log(`  ✅ Asignaciones completadas: ${completedAssignments}`);
        console.log(`  📄 Total remisiones: ${totalRemissions}`);
        console.log(`  📈 Tasa de cumplimiento: ${Math.round((completedAssignments / totalAssignments) * 100)}%`);

        console.log("\n✨ Seed de métricas completado exitosamente!");

    } catch (error) {
        console.error("❌ Error al ejecutar seed:", error);
    } finally {
        await prisma.$disconnect();
    }
}

// Ejecutar el seed
seedMetricsData();