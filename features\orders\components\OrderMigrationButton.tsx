"use client";

import { Button, addToast } from "@heroui/react";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import {
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

export function OrderMigrationButton() {
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<any>(null);

    const handleMigration = async () => {
        try {
            setIsLoading(true);
            setResult(null);

            const response = await fetch("/api/admin/migrate-orders", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
            });

            const data = await response.json();

            setResult(data);

            if (data.success) {
                addToast({
                    title: "Éxito",
                    description: `Migración completada: ${data.data.successCount} pedidos actualizados`,
                    color: "success",
                    icon: <CheckCircleIcon className="w-5 h-5" />,
                });
            } else {
                addToast({
                    title: "Error",
                    description: `Error en la migración: ${data.error}`,
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });
            }
        } catch (error) {
            // REMOVED: console.error("Error al migrar pedidos:", error);
            addToast({
                title: "Error",
                description: "Error al procesar la migración",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="space-y-4">
            <Button
                className="w-full md:w-auto"
                disabled={isLoading}
                onClick={handleMigration}
            >
                {isLoading ? (
                    <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Migrando pedidos...
                    </>
                ) : (
                    "Migrar pedidos sin partidas"
                )}
            </Button>

            {result && (
                <div className="mt-4 p-4 border rounded-md bg-muted">
                    <h3 className="font-medium mb-2">Resultado:</h3>
                    {result.success ? (
                        <div>
                            <p>Mensaje: {result.data.message}</p>
                            {result.data.totalProcessed > 0 && (
                                <>
                                    <p>
                                        Total procesados:{" "}
                                        {result.data.totalProcessed}
                                    </p>
                                    <p>Éxitos: {result.data.successCount}</p>
                                    <p>Errores: {result.data.errorCount}</p>

                                    {result.data.errors &&
                                        result.data.errors.length > 0 && (
                                            <div className="mt-2">
                                                <p className="font-medium">
                                                    Errores:
                                                </p>
                                                <ul className="list-disc pl-5 text-sm">
                                                    {result.data.errors
                                                        .slice(0, 5)
                                                        .map(
                                                            (
                                                                error: string,
                                                                index: number,
                                                            ) => (
                                                                <li key={index}>
                                                                    {error}
                                                                </li>
                                                            ),
                                                        )}
                                                    {result.data.errors.length >
                                                        5 && (
                                                        <li>
                                                            ... y{" "}
                                                            {result.data.errors
                                                                .length -
                                                                5}{" "}
                                                            más
                                                        </li>
                                                    )}
                                                </ul>
                                            </div>
                                        )}
                                </>
                            )}
                        </div>
                    ) : (
                        <p className="text-destructive">
                            Error: {result.error}
                        </p>
                    )}
                </div>
            )}
        </div>
    );
}
