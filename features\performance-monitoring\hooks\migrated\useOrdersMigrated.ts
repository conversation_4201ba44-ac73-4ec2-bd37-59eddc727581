/**
 * Migrated Orders Hook with React Query
 * Drop-in replacement for useOrder.ts with performance optimizations
 */

"use client";

import type { OrderQueryOptions } from "@/features/orders/types/orders";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import {
    getOrders,
    getOrderStatuses,
    deleteOrder as remove,
    getOrder,
    updateOrderWithDetails,
} from "@/features/orders/actions";

import { featureFlags } from "../../feature-flags";
import { useHybridQuery } from "../useHybridQuery";
import { useOrdersOptimized } from "../useOrdersOptimized";

/**
 * Optimized hook for fetching orders
 * Uses React Query when feature flag is enabled
 */
export function useOrders(options: OrderQueryOptions = {}) {
    // Check feature flag
    const enableReactQuery = featureFlags.ENABLE_REACT_QUERY_ORDERS;

    if (enableReactQuery) {
        // Use optimized React Query version
        return useOrdersOptimized(options);
    }

    // Fallback to SWR (existing implementation)
    const { data, error, mutate } = useHybridQuery(
        ["orders", JSON.stringify(options)],
        () => getOrders(options),
        {
            enableReactQuery: false, // Force SWR
            revalidateOnFocus: false,
            refreshInterval: 5 * 60 * 1000,
        },
    );

    return {
        orders: data?.data?.orders || [],
        pagination: data?.data?.pagination,
        isLoading: !error && !data,
        isError: error,
        refresh: mutate,
    };
}

/**
 * Hook for single order with mutations
 */
export function useOrder(id: string) {
    const queryClient = useQueryClient();
    const enableReactQuery = featureFlags.ENABLE_REACT_QUERY_ORDERS;

    // Query for single order
    const query = useHybridQuery(["order", id], () => getOrder(id), {
        enableReactQuery,
        revalidateOnFocus: false,
    });

    // Delete mutation
    const deleteMutation = useMutation({
        mutationFn: (orderId: string) => remove(orderId),
        onSuccess: () => {
            // Invalidate orders list
            queryClient.invalidateQueries({ queryKey: ["orders"] });
            queryClient.invalidateQueries({ queryKey: ["order", id] });
        },
    });

    // Update mutation
    const updateMutation = useMutation({
        mutationFn: (data: any) => updateOrderWithDetails(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["order", id] });
            queryClient.invalidateQueries({ queryKey: ["orders"] });
        },
    });

    return {
        order: query.data?.data,
        isLoading: query.isLoading,
        isError: !!query.error,
        error: query.error,
        deleteOrder: deleteMutation.mutate,
        updateOrder: updateMutation.mutate,
        isDeleting: deleteMutation.isPending,
        isUpdating: updateMutation.isPending,
    };
}

/**
 * Hook for order statuses
 */
export function useOrderStatuses() {
    const enableReactQuery = featureFlags.ENABLE_REACT_QUERY_ORDERS;

    const { data, error, isLoading } = useHybridQuery(
        ["orderStatuses"],
        () => getOrderStatuses(),
        {
            enableReactQuery,
            staleTime: 30 * 60 * 1000, // 30 minutes - statuses rarely change
        },
    );

    return {
        statuses: data?.data || [],
        isLoading,
        isError: !!error,
        error,
    };
}
