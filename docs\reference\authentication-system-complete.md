# Sistema de Autenticación y Autorización - Proyecto Lohari

## 🔐 Stack de Autenticación

- **Framework**: NextAuth v5.0.0-beta.25
- **Estrategia**: JWT (JSON Web Tokens)
- **Provider**: Credentials (email + password)
- **Hashing**: bcryptjs
- **Sesiones**: 24 horas (regular) o 30 días (remember me)

## 🏗️ Arquitectura General

```
┌─────────────┐     ┌──────────────┐     ┌────────────┐
│   Cliente   │────▶│  Middleware  │────▶│ NextAuth   │
│ (Browser)   │     │   (Edge)     │     │  Config    │
└─────────────┘     └──────────────┘     └────────────┘
                            │                    │
                            ▼                    ▼
                    ┌──────────────┐     ┌────────────┐
                    │    Rutas     │     │   Prisma   │
                    │  Protegidas  │     │    (DB)    │
                    └──────────────┘     └────────────┘
```

## 📁 Estructura de Archivos

```
/
├── auth.ts                    # Configuración principal NextAuth
├── middleware.ts              # Protección de rutas
├── app/
│   ├── (auth)/               # Grupo de rutas auth
│   │   ├── login/            # Página de login
│   │   └── register/         # Página de registro
│   └── api/
│       └── auth/
│           ├── [...nextauth]/ # Rutas NextAuth
│           ├── login/         # API login custom
│           └── register/      # API registro
└── features/
    └── auth/
        ├── actions/          # Server actions
        ├── components/       # Componentes auth
        ├── config/          # Configuración
        ├── hooks/           # useAuthForm, useUser
        └── utils/           # Utilidades auth
```

## 🎭 Sistema de Roles

### Roles Disponibles

| Rol | Nombre | Descripción | Color | Icono |
|-----|--------|-------------|--------|-------|
| **ADMIN** | Administrador | Control total del sistema | #EF4444 (Rojo) | ShieldCheckIcon |
| **EMPLOYEE** | Empleado | Usuario interno de la empresa | #3B82F6 (Azul) | UserIcon |
| **CONTRACTOR** | Contratista | Usuario externo (maquila) | #10B981 (Verde) | BriefcaseIcon |
| **GUEST** | Invitado | Usuario por defecto al registrarse | #9CA3AF (Gris) | UserCircleIcon |

### Modelo de Base de Datos

```prisma
model Role {
  id        String   @id @default(cuid())
  name      String   @unique
  iconName  String?
  color     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  users     User[]
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  password      String?
  emailVerified DateTime?
  image         String?
  roleId        String
  role          Role      @relation(fields: [roleId], references: [id])
  // ... más relaciones
}
```

## 🔧 Configuración de NextAuth

### `/auth.ts` - Configuración Principal

```typescript
import NextAuth from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"

export const { auth, handlers, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        remember: { label: "Remember me", type: "checkbox" }
      },
      async authorize(credentials) {
        // 1. Validar credenciales
        // 2. Verificar contraseña con bcrypt
        // 3. Retornar usuario con rol
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60,    // 24 horas default
    updateAge: 60 * 60,      // Actualizar cada hora
  },
  pages: {
    signIn: "/login",
    error: "/error",
  },
  callbacks: {
    authorized({ request, auth }) {
      // Verificar acceso a rutas
      const isLoggedIn = !!auth?.user
      const pathname = request.nextUrl.pathname
      
      if (pathname.startsWith("/dashboard")) {
        return isLoggedIn
      }
      
      return true
    },
    jwt({ token, user, trigger }) {
      // Añadir datos del usuario al token
      if (user) {
        token.id = user.id
        token.role = user.role
        token.name = user.name
        token.email = user.email
      }
      return token
    },
    session({ session, token }) {
      // Enriquecer sesión con datos del token
      if (token && session.user) {
        session.user.id = token.id as string
        session.user.role = token.role as string
      }
      return session
    }
  }
})
```

## 🛡️ Middleware de Protección

### `/middleware.ts`

```typescript
export { auth as middleware } from "@/auth"

export const config = {
  matcher: [
    // Proteger todas las rutas excepto:
    "/((?!_next|api/auth|api/v1/auth|images|favicon.ico|login|register).*)",
  ],
}
```

**Rutas excluidas:**
- `/_next/*` - Archivos internos de Next.js
- `/api/auth/*` - Endpoints de NextAuth
- `/api/v1/auth/*` - API custom de auth
- `/images/*`, `/favicon.ico` - Archivos estáticos
- `/login`, `/register` - Páginas públicas

## 🔄 Flujos de Autenticación

### 1. Login Flow

```mermaid
Usuario → Login Form → signIn('credentials') → authorize() → JWT → Session
                              ↓
                      Verificar credenciales
                              ↓
                      Hash password check
                              ↓
                      Retornar usuario + rol
```

**Código del formulario:**
```typescript
const onSubmit = async (data: LoginInput) => {
  const result = await signIn('credentials', {
    email: data.email,
    password: data.password,
    remember: data.remember,
    redirect: false,
  })
  
  if (result?.error) {
    // Manejar error
  } else {
    // Redirigir a dashboard
    router.push(callbackUrl || '/dashboard')
  }
}
```

### 2. Register Flow

```mermaid
Usuario → Register Form → API Route → Server Action → Create User → Auto Login
                                           ↓
                                    Validar con Zod
                                           ↓
                                    Hash password
                                           ↓
                                    Asignar rol GUEST
```

**Server Action:**
```typescript
export async function register(data: RegisterInput) {
  try {
    // 1. Validar datos
    const validated = registerSchema.parse(data)
    
    // 2. Verificar email único
    const exists = await prisma.user.findUnique({
      where: { email: validated.email }
    })
    
    if (exists) {
      return { success: false, error: "Email ya registrado" }
    }
    
    // 3. Hash password
    const hashedPassword = await hashPassword(validated.password)
    
    // 4. Obtener rol GUEST
    const guestRole = await prisma.role.findUnique({
      where: { name: "GUEST" }
    })
    
    // 5. Crear usuario
    const user = await prisma.user.create({
      data: {
        email: validated.email,
        name: validated.name,
        password: hashedPassword,
        roleId: guestRole.id
      }
    })
    
    return { success: true, data: user }
  } catch (error) {
    return handleDbError(error)
  }
}
```

### 3. Logout Flow

```typescript
// Simple logout
await signOut({ redirect: true, callbackUrl: '/login' })
```

## 🔐 Patrones de Autorización

### 1. En Páginas (Server Components)

```typescript
import { auth } from "@/auth"

export default async function AdminPage() {
  const session = await auth()
  
  // Verificar autenticación
  if (!session) {
    redirect('/login')
  }
  
  // Verificar rol
  if (session.user.role !== 'ADMIN') {
    return <Unauthorized />
  }
  
  // Renderizar página admin
  return <AdminDashboard />
}
```

### 2. En Server Actions

```typescript
export async function deleteOrder(id: string) {
  const session = await auth()
  
  // Verificar autenticación
  if (!session?.user) {
    return { 
      success: false, 
      error: "Usuario no autenticado" 
    }
  }
  
  // Verificar permisos
  const canDelete = ['ADMIN', 'EMPLOYEE'].includes(session.user.role)
  if (!canDelete) {
    return { 
      success: false, 
      error: "No tienes permisos para eliminar órdenes" 
    }
  }
  
  // Ejecutar acción
  // ...
}
```

### 3. En Componentes Cliente

```typescript
"use client"

import { useSession } from "next-auth/react"

export function AdminTools() {
  const { data: session } = useSession()
  
  if (session?.user?.role !== 'ADMIN') {
    return null
  }
  
  return (
    <div>
      {/* Herramientas admin */}
    </div>
  )
}
```

## 🪝 Hooks y Utilidades

### useAuthForm

```typescript
export function useAuthForm({
  type = 'login',
  callbackUrl = '/dashboard',
  onSuccess,
  onError
}: UseAuthFormProps) {
  const form = useForm({
    resolver: zodResolver(type === 'login' ? loginSchema : registerSchema)
  })
  
  const onSubmit = async (data) => {
    if (type === 'login') {
      const result = await signIn('credentials', {
        ...data,
        redirect: false
      })
      // Manejar resultado
    } else {
      // Registro
      const result = await registerUser(data)
      if (result.success) {
        // Auto login después de registro
        await signIn('credentials', {
          email: data.email,
          password: data.password,
          redirect: false
        })
      }
    }
  }
  
  return { form, onSubmit, isLoading }
}
```

### getCurrentUser

```typescript
export async function getCurrentUser() {
  const session = await auth()
  
  if (!session?.user?.email) {
    return null
  }
  
  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: { role: true }
  })
  
  return user
}
```

### Validación de Contraseñas

```typescript
export function isStrongPassword(password: string): boolean {
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasNonalphas = /\W/.test(password)
  
  return (
    password.length >= minLength &&
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasNonalphas
  )
}
```

## 🔒 Seguridad

### Medidas Implementadas

1. **Contraseñas hasheadas** con bcryptjs (salt rounds: 12)
2. **Tokens JWT seguros** con secret aleatorio
3. **CSRF Protection** built-in de Next.js
4. **Rate limiting** en endpoints de auth (TODO)
5. **Validación estricta** con Zod en todos los inputs

### Headers de Seguridad

```typescript
// next.config.js
const securityHeaders = [
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]
```

## 📊 Tipos TypeScript

### Extensión de tipos NextAuth

```typescript
// types/next-auth.d.ts
declare module "next-auth" {
  interface User {
    id: string
    role: string
  }
  
  interface Session {
    user: {
      id: string
      email: string
      name?: string
      role: string
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string
    role: string
  }
}
```

## 🚨 Limitaciones Actuales

1. **Sin permisos granulares**: Solo verificación de roles básica
2. **Sin 2FA**: No hay autenticación de dos factores
3. **Sin OAuth**: Solo login con credenciales
4. **Sin refresh tokens**: JWT no se renueva automáticamente
5. **Sin políticas complejas**: No hay RBAC/ABAC avanzado

## 🎯 Mejores Prácticas

1. **Siempre verificar en el servidor**: No confiar solo en verificaciones del cliente
2. **Usar getCurrentUser()**: Para obtener datos actualizados del usuario
3. **Manejo de errores consistente**: Mensajes amigables al usuario
4. **Logging de intentos fallidos**: Para detectar ataques
5. **Actualizar roles con cuidado**: Revalidar sesiones después de cambios

## 🔧 Troubleshooting

### Error: "NEXTAUTH_URL is not set"
```bash
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key
```

### Error: "User not found"
- Verificar que el usuario existe en BD
- Verificar que tiene un rol asignado
- Revisar logs de authorize()

### Sesión no persiste
- Verificar cookies en browser
- Revisar configuración de sesión
- Comprobar NEXTAUTH_SECRET