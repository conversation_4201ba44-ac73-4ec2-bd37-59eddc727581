import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter, Trend } from 'k6/metrics';

// Custom metrics
const apiDuration = new Trend('api_duration');
const cacheHits = new Counter('cache_hits');
const cacheMisses = new Counter('cache_misses');

// Test configuration
export const options = {
  stages: [
    { duration: '1m', target: 10 },  // Ramp up to 10 users
    { duration: '3m', target: 50 },  // Stay at 50 users
    { duration: '1m', target: 100 }, // Peak at 100 users
    { duration: '2m', target: 50 },  // Scale down to 50
    { duration: '1m', target: 0 },   // Ramp down to 0
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    api_duration: ['p(95)<300'],      // 95% of API calls under 300ms
    'cache_hits': ['count>1000'],     // At least 1000 cache hits
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

export default function () {
  // Test 1: Homepage with static content
  const homeResponse = http.get(`${BASE_URL}/`);
  check(homeResponse, {
    'homepage status is 200': (r) => r.status === 200,
    'homepage loads fast': (r) => r.timings.duration < 500,
  });

  // Check cache headers
  const cacheControl = homeResponse.headers['Cache-Control'];
  if (cacheControl && cacheControl.includes('max-age')) {
    cacheHits.add(1);
  } else {
    cacheMisses.add(1);
  }

  sleep(1);
}