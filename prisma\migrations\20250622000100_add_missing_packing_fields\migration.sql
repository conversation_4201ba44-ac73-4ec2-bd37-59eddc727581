-- AlterTable
ALTER TABLE "Packing" 
ADD COLUMN IF NOT EXISTS "folio" TEXT,
ADD COLUMN IF NOT EXISTS "notes" TEXT,
ADD COLUMN IF NOT EXISTS "packedById" TEXT,
ADD COLUMN IF NOT EXISTS "verifiedById" TEXT,
ADD COLUMN IF NOT EXISTS "packedAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "verifiedAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "qualityCheckPassed" BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS "qualityNotes" TEXT,
ADD COLUMN IF NOT EXISTS "qualityCheckAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "qualityCheckById" TEXT,
ADD COLUMN IF NOT EXISTS "transporterId" TEXT,
ADD COLUMN IF NOT EXISTS "vehicleInfo" TEXT,
ADD COLUMN IF NOT EXISTS "driverName" TEXT,
ADD COLUMN IF NOT EXISTS "driverPhone" TEXT,
ADD COLUMN IF NOT EXISTS "totalWeight" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "totalVolume" DOUBLE PRECISION,
ADD COLUMN IF NOT EXISTS "packagesCount" INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS "trackingNumber" TEXT,
ADD COLUMN IF NOT EXISTS "printedAt" TIMESTAMP(3);

-- Update folio to be unique
UPDATE "Packing" SET "folio" = "code" WHERE "folio" IS NULL;
ALTER TABLE "Packing" ALTER COLUMN "folio" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX IF NOT EXISTS "Packing_folio_key" ON "Packing"("folio");

-- AddForeignKey
ALTER TABLE "Packing" ADD CONSTRAINT "Packing_packedById_fkey" FOREIGN KEY ("packedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Packing" ADD CONSTRAINT "Packing_verifiedById_fkey" FOREIGN KEY ("verifiedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Packing" ADD CONSTRAINT "Packing_qualityCheckById_fkey" FOREIGN KEY ("qualityCheckById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Packing" ADD CONSTRAINT "Packing_transporterId_fkey" FOREIGN KEY ("transporterId") REFERENCES "Contractor"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- CreateTable
CREATE TABLE IF NOT EXISTS "PackingHistory" (
    "id" TEXT NOT NULL,
    "packingId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,

    CONSTRAINT "PackingHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE IF NOT EXISTS "PackingSequence" (
    "id" TEXT NOT NULL DEFAULT 'single',
    "lastDate" TEXT NOT NULL,
    "lastSequence" INTEGER NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PackingSequence_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE IF NOT EXISTS "PackingQualityCheck" (
    "id" TEXT NOT NULL,
    "packingId" TEXT NOT NULL,
    "checkType" TEXT NOT NULL,
    "passed" BOOLEAN NOT NULL,
    "notes" TEXT,
    "checkedById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PackingQualityCheck_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PackingHistory" ADD CONSTRAINT "PackingHistory_packingId_fkey" FOREIGN KEY ("packingId") REFERENCES "Packing"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PackingQualityCheck" ADD CONSTRAINT "PackingQualityCheck_packingId_fkey" FOREIGN KEY ("packingId") REFERENCES "Packing"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PackingQualityCheck" ADD CONSTRAINT "PackingQualityCheck_checkedById_fkey" FOREIGN KEY ("checkedById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- CreateIndex
CREATE INDEX IF NOT EXISTS "PackingQualityCheck_packingId_idx" ON "PackingQualityCheck"("packingId");
CREATE INDEX IF NOT EXISTS "PackingQualityCheck_checkType_idx" ON "PackingQualityCheck"("checkType");
CREATE INDEX IF NOT EXISTS "PackingQualityCheck_createdAt_idx" ON "PackingQualityCheck"("createdAt");

-- AlterTable
ALTER TABLE "PackingDetail" 
ADD COLUMN IF NOT EXISTS "boxNumber" INTEGER,
ADD COLUMN IF NOT EXISTS "defects" INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS "qualityPassed" BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS "verifiedQuantity" INTEGER,
ADD COLUMN IF NOT EXISTS "weight" DOUBLE PRECISION;