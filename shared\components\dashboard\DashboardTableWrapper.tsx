"use client";

import dynamic from "next/dynamic";

import { Spinner } from "@/shared/components/ui/hero-ui-client";

// Dynamically import DashboardTable with no SSR to avoid hydration issues
export const DashboardTable = dynamic(
    () => import("./DashboardTable").then((mod) => mod.DashboardTable),
    {
        ssr: false,
        loading: () => (
            <div className="flex justify-center items-center min-h-[400px]">
                <Spinner label="Cargando tabla..." size="lg" />
            </div>
        ),
    },
);

export type { Column, TableAction } from "./DashboardTable";
