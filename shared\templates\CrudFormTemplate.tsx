"use client";

import React from "react";
import { ArrowLeftIcon, CheckIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Progress,
} from "@/shared/components/ui/hero-ui-client";

export interface WizardStep {
    id: string;
    title: string;
    subtitle?: string;
    icon: React.ReactNode;
    component: React.ReactNode;
    validation?: () => boolean;
}

interface CrudFormTemplateProps {
    // Layout props
    title: string;
    subtitle?: string;
    breadcrumbs?: Array<{ label: string; href?: string }>;

    // Wizard props
    steps: WizardStep[];
    currentStep: number;
    onStepChange: (step: number) => void;

    // Form props
    onSubmit: (e: React.FormEvent) => void | Promise<void>;
    isSubmitting?: boolean;

    // Navigation
    backRoute?: string;
    backLabel?: string;

    // Optional features
    showProgress?: boolean;
    allowStepNavigation?: boolean;
    completedSteps?: Set<string>;
}

export function CrudFormTemplate({
    title,
    subtitle,
    breadcrumbs = [],
    steps,
    currentStep,
    onStepChange,
    onSubmit,
    isSubmitting = false,
    backRoute = "/dashboard",
    backLabel = "Volver",
    showProgress = true,
    allowStepNavigation = false,
    completedSteps = new Set(),
}: CrudFormTemplateProps) {
    const router = useRouter();
    const currentStepData = steps[currentStep];
    const progress = ((currentStep + 1) / steps.length) * 100;

    const canNavigateToStep = (index: number) => {
        if (!allowStepNavigation) return false;
        if (index === currentStep) return true;
        if (index < currentStep) return true;
        // Only check completed steps, not validation during render
        if (index === currentStep + 1) {
            return completedSteps.has(steps[currentStep]?.id);
        }

        return completedSteps.has(steps[index - 1]?.id);
    };

    const handleStepClick = (index: number) => {
        if (canNavigateToStep(index)) {
            onStepChange(index);
        }
    };

    const handleNext = () => {
        if (currentStep < steps.length - 1) {
            if (!currentStepData.validation || currentStepData.validation()) {
                onStepChange(currentStep + 1);
            }
        }
    };

    const handlePrevious = () => {
        if (currentStep > 0) {
            onStepChange(currentStep - 1);
        }
    };

    return (
        <DashboardLayout
            actions={
                <Button
                    startContent={<ArrowLeftIcon className="w-4 h-4" />}
                    variant="light"
                    onPress={() => router.push(backRoute)}
                >
                    {backLabel}
                </Button>
            }
            breadcrumbs={[...breadcrumbs, { label: currentStepData.title }]}
            subtitle={subtitle}
            title={title}
        >
            <form className="space-y-6" onSubmit={onSubmit}>
                {/* Progress and Steps */}
                <Card className="shadow-sm">
                    <CardBody className="p-6">
                        {showProgress && (
                            <div className="mb-6">
                                <div className="flex justify-between items-center mb-2">
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Paso {currentStep + 1} de {steps.length}
                                    </span>
                                    <span className="text-sm text-gray-500 dark:text-gray-400">
                                        {Math.round(progress)}% completado
                                    </span>
                                </div>
                                <Progress
                                    className="mb-6"
                                    color="primary"
                                    size="sm"
                                    value={progress}
                                />
                            </div>
                        )}

                        {/* Desktop Stepper */}
                        <div className="hidden lg:flex items-center justify-between mb-8">
                            {steps.map((step, index) => {
                                const isActive = index === currentStep;
                                const isCompleted =
                                    completedSteps.has(step.id) ||
                                    index < currentStep;
                                const canNavigate = canNavigateToStep(index);

                                return (
                                    <React.Fragment key={step.id}>
                                        <button
                                            className={`
                        flex items-center gap-3 px-4 py-3 rounded-lg transition-all
                        ${isActive ? "bg-primary-50 dark:bg-primary-900/20" : ""}
                        ${canNavigate ? "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800" : "cursor-not-allowed opacity-50"}
                      `}
                                            disabled={!canNavigate}
                                            type="button"
                                            onClick={() =>
                                                handleStepClick(index)
                                            }
                                        >
                                            <div
                                                className={`
                        w-10 h-10 rounded-full flex items-center justify-center
                        ${isActive ? "bg-primary text-white" : ""}
                        ${isCompleted && !isActive ? "bg-success text-white" : ""}
                        ${!isActive && !isCompleted ? "bg-gray-200 dark:bg-gray-700 text-gray-500" : ""}
                      `}
                                            >
                                                {isCompleted && !isActive ? (
                                                    <CheckIcon className="w-5 h-5" />
                                                ) : (
                                                    <span className="text-2xl">
                                                        {step.icon}
                                                    </span>
                                                )}
                                            </div>
                                            <div className="text-left">
                                                <div
                                                    className={`font-medium ${isActive ? "text-primary" : "text-gray-700 dark:text-gray-300"}`}
                                                >
                                                    {step.title}
                                                </div>
                                                {step.subtitle && (
                                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                                        {step.subtitle}
                                                    </div>
                                                )}
                                            </div>
                                        </button>
                                        {index < steps.length - 1 && (
                                            <div
                                                className={`flex-1 h-0.5 mx-4 ${
                                                    index < currentStep
                                                        ? "bg-success"
                                                        : "bg-gray-200 dark:bg-gray-700"
                                                }`}
                                            />
                                        )}
                                    </React.Fragment>
                                );
                            })}
                        </div>

                        {/* Mobile Stepper */}
                        <div className="flex lg:hidden items-center justify-center gap-2 mb-6">
                            {steps.map((_, index) => (
                                <button
                                    key={index}
                                    className={`
                    w-2 h-2 rounded-full transition-all
                    ${index === currentStep ? "w-8 bg-primary" : ""}
                    ${index < currentStep ? "bg-success" : ""}
                    ${index > currentStep ? "bg-gray-300 dark:bg-gray-700" : ""}
                  `}
                                    disabled={!canNavigateToStep(index)}
                                    type="button"
                                    onClick={() => handleStepClick(index)}
                                />
                            ))}
                        </div>
                    </CardBody>
                </Card>

                {/* Step Content */}
                <Card className="shadow-sm">
                    <CardHeader className="border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-primary-50 dark:bg-primary-900/20 text-primary">
                                {currentStepData.icon}
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                                    {currentStepData.title}
                                </h2>
                                {currentStepData.subtitle && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        {currentStepData.subtitle}
                                    </p>
                                )}
                            </div>
                        </div>
                    </CardHeader>
                    <CardBody className="p-6">
                        <AnimatePresence mode="wait">
                            <motion.div
                                key={currentStep}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -20 }}
                                initial={{ opacity: 0, x: 20 }}
                                transition={{ duration: 0.2 }}
                            >
                                {currentStepData.component}
                            </motion.div>
                        </AnimatePresence>
                    </CardBody>
                </Card>

                {/* Navigation Buttons */}
                <div className="flex justify-between items-center">
                    <Button
                        isDisabled={currentStep === 0}
                        startContent={<ArrowLeftIcon className="w-4 h-4" />}
                        type="button"
                        variant="light"
                        onPress={handlePrevious}
                    >
                        Anterior
                    </Button>

                    <div className="flex gap-3">
                        {currentStep === steps.length - 1 ? (
                            <Button
                                color="primary"
                                isDisabled={isSubmitting}
                                isLoading={isSubmitting}
                                type="submit"
                            >
                                {isSubmitting ? "Guardando..." : "Guardar"}
                            </Button>
                        ) : (
                            <Button
                                color="primary"
                                endContent={
                                    <ArrowLeftIcon className="w-4 h-4 rotate-180" />
                                }
                                type="button"
                                onPress={handleNext}
                            >
                                Siguiente
                            </Button>
                        )}
                    </div>
                </div>
            </form>
        </DashboardLayout>
    );
}
