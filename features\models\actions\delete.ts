"use server";

import { db, handleDbError } from "@/shared/lib/db";
import { createServerRevalidation } from "@/shared/lib/revalidation";

// Obtener helpers de revalidación para la entidad "model"
const { revalidateCache } = createServerRevalidation("model");

/**
 * Elimina un modelo
 * @param id ID del modelo a eliminar
 */
export async function deleteModel(id: string) {
    if (!id) return { success: false, error: "ID no válido" };

    const result = await handleDbError(async () => {
        const model = await db.garmentModel.delete({
            where: { id },
        });

        // Revalidar caché usando el helper genérico
        revalidateCache(id);

        return model;
    }, "Error al eliminar modelo");

    return result;
}
