"use server";

import type { LocalStorageFolios } from "../types";

import { prisma } from "@/shared/lib/prisma";

import { generateChecksum } from "./utils";

/**
 * Validates a folio string format
 */
function validateFolio(folio: string): boolean {
    // Check format: LOH-YYYYMMDD-XXXX-Z
    const regex = /^LOH-\d{8}-\d{4}-\d{1}$/;

    if (!regex.test(folio)) return false;

    // Extract parts
    const parts = folio.split("-");
    const folioBase = `${parts[0]}-${parts[1]}-${parts[2]}`;
    const checksum = parts[3];

    // Check checksum
    return generateChecksum(folioBase) === checksum;
}

/**
 * Migrates folios from localStorage to database
 */
export async function migrateFromLocalStorage(data: LocalStorageFolios) {
    // Store results for reporting
    const results = {
        total: data.all.length,
        valid: 0,
        invalid: 0,
        migrated: 0,
        skipped: 0,
        errors: 0,
    };

    // Process each folio
    const migrationResults = await Promise.all(
        data.all.map(async (folio) => {
            // Validate folio format
            if (!validateFolio(folio)) {
                results.invalid++;

                return { folio, status: "invalid" };
            }

            results.valid++;

            try {
                // Check if already exists
                const existing = await prisma.remission.findUnique({
                    where: { folio },
                });

                if (existing) {
                    results.skipped++;

                    return { folio, status: "exists" };
                }

                // Create placeholder remission
                await prisma.remission.create({
                    data: {
                        folio,
                        // assignmentId: "migrated", // TODO: Fix schema relationship
                        // contractorId: "migrated", // TODO: Fix schema relationship
                        status: "MIGRATED" as any,
                        orderDetails: {
                            migrated: true,
                            migrationDate: new Date().toISOString(),
                        },
                    } as any,
                });

                // Record history
                await prisma.remissionHistory.create({
                    data: {
                        remissionId: "migrated", // Will need to be updated
                        action: "MIGRATED_FROM_LOCALSTORAGE",
                        metadata: {
                            originalFolio: folio,
                            migratedAt: new Date().toISOString(),
                        },
                    },
                });

                results.migrated++;

                return { folio, status: "migrated" };
            } catch (error) {
                results.errors++;
                // REMOVED: console.error(`Error migrating folio ${folio}:`, error);

                return { folio, status: "error", error };
            }
        }),
    );

    // Update folio sequence if needed
    if (data.lastDate && data.lastSequence) {
        try {
            await prisma.folioSequence.upsert({
                where: { id: "single" },
                update: {
                    lastDate: data.lastDate,
                    lastSequence: Math.max(
                        data.lastSequence,
                        (
                            await prisma.folioSequence.findUnique({
                                where: { id: "single" },
                            })
                        )?.lastSequence || 0,
                    ),
                },
                create: {
                    id: "single",
                    lastDate: data.lastDate,
                    lastSequence: data.lastSequence,
                },
            });
        } catch (error) {
            // REMOVED: console.error("Error updating folio sequence:", error);
            results.errors++;
        }
    }

    return {
        results,
        details: migrationResults,
    };
}

/**
 * Function to import folios from localStorage data
 */
export async function importFoliosFromLocalStorage(localStorageData: string) {
    try {
        const parsedData = JSON.parse(localStorageData) as LocalStorageFolios;

        return await migrateFromLocalStorage(parsedData);
    } catch (error) {
        // REMOVED: console.error("Error importing folios:", error);
        throw new Error("Failed to import folios from localStorage");
    }
}
