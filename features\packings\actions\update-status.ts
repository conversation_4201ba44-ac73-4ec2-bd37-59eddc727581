"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { db } from "@/lib/db";

const updateStatusSchema = z.object({
    packingId: z.string().min(1, "El ID del packing es requerido"),
    statusId: z.string().min(1, "El estado es requerido"),
    notes: z.string().optional(),
});

export async function updatePackingStatus(
    data: z.infer<typeof updateStatusSchema>,
) {
    try {
        const validatedData = updateStatusSchema.parse(data);

        // Verificar que el packing existe
        const packing = await db.packing.findUnique({
            where: { id: validatedData.packingId },
            include: {
                status: true,
            },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        // Verificar que el nuevo estado existe
        const newStatus = await db.packingStatus.findUnique({
            where: { id: validatedData.statusId },
        });

        if (!newStatus) {
            return { success: false, error: "Estado no válido" };
        }

        // Actualizar el packing y crear registro en el historial
        const updatedPacking = await db.packing.update({
            where: { id: validatedData.packingId },
            data: {
                statusId: validatedData.statusId,
                history: {
                    create: {
                        action: "STATUS_CHANGED",
                        metadata: {
                            fromStatus: packing.status.name,
                            toStatus: newStatus.name,
                            notes: validatedData.notes,
                            changedBy: "system", // TODO: Obtener usuario actual
                        },
                    },
                },
            },
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                details: {
                    include: {
                        garmentSize: {
                            include: {
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                                size: true,
                            },
                        },
                    },
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${validatedData.packingId}`);

        return { success: true, data: updatedPacking };
    } catch (error) {
        console.error("Error updating packing status:", error);
        if (error instanceof z.ZodError) {
            return { success: false, error: error.errors[0].message };
        }

        return {
            success: false,
            error: "Error al actualizar el estado del packing",
        };
    }
}

export async function markPackingAsPrinted(packingId: string) {
    try {
        const packing = await db.packing.update({
            where: { id: packingId },
            data: {
                printedAt: new Date(),
                history: {
                    create: {
                        action: "PRINTED",
                        metadata: {
                            printedBy: "system", // TODO: Obtener usuario actual
                            printedAt: new Date().toISOString(),
                        },
                    },
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${packingId}`);

        return { success: true, data: packing };
    } catch (error) {
        console.error("Error marking packing as printed:", error);

        return {
            success: false,
            error: "Error al marcar el packing como impreso",
        };
    }
}

export async function addPackingSignature(packingId: string, signature: any) {
    try {
        const packing = await db.packing.findUnique({
            where: { id: packingId },
        });

        if (!packing) {
            return { success: false, error: "Packing no encontrado" };
        }

        const currentSignatures = (packing.signatures as any) || {};
        const updatedSignatures = {
            ...currentSignatures,
            ...signature,
        };

        const updatedPacking = await db.packing.update({
            where: { id: packingId },
            data: {
                signatures: updatedSignatures,
                history: {
                    create: {
                        action: "SIGNATURE_ADDED",
                        metadata: {
                            signatureType: Object.keys(signature)[0],
                            addedBy: "system", // TODO: Obtener usuario actual
                        },
                    },
                },
            },
        });

        revalidatePath("/dashboard/packings");
        revalidatePath(`/dashboard/packings/${packingId}`);

        return { success: true, data: updatedPacking };
    } catch (error) {
        console.error("Error adding signature:", error);

        return { success: false, error: "Error al agregar la firma" };
    }
}
