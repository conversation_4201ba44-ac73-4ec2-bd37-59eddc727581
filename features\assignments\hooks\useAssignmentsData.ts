"use client";

import useSWR from "swr";
import { useMemo } from "react";
import { startOfMonth, endOfMonth } from "date-fns";

import { getAssignments } from "../actions";

export interface AssignmentWithRelations {
    id: string;
    quantity: number;
    createdAt: Date;
    updatedAt: Date;
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";
    order: {
        id: string;
        code: string;
        customer?: {
            id: string;
            name: string;
        };
    };
    contractor: {
        id: string;
        name: string;
        email?: string;
        phone?: string;
    };
    garmentSize: {
        id: string;
        quantity: number;
        garment: {
            id: string;
            model: {
                id: string;
                code: string;
                name: string;
            };
            color: {
                id: string;
                name: string;
                hexCode: string;
            };
        };
        size: {
            id: string;
            code: string;
            name: string;
        };
    };
    remissions?: Array<{
        remission: {
            id: string;
            folio: string;
            status: string;
            printedAt?: Date | null;
        };
    }>;
}

export interface UseAssignmentsDataReturn {
    assignments: AssignmentWithRelations[];
    isLoading: boolean;
    isError: boolean;
    error: any;
    mutate: () => void;
    stats: {
        total: number;
        pending: number;
        inProgress: number;
        completed: number;
        thisMonth: number;
        totalQuantity: number;
        averageQuantity: number;
        topContractors: Array<{
            contractor: string;
            count: number;
            quantity: number;
        }>;
        topModels: Array<{
            model: string;
            count: number;
            quantity: number;
        }>;
    };
}

export function useAssignmentsData(filters?: {
    search?: string;
    status?: string;
    contractorId?: string;
    orderId?: string;
    dateFrom?: string;
    dateTo?: string;
}): UseAssignmentsDataReturn {
    const { data, error, isLoading, mutate } = useSWR(
        ["assignments", filters],
        async () => {
            const result = await getAssignments({
                ...filters,
                page: 1,
                perPage: 1000, // Get all for client-side processing
            });

            if (!result.success || !result.data) {
                throw new Error(result.error || "Error loading assignments");
            }

            return result.data;
        },
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            dedupingInterval: 5000,
        },
    );

    // Calculate stats
    const stats = useMemo(() => {
        const assignments = (data || []) as any;

        const now = new Date();
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);

        // Basic counts
        const total = assignments.length;
        const pending = assignments.filter(
            (a: any) => a.status === "PENDING",
        ).length;
        const inProgress = assignments.filter(
            (a: any) => a.status === "IN_PROGRESS",
        ).length;
        const completed = assignments.filter(
            (a: any) => a.status === "COMPLETED",
        ).length;

        // This month's assignments
        const thisMonth = assignments.filter((a: any) => {
            const createdAt = new Date(a.createdAt);

            return createdAt >= monthStart && createdAt <= monthEnd;
        }).length;

        // Quantity stats
        const totalQuantity = assignments.reduce(
            (sum: number, a: any) => sum + a.quantity,
            0,
        );
        const averageQuantity =
            total > 0 ? Math.round(totalQuantity / total) : 0;

        // Top contractors
        const contractorMap = new Map<
            string,
            { count: number; quantity: number }
        >();

        assignments.forEach((a: any) => {
            const key = a.contractor.name;
            const current = contractorMap.get(key) || { count: 0, quantity: 0 };

            contractorMap.set(key, {
                count: current.count + 1,
                quantity: current.quantity + a.quantity,
            });
        });

        const topContractors = Array.from(contractorMap.entries())
            .map(([contractor, data]) => ({ contractor, ...data }))
            .sort((a, b) => b.quantity - a.quantity)
            .slice(0, 5);

        // Top models
        const modelMap = new Map<string, { count: number; quantity: number }>();

        assignments.forEach((a: any) => {
            const key = `${a.garmentSize.garment.model.code} - ${a.garmentSize.garment.model.name}`;
            const current = modelMap.get(key) || { count: 0, quantity: 0 };

            modelMap.set(key, {
                count: current.count + 1,
                quantity: current.quantity + a.quantity,
            });
        });

        const topModels = Array.from(modelMap.entries())
            .map(([model, data]) => ({ model, ...data }))
            .sort((a, b) => b.quantity - a.quantity)
            .slice(0, 5);

        return {
            total,
            pending,
            inProgress,
            completed,
            thisMonth,
            totalQuantity,
            averageQuantity,
            topContractors,
            topModels,
        };
    }, [data]);

    return {
        assignments: (data || []) as any,
        isLoading,
        isError: !!error,
        error,
        mutate,
        stats,
    };
}
