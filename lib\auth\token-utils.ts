/**
 * Token utilities for AuthService compatibility
 * These are mock tokens that reference NextAuth sessions
 */

/**
 * Generate a mock access token that references the NextAuth session
 * This is just for compatibility with AuthService's expected format
 */
export function generateMockAccessToken(user: any): string {
    // Create a simple JWT-like token that contains user ID
    // In reality, the actual auth is handled by NextAuth cookies
    const header = btoa(JSON.stringify({ alg: "HS256", typ: "JWT" }));
    const payload = btoa(
        JSON.stringify({
            sub: user.id,
            email: user.email,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
        }),
    );
    const signature = btoa("mock-signature");

    return `${header}.${payload}.${signature}`;
}

/**
 * Generate a mock refresh token
 * This is just for compatibility with AuthService's expected format
 */
export function generateMockRefreshToken(user: any): string {
    // Simple refresh token format
    return btoa(
        JSON.stringify({
            userId: user.id,
            type: "refresh",
            created: Date.now(),
        }),
    );
}

/**
 * Decode a mock access token to get user info
 * Used for validating tokens in API requests
 */
export function decodeMockAccessToken(token: string): any {
    try {
        const parts = token.split(".");

        if (parts.length !== 3) {
            return null;
        }

        const payload = JSON.parse(atob(parts[1]));

        // Check expiration
        if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
            return null;
        }

        return payload;
    } catch (error) {
        return null;
    }
}

/**
 * Validate if a token is in the correct format
 */
export function isValidMockToken(token: string): boolean {
    try {
        const parts = token.split(".");

        if (parts.length !== 3) {
            return false;
        }

        // Try to decode each part
        JSON.parse(atob(parts[0])); // header
        JSON.parse(atob(parts[1])); // payload

        return true;
    } catch (error) {
        return false;
    }
}
