"use client";

import { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>eader,
    <PERSON>dalBody,
    Modal<PERSON>ooter,
    Button,
    RadioGroup,
    Radio,
    Divider,
} from "@heroui/react";
import { DocumentArrowDownIcon } from "@heroicons/react/24/outline";

interface RemissionPrintModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (layout: "single" | "double") => void;
    action?: "print" | "pdf";
    loading?: boolean;
}

export default function RemissionPrintModal({
    isOpen,
    onClose,
    onConfirm,
    action,
    loading = false,
}: RemissionPrintModalProps) {
    const [layout, setLayout] = useState<"single" | "double">("single");

    const handleConfirm = () => {
        onConfirm(layout);
    };

    return (
        <Modal backdrop="blur" isOpen={isOpen} size="md" onClose={onClose}>
            <ModalContent>
                {(onClose) => (
                    <>
                        <ModalHeader className="flex items-center gap-2">
                            <DocumentArrowDownIcon className="w-5 h-5" />
                            <span>Opciones de PDF</span>
                        </ModalHeader>
                        <ModalBody>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="text-sm font-semibold mb-2">
                                        Diseño de página
                                    </h3>
                                    <RadioGroup
                                        value={layout}
                                        onValueChange={(value) =>
                                            setLayout(
                                                value as "single" | "double",
                                            )
                                        }
                                    >
                                        <Radio value="single">
                                            <div className="flex flex-col">
                                                <span className="font-medium">
                                                    Una copia por hoja
                                                </span>
                                                <span className="text-xs text-gray-500">
                                                    Formato horizontal
                                                    (landscape) - Tamaño
                                                    completo
                                                </span>
                                            </div>
                                        </Radio>
                                        <Radio value="double">
                                            <div className="flex flex-col">
                                                <span className="font-medium">
                                                    Dos copias por hoja
                                                </span>
                                                <span className="text-xs text-gray-500">
                                                    Formato vertical (portrait)
                                                    - Ahorro de papel
                                                </span>
                                            </div>
                                        </Radio>
                                    </RadioGroup>
                                </div>

                                <Divider />

                                <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                    <p className="text-xs text-gray-600 dark:text-gray-400">
                                        <strong>Nota:</strong> La remisión se
                                        ajustará automáticamente al tamaño de
                                        página seleccionado manteniendo la
                                        legibilidad.
                                    </p>
                                </div>
                            </div>
                        </ModalBody>
                        <ModalFooter>
                            <Button
                                color="default"
                                variant="flat"
                                onPress={onClose}
                            >
                                Cancelar
                            </Button>
                            <Button
                                color="primary"
                                isLoading={loading}
                                onPress={handleConfirm}
                            >
                                Descargar PDF
                            </Button>
                        </ModalFooter>
                    </>
                )}
            </ModalContent>
        </Modal>
    );
}
