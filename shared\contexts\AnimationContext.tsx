// Animation context for performance management
"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { MotionConfig } from "framer-motion";

interface AnimationContextType {
    reducedMotion: boolean;
    animationSpeed: number;
    enableAnimations: boolean;
    setAnimationSpeed: (speed: number) => void;
    toggleAnimations: () => void;
}

const AnimationContext = createContext<AnimationContextType>({
    reducedMotion: false,
    animationSpeed: 1,
    enableAnimations: true,
    setAnimationSpeed: () => {},
    toggleAnimations: () => {},
});

export function AnimationProvider({ children }: { children: React.ReactNode }) {
    const [reducedMotion, setReducedMotion] = useState(false);
    const [animationSpeed, setAnimationSpeed] = useState(1);
    const [enableAnimations, setEnableAnimations] = useState(true);

    // Detect user's motion preference
    useEffect(() => {
        const mediaQuery = window.matchMedia(
            "(prefers-reduced-motion: reduce)",
        );

        setReducedMotion(mediaQuery.matches);
        setEnableAnimations(!mediaQuery.matches);

        const handleChange = (e: MediaQueryListEvent) => {
            setReducedMotion(e.matches);
            setEnableAnimations(!e.matches);
        };

        mediaQuery.addEventListener("change", handleChange);

        return () => mediaQuery.removeEventListener("change", handleChange);
    }, []);

    const toggleAnimations = () => {
        setEnableAnimations((prev) => !prev);
    };

    return (
        <AnimationContext.Provider
            value={{
                reducedMotion,
                animationSpeed,
                enableAnimations,
                setAnimationSpeed,
                toggleAnimations,
            }}
        >
            <MotionConfig
                reducedMotion={reducedMotion ? "always" : "never"}
                transition={{
                    duration: enableAnimations ? 0.3 * animationSpeed : 0,
                }}
            >
                {children}
            </MotionConfig>
        </AnimationContext.Provider>
    );
}

export const useAnimation = () => useContext(AnimationContext);

// Performance monitoring hook
export function useAnimationPerformance() {
    const [fps, setFps] = useState(60);
    const [isPerformant, setIsPerformant] = useState(true);

    useEffect(() => {
        let frameCount = 0;
        let lastTime = performance.now();
        let animationFrameId: number;

        const measureFPS = () => {
            const currentTime = performance.now();

            frameCount++;

            if (currentTime >= lastTime + 1000) {
                setFps(
                    Math.round((frameCount * 1000) / (currentTime - lastTime)),
                );
                frameCount = 0;
                lastTime = currentTime;
            }

            animationFrameId = requestAnimationFrame(measureFPS);
        };

        measureFPS();

        return () => {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
        };
    }, []);

    useEffect(() => {
        setIsPerformant(fps >= 55); // Consider 55+ fps as performant
    }, [fps]);

    return { fps, isPerformant };
}
