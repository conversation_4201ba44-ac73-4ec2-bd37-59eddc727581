"use client";

import React, { useState } from "react";
import { Card, CardBody, Input, Button, Chip } from "@heroui/react";
import {
    MagnifyingGlassIcon,
    AdjustmentsHorizontalIcon,
    FunnelIcon,
} from "@heroicons/react/24/outline";

interface ClientControlsProps {
    statuses: Array<{
        id: string;
        name: string;
        color?: string | null;
    }>;
    importances: Array<{
        id: string;
        name: string;
        color?: string | null;
    }>;
}

export default function ClientControls({
    statuses,
    importances,
}: ClientControlsProps) {
    const [search, setSearch] = useState("");
    const [showFilters, setShowFilters] = useState(false);
    const [activeFilters, setActiveFilters] = useState({
        status: null as string | null,
        importance: null as string | null,
    });

    // Simplificamos para un primer MVP y evitar problemas con HeroUI
    return (
        <Card className="bg-white dark:bg-gray-800/50 border border-gray-100 dark:border-gray-700 shadow-sm">
            <CardBody className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                    {/* Búsqueda */}
                    <div className="flex-1">
                        <Input
                            classNames={{
                                base: "w-full",
                                inputWrapper: "bg-white dark:bg-gray-900/40",
                            }}
                            placeholder="Buscar notas..."
                            startContent={
                                <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                            }
                            value={search}
                            variant="bordered"
                            onChange={(e) => setSearch(e.target.value)}
                        />
                    </div>

                    {/* Botones para filtros */}
                    <div className="flex items-center gap-2">
                        <Button
                            className="whitespace-nowrap"
                            color="primary"
                            startContent={<FunnelIcon className="w-4 h-4" />}
                            variant="flat"
                            onClick={() => setShowFilters(!showFilters)}
                        >
                            Filtros
                        </Button>

                        <Button
                            className="whitespace-nowrap"
                            color="default"
                            startContent={
                                <AdjustmentsHorizontalIcon className="w-4 h-4" />
                            }
                            variant="flat"
                        >
                            Ordenar
                        </Button>
                    </div>
                </div>

                {/* Filtros desplegables (versión simplificada) */}
                {showFilters && (
                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        {/* Estados */}
                        <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Estado
                            </h4>
                            <div className="flex flex-wrap gap-2">
                                <Chip
                                    className="cursor-pointer"
                                    color="primary"
                                    variant={
                                        activeFilters.status === null
                                            ? "solid"
                                            : "flat"
                                    }
                                    onClick={() =>
                                        setActiveFilters((prev) => ({
                                            ...prev,
                                            status: null,
                                        }))
                                    }
                                >
                                    Todos
                                </Chip>

                                {statuses.map((status) => {
                                    // Obtener el color del status o usar un valor por defecto
                                    const statusColor =
                                        status.color || "#888888";

                                    return (
                                        <Chip
                                            key={status.id}
                                            className="cursor-pointer"
                                            style={{
                                                backgroundColor:
                                                    activeFilters.status ===
                                                    status.id
                                                        ? statusColor
                                                        : undefined,
                                                color:
                                                    activeFilters.status ===
                                                    status.id
                                                        ? "#fff"
                                                        : statusColor,
                                                borderColor: statusColor,
                                            }}
                                            variant={
                                                activeFilters.status ===
                                                status.id
                                                    ? "solid"
                                                    : "flat"
                                            }
                                            onClick={() =>
                                                setActiveFilters((prev) => ({
                                                    ...prev,
                                                    status: status.id,
                                                }))
                                            }
                                        >
                                            {status.name}
                                        </Chip>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Importancias */}
                        <div className="space-y-2">
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Importancia
                            </h4>
                            <div className="flex flex-wrap gap-2">
                                <Chip
                                    className="cursor-pointer"
                                    color="primary"
                                    variant={
                                        activeFilters.importance === null
                                            ? "solid"
                                            : "flat"
                                    }
                                    onClick={() =>
                                        setActiveFilters((prev) => ({
                                            ...prev,
                                            importance: null,
                                        }))
                                    }
                                >
                                    Todas
                                </Chip>

                                {importances.map((importance) => {
                                    // Obtener el color de importancia o usar un valor por defecto
                                    const importanceColor =
                                        importance.color || "#888888";

                                    return (
                                        <Chip
                                            key={importance.id}
                                            className="cursor-pointer"
                                            style={{
                                                backgroundColor:
                                                    activeFilters.importance ===
                                                    importance.id
                                                        ? importanceColor
                                                        : undefined,
                                                color:
                                                    activeFilters.importance ===
                                                    importance.id
                                                        ? "#fff"
                                                        : importanceColor,
                                                borderColor: importanceColor,
                                            }}
                                            variant={
                                                activeFilters.importance ===
                                                importance.id
                                                    ? "solid"
                                                    : "flat"
                                            }
                                            onClick={() =>
                                                setActiveFilters((prev) => ({
                                                    ...prev,
                                                    importance: importance.id,
                                                }))
                                            }
                                        >
                                            {importance.name}
                                        </Chip>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                )}
            </CardBody>
        </Card>
    );
}
