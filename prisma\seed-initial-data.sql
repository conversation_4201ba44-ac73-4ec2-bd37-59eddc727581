-- Script de inicialización de datos para LOHARI
-- Generado automáticamente el 29/01/2025
-- Este script inserta los datos iniciales necesarios para las tablas de configuración

-- =====================================
-- INSERTAR VALORES PARA ROLE
-- =====================================
INSERT INTO "Role" (id, name, "iconName", color, "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Administrador', 'ShieldCheckIcon', '#7E22CE', NOW(), NOW()),
(gen_random_uuid(), 'Empleado', 'UserIcon', '#0E7490', NOW(), NOW()),
(gen_random_uuid(), 'Contratista', 'UserGroupIcon', '#65A30D', NOW(), NOW()),
(gen_random_uuid(), 'Invitado', 'UserCircleIcon', '#9CA3AF', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- =====================================
-- INSERTAR VALORES PARA PACKINGSTATUS
-- =====================================
INSERT INTO "PackingStatus" (id, name, "iconName", color, "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'En progreso', 'ClockIcon', '#F59E0B', NOW(), NOW()),
(gen_random_uuid(), 'Entregado', 'CheckCircleIcon', '#16A34A', NOW(), NOW()),
(gen_random_uuid(), 'Rechazado', 'ExclamationCircleIcon', '#EF4444', NOW(), NOW()),
(gen_random_uuid(), 'Cancelado', 'XCircleIcon', '#6B7280', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- =====================================
-- INSERTAR VALORES PARA REJECTIONREASON
-- =====================================
INSERT INTO "RejectionReason" (id, name, "iconName", color, "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Calidad', 'ShieldExclamationIcon', '#EF4444', NOW(), NOW()),
(gen_random_uuid(), 'Defecto', 'ExclamationTriangleIcon', '#F97316', NOW(), NOW()),
(gen_random_uuid(), 'Retraso', 'ClockIcon', '#F59E0B', NOW(), NOW()),
(gen_random_uuid(), 'Otro', 'QuestionMarkCircleIcon', '#6B7280', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- =====================================
-- INSERTAR VALORES PARA NOTEIMPORTANCE
-- =====================================
INSERT INTO "NoteImportance" (id, name, "iconName", color, "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Bajo', 'InformationCircleIcon', '#3B82F6', NOW(), NOW()),
(gen_random_uuid(), 'Medio', 'ExclamationTriangleIcon', '#F59E0B', NOW(), NOW()),
(gen_random_uuid(), 'Alto', 'ExclamationTriangleIcon', '#EF4444', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- =====================================
-- INSERTAR VALORES PARA NOTESTATUS
-- =====================================INSERT INTO "NoteStatus" (id, name, "iconName", color, "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Pendiente', 'ClockIcon', '#F59E0B', NOW(), NOW()),
(gen_random_uuid(), 'En progreso', 'ArrowPathIcon', '#3B82F6', NOW(), NOW()),
(gen_random_uuid(), 'Completado', 'CheckCircleIcon', '#16A34A', NOW(), NOW()),
(gen_random_uuid(), 'Cancelado', 'XCircleIcon', '#6B7280', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- =====================================
-- INSERTAR VALORES PARA ORDERSTATUS
-- =====================================
INSERT INTO "OrderStatus" (id, name, "iconName", color, "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Nuevo', 'SparklesIcon', '#10B981', NOW(), NOW()),
(gen_random_uuid(), 'Recibido', 'InboxIcon', '#3B82F6', NOW(), NOW()),
(gen_random_uuid(), 'En producción', 'CogIcon', '#F59E0B', NOW(), NOW()),
(gen_random_uuid(), 'Control de calidad', 'CheckBadgeIcon', '#8B5CF6', NOW(), NOW()),
(gen_random_uuid(), 'Empaquetando', 'CubeIcon', '#14B8A6', NOW(), NOW()),
(gen_random_uuid(), 'Listo para entregar', 'TruckIcon', '#10B981', NOW(), NOW()),
(gen_random_uuid(), 'Entregado', 'CheckCircleIcon', '#16A34A', NOW(), NOW()),
(gen_random_uuid(), 'Rechazado', 'ExclamationCircleIcon', '#EF4444', NOW(), NOW()),
(gen_random_uuid(), 'Cancelado', 'XCircleIcon', '#6B7280', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- =====================================
-- VERIFICAR LOS DATOS INSERTADOS
-- =====================================
SELECT 'Role' as tabla, COUNT(*) as total FROM "Role"
UNION ALL
SELECT 'PackingStatus', COUNT(*) FROM "PackingStatus"
UNION ALL
SELECT 'RejectionReason', COUNT(*) FROM "RejectionReason"
UNION ALL
SELECT 'NoteImportance', COUNT(*) FROM "NoteImportance"
UNION ALL
SELECT 'NoteStatus', COUNT(*) FROM "NoteStatus"
UNION ALL
SELECT 'OrderStatus', COUNT(*) FROM "OrderStatus";