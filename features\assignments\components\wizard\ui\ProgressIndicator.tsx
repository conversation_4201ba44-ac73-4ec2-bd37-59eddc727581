"use client";

import { motion } from "framer-motion";

import { useWizard } from "@/app/dashboard/assignments/new/wizard-context";

interface ProgressIndicatorProps {
    className?: string;
}

export function ProgressIndicator({ className = "" }: ProgressIndicatorProps) {
    const { state } = useWizard();

    // Calcular progreso basado en campos completados
    const calculateProgress = (): number => {
        let progress = 0;
        const weights = {
            contractor: 25,
            orders: 25,
            quantities: 40,
            summary: 10,
        };

        // Contratista seleccionado
        if (state.contractor) {
            progress += weights.contractor;
        }

        // Órdenes seleccionadas
        if (state.selectedOrders.length > 0) {
            // Progreso parcial basado en número de órdenes (hasta 3)
            const orderProgress = Math.min(state.selectedOrders.length / 3, 1);

            progress += weights.orders * orderProgress;
        }

        // Cantidades asignadas
        if (state.assignments.length > 0) {
            // Progreso basado en validez de asignaciones
            const validAssignments = state.assignments.filter(
                (a) =>
                    a.quantity > 0 && !state.validationErrors[a.garmentSizeId],
            );
            const quantityProgress =
                validAssignments.length / Math.max(state.assignments.length, 1);

            progress += weights.quantities * quantityProgress;
        }

        // En resumen
        if (state.currentStep === "summary") {
            progress += weights.summary;
        }

        return Math.round(progress);
    };

    const progress = calculateProgress();

    // Determinar color basado en progreso
    const getProgressColor = () => {
        if (progress < 25) return "bg-gray-400";
        if (progress < 50) return "bg-blue-400";
        if (progress < 75) return "bg-blue-500";
        if (progress < 100) return "bg-blue-600";

        return "bg-green-500";
    };

    // Determinar sombra basada en hitos
    const getProgressShadow = () => {
        if (progress >= 100) return "shadow-xl";
        if (progress >= 75) return "shadow-lg";
        if (progress >= 50) return "shadow-md";
        if (progress >= 25) return "shadow-sm";

        return "";
    };

    return (
        <div className={`w-full ${className}`}>
            <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                    animate={{ width: `${progress}%` }}
                    className={`h-full transition-colors duration-500 ${getProgressColor()} ${getProgressShadow()}`}
                    initial={{ width: 0 }}
                    transition={{
                        duration: 0.5,
                        ease: "easeOut",
                    }}
                />
            </div>

            {/* Tooltip opcional al hover */}
            <div className="mt-1 text-center">
                <motion.span
                    key={progress}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-xs text-gray-500 dark:text-gray-400"
                    initial={{ opacity: 0, y: -5 }}
                >
                    {progress}% completado
                </motion.span>
            </div>
        </div>
    );
}
