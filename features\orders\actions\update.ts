"use server";

import { CONFIG_NAMES } from "@/constants/config-names";
import { getNoteImportanceByName } from "@/shared/lib/config-cache";
import { prisma } from "@/shared/lib/prisma";

import {
    ActionResponse,
    createErrorResponse,
    revalidateCache,
    parseDate,
    getCurrentUser,
} from "./utils";

/**
 * Actualiza una orden existente
 */
export async function updateOrder(
    id: string,
    data: {
        customerId: string;
        subCustomerId?: string | null;
        statusId: string;
        receivedDate: string;
        transferNumber?: string | null;
        cutOrder?: string | null;
        batch?: string | null;
        estimatedDeliveryDate?: string | null;
    },
): Promise<ActionResponse<any>> {
    try {
        // Validación básica de datos
        if (!data.customerId) {
            return {
                success: false,
                error: "El cliente es requerido",
            };
        }

        if (!data.statusId) {
            return {
                success: false,
                error: "El estado es requerido",
            };
        }

        if (!data.receivedDate) {
            return {
                success: false,
                error: "La fecha de recepción es requerida",
            };
        }

        // Verificar que la orden exista
        const existingOrder = await prisma.order.findUnique({
            where: { id },
        });

        if (!existingOrder) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${id}`,
            };
        }

        // Actualizar la orden en la base de datos
        const updatedOrder = await prisma.order.update({
            where: { id },
            data: {
                customerId: data.customerId,
                subCustomerId: data.subCustomerId || null,
                statusId: data.statusId,
                receivedDate: new Date(data.receivedDate),
                transferNumber: data.transferNumber || null,
                cutOrder: data.cutOrder || null,
                batch: data.batch || null,
                estimatedDeliveryDate: await parseDate(
                    data.estimatedDeliveryDate,
                ),
            },
        });

        // Revalidar rutas y tags relacionados con órdenes
        revalidateCache(id);

        return {
            success: true,
            data: updatedOrder,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Actualiza una orden con todos sus componentes (partidas, prendas, notas)
 */
export async function updateOrderWithDetails(
    id: string,
    data: {
        customerId: string;
        subCustomerId?: string | null;
        statusId: string;
        receivedDate: string;
        transferNumber?: string | null;
        cutOrder?: string | null;
        batch?: string | null;
        estimatedDeliveryDate?: string | null;
        // Datos para actualizar componentes relacionados
        parts?: { id?: string; code: string }[];
        garments?: {
            id?: string;
            modelId: string;
            colorId: string;
            sizes: { sizeId: string; quantity: number }[];
        }[];
        notes?: { content: string; statusId: string; importanceId?: string }[];
    },
): Promise<ActionResponse<any>> {
    try {
        // Iniciando actualización de orden con datos completos - comentario removido

        // Validación básica de datos obligatorios
        if (!data.customerId || !data.statusId || !data.receivedDate) {
            return {
                success: false,
                error: "Faltan datos básicos obligatorios (cliente, estado, fecha)",
            };
        }

        // Verificar que la orden exista
        const existingOrder = await prisma.order.findUnique({
            where: { id },
            include: {
                parts: true,
                garments: {
                    include: {
                        sizes: true,
                    },
                },
            },
        });

        if (!existingOrder) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${id}`,
            };
        }

        // Actualizar la información básica de la orden
        const updatedOrder = await prisma.order.update({
            where: { id },
            data: {
                customerId: data.customerId,
                subCustomerId: data.subCustomerId || null,
                statusId: data.statusId,
                receivedDate: new Date(data.receivedDate),
                transferNumber: data.transferNumber || null,
                cutOrder: data.cutOrder || null,
                batch: data.batch || null,
                estimatedDeliveryDate: await parseDate(
                    data.estimatedDeliveryDate,
                ),
            },
        });

        // Información básica actualizada para orden - comentario removido

        // Si se incluyen partidas, actualizar partidas
        if (data.parts) {
            // Obtener las partidas existentes
            const existingPartsIds = existingOrder.parts.map((part) => part.id);

            // Si hay partidas a actualizar, procesarlas
            for (const part of data.parts) {
                if (part.id) {
                    // Si la partida tiene ID, actualizarla
                    await prisma.orderPart.update({
                        where: { id: part.id },
                        data: { code: part.code },
                    });
                } else {
                    // Si no tiene ID, crear una nueva
                    await prisma.orderPart.create({
                        data: {
                            orderId: id,
                            code: part.code,
                        },
                    });
                }
            }

            // Determinar partidas a eliminar (las que existen pero no están en data.parts)
            const updatedPartsIds = data.parts
                .filter((p) => p.id)
                .map((p) => p.id!);
            const partsToDelete = existingPartsIds.filter(
                (partId) => !updatedPartsIds.includes(partId),
            );

            // Eliminar partidas que ya no están en la lista
            if (partsToDelete.length > 0) {
                await prisma.orderPart.deleteMany({
                    where: {
                        id: { in: partsToDelete },
                    },
                });
            }

            // Partidas actualizadas para orden - comentario removido
        }

        // Si se incluyen prendas, actualizar prendas
        if (data.garments) {
            // Obtener las prendas existentes con sus tallas
            const existingGarmentsIds = existingOrder.garments.map(
                (garment) => garment.id,
            );

            // Procesar cada prenda en los datos de actualización
            for (const garment of data.garments) {
                if (garment.id) {
                    // Si la prenda tiene ID, actualizarla
                    await prisma.garment.update({
                        where: { id: garment.id },
                        data: {
                            modelId: garment.modelId,
                            colorId: garment.colorId,
                        },
                    });

                    // Actualizar las tallas de esta prenda
                    const existingGarmentData = existingOrder.garments.find(
                        (g) => g.id === garment.id,
                    );

                    if (existingGarmentData) {
                        const existingSizesIds = existingGarmentData.sizes.map(
                            (s) => s.id,
                        );

                        // Procesar cada talla
                        for (const size of garment.sizes) {
                            // Buscar si esta talla ya existe para esta prenda
                            const existingSize = existingGarmentData.sizes.find(
                                (s) => s.sizeId === size.sizeId,
                            );

                            if (existingSize) {
                                // Actualizar cantidad de talla existente
                                await prisma.garmentSize.update({
                                    where: { id: existingSize.id },
                                    data: { totalQuantity: size.quantity },
                                });
                            } else {
                                // Crear nueva talla para esta prenda
                                await prisma.garmentSize.create({
                                    data: {
                                        garmentId: garment.id,
                                        sizeId: size.sizeId,
                                        totalQuantity: size.quantity,
                                        usedQuantity: 0,
                                    },
                                });
                            }
                        }

                        // Eliminar tallas que ya no están en la lista actualizada
                        const updatedSizesIds = garment.sizes.map(
                            (s) => s.sizeId,
                        );
                        const sizesToDelete = existingGarmentData.sizes.filter(
                            (s) => !updatedSizesIds.includes(s.sizeId),
                        );

                        if (sizesToDelete.length > 0) {
                            await prisma.garmentSize.deleteMany({
                                where: {
                                    id: { in: sizesToDelete.map((s) => s.id) },
                                },
                            });
                        }
                    }
                } else {
                    // Si no tiene ID, crear una nueva prenda con sus tallas
                    const newGarment = await prisma.garment.create({
                        data: {
                            orderId: id,
                            modelId: garment.modelId,
                            colorId: garment.colorId,
                            sizes: {
                                create: garment.sizes.map((size) => ({
                                    sizeId: size.sizeId,
                                    totalQuantity: size.quantity,
                                    usedQuantity: 0,
                                })),
                            },
                        },
                    });
                }
            }

            // Determinar prendas a eliminar (las que existen pero no están en data.garments)
            const updatedGarmentsIds = data.garments
                .filter((g) => g.id)
                .map((g) => g.id!);
            const garmentsToDelete = existingGarmentsIds.filter(
                (garmentId) => !updatedGarmentsIds.includes(garmentId),
            );

            // Eliminar prendas que ya no están en la lista (y sus tallas se eliminarán en cascada)
            if (garmentsToDelete.length > 0) {
                await prisma.garment.deleteMany({
                    where: {
                        id: { in: garmentsToDelete },
                    },
                });
            }
        }

        // Si se incluyen notas, actualizar notas
        if (data.notes) {
            // Procesando notas para orden - comentario removido

            // Obtener el usuario actual para la autoría de las notas
            const user = await getCurrentUser();

            if (!user || !user.id) {
                // No se pudo obtener el usuario actual para las notas - comentario removido
            } else {
                // Para cada nota, crear en la base de datos con el schema correcto
                for (const note of data.notes) {
                    if (note.content.trim()) {
                        // Obtener ID de importancia por defecto si no se proporciona
                        let importanceId = note.importanceId;

                        if (!importanceId) {
                            const mediumImportance =
                                await getNoteImportanceByName(
                                    CONFIG_NAMES.noteImportance.MEDIUM,
                                );

                            if (!mediumImportance) {
                                throw new Error(
                                    "No se encontró la importancia 'Medio' en la base de datos",
                                );
                            }
                            importanceId = mediumImportance.id;
                        }

                        await prisma.note.create({
                            data: {
                                content: note.content.trim(),
                                statusId: note.statusId,
                                orderId: id,
                                authorId: user.id,
                                importanceId: importanceId as string,
                            },
                        });
                    }
                }
                // Notas procesadas para orden - comentario removido
            }
        }

        // Revalidar rutas y tags relacionados con órdenes
        revalidateCache(id);

        // Obtener la orden actualizada con todos sus componentes
        const completeOrder = await prisma.order.findUnique({
            where: { id },
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
                notes: {
                    include: {
                        status: true,
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
        });

        // Orden actualizada completamente con éxito - comentario removido

        return {
            success: true,
            data: completeOrder,
        };
    } catch (error) {
        // REMOVED: console.error(`[updateOrderWithDetails] Error:`, error);

        return createErrorResponse(error);
    }
}
