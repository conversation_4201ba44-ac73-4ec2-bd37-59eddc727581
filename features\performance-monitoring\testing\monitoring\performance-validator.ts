interface ValidationResult {
    passed: boolean;
    improvements: {
        cacheHitRate: number;
        responseTime: number;
        loadTime: number;
    };
    failures: string[];
    warnings: string[];
    successes: string[];
}

export class PerformanceValidator {
    private readonly TARGETS = {
        cacheHitRate: 80, // 80% minimum
        responseTime: 200, // 200ms p95
        loadTime: 2000, // 2 seconds initial load
        bundleReduction: 20, // 20% reduction
    };

    async validate(baseline: any, current: any): Promise<ValidationResult> {
        const result: ValidationResult = {
            passed: true,
            improvements: {
                cacheHitRate: 0,
                responseTime: 0,
                loadTime: 0,
            },
            failures: [],
            warnings: [],
            successes: [],
        };

        // Validate cache hit rate
        if (current.hitRate >= this.TARGETS.cacheHitRate) {
            result.successes.push(
                `✓ Cache hit rate: ${current.hitRate.toFixed(1)}% (target: ${this.TARGETS.cacheHitRate}%)`,
            );
        } else {
            result.failures.push(
                `✗ Cache hit rate: ${current.hitRate.toFixed(1)}% (target: ${this.TARGETS.cacheHitRate}%)`,
            );
            result.passed = false;
        }

        // Calculate improvements
        result.improvements.cacheHitRate = current.hitRate - baseline.hitRate;
        result.improvements.responseTime =
            ((baseline.avgResponseTime - current.avgResponseTime) /
                baseline.avgResponseTime) *
            100;

        return result;
    }
}
