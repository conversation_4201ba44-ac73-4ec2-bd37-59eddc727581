#!/bin/bash
# Script de instalación automática para Memory Bank System

echo "🚀 Instalando Memory Bank System en el proyecto lohari..."

# Crear estructura de directorios
echo "📁 Creando estructura de directorios..."
mkdir -p .cursor/rules/isolation_rules/{core,modes,visual-maps,Level1,Level2,Level3,Level4,Phases}
mkdir -p .cursor/custom_modes
mkdir -p memory-bank

# Crear archivos Memory Bank iniciales
echo "📝 Creando archivos Memory Bank..."
cat > memory-bank/tasks.md << 'EOF'
# Tasks - Source of Truth

## Active Tasks
_No active tasks yet_

## Completed Tasks
_No completed tasks yet_

## Task Template
```
- [ ] Task Name
  - Description: 
  - Complexity: Level [1-4]
  - Status: [Pending/In Progress/Complete]
  - Mode: [VAN/PLAN/CREATIVE/IMPLEMENT/REFLECT/ARCHIVE]
```
EOF

cat > memory-bank/activeContext.md << 'EOF'
# Active Context

## Current Mode
_Not initialized_

## Current Task
_No active task_

## Context
_Context will be maintained here_
EOF

cat > memory-bank/progress.md << 'EOF'
# Progress Tracking

## Implementation Status
_No implementation in progress_

## Completed Implementations
_None yet_
EOF

echo "✅ Instalación básica completada!"
echo ""
echo "📋 SIGUIENTE PASO: Configurar los 6 modos personalizados en Cursor"
echo ""
echo "Por favor, sigue las instrucciones en:"
echo "📄 .cursor/INSTRUCCIONES_INSTALACION.md"
echo ""
echo "Los archivos de instrucciones para cada modo están en:"
echo "📁 .cursor/custom_modes/"
