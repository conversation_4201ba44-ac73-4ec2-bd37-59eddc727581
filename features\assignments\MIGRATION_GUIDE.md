# Guía de Migración - Feature Assignments

## Estado Actual
- ✅ Actions ya migradas
- ✅ Hooks básicos migrados
- ⏳ Components pendientes de migrar
- ⏳ Types y schemas por crear

## Pasos para completar la migración:

### 1. Ejecutar el script de migración
```bash
node scripts/migrate-assignments.js
```

### 2. Actualizar imports en los archivos migrados

#### En los componentes del wizard:
```typescript
// Cambiar de:
import { useOptimisticValidation } from '../hooks/useOptimisticValidation';
import { Button } from '@/components/ui/button';

// A:
import { useOptimisticValidation } from '@/features/assignments/hooks';
import { Button } from '@/shared/components/ui/button';
```

#### En los steps:
```typescript
// Cambiar de:
import { validateOptimistic } from '@/lib/actions/assignments';

// A:
import { validateOptimistic } from '@/features/assignments/actions';
```

### 3. Crear schemas con Zod

```typescript
// features/assignments/schemas/assignmentSchema.ts
import { z } from 'zod';

export const AssignmentItemSchema = z.object({
  orderId: z.string().cuid(),
  quantity: z.number().positive(),
  colorId: z.string().cuid(),
  sizeId: z.string().cuid(),
});

export const CreateAssignmentSchema = z.object({
  contractorId: z.string().cuid(),
  items: z.array(AssignmentItemSchema).min(1),
  notes: z.string().optional(),
  estimatedDeliveryDate: z.date(),
});

export const AssignmentFilterSchema = z.object({
  contractorId: z.string().optional(),
  status: z.enum(['pending', 'in_progress', 'completed']).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
});
```

### 4. Crear types específicos

```typescript
// features/assignments/types/assignment.types.ts
import { z } from 'zod';
import { CreateAssignmentSchema } from '../schemas/assignmentSchema';

export type CreateAssignmentInput = z.infer<typeof CreateAssignmentSchema>;

export interface AssignmentWithRelations {
  id: string;
  contractorId: string;
  contractor: {
    id: string;
    name: string;
  };
  items: AssignmentItem[];
  status: AssignmentStatus;
  createdAt: Date;
  updatedAt: Date;
}

export type AssignmentStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';
```

### 5. Mover hooks del wizard

```bash
# Mover manualmente los hooks de:
components/assignments/wizard/hooks/
# A:
features/assignments/hooks/
```

### 6. Actualizar el archivo principal de exports

```typescript
// features/assignments/index.ts
// Exportar todo lo público de la feature
export * from './components';
export * from './hooks';
export * from './actions';
export * from './types/assignment.types';
export * from './schemas/assignmentSchema';
```

### 7. Limpiar estructura antigua

Una vez verificado que todo funciona:
```bash
# Eliminar carpeta antigua
rm -rf components/assignments
```

## Estructura de imports recomendada:

```typescript
// En páginas de Next.js
import { 
  EnhancedAssignmentWizard,
  useAssignments 
} from '@/features/assignments';

// Entre features
import { useOrders } from '@/features/orders';

// Componentes compartidos
import { DataTable } from '@/shared/components/tables';
```

## Verificación post-migración:

- [ ] Todos los archivos migrados
- [ ] Imports actualizados
- [ ] Types y schemas creados
- [ ] Tests funcionando
- [ ] Build sin errores
- [ ] Carpeta antigua eliminada
