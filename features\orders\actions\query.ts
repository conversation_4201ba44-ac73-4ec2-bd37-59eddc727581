"use server";

import { prisma } from "@/shared/lib/prisma";

import { ActionResponse, createErrorResponse } from "./utils";

/**
 * Obtiene la lista de órdenes con filtros y paginación
 */
export async function getOrders(
    options: {
        search?: string;
        statusId?: string;
        customerId?: string;
        contractorId?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
): Promise<ActionResponse<{ orders: any[]; pagination: any }>> {
    try {
        // Configuración de parámetros por defecto
        const page = options.page || 1;
        const perPage = options.perPage || 25;
        const orderBy = options.orderBy || "createdAt";
        const order = options.order || "desc";

        // Construir el objeto de filtros para Prisma
        const where: any = {};

        // Filtro por búsqueda
        if (options.search) {
            where.OR = [
                {
                    transferNumber: {
                        contains: options.search,
                        mode: "insensitive",
                    },
                },
                { cutOrder: { contains: options.search, mode: "insensitive" } },
                { batch: { contains: options.search, mode: "insensitive" } },
                {
                    customer: {
                        name: { contains: options.search, mode: "insensitive" },
                    },
                },
            ];
        }

        // Filtro por estado
        if (options.statusId) {
            where.statusId = options.statusId;
        }

        // Filtro por cliente
        if (options.customerId) {
            where.customerId = options.customerId;
        }

        // Filtro por contratista
        if (options.contractorId) {
            where.assignments = {
                some: {
                    contractorId: options.contractorId,
                },
            };
        }

        // Calcular total de registros
        const total = await prisma.order.count({ where });

        // Calcular total de páginas
        const lastPage = Math.ceil(total / perPage);

        // Ejecutar consulta principal con paginación
        const orders = await prisma.order.findMany({
            where,
            orderBy: {
                [orderBy]: order,
            },
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
                assignments: {
                    include: {
                        contractor: true,
                    },
                },
                _count: {
                    select: {
                        garments: true,
                        notes: true,
                        packings: true,
                        assignments: true,
                    },
                },
            },
            skip: (page - 1) * perPage,
            take: perPage,
        });

        return {
            success: true,
            data: {
                orders,
                pagination: {
                    total,
                    currentPage: page,
                    lastPage,
                },
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene una orden por su ID
 */
export async function getOrder(id: string): Promise<ActionResponse<any>> {
    try {
        const order = await prisma.order.findUnique({
            where: { id },
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                parts: true,
                garments: {
                    include: {
                        model: true,
                        color: true,
                        sizes: {
                            include: {
                                size: true,
                            },
                        },
                    },
                },
                notes: {
                    include: {
                        status: true,
                        // Incluir datos del usuario que creó la nota
                        // Si el esquema no tiene este campo, eliminar esta parte
                        author: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                image: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                },
                assignments: {
                    include: {
                        contractor: true,
                        garmentSize: {
                            include: {
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                                size: true,
                            },
                        },
                    },
                },
            },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${id}`,
            };
        }

        return {
            success: true,
            data: order,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene datos necesarios para el formulario de órdenes
 */
export async function getFormData(): Promise<ActionResponse<any>> {
    try {
        // Obtener todos los clientes
        const customers = await prisma.customer.findMany({
            orderBy: {
                name: "asc",
            },
        });

        // Obtener todos los estados de órdenes
        const orderStatuses = await prisma.orderStatus.findMany({
            orderBy: {
                name: "asc",
            },
        });

        // Obtener todos los modelos
        const models = await prisma.garmentModel.findMany({
            orderBy: {
                id: "asc",
            },
        });

        // Obtener todos los colores
        const colors = await prisma.color.findMany({
            orderBy: {
                name: "asc",
            },
        });

        // Obtener todas las tallas
        const sizes = await prisma.size.findMany({
            orderBy: {
                code: "asc",
            },
        });

        // Obtener todos los estados de notas
        const noteStatuses = await prisma.noteStatus.findMany({
            orderBy: {
                name: "asc",
            },
        });

        // Obtener todas las importancias de notas
        const noteImportances = await prisma.noteImportance.findMany({
            orderBy: {
                name: "asc",
            },
        });

        return {
            success: true,
            data: {
                customers,
                orderStatuses,
                models,
                colors,
                sizes,
                noteStatuses,
                noteImportances,
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene los posibles estados de una orden
 */
export async function getOrderStatuses(): Promise<
    ActionResponse<{ statuses: any[] }>
> {
    try {
        const statuses = await prisma.orderStatus.findMany({
            orderBy: {
                name: "asc",
            },
        });

        return {
            success: true,
            data: {
                statuses,
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene las órdenes próximas a entregar (para dashboard)
 */
export async function getUpcomingOrders(
    days: number = 7,
): Promise<ActionResponse<{ orders: any[] }>> {
    try {
        // Calcular la fecha limite (hoy + días especificados)
        const today = new Date();
        const limitDate = new Date();

        limitDate.setDate(today.getDate() + days);

        // Obtener órdenes con fecha de entrega entre hoy y la fecha límite
        const orders = await prisma.order.findMany({
            where: {
                estimatedDeliveryDate: {
                    gte: today,
                    lte: limitDate,
                },
                // Solo órdenes que no estén en estado "entregada" o similar
                status: {
                    name: {
                        not: {
                            in: ["Entregada", "Cancelada"],
                        },
                    },
                },
            },
            orderBy: {
                estimatedDeliveryDate: "asc",
            },
            include: {
                customer: true,
                subCustomer: true,
                status: true,
                parts: true,
            },
            take: 10, // Limitar a 10 resultados
        });

        return {
            success: true,
            data: {
                orders,
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene las tendencias de órdenes agrupadas por mes y estado
 */
export async function getOrderTrends(
    options: {
        startDate?: Date;
        endDate?: Date;
    } = {},
): Promise<ActionResponse<any>> {
    try {
        // Configurar fechas por defecto si no se proporcionan
        const endDate = options.endDate || new Date();
        const startDate =
            options.startDate ||
            new Date(endDate.getFullYear(), endDate.getMonth() - 11, 1);

        // Obtener todas las órdenes en el rango de fechas
        const orders = await prisma.order.findMany({
            where: {
                createdAt: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            select: {
                createdAt: true,
                statusId: true,
                status: {
                    select: {
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: "asc",
            },
        });

        // Agrupar órdenes por mes y estado
        const monthlyData: { [key: string]: { [key: string]: number } } = {};

        orders.forEach((order) => {
            const monthKey = order.createdAt.toISOString().slice(0, 7); // YYYY-MM
            const statusName = order.status.name;

            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = {};
            }

            if (!monthlyData[monthKey][statusName]) {
                monthlyData[monthKey][statusName] = 0;
            }

            monthlyData[monthKey][statusName]++;
        });

        // Convertir a formato para Recharts
        const trendData = Object.entries(monthlyData)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([month, statuses]) => ({
                month,
                ...statuses,
            }));

        return {
            success: true,
            data: trendData,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene todos los datos necesarios para editar una orden (formulario + orden existente)
 */
export async function getOrderEditData(orderId: string): Promise<
    ActionResponse<{
        formData: any;
        orderData: any;
    }>
> {
    try {
        // Obtener datos del formulario
        const formDataResponse = await getFormData();

        if (!formDataResponse.success) {
            return {
                success: false,
                error:
                    formDataResponse.error ||
                    "Error al cargar datos del formulario",
            };
        }

        // Obtener datos de la orden
        const orderResponse = await getOrder(orderId);

        if (!orderResponse.success) {
            return {
                success: false,
                error:
                    orderResponse.error || "Error al cargar datos de la orden",
            };
        }

        return {
            success: true,
            data: {
                formData: formDataResponse.data,
                orderData: orderResponse.data,
            },
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}

/**
 * Obtiene datos básicos de una orden por su ID (optimizado para componentes de resumen)
 */
export async function getOrderBasicData(id: string): Promise<
    ActionResponse<{
        id: string;
        cutOrder: string | null;
        parts?: any[];
    }>
> {
    try {
        const order = await prisma.order.findUnique({
            where: { id },
            select: {
                id: true,
                cutOrder: true,
                parts: {
                    select: {
                        id: true,
                        code: true,
                        createdAt: true,
                    },
                },
            },
        });

        if (!order) {
            return {
                success: false,
                error: `No se encontró la orden con ID ${id}`,
            };
        }

        return {
            success: true,
            data: order,
        };
    } catch (error) {
        return createErrorResponse(error);
    }
}
