# Módulo Remissions

Este módulo gestiona las remisiones (documentos de entrega) para las asignaciones de producción en el sistema Lohari.

## Estructura

```
remissions/
├── actions/        # Server actions para operaciones de remisiones
├── components/     # Componentes React para remisiones
├── hooks/          # Custom hooks para manejo de estado
├── schemas/        # Esquemas de validación (pendiente)
├── types/          # Definiciones de TypeScript
└── utils/          # Utilidades auxiliares
```

## API Pública

### Hooks
- `useRemission(id)` - Obtener remisión por ID
- `useRemissionByFolio(folio)` - Obtener remisión por folio
- `useRemissions(filters)` - Listar remisiones con filtros

### Componentes
- `AssignmentRemission` - Componente para crear/gestionar remisiones
- `RemissionDocument` - Documento de remisión para visualizar/imprimir
- `RemissionMigration` - Herramienta de migración de datos

### Types
- `CreateRemissionData` - Datos para crear remisión
- `RemissionFilters` - Filtros para búsqueda
- `RemissionItem` - Item individual de remisión
- `OrderDetails` - Detalles de orden en remisión

## Uso

```typescript
import { useRemission } from '@/features/remissions';
import { RemissionDocument } from '@/features/remissions';

// En un componente
const { data: remission, isLoading } = useRemission(remissionId);
```

## Server Actions

Las acciones del servidor deben importarse directamente:

```typescript
import { createRemission, markRemissionPrinted } from '@/features/remissions/actions';
```

## Funcionalidades

- Generación automática de folios únicos
- Creación de remisiones desde asignaciones
- Visualización e impresión de documentos
- Historial de acciones
- Migración de datos legacy

## Notas de Migración

Este módulo consolida funcionalidad que anteriormente estaba dispersa entre:
- `/features/orders/actions/remissions/`
- `/features/assignments/components/remission/`

Las rutas en `/app/dashboard/remissions/` ahora importan desde este módulo unificado.