/**
 * Utilidades para el manejo de fechas en la aplicación
 * Enfoque simple y directo para evitar problemas de zona horaria
 */

/**
 * Formatea una fecha como YYYY-MM-DD en la zona horaria local
 * @param date Fecha a formatear
 * @returns Fecha formateada como YYYY-MM-DD
 */
export function formatAsYYYYMMDD(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
}

/**
 * Formatea una fecha como DD/MM/YYYY en la zona horaria local
 * @param date Fecha a formatear
 * @returns Fecha formateada como DD/MM/YYYY
 */
export function formatAsDDMMYYYY(date: Date): string {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
}

/**
 * Formatea una fecha como DD MMM en la zona horaria local
 * @param date Fecha a formatear
 * @returns Fecha formateada como DD MMM (ej: 07 abr)
 */
export function formatAsDDMMM(date: Date): string {
    const day = String(date.getDate()).padStart(2, "0");
    const months = [
        "ene",
        "feb",
        "mar",
        "abr",
        "may",
        "jun",
        "jul",
        "ago",
        "sep",
        "oct",
        "nov",
        "dic",
    ];
    const month = months[date.getMonth()];

    return `${day} ${month}`;
}

/**
 * Parsea una fecha desde un string en formato YYYY-MM-DD o Date
 * @param dateInput Fecha en formato string o Date
 * @returns Date o null si no es válida
 */
export function parseDate(
    dateInput: string | Date | null | undefined,
): Date | null {
    if (!dateInput) return null;

    try {
        // Si ya es un objeto Date
        if (dateInput instanceof Date) {
            return isNaN(dateInput.getTime()) ? null : new Date(dateInput);
        }

        // Si es string, intentar parsearlo
        if (typeof dateInput === "string") {
            // Para formato ISO YYYY-MM-DD
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
                const [year, month, day] = dateInput.split("-").map(Number);

                // Crear fecha en zona horaria local (mes es 0-indexed en JS)
                return new Date(year, month - 1, day);
            }

            // Para otros formatos, usar el constructor de Date
            const date = new Date(dateInput);

            return isNaN(date.getTime()) ? null : date;
        }

        return null;
    } catch (error) {
        // REMOVED: console.error("Error parsing date:", error);

        return null;
    }
}

/**
 * Compara dos fechas para determinar si son el mismo día
 * @param date1 Primera fecha
 * @param date2 Segunda fecha
 * @returns true si son el mismo día, false en caso contrario
 */
export function isSameDay(date1: Date, date2: Date): boolean {
    return (
        date1.getFullYear() === date2.getFullYear() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getDate() === date2.getDate()
    );
}

/**
 * Determina si una fecha es hoy
 * @param date Fecha a comprobar
 * @returns true si es hoy, false en caso contrario
 */
export function isToday(date: Date): boolean {
    return isSameDay(date, new Date());
}

/**
 * Determina si una fecha es mañana
 * @param date Fecha a comprobar
 * @returns true si es mañana, false en caso contrario
 */
export function isTomorrow(date: Date): boolean {
    const tomorrow = new Date();

    tomorrow.setDate(tomorrow.getDate() + 1);

    return isSameDay(date, tomorrow);
}

/**
 * Determina si una fecha es ayer
 * @param date Fecha a comprobar
 * @returns true si es ayer, false en caso contrario
 */
export function isYesterday(date: Date): boolean {
    const yesterday = new Date();

    yesterday.setDate(yesterday.getDate() - 1);

    return isSameDay(date, yesterday);
}

/**
 * Calcula la diferencia en días entre dos fechas
 * @param date1 Primera fecha
 * @param date2 Segunda fecha (por defecto: hoy)
 * @returns Número de días de diferencia (positivo si date1 > date2, negativo si date1 < date2)
 */
export function getDaysDifference(
    date1: Date,
    date2: Date = new Date(),
): number {
    // Normalizar las fechas para ignorar la hora
    const normalizedDate1 = new Date(
        date1.getFullYear(),
        date1.getMonth(),
        date1.getDate(),
    );
    const normalizedDate2 = new Date(
        date2.getFullYear(),
        date2.getMonth(),
        date2.getDate(),
    );

    // Calcular diferencia en milisegundos y convertir a días
    const diffTime = normalizedDate1.getTime() - normalizedDate2.getTime();

    return Math.round(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Genera un texto relativo para una fecha (ej: "Hoy", "Mañana", "En 3 días")
 * @param date Fecha para la que generar el texto
 * @param type Tipo de fecha (recibida o estimada)
 * @returns Texto relativo
 */
export function getRelativeText(
    date: Date,
    type: "received" | "estimated" = "estimated",
): string {
    if (isToday(date)) {
        return type === "estimated" ? "Para hoy" : "Hoy";
    }

    if (isTomorrow(date)) {
        return type === "estimated" ? "Para mañana" : "Mañana";
    }

    if (isYesterday(date)) {
        return "Ayer";
    }

    const diffDays = getDaysDifference(date);

    if (diffDays < 0) {
        // Fecha pasada
        const days = Math.abs(diffDays);

        return type === "estimated"
            ? `Atrasada ${days} día${days !== 1 ? "s" : ""}`
            : `Hace ${days} día${days !== 1 ? "s" : ""}`;
    }

    if (diffDays > 0 && diffDays <= 7) {
        // Próximos días (hasta una semana)
        return `En ${diffDays} día${diffDays !== 1 ? "s" : ""}`;
    }

    // Más de una semana
    if (diffDays <= 30) {
        const weeks = Math.floor(diffDays / 7);

        return `En ${weeks} semana${weeks !== 1 ? "s" : ""}`;
    }

    // Más de un mes
    const months = Math.floor(diffDays / 30);

    return `En ${months} mes${months !== 1 ? "es" : ""}`;
}

/**
 * Obtiene el tipo de estado de una fecha (pasado, hoy, mañana, próximo, futuro)
 * @param date Fecha a evaluar
 * @returns Tipo de estado
 */
export function getDateStatus(
    date: Date,
): "past" | "today" | "tomorrow" | "soon" | "future" {
    if (isToday(date)) return "today";
    if (isTomorrow(date)) return "tomorrow";

    const diffDays = getDaysDifference(date);

    if (diffDays < 0) return "past";
    if (diffDays > 0 && diffDays <= 7) return "soon";

    return "future";
}

/**
 * Obtiene las clases CSS para un chip de fecha según su estado
 * @param status Tipo de estado de la fecha
 * @param type Tipo de fecha (recibida o estimada)
 * @returns Objeto con clases CSS para el chip
 */
export function getDateStatusClasses(
    status: "past" | "today" | "tomorrow" | "soon" | "future",
    type: "received" | "estimated" = "estimated",
): { bgClass: string; textClass: string } {
    // Para fechas de entrega estimada
    if (type === "estimated") {
        switch (status) {
            case "past":
                return {
                    bgClass:
                        "bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-800",
                    textClass: "text-red-700 dark:text-red-300",
                };
            case "today":
                return {
                    bgClass:
                        "bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-800",
                    textClass: "text-amber-700 dark:text-amber-300",
                };
            case "tomorrow":
                return {
                    bgClass:
                        "bg-orange-50 dark:bg-orange-900/30 border-orange-200 dark:border-orange-800",
                    textClass: "text-orange-700 dark:text-orange-300",
                };
            case "soon":
                return {
                    bgClass:
                        "bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800",
                    textClass: "text-blue-700 dark:text-blue-300",
                };
            case "future":
            default:
                return {
                    bgClass:
                        "bg-indigo-50 dark:bg-indigo-900/30 border-indigo-200 dark:border-indigo-800",
                    textClass: "text-indigo-700 dark:text-indigo-300",
                };
        }
    }

    // Para fechas de recepción
    switch (status) {
        case "today":
            return {
                bgClass:
                    "bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-800",
                textClass: "text-green-700 dark:text-green-300",
            };
        case "past":
            return {
                bgClass:
                    "bg-violet-50 dark:bg-violet-900/30 border-violet-200 dark:border-violet-800",
                textClass: "text-violet-700 dark:text-violet-300",
            };
        default:
            return {
                bgClass:
                    "bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700",
                textClass: "text-gray-700 dark:text-gray-300",
            };
    }
}
