"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Badge,
    Divider,
    useDisclosure,
} from "@heroui/react";
import {
    ArrowLeftIcon,
    UserGroupIcon,
    CubeIcon,
    DocumentTextIcon,
    ChartBarIcon,
} from "@heroicons/react/24/outline";

import { DashboardLayout } from "@/shared/components/dashboard";
import {
    getContractorsForSelector,
    getOrdersForSelector,
    createAssignments,
} from "@/features/assignments/actions";
import { getContractorMetrics } from "@/features/contractors/actions";

import { showToast } from "./utils/toast";

// Hooks personalizados
import { useAutosave } from "./hooks/useAutosave";
import { useOrderFilters, type OrderFilters } from "./hooks/useOrderFilters";

// Componentes
import { ContractorSelector } from "./components/ContractorSelector";
import { ToastContainer } from "./components/ToastContainer";
import { OrdersSelector } from "./components/OrdersSelector";
import { QuantityAssigner } from "./components/QuantityAssigner";
import { AssignmentSummary } from "./components/AssignmentSummary";
import { DraftIndicator } from "./components/DraftIndicator";
import { OrderFiltersPanel } from "./components/OrderFiltersPanel";
import { DraftRestoreModal } from "./components/DraftRestoreModal";

interface FormData {
    contractorId: string | null;
    selectedOrders: string[];
    quantities: Record<string, Record<string, number>>; // orderId -> garmentSizeId -> quantity
}

interface Contractor {
    id: string;
    name: string;
    email?: string | null;
    phone?: string | null;
    active?: boolean;
}

interface Order {
    id: string;
    code?: string | null;
    cutOrder?: string | null;
    transferNumber?: string | null;
    batch?: string | null;
    status: {
        id: string;
        name: string;
        iconName?: string | null;
        color?: string | null;
    };
    customer?: {
        id: string;
        name: string;
    } | null;
    estimatedDeliveryDate?: string | Date | null;
    deliveryDate?: string | Date | null;
    receivedDate?: string | Date | null;
    _count?: { garments: number };
    parts?: Array<{ id: string; [key: string]: any }>;
    garments?: Array<{
        id?: string;
        model: {
            id: string;
            code?: string | null;
            name?: string | null;
            description?: string | null;
        };
        color: {
            id?: string;
            name?: string | null;
            hexCode?: string | null;
        };
        sizes: Array<{
            id?: string;
            size: {
                id?: string;
                code: string;
            };
            totalQuantity: number;
            usedQuantity: number;
        }>;
    }>;
}

interface ContractorMetrics {
    contractorId: string;
    contractorName: string;
    activeOrders: number;
    lastDeliveryDate: Date | null;
    daysSinceLastDelivery: number | null;
    totalCompletedPieces: number;
    activeAssignments: number;
}

interface DraftData {
    data: FormData & { filters?: OrderFilters };
    timestamp: number;
    version: number;
    restore?: () => void;
    discard?: () => void;
    contractorName?: string;
    ordersInfo?: Array<{
        id: string;
        cutOrder?: string;
        code?: string;
        customerName?: string;
    }>;
}

interface Suggestion {
    value: string;
    metadata?: {
        orderId?: string;
        recentOrderId?: string;
    };
}

export default function UnifiedNewAssignmentPage() {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [contractors, setContractors] = useState<Contractor[]>([]);
    const [orders, setOrders] = useState<Order[]>([]);
    const [selectedContractorMetrics, setSelectedContractorMetrics] =
        useState<ContractorMetrics | null>(null);
    const [validationErrors, setValidationErrors] = useState<
        Record<string, string>
    >({});

    // Modal para restaurar borrador
    const {
        isOpen: isDraftModalOpen,
        onOpen: openDraftModal,
        onClose: closeDraftModal,
    } = useDisclosure();
    const [pendingDraft, setPendingDraft] = useState<DraftData | null>(null);
    const [draftHandled, setDraftHandled] = useState(false);

    // Estado del formulario
    const [formData, setFormData] = useState<FormData>({
        contractorId: null,
        selectedOrders: [],
        quantities: {},
    });

    // Sistema de filtros
    const {
        filters,
        updateFilter,
        clearFilters,
        filteredOrders,
        activeFiltersCount,
    } = useOrderFilters(orders);

    // Auto-guardado
    const { lastSaved, isRestored, saveDraft, clearDraft } = useAutosave(
        "assignment-draft",
        {
            ...formData,
            filters,
        },
        {
            shouldSave: (data) => {
                // Solo guardar si tiene contratista y al menos una orden seleccionada
                return !!(
                    data.contractorId &&
                    data.selectedOrders &&
                    data.selectedOrders.length > 0
                );
            },
            onDraftFound: (draft, restore, discard) => {
                // Guardar el draft temporalmente
                setPendingDraft({
                    ...draft,
                    restore,
                    discard,
                });

                // Si los datos ya están cargados, mostrar el modal inmediatamente
                if (contractors.length > 0 && orders.length > 0) {
                    const contractorInfo = contractors.find(
                        (c) => c.id === draft.data.contractorId,
                    );
                    const ordersInfo = draft.data.selectedOrders
                        .map((orderId: string) =>
                            orders.find((o) => o.id === orderId),
                        )
                        .filter(
                            (order): order is NonNullable<typeof order> =>
                                order !== null && order !== undefined,
                        )
                        .map((order) => ({
                            id: order.id,
                            cutOrder: order.cutOrder ?? undefined,
                            code: order.code ?? undefined,
                            customerName: order.customer?.name ?? undefined,
                        }));

                    setPendingDraft((prev) => {
                        if (!prev) {
                            return {
                                data: draft.data,
                                timestamp: Date.now(),
                                version: 1,
                                contractorName: contractorInfo?.name,
                                ordersInfo,
                            };
                        }

                        return {
                            ...prev,
                            contractorName: contractorInfo?.name,
                            ordersInfo,
                        };
                    });
                    openDraftModal();
                }
                // Si no, el modal se mostrará cuando se carguen los datos
            },
            onRestore: (data) => {
                setFormData({
                    contractorId: data.contractorId,
                    selectedOrders: data.selectedOrders,
                    quantities: data.quantities,
                });
                // Los filtros se restauran automáticamente en useOrderFilters
            },
        },
    );

    // Cargar datos iniciales
    useEffect(() => {
        const loadData = async () => {
            try {
                const [contractorsData, ordersData] = await Promise.all([
                    getContractorsForSelector(),
                    getOrdersForSelector(),
                ]);

                setContractors(contractorsData.contractors || []);
                setOrders(ordersData.orders || []);
            } catch {
                // Error loading data
                showToast({
                    title: "Error",
                    description: "No se pudieron cargar los datos",
                    color: "danger",
                });
            }
        };

        loadData();
    }, []);

    // Mostrar modal de borrador cuando se carguen los datos
    useEffect(() => {
        if (
            pendingDraft &&
            contractors.length > 0 &&
            orders.length > 0 &&
            !isDraftModalOpen &&
            !draftHandled
        ) {
            const contractorInfo = contractors.find(
                (c) => c.id === pendingDraft.data?.contractorId,
            );
            const ordersInfo = pendingDraft.data?.selectedOrders
                ?.map((orderId: string) => orders.find((o) => o.id === orderId))
                .filter(Boolean)
                .map((order: any) => ({
                    id: order.id,
                    cutOrder: order.cutOrder ?? undefined,
                    code: order.code ?? undefined,
                    customerName: order.customer?.name ?? undefined,
                }));

            setPendingDraft((prev) => {
                if (!prev) {
                    return null; // or handle appropriately
                }

                return {
                    ...prev,
                    contractorName: contractorInfo?.name,
                    ordersInfo,
                };
            });
            openDraftModal();
        }
    }, [
        contractors,
        orders,
        pendingDraft,
        isDraftModalOpen,
        draftHandled,
        openDraftModal,
    ]);

    // Cargar métricas del contratista seleccionado
    useEffect(() => {
        if (formData.contractorId) {
            getContractorMetrics(formData.contractorId)
                .then((result) => {
                    if (result.success) {
                        setSelectedContractorMetrics(result.data || null);
                    }
                })
                .catch(() => {
                    // Error loading metrics
                });
        }
    }, [formData.contractorId]);

    // Handlers
    const handleContractorChange = (contractorId: string) => {
        setFormData((prev) => ({ ...prev, contractorId }));
        saveDraft();
    };

    const handleOrderSelection = (orderId: string) => {
        setFormData((prev) => {
            const newSelectedOrders = prev.selectedOrders.includes(orderId)
                ? prev.selectedOrders.filter((id) => id !== orderId)
                : [...prev.selectedOrders, orderId];

            // Si se deselecciona una orden, limpiar sus cantidades
            if (!newSelectedOrders.includes(orderId)) {
                const newQuantities = { ...prev.quantities };

                delete newQuantities[orderId];

                return {
                    ...prev,
                    selectedOrders: newSelectedOrders,
                    quantities: newQuantities,
                };
            }

            return { ...prev, selectedOrders: newSelectedOrders };
        });
        saveDraft();
    };

    const handleQuantityChange = (
        orderId: string,
        garmentSizeId: string,
        quantity: number,
    ) => {
        setFormData((prev) => ({
            ...prev,
            quantities: {
                ...prev.quantities,
                [orderId]: {
                    ...prev.quantities[orderId],
                    [garmentSizeId]: quantity,
                },
            },
        }));
        saveDraft();
    };

    const handleSuggestionSelect = useCallback(
        (suggestion: Suggestion) => {
            // Actualizar el filtro de búsqueda
            updateFilter("searchQuery", suggestion.value);

            // Seleccionar la orden si existe
            if (suggestion.metadata?.orderId) {
                // Si la orden no está ya seleccionada, seleccionarla
                if (
                    !formData.selectedOrders.includes(
                        suggestion.metadata.orderId,
                    )
                ) {
                    handleOrderSelection(suggestion.metadata.orderId);
                }
            } else if (suggestion.metadata?.recentOrderId) {
                // Para clientes, seleccionar la orden más reciente si existe
                if (
                    !formData.selectedOrders.includes(
                        suggestion.metadata.recentOrderId,
                    )
                ) {
                    handleOrderSelection(suggestion.metadata.recentOrderId);
                }
            }
        },
        [updateFilter, formData.selectedOrders, handleOrderSelection],
    );

    const handleSubmit = async () => {
        // Validar formulario
        const errors: Record<string, string> = {};

        if (!formData.contractorId) {
            errors.contractor = "Debes seleccionar un contratista";
        }

        if (formData.selectedOrders.length === 0) {
            errors.orders = "Debes seleccionar al menos una orden";
        }

        const hasQuantities = Object.values(formData.quantities).some(
            (orderQuantities) =>
                Object.values(orderQuantities).some((q) => q > 0),
        );

        if (!hasQuantities) {
            errors.quantities = "Debes asignar al menos una cantidad";
        }

        if (Object.keys(errors).length > 0) {
            setValidationErrors(errors);

            return;
        }

        setIsLoading(true);

        try {
            // Preparar datos para crear asignaciones
            const assignments = [];

            for (const orderId of formData.selectedOrders) {
                const orderQuantities = formData.quantities[orderId] || {};

                for (const [garmentSizeId, quantity] of Object.entries(
                    orderQuantities,
                )) {
                    if (quantity > 0) {
                        assignments.push({
                            contractorId: formData.contractorId!,
                            orderId,
                            garmentSizeId,
                            quantity,
                        });
                    }
                }
            }

            const result = await createAssignments({
                contractorId: formData.contractorId!,
                assignments,
            });

            if (result.success) {
                clearDraft(); // Limpiar borrador después de éxito

                showToast({
                    title: "Asignaciones creadas",
                    description: `Se crearon ${result.data?.assignments?.length || 0} asignaciones exitosamente`,
                    color: "success",
                });

                // Redirigir a crear remisión con los datos
                router.push(
                    `/dashboard/remissions/create?assignmentIds=${result.data?.assignments?.map((a) => a.id).join(",") || ""}&contractor=${formData.contractorId}`,
                );
            } else {
                showToast({
                    title: "Error",
                    description:
                        result.error || "No se pudieron crear las asignaciones",
                    color: "danger",
                });
            }
        } catch {
            // Error creating assignments
            showToast({
                title: "Error",
                description: "Ocurrió un error al crear las asignaciones",
                color: "danger",
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Calcular resumen
    const summary = useMemo(() => {
        const totalPieces = Object.values(formData.quantities).reduce(
            (total, orderQuantities) =>
                total +
                Object.values(orderQuantities).reduce((sum, q) => sum + q, 0),
            0,
        );

        const selectedOrdersData = orders.filter((o) =>
            formData.selectedOrders.includes(o.id),
        );

        return {
            contractor:
                contractors.find((c) => c.id === formData.contractorId) || null,
            ordersCount: formData.selectedOrders.length,
            totalPieces,
            orders: selectedOrdersData as any,
        };
    }, [formData, contractors, orders]);

    return (
        <>
            <ToastContainer />

            {/* Modal de restauración de borrador */}
            <DraftRestoreModal
                draftData={
                    pendingDraft?.data
                        ? {
                              ...pendingDraft.data,
                              contractorName: pendingDraft.contractorName,
                              ordersInfo: pendingDraft.ordersInfo,
                          }
                        : null
                }
                draftTimestamp={pendingDraft?.timestamp || Date.now()}
                isOpen={isDraftModalOpen}
                onClose={closeDraftModal}
                onDiscard={() => {
                    if (pendingDraft?.discard) {
                        pendingDraft.discard();
                        setPendingDraft(null);
                        setDraftHandled(true);
                    }
                }}
                onRestore={() => {
                    if (pendingDraft?.restore) {
                        pendingDraft.restore();
                        setPendingDraft(null);
                        setDraftHandled(true);
                    }
                }}
            />

            <DashboardLayout
                actions={
                    <div className="flex items-center gap-3">
                        <DraftIndicator
                            isRestored={isRestored}
                            lastSaved={lastSaved}
                            onDiscard={clearDraft}
                        />
                        <Button
                            color="default"
                            startContent={<ArrowLeftIcon className="w-4 h-4" />}
                            variant="flat"
                            onPress={() =>
                                router.push("/dashboard/assignments")
                            }
                        >
                            Volver
                        </Button>
                    </div>
                }
                breadcrumbs={[
                    { label: "Asignaciones", href: "/dashboard/assignments" },
                    { label: "Nueva" },
                ]}
                subtitle="Asigna trabajo a contratistas de manera rápida y eficiente"
                title="Nueva Asignación"
            >
                <div className="grid gap-6">
                    {/* Sección 1: Selección de Contratista */}
                    <Card className="shadow-sm">
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <UserGroupIcon className="w-5 h-5 text-primary" />
                                <h3 className="text-lg font-semibold">
                                    Contratista
                                </h3>
                            </div>
                        </CardHeader>
                        <CardBody>
                            <ContractorSelector
                                contractors={contractors}
                                error={validationErrors.contractor}
                                metrics={selectedContractorMetrics || undefined}
                                selectedId={formData.contractorId}
                                onChange={handleContractorChange}
                            />
                        </CardBody>
                    </Card>

                    {/* Sección 2: Selección de Órdenes */}
                    <Card className="shadow-sm">
                        <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <CubeIcon className="w-5 h-5 text-primary" />
                                    <h3 className="text-lg font-semibold">
                                        Órdenes
                                    </h3>
                                    {activeFiltersCount > 0 && (
                                        <Badge
                                            color="primary"
                                            content={activeFiltersCount}
                                            size="sm"
                                        >
                                            <span className="sr-only">
                                                Filtros activos
                                            </span>
                                        </Badge>
                                    )}
                                </div>
                                <span className="text-sm text-gray-500">
                                    {filteredOrders.length} órdenes disponibles
                                </span>
                            </div>
                        </CardHeader>
                        <CardBody className="space-y-4">
                            <OrderFiltersPanel
                                filters={filters}
                                orderCount={filteredOrders.length}
                                orders={
                                    orders.map((order) => ({
                                        ...order,
                                        code: order.code ?? undefined,
                                        parts: order.parts?.map((part) => ({
                                            id: part.id,
                                            code: part.code || "",
                                            orderId: order.id,
                                        })),
                                        createdAt:
                                            typeof order.receivedDate ===
                                            "string"
                                                ? order.receivedDate
                                                : order.receivedDate instanceof
                                                    Date
                                                  ? order.receivedDate.toISOString()
                                                  : new Date().toISOString(),
                                    })) as any
                                }
                                onClearFilters={clearFilters}
                                onFilterChange={
                                    updateFilter as (
                                        key: keyof OrderFilters,
                                        value: unknown,
                                    ) => void
                                }
                                onSuggestionSelect={handleSuggestionSelect}
                            />

                            <Divider />

                            <OrdersSelector
                                error={validationErrors.orders}
                                orders={filteredOrders as any}
                                selectedIds={formData.selectedOrders}
                                onClearFilters={clearFilters}
                                onShowAllOrders={() => {
                                    clearFilters();
                                    // Opcionalmente, puedes agregar lógica adicional aquí
                                }}
                                onToggle={handleOrderSelection}
                            />
                        </CardBody>
                    </Card>

                    {/* Sección 3: Asignación de Cantidades */}
                    {formData.selectedOrders.length > 0 && (
                        <Card className="shadow-sm">
                            <CardHeader className="pb-3">
                                <div className="flex items-center gap-2">
                                    <ChartBarIcon className="w-5 h-5 text-primary" />
                                    <h3 className="text-lg font-semibold">
                                        Cantidades
                                    </h3>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <QuantityAssigner
                                    error={validationErrors.quantities}
                                    orders={
                                        orders.filter((o) =>
                                            formData.selectedOrders.includes(
                                                o.id,
                                            ),
                                        ) as any
                                    }
                                    quantities={formData.quantities}
                                    onChange={handleQuantityChange}
                                />
                            </CardBody>
                        </Card>
                    )}

                    {/* Sección 4: Resumen y Acciones */}
                    <Card className="shadow-sm">
                        <CardHeader className="pb-3">
                            <div className="flex items-center gap-2">
                                <DocumentTextIcon className="w-5 h-5 text-primary" />
                                <h3 className="text-lg font-semibold">
                                    Resumen
                                </h3>
                            </div>
                        </CardHeader>
                        <CardBody>
                            <AssignmentSummary
                                canSubmit={
                                    !!formData.contractorId &&
                                    formData.selectedOrders.length > 0 &&
                                    summary.totalPieces > 0
                                }
                                isLoading={isLoading}
                                summary={summary as any}
                                onSubmit={handleSubmit}
                            />
                        </CardBody>
                    </Card>
                </div>
            </DashboardLayout>
        </>
    );
}
