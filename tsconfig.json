{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/app/*": ["./app/*"], "@/features/*": ["./features/*"], "@/shared/*": ["./shared/*"], "@/core/*": ["./core/*"], "@/prisma/*": ["./prisma/*"], "@/public/*": ["./public/*"], "@/styles/*": ["./styles/*"], "@/tests/*": ["./tests/*"]}}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "backup/**/*", "tests/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/__tests__/**/*", "**/testing/**/*"]}