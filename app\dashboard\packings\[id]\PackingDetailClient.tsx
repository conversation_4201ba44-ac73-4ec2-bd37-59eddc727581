"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Divider,
    Chip,
    Badge,
    Tooltip,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    useDisclosure,
} from "@heroui/react";
import {
    ArrowLeftIcon,
    PrinterIcon,
    PencilSquareIcon,
    TrashIcon,
    CalendarIcon,
    BuildingOfficeIcon,
    UserGroupIcon,
    ClockIcon,
    CubeIcon,
} from "@heroicons/react/24/outline";
import { format, formatDistanceToNow } from "date-fns";
import { es } from "date-fns/locale";

import { PackingStatusBadge } from "@/features/packings/components";
import {
    PackingPreview,
    transformPackingToPreview,
} from "@/features/packings/components/PackingPreview";
import { usePackingPDF, useDeletePacking } from "@/features/packings/hooks";
import { DashboardHeader } from "@/shared/components/dashboard";

interface PackingDetailClientProps {
    packing: any;
}

export default function PackingDetailClient({
    packing,
}: PackingDetailClientProps) {
    const router = useRouter();
    const { printPacking, generatePDF, isGenerating } = usePackingPDF();
    const { deletePacking } = useDeletePacking();
    const {
        isOpen: isDeleteOpen,
        onOpen: onDeleteOpen,
        onClose: onDeleteClose,
    } = useDisclosure();
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
        setIsDeleting(true);
        const result = await deletePacking(packing.id);

        if (result.success) {
            router.push("/dashboard/packings");
        }
        setIsDeleting(false);
    };

    const totalPieces = packing.details.reduce(
        (sum: number, detail: any) => sum + detail.quantity,
        0,
    );

    // Agrupar detalles por modelo y color
    const groupedDetails = packing.details.reduce((acc: any, detail: any) => {
        const key = `${detail.garmentSize.garment.model.code}-${detail.garmentSize.garment.color.name}`;

        if (!acc[key]) {
            acc[key] = {
                model: detail.garmentSize.garment.model,
                color: detail.garmentSize.garment.color.name,
                items: [],
            };
        }
        acc[key].items.push({
            size: detail.garmentSize.size.code,
            quantity: detail.quantity,
            id: detail.id,
        });

        return acc;
    }, {});

    return (
        <div className="space-y-6">
            <DashboardHeader
                actions={
                    <div className="flex items-center gap-2">
                        <Button
                            startContent={<ArrowLeftIcon className="w-4 h-4" />}
                            variant="light"
                            onPress={() => router.push("/dashboard/packings")}
                        >
                            Volver
                        </Button>

                        <Button
                            color="primary"
                            isLoading={isGenerating}
                            startContent={<PrinterIcon className="w-4 h-4" />}
                            variant="flat"
                            onPress={() =>
                                generatePDF(packing.id, packing.folio)
                            }
                        >
                            Descargar PDF
                        </Button>

                        <Button
                            color="primary"
                            startContent={<PrinterIcon className="w-4 h-4" />}
                            onPress={() => printPacking(packing.id)}
                        >
                            Imprimir
                        </Button>

                        <div className="w-px h-6 bg-gray-200 dark:bg-gray-700" />

                        <Tooltip content="Editar">
                            <Button
                                isIconOnly
                                variant="light"
                                onPress={() =>
                                    router.push(
                                        `/dashboard/packings/${packing.id}/edit`,
                                    )
                                }
                            >
                                <PencilSquareIcon className="w-5 h-5" />
                            </Button>
                        </Tooltip>

                        <Tooltip color="danger" content="Eliminar">
                            <Button
                                isIconOnly
                                color="danger"
                                variant="light"
                                onPress={onDeleteOpen}
                            >
                                <TrashIcon className="w-5 h-5" />
                            </Button>
                        </Tooltip>
                    </div>
                }
                breadcrumbs={[
                    { label: "Dashboard", href: "/dashboard" },
                    { label: "Packings", href: "/dashboard/packings" },
                    { label: packing.folio },
                ]}
                subtitle={`Creado ${format(new Date(packing.createdAt), "PPP", { locale: es })}`}
                title={`Packing ${packing.folio}`}
            />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Información principal */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Estado y datos generales */}
                    <Card>
                        <CardHeader className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">
                                Información General
                            </h3>
                            <PackingStatusBadge status={packing.status} />
                        </CardHeader>
                        <Divider />
                        <CardBody className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3">
                                        <BuildingOfficeIcon className="w-5 h-5 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Cliente
                                            </p>
                                            <p className="font-medium">
                                                {packing.customer.name}
                                            </p>
                                        </div>
                                    </div>

                                    {packing.subCustomer && (
                                        <div className="flex items-center gap-3">
                                            <UserGroupIcon className="w-5 h-5 text-gray-400" />
                                            <div>
                                                <p className="text-sm text-gray-500">
                                                    Subcliente
                                                </p>
                                                <p className="font-medium">
                                                    {packing.subCustomer.name}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="space-y-3">
                                    <div className="flex items-center gap-3">
                                        <CalendarIcon className="w-5 h-5 text-gray-400" />
                                        <div>
                                            <p className="text-sm text-gray-500">
                                                Fecha de Entrega
                                            </p>
                                            <p className="font-medium">
                                                {format(
                                                    new Date(
                                                        packing.deliveryDate,
                                                    ),
                                                    "PPP",
                                                    { locale: es },
                                                )}
                                            </p>
                                        </div>
                                    </div>

                                    {packing.printedAt && (
                                        <div className="flex items-center gap-3">
                                            <PrinterIcon className="w-5 h-5 text-gray-400" />
                                            <div>
                                                <p className="text-sm text-gray-500">
                                                    Impreso
                                                </p>
                                                <p className="font-medium">
                                                    {format(
                                                        new Date(
                                                            packing.printedAt,
                                                        ),
                                                        "PPp",
                                                        { locale: es },
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {packing.notes && (
                                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                                        Notas:
                                    </p>
                                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                                        {packing.notes}
                                    </p>
                                </div>
                            )}
                        </CardBody>
                    </Card>

                    {/* Vista previa del documento */}
                    <Card>
                        <CardHeader>
                            <h3 className="text-lg font-semibold">
                                Vista Previa del Documento
                            </h3>
                        </CardHeader>
                        <Divider />
                        <CardBody className="overflow-x-auto">
                            <PackingPreview
                                className="mx-auto"
                                data={transformPackingToPreview(packing)}
                                orientation="landscape"
                                packingId={packing.id}
                                zoom={70}
                            />
                        </CardBody>
                    </Card>

                    {/* Detalles de artículos */}
                    <Card>
                        <CardHeader className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">
                                Artículos del Packing
                            </h3>
                            <Chip color="primary" size="sm" variant="flat">
                                {totalPieces} piezas totales
                            </Chip>
                        </CardHeader>
                        <Divider />
                        <CardBody className="space-y-4">
                            {Object.entries(groupedDetails).map(
                                ([key, group]: [string, any]) => (
                                    <motion.div
                                        key={key}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg"
                                        initial={{ opacity: 0, y: 10 }}
                                    >
                                        <div className="flex items-center justify-between mb-3">
                                            <div>
                                                <p className="font-semibold text-lg">
                                                    {group.model.code}
                                                </p>
                                                <p className="text-sm text-gray-500">
                                                    Color: {group.color}
                                                </p>
                                                {group.model.description && (
                                                    <p className="text-xs text-gray-400 mt-1">
                                                        {
                                                            group.model
                                                                .description
                                                        }
                                                    </p>
                                                )}
                                            </div>
                                            <Badge
                                                color="primary"
                                                content={group.items.reduce(
                                                    (sum: number, item: any) =>
                                                        sum + item.quantity,
                                                    0,
                                                )}
                                                size="lg"
                                            >
                                                <CubeIcon className="w-6 h-6 text-gray-400" />
                                            </Badge>
                                        </div>

                                        <div className="flex flex-wrap gap-2">
                                            {group.items.map((item: any) => (
                                                <Chip
                                                    key={item.id}
                                                    size="sm"
                                                    variant="flat"
                                                >
                                                    Talla {item.size}:{" "}
                                                    {item.quantity} pzs
                                                </Chip>
                                            ))}
                                        </div>
                                    </motion.div>
                                ),
                            )}
                        </CardBody>
                    </Card>
                </div>

                {/* Sidebar con historial */}
                <div className="space-y-6">
                    {/* Resumen rápido */}
                    <Card>
                        <CardHeader>
                            <h3 className="text-lg font-semibold">Resumen</h3>
                        </CardHeader>
                        <Divider />
                        <CardBody className="space-y-3">
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-500">
                                    Líneas totales
                                </span>
                                <span className="font-semibold">
                                    {packing.details.length}
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-500">
                                    Piezas totales
                                </span>
                                <span className="font-semibold">
                                    {totalPieces}
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-500">
                                    Modelos únicos
                                </span>
                                <span className="font-semibold">
                                    {Object.keys(groupedDetails).length}
                                </span>
                            </div>
                        </CardBody>
                    </Card>

                    {/* Historial */}
                    {packing.history && packing.history.length > 0 && (
                        <Card>
                            <CardHeader>
                                <h3 className="text-lg font-semibold">
                                    Historial
                                </h3>
                            </CardHeader>
                            <Divider />
                            <CardBody className="space-y-3">
                                {packing.history.map((event: any) => (
                                    <div
                                        key={event.id}
                                        className="flex items-start gap-3"
                                    >
                                        <div className="p-2 rounded-full bg-gray-100 dark:bg-gray-800">
                                            <ClockIcon className="w-4 h-4 text-gray-400" />
                                        </div>
                                        <div className="flex-1">
                                            <p className="text-sm font-medium">
                                                {event.action}
                                            </p>
                                            {event.metadata?.notes && (
                                                <p className="text-xs text-gray-500">
                                                    {event.metadata.notes}
                                                </p>
                                            )}
                                            <p className="text-xs text-gray-400 mt-1">
                                                {formatDistanceToNow(
                                                    new Date(event.timestamp),
                                                    {
                                                        addSuffix: true,
                                                        locale: es,
                                                    },
                                                )}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </CardBody>
                        </Card>
                    )}
                </div>
            </div>

            {/* Documento para impresión (oculto) */}
            <div
                style={{
                    position: "absolute",
                    left: "-9999px",
                    top: "-9999px",
                    width: "297mm",
                    backgroundColor: "white",
                }}
            >
                <PackingPreview
                    data={transformPackingToPreview(packing)}
                    orientation="landscape"
                    packingId={packing.id}
                    zoom={100}
                />
            </div>

            {/* Modal de confirmación de eliminación */}
            <Modal isOpen={isDeleteOpen} onClose={onDeleteClose}>
                <ModalContent>
                    <ModalHeader className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                            <TrashIcon className="w-5 h-5 text-danger" />
                            Eliminar Packing
                        </div>
                    </ModalHeader>
                    <ModalBody>
                        <p>
                            ¿Estás seguro de que deseas eliminar el packing{" "}
                            <strong>{packing.folio}</strong>?
                        </p>
                        <p className="text-sm text-gray-500 mt-2">
                            Esta acción no se puede deshacer y se eliminarán
                            todos los datos asociados.
                        </p>
                    </ModalBody>
                    <ModalFooter>
                        <Button variant="light" onPress={onDeleteClose}>
                            Cancelar
                        </Button>
                        <Button
                            color="danger"
                            isLoading={isDeleting}
                            onPress={handleDelete}
                        >
                            Eliminar
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
        </div>
    );
}
