"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@heroui/react";
import {
    ExclamationTriangleIcon,
    ArrowPathIcon,
    HomeIcon,
} from "@heroicons/react/24/outline";

export default function GlobalError({
    error,
    reset,
}: {
    error: Error & { digest?: string };
    reset: () => void;
}) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error("Global application error:", error);

        // Send error to monitoring service
        if (typeof window !== "undefined" && (window as any).gtag) {
            (window as any).gtag("event", "exception", {
                description: error.message,
                fatal: true,
                error_type: "global_error",
            });
        }
    }, [error]);

    return (
        <html>
            <body className="min-h-screen bg-gray-50 dark:bg-gray-900">
                <div className="min-h-screen flex items-center justify-center px-4">
                    <div className="max-w-lg w-full">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8">
                            <div className="text-center mb-8">
                                <ExclamationTriangleIcon className="w-24 h-24 text-red-500 mx-auto mb-4" />
                                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                                    Error Crítico del Sistema
                                </h1>
                                <p className="text-gray-600 dark:text-gray-400">
                                    Ha ocurrido un error grave en la aplicación.
                                    Por favor, intenta recargar la página.
                                </p>
                            </div>

                            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                                <p className="text-sm text-red-800 dark:text-red-200 font-mono break-all">
                                    {error.message ||
                                        "Error desconocido del sistema"}
                                </p>
                                {error.digest && (
                                    <p className="text-xs text-red-600 dark:text-red-400 mt-2">
                                        Código de referencia: {error.digest}
                                    </p>
                                )}
                            </div>

                            <div className="space-y-3">
                                <Button
                                    className="w-full"
                                    color="danger"
                                    size="lg"
                                    startContent={
                                        <ArrowPathIcon className="w-5 h-5" />
                                    }
                                    onPress={() => reset()}
                                >
                                    Reintentar
                                </Button>

                                <Button
                                    className="w-full"
                                    size="lg"
                                    startContent={
                                        <HomeIcon className="w-5 h-5" />
                                    }
                                    variant="flat"
                                    onPress={() => (window.location.href = "/")}
                                >
                                    Ir al inicio
                                </Button>
                            </div>

                            <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                <h3 className="font-semibold text-sm text-gray-700 dark:text-gray-300 mb-2">
                                    ¿Qué puedes hacer?
                                </h3>
                                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    <li>• Recargar la página con Ctrl+F5</li>
                                    <li>• Limpiar el caché del navegador</li>
                                    <li>• Intentar en otro navegador</li>
                                    <li>• Contactar al equipo de soporte</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
        </html>
    );
}
