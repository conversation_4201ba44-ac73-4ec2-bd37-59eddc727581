import { test, expect } from "@playwright/test";

import { PerformanceTracker } from "../../../utils/performance-tracker";

test.describe("Performance Monitoring Integration", () => {
    let tracker: PerformanceTracker;

    test.beforeEach(async ({ page }) => {
        tracker = new PerformanceTracker();

        // Inject performance tracking into the page
        await page.addInitScript(() => {
            window.performanceMetrics = [];

            // Track API calls
            const originalFetch = window.fetch;

            window.fetch = async (...args) => {
                const start = performance.now();
                const response = await originalFetch(...args);
                const duration = performance.now() - start;

                window.performanceMetrics.push({
                    type: "api",
                    url: args[0],
                    duration,
                    status: response.status,
                    timestamp: Date.now(),
                });

                return response;
            };
        });
    });

    test("should track API response times", async ({ page }) => {
        await page.goto("/dashboard/orders");

        // Wait for orders to load
        await page.waitForSelector('[data-testid="orders-table"]');

        // Get performance metrics
        const metrics = await page.evaluate(() => window.performanceMetrics);

        // Verify API calls were tracked
        expect(metrics.length).toBeGreaterThan(0);

        // Check response times
        const apiMetrics = metrics.filter((m) => m.type === "api");

        apiMetrics.forEach((metric) => {
            expect(metric.duration).toBeLessThan(2000); // Max 2 seconds
        });
    });
});
