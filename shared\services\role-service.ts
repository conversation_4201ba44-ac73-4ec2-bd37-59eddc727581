import { Role } from "@prisma/client";

import { prisma as db } from "../lib/prisma";

/**
 * Opciones para la validación de roles
 */
interface RoleValidationOptions {
    createIfNotExists?: boolean;
    defaultRoleName?: string;
    throwOnNotFound?: boolean;
}

/**
 * Servicio para centralizar la lógica de gestión de roles
 * Proporciona funcionalidades para validar, encontrar y crear roles
 */
export class RoleService {
    private static instance: RoleService;
    private roleCache: Map<string, Role> = new Map();
    private cacheExpiry: Map<string, number> = new Map();
    private cacheTTL = 5 * 60 * 1000; // 5 minutos

    private constructor() {}

    /**
     * Obtiene la instancia única del servicio (patrón <PERSON>ton)
     */
    public static getInstance(): RoleService {
        if (!RoleService.instance) {
            RoleService.instance = new RoleService();
            // REMOVED: console.log("RoleService inicializado");
        }

        return RoleService.instance;
    }

    /**
     * Busca un rol por ID o nombre con caché integrado
     */
    public async findRoleByIdOrName(
        roleIdOrName: string,
    ): Promise<Role | null> {
        // Verificar cache primero
        if (this.roleCache.has(roleIdOrName)) {
            const expiry = this.cacheExpiry.get(roleIdOrName) || 0;

            if (expiry > Date.now()) {
                return this.roleCache.get(roleIdOrName) || null;
            }
            // Cache expirado, eliminar
            this.roleCache.delete(roleIdOrName);
            this.cacheExpiry.delete(roleIdOrName);
        }

        let role: Role | null = null;

        // Intentar buscar por ID primero
        if (
            roleIdOrName.match(/^[0-9a-fA-F]{24}$/) ||
            roleIdOrName.match(
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
            )
        ) {
            role = await db.role.findUnique({
                where: { id: roleIdOrName },
            });
        }

        // Si no encontramos por ID, buscar por nombre
        if (!role) {
            role = await db.role.findUnique({
                where: { name: roleIdOrName },
            });
        }

        // Actualizar cache si encontramos el rol
        if (role) {
            this.updateCache(roleIdOrName, role);
        }

        return role;
    }

    /**
     * Actualiza la caché de roles con una nueva entrada
     */
    private updateCache(key: string, role: Role): void {
        this.roleCache.set(key, role);
        this.roleCache.set(role.id, role);
        this.roleCache.set(role.name, role);

        const expiryTime = Date.now() + this.cacheTTL;

        this.cacheExpiry.set(key, expiryTime);
        this.cacheExpiry.set(role.id, expiryTime);
        this.cacheExpiry.set(role.name, expiryTime);
    }

    /**
     * Valida un rol por ID o nombre con opciones configurables
     */
    public async validateRole(
        roleIdOrName: string,
        options: RoleValidationOptions = {},
    ): Promise<{ success: boolean; role?: Role; error?: string }> {
        const {
            createIfNotExists = false,
            defaultRoleName = "GUEST",
            throwOnNotFound = false,
        } = options;

        try {
            // Buscar el rol
            let role = await this.findRoleByIdOrName(roleIdOrName);

            // Si no existe y podemos crearlo
            if (!role && createIfNotExists) {
                // Verificar si hay roles en la base de datos
                const roleCount = await db.role.count();

                if (roleCount === 0) {
                    // No hay roles, crear uno predeterminado
                    role = await db.role.create({
                        data: {
                            name: defaultRoleName,
                            iconName: "UserCircleIcon",
                            color: "#9CA3AF",
                        },
                    });

                    // REMOVED: console.log(
                    //     `Rol ${defaultRoleName} creado como recurso de emergencia`,
                    // );

                    // Actualizar caché
                    this.updateCache(role.id, role);
                    this.updateCache(role.name, role);

                    return { success: true, role };
                }
            }

            if (!role) {
                const error = "El rol especificado no existe";

                if (throwOnNotFound) {
                    throw new Error(error);
                }

                return { success: false, error };
            }

            return { success: true, role };
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : "Error validando rol";

            if (throwOnNotFound) {
                throw error;
            }

            return { success: false, error: errorMessage };
        }
    }

    /**
     * Obtiene o crea un rol predeterminado
     */
    public async getOrCreateDefaultRole(roleName = "GUEST"): Promise<Role> {
        // Buscar primero
        let role = await this.findRoleByIdOrName(roleName);

        if (!role) {
            // Crear rol predeterminado
            role = await db.role.create({
                data: {
                    name: roleName,
                    iconName: "UserCircleIcon",
                    color: "#9CA3AF",
                },
            });

            // REMOVED: console.log(`Rol ${roleName} creado como predeterminado`);

            // Actualizar caché
            this.updateCache(role.id, role);
            this.updateCache(role.name, role);
        }

        return role;
    }

    /**
     * Limpia la caché de roles
     */
    public clearCache(): void {
        this.roleCache.clear();
        this.cacheExpiry.clear();
    }
}

// Exportar una instancia para uso fácil
export const roleService = RoleService.getInstance();
