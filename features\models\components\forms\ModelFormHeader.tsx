"use client";

import React from "react";
import { DocumentTextIcon } from "@heroicons/react/24/outline";

interface ModelFormHeaderProps {
    mode: "create" | "edit";
    modelCode?: string;
}

export function ModelFormHeader({ mode, modelCode }: ModelFormHeaderProps) {
    return (
        <div className="mb-6">
            <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <DocumentTextIcon className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        {mode === "create"
                            ? "Nuevo Modelo"
                            : `Editar Modelo ${modelCode || ""}`}
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400">
                        {mode === "create"
                            ? "Completa la información para registrar un nuevo modelo de prenda"
                            : "Actualiza la información del modelo de prenda"}
                    </p>
                </div>
            </div>
        </div>
    );
}
