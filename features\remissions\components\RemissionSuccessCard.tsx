"use client";

import React from "react";
import {
    CheckIcon,
    DocumentTextIcon,
    PrinterIcon,
    ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";

interface RemissionSuccessCardProps {
    remission: {
        id: string;
        folio: string;
        createdAt: Date;
    };
}

export function RemissionSuccessCard({ remission }: RemissionSuccessCardProps) {
    const router = useRouter();

    const handleView = () => {
        router.push(`/dashboard/remissions/${remission.id}`);
    };

    const handlePrint = () => {
        window.open(`/dashboard/remissions/${remission.id}/print`, "_blank");
    };

    const handleDownload = () => {
        window.open(`/api/remissions/pdf?id=${remission.id}`, "_blank");
    };

    return (
        <div className="mt-6 p-6 bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-xl shadow-lg dark:shadow-2xl border border-gray-200 dark:border-gray-700">
            {/* Success Icon and Title */}
            <div className="flex items-start gap-4 mb-4">
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                    <CheckIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                        Remisión Generada Exitosamente
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Se ha creado una remisión para esta asignación
                    </p>
                </div>
            </div>

            {/* Folio Display - Neumorphic Style */}
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg shadow-inner dark:shadow-black/30">
                <div className="text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                        Folio de Remisión
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-100 font-mono">
                        {remission.folio}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {new Date(remission.createdAt).toLocaleString("es-MX")}
                    </p>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 justify-center">
                <button
                    className="flex items-center gap-2 px-4 py-2 bg-blue-500 dark:bg-blue-600 hover:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md"
                    onClick={handleView}
                >
                    <DocumentTextIcon className="w-4 h-4" />
                    <span>Ver Remisión</span>
                </button>

                <button
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-lg transition-colors duration-200 border border-gray-200 dark:border-gray-600"
                    onClick={handlePrint}
                >
                    <PrinterIcon className="w-4 h-4" />
                    <span>Imprimir</span>
                </button>

                <button
                    className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-lg transition-colors duration-200 border border-gray-200 dark:border-gray-600"
                    onClick={handleDownload}
                >
                    <ArrowDownTrayIcon className="w-4 h-4" />
                    <span>Descargar</span>
                </button>
            </div>
        </div>
    );
}
