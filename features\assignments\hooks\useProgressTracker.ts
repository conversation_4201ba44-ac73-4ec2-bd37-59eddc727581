"use client";

import { useMemo } from "react";

export interface ProgressStep {
    id: number;
    label: string;
    description?: string;
}

// Definir tipo para pasos por defecto
export interface DefaultStep {
    id: number;
    title: string;
    description?: string;
}

/**
 * Hook para manejar de manera segura el estado de progreso de un indicador de pasos,
 * garantizando valores válidos incluso cuando los props de entrada sean undefined o inválidos.
 */
export function useProgressTracker(
    currentStep?: number,
    customSteps?: ProgressStep[],
    defaultSteps?: ProgressStep[] | DefaultStep[],
) {
    // Procesar los pasos por defecto si tienen formato diferente
    const processedDefaultSteps = useMemo(() => {
        if (!defaultSteps) return [];

        return defaultSteps.map((step) => {
            // Comprobar si es un DefaultStep (tiene title en lugar de label)
            if ("title" in step) {
                return {
                    id: step.id,
                    label: step.title,
                    description: step.description,
                } as ProgressStep;
            }

            return step as ProgressStep;
        });
    }, [defaultSteps]);

    // Usar los pasos proporcionados o los predeterminados
    const steps = useMemo(() => {
        return customSteps || processedDefaultSteps || [];
    }, [customSteps, processedDefaultSteps]);

    // Encontrar paso actual si no se proporciona
    const safeCurrentStep = useMemo(() => {
        if (currentStep === undefined || currentStep < 1) {
            return steps.length > 0 ? steps[0].id : 1;
        }

        return currentStep;
    }, [currentStep, steps]);

    // Calcular el ancho de la barra de progreso
    const progressWidth = useMemo(() => {
        if (steps.length <= 1) return "100%";

        const maxStepId = Math.max(...steps.map((s) => s.id));
        const minStepId = Math.min(...steps.map((s) => s.id));
        const range = maxStepId - minStepId;

        if (range <= 0) return "0%";

        const value = ((safeCurrentStep - minStepId) / range) * 100;
        // Asegurar que la barra siempre tiene al menos un poco de ancho
        const safeValue = Math.max(5, value);

        return `${safeValue}%`;
    }, [steps, safeCurrentStep]);

    // Verificar si un paso está activo
    const isStepActive = useMemo(() => {
        return (stepId: number) => stepId === safeCurrentStep;
    }, [safeCurrentStep]);

    // Verificar si un paso está completado
    const isStepCompleted = useMemo(() => {
        return (stepId: number) => stepId < safeCurrentStep;
    }, [safeCurrentStep]);

    // Verificar si se puede hacer clic en un paso
    const canStepBeClicked = useMemo(() => {
        return (stepId: number, allowAnyNavigation = false) => {
            if (allowAnyNavigation) return true;

            // Solo se puede navegar a pasos anteriores o al siguiente paso
            return stepId < safeCurrentStep || stepId === safeCurrentStep + 1;
        };
    }, [safeCurrentStep]);

    return {
        steps,
        currentStep: safeCurrentStep,
        progressWidth,
        isStepActive,
        isStepCompleted,
        canStepBeClicked,
    };
}
