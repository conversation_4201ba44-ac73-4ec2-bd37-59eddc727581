# Sistema de Diseño Unificado - Lohari

## 📋 Introducción

Este documento describe el sistema de diseño unificado implementado para el dashboard de Lohari. El objetivo es proporcionar una experiencia de usuario consistente y profesional en todas las páginas de la aplicación.

## ✅ Estado de Implementación

### Módulos Migrados
- ✅ **Colors** - Gestión de paleta de colores con visualización hexadecimal
- ✅ **Sizes** - Catálogo de tallas con contadores de uso
- ✅ **Models** - Modelos de prendas con métricas de popularidad
- ✅ **Contractors** - Directorio de contratistas con información de contacto
- ✅ **Customers** - Base de clientes con análisis de negocio
- ✅ **Notes** - Sistema de notas con importancia y estados
- ✅ **Remissions** - Gestión de remisiones con estados de impresión
- ✅ **Assignments** - Asignaciones de producción con métricas
- ✅ **Orders** - Gestión de órdenes con métricas de estado

### Estado: Migración Completa ✅
Todos los módulos del dashboard ahora utilizan el sistema de diseño unificado, proporcionando una experiencia consistente en toda la aplicación.

## 🎨 Componentes del Dashboard

### DashboardLayout

El componente principal que envuelve todas las páginas del dashboard.

```tsx
import { DashboardLayout } from '@/shared/components/dashboard'

<DashboardLayout
  title="Título de la Página"
  subtitle="Descripción opcional"
  breadcrumbs={[{ label: 'Módulo', href: '/dashboard/modulo' }]}
  stats={<DashboardStats stats={statsData} />}
  actions={<Button>Acción</Button>}
>
  {/* Contenido de la página */}
</DashboardLayout>
```

### DashboardStats

Muestra tarjetas con estadísticas importantes.

```tsx
import { DashboardStats } from '@/shared/components/dashboard'

const stats = [
  {
    title: 'Total',
    value: 150,
    icon: <Icon />,
    color: 'primary',
    change: 12,
    changeLabel: 'vs. mes anterior'
  }
]

<DashboardStats stats={stats} columns={4} />
```

### DashboardTable

Tabla unificada con soporte para acciones, paginación y selección.

```tsx
import { DashboardTable } from '@/shared/components/dashboard'

<DashboardTable
  columns={columns}
  data={data}
  actions={actions}
  page={1}
  totalPages={10}
  onPageChange={setPage}
/>
```

### DashboardFilters

Sistema de filtros y búsqueda consistente con soporte para ordenamiento.

```tsx
import { DashboardFilters } from '@/shared/components/dashboard'

<DashboardFilters
  filters={filters}
  sortOptions={sortOptions}
  searchValue={search}
  onSearchChange={setSearch}
  filterValues={filterValues}
  onFilterChange={handleFilterChange}
  onSortChange={handleSortChange}
  currentSort={currentSort}
/>
```

## 🎯 Plantillas CRUD

### CrudListTemplate

Plantilla para páginas de listado con todas las características integradas.

```tsx
import { CrudListTemplate } from '@/shared/templates'

<CrudListTemplate
  // Layout
  title="Módulo"
  subtitle="Gestiona los registros"
  
  // Stats
  stats={stats}
  
  // Table
  columns={columns}
  data={data}
  actions={actions}
  
  // Create
  createRoute="/dashboard/module/new"
  createLabel="Crear Nuevo"
  
  // Filters
  filters={filters}
  searchValue={search}
  onSearchChange={setSearch}
  
  // Pagination
  page={page}
  totalPages={totalPages}
  onPageChange={setPage}
/>
```

### CrudFormTemplate

Plantilla para formularios CRUD con soporte para wizards multi-paso.

```tsx
import { CrudFormTemplate } from '@/shared/templates'

<CrudFormTemplate
  // Layout
  title="Nueva Orden"
  subtitle="Crea una nueva orden"
  breadcrumbs={[
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Órdenes', href: '/dashboard/orders' }
  ]}
  
  // Wizard
  steps={[
    {
      id: 'basic-info',
      title: 'Información Básica',
      subtitle: 'Datos principales',
      icon: <TagIcon className="w-5 h-5" />,
      component: <BasicInfoStep />,
      validation: () => validateBasicInfo()
    },
    // ... más pasos
  ]}
  currentStep={currentStep}
  onStepChange={handleStepChange}
  
  // Form
  onSubmit={handleSubmit}
  isSubmitting={isSubmitting}
  
  // Navigation
  backRoute="/dashboard/orders"
  backLabel="Volver"
  
  // Features
  showProgress={true}
  allowStepNavigation={true}
  completedSteps={completedSteps}
/>
```

## 🎨 Design Tokens

El sistema utiliza variables CSS para mantener consistencia:

```css
/* Espaciado */
--dashboard-padding: 1.5rem;
--dashboard-gap: 1rem;
--dashboard-max-width: 80rem;

/* Colores */
--gradient-primary: linear-gradient(135deg, #006FEE 0%, #0099FF 100%);
--gradient-surface: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);

/* Sombras */
--card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
--card-shadow-hover: 0 10px 15px rgba(0, 0, 0, 0.1);
```

## 📝 Guía de Migración

### Paso 1: Actualizar imports

```tsx
// Antes
import { Table, Card } from '@heroui/react'
import { useState } from 'react'

// Después
import { CrudListTemplate } from '@/shared/templates'
import { useOrdersData } from '@/features/orders/hooks'
```

### Paso 2: Definir columnas

```tsx
const columns = [
  {
    key: 'name',
    label: 'Nombre',
    sortable: true,
    render: (item) => <span className="font-medium">{item.name}</span>
  }
]
```

### Paso 3: Definir acciones

```tsx
const actions = [
  {
    label: 'Ver',
    icon: <EyeIcon className="w-4 h-4" />,
    onClick: (item) => handleView(item)
  },
  {
    label: 'Editar',
    icon: <PencilIcon className="w-4 h-4" />,
    onClick: (item) => router.push(`/edit/${item.id}`),
    color: 'primary'
  }
]
```

### Paso 4: Implementar la plantilla

```tsx
export default function ModulePage() {
  return (
    <CrudListTemplate
      title="Módulo"
      columns={columns}
      data={data}
      actions={actions}
      createRoute="/dashboard/module/new"
      // ... más props
    />
  )
}
```

## 🚀 Mejores Prácticas

1. **Usa las plantillas**: No reinventes la rueda, usa `CrudListTemplate` para listados
2. **Mantén consistencia**: Usa los mismos patrones de navegación y acciones
3. **Aprovecha los design tokens**: Usa las variables CSS en lugar de valores hardcodeados
4. **Componentes HeroUI**: Importa desde `@/shared/components/ui/hero-ui-client`
5. **Server Components**: Mantén la separación entre Server y Client Components

## 📊 Ejemplos Completos

### Página de Listado
Ver `/app/dashboard/orders/UnifiedClientPage.tsx` para un ejemplo completo de implementación con CrudListTemplate.

### Página de Formulario
Ver `/app/dashboard/orders/new/page.tsx` para un ejemplo completo de formulario multi-paso con CrudFormTemplate.

## 🔧 Sistema de Ordenamiento

### Definir opciones de ordenamiento

```tsx
const sortOptions = [
  { key: 'name-asc', label: 'Nombre A-Z', field: 'name', direction: 'asc' },
  { key: 'name-desc', label: 'Nombre Z-A', field: 'name', direction: 'desc' },
  { key: 'createdAt-desc', label: 'Más reciente', field: 'createdAt', direction: 'desc' },
  { key: 'createdAt-asc', label: 'Más antiguo', field: 'createdAt', direction: 'asc' }
]
```

### Implementar ordenamiento

```tsx
import { sortData } from '@/shared/utils/sortHelpers'

const filteredData = useMemo(() => {
  let filtered = [...data]
  
  // Aplicar filtros...
  
  // Aplicar ordenamiento
  if (currentSort) {
    filtered = sortData(filtered, currentSort.field, currentSort.direction)
  }
  
  return filtered
}, [data, currentSort])
```

## 🔄 Próximos Pasos

1. ~~Migrar todas las páginas al nuevo sistema~~ ✅
2. ~~Crear CrudFormTemplate~~ ✅
3. Crear CrudDetailTemplate
4. Implementar temas personalizables
5. Agregar más variantes de componentes
6. ~~Migrar módulo Orders al sistema unificado~~ ✅

## 📚 Referencias

- [HeroUI Documentation](https://heroui.com)
- [Next.js App Router](https://nextjs.org/docs/app)
- [Framer Motion](https://www.framer.com/motion/)