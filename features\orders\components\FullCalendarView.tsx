import React, {
    useState,
    useEffect,
    useRef,
    useMemo,
    useCallback,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
// Nota: Si necesitas instalar este paquete, puedes hacerlo con: npm install @fullcalendar/list
// import listPlugin from "@fullcalendar/list";
import {
    format,
    differenceInDays,
    startOfMonth,
    endOfMonth,
    eachDayOfInterval,
} from "date-fns";
import { es } from "date-fns/locale";
import { useRouter, useSearchParams } from "next/navigation";
import {
    Card,
    CardHeader,
    CardBody,
    CardFooter,
    Button,
    ButtonGroup,
    Chip,
    Select,
    SelectItem,
    Badge,
    useDisclosure,
} from "@heroui/react";
import {
    CalendarIcon,
    EyeIcon,
    TruckIcon,
    ClockIcon,
    BuildingStorefrontIcon,
    ReceiptPercentIcon,
    TagIcon,
} from "@heroicons/react/24/outline";

// Importaciones para fecha y utilidades
import {
    isSameDay,
    adjustTimezoneOffset,
    DateDisplayType,
    normalizeDate,
} from "@/shared/utils";
import { DateDisplay } from "@/features/orders/components/DateDisplay";
import { Order, OrderStatus } from "@/features/orders/types/orders";

// Funciones de utilidad adicionales implementadas localmente

/**
 * Obtiene un rango de fechas entre dos fechas dadas
 */
const getDatesBetween = (startDate: Date, endDate: Date): Date[] => {
    return eachDayOfInterval({ start: startDate, end: endDate });
};

/**
 * Formatea la distancia temporal desde ahora
 */
const getFormatDistanceToNow = (date: Date | string): string => {
    const normalizedDate = typeof date === "string" ? new Date(date) : date;
    const days = differenceInDays(new Date(), normalizedDate);

    if (days === 0) return "Hoy";
    if (days === 1) return "Ayer";
    if (days < 7) return `Hace ${days} días`;
    if (days < 30) return `Hace ${Math.floor(days / 7)} semanas`;
    if (days < 365) return `Hace ${Math.floor(days / 30)} meses`;

    return `Hace ${Math.floor(days / 365)} años`;
};

/**
 * Tipos para filtrado y visualización de eventos en el calendario
 */

// Tipos de filtrado por fecha
type DateFilterType = "delivery" | "received" | "both";

// Tipos de eventos en el calendario
type CalendarEventType = "delivery" | "received" | "both";

// Estado para almacenar preferencias del calendario
interface CalendarPreferences {
    viewMode: "dayGridMonth" | "timeGridWeek" | "timeGridDay" | "listWeek";
    filterType: DateFilterType;
    showWeekends: boolean;
    highlightToday: boolean;
    compactView: boolean;
    eventDisplay: "auto" | "block" | "list-item";
    expandDetailPanel: boolean;
}

// Tipo para eventos en el calendario
interface CalendarEvent {
    id: string;
    title: string;
    start: Date | string;
    end?: Date | string;
    allDay: boolean;
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    className: string;
    extendedProps: {
        order: Order;
        type: CalendarEventType;
    };
}

// Interfaz para props del componente
interface FullCalendarViewProps {
    orders: Order[];
    onOpenDetail: (order: Order) => void;
}

// Mapeo de estados a colores para eventos
const getStatusColor = (status?: OrderStatus): string => {
    if (!status) return "#9ca3af"; // gray-400

    switch (status.name) {
        case "Completado":
        case "Entregado":
            return "#16a34a"; // green-600
        case "Cancelado":
        case "Rechazado":
            return "#dc2626"; // red-600
        case "En Proceso":
            return "#2563eb"; // blue-600
        case "Pendiente":
            return "#d97706"; // amber-600
        default:
            return "#4b5563"; // gray-600
    }
};

// Función auxiliar para comparar fechas ignorando milisegundos
const isEquivalentDate = (date1: Date, date2: Date): boolean => {
    return (
        date1.getFullYear() === date2.getFullYear() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getDate() === date2.getDate()
    );
};

// Variables globales para rastrear peticiones de actualización y estabilizar renders
declare global {
    interface Window {
        __lastCalendarRequestId?: string;
        __calendarDataCache?: {
            orders?: Order[];
            events?: any[];
            lastUpdated?: number;
            visibleRange?: [Date, Date];
        };
    }
}

// Inicializar cache global si no existe
if (typeof window !== "undefined" && !window.__calendarDataCache) {
    window.__calendarDataCache = {
        orders: [],
        lastUpdated: 0,
        visibleRange: [new Date(), new Date()],
    };
}

// Función auxiliar para verificar si dos rangos de fechas se superponen
const dateRangesOverlap = (
    range1: [Date, Date],
    range2: [Date, Date],
): boolean => {
    // Verificar si hay superposición: si el final de range1 es posterior al inicio de range2
    // y el inicio de range1 es anterior al final de range2
    return range1[1] >= range2[0] && range1[0] <= range2[1];
};

// Animaciones para los componentes
const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
};

const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 },
};

const slideIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3 },
};

// Componente principal - Rediseñado y optimizado
const FullCalendarView: React.FC<FullCalendarViewProps> = ({
    orders,
    onOpenDetail,
}) => {
    // Hooks para navegación - deben declararse antes de usarlos
    const router = useRouter();
    const searchParams = useSearchParams();

    // Referencias y estados para manejo del calendario
    const calendarRef = useRef<any>(null);
    const lastRequestId = useRef<string>("");
    const isFirstRender = useRef<boolean>(true);
    const renderCount = useRef<number>(0);

    // Preferencias del calendario - con valores iniciales
    const [preferences, setPreferences] = useState<CalendarPreferences>({
        viewMode:
            searchParams?.get("view") === "week"
                ? "timeGridWeek"
                : searchParams?.get("view") === "day"
                  ? "timeGridDay"
                  : searchParams?.get("view") === "list"
                    ? "listWeek"
                    : "dayGridMonth",
        filterType: (searchParams?.get("filter") as DateFilterType) || "both",
        showWeekends: searchParams?.get("weekends") !== "false",
        highlightToday: true,
        compactView: false,
        eventDisplay: "auto",
        expandDetailPanel: true,
    });

    // Estados principales para el calendario
    const [selectedDate, setSelectedDate] = useState<Date>(() => {
        // Recuperar fecha desde URL o usar hoy
        const dateParam = searchParams?.get("date");

        if (dateParam) {
            const parsedDate = new Date(dateParam);

            return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
        }

        return new Date();
    });

    const [dateRange, setDateRange] = useState<[Date, Date]>([
        startOfMonth(selectedDate),
        endOfMonth(selectedDate),
    ]);

    const [selectedDateOrders, setSelectedDateOrders] = useState<Order[]>([]);
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [activeTab, setActiveTab] = useState<"calendar" | "list" | "stats">(
        "calendar",
    );
    const [calendarKey, setCalendarKey] = useState<number>(0);

    // Disclosure hooks para componentes expandibles
    const {
        isOpen: isFilterOpen,
        onOpen: openFilter,
        onClose: closeFilter,
    } = useDisclosure();
    const {
        isOpen: isDetailOpen,
        onOpen: openDetail,
        onClose: closeDetail,
    } = useDisclosure({
        defaultOpen: preferences.expandDetailPanel,
    });

    // FUNCIONES PRINCIPALES DEL COMPONENTE

    /**
     * Navegar al día actual en el calendario
     */
    const goToToday = useCallback(() => {
        const today = new Date();

        setSelectedDate(today);
        if (calendarRef.current) {
            calendarRef.current.getApi().gotoDate(today);
        }
    }, []);

    /**
     * Cambiar el modo de visualización del calendario
     */
    const handleViewChange = useCallback(
        (newView: CalendarPreferences["viewMode"]) => {
            setPreferences((prev) => ({
                ...prev,
                viewMode: newView,
            }));
            setCalendarKey((prev) => prev + 1); // Forzar re-renderizado al cambiar vista
        },
        [],
    );

    /**
     * Actualizar las preferencias del calendario
     */
    const updatePreference = useCallback(
        <K extends keyof CalendarPreferences>(
            key: K,
            value: CalendarPreferences[K],
        ) => {
            setPreferences((prev) => ({
                ...prev,
                [key]: value,
            }));
        },
        [],
    );

    /**
     * Alternar valores booleanos de componentes expandibles
     */
    const toggleFilter = useCallback(() => {
        isFilterOpen ? closeFilter() : openFilter();
    }, [isFilterOpen, closeFilter, openFilter]);

    const toggleDetail = useCallback(() => {
        isDetailOpen ? closeDetail() : openDetail();
    }, [isDetailOpen, closeDetail, openDetail]);

    /**
     * Guardar preferencias del calendario en localStorage en lugar de URL
     */
    const saveCalendarPreferences = useCallback(() => {
        if (typeof window === "undefined") return;

        try {
            const prefsToSave = {
                dateStr: selectedDate.toISOString(),
                viewMode: preferences.viewMode,
                filterType: preferences.filterType,
                showWeekends: preferences.showWeekends,
            };

            localStorage.setItem(
                "calendarPreferences",
                JSON.stringify(prefsToSave),
            );
        } catch (err) {
            // REMOVED: console.error("Error saving calendar preferences:", err);
        }
    }, [selectedDate, preferences]);

    // Efecto para cargar preferencias guardadas al iniciar
    useEffect(() => {
        if (typeof window === "undefined" || !isFirstRender.current) return;

        try {
            const savedPrefs = localStorage.getItem("calendarPreferences");

            if (savedPrefs) {
                const prefs = JSON.parse(savedPrefs);

                if (prefs.dateStr) setSelectedDate(new Date(prefs.dateStr));
                if (prefs.viewMode) {
                    setPreferences((prev) => ({
                        ...prev,
                        viewMode: prefs.viewMode,
                        filterType: prefs.filterType || prev.filterType,
                        showWeekends:
                            prefs.showWeekends !== undefined
                                ? prefs.showWeekends
                                : prev.showWeekends,
                    }));
                }
            }
        } catch (err) {
            // REMOVED: console.error("Error loading calendar preferences:", err);
        }
    }, []);

    /**
     * Efectos para sincronizar la interfaz y gestionar actualizaciones
     */

    // Función para filtrar órdenes según la fecha seleccionada y tipo de filtro
    const filterOrdersByDate = useCallback(
        (
            allOrders: Order[],
            date: Date,
            filterType: DateFilterType,
        ): Order[] => {
            if (!allOrders || !date) return [];

            return allOrders.filter((order) => {
                if (filterType === "delivery" || filterType === "both") {
                    // Verificar fechas de entrega estimada
                    const deliveryDate = normalizeDate(
                        order.estimatedDeliveryDate,
                    );

                    if (deliveryDate && isSameDay(deliveryDate, date)) {
                        return true;
                    }
                }

                if (filterType === "received" || filterType === "both") {
                    // Verificar fechas de recepción
                    const receivedDate = normalizeDate(order.receivedDate);

                    if (receivedDate && isSameDay(receivedDate, date)) {
                        return true;
                    }
                }

                return false;
            });
        },
        [],
    );

    // Efecto para guardar preferencias cuando cambian (en localStorage en lugar de URL)
    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;

            return;
        }
        // Guardar en localStorage en lugar de URL para evitar ciclos de revalidación
        saveCalendarPreferences();
    }, [selectedDate, preferences, saveCalendarPreferences]);

    // Efecto para actualizar la vista del calendario cuando cambia de mes/semana
    useEffect(() => {
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi();

            if (preferences.viewMode !== calendarApi.view.type) {
                calendarApi.changeView(preferences.viewMode);
            }
        }
    }, [preferences.viewMode]);

    // Efecto para activar/desactivar fines de semana
    useEffect(() => {
        if (calendarRef.current) {
            const calendarApi = calendarRef.current.getApi();

            calendarApi.setOption("weekends", preferences.showWeekends);
        }
    }, [preferences.showWeekends]);

    /**
     * Manejar la selección de fechas en el calendario
     */
    const handleDateSelect = useCallback((info: any) => {
        const newDate = info.start || new Date();

        setSelectedDate(newDate);
    }, []);

    // Estado para almacenar el rango de fechas visible actualmente
    const [visibleRange, setVisibleRange] = useState<[Date, Date]>([
        new Date(),
        new Date(),
    ]);

    // Función para actualizar el rango de fechas visible
    const handleDatesSet = useCallback((info: any) => {
        const newDateRange: [Date, Date] = [info.start, info.end];

        setDateRange(newDateRange);
        setVisibleRange(newDateRange);
    }, []);

    // Efecto para actualizar órdenes seleccionadas cuando cambia la fecha
    // Implementa estabilidad y caché para evitar actualizaciones innecesarias
    useEffect(() => {
        // Generar un ID único para esta petición
        const requestId = Math.random().toString(36).substring(2);

        lastRequestId.current = requestId;

        // Prevenir actualizaciones demasiado frecuentes usando debounce
        // Si han pasado menos de 1 segundo desde la última actualización, usar caché
        const now = Date.now();

        if (
            window.__calendarDataCache &&
            window.__calendarDataCache.lastUpdated &&
            now - window.__calendarDataCache.lastUpdated < 1000
        ) {
            // No actualizar demasiado frecuentemente, usar caché en su lugar
            return;
        }

        // Iniciar carga y filtrar órdenes para la fecha seleccionada
        setIsLoading(true);

        // Usar timeout para reducir la carga y prevenir bloqueos de UI
        const timeoutId = setTimeout(() => {
            // Verificar que esta siga siendo la última petición
            if (lastRequestId.current !== requestId) return;

            const filteredOrders = filterOrdersByDate(
                orders,
                selectedDate,
                preferences.filterType,
            );

            // Actualizar estado y caché global
            setSelectedDateOrders(filteredOrders);
            setIsLoading(false);

            // Guardar en caché global
            if (window.__calendarDataCache) {
                window.__calendarDataCache.lastUpdated = Date.now();
            }
        }, 250); // Aumentado a 250ms para mejor rendimiento

        // Limpiar timeout si el componente se desmonta
        return () => clearTimeout(timeoutId);
    }, [selectedDate, preferences.filterType, orders, filterOrdersByDate]);

    // Efecto para debugging en modo desarrollo
    useEffect(() => {
        if (process.env.NODE_ENV === "development") {
            // Calendario: órdenes cargadas - comentario removido
            // Filtro y configuración de vista - comentario removido
        }
    }, [
        orders.length,
        preferences.viewMode,
        preferences.filterType,
        preferences.showWeekends,
    ]);

    /**
     * FUNCIONES DE PROCESAMIENTO DE DATOS
     */

    // Determinar las órdenes visibles basado en el rango de fechas actual
    const visibleOrders = useMemo(() => {
        // Si no hay órdenes o rango, devolver array vacío
        if (!orders || !orders.length || !dateRange) return [];

        // Filtrar órdenes que tienen fechas dentro del rango visible
        return orders.filter((order) => {
            // Comprobar fechas de entrega
            if (
                preferences.filterType === "delivery" ||
                preferences.filterType === "both"
            ) {
                const deliveryDate = normalizeDate(order.estimatedDeliveryDate);

                if (
                    deliveryDate &&
                    deliveryDate >= dateRange[0] &&
                    deliveryDate <= dateRange[1]
                ) {
                    return true;
                }
            }

            // Comprobar fechas de recepción
            if (
                preferences.filterType === "received" ||
                preferences.filterType === "both"
            ) {
                const receivedDate = normalizeDate(order.receivedDate);

                if (
                    receivedDate &&
                    receivedDate >= dateRange[0] &&
                    receivedDate <= dateRange[1]
                ) {
                    return true;
                }
            }

            return false;
        });
    }, [orders, dateRange, preferences.filterType]);

    // Calcular eventos para el calendario a partir de las órdenes visibles
    // Optimizado con caché y estabilidad para evitar recalculos innecesarios
    const calendarEvents = useMemo(() => {
        // Si no hay cambios significativos desde la última vez, usar versión en caché
        if (
            window.__calendarDataCache &&
            window.__calendarDataCache.visibleRange &&
            window.__calendarDataCache.orders &&
            dateRangesOverlap(
                dateRange,
                window.__calendarDataCache.visibleRange,
            ) &&
            visibleOrders.length === window.__calendarDataCache.orders.length &&
            renderCount.current > 2
        ) {
            // Solo usar caché después de algunos renders iniciales
            const cachedEvents = window.__calendarDataCache.events;

            if (cachedEvents && cachedEvents.length > 0) {
                return cachedEvents;
            }
        }

        if (!visibleOrders || visibleOrders.length === 0) return [];

        const events: any[] = [];

        // Guardar los datos en el caché global
        if (window.__calendarDataCache) {
            window.__calendarDataCache.orders = [...visibleOrders];
            window.__calendarDataCache.visibleRange = [...dateRange];
        }

        visibleOrders.forEach((order) => {
            // Crear eventos para fechas de entrega si aplica el filtro
            if (
                (preferences.filterType === "delivery" ||
                    preferences.filterType === "both") &&
                order.estimatedDeliveryDate
            ) {
                const deliveryDate = normalizeDate(order.estimatedDeliveryDate);

                if (deliveryDate) {
                    const isSelected =
                        selectedDate && isSameDay(deliveryDate, selectedDate);

                    events.push({
                        id: `delivery-${order.id}`,
                        title: `${order.cutOrder || order.id.substring(0, 8)}${order.customer?.name ? ` - ${order.customer.name}` : ""}`,
                        start: deliveryDate,
                        allDay: true,
                        backgroundColor: getStatusColor(order.status as any),
                        borderColor: getStatusColor(order.status as any),
                        textColor: "#ffffff",
                        className: isSelected
                            ? "selected-event delivery-event"
                            : "delivery-event",
                        extendedProps: {
                            order,
                            type: "delivery",
                        },
                    });
                }
            }

            // Crear eventos para fechas de recepción si aplica el filtro
            if (
                (preferences.filterType === "received" ||
                    preferences.filterType === "both") &&
                order.receivedDate
            ) {
                const receivedDate = normalizeDate(order.receivedDate);

                if (receivedDate) {
                    const isSelected =
                        selectedDate && isSameDay(receivedDate, selectedDate);

                    events.push({
                        id: `received-${order.id}`,
                        title: `${order.cutOrder || order.id.substring(0, 8)}${order.customer?.name ? ` - ${order.customer.name}` : ""}`,
                        start: receivedDate,
                        allDay: true,
                        backgroundColor: "#6366f1", // Indigo para fechas de recepción
                        borderColor: "#6366f1",
                        textColor: "#ffffff",
                        className: isSelected
                            ? "selected-event received-event"
                            : "received-event",
                        extendedProps: {
                            order,
                            type: "received",
                        },
                    });
                }
            }
        });

        // Guardar eventos en caché global para futuras referencias
        if (window.__calendarDataCache) {
            window.__calendarDataCache.events = events;
        }

        return events;
    }, [visibleOrders, preferences.filterType, selectedDate, dateRange]);

    // Función para obtener órdenes por fecha y tipo - usado para detalles
    const getOrdersByDate = useCallback(
        (date: Date, filterType: DateFilterType): Order[] => {
            return filterOrdersByDate(orders, date, filterType);
        },
        [orders, filterOrdersByDate],
    );

    // Inicializar/actualizar contador cuando el componente se monta
    useEffect(() => {
        renderCount.current++;
    }, []);

    // Efecto para debugging cuando los rangos de fechas cambian
    useEffect(() => {
        if (process.env.NODE_ENV === "development") {
            // REMOVED: console.log(`Fecha seleccionada: ${selectedDate.toDateString()}`);
            // Rango de fechas - comentario removido
        }
    }, [selectedDate, dateRange]);

    // Función para determinar el color del chip según el estado
    const getChipColor = (statusName?: string) => {
        if (!statusName) return "default";

        switch (statusName) {
            case "Completado":
            case "Entregado":
                return "success";
            case "Cancelado":
            case "Rechazado":
                return "danger";
            case "En Proceso":
                return "primary";
            case "Pendiente":
                return "warning";
            default:
                return "default";
        }
    };

    // Renderizar las órdenes para la fecha seleccionada con mejor diseño
    const renderSelectedDateOrders = () => {
        if (isLoading) {
            // Renderizar esqueletos de carga para una mejor experiencia
            return (
                <div className="animate-pulse space-y-3">
                    {[1, 2, 3].map((i) => (
                        <div
                            key={i}
                            className="flex flex-col p-2 border rounded-md"
                        >
                            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2" />
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
                        </div>
                    ))}
                </div>
            );
        }

        if (!selectedDateOrders || selectedDateOrders.length === 0) {
            // Mensaje cuando no hay órdenes para la fecha seleccionada
            return (
                <div className="flex flex-col items-center justify-center py-8 text-center space-y-3">
                    <CalendarIcon className="w-12 h-12 text-gray-300 dark:text-gray-600" />
                    <div>
                        <p className="text-gray-500 dark:text-gray-400">
                            No hay órdenes para el{" "}
                            {selectedDate
                                ? format(selectedDate, "d 'de' MMMM", {
                                      locale: es,
                                  })
                                : "día seleccionado"}
                        </p>
                        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                            Selecciona otra fecha o ajusta los filtros
                        </p>
                    </div>
                </div>
            );
        }

        return (
            <div className="mt-4 overflow-y-auto max-h-[calc(100vh-300px)]">
                <div className="space-y-10">
                    <AnimatePresence>
                        {selectedDateOrders.map((order, index) => (
                            <motion.div
                                key={order.id}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                initial={{ opacity: 0, y: 20 }}
                                transition={{
                                    duration: 0.3,
                                    delay: index * 0.05,
                                }}
                            >
                                <Card className="shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden">
                                    <CardHeader className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-100 dark:border-gray-800">
                                        <div className="flex justify-between items-center w-full">
                                            <div className="flex flex-col gap-1">
                                                <div className="text-lg font-bold flex items-center gap-2">
                                                    <span>
                                                        Orden de Trabajo #
                                                        {order.cutOrder ||
                                                            order.id.substring(
                                                                0,
                                                                6,
                                                            )}
                                                    </span>
                                                    {order.batch && (
                                                        <Badge
                                                            color="secondary"
                                                            size="sm"
                                                            variant="flat"
                                                        >
                                                            Lote {order.batch}
                                                        </Badge>
                                                    )}
                                                </div>
                                                <div className="flex gap-2 flex-wrap">
                                                    {order.estimatedDeliveryDate && (
                                                        <DateDisplay
                                                            showIcon
                                                            date={
                                                                order.estimatedDeliveryDate
                                                            }
                                                            type={
                                                                DateDisplayType.estimated
                                                            }
                                                        />
                                                    )}
                                                    {order.receivedDate && (
                                                        <DateDisplay
                                                            showIcon
                                                            date={
                                                                order.receivedDate
                                                            }
                                                            type={
                                                                DateDisplayType.received
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            </div>

                                            {order.status && (
                                                <Chip
                                                    className="capitalize"
                                                    color={getChipColor(
                                                        (order.status as any)
                                                            .name,
                                                    )}
                                                    size="md"
                                                    variant="flat"
                                                >
                                                    {(order.status as any).name}
                                                </Chip>
                                            )}
                                        </div>
                                    </CardHeader>
                                    <CardBody className="p-4">
                                        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                                            <div className="flex items-center gap-2">
                                                <BuildingStorefrontIcon className="w-5 h-5 text-gray-400" />
                                                <div className="flex flex-col">
                                                    <span className="text-xs text-gray-500">
                                                        Cliente
                                                    </span>
                                                    <span className="font-medium">
                                                        {order.customer?.name ||
                                                            "Sin cliente"}
                                                    </span>
                                                </div>
                                            </div>

                                            {order.parts &&
                                                order.parts.length > 0 && (
                                                    <div className="flex items-start gap-2">
                                                        <ReceiptPercentIcon className="w-5 h-5 text-gray-400 mt-0.5" />
                                                        <div className="flex flex-col">
                                                            <span className="text-xs text-gray-500">
                                                                Partidas
                                                            </span>
                                                            <div className="flex flex-wrap gap-1 mt-1">
                                                                {order.parts
                                                                    .slice(0, 5)
                                                                    .map(
                                                                        (
                                                                            part,
                                                                        ) => (
                                                                            <Chip
                                                                                key={
                                                                                    part.id
                                                                                }
                                                                                className="font-mono"
                                                                                color="primary"
                                                                                size="sm"
                                                                                variant="flat"
                                                                            >
                                                                                {
                                                                                    part.code
                                                                                }
                                                                            </Chip>
                                                                        ),
                                                                    )}
                                                                {order.parts
                                                                    .length >
                                                                    5 && (
                                                                    <Chip
                                                                        color="default"
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        +
                                                                        {order
                                                                            .parts
                                                                            .length -
                                                                            5}
                                                                    </Chip>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                            {/* Aquí podemos añadir información de prendas si está disponible */}
                                            {order.garments &&
                                                order.garments.length > 0 && (
                                                    <div className="flex items-start gap-2">
                                                        <TagIcon className="w-5 h-5 text-gray-400 mt-0.5" />
                                                        <div className="flex flex-col">
                                                            <span className="text-xs text-gray-500">
                                                                Prendas
                                                            </span>
                                                            <div className="flex flex-wrap gap-1 mt-1">
                                                                {order.garments
                                                                    .slice(0, 3)
                                                                    .map(
                                                                        (
                                                                            garment,
                                                                            i,
                                                                        ) => (
                                                                            <Chip
                                                                                key={
                                                                                    i
                                                                                }
                                                                                color="secondary"
                                                                                size="sm"
                                                                                variant="flat"
                                                                            >
                                                                                {garment.model &&
                                                                                typeof garment.model ===
                                                                                    "object" &&
                                                                                "code" in
                                                                                    garment.model
                                                                                    ? String(
                                                                                          garment
                                                                                              .model
                                                                                              .code,
                                                                                      )
                                                                                    : "Prenda"}{" "}
                                                                                -{" "}
                                                                                {garment
                                                                                    .color
                                                                                    ?.name ||
                                                                                    "Color"}
                                                                            </Chip>
                                                                        ),
                                                                    )}
                                                                {order.garments
                                                                    .length >
                                                                    3 && (
                                                                    <Chip
                                                                        color="default"
                                                                        size="sm"
                                                                        variant="flat"
                                                                    >
                                                                        +
                                                                        {order
                                                                            .garments
                                                                            .length -
                                                                            3}
                                                                    </Chip>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                        </div>
                                    </CardBody>
                                    <CardFooter className="flex justify-end bg-gray-50/80 dark:bg-gray-900/30 border-t border-gray-100 dark:border-gray-800">
                                        <Button
                                            className="gap-1"
                                            color="primary"
                                            size="sm"
                                            startContent={
                                                <EyeIcon className="w-4 h-4" />
                                            }
                                            variant="flat"
                                            onPress={() =>
                                                onOpenDetail?.(order)
                                            }
                                        >
                                            Ver detalles
                                        </Button>
                                    </CardFooter>
                                </Card>
                            </motion.div>
                        ))}
                    </AnimatePresence>
                </div>
            </div>
        );
    };

    return (
        <div key={`calendar-view-${renderCount.current}`} className="space-y-6">
            {/* Controles y filtros */}
            <motion.div
                animate={{ y: 0, opacity: 1 }}
                className="flex flex-col lg:flex-row justify-between gap-4"
                initial={{ y: -20, opacity: 0 }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex flex-wrap gap-3">
                    <ButtonGroup className="shadow-sm" size="sm">
                        <Button
                            className="relative overflow-hidden"
                            color={
                                preferences.viewMode === "dayGridMonth"
                                    ? "primary"
                                    : "default"
                            }
                            variant={
                                preferences.viewMode === "dayGridMonth"
                                    ? "solid"
                                    : "flat"
                            }
                            onPress={() => handleViewChange("dayGridMonth")}
                        >
                            <span className="relative z-10">Mes</span>
                            {preferences.viewMode === "dayGridMonth" && (
                                <motion.div
                                    className="absolute inset-0 bg-primary/10"
                                    layoutId="viewIndicator"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                        damping: 30,
                                    }}
                                />
                            )}
                        </Button>
                        <Button
                            className="relative overflow-hidden"
                            color={
                                preferences.viewMode === "timeGridWeek"
                                    ? "primary"
                                    : "default"
                            }
                            variant={
                                preferences.viewMode === "timeGridWeek"
                                    ? "solid"
                                    : "flat"
                            }
                            onPress={() => handleViewChange("timeGridWeek")}
                        >
                            <span className="relative z-10">Semana</span>
                            {preferences.viewMode === "timeGridWeek" && (
                                <motion.div
                                    className="absolute inset-0 bg-primary/10"
                                    layoutId="viewIndicator"
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                        damping: 30,
                                    }}
                                />
                            )}
                        </Button>
                    </ButtonGroup>

                    <Button
                        size="sm"
                        startContent={<CalendarIcon className="w-4 h-4" />}
                        variant="flat"
                        onPress={goToToday}
                    >
                        Hoy
                    </Button>
                </div>

                <div className="flex flex-wrap gap-3 items-center">
                    <span className="text-sm text-gray-500">
                        Mostrar fechas de:
                    </span>
                    <Select
                        className="w-40"
                        defaultSelectedKeys={[preferences.filterType]}
                        size="sm"
                        onChange={(e) =>
                            updatePreference(
                                "filterType",
                                e.target.value as DateFilterType,
                            )
                        }
                    >
                        <SelectItem key="both" textValue="Ambos tipos">
                            <div className="flex items-center gap-2">
                                <CalendarIcon className="text-primary w-4 h-4" />
                                <span>Ambos tipos</span>
                            </div>
                        </SelectItem>
                        <SelectItem key="delivery" textValue="Entrega">
                            <div className="flex items-center gap-2">
                                <TruckIcon className="text-success w-4 h-4" />
                                <span>Entrega</span>
                            </div>
                        </SelectItem>
                        <SelectItem key="received" textValue="Recepción">
                            <div className="flex items-center gap-2">
                                <ClockIcon className="text-primary w-4 h-4" />
                                <span>Recepción</span>
                            </div>
                        </SelectItem>
                    </Select>

                    <div className="flex items-center gap-2">
                        <Chip color="primary" size="sm" variant="dot">
                            Recibido
                        </Chip>
                        <Chip color="success" size="sm" variant="dot">
                            Entrega
                        </Chip>
                    </div>
                </div>
            </motion.div>

            {/* Layout principal con dos columnas en pantallas grandes */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                {/* Panel de calendario */}
                <motion.div
                    animate="visible"
                    className="lg:col-span-7 xl:col-span-8"
                    initial="hidden"
                    variants={containerVariants}
                >
                    <Card className="shadow-sm border border-gray-200 dark:border-gray-800 overflow-hidden">
                        <CardHeader className="flex gap-2 px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/10 dark:to-indigo-900/10">
                            <CalendarIcon className="w-5 h-5 text-primary" />
                            <div className="flex flex-col">
                                <p className="text-md font-medium">
                                    Calendario de Órdenes
                                </p>
                                <p className="text-xs text-gray-500">
                                    Visualizando entregas en vista de{" "}
                                    {preferences.viewMode === "dayGridMonth"
                                        ? "mes"
                                        : "semana"}
                                </p>
                            </div>
                        </CardHeader>
                        <CardBody className="p-0">
                            <AnimatePresence initial={false} mode="wait">
                                <motion.div
                                    key={`${preferences.viewMode}-${preferences.filterType}`} // Clave estable que solo cambia cuando es necesario
                                    animate={{ opacity: 1, x: 0 }}
                                    className="calendar-wrapper"
                                    exit={{
                                        opacity: 0,
                                        x:
                                            preferences.viewMode ===
                                            "dayGridMonth"
                                                ? 40
                                                : -40,
                                    }}
                                    initial={{
                                        opacity: 0,
                                        x:
                                            preferences.viewMode ===
                                            "dayGridMonth"
                                                ? -40
                                                : 40,
                                    }}
                                    transition={{ duration: 0.3 }}
                                >
                                    <FullCalendar
                                        aspectRatio={1.8}
                                        dateClick={(info) => {
                                            const newDate =
                                                adjustTimezoneOffset(
                                                    new Date(info.dateStr),
                                                ) || new Date();

                                            setSelectedDate(newDate);
                                        }}
                                        datesSet={handleDatesSet}
                                        dayHeaderFormat={{
                                            weekday: "short",
                                            day: "numeric",
                                        }}
                                        dayMaxEvents={4} // Límite de eventos mostrados por día
                                        eventClick={(info) => {
                                            const order =
                                                info.event.extendedProps.order;

                                            if (order) {
                                                setSelectedDate(
                                                    info.event.start ||
                                                        new Date(),
                                                );
                                                // Abrir detalle si se hace clic en un evento
                                                onOpenDetail(order);
                                            }
                                        }}
                                        eventContent={(eventInfo) => {
                                            const type =
                                                eventInfo.event.extendedProps
                                                    .type;
                                            const order =
                                                eventInfo.event.extendedProps
                                                    .order;

                                            // Personalizar la apariencia del evento según el tipo
                                            return {
                                                html: `
                          <div class="event-content flex items-center gap-1 px-1 py-0.5 overflow-hidden text-ellipsis whitespace-nowrap">
                            <span class="inline-block w-2 h-2 rounded-full ${type === "delivery" ? "bg-green-500" : "bg-blue-500"}"></span>
                            <span class="text-xs font-medium">${eventInfo.event.title}</span>
                          </div>
                        `,
                                            };
                                        }}
                                        eventDidMount={(info) => {
                                            // Añadir tooltip con información más detallada
                                            const order =
                                                info.event.extendedProps.order;
                                            const type =
                                                info.event.extendedProps.type;

                                            if (order) {
                                                const tooltipContent =
                                                    document.createElement(
                                                        "div",
                                                    );

                                                tooltipContent.className =
                                                    "tooltip-content p-2";

                                                tooltipContent.innerHTML = `
                          <div class="font-bold">${order.cutOrder || order.transferNumber || order.id.substring(0, 6)}</div>
                          <div class="text-sm">${order.customer?.name || "Sin cliente"}</div>
                          <div class="text-xs mt-1">${type === "delivery" ? "📦 Entrega" : "📥 Recibido"}</div>
                          <div class="text-xs">${order.status?.name || "Sin estado"}</div>
                        `;

                                                info.el.setAttribute(
                                                    "title",
                                                    "",
                                                );
                                                info.el.setAttribute(
                                                    "data-tooltip",
                                                    "true",
                                                );

                                                // Añadir clase para indicar el tipo de evento
                                                info.el.classList.add(
                                                    `event-type-${type}`,
                                                );
                                            }
                                        }}
                                        eventTimeFormat={{
                                            hour: "2-digit",
                                            minute: "2-digit",
                                            meridiem: false,
                                            hour12: false,
                                        }}
                                        events={calendarEvents}
                                        firstDay={1} // Lunes como primer día de la semana
                                        headerToolbar={{
                                            left: "prev,next",
                                            center: "title",
                                            right: "",
                                        }}
                                        height="auto"
                                        initialDate={selectedDate}
                                        initialView={preferences.viewMode}
                                        locale="es"
                                        plugins={[
                                            dayGridPlugin,
                                            timeGridPlugin,
                                            interactionPlugin,
                                        ]}
                                        select={handleDateSelect}
                                        stickyHeaderDates={true}
                                    />
                                </motion.div>
                            </AnimatePresence>
                        </CardBody>
                    </Card>
                </motion.div>

                {/* Panel de órdenes del día seleccionado */}
                <motion.div
                    animate="visible"
                    className="lg:col-span-5 xl:col-span-4"
                    initial="hidden"
                    variants={containerVariants}
                >
                    <Card className="shadow-sm border border-gray-200 dark:border-gray-800">
                        <CardHeader className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/10 dark:to-indigo-900/10">
                            <div className="flex flex-col">
                                <motion.p
                                    key={selectedDate.toISOString()}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="text-md font-medium"
                                    initial={{ opacity: 0, y: -5 }}
                                    transition={{ duration: 0.3 }}
                                >
                                    Órdenes para{" "}
                                    {selectedDate
                                        ? format(
                                              selectedDate,
                                              "d 'de' MMMM yyyy",
                                              { locale: es },
                                          )
                                        : "Selecciona una fecha"}
                                </motion.p>
                                <p className="text-xs text-gray-500">
                                    {selectedDateOrders.length}{" "}
                                    {selectedDateOrders.length === 1
                                        ? "orden"
                                        : "órdenes"}
                                </p>
                            </div>
                        </CardHeader>
                        <CardBody className="p-4 overflow-hidden bg-white dark:bg-gray-950">
                            {renderSelectedDateOrders()}
                        </CardBody>
                    </Card>
                </motion.div>
            </div>
        </div>
    );
};

// Función para comparar órdenes con deep equality en campos importantes
const areOrdersEquivalent = (prev: Order[], next: Order[]): boolean => {
    if (prev.length !== next.length) return false;

    // Crear diccionarios para búsquedas rápidas
    const prevMap = new Map(prev.map((o) => [o.id, o]));

    // Verificar si todos los elementos en next existen en prev y son equivalentes
    return next.every((order) => {
        const prevOrder = prevMap.get(order.id);

        if (!prevOrder) return false;

        // Comparar solo campos relevantes para el calendario
        return (
            order.id === prevOrder.id &&
            order.estimatedDeliveryDate === prevOrder.estimatedDeliveryDate &&
            order.receivedDate === prevOrder.receivedDate &&
            (order.status as any)?.id === (prevOrder.status as any)?.id
        );
    });
};

// Exportar componente optimizado con React.memo y comparador personalizado mejorado
export default React.memo(FullCalendarView, (prevProps, nextProps) => {
    // Implementación personalizada de comparación para evitar re-renders innecesarios
    if (prevProps.orders.length !== nextProps.orders.length) {
        // Si hay diferencia en la cantidad, checar si es un cambio significativo
        const diff = Math.abs(
            prevProps.orders.length - nextProps.orders.length,
        );

        // Si la diferencia es pequeña (menor al 5% del total), hacer una comparación más profunda
        if (
            diff <=
            Math.max(prevProps.orders.length, nextProps.orders.length) * 0.05
        ) {
            return areOrdersEquivalent(prevProps.orders, nextProps.orders);
        }

        return false;
    }

    // Usar la función de equivalencia para comparación profunda
    return areOrdersEquivalent(prevProps.orders, nextProps.orders);
});
