"use client";

import type { PackingPreviewProps } from "./types";

import React from "react";

import styles from "./PackingPreview.module.css";

// Import child components
import { PackingHeader } from "./components/PackingHeader";
import { PackingProductsTable } from "./components/PackingProductsTable";
import { PackingSummary } from "./components/PackingSummary";
import { PackingSignatures } from "./components/PackingSignatures";

export const PackingPreview: React.FC<PackingPreviewProps> = ({
    data,
    packingId,
    orientation = "portrait",
    editMode = false,
    zoom = 100,
    onEdit,
    className,
}) => {
    const handleFieldEdit = (field: string, value: any) => {
        if (editMode && onEdit) {
            onEdit(field, value);
        }
    };

    const containerClasses = [
        styles.packingPreview,
        orientation === "portrait"
            ? styles.packingPreviewPortrait
            : styles.packingPreviewLandscape,
        editMode && styles.packingPreviewEditable,
        className,
    ]
        .filter(Boolean)
        .join(" ");

    const containerStyle = {
        transform: `scale(${zoom / 100})`,
        transformOrigin: "top center",
    };

    // Generate ID for PDF generation - use packingId if provided, otherwise use folio
    const documentId = packingId
        ? `packing-document-${packingId}`
        : `packing-document-${data.document.folio}`;

    return (
        <div
            className={containerClasses}
            id={documentId}
            style={containerStyle}
        >
            <PackingHeader
                company={data.company}
                document={data.document}
                editMode={editMode}
                onEdit={handleFieldEdit}
            />

            <PackingProductsTable
                editMode={editMode}
                items={data.items}
                onEdit={handleFieldEdit}
            />

            <PackingSummary
                customer={data.customer}
                editMode={editMode}
                order={data.order}
                summary={data.summary}
                onEdit={handleFieldEdit}
            />

            <PackingSignatures
                editMode={editMode}
                receiver={data.receiver}
                transport={data.transport}
                onEdit={handleFieldEdit}
            />
        </div>
    );
};

// Export default for easier imports
export default PackingPreview;
