"use server";

import { db } from "@/shared/lib/db";
import {
    integrityManager,
    compensationStrategies,
} from "@/features/shared/integrity";
import { processAssignmentsForRemission } from "@/features/remissions/actions/remission-utils";
import { createRemissionInTransaction } from "@/features/remissions/actions/helpers";
import { RemissionPreview } from "@/types/remission";
import { getCurrentUser } from "@/shared/utils/actions-utils";

import { createAssignmentsSchema, AssignmentResponse } from "../schemas/schema";

import { revalidateAssignmentPaths } from "./revalidate";

// Constants
const VOLUME_THRESHOLD = 5; // Split transactions for > 5 items (reduced for better performance)

/**
 * Transform remission data to RemissionPreview format
 */
function transformToRemissionPreview(
    remission: any,
    contractor: any,
    orderDetails: any,
): RemissionPreview {
    const items = remission.remissionItems.map((item: any) => ({
        orderNumber: orderDetails.orderNumber || "N/A",
        model: item.modelCode,
        color: item.colorName,
        quantity: item.quantity,
        quantityDelivered: item.quantity,
        pendingQuantity: 0,
        unitPrice: 0,
        totalPrice: 0,
    }));

    return {
        folio: remission.folio,
        date: remission.createdAt.toISOString(),
        contractor: {
            name: contractor.name,
            location: (contractor as any).location || "",
            contact: contractor.phone || contractor.email || "",
        } as any,
        items,
        internalNotes: remission.notes || "",
        externalNotes: "",
        deliveredBy: "",
        receivedBy: contractor.name,
        authorizedBy: "",
        showPreview: true,
    } as any;
}

/**
 * Create assignments with single transaction (optimized for small batches)
 */
async function createAssignmentsSingleTx(
    contractorId: string,
    assignments: Array<{
        orderId: string;
        garmentSizeId: string;
        quantity: number;
    }>,
    contractor: any,
) {
    return await db.$transaction(
        async (tx) => {
            const createdAssignments: Awaited<
                ReturnType<typeof tx.assignment.create>
            >[] = [];

            // Use createMany for better performance when possible
            if (
                assignments.every((a) => a.orderId === assignments[0].orderId)
            ) {
                // All assignments for same order - can use createMany
                const assignmentData = assignments.map((assignment) => ({
                    contractorId,
                    garmentSizeId: assignment.garmentSizeId,
                    orderId: assignment.orderId,
                    quantity: assignment.quantity,
                    isCompleted: false,
                    status: "ACTIVE",
                }));

                await tx.assignment.createMany({
                    data: assignmentData,
                });

                // Fetch created assignments with minimal relations for remission
                const newAssignments = await tx.assignment.findMany({
                    where: {
                        contractorId,
                        createdAt: {
                            gte: new Date(Date.now() - 1000), // Last second
                        },
                    },
                    include: {
                        garmentSize: {
                            select: {
                                id: true,
                                size: {
                                    select: { code: true },
                                },
                                garment: {
                                    select: {
                                        model: { select: { code: true } },
                                        color: { select: { name: true } },
                                    },
                                },
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                    take: assignments.length,
                });

                createdAssignments.push(...newAssignments);
            } else {
                // Different orders - need individual creates
                for (const assignment of assignments) {
                    const newAssignment = await tx.assignment.create({
                        data: {
                            contractorId,
                            garmentSizeId: assignment.garmentSizeId,
                            orderId: assignment.orderId,
                            quantity: assignment.quantity,
                            isCompleted: false,
                            status: "ACTIVE",
                        },
                        include: {
                            garmentSize: {
                                select: {
                                    id: true,
                                    size: {
                                        select: { code: true },
                                    },
                                    garment: {
                                        select: {
                                            model: { select: { code: true } },
                                            color: { select: { name: true } },
                                        },
                                    },
                                },
                            },
                        },
                    });

                    createdAssignments.push(newAssignment);
                }
            }

            // Batch update garmentSize quantities
            const updates = assignments.map((assignment) =>
                tx.garmentSize.update({
                    where: { id: assignment.garmentSizeId },
                    data: {
                        usedQuantity: {
                            increment: assignment.quantity,
                        },
                    },
                }),
            );

            await Promise.all(updates);

            // Create remission
            const remissionItems = processAssignmentsForRemission(
                createdAssignments as any,
            );
            const assignmentIds = createdAssignments.map((a) => a.id);
            const orderId = assignments[0].orderId;

            const remission = await createRemissionInTransaction(tx, {
                assignmentIds,
                contractorId,
                orderId,
                items: remissionItems,
                notes: `Remisión generada automáticamente para ${remissionItems.length} items`,
            });

            // Get complete remission data
            const completeRemission = await tx.remission.findUnique({
                where: { id: remission.id },
                include: {
                    remissionItems: true,
                    assignments: {
                        include: {
                            assignment: {
                                include: {
                                    order: true,
                                },
                            },
                        },
                    },
                },
            });

            let remissionPreview = null;

            if (completeRemission && completeRemission.assignments.length > 0) {
                const orderDetails =
                    completeRemission.assignments[0].assignment.order;

                remissionPreview = transformToRemissionPreview(
                    completeRemission,
                    contractor,
                    orderDetails,
                );
            }

            return { createdAssignments, remission, remissionPreview };
        },
        {
            timeout: Math.max(20000, assignments.length * 2000), // Dynamic timeout: 2s per item, minimum 20s
        },
    );
}

/**
 * Create assignments with split transactions (for large batches)
 */
async function createAssignmentsSplitTx(
    contractorId: string,
    assignments: Array<{
        orderId: string;
        garmentSizeId: string;
        quantity: number;
    }>,
    contractor: any,
) {
    // Phase 1: Create assignments and update quantities
    const assignmentsResult = await db.$transaction(
        async (tx) => {
            const createdAssignments: Awaited<
                ReturnType<typeof tx.assignment.create>
            >[] = [];

            // Batch create assignments
            const assignmentData = assignments.map((assignment) => ({
                contractorId,
                garmentSizeId: assignment.garmentSizeId,
                orderId: assignment.orderId,
                quantity: assignment.quantity,
                isCompleted: false,
                status: "ACTIVE",
            }));

            await tx.assignment.createMany({
                data: assignmentData,
            });

            // Fetch created assignments with minimal data
            const newAssignments = await tx.assignment.findMany({
                where: {
                    contractorId,
                    createdAt: {
                        gte: new Date(Date.now() - 2000), // Last 2 seconds
                    },
                },
                include: {
                    garmentSize: {
                        select: {
                            id: true,
                            size: {
                                select: { code: true },
                            },
                            garment: {
                                select: {
                                    model: { select: { code: true } },
                                    color: { select: { name: true } },
                                },
                            },
                        },
                    },
                },
                orderBy: {
                    createdAt: "desc",
                },
                take: assignments.length,
            });

            createdAssignments.push(...newAssignments);

            // Batch update quantities
            const updates = assignments.map((assignment) =>
                tx.garmentSize.update({
                    where: { id: assignment.garmentSizeId },
                    data: {
                        usedQuantity: {
                            increment: assignment.quantity,
                        },
                    },
                }),
            );

            await Promise.all(updates);

            return createdAssignments;
        },
        {
            timeout: Math.max(15000, assignments.length * 1500), // Dynamic timeout: 1.5s per item, minimum 15s
        },
    );

    // Phase 2: Create remission (separate transaction)
    let remission = null;
    let remissionPreview = null;

    try {
        const remissionResult = await db.$transaction(
            async (tx) => {
                const remissionItems = processAssignmentsForRemission(
                    assignmentsResult as any,
                );
                const assignmentIds = assignmentsResult.map((a) => a.id);
                const orderId = assignments[0].orderId;

                const newRemission = await createRemissionInTransaction(tx, {
                    assignmentIds,
                    contractorId,
                    orderId,
                    items: remissionItems,
                    notes: `Remisión generada automáticamente para ${remissionItems.length} items (batch procesado)`,
                });

                // Get complete remission data
                const completeRemission = await tx.remission.findUnique({
                    where: { id: newRemission.id },
                    include: {
                        remissionItems: true,
                        assignments: {
                            include: {
                                assignment: {
                                    include: {
                                        order: true,
                                    },
                                },
                            },
                        },
                    },
                });

                return completeRemission;
            },
            {
                timeout: 5000, // 5 seconds for remission
            },
        );

        if (remissionResult && remissionResult.assignments.length > 0) {
            remission = remissionResult;
            const orderDetails =
                remissionResult.assignments[0].assignment.order;

            remissionPreview = transformToRemissionPreview(
                remissionResult,
                contractor,
                orderDetails,
            );
        }
    } catch (remissionError) {
        console.error(
            "Error creating remission in split transaction:",
            remissionError,
        );
        // Don't fail the whole operation if remission fails
    }

    return {
        createdAssignments: assignmentsResult,
        remission,
        remissionPreview,
    };
}

/**
 * Retry wrapper for transaction operations
 */
async function retryOperation<T>(
    operation: () => Promise<T>,
    retries: number = 1,
    onRetry?: (attempt: number, error: any) => void,
): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= retries + 1; attempt++) {
        try {
            return await operation();
        } catch (error: any) {
            lastError = error;

            // Check if error is a timeout error
            const isTimeoutError =
                error?.message?.includes("Transaction already closed") ||
                error?.message?.includes("timeout");

            if (isTimeoutError && attempt <= retries) {
                if (onRetry) onRetry(attempt, error);
                // Wait before retry (exponential backoff)
                await new Promise((resolve) =>
                    setTimeout(resolve, attempt * 1000),
                );
                continue;
            }

            throw error;
        }
    }

    throw lastError;
}

/**
 * Main function: Create multiple assignments with integrity guarantees
 */
export async function createAssignments(data: {
    contractorId: string;
    assignments: {
        orderId: string;
        garmentSizeId: string;
        quantity: number;
    }[];
}): Promise<AssignmentResponse> {
    const startTime = Date.now();

    console.log(
        `[createAssignments] Starting with ${data.assignments.length} assignments`,
    );

    try {
        // Validate input data
        const validationResult = createAssignmentsSchema.safeParse(data);

        if (!validationResult.success) {
            return {
                success: false,
                error: "Datos de asignación inválidos",
            };
        }

        const { contractorId, assignments } = validationResult.data;

        // Get current user
        const currentUser = await getCurrentUser();

        if (!currentUser || !currentUser.id) {
            return {
                success: false,
                error: "Usuario no autenticado",
            };
        }

        // Verify contractor exists
        const contractor = await db.contractor.findUnique({
            where: { id: contractorId },
        });

        if (!contractor) {
            return {
                success: false,
                error: "El contratista seleccionado no existe",
            };
        }

        // Validate all assignments
        const validationPromises = assignments.map(async (assignment) => {
            const garmentSize = await db.garmentSize.findUnique({
                where: { id: assignment.garmentSizeId },
                select: {
                    totalQuantity: true,
                    usedQuantity: true,
                    garment: {
                        select: {
                            orderId: true,
                            model: { select: { code: true } },
                            color: { select: { name: true } },
                        },
                    },
                    size: { select: { code: true } },
                },
            });

            if (!garmentSize) {
                return {
                    valid: false,
                    error: `La talla con ID ${assignment.garmentSizeId} no existe`,
                };
            }

            if (garmentSize.garment.orderId !== assignment.orderId) {
                return {
                    valid: false,
                    error: `La talla no pertenece a la orden especificada`,
                };
            }

            const availableQuantity =
                garmentSize.totalQuantity - garmentSize.usedQuantity;

            if (assignment.quantity > availableQuantity) {
                return {
                    valid: false,
                    error: `Cantidad insuficiente disponible para ${garmentSize.garment.model.code} ${garmentSize.garment.color.name} talla ${garmentSize.size.code}`,
                    details: {
                        requested: assignment.quantity,
                        available: availableQuantity,
                    },
                };
            }

            return { valid: true, garmentSize };
        });

        const validationResults = await Promise.all(validationPromises);
        const invalidAssignments = validationResults.filter(
            (result) => !result.valid,
        );

        if (invalidAssignments.length > 0) {
            return {
                success: false,
                error: "Algunas asignaciones no son válidas",
                data: { errors: invalidAssignments },
            };
        }

        // Execute with integrity manager and retry logic
        const result = await retryOperation(
            async () => {
                console.log(
                    `[createAssignments] Executing strategy: ${assignments.length > VOLUME_THRESHOLD ? "split" : "single"}`,
                );

                return await integrityManager.executeWithIntegrity(
                    {
                        type: "ASSIGNMENT_BATCH",
                        userId: currentUser.id,
                        metadata: {
                            contractorId,
                            assignmentCount: assignments.length,
                            volumeThreshold: VOLUME_THRESHOLD,
                            strategy:
                                assignments.length > VOLUME_THRESHOLD
                                    ? "split"
                                    : "single",
                        },
                    },
                    async () => {
                        // Choose strategy based on volume
                        if (assignments.length <= VOLUME_THRESHOLD) {
                            return await createAssignmentsSingleTx(
                                contractorId,
                                assignments,
                                contractor,
                            );
                        } else {
                            return await createAssignmentsSplitTx(
                                contractorId,
                                assignments,
                                contractor,
                            );
                        }
                    },
                    compensationStrategies.compensateAssignmentBatch,
                );
            },
            1, // One retry on timeout
            (attempt, error) => {
                console.warn(
                    `[createAssignments] Retry attempt ${attempt} due to:`,
                    error.message,
                );
                // If it's a timeout on a large batch, we could split it further
                if (assignments.length > VOLUME_THRESHOLD) {
                    console.log(
                        `[createAssignments] Consider reducing batch size from ${assignments.length} items`,
                    );
                }
            },
        );

        // Revalidate paths
        revalidateAssignmentPaths();

        const executionTime = Date.now() - startTime;

        console.log(
            `[createAssignments] Completed successfully in ${executionTime}ms`,
        );

        return {
            success: true,
            data: {
                assignments: result.createdAssignments,
                remission: result.remission
                    ? {
                          id: result.remission.id,
                          folio: result.remission.folio,
                          createdAt: result.remission.createdAt,
                      }
                    : undefined,
                remissionPreview: result.remissionPreview || undefined,
            },
        };
    } catch (error) {
        const executionTime = Date.now() - startTime;

        console.error(
            `[createAssignments] Failed after ${executionTime}ms:`,
            error,
        );

        // Log more details for timeout errors
        if (error instanceof Error && error.message.includes("timeout")) {
            console.error("[createAssignments] Timeout details:", {
                assignmentCount: data.assignments.length,
                strategy:
                    data.assignments.length > VOLUME_THRESHOLD
                        ? "split"
                        : "single",
                timeoutDuration: executionTime,
            });
        }

        return {
            success: false,
            error: "Error al crear las asignaciones",
        };
    }
}

/**
 * Validate assignment availability (unchanged)
 */
export async function validateAssignmentAvailability(
    garmentSizeId: string,
    quantity: number,
): Promise<AssignmentResponse> {
    try {
        const garmentSize = await db.garmentSize.findUnique({
            where: { id: garmentSizeId },
            select: {
                totalQuantity: true,
                usedQuantity: true,
                size: { select: { code: true } },
                garment: {
                    select: {
                        model: { select: { code: true } },
                        color: { select: { name: true } },
                    },
                },
            },
        });

        if (!garmentSize) {
            return {
                success: false,
                error: `La talla con ID ${garmentSizeId} no existe`,
            };
        }

        const availableQuantity =
            garmentSize.totalQuantity - garmentSize.usedQuantity;

        if (quantity > availableQuantity) {
            return {
                success: false,
                error: `Cantidad insuficiente disponible. Máximo: ${availableQuantity}`,
                data: {
                    requested: quantity,
                    available: availableQuantity,
                    garmentSizeId,
                    modelCode: garmentSize.garment.model.code,
                    colorName: garmentSize.garment.color.name,
                    sizeCode: garmentSize.size.code,
                } as any,
            };
        }

        return {
            success: true,
            data: {
                valid: true,
                requested: quantity,
                available: availableQuantity,
                garmentSizeId,
                modelCode: garmentSize.garment.model.code,
                colorName: garmentSize.garment.color.name,
                sizeCode: garmentSize.size.code,
            } as any,
        };
    } catch (error) {
        console.error("Error al validar disponibilidad:", error);

        return {
            success: false,
            error: "Error al validar la disponibilidad",
        };
    }
}
