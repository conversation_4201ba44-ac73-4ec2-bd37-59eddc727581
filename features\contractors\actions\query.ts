"use server";

import { Prisma } from "@prisma/client";

import { db } from "@/shared/lib/db";

import { ContractorsListResponse, ContractorResponse } from "../schemas/schema";

/**
 * Obtiene una lista de contratistas
 * @param options Opciones de consulta
 */
export async function getContractors(
    options: {
        search?: string;
        orderBy?: string;
        order?: "asc" | "desc";
        page?: number;
        perPage?: number;
    } = {},
): Promise<ContractorsListResponse> {
    const {
        search = "",
        orderBy = "createdAt",
        order = "desc",
        page = 1,
        perPage = 10,
    } = options;

    try {
        // Construir filtros
        const filters: Prisma.ContractorWhereInput = {};

        // Filtro por término de búsqueda
        if (search) {
            filters.name = {
                contains: search,
                mode: Prisma.QueryMode.insensitive,
            };
        }

        // Calcular skip y take para paginación
        const skip = Math.max(0, (page - 1) * perPage);
        const take = Math.max(1, perPage);

        // Construir ordenamiento
        const orderByObj: any = {};

        orderByObj[orderBy] = order;

        // Obtener contratistas y total
        const [contractors, total] = await Promise.all([
            db.contractor.findMany({
                where: filters,
                orderBy: orderByObj,
                skip,
                take,
                include: {
                    _count: {
                        select: {
                            assignments: true,
                            remissions: true,
                        },
                    },
                },
            }),
            db.contractor.count({
                where: filters,
            }),
        ]);

        // Transformar fechas para evitar problemas de serialización
        const serializedContractors = contractors.map((contractor) => ({
            ...contractor,
            createdAt: contractor.createdAt.toISOString(),
            updatedAt: contractor.updatedAt.toISOString(),
        }));

        // Calcular paginación
        const totalPages = Math.ceil(total / perPage);
        const hasMore = page < totalPages;

        return {
            success: true,
            data: serializedContractors as any,
            pagination: {
                page,
                perPage,
                total,
                totalPages,
                hasMore,
            },
        };
    } catch (error) {
        console.error("[ContractorQuery] Error en getContractors:", error);

        // Respuesta por defecto en caso de error
        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener contratistas",
        };
    }
}

/**
 * Obtiene un contratista por su ID
 * @param id ID del contratista
 */
export async function getContractor(id: string): Promise<ContractorResponse> {
    try {
        // Validar que el ID es un string válido
        if (!id || typeof id !== "string") {
            return {
                success: false,
                error: "ID de contratista inválido",
            };
        }

        // Obtener contratista con conteo de asignaciones
        const contractor = await db.contractor.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        assignments: true,
                        remissions: true,
                    },
                },
            },
        });

        if (!contractor) {
            return {
                success: false,
                error: "Contratista no encontrado",
            };
        }

        // Transformar fechas para evitar problemas de serialización
        const serializedContractor = {
            ...contractor,
            createdAt: contractor.createdAt.toISOString(),
            updatedAt: contractor.updatedAt.toISOString(),
        };

        return { success: true, data: serializedContractor as any };
    } catch (error) {
        console.error("[ContractorQuery] Error en getContractor:", error);

        // Respuesta por defecto en caso de error
        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener contratista",
        };
    }
}

/**
 * Obtiene un contratista con todos los detalles de sus asignaciones
 * @param id ID del contratista
 */
export async function getContractorWithDetails(
    id: string,
): Promise<ContractorResponse> {
    try {
        // Validar que el ID es un string válido
        if (!id || typeof id !== "string") {
            return {
                success: false,
                error: "ID de contratista inválido",
            };
        }

        // Obtener contratista con asignaciones detalladas
        const contractor = await db.contractor.findUnique({
            where: { id },
            include: {
                _count: {
                    select: {
                        assignments: true,
                        remissions: true,
                    },
                },
                assignments: {
                    include: {
                        order: {
                            include: {
                                customer: true,
                                parts: true,
                            },
                        },
                        garmentSize: {
                            include: {
                                size: true,
                                garment: {
                                    include: {
                                        model: true,
                                        color: true,
                                    },
                                },
                            },
                        },
                        remissions: {
                            include: {
                                remission: {
                                    select: {
                                        id: true,
                                        folio: true,
                                        status: true,
                                        printedAt: true,
                                    },
                                },
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
        });

        if (!contractor) {
            return {
                success: false,
                error: "Contratista no encontrado",
            };
        }

        // Transformar fechas para evitar problemas de serialización
        const serializedContractor = {
            ...contractor,
            createdAt: contractor.createdAt.toISOString(),
            updatedAt: contractor.updatedAt.toISOString(),
            assignments: contractor.assignments.map((assignment: any) => ({
                ...assignment,
                createdAt: assignment.createdAt.toISOString(),
                updatedAt: assignment.updatedAt.toISOString(),
                order: {
                    ...assignment.order,
                    createdAt: assignment.order.createdAt.toISOString(),
                    updatedAt: assignment.order.updatedAt.toISOString(),
                    estimatedDeliveryDate:
                        assignment.order.estimatedDeliveryDate?.toISOString() ||
                        null,
                },
                remissions: assignment.remissions.map((r: any) => ({
                    ...r,
                    remission: {
                        ...r.remission,
                        printedAt: r.remission.printedAt?.toISOString() || null,
                    },
                })),
            })),
        };

        return { success: true, data: serializedContractor as any };
    } catch (error) {
        console.error(
            "[ContractorQuery] Error en getContractorWithDetails:",
            error,
        );

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener detalles del contratista",
        };
    }
}

/**
 * Obtiene los contratistas con más asignaciones
 * @param options Opciones para la consulta
 * @returns Lista de contratistas ordenados por cantidad de asignaciones
 */
export async function getTopContractors(
    options: {
        limit?: number;
        startDate?: Date;
        endDate?: Date;
    } = {},
): Promise<{ success: boolean; data?: any[]; error?: string }> {
    const { limit = 5, startDate, endDate } = options;

    try {
        // Construir condiciones para filtros de fecha
        const dateFilter: Prisma.OrderWhereInput = {};

        if (startDate) {
            dateFilter.createdAt = {
                ...((dateFilter.createdAt as object) || {}),
                gte: startDate,
            };
        }
        if (endDate) {
            dateFilter.createdAt = {
                ...((dateFilter.createdAt as object) || {}),
                lte: endDate,
            };
        }

        // Obtener todos los contratistas con conteo de asignaciones
        const contractors = await db.contractor.findMany({
            include: {
                _count: {
                    select: {
                        assignments: true,
                    },
                },
                assignments: {
                    where: {
                        createdAt: dateFilter.createdAt as any,
                    },
                    include: {
                        order: true,
                    },
                },
            },
        });

        // Ordenar manualmente por número de asignaciones (de mayor a menor)
        const sortedContractors = contractors.sort(
            (a, b) => b._count.assignments - a._count.assignments,
        );

        // Tomar solo los top N
        const topContractors = sortedContractors.slice(0, limit);

        // Transformar datos para facilitar su uso en la UI
        const serializedContractors = topContractors.map((contractor) => {
            // Calcular cuántas órdenes únicas ha trabajado este contratista
            const orderIds = new Set();

            contractor.assignments.forEach((assignment) => {
                if (assignment.order?.id) {
                    orderIds.add(assignment.order.id);
                }
            });

            return {
                id: contractor.id,
                name: contractor.firstName
                    ? `${contractor.firstName} ${contractor.lastName || ""}`.trim()
                    : contractor.name,
                assignmentCount: contractor._count.assignments,
                orderCount: orderIds.size,
            };
        });

        return {
            success: true,
            data: serializedContractors,
        };
    } catch (error) {
        console.error("[ContractorQuery] Error en getTopContractors:", error);

        return {
            success: false,
            error:
                error instanceof Error
                    ? error.message
                    : "Error al obtener top contratistas",
        };
    }
}

/**
 * Obtiene todos los contratistas para usar en filtros
 * @returns Lista de contratistas ordenados por nombre
 */
export async function getContractorsForFilter() {
    const response = await getContractors({
        perPage: 100, // Obtener hasta 100 contratistas
        orderBy: "name",
        order: "asc",
    });

    if (response.success && response.data) {
        return response.data;
    }

    return [];
}
