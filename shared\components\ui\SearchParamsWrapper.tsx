"use client";

import { Suspense } from "react";

interface SearchParamsWrapperProps {
    children: React.ReactNode;
    fallback?: React.ReactNode;
}

export function SearchParamsWrapper({
    children,
    fallback = (
        <div className="flex items-center justify-center p-4">Cargando...</div>
    ),
}: SearchParamsWrapperProps) {
    return <Suspense fallback={fallback}>{children}</Suspense>;
}

// Componente de conveniencia para el loading
export function SearchParamsLoader() {
    return (
        <div className="flex items-center justify-center min-h-[200px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
    );
}
