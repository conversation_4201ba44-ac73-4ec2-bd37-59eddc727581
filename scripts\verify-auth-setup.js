#!/usr/bin/env node

/**
 * Script para verificar la configuración de autenticación
 * Ejecutar con: node scripts/verify-auth-setup.js
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔍 Verificando configuración de autenticación...\n');

let hasErrors = false;

// 1. Verificar que existe .env.local
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.error('❌ No se encontró archivo .env.local');
  console.log('   Crea uno copiando .env.example:\n');
  console.log('   cp .env.example .env.local\n');
  hasErrors = true;
} else {
  console.log('✅ Archivo .env.local encontrado');
}

// 2. Verificar AUTH_SECRET
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  if (!envContent.includes('AUTH_SECRET=') || envContent.includes('AUTH_SECRET=""') || envContent.includes('AUTH_SECRET=your-')) {
    console.error('❌ AUTH_SECRET no está configurado correctamente');
    console.log('   Genera uno con el siguiente comando:\n');
    console.log('   openssl rand -base64 32\n');
    console.log('   O usa:\n');
    console.log('   npx auth secret\n');
    console.log('   Luego añádelo a tu .env.local:\n');
    console.log('   AUTH_SECRET=tu-secret-generado\n');
    hasErrors = true;
  } else {
    console.log('✅ AUTH_SECRET está configurado');
  }
  
  // 3. Verificar DATABASE_URL
  if (!envContent.includes('DATABASE_URL=') || envContent.includes('DATABASE_URL=""')) {
    console.error('❌ DATABASE_URL no está configurado');
    console.log('   Añade tu URL de base de datos a .env.local\n');
    hasErrors = true;
  } else {
    console.log('✅ DATABASE_URL está configurado');
  }
}

// 4. Verificar que no se esté usando Edge Runtime en middleware
const middlewarePath = path.join(process.cwd(), 'middleware.ts');
if (fs.existsSync(middlewarePath)) {
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  
  if (middlewareContent.includes('experimental-edge')) {
    console.error('❌ El middleware está usando Edge Runtime');
    console.log('   Esto puede causar problemas con NextAuth CSRF');
    console.log('   Asegúrate de haber removido la línea: export const runtime = "experimental-edge"\n');
    hasErrors = true;
  } else {
    console.log('✅ Middleware no usa Edge Runtime');
  }
}

// 5. Verificar configuración de auth.ts
const authPath = path.join(process.cwd(), 'auth.ts');
if (fs.existsSync(authPath)) {
  const authContent = fs.readFileSync(authPath, 'utf8');
  
  if (!authContent.includes('trustHost: true')) {
    console.warn('⚠️  Considera añadir trustHost: true a tu configuración de NextAuth');
  } else {
    console.log('✅ trustHost está configurado');
  }
  
  if (!authContent.includes('cookies:')) {
    console.warn('⚠️  Considera añadir configuración explícita de cookies');
  } else {
    console.log('✅ Configuración de cookies encontrada');
  }
}

// 6. Generar un AUTH_SECRET de ejemplo si es necesario
if (hasErrors && !process.env.AUTH_SECRET) {
  console.log('\n📝 AUTH_SECRET de ejemplo generado:');
  const secret = crypto.randomBytes(32).toString('base64');
  console.log(`   AUTH_SECRET=${secret}\n`);
}

// Resumen
console.log('\n' + '='.repeat(50));
if (hasErrors) {
  console.log('❌ Se encontraron problemas en la configuración');
  console.log('   Por favor, corrige los errores anteriores');
  process.exit(1);
} else {
  console.log('✅ La configuración de autenticación parece correcta');
  console.log('\n💡 Si sigues teniendo problemas:');
  console.log('   1. Limpia el caché del navegador');
  console.log('   2. Reinicia el servidor de desarrollo');
  console.log('   3. Verifica los logs de la consola del navegador');
}