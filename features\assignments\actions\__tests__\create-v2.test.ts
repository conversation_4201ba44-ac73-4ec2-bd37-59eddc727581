import { db } from "@/shared/lib/db";
import { integrityManager } from "@/features/shared/integrity";

import { createAssignments } from "../create-v2";

// Mock dependencies
jest.mock("@/shared/lib/db", () => ({
    db: {
        contractor: {
            findUnique: jest.fn(),
        },
        garmentSize: {
            findUnique: jest.fn(),
            update: jest.fn(),
            updateMany: jest.fn(),
        },
        assignment: {
            create: jest.fn(),
            createMany: jest.fn(),
            findMany: jest.fn(),
            updateMany: jest.fn(),
        },
        remission: {
            findUnique: jest.fn(),
            updateMany: jest.fn(),
        },
        $transaction: jest.fn(),
    },
}));

jest.mock("@/features/shared/integrity", () => ({
    integrityManager: {
        executeWithIntegrity: jest.fn(),
    },
    compensationStrategies: {
        compensateAssignmentBatch: jest.fn(),
    },
}));

jest.mock("../revalidate", () => ({
    revalidateAssignmentPaths: jest.fn(),
}));

jest.mock("@/features/remissions/actions/helpers", () => ({
    createRemissionInTransaction: jest.fn(),
}));

describe("createAssignments with Hybrid Approach", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("Volume-based Strategy Selection", () => {
        it("should use single transaction for <= 10 items", async () => {
            // Setup mocks
            const mockContractor = {
                id: "contractor-1",
                name: "Test Contractor",
            };
            const mockGarmentSize = {
                totalQuantity: 100,
                usedQuantity: 0,
                garment: {
                    orderId: "order-1",
                    model: { code: "MODEL-1" },
                    color: { name: "Red" },
                },
                size: { code: "M" },
            };

            (db.contractor.findUnique as jest.Mock).mockResolvedValue(
                mockContractor,
            );
            (db.garmentSize.findUnique as jest.Mock).mockResolvedValue(
                mockGarmentSize,
            );

            // Mock integrityManager to capture the operation
            let capturedOperation: any;

            (
                integrityManager.executeWithIntegrity as jest.Mock
            ).mockImplementation(async (context, operation, compensate) => {
                capturedOperation = context;

                return {
                    createdAssignments: [],
                    remission: null,
                    remissionPreview: null,
                };
            });

            // Test with 5 items (below threshold)
            await createAssignments({
                contractorId: "contractor-1",
                assignments: Array(5).fill({
                    orderId: "order-1",
                    garmentSizeId: "size-1",
                    quantity: 10,
                }),
            });

            expect(capturedOperation.metadata.strategy).toBe("single");
            expect(capturedOperation.metadata.assignmentCount).toBe(5);
        });

        it("should use split transaction for > 10 items", async () => {
            // Setup mocks
            const mockContractor = {
                id: "contractor-1",
                name: "Test Contractor",
            };
            const mockGarmentSize = {
                totalQuantity: 500,
                usedQuantity: 0,
                garment: {
                    orderId: "order-1",
                    model: { code: "MODEL-1" },
                    color: { name: "Red" },
                },
                size: { code: "M" },
            };

            (db.contractor.findUnique as jest.Mock).mockResolvedValue(
                mockContractor,
            );
            (db.garmentSize.findUnique as jest.Mock).mockResolvedValue(
                mockGarmentSize,
            );

            // Mock integrityManager to capture the operation
            let capturedOperation: any;

            (
                integrityManager.executeWithIntegrity as jest.Mock
            ).mockImplementation(async (context, operation, compensate) => {
                capturedOperation = context;

                return {
                    createdAssignments: [],
                    remission: null,
                    remissionPreview: null,
                };
            });

            // Test with 15 items (above threshold)
            await createAssignments({
                contractorId: "contractor-1",
                assignments: Array(15).fill({
                    orderId: "order-1",
                    garmentSizeId: "size-1",
                    quantity: 10,
                }),
            });

            expect(capturedOperation.metadata.strategy).toBe("split");
            expect(capturedOperation.metadata.assignmentCount).toBe(15);
        });
    });

    describe("Validation", () => {
        it("should validate contractor exists", async () => {
            (db.contractor.findUnique as jest.Mock).mockResolvedValue(null);

            const result = await createAssignments({
                contractorId: "non-existent",
                assignments: [
                    {
                        orderId: "order-1",
                        garmentSizeId: "size-1",
                        quantity: 10,
                    },
                ],
            });

            expect(result.success).toBe(false);
            expect(result.error).toBe("El contratista seleccionado no existe");
        });

        it("should validate garment sizes exist", async () => {
            const mockContractor = {
                id: "contractor-1",
                name: "Test Contractor",
            };

            (db.contractor.findUnique as jest.Mock).mockResolvedValue(
                mockContractor,
            );
            (db.garmentSize.findUnique as jest.Mock).mockResolvedValue(null);

            const result = await createAssignments({
                contractorId: "contractor-1",
                assignments: [
                    {
                        orderId: "order-1",
                        garmentSizeId: "non-existent",
                        quantity: 10,
                    },
                ],
            });

            expect(result.success).toBe(false);
            expect(result.error).toBe("Algunas asignaciones no son válidas");
        });

        it("should validate sufficient quantity available", async () => {
            const mockContractor = {
                id: "contractor-1",
                name: "Test Contractor",
            };
            const mockGarmentSize = {
                totalQuantity: 10,
                usedQuantity: 8,
                garment: {
                    orderId: "order-1",
                    model: { code: "MODEL-1" },
                    color: { name: "Red" },
                },
                size: { code: "M" },
            };

            (db.contractor.findUnique as jest.Mock).mockResolvedValue(
                mockContractor,
            );
            (db.garmentSize.findUnique as jest.Mock).mockResolvedValue(
                mockGarmentSize,
            );

            const result = await createAssignments({
                contractorId: "contractor-1",
                assignments: [
                    {
                        orderId: "order-1",
                        garmentSizeId: "size-1",
                        quantity: 5, // Requesting 5 but only 2 available
                    },
                ],
            });

            expect(result.success).toBe(false);
            expect(result.error).toBe("Algunas asignaciones no son válidas");
        });
    });

    describe("Integrity Management", () => {
        it("should wrap operations with integrity manager", async () => {
            const mockContractor = {
                id: "contractor-1",
                name: "Test Contractor",
            };
            const mockGarmentSize = {
                totalQuantity: 100,
                usedQuantity: 0,
                garment: {
                    orderId: "order-1",
                    model: { code: "MODEL-1" },
                    color: { name: "Red" },
                },
                size: { code: "M" },
            };

            (db.contractor.findUnique as jest.Mock).mockResolvedValue(
                mockContractor,
            );
            (db.garmentSize.findUnique as jest.Mock).mockResolvedValue(
                mockGarmentSize,
            );
            (
                integrityManager.executeWithIntegrity as jest.Mock
            ).mockResolvedValue({
                createdAssignments: [{ id: "assignment-1" }],
                remission: {
                    id: "remission-1",
                    folio: "REM-001",
                    createdAt: new Date(),
                },
                remissionPreview: null,
            });

            const result = await createAssignments({
                contractorId: "contractor-1",
                assignments: [
                    {
                        orderId: "order-1",
                        garmentSizeId: "size-1",
                        quantity: 10,
                    },
                ],
            });

            expect(integrityManager.executeWithIntegrity).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: "ASSIGNMENT_BATCH",
                    userId: "contractor-1",
                    metadata: expect.objectContaining({
                        contractorId: "contractor-1",
                        assignmentCount: 1,
                        strategy: "single",
                    }),
                }),
                expect.any(Function),
                expect.any(Function),
            );

            expect(result.success).toBe(true);
            expect(result.data?.assignments).toHaveLength(1);
            expect(result.data?.remission).toBeDefined();
        });
    });
});
