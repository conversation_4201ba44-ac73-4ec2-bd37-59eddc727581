import { revalidateEntityCache } from "./revalidation-actions";

/**
 * Crea helpers de revalidación para una entidad específica
 * @param entityName Nombre de la entidad en singular (ej: "size", "color", "model")
 * @returns Funciones y constantes para revalidación de datos
 */
export function createServerRevalidation(entityName: string) {
    // Determinar la forma plural del nombre (simplificado)
    const pluralName = entityName + "s";

    // Crear rutas para revalidación
    const PATHS = {
        list: `/dashboard/${pluralName}`,
        detail: (id: string) => `/dashboard/${pluralName}/${id}`,
        new: `/dashboard/${pluralName}/new`,
        api: `/api/${pluralName}`,
    };

    // Crear tags para revalidación
    const TAGS = {
        all: pluralName,
        detail: (id: string) => `${entityName}-${id}`,
        client: `client-${pluralName}`,
        related: [`products`, `garments`], // Entidades relacionadas (personalizable)
    };

    /**
     * Wrapper para revalidar la caché de la entidad
     * @param id ID opcional de la entidad específica
     */
    async function revalidateCache(id?: string) {
        return revalidateEntityCache(PATHS, TAGS, entityName, id);
    }

    return {
        revalidateCache,
        PATHS,
        TAGS,
    };
}
