# Guía de Migración: handleDbError → handlePrismaError

## 🎯 Problema
Has eliminado `handleDbError` de `db.ts` siguiendo las mejores prácticas de Prisma, pero tus server actions la siguen usando.

## 💡 Solución
He creado un nuevo sistema de manejo de errores específico para Prisma en `shared/actions/prisma-errors.ts` que incluye:

1. **`handlePrismaError`** - Maneja errores de Prisma con mensajes amigables
2. **`withPrismaErrorHandling`** - Wrapper para operaciones async
3. **`withErrorHandler`** - Decorator para server actions completos

## 🔧 Migración Automática

Ejecuta el script de migración:
```bash
node scripts/migrate-server-actions.js
```

Este script:
- Busca todos los archivos con `"use server"` y `handleDbError`
- Actualiza los imports automáticamente
- Reemplaza las llamadas a `handleDbError` por `handlePrismaError`

## 📝 Ejemplos de Uso

### Antes (con handleDbError):
```typescript
"use server";
import { db, handleDbError } from "@/shared/lib/db";

export async function createOrder(data: OrderInput) {
  try {
    const order = await db.order.create({
      data: data
    });
    return { success: true, data: order };
  } catch (error) {
    return handleDbError(error);
  }
}
```

### Después - Opción 1 (Reemplazo directo):
```typescript
"use server";
import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/actions/prisma-errors";

export async function createOrder(data: OrderInput) {
  try {
    const order = await db.order.create({
      data: data
    });
    return { success: true, data: order };
  } catch (error) {
    return handlePrismaError(error);
  }
}
```

### Después - Opción 2 (Con wrapper):
```typescript
"use server";
import { db } from "@/shared/lib/db";
import { withPrismaErrorHandling } from "@/shared/actions/prisma-errors";

export async function createOrder(data: OrderInput) {
  return withPrismaErrorHandling(async () => {
    const order = await db.order.create({
      data: data
    });
    return order;
  });
}
```

### Después - Opción 3 (Con decorator):
```typescript
"use server";
import { db } from "@/shared/lib/db";
import { withErrorHandler } from "@/shared/actions/prisma-errors";

export const createOrder = withErrorHandler(async (data: OrderInput) => {
  const order = await db.order.create({
    data: data
  });
  return { success: true, data: order };
});
```

## 🔍 Errores Manejados

La función `handlePrismaError` maneja automáticamente:

- **P2002**: Violación de unicidad → "Ya existe un registro con estos datos"
- **P2025**: Registro no encontrado → "El registro solicitado no fue encontrado"
- **P2003**: Violación de clave foránea → "No se puede completar la operación debido a referencias existentes"
- **P2014**: Relación requerida → "Faltan datos requeridos para completar la operación"
- Errores de validación → "Los datos proporcionados no son válidos"
- Errores de conexión → "Error al conectar con la base de datos"

## ✅ Ventajas

1. **Mensajes amigables** para el usuario
2. **Logging automático** de errores
3. **Type-safe** con TypeScript
4. **Consistente** en toda la aplicación
5. **Fácil de extender** con nuevos códigos de error

## 🚀 Próximos Pasos

1. Ejecuta el script de migración
2. Revisa los archivos migrados
3. Para nuevos server actions, usa `withPrismaErrorHandling`
4. Considera agregar más códigos de error según necesites
