import { z } from "zod";

// Esquema base para color
const baseColorSchema = {
    name: z
        .string()
        .min(2, "El nombre debe tener al menos 2 caracteres")
        .max(50, "El nombre no puede exceder los 50 caracteres")
        .transform((val) => val.trim()),
    hexCode: z
        .string()
        .min(1, "El código hexadecimal es requerido")
        .regex(
            /^#(?:[0-9a-fA-F]{3}){1,2}$/,
            "Formato hex inválido. Debe ser #RGB o #RRGGBB",
        )
        .transform((val) => val.toUpperCase()),
};

// Esquema para crear color
export const createColorSchema = z.object(baseColorSchema);

// Esquema para actualizar color (campos opcionales)
export const updateColorSchema = z
    .object({
        name: baseColorSchema.name.optional(),
        hexCode: baseColorSchema.hexCode.optional(),
    })
    .refine((data) => Object.keys(data).length > 0, {
        message: "Debe proporcionar al menos un campo para actualizar",
    });

// Tipos
export type CreateColorInput = z.infer<typeof createColorSchema>;
export type UpdateColorInput = z.infer<typeof updateColorSchema>;
