import { PrismaClient, Prisma } from "@prisma/client";

import { IntegrityManager, CompensationFn } from "../IntegrityManager";

// Mock Prisma Client
export const createMockPrismaClient = () => {
    const mockPrisma = {
        $transaction: jest.fn(),
        operationLog: {
            create: jest.fn(),
            update: jest.fn(),
            findMany: jest.fn(),
        },
        assignment: {
            update: jest.fn(),
            updateMany: jest.fn(),
            findUnique: jest.fn(),
            findMany: jest.fn(),
            createMany: jest.fn(),
        },
        garmentSize: {
            update: jest.fn(),
        },
        order: {
            findUnique: jest.fn(),
        },
        contractor: {
            findUnique: jest.fn(),
        },
        remission: {
            create: jest.fn(),
        },
    } as unknown as PrismaClient;

    return mockPrisma;
};

// Factory Functions
export const createMockOperationLog = (
    overrides?: Partial<Prisma.OperationLogCreateInput>,
) => ({
    id: "op-log-123",
    operationType: "ASSIGNMENT_BATCH",
    status: "IN_PROGRESS",
    startedAt: new Date(),
    completedAt: null,
    failedAt: null,
    errorMessage: null,
    compensatedAt: null,
    metadata: {},
    result: null,
    userId: "user-123",
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
});

export const createMockAssignment = (overrides?: Partial<any>) => ({
    id: "assign-123",
    orderId: "order-123",
    contractorId: "contractor-123",
    garmentSizeId: "size-123",
    quantity: 10,
    unitPrice: 100,
    subtotal: 1000,
    version: 1,
    status: "ACTIVE",
    cancelledAt: null,
    cancelReason: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
});

export const createMockGarmentSize = (overrides?: Partial<any>) => ({
    id: "size-123",
    garmentId: "garment-123",
    sizeId: "size-s",
    quantity: 100,
    availableQuantity: 90,
    assignedQuantity: 10,
    version: 1,
    ...overrides,
});

export const createMockOrder = (overrides?: Partial<any>) => ({
    id: "order-123",
    orderNumber: "ORD-001",
    status: "IN_PROGRESS",
    createdAt: new Date(),
    ...overrides,
});

export const createMockContractor = (overrides?: Partial<any>) => ({
    id: "contractor-123",
    name: "Test Contractor",
    email: "<EMAIL>",
    phone: "1234567890",
    ...overrides,
});

// Test Helpers
export const setupIntegrityManager = (mockPrisma?: PrismaClient) => {
    const prisma = mockPrisma || createMockPrismaClient();
    const integrityManager = new IntegrityManager(prisma);

    return { prisma, integrityManager };
};

export const createMockCompensationFn = (): CompensationFn => {
    return jest.fn().mockResolvedValue(undefined);
};

// Utility to simulate transaction behavior
export const mockTransactionBehavior = (
    mockPrisma: any,
    behavior: "success" | "fail" | "timeout" = "success",
) => {
    switch (behavior) {
        case "success":
            mockPrisma.$transaction.mockImplementation(async (fn: any) => {
                if (typeof fn === "function") {
                    return fn(mockPrisma);
                }

                return Promise.resolve();
            });
            break;
        case "fail":
            mockPrisma.$transaction.mockRejectedValue(
                new Error("Transaction failed"),
            );
            break;
        case "timeout":
            mockPrisma.$transaction.mockRejectedValue(
                new Error("Transaction timed out after 5000ms"),
            );
            break;
    }
};

// Timing utilities for performance tests
export class PerformanceTimer {
    private startTime: number;
    private endTime?: number;

    constructor() {
        this.startTime = performance.now();
    }

    stop() {
        this.endTime = performance.now();

        return this.getDuration();
    }

    getDuration() {
        if (!this.endTime) {
            this.endTime = performance.now();
        }

        return this.endTime - this.startTime;
    }
}

// Mock data generators for volume testing
export const generateBulkAssignments = (count: number, orderId?: string) => {
    return Array.from({ length: count }, (_, i) => ({
        orderId: orderId || `order-${i}`,
        contractorId: `contractor-${i % 5}`, // Distribute among 5 contractors
        garmentSizeId: `size-${i % 10}`, // Distribute among 10 sizes
        quantity: Math.floor(Math.random() * 20) + 1,
        unitPrice: Math.floor(Math.random() * 1000) + 100,
    }));
};

// Assertion helpers
export const assertOperationLogCreated = (
    mockPrisma: any,
    expectedType: string,
) => {
    expect(mockPrisma.operationLog.create).toHaveBeenCalledWith(
        expect.objectContaining({
            data: expect.objectContaining({
                operationType: expectedType,
                status: "IN_PROGRESS",
            }),
        }),
    );
};

export const assertOperationLogCompleted = (
    mockPrisma: any,
    operationId: string,
) => {
    expect(mockPrisma.operationLog.update).toHaveBeenCalledWith(
        expect.objectContaining({
            where: { id: operationId },
            data: expect.objectContaining({
                status: "COMPLETED",
                completedAt: expect.any(Date),
            }),
        }),
    );
};

export const assertOperationLogFailed = (
    mockPrisma: any,
    operationId: string,
) => {
    expect(mockPrisma.operationLog.update).toHaveBeenCalledWith(
        expect.objectContaining({
            where: { id: operationId },
            data: expect.objectContaining({
                status: "FAILED",
                failedAt: expect.any(Date),
                errorMessage: expect.any(String),
            }),
        }),
    );
};

// Concurrency simulation helpers
export const simulateConcurrentOperations = async (
    operations: (() => Promise<any>)[],
    delay: number = 0,
) => {
    if (delay > 0) {
        // Stagger operations with delay
        const results = [];

        for (const op of operations) {
            results.push(op());
            await new Promise((resolve) => setTimeout(resolve, delay));
        }

        return Promise.all(results);
    } else {
        // Run all operations concurrently
        return Promise.all(operations.map((op) => op()));
    }
};

// Error scenario generators
export const createVersionMismatchError = () => {
    const error = new Error("Version mismatch");

    (error as any).code = "P2002";

    return error;
};

export const createTimeoutError = () => {
    return new Error("Transaction timed out after 5000ms");
};

export const createDatabaseError = () => {
    const error = new Error("Database connection lost");

    (error as any).code = "P1001";

    return error;
};
