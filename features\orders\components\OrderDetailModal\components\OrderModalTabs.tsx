"use client";

import React from "react";
import { Tabs, Tab, Badge } from "@heroui/react";
import {
    InformationCircleIcon,
    DocumentTextIcon,
    UserGroupIcon,
    ClockIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

// Importar helpers seguros
import { useThemeSafe, getThemeColors } from "../utils";

interface OrderModalTabsProps {
    activeTab: string;
    onTabChange: (key: string) => void;
    notesCount?: number;
    assignmentsCount?: number;
}

const tabConfig = [
    {
        key: "general",
        title: "Información",
        icon: InformationCircleIcon,
    },
    {
        key: "assignments",
        title: "Asignaciones",
        icon: UserGroupIcon,
        countKey: "assignmentsCount",
    },
    {
        key: "notes",
        title: "Notas",
        icon: DocumentTextIcon,
        countKey: "notesCount",
    },
    {
        key: "history",
        title: "Historial",
        icon: ClockIcon,
    },
];

export default function OrderModalTabs({
    activeTab,
    onTabChange,
    notesCount = 0,
    assignmentsCount = 0,
}: OrderModalTabsProps) {
    // Usar helpers seguros
    const theme = useThemeSafe();
    const colors = getThemeColors(theme);

    const counts = {
        notesCount,
        assignmentsCount,
    };

    return (
        <div className="px-6 pt-4">
            <Tabs
                classNames={{
                    tabList: "gap-6 w-full relative rounded-none p-0",
                    cursor:
                        theme === "light"
                            ? "w-full bg-primary-500"
                            : "w-full bg-primary-400",
                    tab: "max-w-fit px-0 h-12",
                    tabContent:
                        theme === "light"
                            ? "group-data-[selected=true]:text-primary-700"
                            : "group-data-[selected=true]:text-primary-400",
                }}
                selectedKey={activeTab}
                variant="underlined"
                onSelectionChange={(key) => onTabChange(key as string)}
            >
                {tabConfig.map((tab) => {
                    const Icon = tab.icon;
                    const count = tab.countKey
                        ? counts[tab.countKey as keyof typeof counts]
                        : 0;

                    return (
                        <Tab
                            key={tab.key}
                            title={
                                <div className="flex items-center gap-2">
                                    <Icon className="w-5 h-5" />
                                    <span className="font-medium">
                                        {tab.title}
                                    </span>
                                    {count > 0 && (
                                        <motion.div
                                            animate={{ scale: 1 }}
                                            initial={{ scale: 0 }}
                                            transition={{
                                                type: "spring",
                                                stiffness: 500,
                                            }}
                                        >
                                            <Badge
                                                className={
                                                    theme === "light"
                                                        ? "ml-1 bg-primary-100 text-primary-700"
                                                        : "ml-1"
                                                }
                                                color="primary"
                                                size="sm"
                                            >
                                                {count}
                                            </Badge>
                                        </motion.div>
                                    )}
                                </div>
                            }
                        />
                    );
                })}
            </Tabs>
        </div>
    );
}
