"use server";

import { revalidatePath } from "next/cache";
import { Prisma } from "@prisma/client";

import { auth } from "@/lib/auth-helpers";
import { db } from "@/shared/lib/db";
import { handlePrismaError } from "@/shared/utils/prisma-helpers";

// Tipos necesarios para este archivo
interface NoteFilters {
    importanceId?: string;
    statusId?: string;
    authorId?: string;
    searchText?: string;
    page?: number;
    pageSize?: number;
}

/**
 * Revalida múltiples rutas de la aplicación
 */
function revalidateData(paths: string[]) {
    paths.forEach((path) => {
        revalidatePath(path);
    });

    return { revalidated: true };
}

/**
 * Creates a new note inline within the OrderDetailModal
 */
export async function createNoteInline(
    orderId: string,
    content: string,
    importanceId: string,
    statusId: string,
) {
    // REMOVED: console.log("🔍 createNoteInline called with: ", {
    //     orderId,
    //     content: content.substring(0, 20) + "...",
    //     importanceId,
    //     statusId,
    // });

    try {
        const session = await auth();
        const userId = session?.user?.id;

        if (!userId) {
            // REMOVED: console.error("❌ createNoteInline auth error: No user ID in session");

            return { success: false, error: "No autorizado" };
        }

        // REMOVED: console.log("✅ User authenticated:", userId);

        // Verify statusId exists in database before creating
        try {
            // Check if statusId exists in the NoteStatus table
            const statusExists = await db.noteStatus.findUnique({
                where: { id: statusId },
                select: { id: true, name: true },
            });

            if (!statusExists) {
                // REMOVED: console.error(`❌ Status with ID ${statusId} not found in database`);

                return {
                    success: false,
                    error: `Estado de nota inválido. El ID ${statusId} no existe en la base de datos.`,
                };
            }

            // REMOVED: console.log(`✅ Status validated: ${statusExists.name} (${statusId})`);

            // Optionally also check if importanceId exists
            const importanceExists = await db.noteImportance.findUnique({
                where: { id: importanceId },
                select: { id: true, name: true },
            });

            if (!importanceExists) {
                // REMOVED: console.error(`❌ Importance with ID ${importanceId} not found in database`);

                return {
                    success: false,
                    error: `Importancia de nota inválida. El ID ${importanceId} no existe en la base de datos.`,
                };
            }

            // REMOVED: console.log(`✅ Importance validated: ${importanceExists.name} (${importanceId})`);
        } catch (error) {
            // REMOVED: console.error("❌ Error validating IDs:", error);

            return {
                success: false,
                error: "Error al validar los datos de la nota",
            };
        }

        // REMOVED: console.log("⚙️ Creating note in database...");
        const note = await db.note.create({
            data: {
                content,
                importanceId,
                statusId,
                orderId,
                authorId: userId,
            },
            include: {
                author: {
                    select: {
                        id: true,
                        name: true,
                        image: true,
                    },
                },
                importance: true,
                status: true,
            },
        });

        // REMOVED: console.log("✅ Note created successfully:", {
        //     id: note.id,
        //     content: note.content.substring(0, 20) + "...",
        //     importanceId: note.importanceId,
        //     statusId: note.statusId,
        // });

        // Revalidate order data
        const pathsToRevalidate = [
            `/dashboard/orders/${orderId}`,
            "/dashboard/notes",
        ];

        // REMOVED: console.log("🔄 Revalidating paths:", pathsToRevalidate);
        revalidateData(pathsToRevalidate);

        return { success: true, data: note };
    } catch (error: any) {
        // REMOVED: console.error("❌ Error creating note:", error);

        // Check if it's a foreign key constraint error (Prisma error)
        if (
            typeof error === "object" &&
            error !== null &&
            "code" in error &&
            error.code === "P2003" &&
            "meta" in error &&
            error.meta &&
            typeof error.meta === "object" &&
            error.meta !== null &&
            "field_name" in error.meta &&
            typeof error.meta.field_name === "string"
        ) {
            if (error.meta.field_name.includes("statusId")) {
                return {
                    success: false,
                    error: "El estado seleccionado no existe en la base de datos. Por favor, seleccione otro estado.",
                    errorCode: "FOREIGN_KEY_CONSTRAINT",
                };
            } else if (error.meta.field_name.includes("importanceId")) {
                return {
                    success: false,
                    error: "La importancia seleccionada no existe en la base de datos. Por favor, seleccione otra importancia.",
                    errorCode: "FOREIGN_KEY_CONSTRAINT",
                };
            }
        }

        return { success: false, error: "Error al crear la nota" };
    }
}

/**
 * Updates an existing note inline within the OrderDetailModal
 */
export async function updateNoteInline({
    noteId,
    content,
    importanceId,
    statusId,
}: {
    noteId: string;
    content: string;
    importanceId: string;
    statusId: string;
}) {
    // REMOVED: console.log("🔍 updateNoteInline called with: ", {
    //     noteId,
    //     content,
    //     importanceId,
    //     statusId,
    // });

    try {
        const session = await auth();
        const userId = session?.user?.id;

        if (!userId) {
            // REMOVED: console.error("❌ updateNoteInline auth error: No user ID in session");

            return { success: false, error: "No autorizado" };
        }

        // REMOVED: console.log("✅ User authenticated:", userId);

        // Get the note to check author permissions
        const existingNote = await db.note.findUnique({
            where: { id: noteId },
            select: { authorId: true, orderId: true },
        });

        if (!existingNote) {
            // REMOVED: console.error(`❌ Note with ID ${noteId} not found`);

            return { success: false, error: "Nota no encontrada" };
        }

        // REMOVED: console.log("✅ Existing note found:", existingNote);

        // Check if user is author or has admin role
        const userRole = session.user?.role;
        const isAuthor = existingNote.authorId === userId;
        const isAdmin = (userRole as any)?.name === "ADMIN";

        // REMOVED: console.log("🔒 Permission check:", {
        //     userId,
        //     authorId: existingNote.authorId,
        //     userRole,
        //     isAuthor,
        //     isAdmin,
        // });

        if (!isAuthor && !isAdmin) {
            // REMOVED: console.error("❌ Permission denied: User is not author or admin");

            return {
                success: false,
                error: "No tienes permiso para editar esta nota",
            };
        }

        // Verify statusId exists in database before updating
        try {
            // Check if statusId exists in the NoteStatus table
            const statusExists = await db.noteStatus.findUnique({
                where: { id: statusId },
                select: { id: true, name: true },
            });

            if (!statusExists) {
                // REMOVED: console.error(`❌ Status with ID ${statusId} not found in database`);

                return {
                    success: false,
                    error: `Estado de nota inválido. El ID ${statusId} no existe en la base de datos.`,
                };
            }

            // REMOVED: console.log(`✅ Status validated: ${statusExists.name} (${statusId})`);
        } catch (error) {
            // REMOVED: console.error("❌ Error validating status ID:", error);

            return {
                success: false,
                error: "Error al validar el estado de la nota",
            };
        }

        // REMOVED: console.log("⚙️ Updating note in database...");
        const updatedNote = await db.note.update({
            where: { id: noteId },
            data: {
                content,
                importanceId,
                statusId,
                updatedAt: new Date(),
            },
            include: {
                author: {
                    select: {
                        id: true,
                        name: true,
                        image: true,
                    },
                },
                importance: true,
                status: true,
            },
        });

        // REMOVED: console.log("✅ Note updated successfully:", {
        //     id: updatedNote.id,
        //     content: updatedNote.content.substring(0, 20) + "...",
        //     importanceId: updatedNote.importanceId,
        //     statusId: updatedNote.statusId,
        // });

        // Revalidate order data
        const pathsToRevalidate = [
            `/dashboard/orders/${existingNote.orderId}`,
            "/dashboard/notes",
        ];

        // REMOVED: console.log("🔄 Revalidating paths:", pathsToRevalidate);
        revalidateData(pathsToRevalidate);

        return { success: true, data: updatedNote };
    } catch (error: any) {
        // REMOVED: console.error("❌ Error updating note:", error);

        // Check if it's a foreign key constraint error (Prisma error)
        if (
            typeof error === "object" &&
            error !== null &&
            "code" in error &&
            error.code === "P2003" &&
            "meta" in error &&
            error.meta &&
            typeof error.meta === "object" &&
            error.meta !== null &&
            "field_name" in error.meta &&
            typeof error.meta.field_name === "string" &&
            error.meta.field_name.includes("statusId")
        ) {
            return {
                success: false,
                error: "El estado seleccionado no existe en la base de datos. Por favor, seleccione otro estado.",
                errorCode: "FOREIGN_KEY_CONSTRAINT",
            };
        }

        return { success: false, error: "Error al actualizar la nota" };
    }
}

/**
 * Gets notes by order with filters
 */
export async function getNotesByOrder(orderId: string, filters?: NoteFilters) {
    try {
        // REMOVED: console.log("📝 getNotesByOrder called with:", {
        //     orderId,
        //     filters,
        // });

        // Ensure filters is an object even if undefined
        const safeFilters = filters || {};

        // Extraer valores básicos para evitar anidamiento inadecuado
        const {
            importanceId,
            statusId,
            authorId,
            searchText,
            page = 1,
            pageSize = 10,
        } = safeFilters;

        // Construir el objeto where directamente para evitar problemas de estructura
        const where: Prisma.NoteWhereInput = { orderId };

        // Añadir filtros solo si están presentes
        if (importanceId) where.importanceId = importanceId;
        if (statusId) where.statusId = statusId;
        if (authorId) where.authorId = authorId;

        // Añadir búsqueda por texto si está presente
        if (searchText) {
            where.content = {
                contains: searchText,
                mode: "insensitive",
            };
        }

        // Log del where clause para debugging
        // REMOVED: console.log("Where clause for notes query:", JSON.stringify(where));

        // Obtener el conteo total con la cláusula where corregida
        const totalCount = await db.note.count({ where });

        // Cálculo de paginación
        const skip = (page - 1) * pageSize;

        // Obtener notas con paginación
        const notes = await db.note.findMany({
            where,
            orderBy: { createdAt: "desc" },
            skip,
            take: pageSize,
            include: {
                author: {
                    select: {
                        id: true,
                        name: true,
                        image: true,
                    },
                },
                importance: true,
                status: true,
            },
        });

        // Calcular si hay más páginas
        const hasMore = totalCount > skip + notes.length;

        // REMOVED: console.log(`✅ Found ${notes.length} notes of ${totalCount} total`);

        return {
            success: true,
            data: {
                notes,
                totalCount,
                hasMore,
                page,
                pageSize,
                totalPages: Math.ceil(totalCount / pageSize),
            },
        };
    } catch (error) {
        // REMOVED: console.error("❌ Error getting notes:", error);

        // Usar la utilidad de manejo de errores
        return handlePrismaError(error, "Error al obtener las notas");
    }
}

/**
 * Bulk update notes (status, importance)
 */
export async function bulkUpdateNotes(
    noteIds: string[],
    data: { statusId?: string; importanceId?: string },
) {
    try {
        const session = await auth();
        const userId = session?.user?.id;

        if (!userId) {
            return { success: false, error: "No autorizado" };
        }

        // Check if user has permissions for these notes
        const notes = await db.note.findMany({
            where: { id: { in: noteIds } },
            select: { id: true, authorId: true, orderId: true },
        });

        if (notes.length === 0) {
            return { success: false, error: "Notas no encontradas" };
        }

        // Get user role
        const userRole = session.user?.role;
        const isAdmin = (userRole as any)?.name === "ADMIN";

        // Filter notes user has permission to update
        const allowedNoteIds = notes
            .filter((note) => isAdmin || note.authorId === userId)
            .map((note) => note.id);

        if (allowedNoteIds.length === 0) {
            return {
                success: false,
                error: "No tienes permiso para editar estas notas",
            };
        }

        // Update notes with transaction
        await db.$transaction(
            allowedNoteIds.map((noteId) =>
                db.note.update({
                    where: { id: noteId },
                    data: {
                        ...data,
                        updatedAt: new Date(),
                    },
                }),
            ),
        );

        // Get unique order IDs for revalidation
        const orderIds = Array.from(new Set(notes.map((note) => note.orderId)));

        // Revalidate all affected orders
        revalidateData([
            ...orderIds.map((id) => `/dashboard/orders/${id}`),
            "/dashboard/notes",
        ]);

        return {
            success: true,
            data: {
                updatedCount: allowedNoteIds.length,
                totalCount: noteIds.length,
            },
        };
    } catch (error) {
        // REMOVED: console.error("Error updating notes in bulk:", error);

        return { success: false, error: "Error al actualizar las notas" };
    }
}
