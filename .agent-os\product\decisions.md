# Technical Decisions Log

Este documento captura las decisiones técnicas importantes tomadas durante el desarrollo de Lohari, incluyendo el razonamiento detrás de cada elección.

## Architecture Decisions

### 1. Next.js App Router over Pages Router
**Decision**: Use Next.js 14 App Router
**Date**: Project inception
**Reasoning**:
- Better performance with React Server Components
- Simplified data fetching with Server Actions
- More intuitive file-based routing
- Built-in loading and error states
- Future-proof as Pages Router is being phased out

### 2. Server Actions + SWR en lugar de APIs tradicionales
**Decision**: Usar Server Actions para mutaciones y SWR para fetching reactivo
**Date**: Inicio del proyecto
**Reasoning**:
- Sin necesidad de crear y mantener endpoints API
- Type safety de extremo a extremo automático
- Mejor experiencia de desarrollo
- Menos código boilerplate
- Manejo de errores integrado
- Protección CSRF automática

### 3. Feature-Based Module Structure
**Decision**: Organize code by features instead of technical layers
**Date**: Early development
**Reasoning**:
- Better code organization and discoverability
- Easier to maintain and scale
- Clear ownership boundaries
- Facilitates team collaboration
- Aligns with domain-driven design principles

### 4. Stack Tecnológico Completo
**Decision**: Next.js + Prisma + PostgreSQL + HeroUI
**Date**: Inicio del proyecto
**Reasoning**:
- Stack moderno y mantenible
- Excelente soporte de TypeScript en todas las capas
- PostgreSQL para confiabilidad y escalabilidad
- HeroUI para UI consistente y profesional
- Todo open source con comunidades activas
- Facilita contratación futura de desarrolladores

### 5. HeroUI Component Library
**Decision**: Use HeroUI v2 as the primary component library
**Date**: UI framework selection
**Reasoning**:
- Modern, accessible components
- Excellent TypeScript support
- Tailwind CSS integration
- Consistent design system
- Good performance
- Active development

## Technical Standards

### 6. Spanish for Data, English for Code
**Decision**: Use Spanish for database fields and UI, English for code
**Date**: Project inception
**Reasoning**:
- Business operates in Spanish-speaking market
- Database models reflect business domain language
- Code remains internationally maintainable
- Clear separation of concerns

### 7. 500 Line File Limit
**Decision**: Enforce maximum 500 lines per file
**Date**: Code organization phase
**Reasoning**:
- Improves code readability
- Forces proper modularization
- Easier code reviews
- Better git history
- Reduces merge conflicts

### 8. Zod for Runtime Validation
**Decision**: Use Zod for all input validation
**Date**: Form handling implementation
**Reasoning**:
- Runtime type safety
- Automatic TypeScript type inference
- Composable schemas
- Great error messages
- Works well with React Hook Form

## Performance Decisions

### 9. SWR for Client-Side Data Fetching
**Decision**: Use SWR when client-side reactivity is needed
**Date**: Client state management
**Reasoning**:
- Automatic revalidation
- Built-in caching
- Optimistic updates
- Focus revalidation
- Lightweight compared to alternatives

### 10. Lazy Loading for Heavy Components
**Decision**: Implement dynamic imports for PDF generation and charts
**Date**: Performance optimization phase
**Reasoning**:
- Reduces initial bundle size
- Improves First Contentful Paint
- Better Core Web Vitals
- Components loaded only when needed

## Security Decisions

### 11. NextAuth for Authentication
**Decision**: Use NextAuth v4 for authentication
**Date**: Auth implementation
**Reasoning**:
- Built for Next.js
- Supports multiple providers
- Session management included
- CSRF protection built-in
- JWT and database sessions support

### 12. Environment-Based Configuration
**Decision**: Use .env files with validation
**Date**: Configuration setup
**Reasoning**:
- Secrets stay out of code
- Easy environment switching
- Type-safe with zod validation
- Standard practice
- Works with all deployment platforms

## Future Considerations

### 13. Prioridad: Control del Negocio
**Status**: Principio fundamental
**Reasoning**:
- Basado en los 4 principios administrativos
- Enfoque especial en CONTROL
- Cada feature debe mejorar visibilidad y control
- Decisiones técnicas subordinadas a necesidades del negocio
- Sistema debe ser intuitivo para usuarios no técnicos

### 14. Arquitectura para Crecimiento
**Status**: Diseño inicial single-tenant, preparado para multi-tenant
**Reasoning**:
- Comenzar simple para empresa actual
- Estructura preparada para escalar
- Evitar sobre-ingeniería prematura
- Migración a multi-tenant cuando sea necesario
- Enfoque en resolver problemas actuales primero

### 15. Gestión de Subclientes
**Status**: Decisión pendiente de implementar
**Reasoning**:
- Descubierto durante desarrollo de packing
- Clientes tienen estructura jerárquica (ej: Becktel → subclientes)
- Necesario para correcta organización de pedidos
- Impacta múltiples módulos del sistema
- Prioridad inmediata después de packing