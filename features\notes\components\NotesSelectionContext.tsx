"use client";

import React, { createContext, useState, useContext, ReactNode } from "react";

// Define types
interface Note {
    id: string;
    [key: string]: any;
}

interface NotesSelectionContextType {
    selectedNotes: string[];
    isSelectionMode: boolean;
    startSelectionMode: () => void;
    endSelectionMode: () => void;
    toggleSelection: (noteId: string) => void;
    isSelected: (noteId: string) => boolean;
    selectAll: (noteIds: string[]) => void;
    clearSelection: () => void;
}

// Create context with default values
const NotesSelectionContext = createContext<NotesSelectionContextType>({
    selectedNotes: [],
    isSelectionMode: false,
    startSelectionMode: () => {},
    endSelectionMode: () => {},
    toggleSelection: () => {},
    isSelected: () => false,
    selectAll: () => {},
    clearSelection: () => {},
});

// Provider component
interface NotesSelectionProviderProps {
    children: ReactNode;
}

export function NotesSelectionProvider({
    children,
}: NotesSelectionProviderProps) {
    // State
    const [selectedNotes, setSelectedNotes] = useState<string[]>([]);
    const [isSelectionMode, setIsSelectionMode] = useState(false);

    // Start selection mode
    const startSelectionMode = () => {
        setIsSelectionMode(true);
    };

    // End selection mode and clear selections
    const endSelectionMode = () => {
        setIsSelectionMode(false);
        setSelectedNotes([]);
    };

    // Toggle selection of a note
    const toggleSelection = (noteId: string) => {
        setSelectedNotes((prev) => {
            if (prev.includes(noteId)) {
                return prev.filter((id) => id !== noteId);
            } else {
                return [...prev, noteId];
            }
        });
    };

    // Check if a note is selected
    const isSelected = (noteId: string) => {
        return selectedNotes.includes(noteId);
    };

    // Select all notes
    const selectAll = (noteIds: string[]) => {
        setSelectedNotes(noteIds);
        setIsSelectionMode(true);
    };

    // Clear all selections
    const clearSelection = () => {
        setSelectedNotes([]);
    };

    return (
        <NotesSelectionContext.Provider
            value={{
                selectedNotes,
                isSelectionMode,
                startSelectionMode,
                endSelectionMode,
                toggleSelection,
                isSelected,
                selectAll,
                clearSelection,
            }}
        >
            {children}
        </NotesSelectionContext.Provider>
    );
}

// Custom hook to use the context
export function useNotesSelection() {
    return useContext(NotesSelectionContext);
}
