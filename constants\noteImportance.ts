// Enumeración de importancias para notas
export enum NoteImportance {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
}

// DEPRECATED: Los IDs ya no se deben usar directamente
// Usar getNoteImportanceByName() de @/shared/lib/config-cache en su lugar
// export const noteImportanceIds = { ... }

// Mapeo de importancia a colores (para UI)
export const noteImportanceColors = {
    [NoteImportance.LOW]: "#3B82F6", // Azul
    [NoteImportance.MEDIUM]: "#F59E0B", // Ámbar
    [NoteImportance.HIGH]: "#EF4444", // Rojo
};

// Mapeo de importancia a descripciones
export const noteImportanceLabels = {
    [NoteImportance.LOW]: "Bajo",
    [NoteImportance.MEDIUM]: "Medio",
    [NoteImportance.HIGH]: "Alto",
};
