# Best Practices

This document outlines the development best practices derived from the Lohari project.

## Development Philosophy

### 1. Do Only What's Asked
- Implement exactly what was requested - nothing more, nothing less
- Avoid premature optimization or feature creep
- Ask for clarification rather than making assumptions

### 2. Prefer Editing Over Creating
- Always check if a similar component/function exists before creating new ones
- Extend existing patterns rather than inventing new ones
- Keep the codebase DRY (Don't Repeat Yourself)

### 3. Server-First Approach
- Default to Server Components for better performance
- Use Client Components only when interactivity is required
- Leverage Server Actions for all mutations

## Code Quality Standards

### Type Safety
```typescript
// ❌ Avoid
const processData = (data: any) => {
  return data.value; // Unsafe access
};

// ✅ Prefer
interface DataInput {
  value: string;
}
const processData = (data: DataInput) => {
  return data.value; // Type-safe
};
```

### Validation First
```typescript
// Always validate inputs with Zod
const schema = z.object({
  email: z.string().email("Correo inválido"),
  nombre: z.string().min(1, "Nombre requerido"),
});

// Validate early in the function
export async function createUser(input: unknown) {
  const validated = schema.parse(input); // Fails fast
  // ... rest of logic
}
```

### Error Handling
```typescript
// ❌ Avoid silent failures
try {
  await riskyOperation();
} catch (error) {
  // Silent fail - bad!
}

// ✅ Always handle errors explicitly
try {
  await riskyOperation();
} catch (error) {
  console.error("Operation failed:", error);
  return { 
    success: false, 
    error: "Operación falló. Por favor, intente nuevamente." 
  };
}
```

## Performance Best Practices

### Data Fetching
```typescript
// ❌ Avoid N+1 queries
const orders = await db.orden.findMany();
for (const order of orders) {
  const client = await db.cliente.findUnique({
    where: { id: order.cliente_id }
  });
}

// ✅ Use includes for related data
const orders = await db.orden.findMany({
  include: {
    cliente: true
  }
});
```

### Component Optimization
```typescript
// ❌ Avoid unnecessary client components
"use client"; // Don't use unless needed
export function StaticList({ items }) {
  return items.map(item => <div>{item}</div>);
}

// ✅ Keep components server-side when possible
export function StaticList({ items }) {
  return items.map(item => <div>{item}</div>);
}
```

### Caching Strategy
```typescript
// Use revalidation wisely
import { unstable_cache } from "next/cache";

const getCachedData = unstable_cache(
  async () => fetchExpensiveData(),
  ["cache-key"],
  { revalidate: 3600 } // 1 hour
);
```

## Security Best Practices

### Authentication Checks
```typescript
// Always verify authentication in server actions
export async function protectedAction() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return { success: false, error: "No autorizado" };
  }
  
  // Proceed with action
}
```

### Input Sanitization
```typescript
// Always validate and sanitize inputs
const schema = z.object({
  // Strip HTML and limit length
  comment: z.string()
    .max(500, "Comentario muy largo")
    .transform(val => val.replace(/<[^>]*>/g, "")),
});
```

### SQL Injection Prevention
```typescript
// ❌ Never use raw SQL with user input
const query = `SELECT * FROM users WHERE id = ${userId}`;

// ✅ Always use Prisma's built-in protection
const user = await db.user.findUnique({
  where: { id: userId }
});
```

## Database Best Practices

### Transaction Usage
```typescript
// Use transactions for related operations
const result = await db.$transaction(async (prisma) => {
  const order = await prisma.orden.create({ data: orderData });
  
  await prisma.asignacion.createMany({
    data: assignments.map(a => ({
      ...a,
      orden_id: order.id
    }))
  });
  
  return order;
});
```

### Soft Deletes
```typescript
// Prefer soft deletes for important data
model Orden {
  deleted_at DateTime?
  
  // Use in queries
  @@index([deleted_at])
}

// Query active records
const activeOrders = await db.orden.findMany({
  where: { deleted_at: null }
});
```

## UI/UX Best Practices

### Loading States
```typescript
// Always provide feedback during async operations
export function SubmitButton() {
  const { pending } = useFormStatus();
  
  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Guardando..." : "Guardar"}
    </Button>
  );
}
```

### Error Messages
```typescript
// Provide clear, actionable error messages in Spanish
return {
  success: false,
  error: "El número de orden ya existe. Use un número diferente."
};
```

### Responsive Design
```typescript
// Mobile-first approach
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Content adapts to screen size */}
</div>
```

## Testing Guidelines

### Test Naming
```typescript
describe("OrderService", () => {
  // English for technical tests
  it("should calculate total correctly", () => {});
  
  // Spanish for user-facing behavior
  it("debería mostrar error cuando falta el cliente", () => {});
});
```

### Test Coverage Goals
- Unit tests: 80% coverage minimum
- Integration tests: Critical paths covered
- E2E tests: Main user journeys

## Code Review Checklist

Before submitting code:
1. ✅ TypeScript errors resolved
2. ✅ Zod validation implemented
3. ✅ Error handling in place
4. ✅ Loading states implemented
5. ✅ Spanish UI strings correct
6. ✅ No console.logs in production code
7. ✅ Files under 500 lines
8. ✅ Imports properly organized
9. ✅ Server/Client components correctly used
10. ✅ Database queries optimized

## Documentation Standards

### README Files
- Keep technical documentation in English
- Business context can be in Spanish
- Include examples and common use cases

### Code Comments
- Explain "why" not "what"
- Use Spanish for business logic
- Use English for technical details

### API Documentation
```typescript
/**
 * Server Action: Crear nueva orden
 * 
 * @param data - Datos de la orden validados
 * @returns Success con orden creada o error
 * 
 * @example
 * const result = await createOrder({
 *   numero_orden: "ORD-001",
 *   cliente_id: "123",
 *   fecha_entrega: new Date()
 * });
 */
```