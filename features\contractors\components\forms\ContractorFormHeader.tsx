"use client";

import React from "react";
import { UserGroupIcon, PencilIcon } from "@heroicons/react/24/outline";

interface ContractorFormHeaderProps {
    mode: "create" | "edit";
    contractorName?: string;
}

export function ContractorFormHeader({
    mode,
    contractorName,
}: ContractorFormHeaderProps) {
    const isEdit = mode === "edit";
    const icon = isEdit ? (
        <PencilIcon className="w-5 h-5" />
    ) : (
        <UserGroupIcon className="w-5 h-5" />
    );

    return (
        <div className="flex items-center gap-4 mb-6">
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <div className="text-purple-600 dark:text-purple-400">
                    {icon}
                </div>
            </div>

            <div className="flex-1">
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                    {isEdit ? "Editar Contratista" : "Nuevo Contratista"}
                </h1>
                {isEdit && contractorName && (
                    <p className="text-gray-600 dark:text-gray-400 mt-0.5">
                        Editando:{" "}
                        <span className="font-medium">{contractorName}</span>
                    </p>
                )}
                {!isEdit && (
                    <p className="text-gray-600 dark:text-gray-400 mt-0.5">
                        Registra un nuevo contratista en el sistema
                    </p>
                )}
            </div>
        </div>
    );
}
