# Changelog

All notable changes to the LOHARI project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New OrderStatus "Nuevo" (SparklesIcon) as first status in workflow
- Complete icon mapping for all OrderStatus values in UI components
- CogIcon, CheckBadgeIcon, TruckIcon, SparklesIcon to icon maps
- ConfigCache singleton system for configuration management
- Spanish names for all configuration tables (OrderStatus, NoteStatus, etc.)
- Migration adapters for gradual transition from hardcoded IDs
- Validation script for configuration data (validate-config-data.ts)
- CONFIG_NAMES constants with type-safe Spanish names
- Helper functions: getOrderStatusByName, getNoteImportanceByName, etc.
- Initial data seed system for critical tables (Role, OrderStatus, NoteStatus, etc.)
- Comprehensive seed.ts with all required initial data
- SQL script for direct database initialization (seed-initial-data.sql)
- Batch scripts for easy seed execution (run-seeds.bat/sh)
- Database management commands in package.json (db:seed, db:reset, db:push)
- Complete documentation for initial data setup (INITIAL_DATA_SETUP.md)
- Feature flags infrastructure for gradual rollouts
- Deprecation warning system for smooth transitions
- Analytics tracking for deprecated field usage
- Error boundaries for OrderDetailModal components
- "Coming Soon" card for future customer fields
- Comprehensive migration documentation
- `deliveryDate` field to Order model
- OrderParts display in OrderInfoSection
- Complete Prisma migration workflow documentation

### Changed
- All configuration names changed to Spanish (IN_PRODUCTION → "En producción", etc.)
- Order actions now use name-based lookups instead of hardcoded IDs
- Note importance lookups migrated from static IDs to dynamic cache
- OrderDetailModal now shows only fields that exist in database
- Customer information limited to name (phone, address, company hidden)
- Property names aligned with Prisma schema (OrderGarment → garments)
- Order summary shows accurate totals from actual data
- OrderModalHeader now displays `cutOrder` as "Orden de trabajo: xxxxx"
- OrderInfoSection shows 3 dates: received, estimated delivery, actual delivery
- Complete reset of Prisma migrations for clean state

### Removed
- Hardcoded noteImportanceIds constants (replaced with dynamic lookups)
- English names in configuration tables

### Fixed
- Modal no longer shows placeholder text for non-existent fields
- Corrected property names to match Prisma schema exactly
- Progress calculation now based on real assignment data
- Resolved migration conflicts through complete reset
- Synchronized database schema with application code

### Deprecated
- `OrderCustomer.phone` - Will be added in future DB migration
- `OrderCustomer.address` - Will be added in future DB migration  
- `OrderCustomer.company` - Will be added in future DB migration
- `Order.OrderGarment` - Use `Order.garments` instead
- `Order.OrderAssignment` - Use `Order.assignments` instead

## [1.0.0-schema-aligned] - 2025-05-28

### Summary
Major update aligning OrderDetailModal with actual Prisma database schema. This release ensures the UI accurately reflects the data model while preparing for future enhancements.

### Migration Notes
See [Order Modal Schema Alignment Guide](docs/migrations/order-modal-schema-alignment.md) for detailed migration instructions.

---

## Previous Releases

_This is the first tracked release using semantic versioning._
