"use client";

import { useState, useMemo } from "react";
import Link from "next/link";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON>ody,
    Card<PERSON>oot<PERSON>,
    <PERSON>ton,
    Chip,
    Dropdown,
    DropdownTrigger,
    DropdownMenu,
    DropdownItem,
    Tooltip,
    Pagination,
    Input,
    Avatar,
    Modal,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    EllipsisVerticalIcon,
    PencilSquareIcon,
    EyeIcon,
    ChevronDownIcon,
    TrashIcon,
    Squares2X2Icon,
    TableCellsIcon,
    ListBulletIcon,
} from "@heroicons/react/24/outline";

// Tipos para las props
interface NoteItem {
    id: string;
    content: string;
    formattedDate: string;
    authorInitial: string;
    authorName: string;
    statusInfo: {
        name: string;
        color: string;
    };
    importanceInfo: {
        name: string;
        color: string;
    };
    orderCode: string;
    author?: {
        image?: string | null;
        name?: string | null;
    };
}

interface StatusItem {
    id: string;
    name: string;
    color: string;
}

interface ImportanceItem {
    id: string;
    name: string;
    color: string;
}

// Interfaces para items de los menús desplegables
interface BaseMenuItem {
    key: string;
    label: string;
    action: () => void;
}

interface ColoredMenuItem extends BaseMenuItem {
    color: string;
}

interface NotesUIProps {
    notes: NoteItem[];
    statuses: StatusItem[];
    importances: ImportanceItem[];
}

export default function NotesUI({
    notes,
    statuses,
    importances,
}: NotesUIProps) {
    // Estado para búsqueda
    const [searchQuery, setSearchQuery] = useState("");

    // Estado para filtros
    const [showFilters, setShowFilters] = useState(false);
    const [activeFilters, setActiveFilters] = useState<{
        status: string | null;
        importance: string | null;
    }>({
        status: null,
        importance: null,
    });

    // Estados para las nuevas características
    const [expandedNotes, setExpandedNotes] = useState<Set<string>>(new Set());
    const [selectedNote, setSelectedNote] = useState<NoteItem | null>(null);
    const [activeFilter, setActiveFilter] = useState<string>("all");
    const [viewMode, setViewMode] = useState<"card" | "compact" | "list">(
        "card",
    );

    // Estado para paginación
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 12;

    // Filtrar notas basado en búsqueda, filtros activos y filtros rápidos
    const filteredNotes = useMemo(() => {
        return notes.filter((note) => {
            // Filtro de búsqueda (contenido, autor, código de orden)
            const matchesSearch =
                searchQuery === "" ||
                note.content
                    .toLowerCase()
                    .includes(searchQuery.toLowerCase()) ||
                note.authorName
                    .toLowerCase()
                    .includes(searchQuery.toLowerCase()) ||
                note.orderCode
                    .toLowerCase()
                    .includes(searchQuery.toLowerCase());

            // Filtro de estado
            const matchesStatus =
                activeFilters.status === null ||
                note.statusInfo.name ===
                    statuses.find((s) => s.id === activeFilters.status)?.name;

            // Filtro de importancia
            const matchesImportance =
                activeFilters.importance === null ||
                note.importanceInfo.name ===
                    importances.find((i) => i.id === activeFilters.importance)
                        ?.name;

            // Filtros rápidos
            let matchesQuickFilter = true;

            if (activeFilter === "important") {
                // Filtrar solo notas importantes (asumiendo que hay un nivel de importancia "ALTA" o "CRÍTICA")
                matchesQuickFilter = ["ALTA", "CRÍTICA"].includes(
                    note.importanceInfo.name.toUpperCase(),
                );
            } else if (activeFilter === "recent") {
                // Filtrar notas de los últimos 7 días
                const noteDate = new Date(
                    note.formattedDate
                        .split(" ")[0]
                        .split("/")
                        .reverse()
                        .join("-"),
                );
                const sevenDaysAgo = new Date();

                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                matchesQuickFilter = noteDate >= sevenDaysAgo;
            } else if (activeFilter === "my") {
                // Filtrar solo mis notas (implementación básica, ajustar según tu lógica de usuario)
                // Esto dependerá de cómo identifiques "mis notas" en tu aplicación
                matchesQuickFilter = true; // Placeholder, ajustar según necesidad
            }

            return (
                matchesSearch &&
                matchesStatus &&
                matchesImportance &&
                matchesQuickFilter
            );
        });
    }, [
        notes,
        searchQuery,
        activeFilters,
        statuses,
        importances,
        activeFilter,
    ]);

    // Cálculo de páginas
    const totalPages = Math.ceil(filteredNotes.length / itemsPerPage);

    // Notas paginadas
    const paginatedNotes = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;

        return filteredNotes.slice(startIndex, endIndex);
    }, [filteredNotes, currentPage, itemsPerPage]);

    // Manejar cambio de página
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    // Resetear filtros
    const resetFilters = () => {
        setActiveFilters({
            status: null,
            importance: null,
        });
        setShowFilters(false);
    };

    // Crear arrays de items para los menús desplegables
    const statusItems: (BaseMenuItem | ColoredMenuItem)[] = [
        {
            key: "all",
            label: "Todos los estados",
            action: () => setActiveFilters({ ...activeFilters, status: null }),
        },
        ...statuses.map((status) => ({
            key: status.id,
            label: status.name,
            color: status.color,
            action: () =>
                setActiveFilters({ ...activeFilters, status: status.id }),
        })),
    ];

    const importanceItems: (BaseMenuItem | ColoredMenuItem)[] = [
        {
            key: "all",
            label: "Todas las importancias",
            action: () =>
                setActiveFilters({ ...activeFilters, importance: null }),
        },
        ...importances.map((importance) => ({
            key: importance.id,
            label: importance.name,
            color: importance.color,
            action: () =>
                setActiveFilters({
                    ...activeFilters,
                    importance: importance.id,
                }),
        })),
    ];

    // Función para expandir/contraer el contenido de una nota
    const toggleExpandNote = (noteId: string) => {
        setExpandedNotes((prev) => {
            const updated = new Set(prev);

            prev.has(noteId) ? updated.delete(noteId) : updated.add(noteId);

            return updated;
        });
    };

    return (
        <div className="container mx-auto px-4 py-6 max-w-7xl">
            {/* Cabecera con título y botón de nueva nota */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-1">
                        Notas
                    </h1>
                    <p className="text-gray-500 dark:text-gray-400">
                        Gestiona las notas del sistema
                    </p>
                </div>

                <div className="flex items-center gap-3 mt-4 sm:mt-0">
                    {/* Selector de vista */}
                    <div className="bg-gray-100 dark:bg-gray-800/40 rounded-lg p-1 flex">
                        <Button
                            isIconOnly
                            aria-label="Vista tarjetas"
                            size="sm"
                            variant={viewMode === "card" ? "solid" : "light"}
                            onPress={() => setViewMode("card")}
                        >
                            <Squares2X2Icon className="w-4 h-4" />
                        </Button>
                        <Button
                            isIconOnly
                            aria-label="Vista compacta"
                            size="sm"
                            variant={viewMode === "compact" ? "solid" : "light"}
                            onPress={() => setViewMode("compact")}
                        >
                            <TableCellsIcon className="w-4 h-4" />
                        </Button>
                        <Button
                            isIconOnly
                            aria-label="Vista lista"
                            size="sm"
                            variant={viewMode === "list" ? "solid" : "light"}
                            onPress={() => setViewMode("list")}
                        >
                            <ListBulletIcon className="w-4 h-4" />
                        </Button>
                    </div>

                    <Link
                        className="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md flex items-center font-medium transition-colors"
                        href="/dashboard/notes/new"
                    >
                        <PencilSquareIcon className="w-5 h-5 mr-2" />
                        Nueva nota
                    </Link>
                </div>
            </div>

            {/* Barra de búsqueda y filtros */}
            <div className="mb-6">
                <div className="flex flex-col md:flex-row gap-3">
                    <div className="flex-1 relative">
                        <Input
                            isClearable
                            classNames={{
                                inputWrapper: "shadow-sm",
                            }}
                            placeholder="Buscar por contenido, autor o código de orden..."
                            startContent={
                                <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                            }
                            value={searchQuery}
                            onValueChange={setSearchQuery}
                        />
                    </div>

                    <div className="flex gap-2">
                        <Button
                            color={showFilters ? "primary" : "default"}
                            startContent={<FunnelIcon className="w-5 h-5" />}
                            variant={showFilters ? "solid" : "bordered"}
                            onClick={() => setShowFilters(!showFilters)}
                        >
                            Filtros
                            {(activeFilters.status !== null ||
                                activeFilters.importance !== null) && (
                                <Chip
                                    className="ml-2"
                                    color="primary"
                                    size="sm"
                                    variant="flat"
                                >
                                    {(activeFilters.status !== null ? 1 : 0) +
                                        (activeFilters.importance !== null
                                            ? 1
                                            : 0)}
                                </Chip>
                            )}
                        </Button>

                        {(activeFilters.status !== null ||
                            activeFilters.importance !== null) && (
                            <Button
                                color="danger"
                                variant="ghost"
                                onClick={resetFilters}
                            >
                                Limpiar
                            </Button>
                        )}
                    </div>
                </div>

                {/* Filtros rápidos */}
                <div className="flex flex-wrap gap-2 mt-4 mb-2">
                    <Button
                        color="primary"
                        size="sm"
                        variant={activeFilter === "all" ? "solid" : "bordered"}
                        onClick={() => setActiveFilter("all")}
                    >
                        Todas
                    </Button>
                    <Button
                        color="danger"
                        size="sm"
                        variant={
                            activeFilter === "important" ? "solid" : "bordered"
                        }
                        onClick={() => setActiveFilter("important")}
                    >
                        Importantes
                    </Button>
                    <Button
                        color="secondary"
                        size="sm"
                        variant={
                            activeFilter === "recent" ? "solid" : "bordered"
                        }
                        onClick={() => setActiveFilter("recent")}
                    >
                        Recientes
                    </Button>
                    <Button
                        color="success"
                        size="sm"
                        variant={activeFilter === "my" ? "solid" : "bordered"}
                        onClick={() => setActiveFilter("my")}
                    >
                        Mis notas
                    </Button>
                </div>

                {/* Panel de filtros desplegable */}
                {showFilters && (
                    <div className="mt-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Estado
                                </label>
                                <Dropdown>
                                    <DropdownTrigger>
                                        <Button
                                            className="w-full justify-between"
                                            endContent={
                                                <ChevronDownIcon className="w-5 h-5" />
                                            }
                                            variant="bordered"
                                        >
                                            {activeFilters.status
                                                ? statuses.find(
                                                      (s) =>
                                                          s.id ===
                                                          activeFilters.status,
                                                  )?.name
                                                : "Seleccionar estado"}
                                        </Button>
                                    </DropdownTrigger>
                                    <DropdownMenu aria-label="Estados">
                                        {statusItems.map((item) => (
                                            <DropdownItem
                                                key={item.key}
                                                onPress={item.action}
                                            >
                                                {item.key !== "all" ? (
                                                    <div className="flex items-center gap-2">
                                                        <span
                                                            className="w-3 h-3 rounded-full"
                                                            style={{
                                                                backgroundColor:
                                                                    (
                                                                        item as ColoredMenuItem
                                                                    ).color,
                                                            }}
                                                        />
                                                        {item.label}
                                                    </div>
                                                ) : (
                                                    item.label
                                                )}
                                            </DropdownItem>
                                        ))}
                                    </DropdownMenu>
                                </Dropdown>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Importancia
                                </label>
                                <Dropdown>
                                    <DropdownTrigger>
                                        <Button
                                            className="w-full justify-between"
                                            endContent={
                                                <ChevronDownIcon className="w-5 h-5" />
                                            }
                                            variant="bordered"
                                        >
                                            {activeFilters.importance
                                                ? importances.find(
                                                      (i) =>
                                                          i.id ===
                                                          activeFilters.importance,
                                                  )?.name
                                                : "Seleccionar importancia"}
                                        </Button>
                                    </DropdownTrigger>
                                    <DropdownMenu aria-label="Importancias">
                                        {importanceItems.map((item) => (
                                            <DropdownItem
                                                key={item.key}
                                                onPress={item.action}
                                            >
                                                {item.key !== "all" ? (
                                                    <div className="flex items-center gap-2">
                                                        <span
                                                            className="w-3 h-3 rounded-full"
                                                            style={{
                                                                backgroundColor:
                                                                    (
                                                                        item as ColoredMenuItem
                                                                    ).color,
                                                            }}
                                                        />
                                                        {item.label}
                                                    </div>
                                                ) : (
                                                    item.label
                                                )}
                                            </DropdownItem>
                                        ))}
                                    </DropdownMenu>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Mensaje cuando no hay resultados */}
            {filteredNotes.length === 0 && (
                <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                    <div className="flex justify-center mb-4">
                        <MagnifyingGlassIcon className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                        No se encontraron resultados
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                        No hay notas que coincidan con los criterios de búsqueda
                        o filtros aplicados.
                    </p>
                    <Button
                        className="mt-4"
                        variant="bordered"
                        onClick={resetFilters}
                    >
                        Limpiar filtros
                    </Button>
                </div>
            )}

            {/* Grid de notas */}
            {paginatedNotes.length > 0 && (
                <div
                    className={`grid gap-6 mb-8 ${
                        viewMode === "card"
                            ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                            : viewMode === "compact"
                              ? "grid-cols-1 md:grid-cols-3 lg:grid-cols-4"
                              : "grid-cols-1"
                    }`}
                >
                    {paginatedNotes.map((note) => (
                        <Card
                            key={note.id}
                            className={`shadow-sm border-l-4 transition-all duration-200 hover:shadow-md ${
                                viewMode === "compact" ? "p-2" : ""
                            } ${
                                viewMode === "list"
                                    ? "flex flex-row items-center"
                                    : ""
                            }`}
                            style={{
                                borderLeftColor: note.importanceInfo.color,
                                backgroundColor: `${note.importanceInfo.color}05`, // 5% de opacidad
                            }}
                        >
                            <CardHeader
                                className={`flex items-start justify-between gap-2 ${viewMode === "compact" ? "p-2" : ""}`}
                            >
                                <div className="flex items-start space-x-4">
                                    <Avatar
                                        classNames={{
                                            base: `${viewMode === "compact" ? "w-8 h-8 text-base" : "w-10 h-10 text-lg"}`,
                                        }}
                                        color="primary"
                                        name={note.authorInitial}
                                        radius="lg"
                                    />
                                    <div>
                                        <p
                                            className={`text-sm text-gray-600 dark:text-gray-400 ${viewMode === "compact" ? "text-xs" : ""}`}
                                        >
                                            {note.authorName}
                                        </p>
                                        <p
                                            className={`text-xs text-gray-500 ${viewMode === "compact" ? "text-xxs" : ""}`}
                                        >
                                            {note.formattedDate}
                                        </p>
                                    </div>
                                </div>

                                <div className="flex flex-wrap gap-2">
                                    <Tooltip
                                        content={`Estado: ${note.statusInfo.name}`}
                                    >
                                        <Chip
                                            classNames={{
                                                base: "border-2",
                                                content: `px-2 ${viewMode === "compact" ? "text-xs" : ""}`,
                                            }}
                                            style={{
                                                borderColor:
                                                    note.statusInfo.color,
                                                color: note.statusInfo.color,
                                            }}
                                            variant="bordered"
                                        >
                                            {note.statusInfo.name}
                                        </Chip>
                                    </Tooltip>

                                    <Tooltip
                                        content={`Importancia: ${note.importanceInfo.name}`}
                                    >
                                        <Chip
                                            classNames={{
                                                content: `px-2 ${viewMode === "compact" ? "text-xs" : ""}`,
                                            }}
                                            style={{
                                                backgroundColor:
                                                    note.importanceInfo.color,
                                                color: "#fff",
                                            }}
                                            variant="solid"
                                        >
                                            {note.importanceInfo.name}
                                        </Chip>
                                    </Tooltip>
                                </div>
                            </CardHeader>

                            <CardBody
                                className={viewMode === "compact" ? "p-2" : ""}
                            >
                                <Tooltip
                                    content={`Orden de trabajo: ${note.orderCode}`}
                                >
                                    <Chip
                                        className="mb-2"
                                        color="primary"
                                        size="sm"
                                        variant="flat"
                                    >
                                        Orden: {note.orderCode}
                                    </Chip>
                                </Tooltip>

                                <div
                                    className="cursor-pointer"
                                    onClick={() => setSelectedNote(note)}
                                >
                                    <p
                                        className={`text-gray-700 dark:text-gray-300 ${expandedNotes.has(note.id) ? "" : "line-clamp-4"}`}
                                    >
                                        {note.content}
                                    </p>
                                </div>

                                {note.content.length > 280 && (
                                    <Button
                                        aria-expanded={expandedNotes.has(
                                            note.id,
                                        )}
                                        className="mt-2"
                                        size="sm"
                                        variant="light"
                                        onPress={() =>
                                            toggleExpandNote(note.id)
                                        }
                                    >
                                        {expandedNotes.has(note.id)
                                            ? "Ver menos"
                                            : "Ver más"}
                                    </Button>
                                )}
                            </CardBody>

                            <CardFooter
                                className={`flex justify-between gap-2 ${viewMode === "compact" ? "p-2" : ""}`}
                            >
                                <Link href={`/dashboard/notes/${note.id}/edit`}>
                                    <Button
                                        isIconOnly
                                        aria-label="Editar nota"
                                        color="primary"
                                        size={
                                            viewMode === "compact" ? "sm" : "md"
                                        }
                                        variant="light"
                                    >
                                        <PencilSquareIcon
                                            className={`${viewMode === "compact" ? "w-4 h-4" : "w-5 h-5"}`}
                                        />
                                    </Button>
                                </Link>

                                <Dropdown>
                                    <DropdownTrigger>
                                        <Button
                                            isIconOnly
                                            aria-label="Más acciones"
                                            size={
                                                viewMode === "compact"
                                                    ? "sm"
                                                    : "md"
                                            }
                                            variant="light"
                                        >
                                            <EllipsisVerticalIcon
                                                className={`${viewMode === "compact" ? "w-4 h-4" : "w-5 h-5"}`}
                                            />
                                        </Button>
                                    </DropdownTrigger>
                                    <DropdownMenu aria-label="Acciones para nota">
                                        <DropdownItem
                                            key="view"
                                            startContent={
                                                <EyeIcon className="w-4 h-4" />
                                            }
                                            onPress={() =>
                                                setSelectedNote(note)
                                            }
                                        >
                                            Ver detalles
                                        </DropdownItem>
                                        <DropdownItem
                                            key="delete"
                                            className="text-danger"
                                            color="danger"
                                            startContent={
                                                <TrashIcon className="w-4 h-4" />
                                            }
                                        >
                                            Eliminar
                                        </DropdownItem>
                                    </DropdownMenu>
                                </Dropdown>
                            </CardFooter>
                        </Card>
                    ))}
                </div>
            )}

            {/* Paginación */}
            {filteredNotes.length > 0 && totalPages > 1 && (
                <div className="flex justify-center mt-8">
                    <Pagination
                        showControls
                        showShadow
                        color="primary"
                        initialPage={1}
                        page={currentPage}
                        total={totalPages}
                        onChange={handlePageChange}
                    />
                </div>
            )}

            {/* Modal de vista detallada */}
            <Modal
                isOpen={!!selectedNote}
                size="3xl"
                onClose={() => setSelectedNote(null)}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="flex flex-col gap-1">
                                <div className="flex items-center gap-3">
                                    <Avatar
                                        className="w-10 h-10"
                                        color="primary"
                                        name={selectedNote?.authorInitial}
                                        radius="lg"
                                    />
                                    <div>
                                        <p className="text-lg font-medium">
                                            {selectedNote?.authorName}
                                        </p>
                                        <p className="text-sm text-gray-500">
                                            {selectedNote?.formattedDate}
                                        </p>
                                    </div>
                                </div>
                            </ModalHeader>

                            <ModalBody>
                                <div className="flex flex-wrap gap-2 mb-4">
                                    <Chip
                                        classNames={{
                                            base: "border-2",
                                        }}
                                        style={{
                                            borderColor:
                                                selectedNote?.statusInfo.color,
                                            color: selectedNote?.statusInfo
                                                .color,
                                        }}
                                        variant="bordered"
                                    >
                                        {selectedNote?.statusInfo.name}
                                    </Chip>

                                    <Chip
                                        style={{
                                            backgroundColor:
                                                selectedNote?.importanceInfo
                                                    .color,
                                        }}
                                        variant="solid"
                                    >
                                        {selectedNote?.importanceInfo.name}
                                    </Chip>

                                    <Chip color="primary" variant="flat">
                                        Orden de trabajo:{" "}
                                        {selectedNote?.orderCode}
                                    </Chip>
                                </div>

                                <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-4">
                                    <p className="whitespace-pre-line text-gray-700 dark:text-gray-300">
                                        {selectedNote?.content}
                                    </p>
                                </div>
                            </ModalBody>

                            <ModalFooter>
                                <Button variant="bordered" onPress={onClose}>
                                    Cerrar
                                </Button>

                                <Link
                                    href={`/dashboard/notes/${selectedNote?.id}/edit`}
                                >
                                    <Button color="primary">
                                        <PencilSquareIcon className="w-4 h-4 mr-1" />
                                        Editar nota
                                    </Button>
                                </Link>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </div>
    );
}
