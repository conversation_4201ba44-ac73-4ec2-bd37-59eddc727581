"use client";

import React from "react";
import {
    Document,
    Page,
    Text,
    View,
    StyleSheet,
    Image,
    Font,
} from "@react-pdf/renderer";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import { PackingWithFullRelations } from "@/lib/types/packing";

// Register fonts
Font.register({
    family: "Helvetica",
    fonts: [
        { src: "/fonts/helvetica.ttf" },
        { src: "/fonts/helvetica-bold.ttf", fontWeight: "bold" },
    ],
});

const styles = StyleSheet.create({
    page: {
        padding: 30,
        fontSize: 10,
        fontFamily: "Helvetica",
    },
    header: {
        marginBottom: 20,
    },
    title: {
        fontSize: 16,
        fontWeight: "bold",
        textAlign: "center",
        marginBottom: 10,
        letterSpacing: 2,
    },
    headerInfo: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 20,
    },
    logo: {
        width: 80,
        height: 40,
    },
    folioBox: {
        border: "1pt solid black",
        padding: 5,
        minWidth: 100,
    },
    senderReceiver: {
        flexDirection: "row",
        marginBottom: 20,
        gap: 20,
    },
    infoBox: {
        flex: 1,
        padding: 10,
        border: "1pt solid #ccc",
    },
    infoTitle: {
        fontSize: 11,
        fontWeight: "bold",
        marginBottom: 5,
    },
    mainTable: {
        marginBottom: 20,
    },
    tableHeader: {
        flexDirection: "row",
        backgroundColor: "#f0f0f0",
        borderBottom: "1pt solid black",
        padding: 5,
        fontWeight: "bold",
        fontSize: 9,
    },
    tableRow: {
        flexDirection: "row",
        borderBottom: "0.5pt solid #ccc",
        padding: 3,
        fontSize: 9,
    },
    tableCell: {
        flex: 1,
        textAlign: "center",
        padding: 2,
    },
    tableCellLeft: {
        flex: 2,
        textAlign: "left",
        padding: 2,
    },
    summarySection: {
        marginTop: 20,
        marginBottom: 20,
    },
    summaryTitle: {
        fontSize: 11,
        fontWeight: "bold",
        marginBottom: 10,
        backgroundColor: "#f0f0f0",
        padding: 5,
    },
    summaryTable: {
        border: "1pt solid black",
    },
    summaryHeader: {
        flexDirection: "row",
        backgroundColor: "#e0e0e0",
        borderBottom: "1pt solid black",
        padding: 5,
        fontSize: 9,
        fontWeight: "bold",
    },
    summaryRow: {
        flexDirection: "row",
        borderBottom: "0.5pt solid #ccc",
        padding: 3,
        fontSize: 9,
    },
    summaryTotal: {
        flexDirection: "row",
        backgroundColor: "#f0f0f0",
        padding: 5,
        fontSize: 10,
        fontWeight: "bold",
    },
    signatureSection: {
        position: "absolute",
        bottom: 30,
        left: 30,
        right: 30,
    },
    signatures: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginTop: 50,
    },
    signatureBox: {
        width: 150,
        textAlign: "center",
    },
    signatureLine: {
        borderBottom: "1pt solid black",
        marginBottom: 5,
        height: 40,
    },
    signatureLabel: {
        fontSize: 9,
    },
});

interface Props {
    packing: PackingWithFullRelations;
}

export function ProfessionalPackingDocument({ packing }: Props) {
    const companyInfo = (packing.companyInfo as any) || {};
    const summaryBySize = (packing.packingSummaryBySize as any) || {};

    // Group details by order
    const detailsByOrder = packing.details.reduce(
        (acc, detail) => {
            const orderId = packing.orderId || "sin-orden";

            if (!acc[orderId]) {
                acc[orderId] = {
                    order: packing.order,
                    details: [],
                    summary: packing.summaries?.find(
                        (s) => s.orderId === orderId,
                    ),
                };
            }
            acc[orderId].details.push(detail);

            return acc;
        },
        {} as Record<string, any>,
    );

    const getSizeColumns = () => {
        const sizes = new Set<string>();

        packing.details.forEach((d) => sizes.add(d.garmentSize.size.code));

        return Array.from(sizes).sort((a, b) => {
            const order = ["XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL"];

            return order.indexOf(a) - order.indexOf(b);
        });
    };

    const sizeColumns = getSizeColumns();

    return (
        <Document>
            <Page size="LETTER" style={styles.page}>
                {/* Header */}
                <View style={styles.header}>
                    <Text style={styles.title}>LISTA DE EMPAQUE</Text>

                    <View style={styles.headerInfo}>
                        <View>
                            {companyInfo.logo && (
                                <Image
                                    src={companyInfo.logo}
                                    style={styles.logo}
                                />
                            )}
                        </View>

                        <View style={styles.folioBox}>
                            <Text style={{ fontWeight: "bold" }}>
                                FOLIO: {packing.folio}
                            </Text>
                        </View>
                    </View>

                    <View style={{ alignItems: "flex-end", marginBottom: 10 }}>
                        <Text>
                            Fecha:{" "}
                            {format(
                                new Date(packing.deliveryDate),
                                "EEEE, d 'de' MMMM 'de' yyyy",
                                { locale: es },
                            )}
                        </Text>
                    </View>
                </View>

                {/* Sender and Receiver */}
                <View style={styles.senderReceiver}>
                    <View style={styles.infoBox}>
                        <Text style={styles.infoTitle}>DE:</Text>
                        <Text>{companyInfo.name || "PEDRO LOBATO"}</Text>
                        {companyInfo.address && (
                            <Text>{companyInfo.address}</Text>
                        )}
                        {companyInfo.rfc && <Text>RFC: {companyInfo.rfc}</Text>}
                    </View>

                    <View style={styles.infoBox}>
                        <Text style={styles.infoTitle}>PARA:</Text>
                        <Text>{packing.customer.name}</Text>
                        {packing.subCustomer && (
                            <Text style={{ fontSize: 9, marginTop: 2 }}>
                                Cliente: {packing.subCustomer.name}
                            </Text>
                        )}
                    </View>
                </View>

                {/* Main Table */}
                <View style={styles.mainTable}>
                    <View style={styles.tableHeader}>
                        <Text style={[styles.tableCellLeft, { flex: 1.5 }]}>
                            MODELO
                        </Text>
                        <Text style={styles.tableCell}>ORDEN</Text>
                        <Text style={styles.tableCell}>PARTIDA</Text>
                        <Text style={[styles.tableCellLeft, { flex: 1.5 }]}>
                            COLOR
                        </Text>
                        {sizeColumns.map((size) => (
                            <Text key={size} style={styles.tableCell}>
                                {size}
                            </Text>
                        ))}
                        <Text style={styles.tableCell}>TOTAL</Text>
                        <Text style={styles.tableCellLeft}>TIPO</Text>
                    </View>

                    {Object.entries(detailsByOrder).map(
                        ([orderId, orderData]) => {
                            const qualityGroups = orderData.details.reduce(
                                (acc: any, detail: any) => {
                                    if (!acc[detail.qualityType]) {
                                        acc[detail.qualityType] = {};
                                    }
                                    acc[detail.qualityType][
                                        detail.garmentSize.size.code
                                    ] = detail.quantity;

                                    return acc;
                                },
                                {},
                            );

                            return Object.entries(qualityGroups).map(
                                ([quality, sizes]: [string, any]) => (
                                    <View
                                        key={`${orderId}-${quality}`}
                                        style={styles.tableRow}
                                    >
                                        <Text
                                            style={[
                                                styles.tableCellLeft,
                                                { flex: 1.5 },
                                            ]}
                                        >
                                            {orderData.details[0]?.modelCode}
                                        </Text>
                                        <Text style={styles.tableCell}>
                                            {orderData.order?.transferNumber ||
                                                "-"}
                                        </Text>
                                        <Text style={styles.tableCell}>
                                            {orderData.details[0]?.partNumber}
                                        </Text>
                                        <Text
                                            style={[
                                                styles.tableCellLeft,
                                                { flex: 1.5 },
                                            ]}
                                        >
                                            {orderData.details[0]?.colorName}
                                        </Text>
                                        {sizeColumns.map((size) => (
                                            <Text
                                                key={size}
                                                style={styles.tableCell}
                                            >
                                                {sizes[size] || "-"}
                                            </Text>
                                        ))}
                                        <Text style={styles.tableCell}>
                                            {Object.values(sizes).reduce(
                                                (a: number, b: any) => a + b,
                                                0,
                                            )}
                                        </Text>
                                        <Text style={styles.tableCellLeft}>
                                            {quality === "primera"
                                                ? "Primeras"
                                                : quality === "segunda"
                                                  ? "Segundas"
                                                  : quality === "manchada"
                                                    ? "Manchas"
                                                    : "Incompletas"}
                                        </Text>
                                    </View>
                                ),
                            );
                        },
                    )}
                </View>

                {/* Summary Section for Each Order */}
                {packing.summaries?.map((summary) => {
                    const summaryData = {
                        boxesBySize: summary.boxesBySize || {},
                        bagsBySize: summary.bagsBySize || {},
                        piecesBySize: summary.piecesBySize || {},
                    };

                    return (
                        <View key={summary.id} style={styles.summarySection}>
                            <Text style={styles.summaryTitle}>
                                Orden{" "}
                                {summary.order?.transferNumber || "Sin número"}{" "}
                                - Resumen de Empaque
                            </Text>

                            <View style={styles.summaryTable}>
                                <View style={styles.summaryHeader}>
                                    <Text
                                        style={[
                                            styles.tableCell,
                                            { flex: 1.5 },
                                        ]}
                                    >
                                        Piezas por Caja:{" "}
                                        {(summary as any).piecesPerBox || "N/A"}
                                    </Text>
                                    <Text
                                        style={[
                                            styles.tableCell,
                                            { flex: 1.5 },
                                        ]}
                                    >
                                        Piezas por Bolsa:{" "}
                                        {(summary as any).piecesPerBag || "N/A"}
                                    </Text>
                                </View>

                                <View style={styles.summaryHeader}>
                                    <Text style={styles.tableCell}>TALLAS</Text>
                                    <Text style={styles.tableCell}>CAJAS</Text>
                                    <Text style={styles.tableCell}>PIEZAS</Text>
                                    <Text style={styles.tableCell}>
                                        BOLSAS 1°
                                    </Text>
                                    <Text style={styles.tableCell}>
                                        BOLSAS 2°
                                    </Text>
                                </View>

                                {Object.entries(summaryData).map(
                                    ([size, data]: [string, any]) => (
                                        <View
                                            key={size}
                                            style={styles.summaryRow}
                                        >
                                            <Text style={styles.tableCell}>
                                                {size}
                                            </Text>
                                            <Text style={styles.tableCell}>
                                                {data.boxes || 0}
                                            </Text>
                                            <Text style={styles.tableCell}>
                                                {data.loosePieces || 0}
                                            </Text>
                                            <Text style={styles.tableCell}>
                                                {data.bagsFirst || 0}
                                            </Text>
                                            <Text style={styles.tableCell}>
                                                {data.bagsSecond || 0}
                                            </Text>
                                        </View>
                                    ),
                                )}

                                <View style={styles.summaryTotal}>
                                    <Text style={styles.tableCell}>TOTAL</Text>
                                    <Text style={styles.tableCell}>
                                        {summary.totalBoxes}
                                    </Text>
                                    <Text style={styles.tableCell}>
                                        {summary.totalLoosePieces}
                                    </Text>
                                    <Text style={styles.tableCell}>
                                        {(summary as any).totalBagsFirst || 0}
                                    </Text>
                                    <Text style={styles.tableCell}>
                                        {(summary as any).totalBagsSecond || 0}
                                    </Text>
                                </View>
                            </View>
                        </View>
                    );
                })}

                {/* Grand Total */}
                <View style={[styles.summarySection, { marginTop: 30 }]}>
                    <View style={styles.summaryTotal}>
                        <Text style={{ flex: 1 }}>
                            TOTAL DE CAJAS: {packing.totalBoxes}
                        </Text>
                        <Text style={{ flex: 1 }}>
                            TOTAL DE BOLSAS: {packing.totalBags}
                        </Text>
                        <Text style={{ flex: 1 }}>
                            TOTAL PIEZAS:{" "}
                            {packing.details.reduce(
                                (acc, d) => acc + d.quantity,
                                0,
                            )}
                        </Text>
                    </View>
                </View>

                {/* Signatures */}
                <View style={styles.signatureSection}>
                    <View style={styles.signatures}>
                        <View style={styles.signatureBox}>
                            <View style={styles.signatureLine} />
                            <Text style={styles.signatureLabel}>EMPACÓ</Text>
                            {packing.packedByUser && (
                                <Text style={{ fontSize: 8, marginTop: 2 }}>
                                    {packing.packedByUser.name}
                                </Text>
                            )}
                        </View>

                        <View style={styles.signatureBox}>
                            <View style={styles.signatureLine} />
                            <Text style={styles.signatureLabel}>VERIFICÓ</Text>
                            {packing.verifiedByUser && (
                                <Text style={{ fontSize: 8, marginTop: 2 }}>
                                    {packing.verifiedByUser.name}
                                </Text>
                            )}
                        </View>

                        <View style={styles.signatureBox}>
                            <View style={styles.signatureLine} />
                            <Text style={styles.signatureLabel}>
                                TRANSPORTISTA
                            </Text>
                            {(packing as any).transporter?.name && (
                                <Text style={{ fontSize: 8, marginTop: 2 }}>
                                    {(packing as any).transporter.name}
                                </Text>
                            )}
                        </View>

                        <View style={styles.signatureBox}>
                            <View style={styles.signatureLine} />
                            <Text style={styles.signatureLabel}>RECIBIÓ</Text>
                            {packing.receiverName && (
                                <Text style={{ fontSize: 8, marginTop: 2 }}>
                                    {packing.receiverName}
                                </Text>
                            )}
                        </View>
                    </View>
                </View>
            </Page>
        </Document>
    );
}
