"use client";

import React from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
} from "@heroui/react";
import {
    UserCircleIcon,
    EnvelopeIcon,
    PhoneIcon,
    MapPinIcon,
    BuildingOfficeIcon,
} from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface ContractorInfo {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    location?: string;
    company?: string;
    avatar?: string;
}

interface RemissionContractorInfoProps {
    contractor: ContractorInfo;
    showCard?: boolean;
    compact?: boolean;
}

export default function RemissionContractorInfo({
    contractor,
    showCard = true,
    compact = false,
}: RemissionContractorInfoProps) {
    const content = compact ? (
        <div className="flex items-center gap-3">
            <Avatar
                className="ring-2 ring-white dark:ring-gray-800"
                name={contractor.name}
                size="md"
                src={contractor.avatar}
            />
            <div className="flex-1">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                    {contractor.name}
                </h4>
                <div className="flex items-center gap-4 mt-1">
                    {contractor.email && (
                        <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                            <EnvelopeIcon className="w-3 h-3" />
                            <span>{contractor.email}</span>
                        </div>
                    )}
                    {contractor.phone && (
                        <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                            <PhoneIcon className="w-3 h-3" />
                            <span>{contractor.phone}</span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    ) : (
        <div className="space-y-4">
            <div className="flex items-center gap-4">
                <Avatar
                    className="ring-2 ring-white dark:ring-gray-800"
                    name={contractor.name}
                    size="lg"
                    src={contractor.avatar}
                />
                <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {contractor.name}
                    </h4>
                    {contractor.company && (
                        <Chip
                            size="sm"
                            startContent={
                                <BuildingOfficeIcon className="w-3 h-3" />
                            }
                            variant="flat"
                        >
                            {contractor.company}
                        </Chip>
                    )}
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {contractor.email && (
                    <motion.div
                        className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
                        whileHover={{ scale: 1.02 }}
                    >
                        <EnvelopeIcon className="w-5 h-5 text-gray-500" />
                        <div className="flex-1 min-w-0">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Email
                            </p>
                            <p className="text-sm font-medium truncate">
                                {contractor.email}
                            </p>
                        </div>
                    </motion.div>
                )}

                {contractor.phone && (
                    <motion.div
                        className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
                        whileHover={{ scale: 1.02 }}
                    >
                        <PhoneIcon className="w-5 h-5 text-gray-500" />
                        <div className="flex-1 min-w-0">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Teléfono
                            </p>
                            <p className="text-sm font-medium">
                                {contractor.phone}
                            </p>
                        </div>
                    </motion.div>
                )}

                {contractor.location && (
                    <motion.div
                        className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg md:col-span-2"
                        whileHover={{ scale: 1.02 }}
                    >
                        <MapPinIcon className="w-5 h-5 text-gray-500" />
                        <div className="flex-1 min-w-0">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                Ubicación
                            </p>
                            <p className="text-sm font-medium">
                                {contractor.location}
                            </p>
                        </div>
                    </motion.div>
                )}
            </div>
        </div>
    );

    if (!showCard) {
        return content;
    }

    return (
        <motion.div
            animate={{ opacity: 1, scale: 1 }}
            initial={{ opacity: 0, scale: 0.95 }}
            transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
            }}
        >
            <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                        <UserCircleIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <h3 className="text-lg font-semibold">
                            Información del Contratista
                        </h3>
                    </div>
                </CardHeader>
                <Divider />
                <CardBody className="pt-4">{content}</CardBody>
            </Card>
        </motion.div>
    );
}
