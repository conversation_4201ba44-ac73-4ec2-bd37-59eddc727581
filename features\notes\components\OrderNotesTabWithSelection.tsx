"use client";

import React, { useState, useEffect } from "react";
import {
    Input,
    Button,
    Divider,
    Select,
    SelectItem,
    Chip,
    Avatar,
    addToast,
} from "@heroui/react";
import {
    MagnifyingGlassIcon,
    PlusIcon,
    XMarkIcon,
    FunnelIcon,
    PencilIcon,
    TrashIcon,
    CheckCircleIcon,
    ExclamationCircleIcon,
} from "@heroicons/react/24/outline";

import { NotesSelectionProvider } from "@/shared/context/notes-selection-context";
import {
    getNotesByOrder,
    updateNoteInline,
    createNoteInline,
} from "@/features/notes/actions/modal-notes";

import { InlineNoteEditor } from "./InlineNoteEditor";

// Using any types for quick build fix
type NoteData = any;
type NoteOption = any;
type NoteAuthor = any;

// Tipos para las respuestas de Server Actions
interface ErrorResponse {
    success: false;
    error: string;
    errorCode?: string;
    details?: any;
}

interface SuccessNotesResponse {
    success: true;
    data: {
        notes: any[];
        totalCount: number;
        hasMore: boolean;
        page: number;
        pageSize: number;
        totalPages: number;
    };
}

type NotesResponse = ErrorResponse | SuccessNotesResponse;

export interface OrderNotesTabProps {
    orderId: string;
    importanceOptions?: NoteOption[];
    statusOptions?: NoteOption[];
    authorId?: string;
    userRole?: string;
    onNoteClick?: (note: NoteData) => void;
}

// Componente principal exportado
export function OrderNotesTabWithSelection(props: OrderNotesTabProps) {
    return (
        <NotesSelectionProvider>
            <OrderNotesTabContent {...props} />
        </NotesSelectionProvider>
    );
}

// Componente interno con toda la lógica
function OrderNotesTabContent({
    orderId,
    importanceOptions = [],
    statusOptions = [],
    authorId = "",
    userRole = "",
    onNoteClick,
}: OrderNotesTabProps) {
    // Estados para control de notas
    const [notes, setNotes] = useState<NoteData[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isCreating, setIsCreating] = useState(false);
    const [editingNoteId, setEditingNoteId] = useState<string | null>(null);

    // Estados para filtros
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState({
        searchText: "",
        importanceId: "",
        statusId: "",
        authorId: "",
    });

    // Función para cargar notas reales de la base de datos
    const loadNotes = async () => {
        setIsLoading(true);
        setError(null);

        try {
            // Extraemos los valores para crear un objeto de filtros limpio
            const {
                searchText,
                importanceId,
                statusId,
                authorId: filterAuthorId,
            } = filters;

            // Preparar un objeto filtros con solo los valores que no estén vacíos
            const filtersObj = {
                page,
                pageSize: 10,
                ...(searchText ? { searchText } : {}),
                ...(importanceId ? { importanceId } : {}),
                ...(statusId ? { statusId } : {}),
                ...(filterAuthorId ? { authorId: filterAuthorId } : {}),
            };

            // REMOVED: console.log("Llamando getNotesByOrder con:", { orderId, filters: filtersObj });

            // Llamamos a la Server Action con orderId y filtros como parámetros separados
            const result = (await getNotesByOrder(
                orderId,
                filtersObj,
            )) as NotesResponse;

            if (!result.success) {
                // Podemos acceder a error porque ya validamos que success es false
                const errorResponse = result as ErrorResponse;

                setError(errorResponse.error || "Error al cargar las notas");

                return;
            }

            // Si llegamos aquí, sabemos que success es true, así que podemos hacer un cast seguro
            const successResponse = result as SuccessNotesResponse;
            const { notes: fetchedNotes, hasMore: hasMorePages } =
                successResponse.data;

            if (page === 1) {
                // Aseguramos que los datos tengan el formato correcto para NoteData[]
                setNotes(fetchedNotes as NoteData[]);
            } else {
                setNotes((prev) => [...prev, ...(fetchedNotes as NoteData[])]);
            }
            setHasMore(hasMorePages || false);
        } catch (error) {
            // REMOVED: console.error("Error loading notes:", error);
            setError("Error al cargar las notas");
        } finally {
            setIsLoading(false);
        }
    };

    // Cargar notas al iniciar o cambiar filtros
    useEffect(() => {
        if (orderId) {
            loadNotes();
        }
    }, [orderId, page, filters]);

    // Función para actualizar filtros
    const changeFilters = (newFilters: Partial<typeof filters>) => {
        setFilters((prev) => ({ ...prev, ...newFilters }));
        setPage(1);
    };

    // Función para limpiar filtros
    const clearFilters = () => {
        setFilters({
            searchText: "",
            importanceId: "",
            statusId: "",
            authorId: "",
        });
        setPage(1);
    };

    // Función para cargar más notas (paginación)
    const loadMoreNotes = () => {
        setPage((prev) => prev + 1);
    };

    // Toggle de los filtros
    const toggleFilters = () => setShowFilters((prev) => !prev);

    // Funciones para crear/actualizar/eliminar notas
    const handleCreateNote = async (noteData: {
        content: string;
        importanceId: string;
        statusId: string;
    }) => {
        setIsLoading(true);

        try {
            // Llamamos directamente a createNoteInline que está diseñado para esta función
            const result = await createNoteInline(
                orderId,
                noteData.content,
                noteData.importanceId,
                noteData.statusId,
            );

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description: "Nota creada correctamente",
                    color: "success",
                    icon: <CheckCircleIcon className="w-5 h-5" />,
                });
                setIsCreating(false);
                // Recargar las notas para incluir la nueva
                setPage(1);
                await loadNotes();
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al crear la nota",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });
            }
        } catch (error) {
            // REMOVED: console.error("Error creating note:", error);
            addToast({
                title: "Error",
                description: "Error al crear la nota",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleUpdateNote = async (noteData: {
        noteId: string;
        content: string;
        importanceId: string;
        statusId: string;
    }) => {
        try {
            const result = await updateNoteInline({
                noteId: noteData.noteId,
                content: noteData.content,
                importanceId: noteData.importanceId,
                statusId: noteData.statusId,
            });

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description: "Nota actualizada correctamente",
                    color: "success",
                    icon: <CheckCircleIcon className="w-5 h-5" />,
                });
                // Actualizar la nota en el estado local
                setNotes((prev) =>
                    prev.map((note) =>
                        note.id === noteData.noteId
                            ? {
                                  ...note,
                                  content: noteData.content,
                                  importanceId: noteData.importanceId,
                                  statusId: noteData.statusId,
                                  updatedAt: new Date(),
                              }
                            : note,
                    ),
                );
                setEditingNoteId(null);
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al actualizar la nota",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });
            }
        } catch (error) {
            // REMOVED: console.error("Error updating note:", error);
            addToast({
                title: "Error",
                description: "Error al actualizar la nota",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        }
    };

    const handleDeleteNote = async (noteId: string) => {
        try {
            // Para eliminar una nota, usamos la misma función pero con isDeleted=true
            const result = await updateNoteInline({
                noteId,
                content: "", // Estos campos son requeridos por la API, pero se ignorarán
                importanceId: "",
                statusId: "",
                isDeleted: true, // Flag para indicar eliminación
            } as any); // Usamos any para evitar el error de tipo

            if (result.success) {
                addToast({
                    title: "Éxito",
                    description: "Nota eliminada correctamente",
                    color: "success",
                    icon: <TrashIcon className="w-5 h-5" />,
                });
                // Remover la nota del estado local
                setNotes((prev) => prev.filter((note) => note.id !== noteId));
            } else {
                addToast({
                    title: "Error",
                    description: result.error || "Error al eliminar la nota",
                    color: "danger",
                    icon: <ExclamationCircleIcon className="w-5 h-5" />,
                });
            }
        } catch (error) {
            // REMOVED: console.error("Error deleting note:", error);
            addToast({
                title: "Error",
                description: "Error al eliminar la nota",
                color: "danger",
                icon: <ExclamationCircleIcon className="w-5 h-5" />,
            });
        }
    };

    // Verifica si el usuario puede editar una nota
    const canEditNote = (note: NoteData) => {
        return userRole === "ADMIN" || note.author?.id === authorId;
    };

    // Renderizado de opciones de autor
    const renderAuthorOptions = () => {
        const authors = Array.from(
            new Set(notes.map((note) => note.author?.id)),
        )
            .filter(Boolean)
            .map((authorId) => {
                const author = notes.find(
                    (note) => note.author?.id === authorId,
                )?.author;

                if (!author) return null;

                return {
                    id: String(authorId),
                    author,
                };
            })
            .filter(
                (item): item is { id: string; author: NoteAuthor } =>
                    item !== null,
            );

        return authors.map((item) => (
            <SelectItem key={item.id}>
                <div className="flex items-center gap-2">
                    <Avatar
                        name={item.author.name || "Usuario"}
                        size="sm"
                        src={item.author.image || ""}
                    />
                    <span>{item.author.name || "Usuario"}</span>
                </div>
            </SelectItem>
        ));
    };

    // Manejar clic en una nota
    const handleNoteClick = (note: NoteData) => {
        if (editingNoteId === note.id) {
            // Si ya estamos editando esta nota, no hacemos nada
            return;
        }

        // Si hay un callback de clic, lo llamamos
        if (onNoteClick) {
            onNoteClick(note);
        } else {
            // Si no hay callback, entramos en modo de edición directa
            setEditingNoteId(note.id);
        }
    };

    // Cancelar edición
    const handleCancelEdit = () => {
        setEditingNoteId(null);
    };

    // Inside the component, at the top level, add this useEffect
    useEffect(() => {
        if (editingNoteId) {
            const editingNote = notes.find((note) => note.id === editingNoteId);

            if (editingNote) {
                // REMOVED: console.log("Editing note:", { noteId: editingNote.id, content: editingNote.content, importanceId: editingNote.importanceId, statusId: editingNote.statusId, hasStatusOptions: Array.isArray(statusOptions) && statusOptions.length > 0 });
            }
        }
    }, [editingNoteId, notes, statusOptions]);

    return (
        <div className="w-full space-y-4">
            {/* Barra de acciones */}
            <div className="flex flex-wrap justify-between gap-2">
                <div className="flex gap-2">
                    <Button
                        color="primary"
                        size="sm"
                        startContent={<PlusIcon className="w-4 h-4" />}
                        onClick={() => setIsCreating(true)}
                    >
                        Nueva nota
                    </Button>

                    <Button
                        size="sm"
                        startContent={<FunnelIcon className="w-4 h-4" />}
                        variant="flat"
                        onClick={toggleFilters}
                    >
                        Filtros
                    </Button>
                </div>

                {showFilters && filters.searchText && (
                    <Chip
                        color="primary"
                        size="sm"
                        variant="flat"
                        onClose={clearFilters}
                    >
                        Filtros activos
                    </Chip>
                )}
            </div>

            {/* Formulario de filtros */}
            {showFilters && (
                <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <Input
                            className="w-full"
                            placeholder="Buscar en notas..."
                            size="sm"
                            startContent={
                                <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
                            }
                            value={filters.searchText || ""}
                            onChange={(e) =>
                                changeFilters({ searchText: e.target.value })
                            }
                        />

                        <Select
                            className="w-full"
                            placeholder="Filtrar por importancia"
                            selectedKeys={
                                filters.importanceId
                                    ? [filters.importanceId]
                                    : []
                            }
                            size="sm"
                            onChange={(e) =>
                                changeFilters({
                                    importanceId: e.target.value || undefined,
                                })
                            }
                        >
                            <>
                                <SelectItem
                                    key="all-importance"
                                    textValue="Todas las importancias"
                                >
                                    Todas las importancias
                                </SelectItem>
                                {importanceOptions?.map((option) => (
                                    <SelectItem
                                        key={option.id}
                                        textValue={option.name}
                                    >
                                        <div className="flex items-center gap-2">
                                            {option.color && (
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{
                                                        backgroundColor:
                                                            option.color,
                                                    }}
                                                />
                                            )}
                                            <span>{option.name}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </>
                        </Select>

                        <Select
                            className="w-full"
                            placeholder="Filtrar por estado"
                            selectedKeys={
                                filters.statusId ? [filters.statusId] : []
                            }
                            size="sm"
                            onChange={(e) =>
                                changeFilters({
                                    statusId: e.target.value || undefined,
                                })
                            }
                        >
                            <>
                                <SelectItem
                                    key="all-status"
                                    textValue="Todos los estados"
                                >
                                    Todos los estados
                                </SelectItem>
                                {statusOptions?.map((option) => (
                                    <SelectItem
                                        key={option.id}
                                        textValue={option.name}
                                    >
                                        <div className="flex items-center gap-2">
                                            {option.color && (
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{
                                                        backgroundColor:
                                                            option.color,
                                                    }}
                                                />
                                            )}
                                            <span>{option.name}</span>
                                        </div>
                                    </SelectItem>
                                ))}
                            </>
                        </Select>

                        {notes.length > 0 && (
                            <Select
                                className="w-full"
                                placeholder="Filtrar por autor"
                                selectedKeys={
                                    filters.authorId ? [filters.authorId] : []
                                }
                                size="sm"
                                onChange={(e) =>
                                    changeFilters({
                                        authorId: e.target.value || undefined,
                                    })
                                }
                            >
                                <>
                                    <SelectItem
                                        key="all-authors"
                                        textValue="Todos los autores"
                                    >
                                        Todos los autores
                                    </SelectItem>
                                    {renderAuthorOptions()}
                                </>
                            </Select>
                        )}
                    </div>

                    <div className="flex justify-end">
                        <Button
                            size="sm"
                            startContent={<XMarkIcon className="w-4 h-4" />}
                            variant="flat"
                            onClick={clearFilters}
                        >
                            Limpiar filtros
                        </Button>
                    </div>
                </div>
            )}

            {/* Formulario de creación de nota */}
            {isCreating && (
                <div className="mb-4">
                    <InlineNoteEditor
                        autoFocus
                        importanceOptions={
                            Array.isArray(importanceOptions)
                                ? importanceOptions
                                : []
                        }
                        mode="create"
                        orderId={orderId}
                        statusOptions={
                            Array.isArray(statusOptions) ? statusOptions : []
                        }
                        onCancel={() => setIsCreating(false)}
                        onSave={handleCreateNote}
                    />
                </div>
            )}

            <Divider className="my-2" />

            {/* Listado de notas - NUEVO DISEÑO */}
            <div className="space-y-4">
                {isLoading && notes.length === 0 ? (
                    <div className="text-center py-8">
                        <div className="spinner mb-2" />
                        <p>Cargando notas...</p>
                    </div>
                ) : error ? (
                    <div className="text-center py-8 text-danger">
                        <p>{error}</p>
                        <Button
                            className="mt-2"
                            color="primary"
                            size="sm"
                            variant="flat"
                            onClick={loadNotes}
                        >
                            Reintentar
                        </Button>
                    </div>
                ) : notes.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        <p>No hay notas para esta orden.</p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {notes.map((note) =>
                            editingNoteId === note.id ? (
                                <div
                                    key={note.id}
                                    className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/10 rounded-lg border border-blue-200 dark:border-blue-800/50"
                                >
                                    <InlineNoteEditor
                                        autoFocus
                                        importanceOptions={
                                            Array.isArray(importanceOptions)
                                                ? importanceOptions
                                                : []
                                        }
                                        initialContent={note.content}
                                        initialImportanceId={
                                            note.importanceId || ""
                                        }
                                        initialStatusId={note.statusId || ""}
                                        mode="edit"
                                        noteId={note.id}
                                        orderId={orderId}
                                        statusOptions={
                                            Array.isArray(statusOptions)
                                                ? statusOptions
                                                : []
                                        }
                                        onCancel={handleCancelEdit}
                                        onSave={(data) =>
                                            handleUpdateNote({
                                                noteId: note.id,
                                                content: data.content,
                                                importanceId: data.importanceId,
                                                statusId: data.statusId,
                                            })
                                        }
                                    />
                                </div>
                            ) : (
                                <div
                                    key={note.id}
                                    className={`relative rounded-lg p-4 transition-all duration-200 group hover:shadow-md ${
                                        note.author?.id === authorId
                                            ? "bg-primary-50 dark:bg-primary-900/10 ml-8 mr-2"
                                            : "bg-gray-50 dark:bg-gray-800/30 ml-2 mr-8"
                                    }`}
                                >
                                    {/* Colored bar based on importance */}
                                    <div
                                        className="absolute top-0 left-0 w-full h-1 rounded-t-lg"
                                        style={{
                                            backgroundColor:
                                                importanceOptions.find(
                                                    (option) =>
                                                        option.id ===
                                                        note.importanceId,
                                                )?.color || "#ccc",
                                        }}
                                    />

                                    <div className="flex justify-between mb-2 pt-1">
                                        <div className="flex items-center gap-2">
                                            <Avatar
                                                className={
                                                    note.author?.id === authorId
                                                        ? "ring-2 ring-primary-200"
                                                        : ""
                                                }
                                                name={
                                                    note.author?.name ||
                                                    "Usuario"
                                                }
                                                size="sm"
                                                src={note.author?.image || ""}
                                            />
                                            <span className="font-medium text-sm">
                                                {note.author?.name || "Usuario"}
                                            </span>
                                        </div>

                                        <div className="flex items-center gap-1.5">
                                            {note.statusId && (
                                                <Chip
                                                    size="sm"
                                                    style={{
                                                        backgroundColor: `${statusOptions.find((option) => option.id === note.statusId)?.color || "#6b7280"}15`,
                                                        color:
                                                            statusOptions.find(
                                                                (option) =>
                                                                    option.id ===
                                                                    note.statusId,
                                                            )?.color ||
                                                            "#6b7280",
                                                        border: `1px solid ${statusOptions.find((option) => option.id === note.statusId)?.color || "#6b7280"}30`,
                                                    }}
                                                    variant="flat"
                                                >
                                                    {statusOptions.find(
                                                        (option) =>
                                                            option.id ===
                                                            note.statusId,
                                                    )?.name || "Pendiente"}
                                                </Chip>
                                            )}

                                            {note.importanceId && (
                                                <Chip
                                                    size="sm"
                                                    style={{
                                                        backgroundColor: `${importanceOptions.find((option) => option.id === note.importanceId)?.color || "#6b7280"}15`,
                                                        color:
                                                            importanceOptions.find(
                                                                (option) =>
                                                                    option.id ===
                                                                    note.importanceId,
                                                            )?.color ||
                                                            "#6b7280",
                                                        border: `1px solid ${importanceOptions.find((option) => option.id === note.importanceId)?.color || "#6b7280"}30`,
                                                    }}
                                                    variant="flat"
                                                >
                                                    {importanceOptions.find(
                                                        (option) =>
                                                            option.id ===
                                                            note.importanceId,
                                                    )?.name || "Normal"}
                                                </Chip>
                                            )}
                                        </div>
                                    </div>

                                    <div className="pl-9">
                                        <p className="text-sm whitespace-pre-wrap">
                                            {note.content}
                                        </p>

                                        <div className="flex justify-between items-center mt-2 pt-1 text-xs text-gray-500">
                                            <span>
                                                {new Date(
                                                    note.createdAt,
                                                ).toLocaleDateString("es-ES", {
                                                    day: "2-digit",
                                                    month: "2-digit",
                                                    year: "numeric",
                                                    hour: "2-digit",
                                                    minute: "2-digit",
                                                })}
                                            </span>

                                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                                                {canEditNote(note) && (
                                                    <Button
                                                        isIconOnly
                                                        aria-label="Editar nota"
                                                        className="h-7 w-7"
                                                        color="primary"
                                                        size="sm"
                                                        variant="light"
                                                        onClick={() =>
                                                            setEditingNoteId(
                                                                note.id,
                                                            )
                                                        }
                                                    >
                                                        <PencilIcon className="w-3.5 h-3.5" />
                                                    </Button>
                                                )}

                                                {userRole === "ADMIN" && (
                                                    <Button
                                                        isIconOnly
                                                        aria-label="Eliminar nota"
                                                        className="h-7 w-7"
                                                        color="danger"
                                                        size="sm"
                                                        variant="light"
                                                        onClick={() =>
                                                            handleDeleteNote(
                                                                note.id,
                                                            )
                                                        }
                                                    >
                                                        <TrashIcon className="w-3.5 h-3.5" />
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ),
                        )}

                        {/* Botón para cargar más */}
                        {hasMore && (
                            <div className="text-center pt-2">
                                <Button
                                    isLoading={isLoading && notes.length > 0}
                                    size="sm"
                                    variant="flat"
                                    onClick={loadMoreNotes}
                                >
                                    Cargar más notas
                                </Button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
