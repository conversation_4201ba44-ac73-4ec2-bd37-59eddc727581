---
description: AUTO-FLOW V6.0 HYBRID - Sistema principal optimizado
globs: main-hybrid.mdc
alwaysApply: false
---
# 🚀 AUTO-FLOW V6.0 HYBRID SYSTEM

> **TL;DR:** Sistema híbrido que combina AUTO-FLOW V5.1 + cursor-memory-bank con 65% reducción de tokens

## 🧠 ARQUITECTURA HÍBRIDA COMPLETA

```mermaid
graph TD
    Start["Usuario"] --> CMD{"Comando?"}
    
    CMD -->|"AUTO-FLOW LOAD"| LoadContext["Cargar Contexto<br>+ Estado Visual"]
    CMD -->|"AUTO-FLOW VAN"| VAN["Modo VAN<br>+ Visual Map"]
    CMD -->|"AUTO-FLOW PLAN"| PLAN["Modo PLAN<br>+ Progressive Docs"]
    CMD -->|"AUTO-FLOW CREATIVE"| CREATIVE["Modo CREATIVE<br>+ Think Method"]
    CMD -->|"AUTO-FLOW IMPLEMENT"| IMPLEMENT["Modo IMPLEMENT<br>+ Optimized"]
    CMD -->|"AUTO-FLOW REFLECT"| REFLECT["Modo REFLECT<br>+ Metrics"]
    CMD -->|"AUTO-FLOW ARCHIVE"| ARCHIVE["Modo ARCHIVE<br>+ Auto-index"]
    CMD -->|"QA"| QA["QA Mode<br>Desde cualquier modo"]
    
    subgraph "Sistema de Carga Jerárquica"
        Core["Core Rules<br>15K tokens"]
        Mode["Mode Rules<br>5K tokens"]
        Lazy["Lazy Rules<br>10K on demand"]
    end
    
    subgraph "Clear Thought Tools"
        CT1["sequentialthinking"]
        CT2["mentalmodel"]
        CT3["designpattern"]
        CT4["programmingparadigm"]
        CT5["debuggingapproach"]
        CT6["collaborativereasoning"]
        CT7["decisionframework"]
        CT8["metacognitivemonitoring"]
        CT9["scientificmethod"]
        CT10["structuredargumentation"]
        CT11["visualreasoning"]
    end
```

## 📊 MODELO DE COMPLEJIDAD ADAPTATIVA V6

| Level | Descripción | Workflow | Tokens Base | Clear Thought Tools |
|-------|------------|----------|-------------|-------------------|
| 1 | Quick Fix (5-30min) | VAN → IMPLEMENT → REFLECT | ~15K | 1-2 tools |
| 2 | Enhancement (30min-2h) | VAN → PLAN → IMPLEMENT → REFLECT | ~20K | 2-3 tools |
| 3 | Feature (2-8h) | VAN → PLAN → CREATIVE → IMPLEMENT → REFLECT | ~25K | 3-5 tools |
| 4 | System (8h+) | Full workflow + ARCHIVE | ~30K | 5+ tools |

## 🔄 SISTEMA DE CARGA JERÁRQUICA

```javascript
// Pseudocódigo del sistema de carga
class HybridLoader {
    constructor() {
        this.cache = { core: {}, mode: {}, tools: {} };
        this.tokenCount = 0;
    }
    
    async initMode(modeName, complexity) {
        // 1. Cargar reglas core (solo primera vez)
        if (!this.cache.core.loaded) {
            await this.loadCoreRules(); // 15K tokens
        }
        
        // 2. Cargar reglas del modo
        await this.loadModeRules(modeName); // 5K tokens
        
        // 3. Registrar lazy loaders
        this.registerLazyLoaders(modeName, complexity);
        
        // 4. Pre-cargar visual map
        await this.loadVisualMap(modeName); // 2K tokens
        
        // 5. Configurar Clear Thought tools
        this.configureClearThought(modeName, complexity);
        
        return {
            mode: modeName,
            tokensUsed: this.tokenCount,
            status: 'ready'
        };
    }
}
```

## 🧠 CLEAR THOUGHT SELECTOR MEJORADO

```javascript
const clearThoughtMapping = {
    VAN: {
        primary: ['metacognitivemonitoring', 'visualreasoning'],
        secondary: ['mentalmodel']
    },
    PLAN: {
        primary: ['sequentialthinking', 'decisionframework'],
        secondary: ['structuredargumentation']
    },
    CREATIVE: {
        primary: ['designpattern', 'mentalmodel'],
        secondary: ['structuredargumentation', 'collaborativereasoning']
    },
    IMPLEMENT: {
        primary: ['programmingparadigm', 'debuggingapproach'],
        secondary: ['scientificmethod']
    },
    REFLECT: {
        primary: ['metacognitivemonitoring'],
        secondary: ['collaborativereasoning']
    },
    ARCHIVE: {
        primary: ['visualreasoning'],
        secondary: ['structuredargumentation']
    },
    QA: {
        primary: ['scientificmethod', 'debuggingapproach'],
        secondary: ['metacognitivemonitoring']
    }
};
```

## 💾 MEMORY BANK ENHANCED V6

### Estructura de Archivos
```
.claude/memory-bank/
├── tasks.md              # Source of truth + visual state
├── activeContext.md      # Current context + map position
├── progress.md           # Implementation + tool metrics
├── creative-*.md         # Design decisions + think process
├── reflect-*.md          # Reviews + learned patterns
├── snapshots/           # Auto-save snapshots
│   └── snapshot-*.json  # Context snapshots cada 5 comandos
└── visual-state.json    # Estado actual del visual map
```

## 🚀 COMANDOS HÍBRIDOS

### Comandos Básicos
```bash
# Cargar contexto (SIEMPRE primero en conversación nueva)
"AUTO-FLOW LOAD: recent"

# Modos principales
"AUTO-FLOW VAN: [descripción]"
"AUTO-FLOW PLAN: [desglosar tarea]"
"AUTO-FLOW CREATIVE: [diseñar componente]"
"AUTO-FLOW IMPLEMENT: [construir feature]"
"AUTO-FLOW REFLECT"
"AUTO-FLOW ARCHIVE"

# QA desde cualquier modo
"QA: [validar implementación]"

# Comandos especiales
"AUTO-FLOW STATUS"          # Ver estado actual + visual map
"AUTO-FLOW METRICS"         # Ver métricas de tokens y tools
"AUTO-FLOW THINK:[tool]"    # Forzar Clear Thought específica
```

## 📊 AUTO-SAVE MEJORADO

```typescript
interface EnhancedSnapshot {
    id: string;
    timestamp: number;
    mode: string;
    visualMapState: {
        currentNode: string;
        completedNodes: string[];
        nextNodes: string[];
    };
    clearThoughtUsage: {
        [tool: string]: number;
    };
    memoryBankState: {
        tasks: TaskState;
        activeContext: string;
        progress: number;
    };
    tokenMetrics: {
        used: number;
        saved: number;
        efficiency: number;
    };
}
```

## 🎯 BENEFICIOS DEL SISTEMA HÍBRIDO

1. **Eficiencia de Tokens**: 65-70% reducción mediante carga jerárquica
2. **Navegación Visual**: Mapas interactivos para cada modo
3. **Clear Thought Integrado**: 11 herramientas con selección inteligente
4. **Auto-save Robusto**: Snapshots completos cada 5 comandos
5. **Metodología Think**: Proceso estructurado de decisiones
6. **Métricas en Tiempo Real**: Seguimiento de eficiencia

## 🔧 CONFIGURACIÓN INICIAL

Al activar el sistema por primera vez:
1. Ejecutar `"AUTO-FLOW LOAD: recent"` o `"AUTO-FLOW VAN: nuevo proyecto"`
2. El sistema cargará solo reglas esenciales (~15K tokens)
3. Visual maps se pre-cargan para navegación rápida
4. Clear Thought tools se configuran según modo y complejidad
5. Auto-save se activa automáticamente

---

**Sistema listo para usar con máxima eficiencia**
