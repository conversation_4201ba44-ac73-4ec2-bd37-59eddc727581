/**
 * Order Helper Utilities
 * Type guards and safe data access for Order objects
 */

import { Order } from "../types/order-modal.types";

// ============ TYPE GUARDS ============

/**
 * Validate if an object is a valid Order
 */
export const isValidOrder = (order: any): order is Order => {
    return order && typeof order.id === "string" && order.id.length > 0;
};

/**
 * Check if order has valid parts array
 */
export const hasValidParts = (order: Order): boolean => {
    return Array.isArray(order.parts) && order.parts.length > 0;
};

/**
 * Check if order has valid assignments array
 */
export const hasValidAssignments = (order: Order): boolean => {
    return Array.isArray(order.assignments) && order.assignments.length > 0;
};

/**
 * Check if order has valid garments array
 */
export const hasValidGarments = (order: Order): boolean => {
    return Array.isArray(order.garments) && order.garments.length > 0;
};

// ============ SAFE GETTERS ============

/**
 * Generic safe getter with fallback
 */
export const safeGet = <T>(value: T | null | undefined, fallback: T): T => {
    return value ?? fallback;
};

/**
 * Get order display name with fallbacks
 */
export const getOrderDisplayName = (order: Order | null): string => {
    if (!order) return "Sin orden";

    return order.orderCode || order.cutOrder || `Orden ${order.id.slice(-6)}`;
};

/**
 * Get customer name with multiple fallbacks
 */
export const getCustomerName = (order: Order | null): string => {
    if (!order) return "Sin cliente";

    return (
        order.customerName || order.customer?.name || "Cliente no especificado"
    );
};

/**
 * Get customer avatar safely
 */
export const getCustomerAvatar = (order: Order | null): string | undefined => {
    if (!order) return undefined;

    return order.customerAvatar || order.customer?.image;
};

/**
 * Format date safely with fallback
 */
export const formatDate = (
    date: string | Date | null | undefined,
    fallback = "Sin fecha",
): string => {
    if (!date) return fallback;
    try {
        // Si es string ISO fecha (YYYY-MM-DD), tratarla como fecha local
        if (typeof date === "string" && date.length === 10) {
            // Agregar mediodía para evitar cambios por timezone
            date = `${date}T12:00:00`;
        }

        return new Date(date).toLocaleDateString("es-MX", {
            year: "numeric",
            month: "short",
            day: "numeric",
            timeZone: "America/Mexico_City", // Forzar timezone México
        });
    } catch {
        return fallback;
    }
};

// ============ CALCULATIONS ============

/**
 * Calculate total parts count
 */
export const calculateTotalParts = (order: Order): number => {
    return order.parts?.length || order._count?.parts || 0;
};

/**
 * Calculate completed parts
 */
export const calculateCompletedParts = (order: Order): number => {
    if (!order.parts || !Array.isArray(order.parts)) return 0;

    return order.parts.filter((part) => part.progress === 100).length;
};

/**
 * Calculate total assignments
 */
export const calculateTotalAssignments = (order: Order): number => {
    return order.assignments?.length || order._count?.assignments || 0;
};

/**
 * Calculate progress percentage safely
 */
export const calculateProgressPercentage = (order: Order): number => {
    const progress = order.progressPercentage;

    if (typeof progress === "number" && progress >= 0 && progress <= 100) {
        return Math.round(progress);
    }
    // Fallback: calculate from parts if available
    const total = calculateTotalParts(order);
    const completed = calculateCompletedParts(order);

    return total > 0 ? Math.round((completed / total) * 100) : 0;
};

// ============ STATUS HELPERS ============

/**
 * Get status color safely
 */
export const getStatusColor = (order: Order): string => {
    return order.status?.color || "#6B7280"; // Default gray
};

/**
 * Get status name safely
 */
export const getStatusName = (order: Order): string => {
    return order.status?.name || "Sin estado";
};

/**
 * Check if order is overdue
 */
export const isOrderOverdue = (order: Order): boolean => {
    if (!order.estimatedDeliveryDate) return false;
    const deliveryDate = new Date(order.estimatedDeliveryDate);
    const today = new Date();

    today.setHours(0, 0, 0, 0); // Reset time for date comparison

    return deliveryDate < today;
};
