# 📦 Orders Feature

## 📋 Descripción
Módulo principal para la gestión de órdenes textiles en LOHARI. Maneja todo el ciclo de vida de las órdenes desde su creación hasta la entrega.

## 🏗️ Estructura
```
orders/
├── components/     # Componentes UI de órdenes
├── hooks/         # Hooks personalizados
├── actions/       # Server actions
├── schemas/       # Validación con Zod
├── types/         # TypeScript types
└── utils/         # Utilidades específicas
```

## 🧩 Componentes Principales

### OrderList
Lista paginada de órdenes con filtros y búsqueda.
```tsx
import { OrderList } from '@/features/orders'
```

### OrderForm
Formulario completo para crear/editar órdenes.
```tsx
import { OrderForm } from '@/features/orders'
```

### OrderDetails
Vista detallada con timeline y estados.
```tsx
import { OrderDetails } from '@/features/orders'
```

## 🪝 Hooks Disponibles

### useOrders
Hook principal para gestión de órdenes.
```tsx
const { orders, loading, error } = useOrders()
```

### useOrderMutations
Mutaciones CRUD para órdenes.
```tsx
const { createOrder, updateOrder, deleteOrder } = useOrderMutations()
```

## 📝 Schemas

### orderSchema
Schema de validación principal.
```tsx
import { orderSchema } from '@/features/orders/schemas'
```

## 🎯 Uso Típico

```tsx
// En una página
import { OrderList, useOrders } from '@/features/orders'

export default function OrdersPage() {
  const { orders, loading } = useOrders()
  
  if (loading) return <Spinner />
  
  return <OrderList orders={orders} />
}
```

## 🔗 Dependencias
- `@/features/customers` - Para datos de clientes
- `@/features/garments` - Para información de prendas
- `@/shared/components` - Componentes UI base