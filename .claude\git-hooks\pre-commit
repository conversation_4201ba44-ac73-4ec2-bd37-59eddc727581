#!/bin/sh
# AUTO-FLOW V5.1 Git Hook - Pre-commit
# Validates code before allowing commits in AUTO-FLOW branches

# Only run on AUTO-FLOW branches
branch=$(git rev-parse --abbrev-ref HEAD)
if [[ ! "$branch" =~ ^feature/auto-flow/ ]]; then
    exit 0
fi

echo "🔍 AUTO-FLOW: Running pre-commit validation..."

# 1. Check TypeScript
echo "📘 Checking TypeScript..."
npm run type-check --silent
if [ $? -ne 0 ]; then
    echo "❌ TypeScript errors found. AUTO-FLOW will attempt to fix..."
    exit 1
fi

# 2. Check ESLint
echo "🔧 Checking ESLint..."
npm run lint --silent
if [ $? -ne 0 ]; then
    echo "⚠️ Lint warnings found. AUTO-FLOW may auto-fix these..."
fi

# 3. Check tests (only for changed files)
echo "🧪 Running affected tests..."
changed_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|tsx)$')
if [ ! -z "$changed_files" ]; then
    npm test -- --findRelatedTests $changed_files --passWithNoTests
    if [ $? -ne 0 ]; then
        echo "❌ Tests failing. AUTO-FLOW will analyze..."
        exit 1
    fi
fi

echo "✅ AUTO-FLOW: Pre-commit validation passed!"
exit 0