import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EnhancedPackingWizardV3 } from '@/features/packings/components/wizard/EnhancedPackingWizardV3';
import { usePackingWizardStore } from '@/features/packings/hooks/usePackingWizardStore';

// Mock the server actions
jest.mock('@/features/packings/actions/create-packing-enhanced-v3', () => ({
  createPackingEnhancedV3: jest.fn().mockResolvedValue({
    success: true,
    data: { id: 'packing-123', folio: 'PCK-2024-001' }
  })
}));

jest.mock('@/features/packings/actions/calculate-packaging-enhanced', () => ({
  calculatePackagingAutomatically: jest.fn().mockResolvedValue({
    success: true,
    data: {
      orderId: 'order-1',
      sizeBreakdown: [
        { size: 'M', totalQuantity: 100, boxes: 2, bagsFirst: 0, bagsSecond: 0, loosePieces: 0 }
      ]
    }
  })
}));

// Mock SWR
jest.mock('swr', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    data: {
      success: true,
      data: [
        {
          id: 'becktel-id',
          name: 'Becktel',
          children: [
            { id: 'reebok-id', name: 'Reebok', parentId: 'becktel-id' }
          ]
        }
      ]
    },
    error: null,
    isLoading: false,
    mutate: jest.fn()
  }))
}));

// Mock the toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

describe('Enhanced Packing Wizard E2E', () => {
  const mockOnSuccess = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the store
    const { reset } = usePackingWizardStore.getState();
    reset();
  });

  it('should complete the full wizard flow successfully', async () => {
    const user = userEvent.setup();
    
    render(
      <EnhancedPackingWizardV3
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    // Step 1: Basic Information
    expect(screen.getByText('Información Básica')).toBeInTheDocument();
    
    // Select customer
    const customerSelect = screen.getByLabelText('Cliente / Sub-cliente');
    await user.click(customerSelect);
    await user.click(screen.getByText('Becktel > Reebok'));
    
    // Fill sender information
    await user.type(screen.getByLabelText('Nombre del Remitente'), 'John Doe');
    await user.type(screen.getByLabelText('Email del Remitente'), '<EMAIL>');
    await user.type(screen.getByLabelText('Teléfono del Remitente'), '1234567890');
    
    // Add transport notes
    await user.type(screen.getByLabelText('Notas de Transporte'), 'Handle with care');
    
    // Go to next step
    await user.click(screen.getByText('Continuar'));
    
    // Step 2: Order Selection (mocked, so we'll skip interaction)
    await waitFor(() => {
      expect(screen.getByText('Selección de Órdenes')).toBeInTheDocument();
    });
    await user.click(screen.getByText('Continuar'));
    
    // Step 3: Quality Distribution (mocked, so we'll skip interaction)
    await waitFor(() => {
      expect(screen.getByText('Distribución por Calidad')).toBeInTheDocument();
    });
    await user.click(screen.getByText('Continuar'));
    
    // Step 4: Packaging Summary
    await waitFor(() => {
      expect(screen.getByText('Resumen de Empaque')).toBeInTheDocument();
    });
    
    // The automatic calculation should have been triggered
    expect(screen.getByText('M')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument(); // Total quantity
    expect(screen.getByText('2')).toBeInTheDocument(); // Boxes
    
    await user.click(screen.getByText('Continuar'));
    
    // Step 5: Review and Confirmation
    await waitFor(() => {
      expect(screen.getByText('Revisión y Confirmación')).toBeInTheDocument();
    });
    
    // Check summary information
    expect(screen.getByText('Becktel > Reebok')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Handle with care')).toBeInTheDocument();
    
    // Submit the form
    await user.click(screen.getByText('Generar Packing'));
    
    // Wait for success
    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalledWith({
        id: 'packing-123',
        folio: 'PCK-2024-001'
      });
    });
  });

  it('should allow navigation between steps', async () => {
    const user = userEvent.setup();
    
    render(
      <EnhancedPackingWizardV3
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    // Fill basic info and go to step 2
    const customerSelect = screen.getByLabelText('Cliente / Sub-cliente');
    await user.click(customerSelect);
    await user.click(screen.getByText('Becktel > Reebok'));
    await user.click(screen.getByText('Continuar'));
    
    // Should be on step 2
    await waitFor(() => {
      expect(screen.getByText('Selección de Órdenes')).toBeInTheDocument();
    });
    
    // Go back to step 1
    await user.click(screen.getByText('Atrás'));
    
    // Should be back on step 1 with data preserved
    await waitFor(() => {
      expect(screen.getByText('Información Básica')).toBeInTheDocument();
    });
    
    // Customer selection should be preserved
    expect(screen.getByText('Becktel > Reebok')).toBeInTheDocument();
  });

  it('should handle cancellation', async () => {
    const user = userEvent.setup();
    
    render(
      <EnhancedPackingWizardV3
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    // Click cancel button
    await user.click(screen.getByText('Cancelar'));
    
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    
    render(
      <EnhancedPackingWizardV3
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    // Try to continue without filling required fields
    await user.click(screen.getByText('Continuar'));
    
    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/seleccione un cliente/i)).toBeInTheDocument();
    });
  });

  it('should handle server errors gracefully', async () => {
    const user = userEvent.setup();
    const { createPackingEnhancedV3 } = require('@/features/packings/actions/create-packing-enhanced-v3');
    
    // Mock server error
    createPackingEnhancedV3.mockResolvedValueOnce({
      success: false,
      error: 'Server error occurred'
    });
    
    render(
      <EnhancedPackingWizardV3
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    // Complete the wizard flow (abbreviated)
    const customerSelect = screen.getByLabelText('Cliente / Sub-cliente');
    await user.click(customerSelect);
    await user.click(screen.getByText('Becktel > Reebok'));
    
    // Navigate through all steps
    for (let i = 0; i < 4; i++) {
      await user.click(screen.getByText('Continuar'));
      await waitFor(() => {
        expect(screen.queryByText('Continuar') || screen.queryByText('Generar Packing')).toBeInTheDocument();
      });
    }
    
    // Submit
    await user.click(screen.getByText('Generar Packing'));
    
    // Should show error toast
    await waitFor(() => {
      const { toast } = require('sonner');
      expect(toast.error).toHaveBeenCalledWith('Server error occurred');
    });
    
    // Should not call onSuccess
    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it('should preserve state when navigating between steps', async () => {
    const user = userEvent.setup();
    
    render(
      <EnhancedPackingWizardV3
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
      />
    );

    // Fill step 1
    const customerSelect = screen.getByLabelText('Cliente / Sub-cliente');
    await user.click(customerSelect);
    await user.click(screen.getByText('Becktel > Reebok'));
    await user.type(screen.getByLabelText('Nombre del Remitente'), 'John Doe');
    
    // Go to step 2
    await user.click(screen.getByText('Continuar'));
    await waitFor(() => {
      expect(screen.getByText('Selección de Órdenes')).toBeInTheDocument();
    });
    
    // Go to step 3
    await user.click(screen.getByText('Continuar'));
    await waitFor(() => {
      expect(screen.getByText('Distribución por Calidad')).toBeInTheDocument();
    });
    
    // Go back to step 1
    await user.click(screen.getByText('Atrás'));
    await waitFor(() => {
      expect(screen.getByText('Selección de Órdenes')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('Atrás'));
    await waitFor(() => {
      expect(screen.getByText('Información Básica')).toBeInTheDocument();
    });
    
    // Check that data is preserved
    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
  });
});